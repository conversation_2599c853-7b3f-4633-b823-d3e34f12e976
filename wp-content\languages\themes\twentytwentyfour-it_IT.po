# Translation of Themes - Twenty Twenty-Four in Italian
# This file is distributed under the same license as the Themes - Twenty Twenty-Four package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-11-28 15:16:26+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: it\n"
"Project-Id-Version: Themes - Twenty Twenty-Four\n"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Four is designed to be flexible, versatile and applicable to any website. Its collection of templates and patterns tailor to different needs, such as presenting a business, blogging and writing or showcasing work. A multitude of possibilities open up with just a few adjustments to color and typography. Twenty Twenty-Four comes with style variations and full page designs to help speed up the site building process, is fully compatible with the site editor, and takes advantage of new design tools introduced in WordPress 6.4."
msgstr "Twenty Twenty-Four è progettato per essere flessibile, versatile e utilizzabile per qualsiasi sito web. La sua collezione di template e pattern si adatta perfettamente a necessità diverse, come presentare un'azienda, scrivere un blog, o presentare un lavoro. Con pochi ritocchi ai colori e alla tipografia si aprono una moltitudine di possibilità. Twenty Twenty-Four include variazioni di stile e design di pagine intere per velocizzare il procedimento di creazione di un sito, è completamente compatibile con l'editor del sito e sfrutta pienamente i nuovi strumenti di design introdotti in WordPress 6.4."

#. Theme Name of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Four"
msgstr "Twenty Twenty-Four"

#: patterns/text-centered-statement.php
msgctxt "Pattern description"
msgid "A centered text statement with a large amount of padding on all sides."
msgstr "Un testo centrato adatto per una affermazione con molta spaziatura interna su tutti i lati."

#: patterns/hidden-posts-heading.php
msgctxt "Pattern title"
msgid "Posts heading"
msgstr "Titolo degli articoli"

#: patterns/text-title-left-image-right.php
msgctxt "Pattern description"
msgid "A title, a paragraph and a CTA button on the left with an image on the right."
msgstr "Un titolo, un paragrafo e un pulsante di invito all'azione a sinistra con un'immagine a destra."

#: patterns/text-project-details.php
msgctxt "Pattern description"
msgid "A text-only section for project details."
msgstr "Una sezione di solo testo per i dettagli del progetto."

#: patterns/text-feature-grid-3-col.php
msgctxt "Pattern description"
msgid "A feature grid of 2 rows and 3 columns with headings and text."
msgstr "Una griglia in evidenza di 2 righe e 3 colonne con titoli e testo."

#: patterns/text-faq.php:35
msgctxt "Question in the FAQ pattern"
msgid "Who is behind Études?"
msgstr "Chi c'è dietro Études?"

#: patterns/text-faq.php
msgctxt "Pattern description"
msgid "A FAQ section with a large FAQ heading and a group of questions and answers."
msgstr "Una sezione FAQ con un titolo ampio e un elenco di domande e risposte."

#: patterns/text-centered-statement-small.php
msgctxt "Pattern description"
msgid "A centered italic text statement with compact padding."
msgstr "Una frase di testo in corsivo centrata con spaziatura interna compatta."

#: patterns/text-alternating-images.php
msgctxt "Pattern description"
msgid "A text section, then a two-column section with text in one column and an image in the other."
msgstr "Una sezione di testo, poi una sezione a due colonne con del testo in una e un'immagine nell'altra."

#: patterns/testimonial-centered.php
msgctxt "Pattern description"
msgid "A centered testimonial section with an avatar, name, and job title."
msgstr "Una sezione di testimonianze centrata con un avatar, un nome e un ruolo lavorativo."

#: patterns/team-4-col.php
msgctxt "Pattern description"
msgid "A team section, with a heading, a paragraph, and 4 columns for team members."
msgstr "Una sezione dedicata al team, con un titolo, un paragrafo e 4 colonne per i membri del team."

#: patterns/posts-list.php
msgctxt "Pattern description"
msgid "A list of posts without images, 1 column."
msgstr "Elenco di articoli senza immagini, 1 colonna."

#: patterns/posts-images-only-offset-4-col.php
msgctxt "Pattern description"
msgid "A list of posts with featured images only, 4 columns."
msgstr "Un elenco di articoli con sole immagini in evidenza, 4 colonne."

#: patterns/posts-images-only-3-col.php
msgctxt "Pattern description"
msgid "A list of posts with featured images only, 3 columns."
msgstr "Un elenco di articoli con sole immagini in evidenza, a 3 colonne."

#: patterns/posts-grid-2-col.php
msgctxt "Pattern description"
msgid "A grid of posts featuring the first post, 2 columns."
msgstr "Una griglia di articoli, con il primo in evidenza, 2 colonne."

#: patterns/posts-3-col.php
msgctxt "Pattern description"
msgid "A list of posts, 3 columns."
msgstr "Un elenco di articoli, 3 colonne."

#: patterns/posts-1-col.php
msgctxt "Pattern description"
msgid "A list of posts, 1 column."
msgstr "Un elenco di articoli, 1 colonna."

#: patterns/page-portfolio-overview.php
msgctxt "Pattern description"
msgid "A full portfolio page with a section for project description, project details, a full screen image, and a gallery section with two images."
msgstr "Una pagina portfolio con una sezione per la descrizione del progetto, i dettagli del progetto, un'immagine a schermo intero e una sezione galleria con due immagini."

#: patterns/page-newsletter-landing.php
msgctxt "Pattern description"
msgid "A block with a newsletter subscription CTA for a landing page."
msgstr "Un blocco con un invito all'iscrizione alla newsletter, per una landing page."

#: patterns/page-home-portfolio.php
msgctxt "Pattern description"
msgid "A portfolio home page with a description and a 4-column post section with only feature images."
msgstr "Una homepage di portfolio con una descrizione e una sezione a 4 colonne di articoli con sole immagini in evidenza."

#: patterns/page-home-portfolio-gallery.php
msgctxt "Pattern description"
msgid "A portfolio home page that features a gallery."
msgstr "Una homepage di portfolio con una galleria in evidenza."

#: patterns/page-home-business.php
msgctxt "Pattern description"
msgid "A business home page with a hero section, a text section, a services section, a team section, a clients section, a FAQ section, and a CTA section."
msgstr "Homepage aziendale con una sezione hero, una sezione di testo, una sezione servizi, una sezione team, una sezione clienti, una sezione FAQ e una sezione per l'invito all'azione."

#: patterns/page-home-blogging.php
msgctxt "Pattern description"
msgid "A blogging home page with a hero section, a text section, a blog section, and a CTA section."
msgstr "Homepage per un blog con una sezione hero, una sezione di testo, una sezione blog e una sezione per l'invito all'azione."

#: patterns/page-about-business.php
msgctxt "Pattern description"
msgid "A business about page with a hero section, a text section, a services section, a team section, a clients section, a FAQ section, and a CTA section."
msgstr "Una pagina aziendale con una sezione hero, una sezione di testo, una sezione servizi, una sezione team, una sezione clienti, una sezione FAQ e una sezione per l'invito all'azione."

#: patterns/gallery-project-layout.php
msgctxt "Pattern description"
msgid "A gallery section with a project layout with 2 images."
msgstr "Una sezione galleria con un layout di progetto con 2 immagini."

#: patterns/gallery-offset-images-grid-4-col.php
msgctxt "Pattern description"
msgid "A gallery section with 4 columns and offset images."
msgstr "Una sezione galleria con un layout di progetto con 4 colonne con immagini sfalsate."

#: patterns/gallery-offset-images-grid-3-col.php
msgctxt "Pattern description"
msgid "A gallery section with 3 columns and offset images."
msgstr "Una sezione galleria con un layout di progetto con 3 colonne con immagini sfalsate."

#: patterns/gallery-offset-images-grid-2-col.php
msgctxt "Pattern description"
msgid "A gallery section with 2 columns and offset images."
msgstr "Una sezione galleria con un layout di progetto con 2 colonne con immagini sfalsate."

#: patterns/gallery-full-screen-image.php
msgctxt "Pattern description"
msgid "A cover image section that covers the entire width."
msgstr "Una sezione con un'immagine di copertina che copre l'intera larghezza."

#: patterns/footer.php
msgctxt "Pattern description"
msgid "A footer section with a colophon and 4 columns."
msgstr "Una sezione footer con colophon e 4 colonne."

#: patterns/footer-colophon-3-col.php
msgctxt "Pattern description"
msgid "A footer section with a colophon and 3 columns."
msgstr "Una sezione footer con colophon e 3 colonne."

#: patterns/footer-centered-logo-nav.php
msgctxt "Pattern description"
msgid "A footer section with a centered logo, navigation, and WordPress credits."
msgstr "Una sezione footer con un logo centrato, la navigazione e i riconoscimenti per WordPress."

#: patterns/cta-subscribe-centered.php
msgctxt "Pattern description"
msgid "Subscribers CTA section with a title, a paragraph and a CTA button."
msgstr "Una sezione di invito all'azione per chi si abbona, con un titolo, un paragrafo e un pulsante di invito all'azione."

#: patterns/cta-services-image-left.php
msgctxt "Pattern description"
msgid "An image, title, paragraph and a CTA button to describe services."
msgstr "Un'immagine, un titolo, un paragrafo e un pulsante di invito all'azione, per descrivere i servizi."

#: patterns/cta-rsvp.php patterns/page-rsvp-landing.php
msgctxt "Pattern description"
msgid "A large RSVP heading sideways, a description, and a CTA button."
msgstr "Un grande titolo RSVP laterale, una descrizione e un pulsante che invita all'azione."

#: patterns/cta-pricing.php
msgctxt "Pattern description"
msgid "A pricing section with a title, a paragraph and three pricing levels."
msgstr "Una sezione per i prezzi con un titolo, un paragrafo e tre livelli di prezzo."

#: patterns/cta-content-image-on-right.php
msgctxt "Pattern description"
msgid "A title, paragraph, two CTA buttons, and an image for a general CTA section."
msgstr "Un titolo, un paragrafo, due pulsanti che invitano all'azione e un'immagine per una sezione di invito all'azione generale."

#: patterns/banner-project-description.php
msgctxt "Pattern description"
msgid "Project description section with title, paragraph, and an image."
msgstr "Sezione di descrizione del progetto con titolo, paragrafo e immagine."

#: patterns/banner-hero.php
msgctxt "Pattern description"
msgid "A hero section with a title, a paragraph, a CTA button, and an image."
msgstr "Una sezione hero con un titolo, un paragrafo, un pulsante che invita all'azione e un'immagine."

#: patterns/footer.php:96
msgid "Twitter/X"
msgstr "Twitter/X"

#: patterns/footer.php:74
msgid "Contact Us"
msgstr "Contattaci"

#: patterns/footer.php:73
msgid "Terms and Conditions"
msgstr "Termini e condizioni"

#: patterns/footer.php:72
msgid "Privacy Policy"
msgstr "Privacy Policy"

#: patterns/footer.php:51
msgid "Careers"
msgstr "Opportunità di lavoro"

#: patterns/footer.php:50
msgid "History"
msgstr "Cronologia"

#: patterns/footer.php:49
msgid "Team"
msgstr "Team"

#: patterns/posts-list.php
msgctxt "Pattern title"
msgid "List of posts without images, 1 column"
msgstr "Elenco di articoli senza immagini, 1 colonna"

#: patterns/hidden-portfolio-hero.php:16
msgid "I’m <em>Leia Acosta</em>, a passionate photographer who finds inspiration in capturing the fleeting beauty of life."
msgstr "Sono <em>Leia Acosta</em>, un'appassionata fotografa che trova ispirazione dal catturare la fuggevole bellezza della vita."

#: functions.php:200
msgid "A collection of full page layouts."
msgstr "Una collezione di layout di pagine intere."

#: functions.php:199
msgctxt "Block pattern category"
msgid "Pages"
msgstr "Pagine"

#: theme.json
msgctxt "Custom template name"
msgid "Single with Sidebar"
msgstr "Articolo singolo con barra laterale"

#: theme.json
msgctxt "Custom template name"
msgid "Page with Wide Image"
msgstr "Pagina con immagine ampia"

#: theme.json
msgctxt "Custom template name"
msgid "Page with Sidebar"
msgstr "Pagina con barra laterale"

#: theme.json
msgctxt "Custom template name"
msgid "Page No Title"
msgstr "Pagina senza titolo"

#: theme.json
msgctxt "Template part name"
msgid "Post Meta"
msgstr "Meta dell'articolo"

#: theme.json
msgctxt "Template part name"
msgid "Sidebar"
msgstr "Barra laterale"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "Footer"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "Header"

#: theme.json
msgctxt "Space size name"
msgid "6"
msgstr "6"

#: theme.json
msgctxt "Space size name"
msgid "5"
msgstr "5"

#: theme.json
msgctxt "Space size name"
msgid "4"
msgstr "4"

#: theme.json
msgctxt "Space size name"
msgid "3"
msgstr "3"

#: theme.json
msgctxt "Space size name"
msgid "2"
msgstr "2"

#: theme.json
msgctxt "Space size name"
msgid "1"
msgstr "1"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard pewter to white"
msgstr "Verticale staccato da color peltro a bianco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard mint to white"
msgstr "Verticale staccato da color menta a bianco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard sage to white"
msgstr "Verticale staccato da color salvia a bianco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard rust to white"
msgstr "Verticale staccato da color ruggine a bianco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard sandstone to white"
msgstr "Verticale staccato da arenaria a bianco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical hard beige to white"
msgstr "Verticale staccato da beige a bianco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft pewter to white"
msgstr "Verticale sfumato da color peltro a bianco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft mint to white"
msgstr "Verticale sfumato da color menta a bianco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft sage to white"
msgstr "Verticale sfumato da color salvia a bianco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft rust to white"
msgstr "Verticale sfumato da color ruggine a bianco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft sandstone to white"
msgstr "Verticale sfumato da arenaria a bianco"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical soft beige to white"
msgstr "Verticale sfumato da beige a bianco"

#: theme.json
msgctxt "Duotone name"
msgid "Black and pastel blue"
msgstr "Nero e celeste"

#: theme.json
msgctxt "Duotone name"
msgid "Black and sage"
msgstr "Nero e salvia"

#: theme.json
msgctxt "Duotone name"
msgid "Black and rust"
msgstr "Nero e ruggine"

#: theme.json
msgctxt "Duotone name"
msgid "Black and sandstone"
msgstr "Nero e arenaria"

#: theme.json
msgctxt "Duotone name"
msgid "Black and white"
msgstr "Nero e bianco"

#: styles/rust.json
msgctxt "Color name"
msgid "Base / 2"
msgstr "Base / 2"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical hard rust to beige"
msgstr "Verticale staccato da color ruggine a beige"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical rust to beige"
msgstr "Verticale da color ruggine a beige"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical hard transparent rust to beige"
msgstr "Verticale staccato da color ruggine trasparente a beige"

#: styles/rust.json
msgctxt "Gradient name"
msgid "Vertical transparent rust to beige"
msgstr "Verticale da color ruggine trasparente a beige"

#: styles/rust.json
msgctxt "Duotone name"
msgid "Dark rust to beige"
msgstr "Da ruggine scuro a beige"

#: styles/rust.json
msgctxt "Style variation name"
msgid "Rust"
msgstr "Ruggine"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Five"
msgstr "In risalto / cinque"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Four"
msgstr "In risalto / quattro"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Three"
msgstr "In risalto / tre"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent / Two"
msgstr "In risalto / due"

#: styles/onyx.json theme.json
msgctxt "Color name"
msgid "Accent"
msgstr "In risalto"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard pewter to dark gray"
msgstr "Verticale staccato da color peltro a grigio scuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard steel to dark gray"
msgstr "Verticale staccato da acciaio a grigio scuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard olive to dark gray"
msgstr "Verticale staccato da color oliva a grigio scuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard cinnamon to dark gray"
msgstr "Verticale staccato da color cannella a grigio scuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard walnut to dark gray"
msgstr "Verticale staccato da color noce a grigio scuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical hard beige to dark gray"
msgstr "Verticale staccato da beige a grigio scuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft pewter to dark gray"
msgstr "Verticale sfumato da color peltro a grigio scuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft steel to dark gray"
msgstr "Verticale sfumato da acciaio a grigio scuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft olive to dark gray"
msgstr "Verticale sfumato da color oliva a grigio scuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft cinnamon to dark gray"
msgstr "Verticale sfumato da color cannella a grigio scuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft walnut to dark gray"
msgstr "Verticale sfumato da noce a grigio scuro"

#: styles/onyx.json
msgctxt "Gradient name"
msgid "Vertical soft driftwood to dark gray"
msgstr "Verticale sfumato da color legno a grigio scuro"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and steel"
msgstr "Grigio scuro e acciaio"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and olive"
msgstr "Grigio scuro e verde oliva"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and cinnamon"
msgstr "Grigio scuro e rosso cannella"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and walnut"
msgstr "Grigio scuro e noce"

#: styles/onyx.json
msgctxt "Duotone name"
msgid "Dark gray and white"
msgstr "Grigio scuro e bianco"

#: styles/onyx.json
msgctxt "Style variation name"
msgid "Onyx"
msgstr "Onyx"

#: styles/mint.json
msgctxt "Style variation name"
msgid "Mint"
msgstr "Mint"

#: styles/maelstrom.json
msgctxt "Color name"
msgid "Contrast / 3"
msgstr "Contrasto / 3"

#: styles/maelstrom.json
msgctxt "Style variation name"
msgid "Maelstrom"
msgstr "Maelstrom"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ink to ocean"
msgstr "Verticale staccato da color inchiostro a color ardesia"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ocean to slate"
msgstr "Verticale staccato da color oceano a color ardesia"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ink to ice"
msgstr "Verticale staccato da color inchiostro a color ghiaccio"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ocean to ice"
msgstr "Verticale staccato da color oceano a color ghiaccio"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard slate to ice"
msgstr "Verticale staccato da color ardesia a ghiaccio"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical hard ice to azure"
msgstr "Verticale staccato da color ghiaccio ad azzurro"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical ink to ocean"
msgstr "Verticale da color inchiostro a color oceano"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical ocean to slate"
msgstr "Verticale da color oceano a color ardesia"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical ink to ice"
msgstr "Verticale da color inchiostro a color ghiaccio"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical ocean to ice"
msgstr "Verticale da color oceano a color ghiaccio"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical slate to ice"
msgstr "Verticale da color ardesia a color ghiaccio"

#: styles/ice.json
msgctxt "Gradient name"
msgid "Vertical azure to ice"
msgstr "Verticale da azzurro a ghiaccio"

#: styles/ice.json
msgctxt "Style variation name"
msgid "Ice"
msgstr "Ice"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Extra Extra Large"
msgstr "Grandissimo"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "Molto grande"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Large"
msgstr "Grande"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Medium"
msgstr "Medio"

#: styles/fossil.json styles/ice.json styles/maelstrom.json theme.json
msgctxt "Font size name"
msgid "Small"
msgstr "Piccolo"

#: styles/fossil.json styles/maelstrom.json theme.json
msgctxt "Font family name"
msgid "Cardo"
msgstr "Cardo"

#: styles/fossil.json styles/ice.json theme.json
msgctxt "Font family name"
msgid "Inter"
msgstr "Inter"

#: styles/fossil.json styles/ice.json styles/mint.json styles/onyx.json
#: theme.json
msgctxt "Color name"
msgid "Contrast / Three"
msgstr "Contrasto / tre"

#: styles/fossil.json styles/ice.json styles/mint.json styles/onyx.json
#: theme.json
msgctxt "Color name"
msgid "Contrast / Two"
msgstr "Contrasto / due"

#: styles/fossil.json
msgctxt "Style variation name"
msgid "Fossil"
msgstr "Fossil"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json theme.json
msgctxt "Font family name"
msgid "System Serif"
msgstr "System Serif"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json theme.json
msgctxt "Font family name"
msgid "System Sans-serif"
msgstr "System Sans-serif"

#: styles/ember.json styles/ice.json styles/maelstrom.json styles/mint.json
msgctxt "Font family name"
msgid "Jost"
msgstr "Jost"

#: styles/ember.json styles/mint.json
msgctxt "Font family name"
msgid "Instrument Sans"
msgstr "Instrument Sans"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json styles/onyx.json theme.json
msgctxt "Color name"
msgid "Base / Two"
msgstr "Base / due"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json styles/onyx.json styles/rust.json theme.json
msgctxt "Color name"
msgid "Contrast"
msgstr "Contrasto"

#: styles/ember.json styles/maelstrom.json
msgctxt "Color name"
msgid "Contrast / 2"
msgstr "Contrasto / 2"

#: styles/ember.json styles/fossil.json styles/ice.json styles/maelstrom.json
#: styles/mint.json styles/onyx.json styles/rust.json theme.json
msgctxt "Color name"
msgid "Base"
msgstr "Base"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard ebony to sable"
msgstr "Verticale staccato da color ebano a marrone scuro"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard sable to taupe"
msgstr "Verticale staccato da marrone scuro a grigio talpa"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard ebony to beige"
msgstr "Verticale staccato da color ebano a beige"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard sable to beige"
msgstr "Verticale staccato da marrone scuro a beige"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard taupe to beige"
msgstr "Verticale staccato da grigio talpa a beige"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical hard beige to linen"
msgstr "Verticale staccato da beige a lino"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical ebony to sable"
msgstr "Verticale da color ebano a marrone scuro"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical ebony to beige"
msgstr "Verticale da color ebano a beige"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical sable to beige"
msgstr "Verticale da marrone scuro a beige"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical taupe to beige"
msgstr "Verticale da grigio talpa a beige"

#: styles/ember.json styles/fossil.json
msgctxt "Gradient name"
msgid "Vertical linen to beige"
msgstr "Verticale da color lino a beige"

#: styles/ember.json
msgctxt "Duotone name"
msgid "Orange and white"
msgstr "Arancione e bianco"

#: styles/ember.json
msgctxt "Style variation name"
msgid "Ember"
msgstr "Ember"

#: patterns/text-title-left-image-right.php:35
msgctxt "Call to Action button text"
msgid "About us"
msgstr "Chi siamo"

#: patterns/text-title-left-image-right.php:28
msgctxt "Description for the About pattern"
msgid "Leaving an indelible mark on the landscape of tomorrow."
msgstr "Lasciare un segno indelebile nel paesaggio di domani."

#: patterns/text-title-left-image-right.php:21
msgctxt "Headline for the About pattern"
msgid "Études offers comprehensive consulting, management, design, and research solutions. Every architectural endeavor is an opportunity to shape the future."
msgstr "Études offre soluzioni complete di consulenza, gestione, progettazione e ricerca. Ogni progetto architettonico è un'opportunità per plasmare il futuro."

#: patterns/text-title-left-image-right.php
msgctxt "Pattern title"
msgid "Title text and button on left with image on right"
msgstr "Testo del titolo e pulsante sulla sinistra con immagine sulla destra"

#: patterns/text-project-details.php:35 patterns/text-project-details.php:43
msgctxt "Descriptive text for the feature area"
msgid "The revitalized Art Gallery is set to redefine the cultural landscape of Toronto, serving as a nexus of artistic expression, community engagement, and architectural marvel. The expansion and renovation project pay homage to the Art Gallery's rich history while embracing the future, ensuring that the gallery remains a beacon of inspiration."
msgstr "La rinnovata Art Gallery ha il compito di ridefinire l'orizzonte culturale di Toronto, fungendo da punto di incontro tra espressione artistica, coinvolgimento della comunità e meraviglia architettonica. Il progetto di espansione e di ristrutturazione rende omaggio alla ricca storia dell'Art Gallery abbracciandone allo stesso tempo il futuro, assicurando che la galleria rimanga un faro di ispirazione."

#: patterns/text-project-details.php:27
msgctxt "Descriptive title for the feature area"
msgid "With meticulous attention to detail and a commitment to excellence, we create spaces that inspire, elevate, and enrich the lives of those who inhabit them."
msgstr "Con meticolosa attenzione ai dettagli e una devozione all'eccellenza, creiamo spazi che ispirano, elevano e arricchiscono le vite di chi li abita."

#: patterns/text-project-details.php:18
msgctxt "Title text for the feature area"
msgid "The revitalized art gallery is set to redefine cultural landscape."
msgstr "La rivitalizzata Art Gallery ha lo scopo di ridefinire il paesaggio culturale."

#: patterns/text-project-details.php
msgctxt "Pattern title"
msgid "Project details"
msgstr "Dettagli del progetto"

#: patterns/text-feature-grid-3-col.php:112
msgctxt "Sample content"
msgid "Experience the fusion of imagination and expertise with Études Architectural Solutions."
msgstr "Prova la fusione di immaginazione e competenza con Études Architectural Solutions."

#: patterns/text-feature-grid-3-col.php:108
msgctxt "Sample heading"
msgid "Architectural Solutions"
msgstr "Soluzioni d'architettura"

#: patterns/text-feature-grid-3-col.php:96
msgctxt "Sample feature heading"
msgid "Project Management"
msgstr "Gestione di progetti"

#: patterns/text-feature-grid-3-col.php:84
msgctxt "Sample feature heading"
msgid "Consulting"
msgstr "Consulenza"

#: patterns/text-feature-grid-3-col.php:63
msgctxt "Sample feature heading"
msgid "App Access"
msgstr "Accesso alla nostra app"

#: patterns/text-feature-grid-3-col.php:51
msgctxt "Sample feature heading"
msgid "Continuous Support"
msgstr "Supporto continuo"

#: patterns/text-feature-grid-3-col.php:43
#: patterns/text-feature-grid-3-col.php:55
#: patterns/text-feature-grid-3-col.php:67
#: patterns/text-feature-grid-3-col.php:88
#: patterns/text-feature-grid-3-col.php:100
msgctxt "Sample feature content"
msgid "Experience the fusion of imagination and expertise with Études Architectural Solutions."
msgstr "Prova la fusione di immaginazione e competenza con Études Architectural Solutions."

#: patterns/text-feature-grid-3-col.php:39
msgctxt "Sample feature heading"
msgid "Renovation and restoration"
msgstr "Ristrutturazione e restauro"

#: patterns/text-feature-grid-3-col.php:24
msgctxt "Sub-heading of the features"
msgid "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers."
msgstr "La nostra gamma completa di servizi professionali si rivolge a una clientela eterogenea, che va dai proprietari di case agli operatori commerciali."

#: patterns/text-feature-grid-3-col.php:16
msgctxt "Heading of the features"
msgid "A passion for creating spaces"
msgstr "Una passione per la creazione di spazi"

#: patterns/text-feature-grid-3-col.php
msgctxt "Pattern title"
msgid "Feature grid, 3 columns"
msgstr "Griglia in evidenza, 3 colonne"

#: patterns/text-faq.php:57
msgctxt "Question in the FAQ pattern"
msgid "Can I apply to be a part of the team or work as a contractor?"
msgstr "Posso candidarmi per essere parte del team o lavorare come appaltatore?"

#: patterns/text-faq.php:46
msgctxt "Question in the FAQ pattern"
msgid "I'd like to get to meet fellow architects, how can I do that?"
msgstr "Vorrei incontrare altri colleghi architetti, come posso fare?"

#: patterns/text-faq.php:27 patterns/text-faq.php:38 patterns/text-faq.php:49
#: patterns/text-faq.php:60
msgctxt "Answer in the FAQ pattern"
msgid "Études offers comprehensive consulting, management, design, and research solutions. Our vision is to be at the forefront of architectural innovation, fostering a global community of architects and enthusiasts united by a passion for creating spaces. Every architectural endeavor is an opportunity to shape the future."
msgstr "Études offre soluzioni complete di consulenza, management, design e ricerca. La nostra visione è di essere all'avanguardia dell'innovazione nell'architettura, e di promuovere la comunità globale di architetti ed entusiasti uniti da una passione per la creazione degli spazi. Ogni impresa nell'architettura è un'opportunità per dare forma al futuro."

#: patterns/text-faq.php:24
msgctxt "Question in the FAQ pattern"
msgid "What is your process working in smaller projects?"
msgstr "Come procedi per lavorare su progetti più piccoli?"

#: patterns/text-faq.php:15
msgctxt "Heading of the FAQs"
msgid "FAQs"
msgstr "FAQ"

#: patterns/text-faq.php:12
msgctxt "Name of the FAQ pattern"
msgid "FAQs"
msgstr "FAQ"

#: patterns/text-faq.php
msgctxt "Pattern title"
msgid "FAQ"
msgstr "FAQ"

#: patterns/text-centered-statement.php:21
msgid "<em>Études</em> is not confined to the past—we are passionate about the cutting edge designs shaping our world today."
msgstr "<em>Études</em> non è confinata nel passato—abbiamo una passione per i design all'avanguardia che danno forma al nostro mondo oggi."

#: patterns/text-centered-statement.php
msgctxt "Pattern title"
msgid "Centered statement"
msgstr "Affermazione allineata al centro"

#. Translators: About text placeholder
#: patterns/text-centered-statement-small.php:23
msgid "I write about finance, management and economy, my book “%1$s” is out now."
msgstr "Scrivo di finanza, management ed economia, il mio libro “%1$s” è appena stato pubblicato."

#. Translators: About link placeholder
#: patterns/text-centered-statement-small.php:20
msgid "Money Studies"
msgstr "Studi monetari"

#: patterns/text-centered-statement-small.php
msgctxt "Pattern title"
msgid "Centered statement, small"
msgstr "Affermazione allineata al centro, piccola"

#: patterns/text-alternating-images.php:105
msgctxt "Sample list item"
msgid "Exclusive access to design insights."
msgstr "Accesso esclusivo a utili informazioni sul design."

#: patterns/text-alternating-images.php:101
msgctxt "Sample list item"
msgid "Case studies that celebrate architecture."
msgstr "Casi di studio che esaltano l'architettura."

#: patterns/text-alternating-images.php:97
msgctxt "Sample list item"
msgid "A world of thought-provoking articles."
msgstr "Un mondo di articoli che stimolano il pensiero."

#: patterns/text-alternating-images.php:91
msgctxt "Sample heading"
msgid "Études Newsletter"
msgstr "Newsletter Études"

#: patterns/text-alternating-images.php:82
msgid "Windows of a building in Nuremberg, Germany"
msgstr "Finestre di un edificio a Norimberga, in Germania"

#: patterns/text-alternating-images.php:64
msgid "Tourist taking photo of a building"
msgstr "Turista che scatta foto di un edificio"

#: patterns/text-alternating-images.php:37
msgctxt "Sample list heading"
msgid "Études Architect App"
msgstr "App Études Architect"

#: patterns/text-alternating-images.php:23
msgctxt "Sample subheading content"
msgid "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers."
msgstr "La nostra gamma completa di servizi professionali si rivolge a una clientela eterogenea, che va dai proprietari di case agli operatori commerciali."

#: patterns/text-alternating-images.php:19
msgctxt "Sample heading content"
msgid "An array of resources"
msgstr "Una varietà di risorse"

#: patterns/text-alternating-images.php
msgctxt "Pattern title"
msgid "Text with alternating images"
msgstr "Testo con immagini alternate"

#: patterns/testimonial-centered.php:39
msgctxt "Designation of Person Provided Testimonial"
msgid "CEO, Greenprint"
msgstr "Greenprint CEO"

#: patterns/testimonial-centered.php:35
msgctxt "Name of Person Provided the Testimonial"
msgid "Annie Steiner"
msgstr "Annie Steiner"

#: patterns/testimonial-centered.php:26
msgctxt "Name of testimonial citation group"
msgid "Testimonial source"
msgstr "Origine delle testimonianze"

#: patterns/testimonial-centered.php:18
msgctxt "Testimonial Text or Review Text Got From the Person"
msgid "“Études has saved us thousands of hours of work and has unlocked insights we never thought possible.”"
msgstr "“Études ci ha fatto risparmiare migliaia di ore di lavoro, dandoci accesso a prospettive che finora non avevamo mai considerato possibili.”"

#: patterns/testimonial-centered.php:12
msgctxt "Name of testimonial pattern"
msgid "Testimonial"
msgstr "Testimonianze"

#: patterns/testimonial-centered.php
msgctxt "Pattern title"
msgid "Centered testimonial"
msgstr "Testimonianza allineata al centro"

#: patterns/template-single-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio single post template"
msgstr "Modello del singolo articolo del portfolio"

#: patterns/template-search-blogging.php
msgctxt "Pattern title"
msgid "Blogging search template"
msgstr "Modello della ricerca nel blog"

#: patterns/template-search-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio search template"
msgstr "Modello della ricerca nel portfolio"

#: patterns/template-index-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio index template"
msgstr "Modello delle pagine indice del portfolio"

#: patterns/template-index-blogging.php
msgctxt "Pattern title"
msgid "Blogging index template"
msgstr "Modello delle pagine indice del blog"

#: patterns/template-home-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio home template with post featured images"
msgstr "Modello della home del portfolio con articoli formato immagine in evidenza"

#: patterns/template-home-business.php
msgctxt "Pattern title"
msgid "Business home template"
msgstr "Modello della home in versione business"

#: patterns/template-home-blogging.php
msgctxt "Pattern title"
msgid "Blogging home template"
msgstr "Modello della home del blog"

#: patterns/template-archive-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio archive template"
msgstr "Modello delle pagine archivio del portfolio"

#: patterns/template-archive-blogging.php
msgctxt "Pattern title"
msgid "Blogging archive template"
msgstr "Modello delle pagine archivio del blog"

#: patterns/team-4-col.php:121
msgctxt "Sample role of a team member"
msgid "Project Manager"
msgstr "Responsabile di progetto"

#: patterns/team-4-col.php:116
msgctxt "Sample name of a team member"
msgid "Ivan Lawrence"
msgstr "Ivan Lawrence"

#: patterns/team-4-col.php:97
msgctxt "Sample role of a team member"
msgid "Architect"
msgstr "Architetti"

#: patterns/team-4-col.php:92
msgctxt "Sample name of a team member"
msgid "Helga Steiner"
msgstr "Helga Steiner"

#: patterns/team-4-col.php:73
msgctxt "Sample role of a team member"
msgid "Engineering Manager"
msgstr "Responsabile di progettazione"

#: patterns/team-4-col.php:68
msgctxt "Sample name of a team member"
msgid "Rhye Moore"
msgstr "Rhye Moore"

#: patterns/team-4-col.php:49
msgctxt "Sample role of a team member"
msgid "Founder, CEO & Architect"
msgstr "Fondatori, CEO e architetti"

#: patterns/team-4-col.php:44
msgctxt "Sample name of a team member"
msgid "Francesca Piovani"
msgstr "Francesca Piovani"

#: patterns/team-4-col.php:20
msgctxt "Sample descriptive text of the team pattern"
msgid "Our comprehensive suite of professionals caters to a diverse team, ranging from seasoned architects to renowned engineers."
msgstr "Il nostro ampio gruppo di professionisti si rivolge a team eterogeneo, che va da architetti di grande esperienza a rinomati ingegneri."

#: patterns/team-4-col.php:16
msgctxt "Sample heading for the team pattern"
msgid "Meet our team"
msgstr "Incontra il nostro team"

#: patterns/team-4-col.php:11
msgctxt "Name of team pattern"
msgid "Team members"
msgstr "Membri del team"

#: patterns/team-4-col.php
msgctxt "Pattern title"
msgid "Team members, 4 columns"
msgstr "Team, 4 colonne"

#: patterns/posts-images-only-offset-4-col.php
msgctxt "Pattern title"
msgid "Offset posts with featured images only, 4 columns"
msgstr "Articoli sfalsati con le sole immagini in evidenza, 4 colonne"

#: patterns/posts-images-only-3-col.php
msgctxt "Pattern title"
msgid "Posts with featured images only, 3 columns"
msgstr "Articoli con le sole immagini in evidenza, 3 colonne"

#: patterns/posts-grid-2-col.php:14 patterns/posts-list.php:14
#: patterns/template-index-blogging.php:16
msgid "Watch, Read, Listen"
msgstr "Guarda, leggi, ascolta"

#: patterns/posts-grid-2-col.php
msgctxt "Pattern title"
msgid "Grid of posts featuring the first post, 2 columns"
msgstr "Griglia di articoli con in evidenza il primo, 2 colonne"

#: patterns/posts-3-col.php
msgctxt "Pattern title"
msgid "List of posts, 3 columns"
msgstr "Elenco di articoli in tre colonne"

#: patterns/posts-1-col.php
msgctxt "Pattern title"
msgid "List of posts, 1 column"
msgstr "Elenco di articoli in una colonna"

#: patterns/page-rsvp-landing.php:49
msgid "Green staircase at Western University, London, Canada"
msgstr "Una scala verde alla Western University, London, Canada"

#: patterns/page-rsvp-landing.php:14
msgctxt "Name of RSVP landing page pattern"
msgid "RSVP Landing Page"
msgstr "Landing page RSVP"

#: patterns/page-rsvp-landing.php
msgctxt "Pattern title"
msgid "RSVP landing"
msgstr "Landing page RSVP"

#: patterns/page-portfolio-overview.php
msgctxt "Pattern title"
msgid "Portfolio project overview"
msgstr "Anteprima del progetto in portfolio"

#: patterns/page-newsletter-landing.php:40
msgctxt "Sample content for newsletter subscribe button"
msgid "Sign up"
msgstr "Iscriviti"

#: patterns/page-newsletter-landing.php:29
msgctxt "sample content for newsletter subscription"
msgid "Subscribe to the newsletter and stay connected with our community"
msgstr "Iscriviti alla newslettere e resta in collegamento con la nostra community"

#: patterns/page-newsletter-landing.php
msgctxt "Pattern title"
msgid "Newsletter landing"
msgstr "Landing page della newsletter"

#: patterns/page-home-portfolio.php
msgctxt "Pattern title"
msgid "Portfolio home with post featured images"
msgstr "Home del portfolio con immagini in evidenza degli articoli"

#: patterns/page-home-portfolio-gallery.php
msgctxt "Pattern title"
msgid "Portfolio home image gallery"
msgstr "Galleria immagini per l'home del portfolio"

#: patterns/page-home-business.php
msgctxt "Pattern title"
msgid "Business home"
msgstr "Home versione business"

#: patterns/page-home-blogging.php
msgctxt "Pattern title"
msgid "Blogging home"
msgstr "Home con blog"

#: patterns/page-about-business.php
msgctxt "Pattern title"
msgid "About"
msgstr "Chi siamo"

#: patterns/hidden-sidebar.php:75
msgctxt "search form placeholder"
msgid "Search..."
msgstr "Cerca…"

#: patterns/hidden-sidebar.php:72
msgid "Search the website"
msgstr "Cerca nel sito web"

#: patterns/hidden-sidebar.php:60
msgid "Financial apps for families"
msgstr "App finanziarie per famiglie"

#: patterns/hidden-sidebar.php:59
msgid "Latest inflation report"
msgstr "L'ultimo rapporto sull'inflazione"

#: patterns/hidden-sidebar.php:53
msgid "Links I found useful and wanted to share."
msgstr "Link che ho ritenuto utili e che ho voluto condividere."

#: patterns/hidden-sidebar.php:49
msgid "Useful Links"
msgstr "Link utili"

#: patterns/hidden-sidebar.php:33
msgid "Popular Categories"
msgstr "Categorie popolari"

#: patterns/hidden-sidebar.php:17
msgid "About the author"
msgstr "Info sull'autore"

#: patterns/hidden-sidebar.php
msgctxt "Pattern title"
msgid "Sidebar"
msgstr "Barra laterale"

#: patterns/hidden-search.php:9
msgctxt "search button text"
msgid "Search"
msgstr "Cerca"

#: patterns/hidden-search.php:9 patterns/hidden-sidebar.php:75
msgctxt "search form label"
msgid "Search"
msgstr "Cerca"

#: patterns/hidden-search.php
msgctxt "Pattern title"
msgid "Search"
msgstr "Ricerca"

#: patterns/hidden-post-navigation.php:12
msgctxt "Label before the title of the next post. There is a space after the colon."
msgid "Next: "
msgstr "Successivo: "

#: patterns/hidden-post-navigation.php:11
msgctxt "Label before the title of the previous post. There is a space after the colon."
msgid "Previous: "
msgstr "Precedente: "

#: patterns/hidden-post-navigation.php:9 patterns/hidden-post-navigation.php:10
#: patterns/hidden-posts-heading.php:10
#: patterns/template-index-portfolio.php:16
msgid "Posts"
msgstr "Articoli"

#: patterns/hidden-post-navigation.php
msgctxt "Pattern title"
msgid "Post navigation"
msgstr "Navigazione articoli"

#: patterns/hidden-post-meta.php:25
msgctxt "Prefix for the post category block: in category name"
msgid "in "
msgstr "in "

#: patterns/hidden-post-meta.php:20
msgctxt "Prefix for the post author block: By author name"
msgid "by"
msgstr "da"

#: patterns/hidden-post-meta.php
msgctxt "Pattern title"
msgid "Post meta"
msgstr "Metadati articolo"

#: patterns/hidden-portfolio-hero.php
msgctxt "Pattern title"
msgid "Portfolio hero"
msgstr "Portfolio con immagine hero"

#: patterns/hidden-no-results.php:9
msgctxt "Message explaining that there are no results returned from a search"
msgid "No posts were found."
msgstr "Nessun articolo è stato trovato."

#: patterns/hidden-no-results.php
msgctxt "Pattern title"
msgid "No results"
msgstr "Nessun risultato"

#: patterns/hidden-comments.php:12
msgid "Comments"
msgstr "Commenti"

#: patterns/hidden-comments.php
msgctxt "Pattern title"
msgid "Comments"
msgstr "Commenti"

#: patterns/hidden-404.php:13
msgctxt "Message to convey that a webpage could not be found"
msgid "The page you are looking for does not exist, or it has been moved. Please try searching using the form below."
msgstr "La pagina che stai cercando non esiste o è stata spostata. Prova con una nuova ricerca usando il modulo qui sotto."

#: patterns/hidden-404.php:10
msgctxt "Heading for a webpage that is not found"
msgid "Page Not Found"
msgstr "Pagina non trovata"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "404"
msgstr "404"

#: patterns/gallery-project-layout.php:54
msgid "Art Gallery of Ontario, Toronto, Canada"
msgstr "Art Gallery of Ontario, Toronto, Canada"

#: patterns/gallery-project-layout.php:49
msgctxt "Sample text for the feature area"
msgid "2. Case studies that celebrate the artistry can fuel curiosity and ignite inspiration."
msgstr "2. Casi di studio che celebrano la maestria possono alimentare la curiosità e accendere l'ispirazione."

#: patterns/gallery-project-layout.php:38
msgctxt "Sample text for the feature area"
msgid "Our comprehensive suite of professional services caters to a diverse clientele, ranging from homeowners to commercial developers. With a commitment to innovation and sustainability, Études is the bridge that transforms architectural dreams into remarkable built realities."
msgstr "La nostra gamma completa di servizi professionali si rivolge a una clientela eterogenea, che va dai proprietari di case agli operatori commerciali. Con un impegno verso l'innovazione e la sostenibilità, Études è il ponte che trasforma sogni architettonici in straordinarie realtà costruite."

#: patterns/gallery-project-layout.php:26
msgctxt "Sample text for the feature area"
msgid "1. Through Études, we aspire to redefine architectural boundaries and usher in a new era of design excellence that leaves an indelible mark on the built environment."
msgstr "1. Attraverso Études, aspiriamo a ridefinire i confini dell'architettura e dare inizio a una nuova era di eccellenza nel design che lasci un marchio indelebile nell'ambiente delle costruzioni."

#: patterns/gallery-project-layout.php:21
msgid "An empty staircase under an angular roof in Darling Harbour, Sydney, Australia"
msgstr "Una scala vuota sotto un tetto ad angolo a Darling Harbour, Sydney, Australia"

#: patterns/gallery-project-layout.php
msgctxt "Pattern title"
msgid "Project layout"
msgstr "Layout del progetto"

#: patterns/gallery-offset-images-grid-4-col.php
msgctxt "Pattern title"
msgid "Offset gallery, 4 columns"
msgstr "Galleria con immagini sfalsate, 4 colonne"

#: patterns/gallery-offset-images-grid-3-col.php
msgctxt "Pattern title"
msgid "Offset gallery, 3 columns"
msgstr "Galleria con immagini sfalsate, 3 colonne"

#: patterns/gallery-offset-images-grid-2-col.php
msgctxt "Pattern title"
msgid "Offset gallery, 2 columns"
msgstr "Galleria con immagini sfalsate, 2 colonne"

#: patterns/gallery-full-screen-image.php
msgctxt "Pattern title"
msgid "Full screen image"
msgstr "Immagine a schermo intero"

#: patterns/footer.php:92
msgid "Social Media"
msgstr "Social media"

#: patterns/footer.php:86
msgid "Social"
msgstr "Social"

#: patterns/footer.php:64 patterns/footer.php:70
msgid "Privacy"
msgstr "Privacy"

#: patterns/footer.php:41 patterns/footer.php:47
msgid "About"
msgstr "Chi siamo"

#: patterns/footer.php
msgctxt "Pattern title"
msgid "Footer with colophon, 4 columns"
msgstr "Footer con colophon, 4 colonne"

#: patterns/footer-colophon-3-col.php:82
msgid "&copy;"
msgstr "&copy;"

#: patterns/footer-colophon-3-col.php:60 patterns/footer.php:94
msgid "Facebook"
msgstr "Facebook"

#: patterns/footer-colophon-3-col.php:60 patterns/footer.php:95
msgid "Instagram"
msgstr "Instagram"

#: patterns/footer-colophon-3-col.php:57
msgid "Follow"
msgstr "Segui"

#: patterns/footer-colophon-3-col.php:42
msgctxt "Example email in site footer"
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: patterns/footer-colophon-3-col.php:39
msgid "Contact"
msgstr "Contatti"

#: patterns/footer-colophon-3-col.php:30
msgid "Keep up, get in touch."
msgstr "Rimaniamo in contatto."

#: patterns/footer-colophon-3-col.php
msgctxt "Pattern title"
msgid "Footer with colophon, 3 columns"
msgstr "Footer con colophon, 3 colonne"

#. Translators: Designed with WordPress
#: patterns/footer-centered-logo-nav.php:25
#: patterns/footer-colophon-3-col.php:94 patterns/footer.php:120
msgid "Designed with %1$s"
msgstr "Progettato con %1$s"

#: patterns/footer-centered-logo-nav.php
msgctxt "Pattern title"
msgid "Footer with centered logo and navigation"
msgstr "Footer con logo e navigazione allineati al centro"

#: patterns/cta-subscribe-centered.php:31
msgctxt "Sample text for Sign Up Button"
msgid "Sign up"
msgstr "Iscriviti"

#: patterns/cta-subscribe-centered.php:24
msgctxt "Sample text for Subscriber Description"
msgid "Stay in the loop with everything you need to know."
msgstr "Tieniti al corrente con tutto ciò che devi sapere."

#: patterns/cta-subscribe-centered.php:20
msgctxt "Sample text for Subscriber Heading with numbers"
msgid "Join 900+ subscribers"
msgstr "Unisciti a più di 900 iscritti"

#: patterns/cta-subscribe-centered.php
msgctxt "Pattern title"
msgid "Centered call to action"
msgstr "Invito all'azione allineato al centro"

#: patterns/cta-services-image-left.php:39
msgctxt "Sample button text to view the services"
msgid "Our services"
msgstr "I nostri servizi"

#: patterns/cta-services-image-left.php:32
msgctxt "Sample description of the services pattern"
msgid "Experience the fusion of imagination and expertise with Études—the catalyst for architectural transformations that enrich the world around us."
msgstr "Sperimenta la fusione dell'immaginazione e dell'esperienza con Études - il catalizzatore per la trasformazione nel campo dell'architettura che arricchisce il mondo attorno a noi."

#: patterns/cta-services-image-left.php:28
msgctxt "Sample heading of the services pattern"
msgid "Guiding your business through the project"
msgstr "Guidare il tuo business attraverso il progetto"

#: patterns/cta-services-image-left.php
msgctxt "Pattern title"
msgid "Services call to action with image on left"
msgstr "Servizi - Invito all'azione con immagine a sinistra"

#: patterns/cta-rsvp.php:50 patterns/text-title-left-image-right.php:51
msgid "A ramp along a curved wall in the Kiasma Museu, Helsinki, Finland"
msgstr "Una rampa che segue un muro curvo al Kiasma Museu, Helsinki, Finlandia"

#: patterns/cta-rsvp.php:34 patterns/page-rsvp-landing.php:34
msgctxt "Call to action button text for the reservation button"
msgid "Reserve your spot"
msgstr "Prenota il tuo posto"

#: patterns/cta-rsvp.php:27 patterns/page-rsvp-landing.php:28
msgctxt "RSVP call to action description"
msgid "Experience the fusion of imagination and expertise with Études Arch Summit, February 2025."
msgstr "Prova la fusione di immaginazione e competenza con Études Arch Summit, a Febbraio 2025."

#: patterns/cta-rsvp.php:21 patterns/page-rsvp-landing.php:23
msgctxt "Initials for ´please respond´"
msgid "RSVP"
msgstr "RSVP"

#: patterns/cta-rsvp.php:11
msgctxt "Name of RSVP pattern"
msgid "RSVP"
msgstr "RSVP"

#: patterns/cta-rsvp.php
msgctxt "Pattern title"
msgid "RSVP"
msgstr "RSVP"

#: patterns/cta-pricing.php:203
msgctxt "Button text for the third pricing level"
msgid "Subscribe"
msgstr "Abbonati"

#: patterns/cta-pricing.php:189
msgctxt "Feature for pricing level"
msgid "Exclusive access to the <em>Études</em> app for iOS and Android"
msgstr "Accesso esclusivo all'app <em>Études</em> per iOS e Android"

#: patterns/cta-pricing.php:173
msgctxt "Feature for pricing level"
msgid "Exclusive, unlimited access to <em>Études Articles</em>."
msgstr "Accesso illimitato ed esclusivo agli articoli di <em>Études</em>."

#: patterns/cta-pricing.php:162
msgctxt "Sample price for the third pricing level"
msgid "$28"
msgstr "€28"

#: patterns/cta-pricing.php:157
msgctxt "Sample heading for the third pricing level"
msgid "Expert"
msgstr "Expert"

#: patterns/cta-pricing.php:145
msgctxt "Button text for the second pricing level"
msgid "Subscribe"
msgstr "Iscriviti"

#: patterns/cta-pricing.php:115
msgctxt "Feature for pricing level"
msgid "Access to 20 exclusive <em>Études Articles</em> per month."
msgstr "Accedi a 20 esclusivi articoli di <em>Études</em> ogni mese."

#: patterns/cta-pricing.php:104
msgctxt "Sample price for the second pricing level"
msgid "$12"
msgstr "€12"

#: patterns/cta-pricing.php:99
msgctxt "Sample heading for the second pricing level"
msgid "Connoisseur"
msgstr "Connoisseur"

#: patterns/cta-pricing.php:87
msgctxt "Button text for the first pricing level"
msgid "Subscribe"
msgstr "Abbonati"

#: patterns/cta-pricing.php:72 patterns/cta-pricing.php:131
msgctxt "Feature for pricing level"
msgid "Exclusive access to the <em>Études</em> app for iOS and Android."
msgstr "Accesso esclusivo all'app <em>Études</em> per iOS e Android."

#: patterns/cta-pricing.php:62 patterns/cta-pricing.php:123
#: patterns/cta-pricing.php:181
msgctxt "Feature for pricing level"
msgid "Weekly print edition."
msgstr "Edizione stampata settimanale."

#: patterns/cta-pricing.php:53
msgctxt "Feature for pricing level"
msgid "Access to 5 exclusive <em>Études Articles</em> per month."
msgstr "Accedi a 5 esclusivi articoli di <em>Études</em> ogni mese."

#: patterns/cta-pricing.php:42
msgctxt "Sample price for the first pricing level"
msgid "$0"
msgstr "€0"

#: patterns/cta-pricing.php:37
msgctxt "Sample heading for the first pricing level"
msgid "Free"
msgstr "Gratuito"

#: patterns/cta-pricing.php:22
msgctxt "Sample description for a pricing table"
msgid "We offer flexible options, which you can adapt to the different needs of each project."
msgstr "Offriamo opzioni flessibili, che puoi adattare alle diverse necessità di ogni progetto."

#: patterns/cta-pricing.php:18
msgctxt "Sample heading for pricing pattern"
msgid "Our Services"
msgstr "I nostri servizi"

#: patterns/cta-pricing.php:11
msgctxt "Name for the pricing pattern"
msgid "Pricing Table"
msgstr "Tabella dei prezzi"

#: patterns/cta-pricing.php
msgctxt "Pattern title"
msgid "Pricing"
msgstr "Prezzi"

#: patterns/cta-content-image-on-right.php:59
#: patterns/cta-services-image-left.php:19
msgid "White abstract geometric artwork from Dresden, Germany"
msgstr "Un'opera d'arte bianca, geometrica e astratta da Dresda, Germania"

#: patterns/cta-content-image-on-right.php:47
msgctxt "Button text of this section"
msgid "How it works"
msgstr "Come funziona"

#: patterns/cta-content-image-on-right.php:41
msgctxt "Button text of this section"
msgid "Download app"
msgstr "Scarica l'app"

#: patterns/cta-content-image-on-right.php:32
#: patterns/text-alternating-images.php:52
msgctxt "Sample list item"
msgid "Experience the world of architecture."
msgstr "Sperimenta il mondo dell'architettura."

#: patterns/cta-content-image-on-right.php:28
#: patterns/text-alternating-images.php:48
msgctxt "Sample list item"
msgid "Showcase your projects."
msgstr "Mostra i tuoi progetti."

#: patterns/cta-content-image-on-right.php:24
#: patterns/text-alternating-images.php:44
msgctxt "Sample list item"
msgid "Collaborate with fellow architects."
msgstr "Collabora con altri architetti."

#: patterns/cta-content-image-on-right.php:18
msgctxt "Sample heading"
msgid "Enhance your architectural journey with the Études Architect app."
msgstr "Fai progredire la tua scoperta dell'architettura con l'app Études Architect."

#: patterns/cta-content-image-on-right.php
msgctxt "Pattern title"
msgid "Call to action with image on right"
msgstr "Invito all'azione con immagine a destra"

#: patterns/banner-project-description.php:41
msgid "Hyatt Regency San Francisco, San Francisco, United States"
msgstr "Hyatt Regency San Francisco, San Francisco, Stati Uniti"

#: patterns/banner-project-description.php:26
msgctxt "Sample descriptive text for a project or post."
msgid "This transformative project seeks to enhance the gallery's infrastructure, accessibility, and exhibition spaces while preserving its rich cultural heritage."
msgstr "Questo progetto di trasformazione punta a migliorare l'infrastruttura e l'accessibilità della galleria e degli spazi espositivi, preservando allo stesso tempo il suo ricco retaggio culturale."

#: patterns/banner-project-description.php:17
msgctxt "Sample title for a project or post"
msgid "Art Gallery — Overview"
msgstr "Gallerie d'arte - Panoramica"

#: patterns/banner-project-description.php
msgctxt "Pattern title"
msgid "Project description"
msgstr "Descrizione del progetto"

#: patterns/banner-hero.php:52
msgid "Building exterior in Toronto, Canada"
msgstr "Esterno di un edificio a Toronto, Canada"

#: patterns/banner-hero.php:37
msgctxt "Button text of the hero section"
msgid "About us"
msgstr "Chi siamo"

#: patterns/banner-hero.php:26
msgctxt "Content of the hero section"
msgid "Études is a pioneering firm that seamlessly merges creativity and functionality to redefine architectural excellence."
msgstr "Études è un'azienda all'avanguardia che fonde armonicamente creatività e funzionalità per ridefinire l'eccellenza in architettura."

#: patterns/banner-hero.php:18
msgctxt "Heading of the hero section"
msgid "A commitment to innovation and sustainability"
msgstr "Un impegno verso innovazione e sostenibilità"

#: patterns/banner-hero.php
msgctxt "Pattern title"
msgid "Hero"
msgstr "Immagine hero"

#: functions.php:111
msgid "With asterisk"
msgstr "Con asterisco"

#: functions.php:93
msgid "With arrow"
msgstr "Con freccia"

#: functions.php:74
msgid "Checkmark"
msgstr "Segno di spunta"

#: functions.php:51
msgid "Pill"
msgstr "Pillola"

#: functions.php:28
msgid "Arrow icon"
msgstr "Icona freccia"

#. Author URI of the theme
#. Translators: WordPress link.
#: style.css patterns/footer-centered-logo-nav.php:22
#: patterns/footer-colophon-3-col.php:91 patterns/footer.php:117
msgid "https://wordpress.org"
msgstr "https://wordpress.org"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "il team di WordPress"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentyfour/"
msgstr "https://wordpress.org/themes/twentytwentyfour/"
