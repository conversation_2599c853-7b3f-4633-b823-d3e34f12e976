!function(){"use strict";var e={d:function(t,n){for(var l in n)e.o(n,l)&&!e.o(t,l)&&Object.defineProperty(t,l,{enumerable:!0,get:n[l]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{AspectRatioControl:function(){return jt},BackgroundControl:function(){return Mn},BoxShadowRepeaterControl:function(){return We},ColorControl:function(){return he},ControlActionsProvider:function(){return N},ControlAdornments:function(){return Ie},ControlAdornmentsProvider:function(){return Se},ControlFormLabel:function(){return U},ControlReplacementsProvider:function(){return G},ControlToggleButtonGroup:function(){return st},EqualUnequalSizesControl:function(){return gt},FilterRepeaterControl:function(){return et},FontFamilyControl:function(){return wt},FontFamilySelector:function(){return _t},GapControl:function(){return At},ImageControl:function(){return Q},KeyValueControl:function(){return Qn},LinkControl:function(){return Gt},LinkedDimensionsControl:function(){return ht},NumberControl:function(){return vt},PopoverContent:function(){return Ce},PositionControl:function(){return Jn},PropKeyProvider:function(){return S},PropProvider:function(){return C},RepeatableControl:function(){return jn},SelectControl:function(){return Y},SizeControl:function(){return pe},StrokeControl:function(){return xe},SvgMediaControl:function(){return cn},SwitchControl:function(){return $t},TextAreaControl:function(){return te},TextControl:function(){return ee},ToggleControl:function(){return dt},TransformRepeaterControl:function(){return sl},UrlControl:function(){return St},createControlReplacementsRegistry:function(){return L},injectIntoRepeaterItemIcon:function(){return Pe},injectIntoRepeaterItemLabel:function(){return $e},useBoundProp:function(){return P},useControlActions:function(){return K},useSyncExternalState:function(){return ue}});var n=window.React,l=window.elementorV2.editorProps,r=window.elementorV2.ui,a=window.wp.i18n,o=window.elementorV2.utils,i=window.elementorV2.query,c=window.elementorV2.httpClient,s=window.elementorV2.icons,u=window.elementorV2.wpMedia,m=window.elementorV2.editorUi,d=window.elementorV2.editorResponsive,p=window.elementorV2.editorV1Adapters,E=window.elementorV2.locations,v=window.elementorV2.editorElements,b=window.elementorV2.session,g=window.elementorV2.editorCurrentUser,f=window.elementorV2.env,h=(0,o.createError)({code:"missing_prop_provider_prop_type",message:"Prop type is missing"}),y=(0,o.createError)({code:"unsupported_prop_provider_prop_type",message:"Parent prop type is not supported"}),x=(0,o.createError)({code:"hook_outside_provider",message:"Hook used outside of provider"}),_=(0,n.createContext)(null),C=({children:e,value:t,setValue:l,propType:r,placeholder:a,isDisabled:o})=>n.createElement(_.Provider,{value:{value:t,propType:r,setValue:l,placeholder:a,isDisabled:o}},e),T=()=>{const e=(0,n.useContext)(_);if(!e)throw new x({context:{hook:"usePropContext",provider:"PropProvider"}});return e},w=(0,n.createContext)(null),S=({children:e,bind:t})=>{const{propType:l}=T();if(!l)throw new h({context:{bind:t}});if("array"===l.kind)return n.createElement(I,{bind:t},e);if("object"===l.kind)return n.createElement(z,{bind:t},e);throw new y({context:{propType:l}})},z=({children:e,bind:t})=>{const l=T(),{path:r}=(0,n.useContext)(w)??{},a=l.value?.[t],o=l.placeholder?.[t],i=l.propType.shape[t];return n.createElement(w.Provider,{value:{...l,value:a,setValue:(e,n,r)=>{const a={...l.value,[t]:e};return l?.setValue(a,n,{...r,bind:t})},placeholder:o,bind:t,propType:i,path:[...r??[],t]}},e)},I=({children:e,bind:t})=>{const l=T(),{path:r}=(0,n.useContext)(w)??{},a=l.value?.[Number(t)],o=l.propType.item_prop_type;return n.createElement(w.Provider,{value:{...l,value:a,setValue:(e,n)=>{const r=[...l.value??[]];return r[Number(t)]=e,l?.setValue(r,n,{bind:t})},bind:t,propType:o,path:[...r??[],t]}},e)},k=()=>{const e=(0,n.useContext)(w);if(!e)throw new x({context:{hook:"usePropKeyContext",provider:"PropKeyProvider"}});return e};function P(e){const t=k(),{isValid:n,validate:l,restoreValue:r}=V(t.propType),a=t.isDisabled?.(t.propType);if(!e)return{...t,disabled:a};const o=$(t.propType,e.key),i=e.extract(t.value??o.default??null),c=e.extract(t.placeholder??null);return{...t,propType:o,setValue:function(n,r,a){if(l(n))return null===n?t?.setValue(null,r,a):t?.setValue(e?.create(n,r),{},a)},value:n?i:null,restoreValue:r,placeholder:c,disabled:a}}var V=e=>{const[t,l]=(0,n.useState)(!0);return{isValid:t,setIsValid:l,validate:t=>{let n=!0;return e.settings.required&&null===t&&(n=!1),l(n),n},restoreValue:()=>l(!0)}},$=(e,t)=>{let n=e;if("union"===e.kind&&(n=e.prop_types[t]),!n)throw new h({context:{key:t}});return n},U=e=>n.createElement(r.FormLabel,{size:"tiny",...e}),R=(0,n.createContext)([]),G=({replacements:e,children:t})=>n.createElement(R.Provider,{value:e},t),L=()=>{const e=[];return{registerControlReplacement:function(t){e.push(t)},getControlReplacements:function(){return e}}};function O(e){return t=>{const l=(e=>{const{value:t}=P(),l=(0,n.useContext)(R);try{const n=l.find((e=>e.condition({value:t})));return n?.component??e}catch{return e}})(e);return n.createElement(r.ErrorBoundary,{fallback:null},n.createElement(l,{...t}))}}Symbol("control");var F="elementor/v1/settings",B=e=>e.data.value,A="elementor_unfiltered_files_upload",M={queryKey:[A]},W=()=>(0,i.useQuery)({...M,queryFn:()=>{return(e=A,(0,c.httpService)().get(`${F}/${e}`).then((e=>B(e.data)))).then((e=>D(e)));var e},staleTime:1/0}),D=e=>Boolean("1"===e),j=(0,n.createContext)(null),N=({children:e,items:t})=>n.createElement(j.Provider,{value:{items:t}},e),K=()=>{const e=(0,n.useContext)(j);if(!e)throw new Error("useControlActions must be used within a ControlActionsProvider");return e},H=(0,r.styled)("span")`
	display: contents;

	.MuiFloatingActionBar-popper:has( .MuiFloatingActionBar-actions:empty ) {
		display: none;
	}

	.MuiFloatingActionBar-popper {
		z-index: 1000;
	}
`;function q({children:e}){const{items:t}=K(),{disabled:l}=P();if(0===t.length||l)return e;const a=t.map((({MenuItem:e,id:t})=>n.createElement(e,{key:t})));return n.createElement(H,null,n.createElement(r.UnstableFloatingActionBar,{actions:a},e))}var X=O((({mediaTypes:e=["image"]})=>{const{value:t,setValue:o}=P(l.imageSrcPropTypeUtil),{id:i,url:c}=t??{},{data:m,isFetching:d}=(0,u.useWpMediaAttachment)(i?.value||null),p=m?.url??c?.value??null,{open:E}=(0,u.useWpMediaFrame)({mediaTypes:e,multiple:!1,selected:i?.value||null,onSelect:e=>{o({id:{$$type:"image-attachment-id",value:e.id},url:null})}});return n.createElement(q,null,n.createElement(r.Card,{variant:"outlined"},n.createElement(r.CardMedia,{image:p,sx:{height:150}},d?n.createElement(r.Stack,{justifyContent:"center",alignItems:"center",width:"100%",height:"100%"},n.createElement(r.CircularProgress,null)):n.createElement(n.Fragment,null)),n.createElement(r.CardOverlay,null,n.createElement(r.Stack,{gap:1},n.createElement(r.Button,{size:"tiny",color:"inherit",variant:"outlined",onClick:()=>E({mode:"browse"})},(0,a.__)("Select image","elementor")),n.createElement(r.Button,{size:"tiny",variant:"text",color:"inherit",startIcon:n.createElement(s.UploadIcon,null),onClick:()=>E({mode:"upload"})},(0,a.__)("Upload","elementor"))))))})),Y=O((({options:e,onChange:t})=>{const{value:a,setValue:o,disabled:i,placeholder:c}=P(l.stringPropTypeUtil);return n.createElement(q,null,n.createElement(r.Select,{sx:{overflow:"hidden"},displayEmpty:!0,size:"tiny",renderValue:t=>{const l=t=>e.find((e=>e.value===t));if(!t||""===t){if(c){const e=l(c),t=e?.label||c;return n.createElement(r.Typography,{component:"span",variant:"caption",color:"text.tertiary"},t)}return""}const a=l(t);return a?.label||t},value:a??"",onChange:e=>{const n=e.target.value||null;t?.(n,a),o(n)},disabled:i,fullWidth:!0},e.map((({label:e,...t})=>n.createElement(m.MenuListItem,{key:t.value,...t,value:t.value??""},e)))))})),Q=O((({sizes:e,showMode:t="all"})=>{const o=P(l.imagePropTypeUtil);let i;switch(t){case"media":i=n.createElement(Z,null);break;case"sizes":i=n.createElement(J,{sizes:e});break;default:i=n.createElement(r.Stack,{gap:1.5},n.createElement(U,null,(0,a.__)("Image","elementor")),n.createElement(Z,null),n.createElement(r.Grid,{container:!0,gap:1.5,alignItems:"center",flexWrap:"nowrap"},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(U,null,(0,a.__)("Resolution","elementor"))),n.createElement(r.Grid,{item:!0,xs:6,sx:{overflow:"hidden"}},n.createElement(J,{sizes:e}))))}return n.createElement(C,{...o},i)})),Z=()=>{const{data:e}=W(),t=e?["image","svg"]:["image"];return n.createElement(S,{bind:"src"},n.createElement(X,{mediaTypes:t}))},J=({sizes:e})=>n.createElement(S,{bind:"size"},n.createElement(Y,{options:e})),ee=O((({placeholder:e})=>{const{value:t,setValue:a,disabled:o}=P(l.stringPropTypeUtil);return n.createElement(q,null,n.createElement(r.TextField,{size:"tiny",fullWidth:!0,disabled:o,value:t??"",onChange:e=>a(e.target.value),placeholder:e}))})),te=O((({placeholder:e})=>{const{value:t,setValue:a,disabled:o}=P(l.stringPropTypeUtil);return n.createElement(q,null,n.createElement(r.TextField,{size:"tiny",multiline:!0,fullWidth:!0,minRows:5,disabled:o,value:t??"",onChange:e=>{a(e.target.value)},placeholder:e}))})),ne=["px","%","em","rem","vw","vh"],le=["auto","custom"];function re(e){return le.includes(e)}var ae=(0,n.forwardRef)((({placeholder:e,type:t,value:l,onChange:a,onBlur:o,onKeyDown:i,onKeyUp:c,shouldBlockInput:s=!1,inputProps:u,disabled:m},d)=>n.createElement(r.TextField,{ref:d,sx:{input:{cursor:s?"default !important":void 0}},size:"tiny",fullWidth:!0,type:s?void 0:t,value:l,onChange:s?void 0:a,onKeyDown:s?void 0:i,onKeyUp:s?void 0:c,disabled:m,onBlur:o,placeholder:e,InputProps:u}))),oe=({options:e,alternativeOptionLabels:t={},onClick:l,value:a,menuItemsAttributes:o={},disabled:i})=>{const c=(0,r.usePopupState)({variant:"popover",popupId:(0,n.useId)()});return n.createElement(r.InputAdornment,{position:"end"},n.createElement(r.Button,{size:"small",color:"secondary",disabled:i,sx:{font:"inherit",minWidth:"initial",textTransform:"uppercase"},...(0,r.bindTrigger)(c)},t[a]??a),n.createElement(r.Menu,{MenuListProps:{dense:!0},...(0,r.bindMenu)(c)},e.map(((r,a)=>n.createElement(m.MenuListItem,{key:r,onClick:()=>(t=>{l(e[t]),c.close()})(a),...o?.[r]},t[r]??r.toUpperCase())))))},ie=["e","E","+","-"],ce=({units:e,handleUnitChange:t,handleSizeChange:l,placeholder:a,startIcon:o,onBlur:i,onFocus:c,onClick:u,size:m,unit:d,popupState:p,disabled:E})=>{const v=(0,n.useRef)(""),b=re(d)?"text":"number",g=!re(d)&&Number.isNaN(m)?"":m??"",f={"aria-controls":p.isOpen?p.popupId:void 0,"aria-haspopup":!0},h={...f,autoComplete:"off",onClick:u,onFocus:c,startAdornment:o?n.createElement(r.InputAdornment,{position:"start",disabled:E},o):void 0,endAdornment:n.createElement(oe,{disabled:E,options:e,onClick:t,value:d,alternativeOptionLabels:{custom:n.createElement(s.PencilIcon,{fontSize:"small"})},menuItemsAttributes:e.includes("custom")?{custom:f}:void 0})};return n.createElement(q,null,n.createElement(r.Box,null,n.createElement(ae,{disabled:E,placeholder:a,type:b,value:g,onChange:l,onKeyDown:e=>{ie.includes(e.key)&&e.preventDefault()},onKeyUp:n=>{const{key:l}=n;if(!/^[a-zA-Z%]$/.test(l))return;n.preventDefault();const r=l.toLowerCase(),a=(v.current+r).slice(-3);v.current=a;const o=e.find((e=>e.includes(a)))||e.find((e=>e.startsWith(r)))||e.find((e=>e.includes(r)));o&&t(o)},onBlur:i,shouldBlockInput:re(d),inputProps:h})))},se=e=>{const{popupState:t,restoreValue:l,anchorRef:a,value:o,onChange:i}=e;return n.createElement(r.Popover,{disablePortal:!0,slotProps:{paper:{sx:{borderRadius:2,width:a.current?.offsetWidth+"px",p:1.5}}},...(0,r.bindPopover)(t),anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},onClose:()=>{l(),t.close()}},n.createElement(r.TextField,{value:o,onChange:i,size:"tiny",type:"text",fullWidth:!0,inputProps:{autoFocus:!0}}))},ue=({external:e,setExternal:t,persistWhen:l,fallback:r})=>{function a(e,t){return e||r(t)}const[o,i]=(0,n.useState)(a(e,null));return(0,n.useEffect)((()=>{i((t=>a(e,t)))}),[e]),[o,e=>{const n=("function"==typeof e?e:()=>e)(o);var r;i(n),t(l(r=n)?r:null)}]},me="px",de=NaN,pe=O((e=>{const t=e.defaultUnit??me,{units:a=[...ne],placeholder:o,startIcon:i,anchorRef:c}=e,{value:s,setValue:u,disabled:m,restoreValue:E}=P(l.sizePropTypeUtil),[v,b]=(0,n.useState)(ve(s,t)),g=(0,d.useActiveBreakpoint)(),f=(h=e.extendedOptions||[],y=e.disableCustom??!1,(0,n.useMemo)((()=>{const e=!(0,p.isExperimentActive)("e_v_3_30")||y,t=[...h];return e||t.includes("custom")?h.includes("custom")&&t.splice(t.indexOf("custom"),1):t.push("custom"),t}),[h,y]));var h,y;const x=(0,r.usePopupState)({variant:"popover"}),[_,C]=ue({external:v,setExternal:e=>u(be(e)),persistWhen:e=>!!e?.unit&&(re(e.unit)?"auto"===e.unit||!!e.custom:!!e?.numeric||0===e?.numeric),fallback:t=>({unit:t?.unit??e.defaultUnit??me,numeric:t?.numeric??de,custom:t?.custom??""})}),{size:T=de,unit:w=me}=be(_)||{},S=e=>{const{value:t}=e.target;C("auto"!==w?e=>({...e,["custom"===w?"custom":"numeric"]:Ee(t,w),unit:w}):e=>({...e,unit:w}))};return(0,n.useEffect)((()=>{const e=ve(s,t),n=re(_.unit)?"custom":"numeric",l={..._,[n]:e[n]};"auto"!==l.unit&&ge(_,l)||(_.unit!==e.unit?C(e):b(l))}),[s]),(0,n.useEffect)((()=>{const e=ve(s,t);g&&!ge(e,_)&&C(e)}),[g]),n.createElement(n.Fragment,null,n.createElement(ce,{disabled:m,size:T,unit:w,units:[...a,...f||[]],placeholder:o,startIcon:i,handleSizeChange:S,handleUnitChange:e=>{"custom"===e&&x.open(c?.current),C((t=>({...t,unit:e})))},onBlur:E,onFocus:e=>{re(_.unit)&&e.target?.blur()},onClick:e=>{e.target.closest("input")&&"custom"===_.unit&&x.open(c?.current)},popupState:x}),c?.current&&n.createElement(se,{popupState:x,anchorRef:c,restoreValue:E,value:T,onChange:S}))}));function Ee(e,t){return re(t)?"auto"===t?"":String(e??""):e||0===e?Number(e):NaN}function ve(e,t){const n=e?.unit??t,l=e?.size??"";return{numeric:re(n)||isNaN(Number(l))||!l&&0!==l?de:Number(l),custom:"custom"===n?String(l):"",unit:n}}function be(e){if(!e)return null;if(!e?.unit)return{size:de,unit:me};const{unit:t}=e;return"auto"===t?{size:"",unit:t}:{size:e["custom"===t?"custom":"numeric"],unit:t}}function ge(e,t){return e.unit===t.unit&&e.custom===t.custom&&(re(e.unit)?e.custom===t.custom:e.numeric===t.numeric||isNaN(e.numeric)&&isNaN(t.numeric))}var fe=({gap:e=2,sx:t,children:l})=>n.createElement(r.Stack,{gap:e,sx:{...t}},l),he=O((({propTypeUtil:e=l.colorPropTypeUtil,anchorEl:t,slotProps:a={},...o})=>{const{value:i,setValue:c,disabled:s}=P(e);return n.createElement(q,null,n.createElement(r.UnstableColorField,{size:"tiny",fullWidth:!0,value:i??"",onChange:e=>{c(e||null)},...o,disabled:s,slotProps:{...a,colorPicker:{anchorEl:t,anchorOrigin:{vertical:"top",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:-10}}}}))})),ye=["px","em","rem"],xe=O((()=>{const e=P(l.strokePropTypeUtil),t=(0,n.useRef)(null);return n.createElement(C,{...e},n.createElement(fe,null,n.createElement(_e,{bind:"width",label:(0,a.__)("Stroke width","elementor"),ref:t},n.createElement(pe,{units:ye,anchorRef:t})),n.createElement(_e,{bind:"color",label:(0,a.__)("Stroke color","elementor")},n.createElement(he,null))))})),_e=(0,n.forwardRef)((({bind:e,label:t,children:l},a)=>n.createElement(S,{bind:e},n.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap",ref:a},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(U,null,t)),n.createElement(r.Grid,{item:!0,xs:6},l))))),Ce=({gap:e=1.5,children:t,...l})=>n.createElement(r.Stack,{...l,gap:e},t),Te=(0,n.forwardRef)((({gap:e=1.5,alignItems:t="center",flexWrap:l="nowrap",children:a},o)=>n.createElement(r.Grid,{container:!0,gap:e,alignItems:t,flexWrap:l,ref:o},a))),we=(0,n.createContext)(null),Se=({children:e,items:t})=>n.createElement(we.Provider,{value:{items:t}},e),ze=()=>{const e=(0,n.useContext)(we);return e?.items??[]};function Ie(){const e=ze();return 0===e?.length?null:n.createElement(n.Fragment,null,e.map((({Adornment:e,id:t})=>n.createElement(e,{key:t}))))}var{Slot:ke,inject:Pe}=(0,E.createReplaceableLocation)(),{Slot:Ve,inject:$e}=(0,E.createReplaceableLocation)(),Ue=e=>n.createElement(r.List,{sx:{p:0,my:-.5,mx:0}},n.createElement(r.UnstableSortableProvider,{restrictAxis:!0,disableDragOverlay:!1,variant:"static",...e})),Re=({id:e,children:t,disabled:l})=>n.createElement(r.UnstableSortableItem,{id:e,disabled:l,render:({itemProps:e,triggerProps:r,itemStyle:a,triggerStyle:o,showDropIndication:i,dropIndicationStyle:c})=>n.createElement(Ge,{...e,style:a},!l&&n.createElement(Le,{...r,style:o}),t,i&&n.createElement(Oe,{style:c}))}),Ge=(0,r.styled)(r.ListItem)`
	position: relative;
	margin-inline: 0px;
	padding-inline: 0px;
	padding-block: ${({theme:e})=>e.spacing(.5)};

	& .class-item-sortable-trigger {
		color: ${({theme:e})=>e.palette.action.active};
		height: 100%;
		display: flex;
		align-items: center;
		visibility: hidden;
		position: absolute;
		top: 50%;
		padding-inline-end: ${({theme:e})=>e.spacing(.5)};
		transform: translate( -75%, -50% );
	}

	&[aria-describedby=''] > .MuiTag-root {
		background-color: ${({theme:e})=>e.palette.background.paper};
		box-shadow: ${({theme:e})=>e.shadows[3]};
	}

	&:hover {
		& .class-item-sortable-trigger {
			visibility: visible;
		}
	}
`,Le=e=>n.createElement("div",{...e,role:"button",className:"class-item-sortable-trigger"},n.createElement(s.GripVerticalIcon,{fontSize:"tiny"})),Oe=(0,r.styled)(r.Divider)`
	height: 0px;
	border: none;
	overflow: visible;

	&:after {
		--height: 2px;
		content: '';
		display: block;
		width: 100%;
		height: var( --height );
		margin-block: calc( -1 * var( --height ) / 2 );
		border-radius: ${({theme:e})=>e.spacing(.5)};
		background-color: ${({theme:e})=>e.palette.text.primary};
	}
`,Fe="tiny",Be=({label:e,itemSettings:t,disabled:l=!1,openOnAdd:o=!1,addToBottom:i=!1,values:c=[],setValues:u,showDuplicate:m=!0,showToggle:d=!0,isSortable:p=!0})=>{const[E,v]=(0,n.useState)(-1),[b,g]=ue({external:c,setExternal:u,persistWhen:()=>!0}),[f,h]=(0,n.useState)(b.map(((e,t)=>t))),y=e=>1+Math.max(0,...e);return n.createElement(fe,null,n.createElement(r.Stack,{direction:"row",justifyContent:"start",alignItems:"center",gap:1,sx:{marginInlineEnd:-.75}},n.createElement(r.Typography,{component:"label",variant:"caption",color:"text.secondary"},e),n.createElement(Ie,null),n.createElement(r.IconButton,{size:Fe,sx:{ml:"auto"},disabled:l,onClick:()=>{const e=structuredClone(t.initialValues),n=y(f);i?(g([...b,e]),h([...f,n])):(g([e,...b]),h([n,...f])),o&&v(n)},"aria-label":(0,a.__)("Add item","elementor")},n.createElement(s.PlusIcon,{fontSize:Fe}))),0<f.length&&n.createElement(Ue,{value:f,onChange:e=>{h(e),g((t=>e.map((e=>{const n=f.indexOf(e);return t[n]}))))}},f.map(((e,r)=>{const a=b[r];return a?n.createElement(Re,{id:e,key:`sortable-${e}`,disabled:!p},n.createElement(Ae,{disabled:l,propDisabled:a?.disabled,label:n.createElement(Ve,{value:a},n.createElement(t.Label,{value:a})),startIcon:n.createElement(ke,{value:a},n.createElement(t.Icon,{value:a})),removeItem:()=>(e=>{h(f.filter(((t,n)=>n!==e))),g(b.filter(((t,n)=>n!==e)))})(r),duplicateItem:()=>(e=>{const t=structuredClone(b[e]),n=y(f),l=1+e;g([...b.slice(0,l),t,...b.slice(l)]),h([...f.slice(0,l),n,...f.slice(l)])})(r),toggleDisableItem:()=>(e=>{g(b.map(((t,n)=>{if(n===e){const{disabled:e,...n}=t;return{...n,...e?{}:{disabled:!0}}}return t})))})(r),openOnMount:o&&E===e,onOpen:()=>v(-1),showDuplicate:m,showToggle:d},(e=>n.createElement(t.Content,{...e,value:a,bind:String(r)})))):null}))))},Ae=({label:e,propDisabled:t,startIcon:l,children:o,removeItem:i,duplicateItem:c,toggleDisableItem:u,openOnMount:m,onOpen:d,showDuplicate:p,showToggle:E,disabled:v})=>{const[b,g]=(0,n.useState)(null),{popoverState:f,popoverProps:h,ref:y,setRef:x}=Me(m,d),_=(0,a.__)("Duplicate","elementor"),C=t?(0,a.__)("Show","elementor"):(0,a.__)("Hide","elementor"),T=(0,a.__)("Remove","elementor");return n.createElement(n.Fragment,null,n.createElement(r.UnstableTag,{disabled:v,label:e,showActionsOnHover:!0,fullWidth:!0,ref:x,variant:"outlined","aria-label":(0,a.__)("Open item","elementor"),...(0,r.bindTrigger)(f),startIcon:l,actions:n.createElement(n.Fragment,null,p&&n.createElement(r.Tooltip,{title:_,placement:"top"},n.createElement(r.IconButton,{size:Fe,onClick:c,"aria-label":_},n.createElement(s.CopyIcon,{fontSize:Fe}))),E&&n.createElement(r.Tooltip,{title:C,placement:"top"},n.createElement(r.IconButton,{size:Fe,onClick:u,"aria-label":C},t?n.createElement(s.EyeOffIcon,{fontSize:Fe}):n.createElement(s.EyeIcon,{fontSize:Fe}))),n.createElement(r.Tooltip,{title:T,placement:"top"},n.createElement(r.IconButton,{size:Fe,onClick:i,"aria-label":T},n.createElement(s.XIcon,{fontSize:Fe}))))}),n.createElement(r.Popover,{disablePortal:!0,slotProps:{paper:{ref:g,sx:{mt:.5,width:y?.getBoundingClientRect().width}}},anchorOrigin:{vertical:"bottom",horizontal:"left"},...h,anchorEl:y},n.createElement(r.Box,null,o({anchorEl:b}))))},Me=(e,t)=>{const[l,a]=(0,n.useState)(null),o=(0,r.usePopupState)({variant:"popover"}),i=(0,r.bindPopover)(o);return(0,n.useEffect)((()=>{e&&l&&(o.open(l),t?.())}),[l]),{popoverState:o,ref:l,setRef:a,popoverProps:i}},We=O((()=>{const{propType:e,value:t,setValue:r,disabled:o}=P(l.boxShadowPropTypeUtil);return n.createElement(C,{propType:e,value:t,setValue:r,isDisabled:()=>o},n.createElement(Be,{openOnAdd:!0,disabled:o,values:t??[],setValues:r,label:(0,a.__)("Box shadow","elementor"),itemSettings:{Icon:De,Label:He,Content:je,initialValues:qe}}))})),De=({value:e})=>n.createElement(r.UnstableColorIndicator,{size:"inherit",component:"span",value:e.value.color?.value}),je=({anchorEl:e,bind:t})=>n.createElement(S,{bind:t},n.createElement(Ne,{anchorEl:e})),Ne=({anchorEl:e})=>{const t=P(l.shadowPropTypeUtil),r=[(0,n.useRef)(null),(0,n.useRef)(null)];return n.createElement(C,{...t},n.createElement(Ce,{p:1.5},n.createElement(Te,null,n.createElement(Ke,{bind:"color",label:(0,a.__)("Color","elementor")},n.createElement(he,{anchorEl:e})),n.createElement(Ke,{bind:"position",label:(0,a.__)("Position","elementor"),sx:{overflow:"hidden"}},n.createElement(Y,{options:[{label:(0,a.__)("Inset","elementor"),value:"inset"},{label:(0,a.__)("Outset","elementor"),value:null}]}))),n.createElement(Te,{ref:r[0]},n.createElement(Ke,{bind:"hOffset",label:(0,a.__)("Horizontal","elementor")},n.createElement(pe,{anchorRef:r[0]})),n.createElement(Ke,{bind:"vOffset",label:(0,a.__)("Vertical","elementor")},n.createElement(pe,{anchorRef:r[0]}))),n.createElement(Te,{ref:r[1]},n.createElement(Ke,{bind:"blur",label:(0,a.__)("Blur","elementor")},n.createElement(pe,{anchorRef:r[1]})),n.createElement(Ke,{bind:"spread",label:(0,a.__)("Spread","elementor")},n.createElement(pe,{anchorRef:r[1]})))))},Ke=({label:e,bind:t,children:l,sx:a})=>n.createElement(S,{bind:t},n.createElement(r.Grid,{item:!0,xs:6,sx:a},n.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(r.FormLabel,{size:"tiny"},e)),n.createElement(r.Grid,{item:!0,xs:12},l)))),He=({value:e})=>{const{position:t,hOffset:l,vOffset:r,blur:a,spread:o}=e.value,{size:i="",unit:c=""}=a?.value||{},{size:s="",unit:u=""}=o?.value||{},{size:m="unset",unit:d=""}=l?.value||{},{size:p="unset",unit:E=""}=r?.value||{},v=t?.value||"outset",b=[m+d,p+E,i+c,s+u].join(" ");return n.createElement("span",{style:{textTransform:"capitalize"}},v,": ",b)},qe={$$type:"shadow",value:{hOffset:{$$type:"size",value:{unit:"px",size:0}},vOffset:{$$type:"size",value:{unit:"px",size:0}},blur:{$$type:"size",value:{unit:"px",size:10}},spread:{$$type:"size",value:{unit:"px",size:0}},color:{$$type:"color",value:"rgba(0, 0, 0, 1)"},position:null}},Xe=({children:e})=>n.createElement(r.Stack,{direction:"row",alignItems:"center",justifyItems:"start",gap:.25},n.createElement(U,null,e),n.createElement(Ie,null)),Ye="blur",Qe={blur:{defaultValue:{$$type:"radius",radius:{$$type:"size",value:{size:0,unit:"px"}}},name:(0,a.__)("Blur","elementor"),valueName:(0,a.__)("Radius","elementor"),propType:l.blurFilterPropTypeUtil,units:ne.filter((e=>"%"!==e))},brightness:{defaultValue:{$$type:"amount",amount:{$$type:"size",value:{size:100,unit:"%"}}},name:(0,a.__)("Brightness","elementor"),valueName:(0,a.__)("Amount","elementor"),propType:l.brightnessFilterPropTypeUtil,units:["%"]}},Ze=Object.keys(Qe),Je=Ze.filter((e=>{const t=Qe[e].defaultValue;return"size"===t[t.$$type].$$type})),et=O((()=>{const{propType:e,value:t,setValue:r,disabled:o}=P(l.filterPropTypeUtil);return n.createElement(C,{propType:e,value:t,setValue:r},n.createElement(Be,{openOnAdd:!0,disabled:o,values:t??[],setValues:r,label:(0,a.__)("Filter","elementor"),itemSettings:{Icon:tt,Label:nt,Content:rt,initialValues:{$$type:Ye,value:Qe[Ye].defaultValue}}}))})),tt=()=>n.createElement(n.Fragment,null),nt=e=>{const{$$type:t}=e.value;return Je.includes(t)&&n.createElement(lt,{value:e.value})},lt=({value:e})=>{const{$$type:t,value:l}=e,{$$type:a}=Qe[t].defaultValue,o=Qe[t].defaultValue[a].value.unit,{unit:i,size:c}=l[a]?.value??{unit:o,size:0},s=n.createElement(r.Box,{component:"span",style:{textTransform:"capitalize"}},e.$$type,":");return n.createElement(r.Box,{component:"span"},s,"custom"!==i?` ${c??0}${i??o}`:c)},rt=({bind:e})=>{const{value:t,setValue:o}=P(l.filterPropTypeUtil),i=parseInt(e,10),c=t?.[i];return n.createElement(S,{bind:e},n.createElement(Ce,{p:1.5},n.createElement(Te,null,n.createElement(r.Grid,{item:!0,xs:6},n.createElement(Xe,null,(0,a.__)("Filter","elementor"))),n.createElement(r.Grid,{item:!0,xs:6},n.createElement(r.Select,{sx:{overflow:"hidden"},size:"tiny",value:c?.$$type??Ye,onChange:e=>{const n=[...t],l=e.target.value;n[i]={$$type:l,value:Qe[l].defaultValue},o(n)},fullWidth:!0},Ze.map((e=>n.createElement(m.MenuListItem,{key:e,value:e},Qe[e].name)))))),n.createElement(at,{filterType:c?.$$type})))},at=({filterType:e})=>Je.includes(e)&&n.createElement(ot,{filterType:e}),ot=({filterType:e})=>{const{propType:t,valueName:l,defaultValue:a,units:o}=Qe[e],{$$type:i}=a,c=P(t),s=(0,n.useRef)(null);return n.createElement(C,{...c},n.createElement(S,{bind:i},n.createElement(Te,{ref:s},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(Xe,null,l)),n.createElement(r.Grid,{item:!0,xs:6},n.createElement(pe,{anchorRef:s,units:o})))))},it=({showTooltip:e,children:t,label:l})=>e&&l?n.createElement(r.Tooltip,{title:l,disableFocusListener:!0,placement:"top"},t):t,ct=(0,r.styled)(r.ToggleButtonGroup)`
	${({justify:e})=>`justify-content: ${e};`}
	button:not( :last-of-type ) {
		border-start-end-radius: 0;
		border-end-end-radius: 0;
	}
	button:not( :first-of-type ) {
		border-start-start-radius: 0;
		border-end-start-radius: 0;
	}
	button:last-of-type {
		border-start-end-radius: 8px;
		border-end-end-radius: 8px;
	}
`,st=({justify:e="end",size:t="tiny",value:l,onChange:a,items:o,maxItems:i,exclusive:c=!1,fullWidth:s=!1,disabled:u})=>{const m=c&&void 0!==i&&o.length>i,d=m?o.slice(i-1):[],p=m?o.slice(0,i-1):o,E="rtl"===(0,r.useTheme)().direction,v=(0,n.useMemo)((()=>{const e=d?.length;return`repeat(${e?p.length+1:p.length}, minmax(0, 25%)) ${e?"auto":""}`}),[d?.length,p.length]);return n.createElement(q,null,n.createElement(ct,{justify:e,value:l,onChange:(e,t)=>{a(t)},exclusive:c,disabled:u,sx:{direction:E?"rtl /* @noflip */":"ltr /* @noflip */",display:"grid",gridTemplateColumns:v,width:"100%"}},p.map((({label:e,value:l,renderContent:a,showTooltip:o})=>n.createElement(it,{key:l,label:e,showTooltip:o||!1},n.createElement(r.ToggleButton,{value:l,"aria-label":e,size:t,fullWidth:s},n.createElement(a,{size:t}))))),d.length&&c&&n.createElement(ut,{size:t,value:l||null,onChange:a,items:d,fullWidth:s})))},ut=({size:e="tiny",onChange:t,items:l,fullWidth:a,value:o})=>{const i=mt(l,o),[c,u]=(0,n.useState)(!1),m=(0,n.useRef)(null),d=e=>{u(!1),p(e)},p=e=>{t(e===o?null:e)};return n.createElement(n.Fragment,null,n.createElement(r.ToggleButton,{value:i.value,"aria-label":i.label,size:e,fullWidth:a,onClick:e=>{e.preventDefault(),d(i.value)},ref:m},i.renderContent({size:e})),n.createElement(r.ToggleButton,{size:e,"aria-expanded":c?"true":void 0,"aria-haspopup":"menu","aria-pressed":void 0,onClick:e=>{u((e=>!e)),e.preventDefault()},ref:m,value:"__chevron-icon-button__"},n.createElement(s.ChevronDownIcon,{fontSize:e})),n.createElement(r.Menu,{open:c,onClose:()=>u(!1),anchorEl:m.current,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},sx:{mt:.5}},l.map((({label:e,value:t})=>n.createElement(r.MenuItem,{key:t,selected:t===o,onClick:()=>d(t)},n.createElement(r.ListItemText,null,n.createElement(r.Typography,{sx:{fontSize:"14px"}},e)))))))},mt=(e,t)=>{const[l,r]=(0,n.useState)(e.find((e=>e.value===t))??e[0]);return(0,n.useEffect)((()=>{const n=e.find((e=>e.value===t));n&&r(n)}),[e,t]),l},dt=O((({options:e,fullWidth:t=!1,size:r="tiny",exclusive:a=!0,maxItems:o})=>{const{value:i,setValue:c,placeholder:s,disabled:u}=P(l.stringPropTypeUtil),m=e.filter((e=>e.exclusive)).map((e=>e.value)),d={items:e,maxItems:o,fullWidth:t,size:r};return a?n.createElement(st,{...d,value:i??s??null,onChange:c,disabled:u,exclusive:!0}):n.createElement(st,{...d,value:(i??s)?.split(" ")??[],onChange:e=>{const t=e[e.length-1],n=m.includes(t)?[t]:e?.filter((e=>!m.includes(e)));c(n?.join(" ")||null)},disabled:u,exclusive:!1})})),pt=e=>null==e||""===e||Number.isNaN(Number(e)),Et=["e","E","+","-"],vt=O((({placeholder:e,max:t=Number.MAX_VALUE,min:a=-Number.MAX_VALUE,step:o=1,shouldForceInt:i=!1})=>{const{value:c,setValue:s,placeholder:u,disabled:m}=P(l.numberPropTypeUtil);return n.createElement(q,null,n.createElement(r.TextField,{size:"tiny",type:"number",fullWidth:!0,disabled:m,value:pt(c)?"":c,onChange:e=>{const n=e.target.value;if(pt(n))return void s(null);const l=i?+parseInt(n):Number(n);s(Math.min(Math.max(l,a),t))},placeholder:e??(u?String(u):""),inputProps:{step:o},onKeyDown:e=>{Et.includes(e.key)&&e.preventDefault()}}))})),bt=(e,t)=>{const n=Object.values(e);if(n.length!==t.length)return!1;const[l,...r]=n;return r.every((e=>e?.value?.size===l?.value?.size&&e?.value?.unit===l?.value?.unit))};function gt({label:e,icon:t,tooltipLabel:o,items:i,multiSizePropTypeUtil:c}){const s=(0,n.useId)(),u=(0,r.usePopupState)({variant:"popover",popupId:s}),{propType:m,value:d,setValue:E,disabled:v}=P(c),{value:b,setValue:g}=P(l.sizePropTypeUtil),f=[(0,n.useRef)(null),(0,n.useRef)(null)],h=()=>b?i.reduce(((e,{bind:t})=>({...e,[t]:l.sizePropTypeUtil.create(b)})),{}):null,y=!(0,p.isExperimentActive)("e_v_3_30")||!u.isOpen,x=!!d;return n.createElement(n.Fragment,null,n.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap",ref:f[0]},n.createElement(r.Grid,{item:!0,xs:6},y?n.createElement(Xe,null,e):n.createElement(U,null,e)),n.createElement(r.Grid,{item:!0,xs:6},n.createElement(r.Stack,{direction:"row",alignItems:"center",gap:1},n.createElement(pe,{placeholder:x?(0,a.__)("Mixed","elementor"):void 0,anchorRef:f[0]}),n.createElement(r.Tooltip,{title:o,placement:"top"},n.createElement(r.ToggleButton,{size:"tiny",value:"check",sx:{marginLeft:"auto"},...(0,r.bindToggle)(u),selected:u.isOpen,"aria-label":o},t))))),n.createElement(r.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},...(0,r.bindPopover)(u),slotProps:{paper:{sx:{mt:.5,width:f[0].current?.getBoundingClientRect().width}}}},n.createElement(C,{propType:m,value:d||(h()??null),setValue:e=>{const t={...d??h(),...e};if(bt(t,i))return g(Object.values(t)[0]?.value);E(t)},isDisabled:()=>v},n.createElement(Ce,{p:1.5},n.createElement(Te,{ref:f[1]},n.createElement(ft,{item:i[0],rowRef:f[1]}),n.createElement(ft,{item:i[1],rowRef:f[1]})),n.createElement(Te,{ref:f[2]},n.createElement(ft,{item:i[2],rowRef:f[2]}),n.createElement(ft,{item:i[3],rowRef:f[2]}))))))}var ft=({item:e,rowRef:t})=>{const l=(0,p.isExperimentActive)("e_v_3_30");return n.createElement(S,{bind:e.bind},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},n.createElement(r.Grid,{item:!0,xs:12},l?n.createElement(Xe,null,e.label):n.createElement(U,null,e.label)),n.createElement(r.Grid,{item:!0,xs:12},n.createElement(pe,{startIcon:e.icon,anchorRef:t})))))},ht=O((({label:e,isSiteRtl:t=!1,extendedOptions:o})=>{const{value:i,setValue:c,disabled:u}=P(l.sizePropTypeUtil),m=[(0,n.useRef)(null),(0,n.useRef)(null)],{value:d,setValue:E,propType:v,disabled:b}=P(l.dimensionsPropTypeUtil),g=!d&&!i||!!i,f=(0,p.isExperimentActive)("e_v_3_30"),h=e.toLowerCase(),y=g?s.LinkIcon:s.DetachIcon,x=(0,a.__)("Link %s","elementor").replace("%s",h),_=(0,a.__)("Unlink %s","elementor").replace("%s",h),T=u||b;return n.createElement(C,{propType:v,value:d,setValue:E,isDisabled:()=>T},n.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},f?n.createElement(U,null,e):n.createElement(Xe,null,e),n.createElement(r.Tooltip,{title:g?_:x,placement:"top"},n.createElement(r.ToggleButton,{"aria-label":g?_:x,size:"tiny",value:"check",selected:g,sx:{marginLeft:"auto"},onChange:()=>{if(!g)return void c(d["block-start"]?.value??null);const e=i?l.sizePropTypeUtil.create(i):null;E({"block-start":e,"block-end":e,"inline-start":e,"inline-end":e})},disabled:T},n.createElement(y,{fontSize:"tiny"})))),function(e){return[[{bind:"block-start",label:(0,a.__)("Top","elementor"),icon:n.createElement(s.SideTopIcon,{fontSize:"tiny"})},{bind:"inline-end",label:e?(0,a.__)("Left","elementor"):(0,a.__)("Right","elementor"),icon:e?n.createElement(s.SideLeftIcon,{fontSize:"tiny"}):n.createElement(s.SideRightIcon,{fontSize:"tiny"})}],[{bind:"block-end",label:(0,a.__)("Bottom","elementor"),icon:n.createElement(s.SideBottomIcon,{fontSize:"tiny"})},{bind:"inline-start",label:e?(0,a.__)("Right","elementor"):(0,a.__)("Left","elementor"),icon:e?n.createElement(s.SideRightIcon,{fontSize:"tiny"}):n.createElement(s.SideLeftIcon,{fontSize:"tiny"})}]]}(t).map(((e,t)=>n.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap",key:t,ref:m[t]},e.map((({icon:e,...l})=>n.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center",key:l.bind},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(xt,{...l})),n.createElement(r.Grid,{item:!0,xs:12},n.createElement(yt,{bind:l.bind,startIcon:e,isLinked:g,extendedOptions:o,anchorRef:m[t]})))))))))})),yt=({bind:e,startIcon:t,isLinked:l,extendedOptions:r,anchorRef:a})=>l?n.createElement(pe,{startIcon:t,extendedOptions:r,anchorRef:a}):n.createElement(S,{bind:e},n.createElement(pe,{startIcon:t,extendedOptions:r,anchorRef:a})),xt=({label:e,bind:t})=>(0,p.isExperimentActive)("e_v_3_30")?n.createElement(S,{bind:t},n.createElement(Xe,null,e)):n.createElement(U,null,e),_t=({fontFamilies:e,fontFamily:t,onFontFamilyChange:l,onClose:o,sectionWidth:i})=>{const[c,u]=(0,n.useState)(""),d=((e,t)=>e.reduce(((e,n)=>{const l=n.fonts.filter((e=>e.toLowerCase().includes(t.toLowerCase())));return l.length&&(e.push({type:"category",value:n.label}),l.forEach((t=>{e.push({type:"font",value:t})}))),e}),[]))(e,c),p=()=>{u(""),o()};return n.createElement(r.Stack,null,n.createElement(m.PopoverHeader,{title:(0,a.__)("Font Family","elementor"),onClose:p,icon:n.createElement(s.TextIcon,{fontSize:"tiny"})}),n.createElement(m.PopoverSearch,{value:c,onSearch:e=>{u(e)},placeholder:(0,a.__)("Search","elementor")}),n.createElement(r.Divider,null),n.createElement(m.PopoverScrollableContent,{width:i},d.length>0?n.createElement(Ct,{fontListItems:d,setFontFamily:l,handleClose:p,fontFamily:t}):n.createElement(r.Stack,{alignItems:"center",justifyContent:"center",height:"100%",p:2.5,gap:1.5,overflow:"hidden"},n.createElement(s.TextIcon,{fontSize:"large"}),n.createElement(r.Box,{sx:{maxWidth:160,overflow:"hidden"}},n.createElement(r.Typography,{align:"center",variant:"subtitle2",color:"text.secondary"},(0,a.__)("Sorry, nothing matched","elementor")),n.createElement(r.Typography,{variant:"subtitle2",color:"text.secondary",sx:{display:"flex",width:"100%",justifyContent:"center"}},n.createElement("span",null,"“"),n.createElement("span",{style:{maxWidth:"80%",overflow:"hidden",textOverflow:"ellipsis"}},c),n.createElement("span",null,"”."))),n.createElement(r.Typography,{align:"center",variant:"caption",color:"text.secondary",sx:{display:"flex",flexDirection:"column"}},(0,a.__)("Try something else.","elementor"),n.createElement(r.Link,{color:"secondary",variant:"caption",component:"button",onClick:()=>u("")},(0,a.__)("Clear & try again","elementor"))))))},Ct=({fontListItems:e,setFontFamily:t,handleClose:l,fontFamily:r})=>{const a=e.find((e=>e.value===r)),o=Tt((({getVirtualIndexes:t})=>{t().forEach((t=>{const n=e[t];n&&"font"===n.type&&((e,t="editor")=>{const n=window;n.elementor?.helpers?.enqueueFont?.(e,t)})(n.value)}))}),100);return n.createElement(m.PopoverMenuList,{items:e,selectedValue:a?.value,onChange:o,onSelect:t,onClose:l,itemStyle:e=>({fontFamily:e.value}),"data-testid":"font-list"})},Tt=(e,t)=>{const[l]=(0,n.useState)((()=>(0,o.debounce)(e,t)));return(0,n.useEffect)((()=>()=>l.cancel()),[l]),l},wt=O((({fontFamilies:e,sectionWidth:t})=>{const{value:a,setValue:o,disabled:i,placeholder:c}=P(l.stringPropTypeUtil),u=(0,r.usePopupState)({variant:"popover"}),m=!a&&c;return n.createElement(n.Fragment,null,n.createElement(q,null,n.createElement(r.UnstableTag,{variant:"outlined",label:a||c,endIcon:n.createElement(s.ChevronDownIcon,{fontSize:"tiny"}),...(0,r.bindTrigger)(u),fullWidth:!0,disabled:i,sx:m?{"& .MuiTag-label":{color:e=>e.palette.text.tertiary},textTransform:"capitalize"}:void 0})),n.createElement(r.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},sx:{my:1.5},...(0,r.bindPopover)(u)},n.createElement(_t,{fontFamilies:e,fontFamily:a,onFontFamilyChange:o,onClose:u.close,sectionWidth:t})))})),St=O((({placeholder:e})=>{const{value:t,setValue:a,disabled:o}=P(l.urlPropTypeUtil);return n.createElement(q,null,n.createElement(r.TextField,{size:"tiny",fullWidth:!0,value:t??"",disabled:o,onChange:e=>a(e.target.value),placeholder:e}))})),zt=(0,n.forwardRef)(((e,t)=>{const{options:l,onOptionChange:a,onTextChange:o,allowCustomValues:i=!1,placeholder:c="",minInputLength:s=2,value:u="",...m}=e,d=function(e,t,n){if(null===e)return t;const l=String(e||"")?.toLowerCase();return l.length<n?new Array(0):t.filter((e=>String(e.id).toLowerCase().includes(l)||e.label.toLowerCase().includes(l)))}(u,l,s).map((({id:e})=>e)),p=!!u,E=i||u?.toString()?.length?void 0:()=>!0,v="number"==typeof u&&!!Pt(l,u);return n.createElement(r.Autocomplete,{...m,ref:t,forcePopupIcon:!1,disableClearable:!0,freeSolo:i,value:u?.toString()||"",size:"tiny",onChange:(e,t)=>a(Number(t)),readOnly:v,options:d,getOptionKey:e=>Pt(l,e)?.id||e,getOptionLabel:e=>Pt(l,e)?.label||e.toString(),groupBy:Vt(l)?e=>Pt(l,e)?.groupLabel||e:void 0,isOptionEqualToValue:E,filterOptions:()=>d,renderOption:(e,t)=>n.createElement(r.Box,{component:"li",...e,key:e.id},Pt(l,t)?.label??t),renderInput:e=>n.createElement(It,{params:e,handleChange:e=>o?.(e),allowClear:p,placeholder:c,hasSelectedValue:v})})})),It=({params:e,allowClear:t,placeholder:l,handleChange:a,hasSelectedValue:o})=>n.createElement(r.TextField,{...e,placeholder:l,onChange:e=>{a(e.target.value)},sx:{"& .MuiInputBase-input":{cursor:o?"default":void 0}},InputProps:{...e.InputProps,endAdornment:n.createElement(kt,{params:e,allowClear:t,handleChange:a})}}),kt=({allowClear:e,handleChange:t,params:l})=>n.createElement(r.InputAdornment,{position:"end"},e&&n.createElement(r.IconButton,{size:l.size,onClick:()=>t(null),sx:{cursor:"pointer"}},n.createElement(s.XIcon,{fontSize:l.size})));function Pt(e,t=null){const n=(t||"").toString();return e.find((({id:e})=>n===e.toString()))}function Vt(e){return e.every((e=>"groupLabel"in e))}var $t=O((()=>{const{value:e,setValue:t,disabled:a}=P(l.booleanPropTypeUtil);return n.createElement("div",{style:{display:"flex",justifyContent:"flex-end"}},n.createElement(r.Switch,{checked:!!e,onChange:e=>{t(e.target.checked)},size:"small",disabled:a,inputProps:{...a?{style:{opacity:0}}:{}}}))})),Ut="tiny",Rt={label:(0,a.__)("Learn More","elementor"),href:"https://go.elementor.com/element-link-inside-link-infotip"},Gt=O((e=>{const{value:t,path:i,setValue:s,...u}=P(l.linkPropTypeUtil),[m,d]=(0,b.useSessionStorage)(i.join("/")),[p,E]=(0,n.useState)(!!t),{allowCustomValues:g,queryOptions:{endpoint:f="",requestParams:h={}},placeholder:y,minInputLength:x=2,context:{elementId:_},label:T=(0,a.__)("Link","elementor")}=e||{},[w,z]=(0,n.useState)((0,v.getLinkInLinkRestriction)(_)),[I,k]=(0,n.useState)(function(e){const t=e?.destination?.value,n=e?.label?.value;return t&&n&&"number"===(e?.destination?.$$type||"url")?[{id:t.toString(),label:n}]:[]}(t)),V=!p&&w.shouldRestrict,$=e=>{s(e),d({...m,value:e})},R=(0,n.useMemo)((()=>(0,o.debounce)((e=>async function(e,t){if(!t||!e)return[];try{const{data:n}=await(0,c.httpService)().get(e,{params:t});return n.data.value}catch{return[]}}(f,e).then((e=>{k(function(e){const t=Vt(e)?"groupLabel":"label";return e.sort(((e,n)=>e[t]&&n[t]?e[t].localeCompare(n[t]):0))}(e))}))),400)),[f]);return n.createElement(C,{...u,value:t,setValue:s},n.createElement(r.Stack,{gap:1.5},n.createElement(r.Stack,{direction:"row",sx:{justifyContent:"space-between",alignItems:"center",marginInlineEnd:-.75}},n.createElement(U,null,T),n.createElement(Ft,{isVisible:!p,linkInLinkRestriction:w},n.createElement(Lt,{disabled:V,active:p,onIconClick:()=>{if(z((0,v.getLinkInLinkRestriction)(_)),w.shouldRestrict&&!p)return;const e=!p;E(e),e||null===t||s(null),e&&m?.value&&s(m.value),d({value:m?.value,meta:{isEnabled:e}})},label:(0,a.__)("Toggle link","elementor")}))),n.createElement(r.Collapse,{in:p,timeout:"auto",unmountOnExit:!0},n.createElement(r.Stack,{gap:1.5},n.createElement(S,{bind:"destination"},n.createElement(q,null,n.createElement(zt,{options:I,allowCustomValues:g,placeholder:y,value:t?.destination?.value?.settings?.label||t?.destination?.value,onOptionChange:e=>{const n=e?{...t,destination:l.numberPropTypeUtil.create(e),label:l.stringPropTypeUtil.create(Pt(I,e)?.label||null)}:null;$(n)},onTextChange:e=>{const n=(e=e?.trim()||"")?{...t,destination:l.urlPropTypeUtil.create(e),label:l.stringPropTypeUtil.create("")}:null;$(n),(e=>{k([]),!e||!f||e.length<x||R({...h,term:e})})(e)},minInputLength:x}))),n.createElement(S,{bind:"isTargetBlank"},n.createElement(r.Grid,{container:!0,alignItems:"center",flexWrap:"nowrap",justifyContent:"space-between"},n.createElement(r.Grid,{item:!0},n.createElement(U,null,(0,a.__)("Open in a new tab","elementor"))),n.createElement(r.Grid,{item:!0,sx:{marginInlineEnd:-1}},n.createElement(Ot,{disabled:u.disabled||!t}))))))))})),Lt=({disabled:e,active:t,onIconClick:l,label:a})=>n.createElement(r.IconButton,{size:Ut,onClick:l,"aria-label":a,disabled:e},t?n.createElement(s.MinusIcon,{fontSize:Ut}):n.createElement(s.PlusIcon,{fontSize:Ut})),Ot=({disabled:e})=>{const{value:t,setValue:a}=P(l.booleanPropTypeUtil);return(0,p.isExperimentActive)("e_v_3_31")?n.createElement($t,null):n.createElement(r.Switch,{checked:t??!1,onClick:()=>{a(!t)},disabled:e,inputProps:{...e?{style:{opacity:0}}:{}}})},Ft=({linkInLinkRestriction:e,isVisible:t,children:l})=>{const{shouldRestrict:o,reason:i,elementId:c}=e;return o&&t?n.createElement(r.Infotip,{placement:"right",content:n.createElement(m.InfoTipCard,{content:Bt[i],svgIcon:n.createElement(s.AlertTriangleIcon,null),learnMoreButton:Rt,ctaButton:{label:(0,a.__)("Take me there","elementor"),onClick:()=>{c&&(0,v.selectElement)(c)}}})},n.createElement(r.Box,null,l)):n.createElement(n.Fragment,null,l)},Bt={descendant:n.createElement(n.Fragment,null,(0,a.__)("To add a link to this container,","elementor"),n.createElement("br",null),(0,a.__)("first remove the link from the elements inside of it.","elementor")),ancestor:n.createElement(n.Fragment,null,(0,a.__)("To add a link to this element,","elementor"),n.createElement("br",null),(0,a.__)("first remove the link from its parent container.","elementor"))},At=O((({label:e})=>{const{value:t,setValue:o,propType:i,disabled:c}=P(l.layoutDirectionPropTypeUtil),u=(0,n.useRef)(null),{value:m,setValue:d,disabled:p}=P(l.sizePropTypeUtil),E=!t&&!m||!!m,v=e.toLowerCase(),b=E?s.LinkIcon:s.DetachIcon,g=(0,a.__)("Link %s","elementor").replace("%s",v),f=(0,a.__)("Unlink %s","elementor").replace("%s",v),h=p||c;return n.createElement(C,{propType:i,value:t,setValue:o},n.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},n.createElement(Xe,null,e),n.createElement(r.Tooltip,{title:E?f:g,placement:"top"},n.createElement(r.ToggleButton,{"aria-label":E?f:g,size:"tiny",value:"check",selected:E,sx:{marginLeft:"auto"},onChange:()=>{if(!E)return void d(t?.column?.value??null);const e=m?l.sizePropTypeUtil.create(m):null;o({row:e,column:e})},disabled:h},n.createElement(b,{fontSize:"tiny"})))),n.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap",ref:u},n.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(U,null,(0,a.__)("Column","elementor"))),n.createElement(r.Grid,{item:!0,xs:12},n.createElement(Mt,{bind:"column",isLinked:E,anchorRef:u}))),n.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(U,null,(0,a.__)("Row","elementor"))),n.createElement(r.Grid,{item:!0,xs:12},n.createElement(Mt,{bind:"row",isLinked:E,anchorRef:u})))))})),Mt=({bind:e,isLinked:t,anchorRef:l})=>t?n.createElement(pe,{anchorRef:l}):n.createElement(S,{bind:e},n.createElement(pe,{anchorRef:l})),Wt=[{label:(0,a.__)("Auto","elementor"),value:"auto"},{label:"1/1",value:"1/1"},{label:"4/3",value:"4/3"},{label:"3/4",value:"3/4"},{label:"16/9",value:"16/9"},{label:"9/16",value:"9/16"},{label:"3/2",value:"3/2"},{label:"2/3",value:"2/3"}],Dt="custom",jt=O((({label:e})=>{const{value:t,setValue:o,disabled:i}=P(l.stringPropTypeUtil),c=t&&!Wt.some((e=>e.value===t)),[u,d]=c?t.split("/"):["",""],[p,E]=(0,n.useState)(c),[v,b]=(0,n.useState)(u),[g,f]=(0,n.useState)(d),[h,y]=(0,n.useState)(c?Dt:t||"");return(0,n.useEffect)((()=>{if(t&&!Wt.some((e=>e.value===t))){const[e,n]=t.split("/");b(e||""),f(n||""),y(Dt),E(!0)}else y(t||""),E(!1),b(""),f("")}),[t]),n.createElement(q,null,n.createElement(r.Stack,{direction:"column",gap:2},n.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(Xe,null,e)),n.createElement(r.Grid,{item:!0,xs:6},n.createElement(r.Select,{size:"tiny",displayEmpty:!0,sx:{overflow:"hidden"},disabled:i,value:h,onChange:e=>{const t=e.target.value,n=t===Dt;E(n),y(t),n||o(t)},fullWidth:!0},[...Wt,{label:(0,a.__)("Custom","elementor"),value:Dt}].map((({label:e,...t})=>n.createElement(m.MenuListItem,{key:t.value,...t,value:t.value??""},e)))))),p&&n.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(r.TextField,{size:"tiny",type:"number",fullWidth:!0,disabled:i,value:v,onChange:e=>{const t=e.target.value;b(t),t&&g&&o(`${t}/${g}`)},InputProps:{startAdornment:n.createElement(s.ArrowsMoveHorizontalIcon,{fontSize:"tiny"})}})),n.createElement(r.Grid,{item:!0,xs:6},n.createElement(r.TextField,{size:"tiny",type:"number",fullWidth:!0,disabled:i,value:g,onChange:e=>{const t=e.target.value;f(t),v&&t&&o(`${v}/${t}`)},InputProps:{startAdornment:n.createElement(s.ArrowsMoveVerticalIcon,{fontSize:"tiny"})}})))))})),Nt=(0,a.__)("Enable Unfiltered Uploads","elementor"),Kt=(0,a.__)("Before you enable unfiltered files upload, note that such files include a security risk. Elementor does run a process to remove possible malicious code, but there is still risk involved when using such files.","elementor"),Ht=(0,a.__)("Sorry, you can't upload that file yet","elementor"),qt=(0,a.__)("This is because this file type may pose a security risk. To upload them anyway, ask the site administrator to enable unfiltered file uploads.","elementor"),Xt=(0,a.__)("Failed to enable unfiltered files upload.","elementor"),Yt=(0,a.__)("You can try again, if the problem persists, please contact support.","elementor"),Qt=e=>{const{mutateAsync:t,isPending:l}=function(){const e=(0,i.useQueryClient)();return(0,i.useMutation)({mutationFn:({allowUnfilteredFilesUpload:e})=>{return t=A,n=e?"1":"0",(0,c.httpService)().put(`${F}/${t}`,{value:n});var t,n},onSuccess:()=>e.invalidateQueries(M)})}(),{canUser:r}=(0,g.useCurrentUserCapabilities)(),[a,o]=(0,n.useState)(!1),s=r("manage_options"),u={...e,isPending:l,handleEnable:async()=>{try{const n=await t({allowUnfilteredFilesUpload:!0});!1===n?.data?.success?o(!0):e.onClose(!0)}catch{o(!0)}},isError:a,onClose:t=>{e.onClose(t),setTimeout((()=>o(!1)),300)}};return s?n.createElement(Zt,{...u}):n.createElement(Jt,{...u})},Zt=({open:e,onClose:t,handleEnable:l,isPending:o,isError:i})=>n.createElement(r.Dialog,{open:e,maxWidth:"sm",onClose:()=>t(!1)},n.createElement(r.DialogHeader,{logo:!1},n.createElement(r.DialogTitle,null,Nt)),n.createElement(r.Divider,null),n.createElement(r.DialogContent,null,n.createElement(r.DialogContentText,null,i?n.createElement(n.Fragment,null,Xt," ",n.createElement("br",null)," ",Yt):Kt)),n.createElement(r.DialogActions,null,n.createElement(r.Button,{size:"medium",color:"secondary",onClick:()=>t(!1)},(0,a.__)("Cancel","elementor")),n.createElement(r.Button,{size:"medium",onClick:()=>l(),variant:"contained",color:"primary",disabled:o},o?n.createElement(r.CircularProgress,{size:24}):(0,a.__)("Enable","elementor")))),Jt=({open:e,onClose:t})=>n.createElement(r.Dialog,{open:e,maxWidth:"sm",onClose:()=>t(!1)},n.createElement(r.DialogHeader,{logo:!1},n.createElement(r.DialogTitle,null,Ht)),n.createElement(r.Divider,null),n.createElement(r.DialogContent,null,n.createElement(r.DialogContentText,null,qt)),n.createElement(r.DialogActions,null,n.createElement(r.Button,{size:"medium",onClick:()=>t(!1),variant:"contained",color:"primary"},(0,a.__)("Got it","elementor")))),en="transparent",tn="#c1c1c1",nn=`linear-gradient(45deg, ${tn} 25%, ${en} 0, ${en} 75%, ${tn} 0, ${tn})`,ln=(0,r.styled)(r.Card)`
	background-color: white;
	background-image: ${nn}, ${nn};
	background-size: ${8}px ${8}px;
	background-position:
		0 0,
		${4}px ${4}px;
	border: none;
`,rn=(0,r.styled)(r.Stack)`
	position: relative;
	height: 140px;
	object-fit: contain;
	padding: 5px;
	justify-content: center;
	align-items: center;
	background-color: rgba( 255, 255, 255, 0.37 );
`,an={mode:"browse"},on={mode:"upload"},cn=O((()=>{const{value:e,setValue:t}=P(l.imageSrcPropTypeUtil),{id:o,url:i}=e??{},{data:c,isFetching:m}=(0,u.useWpMediaAttachment)(o?.value||null),d=c?.url??i?.value??null,{data:p}=W(),[E,v]=(0,n.useState)(!1),{open:b}=(0,u.useWpMediaFrame)({mediaTypes:["svg"],multiple:!1,selected:o?.value||null,onSelect:e=>{t({id:{$$type:"image-attachment-id",value:e.id},url:null})}}),g=e=>{p||e!==on?b(e):v(!0)};return n.createElement(r.Stack,{gap:1},n.createElement(Qt,{open:E,onClose:e=>{v(!1),e&&b(on)}}),n.createElement(q,null,n.createElement(ln,{variant:"outlined"},n.createElement(rn,null,m?n.createElement(r.CircularProgress,{role:"progressbar"}):n.createElement(r.CardMedia,{component:"img",image:d,alt:(0,a.__)("Preview SVG","elementor"),sx:{maxHeight:"140px",width:"50px"}})),n.createElement(r.CardOverlay,{sx:{"&:hover":{backgroundColor:"rgba( 0, 0, 0, 0.75 )"}}},n.createElement(r.Stack,{gap:1},n.createElement(r.Button,{size:"tiny",color:"inherit",variant:"outlined",onClick:()=>g(an)},(0,a.__)("Select SVG","elementor")),n.createElement(r.Button,{size:"tiny",variant:"text",color:"inherit",startIcon:n.createElement(s.UploadIcon,null),onClick:()=>g(on)},(0,a.__)("Upload","elementor")))))))})),{env:sn}=(0,f.parseEnv)("@elementor/editor-controls"),un=O((()=>{const{value:e,setValue:t}=P(l.backgroundGradientOverlayPropTypeUtil);return n.createElement(q,null,n.createElement(r.UnstableGradientBox,{sx:{width:"auto",padding:1.5},value:(()=>{if(!e)return;const{type:t,angle:n,stops:l,positions:r}=e;return{type:t.value,angle:n.value,stops:l.value.map((({value:{color:e,offset:t}})=>({color:e.value,offset:t.value}))),positions:r?.value.split(" ")}})(),onChange:e=>{const n=(e=>({...e,type:l.stringPropTypeUtil.create(e.type),angle:l.numberPropTypeUtil.create(e.angle),stops:l.gradientColorStopPropTypeUtil.create(e.stops.map((({color:e,offset:t})=>l.colorStopPropTypeUtil.create({color:l.colorPropTypeUtil.create(e),offset:l.numberPropTypeUtil.create(t)}))))}))(e);n.positions&&(n.positions=l.stringPropTypeUtil.create(e.positions.join(" "))),t(n)}}))})),mn=l.backgroundGradientOverlayPropTypeUtil.create({type:l.stringPropTypeUtil.create("linear"),angle:l.numberPropTypeUtil.create(180),stops:l.gradientColorStopPropTypeUtil.create([l.colorStopPropTypeUtil.create({color:l.colorPropTypeUtil.create("rgb(0,0,0)"),offset:l.numberPropTypeUtil.create(0)}),l.colorStopPropTypeUtil.create({color:l.colorPropTypeUtil.create("rgb(255,255,255)"),offset:l.numberPropTypeUtil.create(100)})])}),dn=[{value:"fixed",label:(0,a.__)("Fixed","elementor"),renderContent:({size:e})=>n.createElement(s.PinIcon,{fontSize:e}),showTooltip:!0},{value:"scroll",label:(0,a.__)("Scroll","elementor"),renderContent:({size:e})=>n.createElement(s.PinnedOffIcon,{fontSize:e}),showTooltip:!0}],pn=()=>n.createElement(Te,null,n.createElement(r.Grid,{item:!0,xs:6},n.createElement(U,null,(0,a.__)("Attachment","elementor"))),n.createElement(r.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end",overflow:"hidden"}},n.createElement(dt,{options:dn}))),En=[{label:(0,a.__)("Center center","elementor"),value:"center center"},{label:(0,a.__)("Center left","elementor"),value:"center left"},{label:(0,a.__)("Center right","elementor"),value:"center right"},{label:(0,a.__)("Top center","elementor"),value:"top center"},{label:(0,a.__)("Top left","elementor"),value:"top left"},{label:(0,a.__)("Top right","elementor"),value:"top right"},{label:(0,a.__)("Bottom center","elementor"),value:"bottom center"},{label:(0,a.__)("Bottom left","elementor"),value:"bottom left"},{label:(0,a.__)("Bottom right","elementor"),value:"bottom right"},{label:(0,a.__)("Custom","elementor"),value:"custom"}],vn=()=>{const e=P(l.backgroundImagePositionOffsetPropTypeUtil),t=P(l.stringPropTypeUtil),o=!!e.value,i=(0,n.useRef)(null);return n.createElement(r.Grid,{container:!0,spacing:1.5},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(Te,null,n.createElement(r.Grid,{item:!0,xs:6},n.createElement(U,null,(0,a.__)("Position","elementor"))),n.createElement(r.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end",overflow:"hidden"}},n.createElement(r.Select,{fullWidth:!0,size:"tiny",onChange:n=>{const l=n.target.value||null;"custom"===l?e.setValue({x:null,y:null}):t.setValue(l)},disabled:t.disabled,value:(e.value?"custom":t.value)??""},En.map((({label:e,value:t})=>n.createElement(m.MenuListItem,{key:t,value:t??""},e))))))),o?n.createElement(C,{...e},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(r.Grid,{container:!0,spacing:1.5,ref:i},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(S,{bind:"x"},n.createElement(pe,{startIcon:n.createElement(s.LetterXIcon,{fontSize:"tiny"}),anchorRef:i}))),n.createElement(r.Grid,{item:!0,xs:6},n.createElement(S,{bind:"y"},n.createElement(pe,{startIcon:n.createElement(s.LetterYIcon,{fontSize:"tiny"}),anchorRef:i})))))):null)},bn=[{value:"repeat",label:(0,a.__)("Repeat","elementor"),renderContent:({size:e})=>n.createElement(s.GridDotsIcon,{fontSize:e}),showTooltip:!0},{value:"repeat-x",label:(0,a.__)("Repeat-x","elementor"),renderContent:({size:e})=>n.createElement(s.DotsHorizontalIcon,{fontSize:e}),showTooltip:!0},{value:"repeat-y",label:(0,a.__)("Repeat-y","elementor"),renderContent:({size:e})=>n.createElement(s.DotsVerticalIcon,{fontSize:e}),showTooltip:!0},{value:"no-repeat",label:(0,a.__)("No-repeat","elementor"),renderContent:({size:e})=>n.createElement(s.XIcon,{fontSize:e}),showTooltip:!0}],gn=()=>n.createElement(Te,null,n.createElement(r.Grid,{item:!0,xs:6},n.createElement(U,null,(0,a.__)("Repeat","elementor"))),n.createElement(r.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end"}},n.createElement(dt,{options:bn}))),fn=[{value:"auto",label:(0,a.__)("Auto","elementor"),renderContent:({size:e})=>n.createElement(s.LetterAIcon,{fontSize:e}),showTooltip:!0},{value:"cover",label:(0,a.__)("Cover","elementor"),renderContent:({size:e})=>n.createElement(s.ArrowsMaximizeIcon,{fontSize:e}),showTooltip:!0},{value:"contain",label:(0,a.__)("Contain","elementor"),renderContent:({size:e})=>n.createElement(s.ArrowBarBothIcon,{fontSize:e}),showTooltip:!0},{value:"custom",label:(0,a.__)("Custom","elementor"),renderContent:({size:e})=>n.createElement(s.PencilIcon,{fontSize:e}),showTooltip:!0}],hn=()=>{const e=P(l.backgroundImageSizeScalePropTypeUtil),t=P(l.stringPropTypeUtil),o=!!e.value,i=(0,n.useRef)(null);return n.createElement(r.Grid,{container:!0,spacing:1.5},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(Te,null,n.createElement(r.Grid,{item:!0,xs:6},n.createElement(U,null,(0,a.__)("Size","elementor"))),n.createElement(r.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end"}},n.createElement(st,{exclusive:!0,items:fn,onChange:n=>{"custom"===n?e.setValue({width:null,height:null}):t.setValue(n)},disabled:t.disabled,value:e.value?"custom":t.value})))),o?n.createElement(C,{...e},n.createElement(r.Grid,{item:!0,xs:12,ref:i},n.createElement(Te,null,n.createElement(r.Grid,{item:!0,xs:6},n.createElement(S,{bind:"width"},n.createElement(pe,{startIcon:n.createElement(s.ArrowsMoveHorizontalIcon,{fontSize:"tiny"}),extendedOptions:["auto"],anchorRef:i}))),n.createElement(r.Grid,{item:!0,xs:6},n.createElement(S,{bind:"height"},n.createElement(pe,{startIcon:n.createElement(s.ArrowsMoveVerticalIcon,{fontSize:"tiny"}),extendedOptions:["auto"],anchorRef:i})))))):null)},yn=l.backgroundColorOverlayPropTypeUtil.create({color:l.colorPropTypeUtil.create("#00000033")}),xn=()=>({$$type:"background-image-overlay",value:{image:{$$type:"image",value:{src:{$$type:"image-src",value:{url:{$$type:"url",value:sn.background_placeholder_image},id:null}},size:{$$type:"string",value:"large"}}}}}),_n=[{label:(0,a.__)("Thumbnail - 150 x 150","elementor"),value:"thumbnail"},{label:(0,a.__)("Medium - 300 x 300","elementor"),value:"medium"},{label:(0,a.__)("Large 1024 x 1024","elementor"),value:"large"},{label:(0,a.__)("Full","elementor"),value:"full"}],Cn=O((()=>{const{propType:e,value:t,setValue:r,disabled:o}=P(l.backgroundOverlayPropTypeUtil);return n.createElement(C,{propType:e,value:t,setValue:r,isDisabled:()=>o},n.createElement(Be,{openOnAdd:!0,disabled:o,values:t??[],setValues:r,label:(0,a.__)("Overlay","elementor"),itemSettings:{Icon:Sn,Label:Vn,Content:Tn,initialValues:xn()}}))})),Tn=({anchorEl:e=null,bind:t})=>n.createElement(S,{bind:t},n.createElement(wn,{anchorEl:e})),wn=({anchorEl:e})=>{const{getTabsProps:t,getTabProps:o,getTabPanelProps:i}=(({color:e,image:t,gradient:a})=>{const{value:o,setValue:i}=P(l.backgroundImageOverlayPropTypeUtil),{value:c,setValue:s}=P(l.backgroundColorOverlayPropTypeUtil),{value:u,setValue:m}=P(l.backgroundGradientOverlayPropTypeUtil),{getTabsProps:d,getTabProps:p,getTabPanelProps:E}=(0,r.useTabs)(c?"color":u?"gradient":"image"),v=(0,n.useRef)({image:t,color:e,gradient:a}),b=(e,t)=>{t&&(v.current[e]=t)},g=(e,t)=>{switch(t){case"image":i(v.current.image),b("color",c),b("gradient",u);break;case"gradient":m(v.current.gradient),b("color",c),b("image",o);break;case"color":s(v.current.color),b("image",o),b("gradient",u)}return d().onChange(e,t)};return{getTabProps:p,getTabPanelProps:E,getTabsProps:()=>({...d(),onChange:g})}})({image:xn().value,color:yn.value,gradient:mn.value});return n.createElement(r.Box,{sx:{width:"100%"}},n.createElement(r.Box,{sx:{borderBottom:1,borderColor:"divider"}},n.createElement(r.Tabs,{size:"small",variant:"fullWidth",...t(),"aria-label":(0,a.__)("Background Overlay","elementor")},n.createElement(r.Tab,{label:(0,a.__)("Image","elementor"),...o("image")}),n.createElement(r.Tab,{label:(0,a.__)("Gradient","elementor"),...o("gradient")}),n.createElement(r.Tab,{label:(0,a.__)("Color","elementor"),...o("color")}))),n.createElement(r.TabPanel,{sx:{p:1.5},...i("image")},n.createElement(Ce,null,n.createElement(Ln,null))),n.createElement(r.TabPanel,{sx:{p:1.5},...i("gradient")},n.createElement(un,null)),n.createElement(r.TabPanel,{sx:{p:1.5},...i("color")},n.createElement(Ce,null,n.createElement(Gn,{anchorEl:e}))))},Sn=({value:e})=>{switch(e.$$type){case"background-image-overlay":return n.createElement(kn,{value:e});case"background-color-overlay":return n.createElement(In,{value:e});case"background-gradient-overlay":return n.createElement(Pn,{value:e});default:return null}},zn=e=>e?.value?.color?.value?e.value.color.value:"",In=({value:e})=>{const t=zn(e);return n.createElement(On,{size:"inherit",component:"span",value:t})},kn=({value:e})=>{const{imageUrl:t}=Fn(e);return n.createElement(r.CardMedia,{image:t,sx:e=>({height:"1em",width:"1em",borderRadius:e.shape.borderRadius/2+"px",outline:`1px solid ${e.palette.action.disabled}`})})},Pn=({value:e})=>{const t=An(e);return n.createElement(On,{size:"inherit",component:"span",value:t})},Vn=({value:e})=>{switch(e.$$type){case"background-image-overlay":return n.createElement(Un,{value:e});case"background-color-overlay":return n.createElement($n,{value:e});case"background-gradient-overlay":return n.createElement(Rn,{value:e});default:return null}},$n=({value:e})=>{const t=zn(e);return n.createElement("span",null,t)},Un=({value:e})=>{const{imageTitle:t}=Fn(e);return n.createElement("span",null,t)},Rn=({value:e})=>"linear"===e.value.type.value?n.createElement("span",null,(0,a.__)("Linear Gradient","elementor")):n.createElement("span",null,(0,a.__)("Radial Gradient","elementor")),Gn=({anchorEl:e})=>{const t=P(l.backgroundColorOverlayPropTypeUtil);return n.createElement(C,{...t},n.createElement(S,{bind:"color"},n.createElement(he,{anchorEl:e})))},Ln=()=>{const e=P(l.backgroundImageOverlayPropTypeUtil);return n.createElement(C,{...e},n.createElement(S,{bind:"image"},n.createElement(Q,{sizes:_n})),n.createElement(S,{bind:"position"},n.createElement(vn,null)),n.createElement(S,{bind:"repeat"},n.createElement(gn,null)),n.createElement(S,{bind:"size"},n.createElement(hn,null)),n.createElement(S,{bind:"attachment"},n.createElement(pn,null)))},On=(0,r.styled)(r.UnstableColorIndicator)((({theme:e})=>({borderRadius:e.shape.borderRadius/2+"px"}))),Fn=e=>{let t,n=null;const l=e?.value.image.value?.src.value,{data:r}=(0,u.useWpMediaAttachment)(l.id?.value||null);if(l.id){const e=Bn(r?.filename);t=`${r?.title}${e}`||null,n=r?.url||null}else l.url&&(n=l.url.value,t=n?.substring(n.lastIndexOf("/")+1)||null);return{imageTitle:t,imageUrl:n}},Bn=e=>e?`.${e.substring(e.lastIndexOf(".")+1)}`:"",An=e=>{const t=e.value,n=t.stops.value?.map((({value:{color:e,offset:t}})=>`${e.value} ${t.value??0}%`))?.join(",");return"linear"===t.type.value?`linear-gradient(${t.angle.value}deg, ${n})`:`radial-gradient(circle at ${t.positions.value}, ${n})`},Mn=O((()=>{const e=P(l.backgroundPropTypeUtil),t=(0,p.isExperimentActive)("e_v_3_30"),o=(0,a.__)("Color","elementor");return n.createElement(C,{...e},n.createElement(S,{bind:"background-overlay"},n.createElement(Cn,null)),n.createElement(S,{bind:"color"},n.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},n.createElement(r.Grid,{item:!0,xs:6},t?n.createElement(Xe,null,o):n.createElement(U,null,o)),n.createElement(r.Grid,{item:!0,xs:6},n.createElement(he,null)))))})),Wn=(0,n.createContext)(void 0),Dn=()=>{const e=(0,n.useContext)(Wn);if(!e)throw new Error("useRepeatableControlContext must be used within RepeatableControl");return e},jn=O((({repeaterLabel:e,childControlConfig:t,showDuplicate:r,showToggle:a,initialValues:o,patternLabel:i,placeholder:c})=>{const{propTypeUtil:s}=t;if(!s)return null;const u=(0,n.useMemo)((()=>(0,l.createArrayPropUtils)(s.key,s.schema)),[s.key,s.schema]),m=(0,n.useMemo)((()=>({...t,placeholder:c||"",patternLabel:i||""})),[t,c,i]),{propType:d,value:p,setValue:E}=P(u);return n.createElement(C,{propType:d,value:p,setValue:E},n.createElement(Wn.Provider,{value:m},n.createElement(Be,{openOnAdd:!0,values:p??[],setValues:E,label:e,isSortable:!1,itemSettings:{Icon:Kn,Label:Xn,Content:Nn,initialValues:s.create(o||null)},showDuplicate:r,showToggle:a})))})),Nn=({bind:e})=>n.createElement(S,{bind:e},n.createElement(Hn,null)),Kn=()=>n.createElement(n.Fragment,null),Hn=()=>{const{component:e,props:t={}}=Dn();return n.createElement(Ce,{p:1.5},n.createElement(Te,null,n.createElement(e,{...t})))},qn=e=>"string"==typeof e?""===e.trim():!!Number.isNaN(e)||(Array.isArray(e)?0===e.length:"object"==typeof e&&null!==e&&0===Object.keys(e).length),Xn=({value:e})=>{const{placeholder:t,patternLabel:l}=Dn(),a=((e,t)=>{const n=Yn(e).map((e=>((e,t)=>t.split(".").reduce(((e,t)=>e?.[t]),e))(t,e)));return!(0===n.length||!n.some((e=>null==e))&&!n.every(qn))})(l,e)?t:(o=l,(i=e)?new Function(...Object.keys(i),`return \`${o}\`;`)(...Object.values(i)):o);var o,i;return n.createElement(r.Box,{component:"span",color:"text.tertiary"},a)},Yn=e=>e.match(/\$\{([^}]+)\}/g)?.map((e=>e.slice(2,-1)))||[],Qn=O(((e={})=>{const{value:t,setValue:o}=P(l.keyValuePropTypeUtil),[i,c]=(0,n.useState)(null),[s,u]=(0,n.useState)(null),[m,d]=(0,n.useState)({key:t?.key?.value||"",value:t?.value?.value||""}),p=e.keyName||(0,a.__)("Key","elementor"),E=e.valueName||(0,a.__)("Value","elementor"),[v,b,g]=(0,n.useMemo)((()=>[e.regexKey?new RegExp(e.regexKey):void 0,e.regexValue?new RegExp(e.regexValue):void 0,e.validationErrorMessage||(0,a.__)("Invalid Format","elementor")]),[e.regexKey,e.regexValue,e.validationErrorMessage]),f=(e,n)=>{const l=e.target.value;d((e=>({...e,[n]:l}))),((e,t)=>{if("key"===t&&v){const t=v.test(e);return c(t?null:g),t}if("value"===t&&b){const t=b.test(e);return u(t?null:g),t}return!0})(l,n)?o({...t,[n]:{value:l,$$type:"string"}}):o({...t,[n]:{value:"",$$type:"string"}})},h=null!==i,y=null!==s;return n.createElement(q,null,n.createElement(r.Grid,{container:!0,gap:1.5},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(r.FormLabel,{size:"tiny"},p),n.createElement(r.TextField,{autoFocus:!0,sx:{pt:1},size:"tiny",fullWidth:!0,value:m.key,onChange:e=>f(e,"key"),error:h}),h&&n.createElement(r.FormHelperText,{error:!0},i)),n.createElement(r.Grid,{item:!0,xs:12},n.createElement(r.FormLabel,{size:"tiny"},E),n.createElement(r.TextField,{sx:{pt:1},size:"tiny",fullWidth:!0,value:m.value,onChange:e=>f(e,"value"),disabled:h,error:y}),y&&n.createElement(r.FormHelperText,{error:!0},s))))})),Zn=[{label:(0,a.__)("Center center","elementor"),value:"center center"},{label:(0,a.__)("Center left","elementor"),value:"center left"},{label:(0,a.__)("Center right","elementor"),value:"center right"},{label:(0,a.__)("Top center","elementor"),value:"top center"},{label:(0,a.__)("Top left","elementor"),value:"top left"},{label:(0,a.__)("Top right","elementor"),value:"top right"},{label:(0,a.__)("Bottom center","elementor"),value:"bottom center"},{label:(0,a.__)("Bottom left","elementor"),value:"bottom left"},{label:(0,a.__)("Bottom right","elementor"),value:"bottom right"}],Jn=()=>{const e=P(l.positionPropTypeUtil),t=P(l.stringPropTypeUtil),o=(0,p.isExperimentActive)("e_v_3_31"),i=!!e.value&&o,c=(0,n.useMemo)((()=>{const e=[...Zn];return o&&e.push({label:(0,a.__)("Custom","elementor"),value:"custom"}),e}),[o]);return n.createElement(r.Grid,{container:!0,spacing:1.5},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(U,null,(0,a.__)("Object position","elementor"))),n.createElement(r.Grid,{item:!0,xs:6,sx:{overflow:"hidden"}},n.createElement(r.Select,{size:"tiny",disabled:t.disabled,value:(e.value?"custom":t.value)??"",onChange:n=>{const l=n.target.value||null;"custom"===l&&o?e.setValue({x:null,y:null}):t.setValue(l)},fullWidth:!0},c.map((({label:e,value:t})=>n.createElement(m.MenuListItem,{key:t,value:t??""},e))))))),i&&n.createElement(C,{...e},n.createElement(r.Grid,{item:!0,xs:12},n.createElement(r.Grid,{container:!0,spacing:1.5},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(S,{bind:"x"},n.createElement(pe,{startIcon:n.createElement(s.LetterXIcon,{fontSize:"tiny"})}))),n.createElement(r.Grid,{item:!0,xs:6},n.createElement(S,{bind:"y"},n.createElement(pe,{startIcon:n.createElement(s.LetterYIcon,{fontSize:"tiny"})})))))))},el=({label:e,bindValue:t,startIcon:l,anchorRef:a})=>n.createElement(r.Grid,{item:!0,xs:12},n.createElement(Te,{ref:a},n.createElement(r.Grid,{item:!0,xs:6},n.createElement(Xe,null,e)),n.createElement(r.Grid,{item:!0,xs:6},n.createElement(S,{bind:t},n.createElement(pe,{anchorRef:a,startIcon:l}))))),tl=[{label:(0,a.__)("Move X","elementor"),bindValue:"x",startIcon:n.createElement(s.ArrowRightIcon,{fontSize:"tiny"})},{label:(0,a.__)("Move Y","elementor"),bindValue:"y",startIcon:n.createElement(s.ArrowDownSmallIcon,{fontSize:"tiny"})},{label:(0,a.__)("Move Z","elementor"),bindValue:"z",startIcon:n.createElement(s.ArrowDownLeftIcon,{fontSize:"tiny"})}],nl=()=>{const e=P(l.moveTransformPropTypeUtil),t=(0,n.useRef)(null);return n.createElement(r.Grid,{container:!0,spacing:1.5},n.createElement(C,{...e},n.createElement(S,{bind:"transform-move"},tl.map((e=>n.createElement(el,{key:e.bindValue,...e,anchorRef:t}))))))},ll=({bind:e})=>{const{getTabsProps:t,getTabProps:l,getTabPanelProps:o}=(0,r.useTabs)("transform-move");return n.createElement(S,{bind:e},n.createElement(Ce,null,n.createElement(r.Box,{sx:{width:"100%"}},n.createElement(r.Box,{sx:{borderBottom:1,borderColor:"divider"}},n.createElement(r.Tabs,{size:"small",variant:"fullWidth",...t(),"aria-label":(0,a.__)("Transform","elementor")},n.createElement(r.Tab,{label:(0,a.__)("Move","elementor"),...l("transform-move")}))),n.createElement(r.TabPanel,{sx:{p:1.5},...o("transform-move")},n.createElement(nl,null)))))},rl=({value:e})=>"transform-move"===e.$$type?n.createElement(s.ArrowsMaximizeIcon,{fontSize:"tiny"}):null,al=e=>Object.values(e).map((e=>`${e?.value.size}${e?.value.unit}`)).join(", "),ol=e=>{const{$$type:t,value:l}=e.value;return"transform-move"===t?n.createElement(il,{label:(0,a.__)("Move","elementor"),value:al(l)}):""},il=({label:e,value:t})=>n.createElement(r.Box,{component:"span"},e,": ",t),cl={$$type:"transform-move",value:{x:{$$type:"size",value:{size:0,unit:"px"}},y:{$$type:"size",value:{size:0,unit:"px"}},z:{$$type:"size",value:{size:0,unit:"px"}}}},sl=O((()=>{const{propType:e,value:t,setValue:r,disabled:o}=P(l.transformPropTypeUtil);return n.createElement(C,{propType:e,value:t,setValue:r},n.createElement(Be,{openOnAdd:!0,disabled:o,values:t??[],setValues:r,label:(0,a.__)("Transform","elementor"),showDuplicate:!1,itemSettings:{Icon:rl,Label:ol,Content:ll,initialValues:cl}}))}));(window.elementorV2=window.elementorV2||{}).editorControls=t}(),window.elementorV2.editorControls?.init?.();