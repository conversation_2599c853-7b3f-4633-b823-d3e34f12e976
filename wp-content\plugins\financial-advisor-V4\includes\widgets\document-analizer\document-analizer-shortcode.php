<?php
if (!defined('ABSPATH')) {
    exit;
}

// Includi la classe Document_Stats per le statistiche
require_once plugin_dir_path(dirname(__FILE__)) . '../class-document-stats.php';

// Shortcode per il nuovo widget Document Analizer - Clone completo del Document Viewer
function render_document_analizer_shortcode($atts) {
    $atts = shortcode_atts([
        'title' => __('Analisi Avanzata Documento', 'document-analizer-plugin'),
    ], $atts);

    // Verifica che l'utente sia loggato o sia un subscriber
    $is_wp_user = is_user_logged_in();
    $is_subscriber = isset($_COOKIE['fa_subscriber_login']) && !empty($_COOKIE['fa_subscriber_login']);
    
    if (!$is_wp_user && !$is_subscriber) {
        return '<div class="document-analizer-login-required">
            <p>' . __('Devi effettuare il login per accedere a questa funzionalità.', 'document-viewer-plugin') . '</p>
            <a href="' . site_url('/') . '" class="login-button">' . __('Accedi', 'document-viewer-plugin') . '</a>
        </div>';
    }

    // Ottieni le statistiche utente (stessa logica del Document Viewer)
    $stats_html = get_document_analizer_stats_html();

    ob_start();
    ?>
    <div class="document-analizer-widget">
        <!-- Container principale con layout a 3 colonne -->
        <div class="analizer-layout">
            
            <!-- Colonna statistiche (sinistra) -->
            <div class="analizer-stats-column">
                <?php echo $stats_html; ?>
            </div>

            <!-- Colonna del form (centro) -->
            <div class="analizer-form-column">
                <h3><?php _e('Carica e Analizza', 'document-analizer-plugin'); ?></h3>

                <form id="document-analizer-form" enctype="multipart/form-data">
                    <!-- Custom logo upload -->
                    <div class="form-row custom-logo-field">
                        <input type="file" id="analizer-custom-logo-upload" accept="image/*" />
                        <label for="analizer-custom-logo-upload"><?php _e('Scegli Logo', 'document-analizer-plugin'); ?></label>
                        <label for="analizer-custom-logo-upload" class="logo-description"><?php _e('Logo Personalizzato (opzionale)', 'document-analizer-plugin'); ?></label>
                        <div class="logo-preview-container">
                            <img id="analizer-logo-preview" src="" alt="Logo Preview" style="display: none;">
                            <div id="analizer-logo-dimensions-info" style="display: none;"></div>
                        </div>
                    </div>

                    <!-- Titolo analisi -->
                    <div class="form-row">
                        <label for="analizer-analysis-title"><?php _e('Titolo Analisi:', 'document-analizer-plugin'); ?></label>
                        <input type="text" id="analizer-analysis-title" placeholder="<?php _e('Inserisci un titolo per l\'analisi', 'document-analizer-plugin'); ?>" />
                    </div>

                    <!-- File upload section -->
                    <div class="form-row file-upload-row">
                        <label for="analizer-document-upload"><?php _e('Carica Documento (PDF, Word o Immagine):', 'document-analizer-plugin'); ?></label>
                        <div class="file-upload-container">
                            <div class="file-upload-input">
                                <input type="file" id="analizer-document-upload" accept=".pdf,.doc,.docx" />
                                <label for="analizer-document-upload"><?php _e('Scegli PDF/Word', 'document-analizer-plugin'); ?></label>

                                <!-- Campo per il caricamento delle immagini -->
                                <input type="file" id="analizer-image-upload" accept="image/*" />
                                <label for="analizer-image-upload"><?php _e('Scegli Immagine', 'document-analizer-plugin'); ?></label>
                            </div>
                            <div id="analizer-document-info-inline" class="document-info-inline" style="display: none;">
                                <span id="analizer-document-name-inline"></span>
                                <span id="analizer-document-size-inline"></span>
                                <span id="analizer-document-type-inline"></span>
                                <span id="analizer-document-chars-inline"></span>
                                <span id="analizer-document-tokens-inline"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Descrizione/domanda -->
                    <div class="form-row">
                        <label for="analizer-document-description"><?php _e('Descrizione o Domanda:', 'document-analizer-plugin'); ?></label>

                        <!-- Menu a tendina per richieste predefinite -->
                        <div class="preset-queries-container">
                            <select id="analizer-preset-queries" class="preset-queries-select">
                                <option value=""><?php _e('-- Seleziona una richiesta predefinita --', 'document-analizer-plugin'); ?></option>
                                <?php
                                // Recupera le richieste predefinite dal database (stessa logica del Document Viewer)
                                if (class_exists('Document_Viewer_Plugin')) {
                                    $document_viewer = new Document_Viewer_Plugin();
                                    $preset_queries = $document_viewer->get_preset_queries();
                                } else {
                                    // Fallback: recupera direttamente dal database
                                    global $wpdb;
                                    $table_name = $wpdb->prefix . 'document_preset_queries';
                                    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name) {
                                        $preset_queries = $wpdb->get_results("SELECT * FROM $table_name ORDER BY title", ARRAY_A);
                                    } else {
                                        $preset_queries = [];
                                    }
                                }
                                
                                foreach ($preset_queries as $query) {
                                    echo '<option value="' . esc_attr($query['query_text']) . '">' . esc_html($query['title']) . '</option>';
                                }
                                ?>
                            </select>
                            <span class="preset-info-tip" title="<?php _e('Seleziona una richiesta predefinita o scrivi la tua domanda personalizzata', 'document-analizer-plugin'); ?>">?</span>
                        </div>

                        <textarea id="analizer-document-description" rows="4"
                                placeholder="<?php _e('Inserisci una domanda o descrivi cosa vuoi analizzare nel documento', 'document-analizer-plugin'); ?>"></textarea>
                    </div>

                    <!-- Action buttons -->
                    <div class="form-row">
                        <button type="button" id="analizer-analyze-description"><?php _e('Analizza', 'document-analizer-plugin'); ?></button>
                        <button type="button" id="analizer-clear-document"><?php _e('Cancella', 'document-analizer-plugin'); ?></button>
                    </div>

                    <!-- Note/Annotations section -->
                    <div class="form-row">
                        <label for="analizer-document-annotations"><?php _e('Note Aggiuntive (opzionale):', 'document-analizer-plugin'); ?></label>
                        <textarea id="analizer-document-annotations" rows="3"
                                placeholder="<?php _e('Eventuali note da includere nel report', 'document-analizer-plugin'); ?>"></textarea>
                    </div>

                    <!-- Export actions -->
                    <div class="export-actions">
                        <button type="button" id="analizer-export-pdf"><?php _e('Esporta in PDF', 'document-analizer-plugin'); ?></button>
                        <button type="button" id="analizer-save-analysis"><?php _e('Salva Analisi', 'document-analizer-plugin'); ?></button>
                        <div id="analizer-save-result-message" class="save-result-message"></div>
                    </div>
                </form>
            </div>

            <!-- Colonna di visualizzazione (destra) -->
            <div class="analizer-display-column">
                <!-- Analysis results -->
                <h3><?php _e('', 'document-analizer-plugin'); ?></h3>
                <div id="analizer-analysis-results">
                    <div style="background-image: url('<?php echo esc_url(plugin_dir_url(dirname(dirname(dirname(__FILE__)))) . 'images/bg-da.jpg'); ?>'); background-size: cover; background-position: center; padding: 40px 20px; border-radius: 8px; min-height: 150px; display: flex; align-items: center; padding: 30px; border-radius: 8px; max-width: 800px;">
                        <div style="padding: 20px; border-radius: 8px; max-width: 600px;">
                            <h5 style="text-align: left; margin: 0 0 15px 0; color: #2d3748; font-size: 1.5em; line-height: 1.4;">
                                <?php _e('Carica un documento', 'document-analizer-plugin'); ?>
                            </h5>
                            <p>
                                <h5 style="text-align: left; margin: 0 0 15px 0; color: #2d3748; font-size: 1.5em; line-height: 1.4;">
                                <?php _e('per iniziare l\'analisi avanzata', 'document-analizer-plugin'); ?>
                                </h5>
                            </p>
                            <h5 style="text-align: left; margin: 0; color: #2d3748; font-size: 1.5em; line-height: 1.4;">
                                <?php _e('con supporto AI Financial Expert', 'document-analizer-plugin'); ?>
                            </h5>
                        </div>
                    </div>
                </div>

                <!-- Document display -->
                <h3><?php _e('Visualizzazione Documento', 'document-analizer-plugin'); ?></h3>

                <!-- Status notification area -->
                <div id="analizer-document-notification-area" class="document-notification-area" style="display: none;">
                    <div class="notification-content"></div>
                </div>

                <!-- Zoom controls -->
                <div class="zoom-controls">
                    <button type="button" id="analizer-zoom-in" class="zoom-btn" style="display: none;">+</button>
                    <button type="button" id="analizer-zoom-out" class="zoom-btn" style="display: none;">-</button>
                </div>

                <div id="analizer-document-display" style="display: none;">
                    <iframe id="analizer-document-frame" style="display: none;"></iframe>
                </div>
            </div>

        </div> <!-- End analizer-layout -->
    </div> <!-- End document-analizer-widget -->
    <?php
    return ob_get_clean();
}

// Funzione per ottenere le statistiche HTML (clone della logica del Document Viewer)
function get_document_analizer_stats_html() {
    $user_id = get_current_user_id();
    $is_wp_user = $user_id > 0;
    $external_user_id = null;

    // Verifica se è un utente esterno non WordPress
    if (!$is_wp_user && isset($_COOKIE['fa_subscriber_login'])) {
        try {
            $cookie_data = json_decode(base64_decode($_COOKIE['fa_subscriber_login']), true);
            if (isset($cookie_data['id'])) {
                $external_user_id = intval($cookie_data['id']);
            }
        } catch (Exception $e) {
            // Log dell'errore se necessario
        }
    }

    // Se non è né un utente WordPress né un utente esterno, mostra messaggio di login
    if (!$is_wp_user && !$external_user_id) {
        return '<div class="stats-not-logged">
            <p>Accedi per visualizzare le tue statistiche di utilizzo.</p>
        </div>';
    }

    // Ottieni le statistiche usando la stessa classe del Document Viewer
    $document_stats = new Document_Stats();
    if ($is_wp_user) {
        $stats = $document_stats->get_user_stats($user_id);
        if (empty($stats)) {
            $document_stats->initialize_user_stats($user_id);
            $stats = $document_stats->get_user_stats($user_id);
        }
    } else {
        $stats = $document_stats->get_external_user_stats($external_user_id);
        if (empty($stats)) {
            $stats = (object)[
                'analysis_count' => 0,
                'tokens_used' => 0,
                'credits_available' => 0,
                'actual_cost' => 0,
                'tot_cost' => 0
            ];
        }
    }

    // Formattazione numeri
    $analysis_count = $stats->analysis_count;
    $tokens_used = number_format($stats->tokens_used, 0, ',', '.');
    $credits_available = number_format((float)$stats->credits_available, 2, ',', '.');
    $actual_cost = number_format((float)$stats->actual_cost, 2, ',', '.');

    // Recupera le analisi recenti
    $recent_analyses = $document_stats->get_recent_analyses($is_wp_user ? $user_id : $external_user_id, 5);

    // Genera HTML delle analisi recenti
    $recent_analyses_html = '';
    if (!empty($recent_analyses)) {
        $recent_analyses_html = '<ul class="recent-analyses-list" id="recent-analyses-list">';
        foreach ($recent_analyses as $analysis) {
            $recent_analyses_html .= '<li class="recent-analysis-item" data-id="' . esc_attr($analysis['id']) . '">
                <div class="analysis-item-title">' . esc_html($analysis['title']) . '</div>
                <div class="analysis-item-meta">
                    <span class="analysis-date">' . esc_html($analysis['date']) . '</span>
                    <span class="analysis-tokens">' . esc_html(number_format($analysis['tokens'], 0, ',', '.')) . ' token</span>
                </div>
            </li>';
        }
        $recent_analyses_html .= '</ul>';
    } else {
        $recent_analyses_html = '<p class="no-analyses">Nessuna analisi effettuata.</p>';
    }

    // Genera HTML delle statistiche con prefisso specifico per il Document Analizer
    return '<div class="document-analizer-stats-container">
        <div class="stats-grid">
            <div class="stats-row costs-row">
                <div class="stats-item cost-item">
                    <div class="stats-label">
                        Spesa Stimata
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Stima del costo basata sul numero di token utilizzati</div>
                    </div>
                    <div class="stats-value cost-highlight" id="analizer-cost-estimate">€' . esc_html($actual_cost) . '</div>
                </div>
                <div class="stats-item cost-item">
                    <div class="stats-label">
                        Spesa Effettiva
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Costo effettivo dell\'analisi completata</div>
                    </div>
                    <div class="stats-value cost-highlight" id="analizer-actual-cost">€' . esc_html($actual_cost) . '</div>
                </div>
            </div>

            <div class="stats-row credit-row">
                <div class="stats-item credit-item">
                    <div class="stats-label">
                        Credito Disponibile
                        <span class="stats-info-icon">i</span>
                        <div class="stats-tooltip">Credito disponibile per l\'esecuzione di nuove analisi</div>
                    </div>
                    <div class="stats-value credit-highlight" id="analizer-credits-available">€' . esc_html($credits_available) . '</div>
                </div>
            </div>
        </div>

        <!-- Sezione Analisi Recenti -->
        <div class="stats-section" id="recent-analyses-container">
            <div class="stats-section-header">
                <h4 class="stats-section-title">Analisi Recenti</h4>
                <span class="toggle-icon"></span>
            </div>
            <div class="stats-section-content">
                ' . $recent_analyses_html . '
            </div>
        </div>
    </div>';
}

add_shortcode('document_analizer', 'render_document_analizer_shortcode');
