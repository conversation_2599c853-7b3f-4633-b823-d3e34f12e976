# Translation of Themes - Blocksy in Italian
# This file is distributed under the same license as the Themes - Blocksy package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-04-24 16:34:42+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: it\n"
"Project-Id-Version: Themes - Blocksy\n"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "Blocksy is a blazing fast and lightweight WordPress theme built with the latest web technologies. It was built with the G<PERSON>nberg editor in mind and has a lot of options that makes it extendable and customizable. You can easily create any type of website, such as business agency, shop, corporate, education, restaurant, blog, portfolio, landing page and so on. It works like a charm with popular WordPress page builders, including Elementor, Beaver Builder, Visual Composer and Brizy. Since it is responsive and adaptive, translation ready, SEO optimized and has WooCommerce built-in, you will experience an easy build and even an increase in conversions."
msgstr "Blocksy è un tema WordPress veloce e leggero, creato con le ultime tecnologie web. È stato creato pensando all'editor Gutenberg e ha molte opzioni che lo rendono estensibile e personalizzabile. Puoi creare facilmente qualsiasi tipo di sito web, come agenzia commerciale, negozio, azienda, istruzione, ristorante, blog, portfolio, landing page e così via. Funziona a meraviglia con i più popolari page builder di WordPress, tra cui Elementor, Beaver Builder, Visual Composer e Brizy. Poiché è reattivo e adattabile, pronto per la traduzione, ottimizzato per SEO e ha WooCommerce integrato, sperimenterai una creazione facile e persino un aumento delle conversioni."

#. Theme Name of the theme
#: style.css admin/helpers/meta-boxes.php:145
#, gp-priority: high
msgid "Blocksy"
msgstr "Blocksy"

#: inc/panel-builder/header/cart/options.php:75
#: inc/panel-builder/header/search/options.php:72
#: inc/panel-builder/header/trigger/options.php:49
msgid "Icon Visibility"
msgstr "Visibilità icona"

#: inc/components/social-box.php:948
#: inc/options/single-elements/post-share-box.php:152
msgid "Bluesky"
msgstr "Bluesky"

#: inc/panel-builder/header/cart/options.php:161
msgid "Hide Empty Cart"
msgstr "Nascondi il carrello vuoto"

#: inc/options/general/posts-listing.php:1267
msgid "Featured Image Shadow"
msgstr "Ombra dell'immagine in evidenza"

#: inc/options/general/form-elements.php:46
#: inc/options/general/form-elements.php:144
msgid "Form Fields"
msgstr "Campi del modulo"

#. translators: %s is the product name
#: woocommerce/cart/mini-cart.php:58
msgid "&ldquo;%s&rdquo; has been removed from your cart"
msgstr "&ldquo;%s&rdquo; è stato rimosso dal tuo carrello"

#: inc/options/woocommerce/card-product-elements.php:970
msgid "Button Background"
msgstr "Sfondo del pulsante"

#: inc/options/general/posts-listing.php:991
#: inc/options/single-elements/related-posts.php:810
msgid "Meta Button Font Color"
msgstr "Colore del font del pulsante Meta"

#: inc/options/general/meta.php:354
#: inc/options/woocommerce/card-product-elements.php:481
msgid "Taxonomy Source"
msgstr "Sorgente tassonomia"

#: inc/options/woocommerce/single-product-layers.php:396
msgid "Reasons to Buy"
msgstr "Motivi per l'acquisto"

#: inc/options/single-elements/post-share-box.php:282
msgid "Tooltip"
msgstr "Tooltip"

#: inc/manager.php:257
msgid "Failed to Copy"
msgstr "Errore durante la copia"

#: inc/manager.php:256
msgid "Copied!"
msgstr "Copiato!"

#: inc/components/social-box.php:1393
msgid "Share on %s"
msgstr "Condividi su %s"

#: inc/components/social-box.php:935 inc/components/social-box.php:1398
#: inc/options/single-elements/post-share-box.php:156
msgid "Copy to Clipboard"
msgstr "Copia negli appunti"

#: inc/options/integrations/the-events-calendar/single.php:24
msgid "Event Structure"
msgstr "Struttura dell'evento"

#: inc/panel-builder/header/middle-row/options.php:222
#: inc/panel-builder/header/middle-row/options.php:249
#: inc/panel-builder/header/middle-row/options.php:259
#: inc/panel-builder/header/middle-row/options.php:269
msgid "Backdrop Blur"
msgstr "Sfocatura dello sfondo"

#: inc/options/single-elements/post-share-box.php:144
msgid "Threads"
msgstr "Threads"

#: inc/options/woocommerce/card-product-elements.php:591
msgid "Align all buttons on the same line, when positioned as the last element."
msgstr "Allinea tutti i pulsanti sulla stessa riga, quando posizionati come ultimo elemento."

#: inc/components/blocks/blocks-fallback.php:76
msgid "The %s block is moved to the Blocksy Companion plugin. Please install this plugin to get access to the block."
msgstr "Il blocco %s è stato spostato nel plugin Blocksy Companion. Installa questo plugin per accedere al blocco."

#: inc/options/woocommerce/general/checkout-page.php:230
msgid "Order Review Padding"
msgstr "Spaziatura interna ordinamento recensione"

#: inc/options/woocommerce/general/checkout-page.php:220
msgid "Order Review Border Radius"
msgstr "Raggio del bordo ordinamento recensione"

#: inc/options/woocommerce/general/checkout-page.php:205
msgid "Order Review Border"
msgstr "Bordo ordinamento recensione"

#: inc/options/woocommerce/general/cart-page.php:88
msgid "Cart Totals Padding"
msgstr "Spaziatura interna totali carrello"

#: inc/options/woocommerce/general/cart-page.php:78
msgid "Cart Totals Border Radius"
msgstr "Raggio del bordo totali carrello"

#: inc/options/woocommerce/general/cart-page.php:63
msgid "Cart Totals Border"
msgstr "Bordo totali carrello"

#: inc/options/woocommerce/single-product-elements.php:87
msgid "Right Column"
msgstr "Colonna destra"

#: inc/options/woocommerce/single-product-elements.php:73
msgid "Left Column"
msgstr "Colonna sinistra"

#: inc/options/woocommerce/related-upsells.php:159
msgid "Module Title Color"
msgstr "Colore del titolo del modulo"

#: inc/panel-builder/header/search/options.php:526
msgid "Search through taxonomies from selected custom post types."
msgstr "Ricerca tra le tassonomie del tipo di contenuto personalizzato selezionato."

#: inc/panel-builder/header/search/options.php:522
msgid "Search Through Taxonomies"
msgstr "Ricerca tra le tassonomie"

#: inc/options/woocommerce/single-product-gallery.php:347
msgid "Spacing"
msgstr "Spaziatura"

#: inc/options/woocommerce/single-product-gallery.php:301
msgid "Gallery Thumbnails"
msgstr "Miniature della galleria"

#: inc/options/woocommerce/single-product-elements.php:574
msgid "Container Border"
msgstr "Bordo del contenitore"

#: inc/options/woocommerce/card-product-elements.php:583
msgid "Automatically hide \"Add to cart\" button after adding the product to cart."
msgstr "Nascondi automaticamente il pulsante \"Aggiungi al carrello\" dopo aver aggiunto il prodotto al carrello."

#: inc/options/woocommerce/card-product-elements.php:579
msgid "Auto Hide"
msgstr "Nascondi automatico"

#: inc/options/general/posts-listing.php:225
#: inc/options/single-elements/related-posts.php:406
#: inc/options/woocommerce/card-product-elements.php:391
msgid "Heading Tag"
msgstr "Tag del titolo"

#: inc/integrations/coauthors.php:32
msgid " and "
msgstr " e "

#: inc/options/general/posts-listing.php:246
#: inc/options/general/posts-listing.php:419
#: inc/options/single-elements/related-posts.php:379
#: inc/options/single-elements/related-posts.php:429
msgid "Link To Post"
msgstr "Link all'articolo"

#: inc/panel-builder/header/mobile-menu/options.php:93
msgid "Submenu Dots"
msgstr "Punti del sottomenu"

#: inc/panel-builder/header/button/options.php:525
msgid "Secondary Label Text Color"
msgstr "Colore del testo dell'etichetta secondaria"

#: inc/panel-builder/header/button/options.php:516
msgid "Secondary Label Text Font"
msgstr "Font del testo dell'etichetta secondaria"

#: inc/panel-builder/header/button/options.php:325
#: inc/panel-builder/header/button/options.php:355
#: inc/panel-builder/header/button/options.php:407
#: inc/panel-builder/header/button/options.php:457
#: inc/panel-builder/header/button/options.php:555
#: inc/panel-builder/header/button/options.php:605
#: inc/panel-builder/header/button/options.php:655
msgid "Label Text Color"
msgstr "Colore del testo dell'etichetta"

#: inc/panel-builder/header/button/options.php:318
msgid "Label Text Font"
msgstr "Font del testo dell'etichetta"

#: inc/panel-builder/header/button/options.php:69
#: inc/panel-builder/header/button/view.php:93
msgid "Hurry Up!"
msgstr "Affrettati!"

#: inc/panel-builder/header/button/options.php:54
msgid "Secondary Label"
msgstr "Etichetta secondaria"

#: inc/panel-builder/header/menu/options.php:396
#: inc/panel-builder/header/menu/options.php:437
#: inc/panel-builder/header/menu/options.php:476
#: inc/panel-builder/header/menu/options.php:514
msgid "Indicator Color"
msgstr "Colore dell'indicatore"

#: inc/panel-builder/header/menu/options.php:104
msgid "Items Inner Spacing"
msgstr "Spaziatura interna degli elementi"

#: inc/options/general/page-title.php:376
msgid "Archive Label"
msgstr "Etichetta d'archivio"

#: inc/components/blocks/blocks-fallback.php:39
msgid "Advanced Search"
msgstr "Ricerca avanzata"

#: inc/components/blocks/blocks-fallback.php:42
msgid "Dynamic Data"
msgstr "Dati dinamici"

#: inc/options/woocommerce/card-product-elements.php:1014
msgid "SKU"
msgstr "SKU"

#: inc/components/blocks/blocks-fallback.php:35
msgid "About Me"
msgstr "Chi sono"

#: inc/panel-builder/header/offcanvas/options.php:135
msgid "Offset"
msgstr "Offset"

#: inc/components/blocks/blocks-fallback.php:38
msgid "Advanced Posts"
msgstr "Articoli avanzati"

#: searchform.php:282
msgid "Search in category"
msgstr "Cerca per categoria"

#: inc/panel-builder/header/text/options.php:319
#: inc/panel-builder/header/text/options.php:348
#: inc/panel-builder/header/text/options.php:370
#: inc/panel-builder/header/text/options.php:391
msgid "Heading Color"
msgstr "Colore del titolo"

#: inc/panel-builder/header/offcanvas/options.php:95
msgid "Panel Heading"
msgstr "Titolo del pannello"

#: inc/panel-builder/header/logo/options.php:242
#: inc/panel-builder/header/offcanvas-logo/options.php:60
msgid "Inline SVG File"
msgstr "File SVG in linea"

#: inc/panel-builder/header/button/options.php:174
msgid "Create Popup"
msgstr "Crea popup"

#: inc/options/woocommerce/single-product-elements.php:589
#: inc/panel-builder/footer/options.php:156
msgid "Container Border Radius"
msgstr "Raggio del bordo del contenitore"

#: inc/panel-builder/footer/options.php:24
msgid "Container Bottom Offset"
msgstr "Offset in basso del contenitore"

#: inc/options/woocommerce/single-product-tabs.php:141
msgid "Description Heading "
msgstr "Descrizione Titolo "

#: inc/options/woocommerce/single-product-tabs.php:122
msgid "Close Adjacent Tabs"
msgstr "Chiudi le schede adiacenti"

#: inc/options/woocommerce/single-product-tabs.php:110
msgid "First Tab Expanded"
msgstr "Prima scheda espansa"

#: inc/options/woocommerce/single-product-tabs.php:92
msgid "Summary"
msgstr "Sommario"

#: inc/options/woocommerce/single-product-layers.php:408
msgid "Items"
msgstr "Elementi"

#: inc/options/woocommerce/single-product-layers.php:302
msgid "Payment Methods"
msgstr "Metodi di pagamento"

#: inc/options/woocommerce/single-product-layers.php:50
msgid "Item Label"
msgstr "Etichetta elemento"

#: inc/options/woocommerce/single-product-layers.php:31
msgid "Google Pay"
msgstr "Google Pay"

#: inc/options/woocommerce/single-product-layers.php:27
msgid "Apple Pay"
msgstr "Apple Pay"

#: inc/options/woocommerce/single-product-layers.php:23
msgid "PayPal"
msgstr "PayPal"

#: inc/options/woocommerce/single-product-layers.php:19
#: inc/options/woocommerce/single-product-layers.php:345
msgid "Discover"
msgstr "Scopri"

#: inc/options/woocommerce/single-product-layers.php:15
#: inc/options/woocommerce/single-product-layers.php:339
msgid "Amex"
msgstr "Amex"

#: inc/options/woocommerce/single-product-layers.php:11
#: inc/options/woocommerce/single-product-layers.php:333
msgid "Mastercard"
msgstr "Mastercard"

#: inc/options/woocommerce/single-product-layers.php:7
#: inc/options/woocommerce/single-product-layers.php:327
msgid "Visa"
msgstr "Visa"

#: inc/options/woocommerce/single-product-gallery.php:340
msgid "Image size used for the gallery thumbnails on single product pages."
msgstr "Dimensione dell'immagine utilizzata per le miniature della galleria nelle pagine dei singoli prodotti."

#: inc/options/woocommerce/single-product-elements.php:535
msgid "Payment Methods Icons Color"
msgstr "Colore delle icone dei metodi di pagamento"

#: inc/options/woocommerce/single-product-elements.php:190
msgid "Price Font Color"
msgstr "Colore del font del prezzo"

#: inc/options/woocommerce/single-product-elements.php:118
msgid "Sticky Container"
msgstr "Contenitore fisso"

#: inc/options/woocommerce/general/product-badges.php:104
#: inc/options/woocommerce/general/product-badges.php:118
#: inc/options/woocommerce/general/product-badges.php:163
msgid "Badge Label"
msgstr "Etichetta badge"

#: inc/options/woocommerce/general/product-badges.php:53
msgid "Show Sale Badge"
msgstr "Mostra il badge In offerta"

#: inc/options/woocommerce/general/product-badges.php:13
msgid "Product Badges"
msgstr "Badge prodotto"

#: inc/options/woocommerce/card-product-elements.php:675
msgid "Rows Gap"
msgstr "Interlinea"

#: inc/options/woocommerce/card-product-elements.php:659
msgid "Columns Gap"
msgstr "Spazio tra le colonne"

#: inc/options/woocommerce/card-product-elements.php:610
msgid "Add to Cart and Price"
msgstr "Aggiungi al carrello e prezzo"

#: inc/options/woocommerce/card-product-elements.php:294
msgid "Product Image"
msgstr "Immagine prodotto"

#: inc/options/woocommerce/products-sorting.php:5
msgid "Products Sorting"
msgstr "Ordinamento prodotti"

#: inc/options/woocommerce/results-count.php:5
msgid "Results Count"
msgstr "Conteggio risultati"

#: inc/options/single-elements/related-posts.php:763
msgid "Posts Meta Font"
msgstr "Font dei meta degli articoli"

#: inc/options/single-elements/related-posts.php:684
msgid "Posts Title Font"
msgstr "Font del titolo degli articoli"

#: inc/options/single-elements/post-share-box.php:444
#: inc/options/single-elements/post-tags.php:167
#: inc/options/single-elements/related-posts.php:623
#: inc/options/woocommerce/related-upsells.php:151
msgid "Module Title Font"
msgstr "Font del titolo del modulo"

#: inc/options/pages/author-page.php:48 inc/options/pages/search-page.php:52
#: inc/options/posts/blog.php:55 inc/options/posts/categories.php:41
#: inc/options/posts/custom-post-type-archive.php:52
msgid "Posts Listing"
msgstr "Elenco degli articoli"

#: inc/options/general/typography.php:143
msgid "Figcaption"
msgstr "Figcaption"

#: inc/options/general/posts-listing.php:490
msgid "Button Type"
msgstr "Tipo di pulsante"

#: inc/options/general/layout.php:58
msgid "Adjusts horizontal spacing between main content area and edges of the screen."
msgstr "Regola la spaziatura orizzontale tra l'area del contenuto principale e i bordi dello schermo."

#: inc/options/general/layout.php:45
msgid "Content Edge Spacing"
msgstr "Spaziatura del bordo del contenuto"

#: inc/options/general/layout.php:41
msgid "Adjusts vertical spacing between main content area and header/footer."
msgstr "Regola la spaziatura verticale tra l'area del contenuto principale e header/footer."

#: inc/options/general/breadcrumbs.php:255
msgid "Shop Page in Breadcrumbs"
msgstr "Breadcrumb nella pagina del negozio"

#: inc/options/general/breadcrumbs.php:242
msgid "Blog Page in Breadcrumbs"
msgstr "Pagina del blog nei breadcrumb."

#: inc/components/patterns.php:32
msgid "Patterns that contain buttons and call to actions."
msgstr "Pattern che contengono pulsanti e inviti all'azione."

#: inc/components/patterns.php:31
msgctxt "Block pattern category"
msgid "Blocksy"
msgstr "Blocksy"

#: inc/helpers.php:214
msgid "WooCommerce Archive Thumbnail"
msgstr "Miniatura dell'archivio WooCommerce"

#: inc/helpers.php:210
msgid "WooCommerce Gallery Thumbnail"
msgstr "Miniatura della galleria di WooCommerce"

#: inc/helpers.php:209
msgid "WooCommerce Single"
msgstr "Singolo WooCommerce"

#: inc/helpers.php:208
msgid "WooCommerce Thumbnail"
msgstr "Miniatura WooCommerce"

#: inc/components/woocommerce/single/single.php:421
#: inc/options/woocommerce/single-product-layers.php:402
msgid "Extra Features"
msgstr "Funzionalità extra"

#: inc/components/woocommerce/single/single.php:405
#: inc/options/woocommerce/single-product-layers.php:437
msgid "Money Back Guarantee"
msgstr "Garanzia soddisfatti o rimborsati"

#: inc/components/woocommerce/single/single.php:400
#: inc/options/woocommerce/single-product-layers.php:432
msgid "Worldwide Shipping"
msgstr "Spedizione in tutto il mondo"

#: inc/components/woocommerce/single/single.php:395
#: inc/options/woocommerce/single-product-layers.php:427
msgid "Satisfaction Guarantee"
msgstr "Soddisfazione garantita"

#: inc/components/woocommerce/single/single.php:390
#: inc/options/woocommerce/single-product-layers.php:422
msgid "Secure Payments"
msgstr "Pagamenti sicuri"

#: inc/components/woocommerce/single/single.php:385
#: inc/options/woocommerce/single-product-layers.php:417
msgid "Premium Quality"
msgstr "Qualità Premium"

#: inc/components/woocommerce/single/single.php:289
#: inc/options/woocommerce/single-product-layers.php:312
msgid "Guaranteed Safe Checkout"
msgstr "Pagamento sicuro garantito"

#: inc/components/woocommerce/single/additional-actions-layer.php:107
#: inc/components/woocommerce/single/single-modifications.php:100
#: inc/options/general/posts-listing.php:255
#: inc/options/general/posts-listing.php:428
#: inc/options/general/posts-listing.php:473
#: inc/options/general/posts-listing.php:525
#: inc/options/general/posts-listing.php:572
#: inc/options/general/posts-listing.php:593
#: inc/options/single-elements/related-posts.php:388
#: inc/options/single-elements/related-posts.php:438
#: inc/options/single-elements/related-posts.php:479
#: inc/options/woocommerce/card-product-elements.php:372
#: inc/options/woocommerce/card-product-elements.php:411
#: inc/options/woocommerce/card-product-elements.php:431
#: inc/options/woocommerce/card-product-elements.php:462
#: inc/options/woocommerce/card-product-elements.php:512
#: inc/options/woocommerce/card-product-elements.php:558
#: inc/options/woocommerce/card-product-elements.php:595
#: inc/options/woocommerce/card-product-elements.php:617
#: inc/options/woocommerce/single-product-layers.php:136
#: inc/options/woocommerce/single-product-layers.php:153
#: inc/options/woocommerce/single-product-layers.php:170
#: inc/options/woocommerce/single-product-layers.php:190
#: inc/options/woocommerce/single-product-layers.php:210
#: inc/options/woocommerce/single-product-layers.php:228
#: inc/options/woocommerce/single-product-layers.php:271
#: inc/options/woocommerce/single-product-layers.php:288
#: inc/options/woocommerce/single-product-layers.php:381
#: inc/options/woocommerce/single-product-layers.php:444
msgid "Bottom Spacing"
msgstr "Spaziatura inferiore"

#: inc/components/woocommerce/single/additional-actions-layer.php:53
msgid "Buttons Type"
msgstr "Tipo di pulsanti"

#: inc/components/woocommerce/single/additional-actions-layer.php:49
#: inc/options/woocommerce/card-product-elements.php:25
#: inc/options/woocommerce/card-product-elements.php:38
#: inc/options/woocommerce/card-product-elements.php:75
msgid "Additional Actions"
msgstr "Azioni aggiuntive"

#: inc/components/woocommerce/common/stock-badge.php:34
#: inc/options/woocommerce/general/product-badges.php:166
msgid "SOLD OUT"
msgstr "ESAURITO"

#: inc/components/woocommerce/common/sale-flash.php:13
msgid "SALE"
msgstr "OFFERTA"

#: inc/components/woocommerce/common/rest-api.php:127
msgid "Product Status"
msgstr "Stato del prodotto"

#. translators: 1: the search query
#: inc/components/hero/elements.php:186
msgid "Search Results for %1$s"
msgstr "Risultati della ricerca per: %1$s"

#: inc/components/builder/header-elements.php:247
msgid "Close search modal"
msgstr "Chiudi la ricerca modale"

#: searchform.php:123
msgid "Select Category"
msgstr "Seleziona la categoria"

#: inc/options/pages/search-page.php:118
#: inc/panel-builder/header/search/options.php:488
msgid "Live Results Product Status"
msgstr "Risultato del prezzo del prodotto in tempo reale"

#: inc/components/blocks/legacy/legacy-newsletter-subscribe.php:18
msgid "Enter your email address below to subscribe to our newsletter"
msgstr "Inserisci il tuo indirizzo email qui sotto per iscriverti alla nostra newsletter"

#: inc/components/blocks/legacy/legacy-newsletter-subscribe.php:17
msgid "Newsletter"
msgstr "Newsletter"

#: inc/components/blocks/legacy/legacy-advertisement-transformer.php:19
msgid "Insert ad code here"
msgstr "Inserisci il codice pubblicitario qui"

#: inc/components/blocks/legacy/legacy-advertisement-transformer.php:17
msgid "Advertisement"
msgstr "Pubblicità"

#: inc/components/woocommerce/common/rest-api.php:122
msgid "In Stock"
msgstr "In magazzino"

#: inc/options/general/meta.php:418
msgid "Terms accent color"
msgstr "Colore in risalto dei termini"

#: inc/components/blocks/legacy/legacy-contact-info-transformer.php:51
msgid "Mobile:"
msgstr "Cellulare:"

#: inc/components/blocks/legacy/legacy-contact-info-transformer.php:43
msgid "Phone:"
msgstr "Telefono:"

#: inc/components/blocks/legacy/legacy-contact-info-transformer.php:35
msgid "Address:"
msgstr "Indirizzo:"

#: inc/components/blocks/blocks-fallback.php:37
#: inc/components/blocks/legacy/legacy-contact-info-transformer.php:29
msgid "Contact Info"
msgstr "Informazioni di contatto"

#: inc/components/blocks/legacy/legacy-about-me-transformer.php:17
msgid "About me"
msgstr "Chi sono"

#: inc/classes/screen-manager.php:262 inc/classes/screen-manager.php:273
msgid "Singulars"
msgstr "Singoli"

#: inc/classes/screen-manager.php:226 inc/classes/screen-manager.php:237
msgid "Archives"
msgstr "Archivi"

#: inc/classes/screen-manager.php:218
msgid "WooCommerce Categories"
msgstr "Categorie WooCommerce"

#: admin/dashboard/plugins/config.php:21
msgid "Greenshift helps you create complex pages and animations without code skills and directly inside core editor."
msgstr "Greenshift ti aiuta a creare pagine e animazioni complesse senza bisogno di competenze di programmazione e direttamente all'interno dell'editor di base."

#: inc/options/integrations/the-events-calendar/archive.php:22
msgid "Events Calendar Archive Structure"
msgstr "Struttura del calendario degli eventi archivio"

#: inc/options/general/custom-post-types.php:76
msgid "The Events Calendar"
msgstr "The Events Calendar"

#: inc/options/general/breadcrumbs.php:228
msgid "Archive Taxonomy Title"
msgstr "Titolo della tassonomia dell'archivio"

#: inc/options/general/breadcrumbs.php:218
msgid "Single Page/Post Taxonomy Title"
msgstr "Titolo della tassonomia dell'articolo/pagina"

#: inc/options/general/breadcrumbs.php:208
msgid "Single Page/Post Title"
msgstr "Titolo dell'articolo/pagina"

#: inc/options/single-elements/post-share-box.php:96
msgid "X (Twitter)"
msgstr "X (Twitter)"

#: inc/options/single-elements/author-box.php:106
msgid "Author Archive Link"
msgstr "Link all'archivio autore"

#: inc/panel-builder/header/search/options.php:610
msgid "Input Border Color"
msgstr "Colore bordo dell'input"

#. translators: %s is the product name
#: woocommerce/cart/cart.php:178 woocommerce/cart/cart.php:226
#: woocommerce/cart/mini-cart.php:53
msgid "Remove %s from cart"
msgstr "Rimuovi %s dal carrello"

#: admin/dashboard/plugins/config.php:27
msgid "Build stunning lightbox galleries, masonry grids, custom grids with no more than a few clicks right from the WordPress dashboard."
msgstr "Realizza splendide gallerie lightbox, griglie masonry e personalizzate con pochi clic direttamente dalla bacheca di WordPress."

#: inc/panel-builder/header/menu/options.php:145
msgid "Collapse menu items in to a dropdown if there is no enough room in the row. "
msgstr "Se non c'è abbastanza spazio nella riga, comprimi le voci del menu in un menu a discesa. "

#: inc/panel-builder/header/menu/options.php:141
msgid "Collapse Non Fitting Items"
msgstr "Comprimi gli elementi che non si adattano"

#. translators: 1: link to WP admin new post page open 2: link closing.
#: template-parts/content-none.php:16
msgid "Ready to publish your first post? %1$sGet started here%2$s."
msgstr "Tutto pronto per pubblicare il tuo primo articolo? %1$sInizia da qui%2$s."

#. translators: 1: span opening 2: Post title 3: span closing.
#: inc/components/single/content-helpers.php:284
msgid "Edit%1$s \"%2$s\"%3$s"
msgstr "Modifica%1$s \"%2$s\"%3$s"

#. translators: 1: span open 2: Name of current post. Only visible to screen
#. readers 3: span closing
#: inc/components/single/content-helpers.php:234 inc/template-tags.php:145
msgid "Continue reading%1$s \"%2$s\"%3$s"
msgstr "Continua a leggere%1$s \"%2$s\"%3$s"

#: inc/panel-builder/header/button/options.php:139
msgid "Set link to sponsored"
msgstr "Imposta il link come sponsorizzato"

#. translators: 1: span opening 2: span closing 3: the number of results
#: inc/components/hero/elements.php:322
msgid "%1$sSearch Results for%2$s %3$s"
msgstr "%1$sRisultati della ricerca per%2$s %3$s"

#: inc/options/general/typography.php:121
msgid "Pullquote"
msgstr "Citazione evidenziata"

#: inc/options/general/typography.php:114
msgid "Quote"
msgstr "Citazione"

#: inc/panel-builder/header/menu/options.php:727
msgid "Please note, this option will affect only submenus on 3rd level and below."
msgstr "Tieni presente che questa opzione ha effetto solo sui sottomenu di 3° livello e successivi."

#. translators: placeholder here means the actual URL.
#: inc/panel-builder/header/button/options.php:300
#: inc/panel-builder/header/logo/options.php:390
msgid "Add a custom %saria label%s attribute."
msgstr "Aggiungi un attributo personalizzato %setichetta aria%s."

#: inc/panel-builder/header/button/options.php:293
#: inc/panel-builder/header/logo/options.php:383
msgid "Custom Aria Label"
msgstr "Etichetta aria personalizzata"

#: inc/panel-builder/header/search/options.php:457
msgid "Live Results"
msgstr "Risultati in diretta"

#: searchform.php:269
msgid "Search for..."
msgstr "Cerca per..."

#: inc/options/general/comments-single.php:46
msgid "Outside"
msgstr "Esterno"

#: inc/options/general/comments-single.php:47
msgid "Inside"
msgstr "Interno"

#: inc/options/general/comments-single.php:39
msgid "Inputs Label Position"
msgstr "Posizione etichetta di input"

#: inc/options/general/back-to-top.php:31
#: inc/options/woocommerce/single-product-layers.php:55
#: inc/options/woocommerce/single-product-layers.php:100
msgid "Icon Source"
msgstr "Origine dell'icona"

#: inc/options/single-elements/author-box.php:298
msgid "Author Bio Font Color"
msgstr "Colore del font della biografia dell'autore"

#: inc/options/single-elements/author-box.php:292
msgid "Author Bio Font"
msgstr "Font della biografia dell'autore"

#: inc/options/single-elements/author-box.php:239
msgid "Author Name Font"
msgstr "Font del nome dell'autore"

#: inc/options/pages/page.php:158
#: inc/options/posts/custom-post-type-single.php:229
#: inc/options/posts/post.php:150
msgid "This single page is overrided by a custom template, to edit it please access %sthis page%s."
msgstr "Questa pagina singola è stata sovrascritta da un template personalizzato. Per modificarla, accedi a %squesta pagina%s."

#: inc/options/single-elements/post-nav.php:53
msgid "Navigate through posts that are from the same taxonomy."
msgstr "Naviga tra gli articoli che appartengono alla stessa tassonomia."

#: inc/options/single-elements/post-nav.php:34
msgid "Navigation Criteria"
msgstr "Criteri di navigazione"

#: inc/options/woocommerce/card-product-elements.php:527
#: inc/options/woocommerce/card-product-elements.php:796
#: inc/options/woocommerce/single-product-layers.php:207
msgid "Short Description"
msgstr "Descrizione breve"

#: inc/options/pages/search-page.php:111
#: inc/panel-builder/header/search/options.php:480
msgid "Live Results Product Price"
msgstr "Risultato live del prezzo del prodotto"

#: inc/options/integrations/tutorlms-single.php:141
msgid "Rating Font Color"
msgstr "Colore font delle valutazioni"

#: inc/options/integrations/tutorlms-single.php:129
msgid "Rating Font"
msgstr "Font valutazioni"

#: inc/options/integrations/tutorlms-single.php:97
msgid "Course Actions Font Color"
msgstr "Colore font azioni corso"

#: inc/options/integrations/tutorlms-single.php:84
msgid "Course Actions Font"
msgstr "Font azioni corso"

#: inc/options/integrations/tutorlms-single.php:52
msgid "Categories Font Color"
msgstr "Colore font categorie"

#: inc/options/integrations/tutorlms-single.php:39
msgid "Categories Font"
msgstr "Font categorie"

#: inc/options/general/performance.php:92
msgid "Shop Archive Featured Image"
msgstr "Immagine in evidenza dell'archivio del negozio"

#: inc/options/general/performance.php:85
msgid "Single Product Image"
msgstr "Immagine singolo prodotto"

#: inc/options/general/custom-post-types.php:67
#: inc/options/integrations/tutorlms-single.php:199
msgid "Course Single"
msgstr "Corso singolo"

#: inc/options/general/custom-post-types.php:61
#: inc/options/integrations/tutorlms-archive.php:16
msgid "Course Archive"
msgstr "Archivio corsi"

#: inc/options/general/comments-single.php:64
msgid "Above List"
msgstr "Elenco superiore"

#: inc/options/general/comments-single.php:63
msgid "Below List"
msgstr "Elenco inferiore"

#: inc/options/general/comments-single.php:56
msgid "Comment Form Position"
msgstr "Posizione modulo commenti"

#: inc/manager.php:243 inc/manager.php:249
msgid "You got %s result. Please press Tab to select it."
msgid_plural "You got %s results. Please press Tab to select one."
msgstr[0] "Hai ottenuto %s risultato. Premi la scheda per selezionarlo."
msgstr[1] "Hai ottenuto %s risultati. Premi la scheda per selezionarne uno."

#: inc/manager.php:240 inc/manager.php:242 searchform.php:347
msgid "No results"
msgstr "Nessun risultato"

#: inc/options/general/performance.php:76
msgid "Related Posts Featured Image"
msgstr "Immagine in evidenza per gli articoli correlati"

#: inc/options/general/performance.php:69
msgid "Archives Featured Image"
msgstr "Immagine in evidenza degli archivi"

#: inc/options/general/performance.php:62
msgid "Single Post/Page Featured Image"
msgstr "Immagine in evidenza articolo singolo/pagina"

#: inc/options/general/performance.php:55
msgid "Post/Page Title Featured Image"
msgstr "Immagine in evidenza del titolo dell'articolo/Pagina"

#: inc/options/general/performance.php:45
msgid "Enable lazy loading to improve performance."
msgstr "Abilita il Lazy Load (Caricamento lento) per migliorare le prestazioni."

#: inc/options/single-elements/structure-design.php:88
msgid "Content Area Border"
msgstr "Bordo dell'area contenuto"

#: inc/options/pages/author-page.php:102 inc/options/pages/search-page.php:147
#: inc/options/posts/blog.php:129 inc/options/posts/categories.php:95
msgid "This archive page is overrided by a custom template, to edit it please access %sthis page%s."
msgstr "Questa pagina di archivio è sovrascritta da un template personalizzato, per modificarla accedi a %squesta pagina%s."

#: inc/panel-builder/footer/copyright/options.php:14
#: inc/panel-builder/footer/copyright/view.php:28
msgid "Copyright &copy; {current_year} - WordPress Theme by {theme_author}"
msgstr "Copyright &copy; {current_year} - Tema WordPress sviluppato da {theme_author}"

#: inc/options/single-elements/post-nav.php:219
msgid "Thumbnail Border Radius"
msgstr "Raggio del bordo della miniatura"

#: inc/options/woocommerce/general/product-badges.php:25
msgid "Badge Shape"
msgstr "Forma del Badge"

#: inc/components/breadcrumbs.php:638
msgctxt "breadcrumb"
msgid "Home"
msgstr "Home"

#: inc/panel-builder/header/middle-row/options.php:109
msgid "Render Empty Row"
msgstr "Rendering della riga vuota"

#: inc/options/general/page-title.php:801
msgid "Image Parallax Effect"
msgstr "Effetto parallasse dell'immagine"

#: inc/options/woocommerce/general/messages.php:250
msgid "Error Messages"
msgstr "Messaggi di errore"

#: inc/options/woocommerce/general/messages.php:134
msgid "Success Messages"
msgstr "Messaggio di conferma"

#: inc/options/woocommerce/general/messages.php:20
msgid "Info Messages"
msgstr "Messaggi informativi"

#: inc/panel-builder/header/cart/view.php:212
msgid "Shopping cart"
msgstr "Carrello"

#: inc/panel-builder/header/offcanvas-logo/config.php:4
msgid "Off Canvas Logo"
msgstr "Logo off-canvas"

#: inc/panel-builder/header/button/options.php:289
#: inc/panel-builder/header/logo/options.php:379
msgid "Separate multiple classes with spaces."
msgstr "Separa le classi multiple con degli spazi."

#: inc/panel-builder/header/button/options.php:284
#: inc/panel-builder/header/logo/options.php:374
msgid "CSS Class"
msgstr "Classe CSS"

#: inc/options/general/comments-single.php:146
#: inc/options/single-elements/author-box.php:245
msgid "Author Name Font Color"
msgstr "Colore font del nome dell'autore"

#: inc/options/single-elements/author-box.php:55
msgid "Author Name Tag"
msgstr "Tag nome autore"

#: inc/css/fonts-manager.php:244 static/bundle/customizer-controls.js:13
#: static/bundle/options.js:13
#: static/js/options/options/typography/default-data.js:27
#: static/js/options/options/typography/helpers.js:127
msgid "System Default"
msgstr "Predefinito dal sistema"

#: inc/options/posts/woo-general.php:372
msgid "Single"
msgstr "Singolo"

#: inc/options/posts/woo-general.php:360
msgid "Show Stock Badge"
msgstr "Mostra la disponibilità di magazzino"

#: inc/components/woocommerce/common/stock-badge.php:31
#: inc/options/posts/woo-general.php:393
msgid "OUT OF STOCK"
msgstr "ESAURITO"

#: inc/components/woocommerce/common/sale-flash.php:8
msgid "SALE!"
msgstr "SCONTATO!"

#: inc/panel-builder/header/mobile-menu/options.php:306
msgid "Container Margin"
msgstr "Margini del contenitore"

#: inc/panel-builder/header/mobile-menu/options.php:245
msgid "Dropdown Font"
msgstr "Font del menu a discesa"

#: inc/panel-builder/header/mobile-menu/options.php:101
msgid "Items Vertical Spacing"
msgstr "Spaziatura verticale degli elementi"

#: inc/panel-builder/header/mobile-menu/options.php:76
msgid "Dropdown Toggle Shape"
msgstr "Forma del pulsante mostra/nascondi del menu a discesa"

#: inc/panel-builder/header/mobile-menu/options.php:48
msgid "Dropdown Toggle Icon"
msgstr "Forma dell'icona mostra/nascondi del menu a discesa"

#: inc/panel-builder/header/menu/options.php:706
msgid "Top Offset (Sticky State)"
msgstr "Offset in alto (Stato fisso)"

#: inc/panel-builder/header/menu/options.php:626
msgid "Only Arrow"
msgstr "Solo freccia"

#: inc/panel-builder/header/menu/options.php:625
msgid "Entire Item"
msgstr "Intero elemento"

#: inc/panel-builder/header/menu/options.php:617
msgid "Click Area"
msgstr "Fai clic su area"

#: inc/panel-builder/header/menu/options.php:608
msgid "Choose the interaction mode with the menu dropdown. "
msgstr "Scegli la modalità di interazione con il menu a discesa. "

#: inc/panel-builder/header/menu/options.php:606
msgid "Click"
msgstr "Clic"

#: inc/panel-builder/header/menu/options.php:598
msgid "Interaction Type"
msgstr "Tipo di interazione"

#: inc/panel-builder/header/menu/options.php:559
msgid "Items Border Radius"
msgstr "Raggio del bordo degli elementi"

#: inc/options/woocommerce/general/quantity-input.php:24
msgid "Custom Quantity Input"
msgstr "Inserimento quantità personalizzata"

#: inc/manager.php:260
msgid "Collapse dropdown menu"
msgstr "Chiudi il menu a discesa"

#: inc/components/menus.php:80 inc/components/menus.php:92
#: inc/components/menus.php:241 inc/manager.php:259
msgid "Expand dropdown menu"
msgstr "Apri il menu a discesa"

#: inc/components/builder/header-elements.php:138
msgid "Close drawer"
msgstr "Chiudi il carrello"

#: inc/panel-builder/header/button/options.php:277
#: inc/panel-builder/header/text/options.php:142
msgid "Logged Out"
msgstr "Disconnesso"

#: inc/panel-builder/header/button/options.php:276
#: inc/panel-builder/header/text/options.php:141
msgid "Logged In"
msgstr "Connesso"

#: inc/options/general/posts-listing.php:369
#: inc/options/single-elements/featured-image.php:56
#: inc/options/single-elements/related-posts.php:367
#: inc/options/woocommerce/card-product-elements.php:361
msgid "Video Thumbnail"
msgstr "Miniatura del video"

#: inc/panel-builder/header/cart/options.php:983
#: inc/panel-builder/header/offcanvas/options.php:280
#: inc/panel-builder/header/search/options.php:714
msgid "Close Button Type"
msgstr "Tipo di pulsante di chiusura"

#: inc/options/single-elements/post-nav.php:199
msgid "Thumbnail Overlay Color"
msgstr "Colore overlay della miniatura"

#: inc/options/woocommerce/single-product-gallery.php:244
msgid "Lightbox Button Background"
msgstr "Sfondo del pulsante Lightbox"

#: inc/options/woocommerce/single-product-gallery.php:213
msgid "Lightbox Button Icon Color"
msgstr "Colore dell'icona del pulsante Lightbox"

#: inc/options/general/back-to-top.php:287
msgid "Shape Border Radius"
msgstr "Raggio del bordo della forma"

#: inc/panel-builder/header/text/options.php:25
msgid "Allow the item container to expand and fill in all the available space."
msgstr "Consenti all'elemento contenitore di adattarsi allo spazio disponibile."

#: inc/panel-builder/header/text/options.php:24
msgid "Stretch Container"
msgstr "Contenitore stretto"

#: inc/options/general/buttons.php:130
#: inc/panel-builder/header/button/options.php:842
msgid "Padding"
msgstr "Spaziatura interna"

#: admin/dashboard/plugins/config.php:33
msgid "A better way to translate your WordPress site and go multilingual, directly from the front-end using a visual translation interface.s."
msgstr "Una migliore esperienza di traduzione per rendere qualsiasi sito WordPress multilingue, direttamente dal frontend attraverso un'interfaccia visiva di traduzione."

#: inc/panel-builder/header/search/options.php:581
msgid "Input Font Color"
msgstr "Colore font input"

#: inc/options/general/posts-listing.php:1394
msgid "Card Overlay Color"
msgstr "Colore overlay della scheda"

#: inc/options/general/posts-listing.php:652
msgid "Card Min Height"
msgstr "Altezza minima della scheda"

#: inc/panel-builder/header/button/options.php:157
#: inc/panel-builder/header/button/options.php:171
msgid "Popup Template"
msgstr "Template del popup"

#: inc/panel-builder/header/button/options.php:118
msgid "Link/URL"
msgstr "Link/URL"

#: inc/panel-builder/header/button/options.php:108
msgid "Open Popup"
msgstr "Apri popup"

#: inc/panel-builder/header/button/options.php:107
msgid "Open Link"
msgstr "Apri link"

#: inc/panel-builder/header/button/options.php:97
msgid "Click Behavior"
msgstr "Comportamento del clic"

#: inc/options/single-elements/related-posts.php:148
msgid "Random"
msgstr "Casuale"

#: inc/options/single-elements/related-posts.php:147
msgid "Most Commented"
msgstr "Più commentati"

#: inc/options/single-elements/related-posts.php:146
msgid "Recent"
msgstr "Recente"

#: inc/options/single-elements/related-posts.php:140
msgid "Sort by"
msgstr "Ordina per"

#: inc/panel-builder/header/mobile-menu/options.php:39
msgid "This option will collapse/expand the sub menu items on click/touch."
msgstr "Questa opzione consente di chiudere/aprire con un clic/tocco gli elementi del sottomenu."

#: inc/panel-builder/header/mobile-menu/options.php:35
msgid "Interactive Collapse"
msgstr "Chiusura interattiva"

#: inc/options/general/posts-listing.php:451
msgid "Full Post"
msgstr "Articolo completo"

#: inc/panel-builder/footer/widget-area-6/config.php:4
msgid "Widget Area 6"
msgstr "Area widget 6"

#: inc/panel-builder/footer/widget-area-5/config.php:4
msgid "Widget Area 5"
msgstr "Area widget 5"

#: inc/panel-builder/footer/widget-area-4/config.php:4
msgid "Widget Area 4"
msgstr "Area widget 4"

#: inc/panel-builder/footer/widget-area-3/config.php:4
msgid "Widget Area 3"
msgstr "Area widget 3"

#: inc/panel-builder/footer/widget-area-2/config.php:4
msgid "Widget Area 2"
msgstr "Area widget 2"

#: inc/panel-builder/footer/widget-area-1/config.php:4
msgid "Widget Area 1"
msgstr "Area widget 1"

#: inc/options/single-elements/post-nav.php:42
#: inc/options/single-elements/post-nav.php:52
#: inc/options/single-elements/post-tags.php:73
msgid "Taxonomy"
msgstr "Tassonomia"

#: inc/options/general/layout.php:72
msgid "This option applies only if the posts or pages are set to Narrow Width structure."
msgstr "Questo opzione si applica soltanto ad articoli e pagine con layout stretto."

#: inc/options/general/content-elements.php:31
msgid "Adjusts the spacing between the Gutenberg blocks."
msgstr "Adatta la spaziatura tra i blocchi Gutenberg."

#: inc/options/engagement/social-accounts.php:36
msgid "Easily link your social media accounts and display them throughout your website, with the various elements provided in the customizer."
msgstr "Collega facilmente i tuoi account social media e inseriscili in tutto il tuo sito web, grazie ai vari elementi forniti nel personalizzatore."

#: inc/options/engagement/general.php:20
msgid "Enable Schema.org markup features for your website. You can disable this option if you use a SEO plugin and let it do the work."
msgstr "Abilita le funzioni di markup Schema.org per il tuo sito web. Puoi disattivare questa opzione se utilizzi un plugin SEO che se ne occupa."

#: inc/panel-builder/header/button/options.php:133
msgid "Set link to nofollow"
msgstr "Imposta il link come nofollow"

#: inc/components/woocommerce/single/additional-actions-layer.php:59
msgid "Link"
msgstr "Link"

#: inc/panel-builder/footer/widget-area-1/options.php:102
msgid "Links Decoration"
msgstr "Decorazione dei link"

#: inc/options/single-elements/related-posts.php:202
msgid "Module Title Alignment"
msgstr "Allineamento del modulo titolo"

#: inc/options/general/page-title.php:619
#: inc/options/single-elements/author-box.php:177
#: inc/options/single-elements/post-share-box.php:288
#: inc/panel-builder/footer/socials/options.php:59
#: inc/panel-builder/header/socials/options.php:58
msgid "Set links to nofollow"
msgstr "Imposta i link come nofollow"

#: inc/options/single-elements/post-nav.php:146
msgid "Module Visibility"
msgstr "Visibilità del modulo"

#: inc/options/single-elements/post-nav.php:127
msgid "Title Visibility"
msgstr "Visibilità del titolo"

#: inc/options/woocommerce/single-product-gallery.php:54
msgid "Sticky Gallery"
msgstr "Galleria fissa"

#: inc/options/woocommerce/single-product-gallery.php:178
msgid "Prev/Next Background"
msgstr "Sfondo Prec/Succ"

#: inc/options/woocommerce/single-product-gallery.php:148
msgid "Prev/Next Arrow"
msgstr "Freccia Prec/Succ"

#: inc/options/woocommerce/single-product-elements.php:180
msgid "Price Font"
msgstr "Font del prezzo"

#: inc/options/woocommerce/card-product-elements.php:1249
msgid "Card Border Radius"
msgstr "Raggio del bordo della scheda"

#: inc/options/woocommerce/card-product-elements.php:1059
msgid "Add to Cart Button"
msgstr "Pulsante \"aggiungi al carrello\""

#: inc/options/general/sidebar.php:213
msgid "Last Widgets"
msgstr "Widget recenti"

#: inc/options/general/sidebar.php:200
msgid "Last X Widgets"
msgstr "Widget X recenti"

#: inc/options/general/sidebar.php:199
msgid "Entire Sidebar"
msgstr "Barra laterale completa"

#: inc/options/general/sidebar.php:192
msgid "Stick Behavior"
msgstr "Comportamento stick"

#: inc/options/general/sidebar.php:181
msgid "Sticky Top Offset"
msgstr "Offset in alto fisso"

#: inc/options/general/posts-listing.php:1640
#: inc/options/single-elements/related-posts.php:84
msgid "Number of posts"
msgstr "Numero di articoli"

#: inc/options/general/posts-listing.php:1613
#: inc/options/single-elements/related-posts.php:51
msgid "Columns & Posts"
msgstr "Colonne e articoli"

#: inc/options/general/performance.php:27
msgid "Enable this option if you want to remove WordPress emojis script in order to improve the performance."
msgstr "Abilita questa opzione per rimuovere lo script delle emoji in WordPress, per migliorare le prestazioni."

#: inc/options/general/performance.php:23
msgid "Disable Emojis Script"
msgstr "Disabilita lo script delle emoji"

#: inc/options/general/page-title.php:471
#: inc/panel-builder/header/text/options.php:37
msgid "Max Width"
msgstr "Larghezza massima"

#: inc/components/social-box.php:674 inc/components/social-box.php:1512
msgid "Facebook Messenger"
msgstr "Facebook Messenger"

#: admin/dashboard/plugins/config.php:15
msgid "JetEngine is a dynamic content plugin that lets you build a complex websites fast and cost-effectively."
msgstr "JetEngine è un plugin per contenuti dinamici che consente di realizzare siti web complessi in modo rapido ed economico."

#: inc/options/woocommerce/general/account-page.php:62
msgid "Navigation Quick Links"
msgstr "Link di navigazione rapida"

#: inc/init.php:488
msgid "Footer Widget Area "
msgstr "Area dei widget nel footer "

#: inc/panel-builder/header/cart/options.php:701
msgid "Display the quantity input field inside the off-canvas cart panel."
msgstr "Visualizza il campo di input della quantità all'interno del pannello del carrello off-canvas."

#: inc/components/woocommerce/common/account.php:98
msgid "Log out"
msgstr "Esci"

#: inc/options/woocommerce/single-product-gallery.php:114
msgid "Image size used for the main image on single product pages."
msgstr "Dimensione dell'immagine utilizzata per l'immagine principale nelle pagine dei singoli prodotti."

#: inc/options/woocommerce/general/checkout-page.php:184
msgid "I have read and agree to the website %s"
msgstr "Ho letto e accetto %s del sito web"

#: inc/options/woocommerce/general/checkout-page.php:181
msgid "Optionally add some text for the terms checkbox that customers must accept."
msgstr "Puoi aggiungere del testo personalizzato da far accettare ai clienti nel checkbox dei termini e condizioni."

#: inc/options/woocommerce/general/checkout-page.php:180
msgid "Terms and conditions"
msgstr "Termini e condizioni"

#: inc/options/woocommerce/general/checkout-page.php:167
msgid "Your personal data will be used to process your order, support your experience throughout this website, and for other purposes described in our [privacy_policy]."
msgstr "I tuoi dati personali saranno usati per processare il tuo ordine, fornirti assistenza all'interno di questo sito web e per altri scopi descritti nella nostra [privacy_policy]."

#: inc/options/woocommerce/general/checkout-page.php:165
msgid "Optionally add some text about your store privacy policy to show during checkout."
msgstr "Puoi aggiungere un testo facoltativo sul tuo negozio, relativo all'informativa sulla privacy policy da mostrare al momento del pagamento."

#: inc/options/woocommerce/general/checkout-page.php:164
msgid "Privacy policy"
msgstr "Privacy policy"

#: inc/options/woocommerce/general/checkout-page.php:152
msgid "Terms And Conditions Page"
msgstr "Pagina dei Termini e Condizioni"

#: inc/options/woocommerce/general/checkout-page.php:139
msgid "Privacy Policy Page"
msgstr "Pagina della privacy policy"

#: inc/options/woocommerce/general/checkout-page.php:121
msgid "Phone Field"
msgstr "Campo Telefono"

#: inc/options/woocommerce/general/checkout-page.php:103
msgid "Address Line 2 Field"
msgstr "Campo Indirizzo 2"

#: inc/options/woocommerce/general/checkout-page.php:94
#: inc/options/woocommerce/general/checkout-page.php:112
#: inc/options/woocommerce/general/checkout-page.php:130
msgid "Required"
msgstr "Necessario"

#: inc/options/woocommerce/general/checkout-page.php:93
#: inc/options/woocommerce/general/checkout-page.php:111
#: inc/options/woocommerce/general/checkout-page.php:129
msgid "Optional"
msgstr "Facoltativo"

#: inc/options/woocommerce/general/checkout-page.php:92
#: inc/options/woocommerce/general/checkout-page.php:110
#: inc/options/woocommerce/general/checkout-page.php:128
msgid "Hidden"
msgstr "Nascosto"

#: inc/options/woocommerce/general/checkout-page.php:85
msgid "Company Name Field"
msgstr "Campo nome azienda"

#: inc/options/woocommerce/general/checkout-page.php:74
msgid "Highlight Required Fields"
msgstr "Evidenzia campi obbligatori"

#: inc/options/woocommerce/general/checkout-page.php:58
msgid "Coupon Form"
msgstr "Modulo codice promozionale"

#: inc/options/woocommerce/general/account-page.php:159
msgid "Navigation Shadow"
msgstr "Ombreggiatura navigazione"

#: inc/options/woocommerce/general/account-page.php:139
msgid "Navigation Divider Color"
msgstr "Colore divisore navigazione"

#: inc/options/woocommerce/general/account-page.php:109
msgid "Navigation Background Color"
msgstr "Colore di sfondo della navigazione"

#: inc/options/woocommerce/general/account-page.php:79
msgid "Navigation Text Color"
msgstr "Colore del testo della navigazione"

#: inc/options/woocommerce/general/account-page.php:52
msgid "User Name"
msgstr "Nome utente"

#: inc/options/woocommerce/general/account-page.php:24
msgid "User Avatar"
msgstr "Avatar utente"

#: inc/options/woocommerce/general/account-page.php:13
msgid "Account Page"
msgstr "Pagina dell'account"

#: inc/options/woocommerce/general/messages.php:13
msgid "Messages"
msgstr "Messaggi"

#: inc/options/woocommerce/general/quantity-input.php:13
#: inc/panel-builder/header/cart/options.php:697
msgid "Quantity Input"
msgstr "Input quantità"

#: inc/options/woocommerce/general/checkout-page.php:30
msgid "No page set"
msgstr "Nessuna pagina impostata"

#: inc/options/customizer.php:33 static/bundle/customizer-controls.js:49
#: static/js/customizer/components/ProOverlay.js:20
#: static/js/customizer/components/ProOverlay.js:68
msgid "View Pro Features"
msgstr "Visualizza le funzionalità Premium"

#: inc/options/general/posts-listing.php:351
#: inc/options/single-elements/related-posts.php:349
#: inc/options/woocommerce/card-product-elements.php:344
msgid "Zoom Out"
msgstr "Diminuisci zoom"

#: inc/options/general/posts-listing.php:350
#: inc/options/single-elements/related-posts.php:348
#: inc/options/woocommerce/card-product-elements.php:343
msgid "Zoom In"
msgstr "Aumenta zoom"

#: inc/options/general/form-elements.php:259
msgid "Select Dropdown"
msgstr "Seleziona menu a discesa"

#: searchform.php:259
msgid "Search button"
msgstr "Pulsante di ricerca"

#: inc/options/general/page-title.php:613
#: inc/options/single-elements/author-box.php:160
#: inc/panel-builder/footer/socials/options.php:52
#: inc/panel-builder/header/socials/options.php:52
msgid "Open links in new tab"
msgstr "Apri i link in una nuova scheda"

#: inc/options/single-elements/post-share-box.php:140
msgid "Line"
msgstr "Linea"

#: inc/classes/colors.php:107 static/bundle/customizer-controls.js:13
#: static/bundle/options.js:13
#: static/js/options/options/color-palettes/EditColorName.js:12
#: static/js/options/options/color-palettes/PalettePreview.js:190
#: static/js/options/options/color-picker/picker-modal.js:79
#: static/js/options/options/color-picker/picker-modal.js:94
msgid "Color %s"
msgstr "Colore %s"

#: inc/panel-builder/header/search/options.php:670
msgid "Search Button Color"
msgstr "Colore del pulsante di ricerca"

#: inc/panel-builder/header/search/options.php:641
msgid "Search Icon Color"
msgstr "Colore dell'icona di ricerca"

#: inc/panel-builder/footer/menu/options.php:38
#: inc/panel-builder/footer/socials/options.php:138
msgid "Items Direction"
msgstr "Direzione elemento"

#: inc/panel-builder/footer/middle-row/options.php:685
msgid "Columns Divider"
msgstr "Divisore colonne"

#: inc/panel-builder/footer/middle-row/options.php:388
msgid "Widgets Spacing"
msgstr "Spaziatura dei widget"

#: inc/panel-builder/footer/middle-row/options.php:28
msgid "Columns"
msgstr "Colonne"

#. translators: placeholder here means the actual URL.
#: inc/panel-builder/footer/socials/options.php:37
#: inc/panel-builder/header/socials/options.php:37
msgid "Configure the social links in General ➝ %sSocial Network Accounts%s."
msgstr "Configura i link social in Generale → %sAccount Social Network%s."

#: inc/options/single-elements/related-posts.php:772
msgid "Posts Meta Font Color"
msgstr "Colore font dei meta degli articoli"

#: inc/options/single-elements/post-nav.php:76
#: inc/panel-builder/header/trigger/options.php:88
msgid "Container Spacing"
msgstr "Spaziatura del contenitore"

#: inc/options/woocommerce/single-product-tabs.php:302
msgid "Active Tab Colors"
msgstr "Colori scheda attiva"

#: inc/options/woocommerce/related-upsells.php:123
msgid "Upsell Products Visibility"
msgstr "Visibilità dei prodotti più venduti"

#: inc/options/woocommerce/related-upsells.php:101
msgid "Related Products Visibility"
msgstr "Visibilità dei prodotti correlati"

#: inc/components/woocommerce/common/rest-api.php:102
msgid "Product Price"
msgstr "Prezzo del prodotto"

#: inc/options/general/content-elements.php:13
msgid "Entry Content"
msgstr "Inserisci contenuto"

#: inc/options/general/general.php:89
msgid "Click this button if you want to reset all settings to their default values."
msgstr "Fai clic su questo pulsante per reimpostare tutte le impostazioni ai valori predefiniti."

#: inc/options/general/general.php:88 static/bundle/customizer-controls.js:13
#: static/bundle/options.js:13
#: static/js/options/options/ct-customizer-reset-options.js:18
msgid "Reset Options"
msgstr "Reimposta le opzioni"

#: inc/options/general/general.php:79
msgid "Manage Options"
msgstr "Gestisci le opzioni"

#: inc/options/general/meta.php:364
#: inc/options/woocommerce/card-product-elements.php:494
#: inc/panel-builder/header/trigger/options.php:69
msgid "Style"
msgstr "Stile"

#: inc/options/general/sidebar.php:252
msgid "Mobile Sidebar Position"
msgstr "Posizione della barra laterale sui dispositivi mobili"

#: inc/options/general/sidebar.php:235
msgid "Sidebar Visibility"
msgstr "Visibilità della barra laterale"

#. translators: %s entity of font color
#: inc/options/general/page-title.php:1074
msgid "%s Font Color"
msgstr "Colore del font %s"

#. translators: %s entity of font
#: inc/options/general/page-title.php:1062
msgid "%s Font"
msgstr "Font %s"

#: inc/options/general/page-title.php:1010
#: inc/options/general/posts-listing.php:1023
#: inc/options/single-elements/related-posts.php:842
msgid "Meta Button Background"
msgstr "Sfondo del pulsante dei meta"

#: inc/options/general/page-title.php:978
msgid "Meta Button Font"
msgstr "Font del pulsante dei meta"

#: inc/options/general/colors.php:613
msgid "All Headings (H1 - H6)"
msgstr "Intestazioni (H1 - H6)"

#: inc/options/general/colors.php:593
msgid "Borders"
msgstr "Bordi"

#: inc/options/general/colors.php:563
msgid "Text Selection"
msgstr "Seleziona testo"

#: inc/options/general/colors.php:533
msgid "Links"
msgstr "Link"

#: inc/options/general/colors.php:512
msgid "Base Text"
msgstr "Testo base"

#: inc/options/general/colors.php:18
msgid "Global Color Palette"
msgstr "Palette globale dei colori"

#: inc/options/meta/default.php:89 inc/options/meta/page.php:74
#: inc/options/meta/post.php:70
msgid "Content Area Style Source"
msgstr "Origine stile dell'area del contenuto"

#: inc/panel-builder/header/cart/options.php:198
msgid "Cart Total Font Color"
msgstr "Colore font del Totale carrello"

#: inc/panel-builder/header/cart/options.php:187
msgid "Cart Total Font"
msgstr "Font totale carrello"

#: inc/panel-builder/header/search/options.php:179
#: inc/panel-builder/header/socials/options.php:217
#: inc/panel-builder/header/trigger/options.php:193
msgid "Label Font Color"
msgstr "Colore font dell'etichetta"

#: inc/panel-builder/footer/socials/options.php:246
#: inc/panel-builder/header/search/options.php:168
#: inc/panel-builder/header/socials/options.php:206
#: inc/panel-builder/header/trigger/options.php:182
msgid "Label Font"
msgstr "Font etichetta"

#: inc/panel-builder/header/cart/options.php:132
#: inc/panel-builder/header/search/options.php:121
#: inc/panel-builder/header/trigger/options.php:135
msgid "Label Position"
msgstr "Posizione etichetta"

#: inc/options/general/breadcrumbs.php:339
msgid "Breadcrumbs Source"
msgstr "Origine dei breadcrumb"

#: inc/options/general/breadcrumbs.php:43
msgid "Breadcrumb NavXT"
msgstr "Breadcrumb NavXT"

#: inc/options/general/breadcrumbs.php:39
msgid "SeoPress"
msgstr "SeoPress"

#: inc/options/general/breadcrumbs.php:34
msgid "Yoast"
msgstr "Yoast"

#: inc/options/general/breadcrumbs.php:24
msgid "RankMath"
msgstr "RankMath"

#: inc/options/general/back-to-top.php:118
#: inc/panel-builder/header/cart/options.php:57
msgid "Type 6"
msgstr "Tipo 6"

#: inc/panel-builder/header/search/options.php:468
msgid "Live Results Images"
msgstr "Risultati delle Immagini in tempo reale"

#: inc/panel-builder/header/search/options.php:449
msgid "Placeholder Text"
msgstr "Segnaposto per il testo"

#: inc/options/pages/page.php:68
msgid "Page %s"
msgstr "Pagina %s"

#: inc/options/single-elements/post-tags.php:43
#: inc/options/single-elements/post-tags.php:50
msgid "%s %s"
msgstr "%s %s"

#: inc/options/woocommerce/single-product-gallery.php:38
msgid "Zoom Effect"
msgstr "Effetto zoom"

#: inc/options/woocommerce/single-product-gallery.php:27
msgid "Lightbox"
msgstr "Lightbox"

#: inc/panel-builder/footer/middle-row/options.php:374
msgid "Columns Spacing"
msgstr "Spaziatura delle colonne"

#: inc/options/woocommerce/single-product-gallery.php:5
msgid "Product Gallery"
msgstr "Galleria dei prodotti"

#: inc/options/meta/default.php:204
msgid "Disable %s %s"
msgstr "Disabilita %s %s"

#: inc/options/general/page-title.php:1284
#: inc/panel-builder/footer/options.php:110
#: inc/panel-builder/footer/options.php:127
msgid "Container Padding"
msgstr "Spaziatura interna del contenitore"

#: inc/options/woocommerce/single-product-tabs.php:274
msgid "Active Tab Border"
msgstr "Bordo scheda attiva"

#: inc/components/woocommerce/single/single-modifications.php:91
#: inc/options/woocommerce/single-product-tabs.php:5
msgid "Product Tabs"
msgstr "Schede prodotto"

#: inc/panel-builder/header/cart/options.php:849
msgid "Products Font Color"
msgstr "Colore del font dei prodotti"

#: inc/panel-builder/header/cart/options.php:828
#: inc/panel-builder/header/offcanvas/options.php:256
msgid "Panel Heading Font Color"
msgstr "Colore Font del pannello intestazione"

#: inc/panel-builder/header/cart/options.php:779
#: inc/panel-builder/header/cart/options.php:890
msgid "Subtotal Font Color"
msgstr "Colore Font del Subtotale"

#: inc/panel-builder/header/cart/options.php:948
#: inc/panel-builder/header/offcanvas/options.php:179
msgid "Panel Backdrop"
msgstr "Sfondo del Pannello"

#: inc/options/single-elements/post-share-box.php:136
msgid "Flipboard"
msgstr "Flipboard"

#: inc/options/woocommerce/single-product-elements.php:429
msgid "View Cart Button"
msgstr "Visualizza il pulsante del carrello"

#: inc/options/woocommerce/single-product-elements.php:356
msgid "Add To Cart Button"
msgstr "Pulsante Aggiungi al carrello"

#: inc/options/woocommerce/general/quantity-input.php:97
#: inc/options/woocommerce/single-product-elements.php:310
msgid "Quantity Arrows Color"
msgstr "Colore delle frecce quantità"

#: inc/options/woocommerce/general/quantity-input.php:66
msgid "Quantity Main Color"
msgstr "Colore principale quantità"

#: inc/options/general/posts-listing.php:1314
msgid "Card Divider"
msgstr "Separatore schede"

#: inc/panel-builder/header/cart/options.php:692
msgid "Product Page"
msgstr "Pagina prodotto"

#: inc/panel-builder/header/cart/options.php:691
msgid "Archive Page"
msgstr "Pagina Archivio"

#: inc/panel-builder/header/cart/options.php:685
msgid "Automatically open the cart drawer after a product is added to cart."
msgstr "Apri automaticamente il mini carrello laterale quando un prodotto viene aggiunto al carrello."

#: inc/panel-builder/header/cart/options.php:678
msgid "Open Cart Automatically On"
msgstr "Apri automaticamente il carrello"

#: inc/panel-builder/header/menu/options.php:86
msgid "Left to Right"
msgstr "Da sinistra a destra"

#: inc/panel-builder/header/menu/options.php:85
msgid "Center to Sides"
msgstr "Dal centro ai lati"

#: inc/panel-builder/header/menu/options.php:76
msgid "Indicator Effect"
msgstr "Effetto Indicatore"

#: inc/panel-builder/footer/copyright/options.php:67
#: inc/panel-builder/footer/menu/options.php:124
#: inc/panel-builder/footer/socials/options.php:204
#: inc/panel-builder/header/button/options.php:191
#: inc/panel-builder/header/button/options.php:246
#: inc/panel-builder/header/cart/options.php:1163
#: inc/panel-builder/header/logo/options.php:354
#: inc/panel-builder/header/mobile-menu/options.php:161
#: inc/panel-builder/header/search/options.php:883
#: inc/panel-builder/header/socials/options.php:167
#: inc/panel-builder/header/text/options.php:111
#: inc/panel-builder/header/text/options.php:442
#: inc/panel-builder/header/trigger/options.php:638
msgid "Element Visibility"
msgstr "Visibilità elemento"

#: inc/components/woocommerce/common/rest-api.php:123
msgid "Out of Stock"
msgstr "Esaurito"

#: inc/options/single-elements/author-box.php:88
msgid "Posts Count"
msgstr "Conteggio degli articoli"

#: inc/options/woocommerce/single-main.php:6
msgid "Product Title"
msgstr "Titolo del prodotto"

#: inc/options/woocommerce/general/product-badges.php:230
msgid "Out of Stock Badge"
msgstr "Badge Esaurito"

#: inc/options/general/pagination.php:92
msgid "Arrows Visibility"
msgstr "Visibilità Frecce"

#: inc/options/general/pagination.php:74
msgid "Numbers Visibility"
msgstr "Visibilità Numeri"

#: inc/options/general/page-title.php:657
msgid "Container Bottom Spacing"
msgstr "Spaziatura inferiore contenitore"

#: inc/options/integrations/tutorlms-archive.php:21
#: inc/options/integrations/tutorlms-single.php:204
msgid "Course Structure"
msgstr "Struttura Corso"

#: inc/options/general/custom-post-types.php:154
msgid "LearnDash"
msgstr "LearnDash"

#: inc/options/general/custom-post-types.php:56
msgid "Tutor LMS"
msgstr "Tutor LMS"

#: inc/helpers/cpt.php:11
msgid "Tag"
msgstr "Tag"

#: inc/helpers/cpt.php:10
msgid "Category"
msgstr "Categoria"

#: inc/components/woocommerce/single/additional-actions-layer.php:86
#: inc/panel-builder/footer/socials/options.php:185
#: inc/panel-builder/header/cart/options.php:102
#: inc/panel-builder/header/search/options.php:91
#: inc/panel-builder/header/socials/options.php:140
#: inc/panel-builder/header/trigger/options.php:106
msgid "Label Visibility"
msgstr "Visibilità Etichetta"

#: inc/integrations/beaver-themer.php:30
msgid "After Footer"
msgstr "Dopo il footer"

#: inc/integrations/beaver-themer.php:29
msgid "Before Footer"
msgstr "Prima del footer"

#: inc/integrations/beaver-themer.php:21
msgid "After Content"
msgstr "Dopo il contenuto"

#: inc/integrations/beaver-themer.php:20
msgid "Bottom Content"
msgstr "Contenuto di fondo"

#: inc/integrations/beaver-themer.php:19
msgid "Top Content"
msgstr "Contenuti in alto"

#: inc/integrations/beaver-themer.php:18
msgid "Before Content"
msgstr "Prima del contenuto"

#: inc/integrations/beaver-themer.php:16
msgid "Content"
msgstr "Contenuto"

#: inc/integrations/beaver-themer.php:11
msgid "After Header"
msgstr "Dopo l'header"

#: inc/integrations/beaver-themer.php:10
msgid "Before Header"
msgstr "Prima dell'header"

#: inc/options/woocommerce/single-product-elements.php:279
msgid "Quantity Color"
msgstr "Colore Quantità"

#: inc/options/woocommerce/single-product-layers.php:257
msgid "Button Width"
msgstr "Larghezza pulsante"

#: inc/options/woocommerce/card-product-elements.php:572
#: inc/options/woocommerce/single-product-layers.php:242
msgid "Add to Cart"
msgstr "Aggiungi al carrello"

#: inc/panel-builder/header/cart/options.php:964
#: inc/panel-builder/header/offcanvas/options.php:205
msgid "Panel Shadow"
msgstr "Ombra del pannello"

#: inc/panel-builder/header/cart/options.php:932
#: inc/panel-builder/header/offcanvas/options.php:159
msgid "Panel Background"
msgstr "Sfondo del pannello"

#: inc/panel-builder/header/cart/options.php:649
#: inc/panel-builder/header/offcanvas/options.php:39
msgid "Panel Width"
msgstr "Larghezza pannello"

#: inc/panel-builder/header/cart/options.php:618
msgid "Dropdown"
msgstr "Menu a discesa"

#: inc/panel-builder/header/cart/options.php:607
msgid "Cart Drawer Type"
msgstr "Tipo di cassetto del carrello"

#: inc/panel-builder/header/cart/options.php:589
msgid "Cart Drawer"
msgstr "Cassetto del carrello"

#: inc/components/social-box.php:505 inc/components/social-box.php:1472
msgid "Apple Podcasts"
msgstr "Apple Podcasts"

#: inc/options/general/back-to-top.php:169
msgid "Side Offset"
msgstr "Offset laterale"

#: inc/helpers.php:207 inc/helpers.php:223
msgid "Full Size"
msgstr "Dimensione originale"

#: inc/panel-builder/header/logo/options.php:108
msgid "Sticky State Shrink"
msgstr "Riduci stato \"in evidenza\""

#: inc/options/general/general.php:35
msgid "Website Frame"
msgstr "Cornice Sito Web"

#: inc/components/social-box.php:909 inc/components/social-box.php:1568
msgid "Phone"
msgstr "Telefono"

#. translators: placeholder here means the actual URL.
#: inc/panel-builder/header/menu/options.php:19
msgid "Manage your menu items in the %sMenus screen%s."
msgstr "Gestisci gli elementi del tuo menu dalla %sSchermata del menu%s."

#: inc/options/meta/page.php:192
msgid "Disable Page %s"
msgstr "Disabilita la pagina %s"

#: inc/options/general/typography.php:158
msgid "The font used if the chosen font isn't available."
msgstr "Il font di riserva da usare se il font scelto non è disponibile."

#: inc/options/general/typography.php:157
msgid "Fallback Font Family"
msgstr "Famiglia Font di Riserva"

#: inc/options/single-elements/featured-image.php:116
#: inc/options/single-elements/post-nav.php:108
msgid "Image Visibility"
msgstr "Visibilità dell'immagine"

#: inc/panel-builder/header/menu/options.php:1066
msgid "Dropdown Border Radius"
msgstr "Raggio bordo menu a discesa"

#: inc/panel-builder/header/menu/options.php:1048
msgid "Dropdown Shadow"
msgstr "Ombreggiatura del menu a discesa"

#: inc/panel-builder/header/menu/options.php:907
#: inc/panel-builder/header/menu/options.php:937
#: inc/panel-builder/header/menu/options.php:970
#: inc/panel-builder/header/menu/options.php:1000
msgid "Items Background Color"
msgstr "Colore di sfondo degli elementi"

#: inc/panel-builder/header/menu/options.php:645
msgid "Boxed Color"
msgstr "Colore boxed"

#: inc/panel-builder/header/menu/options.php:644
msgid "Solid Color"
msgstr "Tinta unita"

#: inc/panel-builder/header/menu/options.php:634
msgid "Items Hover Effect"
msgstr "Effetto degli elementi al passaggio del mouse"

#: inc/options/single-elements/structure-design.php:104
msgid "Content Area Border Radius"
msgstr "Raggio del bordo dell'area del contenuto"

#: inc/options/single-elements/structure-design.php:119
msgid "Content Area Padding"
msgstr "Spaziatura interna area contenuto"

#: inc/options/general/comments-single.php:30
msgid "Website Input Field"
msgstr "Campo di input del sito web"

#: inc/panel-builder/footer/middle-row/options.php:659
#: inc/panel-builder/header/middle-row/options.php:481
msgid "Bottom Border Width"
msgstr "Larghezza del bordo inferiore"

#: inc/panel-builder/footer/middle-row/options.php:618
#: inc/panel-builder/header/middle-row/options.php:374
msgid "Top Border Width"
msgstr "Larghezza del bordo superiore"

#: inc/panel-builder/header/middle-row/options.php:88
msgid "Row Max Height"
msgstr "Altezza massima riga"

#: inc/panel-builder/header/middle-row/options.php:69
msgid "Sticky State Row Shrink"
msgstr "Restringi la riga con lo stato fisso"

#: inc/panel-builder/header/middle-row/options.php:52
msgid "Row Min Height"
msgstr "Altezza minima riga"

#: inc/panel-builder/footer/middle-row/options.php:436
#: inc/panel-builder/footer/middle-row/options.php:627
#: inc/panel-builder/footer/middle-row/options.php:668
#: inc/panel-builder/footer/options.php:14
#: inc/panel-builder/header/middle-row/options.php:43
#: inc/panel-builder/header/middle-row/options.php:383
#: inc/panel-builder/header/middle-row/options.php:490
msgid "Full Width"
msgstr "Larghezza piena"

#: inc/panel-builder/header/middle-row/config.php:4
#: static/bundle/customizer-controls.js:1 static/bundle/options.js:13
#: static/js/customizer/panels-builder/placements/PlacementsBuilder/Row.js:18
#: static/js/customizer/sync/builder.js:126
msgid "Main Row"
msgstr "Riga principale"

#: inc/panel-builder/header/logo/options.php:271
msgid "Logo Image Position"
msgstr "Posizione dell'immagine del logo"

#: inc/panel-builder/header/logo/options.php:169
msgid "Site Title Visibility"
msgstr "Visibilità del titolo del sito"

#: inc/panel-builder/header/logo/options.php:88
#: inc/panel-builder/header/logo/options.php:129
#: inc/panel-builder/header/offcanvas-logo/options.php:43
msgid "Logo Height"
msgstr "Altezza del logo"

#: inc/panel-builder/header/logo/options.php:61
msgid "Sticky State Logo"
msgstr "Logo di stato fisso"

#: inc/panel-builder/header/logo/options.php:28
msgid "Transparent State Logo"
msgstr "Logo di stato trasparente"

#: inc/panel-builder/header/button/options.php:345
#: inc/panel-builder/header/button/options.php:545
#: inc/panel-builder/header/button/options.php:731
#: inc/panel-builder/header/cart/options.php:217
#: inc/panel-builder/header/cart/options.php:343
#: inc/panel-builder/header/cart/options.php:468
#: inc/panel-builder/header/logo/options.php:443
#: inc/panel-builder/header/logo/options.php:589
#: inc/panel-builder/header/menu/options.php:194
#: inc/panel-builder/header/menu/options.php:425
#: inc/panel-builder/header/menu/options.php:774
#: inc/panel-builder/header/menu/options.php:927
#: inc/panel-builder/header/middle-row/options.php:172
#: inc/panel-builder/header/middle-row/options.php:240
#: inc/panel-builder/header/middle-row/options.php:307
#: inc/panel-builder/header/middle-row/options.php:414
#: inc/panel-builder/header/middle-row/options.php:520
#: inc/panel-builder/header/mobile-menu/options.php:366
#: inc/panel-builder/header/search/options.php:198
#: inc/panel-builder/header/search/options.php:323
#: inc/panel-builder/header/socials/options.php:240
#: inc/panel-builder/header/socials/options.php:366
#: inc/panel-builder/header/socials/options.php:510
#: inc/panel-builder/header/text/options.php:185
#: inc/panel-builder/header/text/options.php:338
#: inc/panel-builder/header/trigger/options.php:212
#: inc/panel-builder/header/trigger/options.php:338
#: inc/panel-builder/header/trigger/options.php:471
msgid "Sticky State"
msgstr "Stato fisso"

#: inc/panel-builder/header/button/options.php:336
#: inc/panel-builder/header/button/options.php:536
#: inc/panel-builder/header/button/options.php:722
#: inc/panel-builder/header/cart/options.php:208
#: inc/panel-builder/header/cart/options.php:334
#: inc/panel-builder/header/cart/options.php:458
#: inc/panel-builder/header/logo/options.php:434
#: inc/panel-builder/header/logo/options.php:580
#: inc/panel-builder/header/menu/options.php:185
#: inc/panel-builder/header/menu/options.php:415
#: inc/panel-builder/header/menu/options.php:765
#: inc/panel-builder/header/menu/options.php:918
#: inc/panel-builder/header/middle-row/options.php:164
#: inc/panel-builder/header/middle-row/options.php:232
#: inc/panel-builder/header/middle-row/options.php:299
#: inc/panel-builder/header/middle-row/options.php:406
#: inc/panel-builder/header/middle-row/options.php:512
#: inc/panel-builder/header/mobile-menu/options.php:357
#: inc/panel-builder/header/search/options.php:189
#: inc/panel-builder/header/search/options.php:315
#: inc/panel-builder/header/socials/options.php:230
#: inc/panel-builder/header/socials/options.php:356
#: inc/panel-builder/header/socials/options.php:499
#: inc/panel-builder/header/text/options.php:176
#: inc/panel-builder/header/text/options.php:329
#: inc/panel-builder/header/trigger/options.php:203
#: inc/panel-builder/header/trigger/options.php:329
#: inc/panel-builder/header/trigger/options.php:461
msgid "Transparent State"
msgstr "Stato trasparente"

#: inc/panel-builder/header/button/options.php:331
#: inc/panel-builder/header/button/options.php:531
#: inc/panel-builder/header/button/options.php:717
#: inc/panel-builder/header/cart/options.php:203
#: inc/panel-builder/header/cart/options.php:329
#: inc/panel-builder/header/cart/options.php:450
#: inc/panel-builder/header/logo/options.php:429
#: inc/panel-builder/header/logo/options.php:575
#: inc/panel-builder/header/menu/options.php:180
#: inc/panel-builder/header/menu/options.php:409
#: inc/panel-builder/header/menu/options.php:760
#: inc/panel-builder/header/menu/options.php:913
#: inc/panel-builder/header/middle-row/options.php:159
#: inc/panel-builder/header/middle-row/options.php:227
#: inc/panel-builder/header/middle-row/options.php:294
#: inc/panel-builder/header/middle-row/options.php:401
#: inc/panel-builder/header/middle-row/options.php:507
#: inc/panel-builder/header/mobile-menu/options.php:352
#: inc/panel-builder/header/search/options.php:184
#: inc/panel-builder/header/search/options.php:310
#: inc/panel-builder/header/socials/options.php:222
#: inc/panel-builder/header/socials/options.php:350
#: inc/panel-builder/header/socials/options.php:490
#: inc/panel-builder/header/text/options.php:171
#: inc/panel-builder/header/text/options.php:324
#: inc/panel-builder/header/trigger/options.php:198
#: inc/panel-builder/header/trigger/options.php:324
#: inc/panel-builder/header/trigger/options.php:455
msgid "Default State"
msgstr "Stato predefinito"

#: inc/options/general/page-title.php:802
msgid "Choose for which devices you want to enable the parallax effect."
msgstr "Scegli per quali dispositivi vuoi abilitare l'effetto parallasse."

#: inc/options/general/page-title.php:749
msgid "Featured"
msgstr "In evidenza"

#: inc/options/meta/blog.php:12 inc/options/meta/default.php:250
#: inc/options/meta/page.php:208 inc/options/meta/post.php:230
msgid "Disable Footer"
msgstr "Disabilita il footer"

#: inc/options/meta/blog.php:6 inc/options/meta/default.php:244
#: inc/options/meta/page.php:202 inc/options/meta/post.php:224
msgid "Disable Header"
msgstr "Disabilita l'header"

#: inc/classes/db-versioning/v2-0-31.php:26
#: inc/classes/db-versioning/v2-0-31.php:103 inc/components/social-box.php:85
#: inc/options/single-elements/post-share-box.php:234
msgid "Share your love"
msgstr "Condividi il tuo amore"

#: inc/options/woocommerce/general/product-badges.php:84
msgid "Sale Badge Value"
msgstr "Valore del Badge Offerta"

#: inc/options/customizer.php:169 inc/options/woocommerce/archive-main.php:6
msgid "Product Archives"
msgstr "Archivio prodotti"

#: inc/options/woocommerce/card-product-elements.php:342
msgid "Swap Images"
msgstr "Scambia immagini"

#: inc/options/woocommerce/general/store-notice.php:83
msgid "Notice Background Color"
msgstr "Colore di sfondo delle notifiche"

#: inc/options/woocommerce/general/store-notice.php:61
msgid "Notice Font Color"
msgstr "Colore del font delle notifiche"

#: inc/options/woocommerce/general/store-notice.php:41
msgid "Notice Position"
msgstr "Posizione notifica"

#: admin/dashboard/plugins/config.php:45
msgid "The most advanced frontend drag & drop page builder. Create high-end, pixel perfect websites at record speeds. Any theme, any page, any design."
msgstr "Il più avanzato builder drag & drop di pagine per frontend. Realizza siti web di alta qualità, perfetti al pixel e in tempi da record. Qualsiasi tema, pagina e design."

#: admin/dashboard/plugins/config.php:57
msgid "Drag & Drop online form builder that helps you create beautiful contact forms with just a few clicks."
msgstr "Il builder dei moduli online drag & drop ti aiuta a realizzare splendidi moduli di contatto con pochi clic."

#: admin/dashboard/plugins/config.php:51
msgid "Capture, organize and engage web visitors with free forms, live chat, CRM (contact management), email marketing, and analytics."
msgstr "Cattura, organizza e coinvolgi i visitatori del sito con moduli gratuiti, chat in tempo reale, CRM (gestione dei contatti), email marketing, e statistiche."

#: admin/dashboard/plugins/config.php:8
msgid "A Library of Page Builder Gutenberg Blocks which will reimagine the way you use the WordPress Block Editor."
msgstr "Una Libreria di Blocchi per il Page Builder Gutenberg che reinventerà il modo in cui utilizzi l'Editor a Blocchi di WordPress."

#: inc/components/blocks/blocks-fallback.php:41
#: inc/panel-builder/footer/socials/config.php:4
#: inc/panel-builder/header/socials/config.php:4
msgid "Socials"
msgstr "Social"

#: inc/options/posts/custom-post-type-single.php:204
msgid "%s Elements"
msgstr "%s Elementi"

#. translators: placeholder here means the actual URL.
#: inc/options/general/posts-listing.php:1697 inc/options/meta/default.php:161
#: inc/options/meta/page.php:146 inc/options/meta/post.php:145
#: inc/options/single-elements/structure.php:118
#: inc/options/woocommerce/archive-main.php:154
msgid "You can customize the global spacing value in General ➝ Layout ➝ %sContent Area Spacing%s."
msgstr "Puoi personalizzare i valori della spaziatura globale in Generale → Layout → %sSpaziatura dell'area del contenuto%s."

#: inc/options/general/posts-listing.php:1690 inc/options/meta/default.php:154
#: inc/options/meta/page.php:139 inc/options/meta/post.php:138
#: inc/options/single-elements/structure.php:111
#: inc/options/woocommerce/archive-main.php:147
msgid "Only Bottom"
msgstr "Solo in basso"

#: inc/options/general/posts-listing.php:1687 inc/options/meta/default.php:151
#: inc/options/meta/page.php:136 inc/options/meta/post.php:135
#: inc/options/single-elements/structure.php:108
#: inc/options/woocommerce/archive-main.php:144
msgid "Only Top"
msgstr "Solo in alto"

#: inc/options/general/posts-listing.php:1684 inc/options/meta/default.php:148
#: inc/options/meta/page.php:133 inc/options/meta/post.php:132
#: inc/options/single-elements/structure.php:105
#: inc/options/woocommerce/archive-main.php:141
msgid "Top & Bottom"
msgstr "In alto e in basso"

#: inc/options/meta/default.php:137 inc/options/meta/page.php:122
#: inc/options/meta/post.php:121
msgid "You can customize the spacing value in general settings panel."
msgstr "Puoi personalizzare il valore di spaziatura nel pannello delle impostazioni generali."

#: inc/options/general/posts-listing.php:1673 inc/options/meta/default.php:119
#: inc/options/meta/page.php:104 inc/options/meta/post.php:104
#: inc/options/single-elements/structure.php:94
#: inc/options/woocommerce/archive-main.php:130
msgid "Content Area Vertical Spacing"
msgstr "Spaziatura verticale dell'area contenuto"

#: inc/options/general/breadcrumbs.php:161
msgid "Home Page Text"
msgstr "Testo della home page"

#: inc/options/general/back-to-top.php:55
#: inc/options/general/breadcrumbs.php:116
#: inc/options/general/breadcrumbs.php:148
#: inc/options/general/breadcrumbs.php:180
#: inc/options/woocommerce/single-product-layers.php:72
#: inc/options/woocommerce/single-product-layers.php:117
msgid "Icon"
msgstr "Icona"

#: inc/options/general/breadcrumbs.php:141
msgid "Home Item"
msgstr "Elemento della Home"

#: inc/options/general/page-title.php:742
msgid "Container Background Image"
msgstr "Immagine di sfondo del contenitore"

#: inc/options/general/meta.php:349
#: inc/options/woocommerce/card-product-elements.php:476
#: inc/options/woocommerce/card-product-elements.php:882
msgid "Taxonomies"
msgstr "Tassonomie"

#: woocommerce/cart/cart.php:252
msgid "Update cart"
msgstr "Aggiorna carrello"

#: woocommerce/cart/cart.php:247
msgid "Apply coupon"
msgstr "Applica codice promozionale"

#: woocommerce/cart/cart.php:247
msgid "Coupon code"
msgstr "Codice promozionale"

#: woocommerce/cart/cart.php:247
msgid "Coupon:"
msgstr "Codice promozionale:"

#: woocommerce/cart/cart.php:109
msgid "Available on backorder"
msgstr "Disponibile in prevendita"

#: woocommerce/cart/cart.php:216
msgid "Remove product"
msgstr "Rimuovi prodotto"

#: inc/options/woocommerce/card-product-elements.php:425
#: inc/options/woocommerce/card-product-elements.php:838
#: inc/options/woocommerce/single-product-layers.php:184
msgid "Price"
msgstr "Prezzo"

#: woocommerce/cart/cart.php:40 woocommerce/cart/cart.php:89
msgid "Product"
msgstr "Prodotto"

#: inc/options/single-elements/featured-image.php:83
#: inc/options/woocommerce/card-product-elements.php:536
msgid "Full"
msgstr "Pieno"

#: inc/helpers.php:205
msgid "Medium Large"
msgstr "Medio Grande"

#: inc/options/single-elements/post-share-box.php:453
#: inc/options/single-elements/post-tags.php:176
#: inc/options/single-elements/related-posts.php:629
msgid "Module Title Font Color"
msgstr "Colore del carattere del titolo del modulo"

#: inc/options/single-elements/related-posts.php:535
msgid "Location"
msgstr "Posizione"

#: inc/options/single-elements/post-share-box.php:245
#: inc/options/single-elements/post-tags.php:99
#: inc/options/single-elements/related-posts.php:173
#: inc/options/woocommerce/related-upsells.php:76
msgid "Module Title Tag"
msgstr "Tag del titolo del modulo"

#: inc/options/single-elements/post-share-box.php:232
#: inc/options/single-elements/post-tags.php:87
#: inc/options/single-elements/related-posts.php:165
msgid "Module Title"
msgstr "Titolo del modulo"

#. translators: %s: Author's display name.
#: inc/components/post-meta.php:72
msgid "Posts by %s"
msgstr "Articoli scritti da %s"

#: inc/options/integrations/the-events-calendar/archive.php:14
#: inc/options/integrations/the-events-calendar/single.php:15
#: inc/options/integrations/tutorlms-archive.php:12
#: inc/options/integrations/tutorlms-single.php:195
#: inc/options/meta/default.php:13 inc/options/meta/default.php:26
#: inc/options/posts/custom-post-type-archive.php:8
#: inc/options/posts/custom-post-type-single.php:21
msgid "%s Title"
msgstr "%s Titolo"

#: inc/options/woocommerce/archive-main.php:83
#: inc/options/woocommerce/related-upsells.php:33
msgid "Columns & Rows"
msgstr "Colonne e righe"

#: inc/options/woocommerce/archive-main.php:45
msgid "Shop Settings"
msgstr "Impostazioni del negozio"

#: inc/options/general/custom-post-types.php:124
#: inc/options/posts/custom-post-type-single.php:26
msgid "Single %s"
msgstr "Singolo %s"

#: inc/options/general/custom-post-types.php:38
#: inc/options/general/custom-post-types.php:44
msgid "BuddyPress"
msgstr "BuddyPress"

#: inc/options/general/custom-post-types.php:15
#: inc/options/general/custom-post-types.php:21
msgid "bbPress"
msgstr "bbPress"

#: inc/options/general/meta.php:453 static/bundle/customizer-controls.js:13
#: static/bundle/options.js:13 static/js/options/options/ct-border.js:47
msgid "none"
msgstr "nessuno"

#: inc/options/general/meta.php:440
msgid "Icons"
msgstr "Icone"

#: inc/options/general/meta.php:439
msgid "Labels"
msgstr "Etichette"

#: inc/options/general/meta.php:433
msgid "Items Style"
msgstr "Stile delle voci"

#: inc/components/post-meta.php:207 inc/components/post-meta.php:799
#: inc/options/general/meta.php:388
#: static/js/customizer/sync/helpers/entry-meta.js:107
msgid "In"
msgstr "In"

#: inc/options/general/meta.php:205
msgid "Author Avatar"
msgstr "Avatar dell'autore"

#: inc/options/general/meta.php:181
msgid "Date format %sinstructions%s."
msgstr "Formato data %sistruzioni%s."

#: inc/options/general/colors.php:23
msgid "Learn more about palettes and colors %shere%s."
msgstr "Ulteriori informazioni su palette e colori %squi%s."

#: inc/components/breadcrumbs.php:303
msgid "No title"
msgstr "Nessun titolo"

#: inc/components/breadcrumbs.php:85
msgid "Searching for:"
msgstr "Ricerca per:"

#: inc/components/breadcrumbs.php:77
msgid "404 Not found"
msgstr "404 Pagina non trovata"

#: inc/classes/screen-manager.php:214 inc/components/breadcrumbs.php:416
#: inc/options/general/posts-listing.php:14 inc/options/posts/blog.php:20
msgid "Blog"
msgstr "Blog"

#. translators: placeholder here means the actual URL.
#: inc/options/general/posts-listing.php:1730
#: inc/options/single-elements/structure-design.php:36
#: inc/options/woocommerce/archive-main.php:186
msgid "Please note, by default this option is inherited from Colors ➝ %sSite Background%s."
msgstr "Nota che per impostazione predefinita questa opzione è ereditata da Colori ➝ %sSfondo del sito%s."

#: inc/options/general/breadcrumbs.php:287
#: inc/options/general/page-title.php:1187
#: inc/options/woocommerce/single-product-elements.php:229
msgid "Breadcrumbs Font Color"
msgstr "Colore del font dei breadcrumb"

#: inc/options/general/breadcrumbs.php:275
#: inc/options/general/page-title.php:1179
#: inc/options/woocommerce/single-product-elements.php:221
msgid "Breadcrumbs Font"
msgstr "Font dei breadcrumb"

#: inc/options/general/page-title.php:591
msgid "Social Channels"
msgstr "Canali social"

#: inc/options/general/page-title.php:528
msgid "Author Meta"
msgstr "Meta autore"

#: inc/options/general/page-title.php:329
msgid "Name & Avatar"
msgstr "Nome e avatar"

#: inc/options/general/page-title.php:291
#: inc/options/general/page-title.php:426
#: inc/options/general/page-title.php:491
#: inc/options/general/page-title.php:575
#: inc/options/general/page-title.php:598
msgid "Top Spacing"
msgstr "Spaziatura superiore"

#: inc/options/general/page-title.php:114
msgid "Subtitle"
msgstr "Sottotitolo"

#: inc/options/general/page-title.php:102
msgid "Bio"
msgstr "Biografia"

#: inc/options/general/page-title.php:788
#: inc/options/general/posts-listing.php:327
#: inc/options/single-elements/featured-image.php:36
#: inc/options/single-elements/post-nav.php:89
#: inc/options/single-elements/related-posts.php:325
#: inc/options/woocommerce/card-product-elements.php:314
#: inc/options/woocommerce/general/cart-page.php:40
#: inc/options/woocommerce/single-product-gallery.php:113
#: inc/options/woocommerce/single-product-gallery.php:339
#: inc/panel-builder/header/cart/options.php:716
msgid "Image Size"
msgstr "Dimensione dell'immagine"

#: inc/options/woocommerce/card-product-elements.php:215
#: inc/options/woocommerce/card-product-elements.php:322
msgid "Image height will be automatically calculated based on the image ratio."
msgstr "L'altezza dell'immagine verrà calcolata automaticamente in base alle sue proporzioni."

#: inc/helpers.php:203
msgid "Thumbnail"
msgstr "Miniatura"

#: inc/components/woocommerce/common/account.php:98
msgid "Account"
msgstr "Account"

#: inc/panel-builder/header/search/options.php:426
msgid "Icon Margin"
msgstr "Margine dell'icona"

#: inc/components/woocommerce/boot.php:97
msgid "WooCommerce Sidebar"
msgstr "Barra laterale di WooCommerce"

#: inc/panel-builder/header/trigger/options.php:482
#: inc/panel-builder/header/trigger/options.php:522
#: inc/panel-builder/header/trigger/options.php:560
msgid "Trigger Border Color"
msgstr "Colore del bordo del trigger"

#: inc/panel-builder/header/button/options.php:46
#: inc/panel-builder/header/button/options.php:66
#: inc/panel-builder/header/cart/options.php:148
#: inc/panel-builder/header/search/options.php:135
#: inc/panel-builder/header/trigger/options.php:149
msgid "Label Text"
msgstr "Testo dell'etichetta"

#: inc/options/woocommerce/related-upsells.php:5
msgid "Related & Upsells"
msgstr "Correlati e Più venduti"

#: inc/options/woocommerce/general/star-rating.php:42
msgid "Inactive"
msgstr "Inattivo"

#: inc/options/woocommerce/card-product-elements.php:1137
msgid "Button Text Color"
msgstr "Colore del testo del pulsante"

#: inc/options/general/posts-listing.php:1621
#: inc/options/single-elements/related-posts.php:70
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/ct-woocommerce-columns-and-rows.js:51
msgid "Number of columns"
msgstr "Numero di colonne"

#: inc/options/single-elements/related-posts.php:505
msgid "Separate or unify the related posts module from or with the entry content area."
msgstr "Separa o unisci il modulo degli articoli correlati da o con l'area del contenuto della voce."

#: inc/options/general/content-elements.php:28
msgid "Spacious"
msgstr "Spazioso"

#: inc/options/general/content-elements.php:27
msgid "Comfortable"
msgstr "Comodo"

#: inc/options/general/content-elements.php:26
msgid "Compact"
msgstr "Compatto"

#: inc/options/general/content-elements.php:19
msgid "Content Spacing"
msgstr "Spaziatura del contenuto"

#: inc/options/general/comments-single.php:85
#: inc/options/single-elements/related-posts.php:508
msgid "Contained"
msgstr "Contenuto"

#: inc/options/general/comments-single.php:84
#: inc/options/single-elements/related-posts.php:507
msgid "Separated"
msgstr "Diviso"

#: inc/options/general/comments-single.php:82
msgid "Separate or unify the comments module from or with the entry content area."
msgstr "Separa o unisci il modulo dei commenti da o con l'area del contenuto della voce."

#: inc/options/general/comments-single.php:77
#: inc/options/single-elements/related-posts.php:500
#: inc/options/woocommerce/single-product-tabs.php:85
msgid "Module Placement"
msgstr "Posizionamento del modulo"

#: inc/options/general/breadcrumbs.php:320
#: inc/options/general/page-title.php:1220 inc/options/general/sidebar.php:372
#: inc/options/single-elements/author-box.php:332
#: inc/options/woocommerce/single-product-elements.php:262
#: inc/panel-builder/footer/copyright/options.php:137
#: inc/panel-builder/footer/middle-row/options.php:571
#: inc/panel-builder/footer/widget-area-1/options.php:94
#: inc/panel-builder/header/cart/options.php:771
#: inc/panel-builder/header/cart/options.php:882
#: inc/panel-builder/header/text/options.php:229
#: inc/panel-builder/header/text/options.php:269
#: inc/panel-builder/header/text/options.php:308
msgid "Link Hover"
msgstr "Link Hover"

#: inc/options/general/breadcrumbs.php:314
#: inc/options/general/page-title.php:1214 inc/options/general/sidebar.php:367
#: inc/options/single-elements/author-box.php:326
#: inc/options/woocommerce/single-product-elements.php:256
#: inc/panel-builder/footer/copyright/options.php:131
#: inc/panel-builder/footer/middle-row/options.php:565
#: inc/panel-builder/footer/widget-area-1/options.php:89
#: inc/panel-builder/header/cart/options.php:765
#: inc/panel-builder/header/cart/options.php:877
#: inc/panel-builder/header/text/options.php:223
#: inc/panel-builder/header/text/options.php:264
#: inc/panel-builder/header/text/options.php:303
msgid "Link Initial"
msgstr "Link iniziale"

#: inc/components/social-box.php:661 inc/components/social-box.php:1508
msgid "Facebook Group"
msgstr "Gruppo Facebook"

#: inc/options/general/page-title.php:543
msgid "Joined Date"
msgstr "Data di iscrizione"

#: inc/init.php:470
msgid "Main Sidebar"
msgstr "Barra laterale principale"

#: inc/components/single/comments.php:281
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/customizer/sync/builder.js:111
#: static/js/options/options/ct-image-uploader.js:385
msgid "Edit"
msgstr "Modifica"

#: inc/options/general/page-title.php:1428
msgid "Options will appear here only if you will set Custom in Page Title option."
msgstr "Le opzioni vengono visualizzate qui solo quando selezioni personalizzato nelle opzioni del titolo della pagina."

#: inc/options/general/page-title.php:1368
msgid "By default these options are inherited from Customizer options."
msgstr "Per impostazione predefinita, queste opzioni vengono ereditate dalle opzioni del personalizzatore."

#: inc/options/general/comments-single.php:126
#: inc/options/single-elements/related-posts.php:585
msgid "Container Max Width"
msgstr "Larghezza massima del contenitore"

#: inc/options/general/comments-single.php:110
#: inc/options/single-elements/related-posts.php:568
msgid "Normal"
msgstr "Normale"

#: inc/options/general/comments-single.php:109
#: inc/options/general/page-title.php:733
#: inc/options/single-elements/related-posts.php:569
msgid "Narrow"
msgstr "Stretto"

#: inc/options/general/comments-single.php:103
#: inc/options/single-elements/related-posts.php:561
#: inc/panel-builder/footer/options.php:6
#: inc/panel-builder/header/middle-row/options.php:32
msgid "Container Structure"
msgstr "Struttura del contenitore"

#: inc/panel-builder/footer/options.php:38
msgid "Enable reveal effect on"
msgstr "Abilita l'effetto rivela su"

#: inc/panel-builder/footer/socials/options.php:121
#: inc/panel-builder/header/socials/options.php:120
msgid "Shape Fill Type"
msgstr "Tipo di riempimento della forma"

#: inc/panel-builder/footer/socials/options.php:102
#: inc/panel-builder/header/socials/options.php:101
msgid "Icons Shape Type"
msgstr "Tipo di forma delle icone"

#: inc/options/general/page-title.php:694
#: inc/options/general/posts-listing.php:791
#: inc/panel-builder/footer/copyright/options.php:47
#: inc/panel-builder/footer/menu/options.php:105
#: inc/panel-builder/footer/middle-row/options.php:411
#: inc/panel-builder/footer/socials/options.php:169
#: inc/panel-builder/footer/widget-area-1/options.php:39
#: inc/panel-builder/header/button/options.php:230
#: inc/panel-builder/header/logo/options.php:338
#: inc/panel-builder/header/offcanvas/options.php:63
#: inc/panel-builder/header/text/options.php:95
msgid "Vertical Alignment"
msgstr "Allineamento verticale"

#: inc/options/general/page-title.php:638
#: inc/options/general/page-title.php:678
#: inc/options/general/posts-listing.php:738
#: inc/options/woocommerce/single-product-tabs.php:60
#: inc/panel-builder/footer/copyright/options.php:31
#: inc/panel-builder/footer/menu/options.php:84
#: inc/panel-builder/footer/socials/options.php:152
#: inc/panel-builder/footer/widget-area-1/options.php:23
#: inc/panel-builder/header/button/options.php:213
#: inc/panel-builder/header/logo/options.php:322
#: inc/panel-builder/header/mobile-menu/options.php:143
#: inc/panel-builder/header/offcanvas/options.php:79
#: inc/panel-builder/header/text/options.php:78
msgid "Horizontal Alignment"
msgstr "Allineamento orizzontale"

#: inc/integrations/elementor.php:134
msgid "Full Width Section"
msgstr "Sezione a larghezza piena"

#: inc/options/general/sidebar.php:118
msgid "Widget Title Tag"
msgstr "Tag del titolo del widget"

#: inc/options/meta/default.php:238 inc/options/meta/post.php:218
msgid "Disable Related Posts"
msgstr "Disabilita gli articoli correlati"

#: inc/options/meta/default.php:227 inc/options/meta/post.php:200
msgid "Disable Posts Navigation"
msgstr "Disabilita la navigazione degli articoli"

#: inc/options/meta/default.php:221 inc/options/meta/post.php:194
msgid "Disable Author Box"
msgstr "Disabilita il box dell'Autore"

#: inc/options/meta/default.php:215 inc/options/meta/page.php:183
#: inc/options/meta/post.php:188
msgid "Disable Share Box"
msgstr "Disabilita il box della condivisione"

#: inc/options/meta/post.php:182
msgid "Disable Post Tags"
msgstr "Disabilita i tag dell'articolo"

#: inc/options/meta/default.php:195 inc/options/meta/page.php:177
#: inc/options/meta/post.php:176
msgid "Disable Featured Image"
msgstr "Disabilita immagine in evidenza"

#: inc/options/general/sidebar.php:339
#: inc/panel-builder/footer/middle-row/options.php:537
msgid "Widgets Font Color"
msgstr "Colore del font dei widget"

#: inc/options/general/sidebar.php:330
#: inc/panel-builder/footer/middle-row/options.php:528
msgid "Widgets Font"
msgstr "Font dei widget"

#: inc/options/general/page-title.php:390
msgid "Author avatar"
msgstr "Avatar dell'autore"

#: inc/options/general/page-title.php:225
#: static/bundle/customizer-controls.js:1
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/customizer/panels-builder/columns/AvailableItems.js:74
#: static/js/customizer/panels-builder/placements/AvailableItems.js:78
msgid "Elements"
msgstr "Elementi"

#: inc/panel-builder/footer/copyright/options.php:16
msgid "You can insert some arbitrary HTML code tags: {current_year}, {site_title} and {theme_author}"
msgstr "Puoi inserire alcuni tag di codice HTML arbitrari: {current_year}, {site_title} e {theme_author}"

#: inc/panel-builder/footer/copyright/options.php:10
msgid "Copyright Text"
msgstr "Testo del copyright"

#: inc/panel-builder/footer/copyright/config.php:3
msgid "Copyright"
msgstr "Copyright"

#: inc/panel-builder/footer/options.php:101
msgid "Please note, you can also change the background color for each row individually."
msgstr "Nota, puoi anche cambiare il colore di sfondo per ogni riga individualmente."

#: inc/panel-builder/footer/options.php:39
msgid "Enables a nice reveal effect as you scroll down."
msgstr "Abilita un piacevole effetto visivo mentre scorri verso il basso."

#: inc/options/general/sidebar.php:283
#: inc/panel-builder/footer/middle-row/options.php:480
msgid "Widgets Title Font Color"
msgstr "Colore del font del titolo dei widget"

#: inc/options/general/sidebar.php:275
#: inc/panel-builder/footer/middle-row/options.php:472
msgid "Widgets Title Font"
msgstr "Font del titolo dei widget"

#: inc/panel-builder/footer/middle-row/options.php:639
msgid "Row Bottom Divider"
msgstr "Divisore inferiore della riga"

#: inc/panel-builder/footer/middle-row/options.php:598
msgid "Row Top Divider"
msgstr "Divisore superiore della riga"

#: inc/panel-builder/footer/middle-row/options.php:585
msgid "Row Background"
msgstr "Sfondo della riga"

#: inc/panel-builder/footer/middle-row/options.php:441
#: inc/panel-builder/header/middle-row/options.php:126
msgid "Row Visibility"
msgstr "Visibilità della riga"

#: inc/panel-builder/footer/middle-row/options.php:399
msgid "Row Vertical Spacing"
msgstr "Spaziatura verticale della riga"

#: inc/panel-builder/footer/middle-row/options.php:92
#: inc/panel-builder/footer/middle-row/options.php:104
#: inc/panel-builder/footer/middle-row/options.php:155
#: inc/panel-builder/footer/middle-row/options.php:167
#: inc/panel-builder/footer/middle-row/options.php:218
#: inc/panel-builder/footer/middle-row/options.php:230
#: inc/panel-builder/footer/middle-row/options.php:281
#: inc/panel-builder/footer/middle-row/options.php:293
#: inc/panel-builder/footer/middle-row/options.php:344
#: inc/panel-builder/footer/middle-row/options.php:356
msgid "Two Columns"
msgstr "Due colonne"

#: inc/panel-builder/footer/middle-row/options.php:87
#: inc/panel-builder/footer/middle-row/options.php:99
#: inc/panel-builder/footer/middle-row/options.php:150
#: inc/panel-builder/footer/middle-row/options.php:162
#: inc/panel-builder/footer/middle-row/options.php:213
#: inc/panel-builder/footer/middle-row/options.php:225
#: inc/panel-builder/footer/middle-row/options.php:276
#: inc/panel-builder/footer/middle-row/options.php:288
#: inc/panel-builder/footer/middle-row/options.php:339
#: inc/panel-builder/footer/middle-row/options.php:351
msgid "Stacked"
msgstr "Impilato"

#: inc/panel-builder/footer/middle-row/options.php:51
#: inc/panel-builder/footer/middle-row/options.php:118
#: inc/panel-builder/footer/middle-row/options.php:181
#: inc/panel-builder/footer/middle-row/options.php:244
#: inc/panel-builder/footer/middle-row/options.php:307
msgid "Columns Layout"
msgstr "Layout delle colonne"

#: inc/options/general/general.php:53
msgid "Frame Color"
msgstr "Colore della cornice"

#: inc/options/general/general.php:43
msgid "Frame Size"
msgstr "Dimensione del frame"

#: inc/options/general/layout.php:84
msgid "This option will apply only to those elements that have a wide alignment option."
msgstr "Questa opzione si applicherà solo a quegli elementi che hanno una opzione di allineamento larga."

#: inc/options/woocommerce/card-product-elements.php:107
#: inc/options/woocommerce/card-product-elements.php:118
#: inc/options/woocommerce/card-product-elements.php:154
#: inc/options/woocommerce/card-product-elements.php:165
#: inc/panel-builder/header/menu/options.php:961
#: inc/panel-builder/header/menu/options.php:992
#: inc/panel-builder/header/menu/options.php:1022
msgid "Hover/Active"
msgstr "Hover/Attivo"

#: inc/options/general/buttons.php:13 inc/options/general/typography.php:104
msgid "Buttons"
msgstr "Pulsanti"

#: inc/components/single/single-helpers.php:410
#: inc/components/single/single-helpers.php:546
#: inc/options/general/page-title.php:544
msgid "Articles"
msgstr "Articoli"

#: inc/components/single/single-helpers.php:406
msgid "Joined"
msgstr "Iscritto"

#: inc/init.php:407
msgid "Header Menu 1"
msgstr "Menu dell'header 1"

#: inc/options/general/posts-listing.php:1286
msgid "Card Bottom Divider"
msgstr "Divisore inferiore della scheda"

#: inc/options/customizer.php:91
msgid "Performance"
msgstr "Prestazioni"

#. translators: placeholder here means the actual URL.
#: inc/panel-builder/footer/menu/options.php:19
#: inc/panel-builder/header/mobile-menu/options.php:15
msgid "Manage your menus in the %sMenus screen%s."
msgstr "Gestisci i tuoi menu nella %sschermata dei menu%s."

#: inc/components/media/simple.php:48
msgid "Default image"
msgstr "Immagine predefinita"

#: inc/components/gallery.php:257 inc/components/gallery.php:269
msgid "Slide %s"
msgstr "Diapositiva %s"

#: inc/components/pagination.php:69
msgid "No more products to load"
msgstr "Non ci sono altri prodotti da caricare"

#: inc/panel-builder/header/search/options.php:503
msgid "Search Through Criteria"
msgstr "Cerca tra i criteri"

#: inc/options/woocommerce/general/messages.php:103
#: inc/options/woocommerce/general/messages.php:219
#: inc/options/woocommerce/general/messages.php:331
#: inc/options/woocommerce/single-product-elements.php:391
#: inc/options/woocommerce/single-product-elements.php:464
msgid "Button Background Color"
msgstr "Colore sfondo del pulsante"

#: inc/options/general/back-to-top.php:113
#: inc/options/general/content-elements.php:51
#: inc/panel-builder/header/cart/options.php:52
msgid "Type 5"
msgstr "Tipo 5"

#: inc/options/general/content-elements.php:39
msgid "Links Type"
msgstr "Tipo di link"

#: inc/options/general/posts-listing.php:1248
msgid "Featured Image Radius"
msgstr "Raggio dell'immagine in evidenza"

#: inc/options/single-elements/related-posts.php:882
#: inc/options/woocommerce/card-product-elements.php:1245
#: inc/options/woocommerce/general/checkout-page.php:264
#: inc/panel-builder/header/cart/options.php:915
msgid "Image Border Radius"
msgstr "Raggio del bordo dell'immagine"

#: inc/options/woocommerce/single-product-gallery.php:317
msgid "Position"
msgstr "Posizione"

#: inc/options/woocommerce/general/store-notice.php:31
msgid "This is a demo store for testing purposes &mdash; no orders shall be fulfilled."
msgstr "Questo è un negozio demo a scopo di test &mdash; nessun ordine deve essere evaso."

#: inc/options/woocommerce/general/store-notice.php:13
msgid "Store Notice"
msgstr "Avviso del negozio"

#: inc/options/general/form-elements.php:50
#: inc/options/woocommerce/card-product-elements.php:261
msgid "Height"
msgstr "Altezza"

#: inc/options/woocommerce/card-product-elements.php:235
#: inc/options/woocommerce/card-product-elements.php:248
#: inc/panel-builder/header/menu/options.php:678
msgid "Width"
msgstr "Larghezza"

#: inc/options/posts/categories.php:72
msgid "Categories Elements"
msgstr "Elementi delle categorie"

#: inc/options/pages/search-page.php:96 inc/options/posts/blog.php:99
#: inc/options/posts/custom-post-type-archive.php:96
#: inc/options/woocommerce/archive-main.php:228
#: inc/options/woocommerce/single-main.php:100
msgid "Functionality Options"
msgstr "Opzioni funzionalità"

#: inc/options/woocommerce/single-product-elements.php:9
msgid "Product Elements"
msgstr "Elementi del prodotto"

#: inc/options/single-elements/related-posts.php:693
msgid "Posts Title Font Color"
msgstr "Colore del font del titolo degli articoli"

#: inc/options/general/posts-listing.php:408
msgid "Boundless Image"
msgstr "Immagine senza bordi"

#: inc/options/single-elements/structure-design.php:69
msgid "Content Area Shadow"
msgstr "Ombra dell'area del contenuto"

#: inc/options/general/pagination.php:178
msgid "Accent"
msgstr "In risalto"

#: inc/options/general/pagination.php:171
msgid "Text Active"
msgstr "Testo attivo"

#: inc/options/general/pagination.php:165 inc/options/general/sidebar.php:361
#: inc/panel-builder/footer/middle-row/options.php:559
#: inc/panel-builder/header/cart/options.php:760
#: inc/panel-builder/header/cart/options.php:841
#: inc/panel-builder/header/cart/options.php:871
#: inc/panel-builder/header/cart/options.php:904
#: inc/panel-builder/header/offcanvas/options.php:270
#: inc/panel-builder/header/text/options.php:217
#: inc/panel-builder/header/text/options.php:259
#: inc/panel-builder/header/text/options.php:298
msgid "Text Initial"
msgstr "Testo iniziale"

#: inc/options/woocommerce/general/messages.php:24
#: inc/options/woocommerce/general/messages.php:138
#: inc/options/woocommerce/general/messages.php:254
msgid "Text Color"
msgstr "Colore del testo"

#: inc/options/general/page-title.php:1072
msgid "Excerpt Font Color"
msgstr "Colore del font del riassunto"

#: inc/options/general/page-title.php:1060
#: inc/options/general/posts-listing.php:907
msgid "Excerpt Font"
msgstr "Font del riassunto"

#: inc/options/general/page-title.php:943
#: inc/options/general/posts-listing.php:956
msgid "Meta Font Color"
msgstr "Colore del font dei metadati"

#: inc/panel-builder/header/menu/options.php:720
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/box-shadow/options.js:52
msgid "Horizontal Offset"
msgstr "Offset orizzontale"

#: inc/options/general/buttons.php:110 inc/options/general/sidebar.php:414
#: inc/options/single-elements/author-box.php:490
#: inc/options/single-elements/post-share-box.php:545
#: inc/options/woocommerce/single-product-tabs.php:324
#: inc/panel-builder/header/cart/options.php:993
#: inc/panel-builder/header/mobile-menu/options.php:85
#: inc/panel-builder/header/offcanvas/options.php:290
#: inc/panel-builder/header/search/options.php:724
msgid "Border"
msgstr "Bordo"

#: inc/panel-builder/header/menu/options.php:688
msgid "Top Offset"
msgstr "Offset superiore"

#: inc/options/general/posts-listing.php:1445
#: inc/options/woocommerce/card-product-elements.php:1206
msgid "Card Shadow"
msgstr "Ombreggiatura scheda"

#: inc/options/general/back-to-top.php:158
msgid "Bottom Offset"
msgstr "Offset inferiore"

#. translators: % refers to the number of reviews, when more than 1
#: inc/components/post-meta.php:511
msgid "% Reviews"
msgstr "% recensioni"

#. translators: text for one review
#: inc/components/post-meta.php:509
msgid "1 Review"
msgstr "1 recensione"

#. translators: text for one review
#: inc/components/post-meta.php:503
msgid "1 Comment"
msgstr "1 commento"

#: inc/manager.php:238
msgid "More"
msgstr "Di più"

#: inc/options/general/back-to-top.php:302 inc/options/general/sidebar.php:461
#: inc/options/single-elements/author-box.php:471
#: inc/panel-builder/footer/options.php:63
#: inc/panel-builder/header/middle-row/options.php:502
#: inc/panel-builder/header/middle-row/options.php:529
#: inc/panel-builder/header/middle-row/options.php:547
#: inc/panel-builder/header/middle-row/options.php:565
msgid "Shadow"
msgstr "Ombra"

#: inc/components/single/comments.php:289
msgid "Reply"
msgstr "Rispondi"

#: inc/init.php:409 inc/panel-builder/header/mobile-menu/config.php:4
msgid "Mobile Menu"
msgstr "Menu mobile"

#: inc/panel-builder/header/cart/config.php:4
msgid "Cart"
msgstr "Carrello"

#: inc/panel-builder/header/cart/options.php:94
msgid "Icon Badge"
msgstr "Badge dell'icona"

#: inc/options/woocommerce/general/checkout-page.php:46
msgid "Checkout Page"
msgstr "Pagina di pagamento"

#: inc/options/woocommerce/general/cart-page.php:13
msgid "Cart Page"
msgstr "Pagina del carrello"

#: inc/panel-builder/header/text/config.php:4
msgid "HTML"
msgstr "HTML"

#: inc/panel-builder/header/text/options.php:13
msgid "You can add here some arbitrary HTML code."
msgstr "Puoi aggiungere qui il codice HTML arbitrario."

#: inc/panel-builder/footer/socials/options.php:339
#: inc/panel-builder/header/socials/options.php:481
#: inc/panel-builder/header/socials/options.php:527
#: inc/panel-builder/header/socials/options.php:566
#: inc/panel-builder/header/socials/options.php:605
msgid "Icons Border Color"
msgstr "Colore bordo delle icone"

#: inc/options/general/page-title.php:1138
#: inc/options/single-elements/author-box.php:378
#: inc/panel-builder/footer/socials/options.php:335
#: inc/panel-builder/header/socials/options.php:477
#: inc/panel-builder/header/socials/options.php:523
#: inc/panel-builder/header/socials/options.php:562
#: inc/panel-builder/header/socials/options.php:601
msgid "Icons Background Color"
msgstr "Colore di sfondo delle icone"

#: inc/panel-builder/footer/socials/options.php:128
#: inc/panel-builder/header/socials/options.php:128
#: inc/panel-builder/header/trigger/options.php:78
msgid "Solid"
msgstr "Solido"

#: inc/panel-builder/footer/socials/options.php:110
#: inc/panel-builder/header/socials/options.php:109
msgid "Rounded"
msgstr "Arrotondato"

#: inc/options/single-elements/post-share-box.php:202
#: inc/panel-builder/footer/socials/options.php:97
#: inc/panel-builder/header/socials/options.php:96
msgid "Official"
msgstr "Ufficiale"

#: inc/options/general/back-to-top.php:38
#: inc/options/general/breadcrumbs.php:62 inc/options/general/meta.php:164
#: inc/options/general/page-title.php:751
#: inc/options/general/page-title.php:1356
#: inc/options/general/page-title.php:1397 inc/options/meta/default.php:95
#: inc/options/meta/default.php:126 inc/options/meta/page.php:80
#: inc/options/meta/page.php:111 inc/options/meta/post.php:76
#: inc/options/meta/post.php:110
#: inc/options/single-elements/post-share-box.php:201
#: inc/options/woocommerce/general/product-badges.php:91
#: inc/options/woocommerce/single-product-layers.php:41
#: inc/options/woocommerce/single-product-layers.php:62
#: inc/options/woocommerce/single-product-layers.php:107
#: inc/options/woocommerce/single-product-layers.php:373
#: inc/panel-builder/footer/socials/options.php:96
#: inc/panel-builder/header/socials/options.php:95
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/bundle/options.js:49 static/js/backend/woo-variation.js:47
#: static/js/options/options/color-palettes/ColorPalettesModal.js:41
#: static/js/options/options/ct-ratio.js:108
#: static/js/options/options/ct-ratio.js:263
#: static/js/options/options/ct-slider.js:112
#: static/js/options/options/ct-slider.js:150
#: static/js/options/options/ct-spacing.js:150
#: static/js/options/options/ct-spacing.js:209
#: static/js/options/options/ct-typography.js:151
msgid "Custom"
msgstr "Personalizzato"

#: inc/options/single-elements/post-share-box.php:324
#: inc/panel-builder/footer/socials/options.php:78
#: inc/panel-builder/header/socials/options.php:77
msgid "Icons Spacing"
msgstr "Spaziatura delle icone"

#: inc/panel-builder/footer/socials/options.php:68
#: inc/panel-builder/header/socials/options.php:67
msgid "Icons Size"
msgstr "Dimensioni delle icone"

#: inc/panel-builder/header/button/options.php:266
#: inc/panel-builder/header/text/options.php:131
msgid "User Visibility"
msgstr "Visibilità dell'utente"

#: inc/panel-builder/header/search/options.php:699
msgid "Modal Background"
msgstr "Sfondo modale"

#: inc/options/pages/search-page.php:100
msgid "Live results"
msgstr "Risultati in diretta"

#: inc/options/general/back-to-top.php:147
#: inc/options/general/breadcrumbs.php:127
#: inc/options/general/breadcrumbs.php:195
#: inc/options/single-elements/post-share-box.php:297
#: inc/options/woocommerce/single-product-layers.php:354
#: inc/panel-builder/header/cart/options.php:65
#: inc/panel-builder/header/cart/options.php:1110
#: inc/panel-builder/header/offcanvas/options.php:297
#: inc/panel-builder/header/search/options.php:62
#: inc/panel-builder/header/search/options.php:731
#: inc/panel-builder/header/trigger/options.php:38
msgid "Icon Size"
msgstr "Dimensione icona"

#: inc/panel-builder/header/trigger/config.php:4
msgid "Trigger"
msgstr "Trigger"

#: inc/components/woocommerce/single/additional-actions-layer.php:60
#: inc/options/general/meta.php:373
#: inc/options/woocommerce/card-product-elements.php:502
#: inc/panel-builder/header/button/config.php:4 static/bundle/options.js:49
#: static/js/backend/woo-attributes/AfterHeading.js:15
msgid "Button"
msgstr "Pulsante"

#: inc/options/general/buttons.php:34 inc/options/general/posts-listing.php:341
#: inc/options/single-elements/related-posts.php:339
#: inc/options/woocommerce/card-product-elements.php:333
msgid "Hover Effect"
msgstr "Effetto hover"

#: inc/panel-builder/header/button/options.php:29
msgid "Size"
msgstr "Dimensione"

#: inc/panel-builder/header/button/options.php:22
msgid "Ghost"
msgstr "Fantasma"

#: inc/panel-builder/header/cart/options.php:673
#: inc/panel-builder/header/offcanvas/options.php:34
msgid "Right Side"
msgstr "Lato destro"

#: inc/panel-builder/header/cart/options.php:672
#: inc/panel-builder/header/offcanvas/options.php:33
msgid "Left Side"
msgstr "Lato sinistro"

#: inc/panel-builder/header/cart/options.php:664
#: inc/panel-builder/header/offcanvas/options.php:27
msgid "Reveal From"
msgstr "Rivela da"

#: inc/panel-builder/header/offcanvas/options.php:17
msgid "Side Panel"
msgstr "Pannello laterale"

#: inc/panel-builder/header/offcanvas/options.php:10
msgid "Reveal as"
msgstr "Rivela come"

#: inc/options/general/buttons.php:24
msgid "Min Height"
msgstr "Altezza minima"

#: inc/panel-builder/header/menu-secondary/config.php:4
msgid "Menu 2"
msgstr "Menu 2"

#: inc/panel-builder/header/menu/config.php:4
msgid "Menu 1"
msgstr "Menu 1"

#: inc/panel-builder/footer/copyright/options.php:145
#: inc/panel-builder/footer/menu/options.php:202
#: inc/panel-builder/footer/socials/options.php:376
#: inc/panel-builder/footer/widget-area-1/options.php:116
#: inc/panel-builder/header/button/options.php:851
#: inc/panel-builder/header/cart/options.php:573
#: inc/panel-builder/header/logo/options.php:673
#: inc/panel-builder/header/menu/options.php:571
#: inc/panel-builder/header/mobile-menu/options.php:497
#: inc/panel-builder/header/offcanvas-logo/options.php:76
#: inc/panel-builder/header/socials/options.php:642
#: inc/panel-builder/header/text/options.php:419
#: inc/panel-builder/header/trigger/options.php:621
msgid "Margin"
msgstr "Margine"

#: inc/panel-builder/footer/menu/options.php:71
#: inc/panel-builder/header/menu/options.php:136
#: inc/panel-builder/header/mobile-menu/options.php:137
msgid "Enabling this option will make the menu to stretch and fit the width of its parent column. "
msgstr "Abilitando questa opzione, il menu si allungherà e adatterà alla larghezza della colonna principale. "

#: inc/panel-builder/footer/menu/options.php:67
#: inc/panel-builder/header/menu/options.php:132
#: inc/panel-builder/header/mobile-menu/options.php:133
msgid "Stretch Menu"
msgstr "Menu esteso"

#: inc/panel-builder/header/menu/options.php:119
msgid "Items Height"
msgstr "Altezza degli elementi"

#: inc/panel-builder/footer/menu/options.php:15
#: inc/panel-builder/header/menu/options.php:15
#: inc/panel-builder/header/mobile-menu/options.php:11
msgid "Select menu..."
msgstr "Seleziona menu..."

#: inc/panel-builder/footer/menu/options.php:9
#: inc/panel-builder/header/menu/options.php:9
#: inc/panel-builder/header/mobile-menu/options.php:5
msgid "Select Menu"
msgstr "Seleziona menu"

#: inc/helpers.php:79
msgid "You don't have a menu yet, please create one here &rarr;"
msgstr "Ancora non hai un menu, creane uno qui &rarr;"

#: inc/options/general/posts-listing.php:1429
#: inc/options/woocommerce/card-product-elements.php:1225
msgid "Card Border"
msgstr "Bordo della scheda"

#: inc/options/general/page-title.php:930
#: inc/options/general/posts-listing.php:942
msgid "Meta Font"
msgstr "Font dei metadati"

#: inc/options/general/meta.php:313
msgid "Updated Date"
msgstr "Data aggiornata"

#: inc/options/general/meta.php:276
msgid "Published Date"
msgstr "Data di pubblicazione"

#: admin/notices/templates.php:42
msgid "We strongly recommend you to activate the"
msgstr "Ti consigliamo vivamente di attivare il"

#: admin/notices/templates.php:40 static/bundle/274.1d3d5b69a8da41b72034.js:1
#: static/js/notification/Notification.js:68
msgid "Thanks for installing Blocksy, you rock!"
msgstr "Grazie per aver installato Blocksy, sei forte!"

#: inc/options/single-elements/post-share-box.php:132
msgid "WhatsApp"
msgstr "WhatsApp"

#: inc/options/single-elements/post-share-box.php:128
msgid "Viber"
msgstr "Viber"

#: inc/options/single-elements/post-share-box.php:112
msgid "Hacker News"
msgstr "Hacker News"

#: inc/components/skip-to-content-link.php:12
msgid "Skip to content"
msgstr "Salta al contenuto"

#: inc/options/general/meta.php:193 inc/options/general/page-title.php:536
msgid "Meta Elements"
msgstr "Elementi meta"

#: inc/panel-builder/header/offcanvas/config.php:4
msgid "Offcanvas"
msgstr "Offcanvas"

#: inc/panel-builder/footer/bottom-row/config.php:4
#: inc/panel-builder/header/bottom-row/config.php:4
#: static/bundle/customizer-controls.js:1
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/customizer/panels-builder/placements/PlacementsBuilder/Row.js:19
#: static/js/customizer/sync/builder.js:134
msgid "Bottom Row"
msgstr "Riga inferiore"

#: inc/panel-builder/footer/middle-row/config.php:4
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
msgid "Middle Row"
msgstr "Riga di mezzo"

#: inc/panel-builder/footer/top-row/config.php:4
#: inc/panel-builder/header/top-row/config.php:4
#: static/bundle/customizer-controls.js:1
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/customizer/panels-builder/placements/PlacementsBuilder/Row.js:17
#: static/js/customizer/sync/builder.js:118
msgid "Top Row"
msgstr "Riga superiore"

#: woocommerce/content-widget-reviews.php:48
msgid "by %s"
msgstr "di %s"

#: inc/components/pagination.php:208
msgid "Next"
msgstr "Succ"

#: inc/components/pagination.php:206
msgid "Prev"
msgstr "Prec"

#: inc/classes/screen-manager.php:252
#: inc/panel-builder/header/search/options.php:6
msgid "Posts"
msgstr "Articoli"

#: inc/panel-builder/header/search/options.php:504
msgid "Chose in which post types do you want to perform searches."
msgstr "Scegli in quali tipi di articoli desideri eseguire le ricerche."

#: inc/panel-builder/header/logo/options.php:570
#: inc/panel-builder/header/logo/options.php:599
#: inc/panel-builder/header/logo/options.php:621
#: inc/panel-builder/header/logo/options.php:642
msgid "Site Tagline Color"
msgstr "Colore del motto del sito"

#: inc/panel-builder/header/logo/options.php:560
msgid "Site Tagline Font"
msgstr "Font del motto del sito"

#: inc/panel-builder/header/logo/options.php:424
#: inc/panel-builder/header/logo/options.php:453
#: inc/panel-builder/header/logo/options.php:484
#: inc/panel-builder/header/logo/options.php:514
msgid "Site Title Color"
msgstr "Colore del titolo del sito"

#: inc/panel-builder/header/logo/options.php:214
msgid "Site Tagline Visibility"
msgstr "Visibilità del motto del sito"

#: inc/panel-builder/header/logo/options.php:194
msgid "Site Tagline"
msgstr "Motto del sito"

#: inc/options/general/comments-single.php:211
#: inc/options/general/page-title.php:1269
#: inc/options/single-elements/related-posts.php:921
#: inc/panel-builder/footer/options.php:89
msgid "Container Background"
msgstr "Sfondo del contenitore"

#: inc/options/meta/post.php:20 inc/options/posts/post.php:13
msgid "Post Structure"
msgstr "Struttura dell'articolo"

#: inc/options/posts/blog.php:13
msgid "Blog Title"
msgstr "Titolo del blog"

#: inc/options/general/meta.php:233 inc/options/general/meta.php:287
#: inc/options/general/meta.php:324 inc/options/general/meta.php:385
#: inc/options/general/pagination.php:58
msgid "Label"
msgstr "Etichetta"

#: inc/options/general/posts-listing.php:312
#: inc/options/single-elements/featured-image.php:74
#: inc/options/woocommerce/card-product-elements.php:214
msgid "Image Width"
msgstr "Larghezza immagine"

#: inc/options/single-elements/featured-image.php:106
msgid "Below Title"
msgstr "Sotto al titolo"

#: inc/options/single-elements/featured-image.php:105
msgid "Above Title"
msgstr "Sopra al titolo"

#: inc/options/single-elements/featured-image.php:98
msgid "Image Location"
msgstr "Posizione dell'immagine"

#: inc/options/single-elements/structure-design.php:54
msgid "Content Area Background"
msgstr "Sfondo dell'area del contenuto"

#: inc/options/general/posts-listing.php:1715
#: inc/options/single-elements/structure-design.php:21
#: inc/options/woocommerce/archive-main.php:171
msgid "Page Background"
msgstr "Sfondo della pagina"

#: inc/options/meta/default.php:111 inc/options/meta/page.php:96
#: inc/options/meta/post.php:92
#: inc/options/single-elements/featured-image.php:82
#: inc/options/single-elements/structure.php:85
msgid "Wide"
msgstr "Ampio"

#: inc/options/meta/default.php:104 inc/options/meta/page.php:89
#: inc/options/meta/post.php:85 inc/options/single-elements/structure.php:77
msgid "Content Area Style"
msgstr "Stile dell'area del contenuto"

#: inc/options/general/page-title.php:1355
#: inc/options/general/page-title.php:1396 inc/options/meta/default.php:94
#: inc/options/meta/default.php:125 inc/options/meta/page.php:79
#: inc/options/meta/page.php:110 inc/options/meta/post.php:75
#: inc/options/meta/post.php:109
#: inc/panel-builder/footer/widget-area-1/options.php:110
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/bundle/options.js:49
#: static/js/backend/woo-attributes/AfterHeading.js:30
#: static/js/options/options/background/ImagePicker.js:119
#: static/js/options/options/ct-border.js:45
#: static/js/options/options/ct-box-shadow.js:61
msgid "Inherit"
msgstr "Eredita"

#: inc/options/general/posts-listing.php:68
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/background/ImagePicker.js:93
msgid "Cover"
msgstr "Copertina"

#: inc/options/general/posts-listing.php:1068
#: inc/options/general/posts-listing.php:1114
#: inc/options/general/posts-listing.php:1160
#: inc/options/woocommerce/card-product-elements.php:937
#: inc/options/woocommerce/general/messages.php:73
#: inc/options/woocommerce/general/messages.php:189
#: inc/options/woocommerce/general/messages.php:303
#: inc/options/woocommerce/single-product-elements.php:360
#: inc/options/woocommerce/single-product-elements.php:433
msgid "Button Font Color"
msgstr "Colore del font del pulsante"

#: inc/options/general/back-to-top.php:179
msgid "Alignment"
msgstr "Allineamento"

#: inc/options/general/posts-listing.php:516
msgid "Show Arrow"
msgstr "Mostra freccia"

#: inc/options/general/posts-listing.php:497
#: inc/panel-builder/footer/socials/options.php:129
#: inc/panel-builder/header/socials/options.php:127
#: inc/panel-builder/header/trigger/options.php:77
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/box-shadow/options.js:159
msgid "Outline"
msgstr "Contorno"

#: inc/options/general/posts-listing.php:487
msgid "Read More Button"
msgstr "Pulsante Leggi di Più"

#: inc/options/general/page-title.php:1106
#: inc/options/single-elements/author-box.php:345
#: inc/options/single-elements/post-share-box.php:194
#: inc/options/single-elements/post-share-box.php:511
#: inc/options/single-elements/post-share-box.php:571
#: inc/options/woocommerce/single-product-layers.php:366
#: inc/panel-builder/footer/socials/options.php:89
#: inc/panel-builder/footer/socials/options.php:293
#: inc/panel-builder/header/socials/options.php:88
#: inc/panel-builder/header/socials/options.php:344
#: inc/panel-builder/header/socials/options.php:377
#: inc/panel-builder/header/socials/options.php:410
#: inc/panel-builder/header/socials/options.php:441
msgid "Icons Color"
msgstr "Colore delle icone"

#: inc/panel-builder/header/cart/options.php:142
#: inc/panel-builder/header/logo/options.php:281
#: inc/panel-builder/header/search/options.php:129
#: inc/panel-builder/header/trigger/options.php:143
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/ct-spacing/input.js:152
msgid "Right"
msgstr "Destra"

#: inc/panel-builder/header/cart/options.php:141
#: inc/panel-builder/header/logo/options.php:280
#: inc/panel-builder/header/search/options.php:128
#: inc/panel-builder/header/trigger/options.php:142
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/ct-spacing/input.js:151
msgid "Left"
msgstr "Sinistra"

#: inc/options/general/typography.php:17
msgid "Base Font"
msgstr "Font base"

#: inc/panel-builder/header/logo/options.php:149
#: inc/panel-builder/header/logo/options.php:413
msgid "Site Title"
msgstr "Titolo del sito"

#. translators: placeholder here is the link URL.
#: inc/options/single-elements/author-box.php:129
msgid "You can set the author social channels %shere%s."
msgstr "Puoi impostare i canali social dell'autore %squi%s."

#: inc/options/customizer.php:85
msgid "Typography"
msgstr "Tipografia"

#: inc/options/general/content-elements.php:25
#: inc/options/general/page-title.php:752
#: inc/options/general/posts-listing.php:349
#: inc/options/single-elements/related-posts.php:347
#: inc/options/woocommerce/card-product-elements.php:341
#: inc/panel-builder/footer/socials/options.php:109
#: inc/panel-builder/footer/widget-area-1/options.php:109
#: inc/panel-builder/header/button/options.php:163
#: inc/panel-builder/header/socials/options.php:108
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/ct-border.js:75
#: static/js/options/options/ct-box-shadow.js:64
msgid "None"
msgstr "Nessuno"

#: inc/options/general/pagination.php:266
#: inc/options/general/posts-listing.php:586
#: inc/options/general/sidebar.php:438
#: inc/options/woocommerce/single-product-elements.php:506
#: inc/options/woocommerce/single-product-layers.php:224
msgid "Divider"
msgstr "Divisore"

#: inc/options/general/typography.php:132
msgid "Preformatted"
msgstr "Preformattato"

#: inc/options/general/colors.php:745 inc/options/general/typography.php:88
msgid "Heading 6 (H6)"
msgstr "Titolo 6 (H6)"

#: inc/options/general/colors.php:723 inc/options/general/typography.php:77
msgid "Heading 5 (H5)"
msgstr "Titolo 5 (H5)"

#: inc/options/general/colors.php:701 inc/options/general/typography.php:66
msgid "Heading 4 (H4)"
msgstr "Titolo 4 (H4)"

#: inc/options/general/colors.php:679 inc/options/general/typography.php:55
msgid "Heading 3 (H3)"
msgstr "Titolo 3 (H3)"

#: inc/options/general/colors.php:657 inc/options/general/typography.php:44
msgid "Heading 2 (H2)"
msgstr "Titolo 2 (H2)"

#: inc/options/general/colors.php:635 inc/options/general/typography.php:33
msgid "Heading 1 (H1)"
msgstr "Titolo 1 (H1)"

#: inc/options/general/meta.php:157
msgid "Date Format"
msgstr "Formato della data"

#: inc/options/general/posts-listing.php:1532
msgid "Gutenberg"
msgstr "Gutenberg"

#: inc/options/general/posts-listing.php:1527
msgid "Enhanced Grid"
msgstr "Griglia aumentata"

#: inc/options/general/page-title.php:853
#: inc/options/general/posts-listing.php:824
#: inc/options/integrations/tutorlms-single.php:8
#: inc/options/woocommerce/single-product-elements.php:141
msgid "Title Font"
msgstr "Font del titolo"

#: inc/options/general/form-elements.php:107
#: inc/options/woocommerce/card-product-elements.php:718
#: inc/options/woocommerce/card-product-elements.php:801
#: inc/options/woocommerce/card-product-elements.php:843
#: inc/options/woocommerce/card-product-elements.php:887
#: inc/options/woocommerce/card-product-elements.php:1019
#: inc/options/woocommerce/general/product-badges.php:189
#: inc/options/woocommerce/results-count.php:50
#: inc/options/woocommerce/single-product-tabs.php:188
#: inc/panel-builder/footer/copyright/options.php:93
#: inc/panel-builder/footer/menu/options.php:151
#: inc/panel-builder/header/menu/options.php:158
#: inc/panel-builder/header/menu/options.php:739
#: inc/panel-builder/header/mobile-menu/options.php:196
#: inc/panel-builder/header/mobile-menu/options.php:329
#: inc/panel-builder/header/search/options.php:540
#: inc/panel-builder/header/text/options.php:156
msgid "Font"
msgstr "Font"

#: inc/components/single/comments.php:73
msgid "Save my name, email, and website in this browser for the next time I comment."
msgstr "Salva il mio nome, email e sito web in questo browser per la prossima volta che commento."

#: inc/options/single-elements/post-share-box.php:124
msgid "Telegram"
msgstr "Telegram"

#: inc/options/single-elements/post-share-box.php:116
msgid "VKontakte"
msgstr "Vkontakte"

#: admin/dashboard/plugins/config.php:39
msgid "A new and innovative way of building WordPress pages visually. No designer or developer skills required. The only tools you'll need to master are clicks and drags."
msgstr "Un modo nuovo e innovativo di creare visivamente pagine WordPress. Non sono richieste competenze da designer o sviluppatore. Gli unici strumenti che devi padroneggiare sono clic e trascinamenti."

#: admin/dashboard/api.php:55
msgid "Theme"
msgstr "Tema"

#: inc/options/woocommerce/single-product-layers.php:40
msgid "%s"
msgstr "%s"

#: comments.php:107
msgid "Comments are closed."
msgstr "I commenti sono chiusi."

#: comments.php:99
msgid "Newer Comments &rarr;"
msgstr "Commenti più recenti &rarr;"

#: comments.php:95
msgid "&larr; Older Comments"
msgstr "&larr; Commenti meno recenti"

#: comments.php:90
msgid "Comment navigation"
msgstr "Navigazione dei commenti"

#. translators: % refers to the number of comments, when more than 1
#: comments.php:62 inc/components/post-meta.php:505
msgid "% Comments"
msgstr "% commenti"

#: comments.php:62
msgid "One comment"
msgstr "Un commento"

#: comments.php:62
msgid "No comments yet"
msgstr "Ancora nessun commento"

#: inc/components/single/comments.php:56
msgid "Add Comment"
msgstr "Aggiungi commento"

#: inc/components/single/comments.php:127 inc/components/social-box.php:922
#: inc/components/social-box.php:1500
#: inc/components/woocommerce/single/review-form.php:20
#: inc/options/single-elements/post-share-box.php:148
msgid "Email"
msgstr "Email"

#: inc/components/single/comments.php:120
#: inc/components/woocommerce/single/review-form.php:18
msgid "Name"
msgstr "Nome"

#: inc/components/single/comments.php:43
msgid "Cancel Reply"
msgstr "Cancella risposta"

#: inc/components/single/comments.php:42
msgid "Leave a Reply"
msgstr "Lascia una risposta"

#: inc/components/hero/elements.php:334
msgid "%sSorry, but nothing matched your search terms. Please try again with some different keywords.%s"
msgstr "%sNon corrisponde nulla ai tuoi termini di ricerca. Riprova con altre parole chiave.%s"

#: inc/components/single/comments.php:305
msgid "Your comment is awaiting moderation."
msgstr "Il tuo commento è in attesa di moderazione."

#. translators: 1: date, 2: time
#: inc/components/single/comments.php:258
msgid "%1$s / %2$s"
msgstr "%1$s / %2$s"

#. translators: post title
#: inc/template-tags.php:377
msgid "Next %s"
msgstr "Prossimo %s"

#. translators: post title
#: inc/template-tags.php:349
msgid "Previous %s"
msgstr "Precedente %s"

#: inc/components/social-box.php:635 inc/components/social-box.php:1556
#: inc/options/single-elements/post-share-box.php:120
msgid "Odnoklassniki"
msgstr "Odnoklassniki"

#: inc/options/single-elements/post-share-box.php:108
msgid "Reddit"
msgstr "Reddit"

#: inc/customizer/init.php:63
msgid "Core"
msgstr "Core"

#: inc/options/customizer.php:156
msgid "WooCommerce"
msgstr "WooCommerce"

#: inc/manager.php:237
msgid "Show more"
msgstr "Mostra di più"

#: inc/components/woocommerce/boot.php:99 inc/init.php:472
msgid "Add widgets here."
msgstr "Aggiungi widget qui."

#: inc/components/post-meta.php:199 inc/components/post-meta.php:783
#: inc/components/post-meta.php:791 inc/options/general/meta.php:290
#: inc/options/general/meta.php:327
#: static/js/customizer/sync/helpers/entry-meta.js:82
#: static/js/customizer/sync/helpers/entry-meta.js:93
msgid "On"
msgstr "On"

#: inc/components/post-meta.php:189 inc/components/post-meta.php:775
#: inc/options/general/meta.php:236
#: static/js/customizer/sync/helpers/entry-meta.js:71
msgid "By"
msgstr "Di"

#. translators: placeholder here is the actual PHP version.
#: inc/php-fallback.php:16 inc/php-fallback.php:28 inc/php-fallback.php:42
msgid "Blocksy requires at least PHP version 5.7.0. You are running version %s. Please upgrade and try again."
msgstr "Blocksy richiede che la versione PHP sia almeno 5.7.0. Stai utilizzando la versione %s. Aggiorna e prova di nuovo."

#: inc/options/engagement/general.php:16
msgid "Schema.org Markup"
msgstr "Schema.org Markup"

#: inc/options/customizer.php:202
msgid "Extensions"
msgstr "Estensioni"

#: inc/options/general/form-elements.php:13
msgid "Form Elements"
msgstr "Elementi del modulo"

#: inc/options/general/pagination.php:13 inc/options/pages/author-page.php:60
#: inc/options/pages/search-page.php:64 inc/options/posts/blog.php:67
#: inc/options/posts/categories.php:53
#: inc/options/posts/custom-post-type-archive.php:64
msgid "Pagination"
msgstr "Paginazione"

#: inc/options/engagement/social-accounts.php:28
#: inc/options/engagement/social-accounts.php:34
msgid "Social Network Accounts"
msgstr "Account dei social network"

#: inc/options/engagement/general.php:5
msgid "Visitor Engagement"
msgstr "Coinvolgimento dei visitatori"

#: inc/options/customizer.php:177 inc/options/woocommerce/single-main.php:7
msgid "Single Product"
msgstr "Prodotto singolo"

#: inc/options/general/custom-post-types.php:81
#: inc/options/woocommerce/general/product-badges.php:64
#: inc/options/woocommerce/general/product-badges.php:144
msgid "Archive"
msgstr "Archivio"

#: inc/components/breadcrumbs.php:359
msgid "Shop"
msgstr "Negozio"

#: inc/options/customizer.php:139 inc/options/pages/author-page.php:18
msgid "Author Page"
msgstr "Pagina dell'autore"

#: inc/options/customizer.php:103 inc/options/posts/blog.php:15
msgid "Blog Posts"
msgstr "Articoli del blog"

#: inc/options/customizer.php:98
msgid "Post types"
msgstr "Tipi di articolo"

#: inc/integrations/beaver-themer.php:27 inc/options/customizer.php:67
msgid "Footer"
msgstr "Footer"

#: inc/options/general/layout.php:13
msgid "Layout"
msgstr "Layout"

#: inc/manager.php:239
msgid "Search results"
msgstr "Risultati della ricerca"

#: inc/options/woocommerce/single-main.php:104
msgid "AJAX Add To Cart"
msgstr "Aggiungi al carrello con AJAX"

#: inc/options/woocommerce/single-product-gallery.php:325
#: inc/panel-builder/footer/menu/options.php:46
#: inc/panel-builder/footer/socials/options.php:146
msgid "Vertical"
msgstr "Verticale"

#: inc/options/woocommerce/single-product-gallery.php:324
#: inc/panel-builder/footer/menu/options.php:45
#: inc/panel-builder/footer/socials/options.php:145
msgid "Horizontal"
msgstr "Orizzontale"

#: inc/components/blocks/blocks-fallback.php:36
#: inc/options/general/breadcrumbs.php:334
#: inc/options/general/page-title.php:283
#: inc/options/woocommerce/single-product-layers.php:133
msgid "Breadcrumbs"
msgstr "Breadcrumb"

#: inc/options/meta/default.php:234 inc/options/meta/page.php:173
#: inc/options/meta/post.php:214 inc/options/pages/author-page.php:79
#: inc/options/pages/page.php:52 inc/options/pages/page.php:129
#: inc/options/pages/search-page.php:83 inc/options/posts/blog.php:86
#: inc/options/posts/custom-post-type-archive.php:83
#: inc/options/posts/custom-post-type-single.php:160
#: inc/options/posts/custom-post-type-single.php:182
#: inc/options/posts/post.php:68 inc/options/posts/post.php:122
#: inc/options/woocommerce/archive-main.php:199
#: inc/options/woocommerce/single-main.php:80
msgid "Page Elements"
msgstr "Elementi della pagina"

#: inc/options/integrations/tutorlms-single.php:162
#: inc/options/woocommerce/general/star-rating.php:19
msgid "Star Rating Color"
msgstr "Colore delle stelle di valutazione"

#: inc/options/woocommerce/general/product-badges.php:199
msgid "Sale Badge"
msgstr "Badge Offerta"

#: inc/options/woocommerce/card-product-elements.php:445
#: inc/options/woocommerce/general/star-rating.php:13
#: inc/options/woocommerce/single-product-layers.php:167
msgid "Star Rating"
msgstr "Stelle di valutazione"

#: inc/options/woocommerce/archive-main.php:304
msgid "Sort by price (desc)"
msgstr "Ordina per prezzo (decr)"

#: inc/options/woocommerce/archive-main.php:303
msgid "Sort by price (asc)"
msgstr "Ordina per prezzo (cresc)"

#: inc/options/woocommerce/archive-main.php:302
msgid "Sort by most recent"
msgstr "Ordina per più recente"

#: inc/options/woocommerce/archive-main.php:301
msgid "Average rating"
msgstr "Voto medio"

#: inc/options/woocommerce/archive-main.php:300
msgid "Popularity (sales)"
msgstr "Popolarità (vendite)"

#: inc/options/woocommerce/archive-main.php:299
msgid "Default sorting (custom ordering + name)"
msgstr "Ordinamento predefinito (ordinamento personalizzato + nome)"

#: inc/options/woocommerce/archive-main.php:291
msgid "How should products be sorted in the catalog by default?"
msgstr "Qual è l'impostazione di ordinamento predefinita per i prodotti nel catalogo?"

#: inc/options/woocommerce/archive-main.php:286
msgid "Default product sorting"
msgstr "Ordinamento predefinito del prodotto"

#: inc/options/woocommerce/archive-main.php:280
msgid "Show subcategories & products"
msgstr "Mostra sottocategorie e prodotti"

#: inc/options/woocommerce/archive-main.php:279
msgid "Show subcategories"
msgstr "Mostra sottocategorie"

#: inc/options/woocommerce/archive-main.php:275
msgid "Choose what to display on product category pages."
msgstr "Scegli cosa visualizzare nelle pagine delle categorie del prodotto."

#: inc/options/woocommerce/archive-main.php:266
msgid "Category display"
msgstr "Visualizzazione categoria"

#: inc/options/woocommerce/archive-main.php:260
msgid "Show categories & products"
msgstr "Mostra categorie e prodotti"

#: inc/options/woocommerce/archive-main.php:259
msgid "Show categories"
msgstr "Mostra categorie"

#: inc/options/woocommerce/archive-main.php:255
msgid "Choose what to display on the main shop page."
msgstr "Scegli cosa visualizzare nella pagina principale del negozio."

#: inc/options/woocommerce/archive-main.php:250
#: inc/options/woocommerce/archive-main.php:258
#: inc/options/woocommerce/archive-main.php:270
#: inc/options/woocommerce/archive-main.php:278
msgid "Show products"
msgstr "Mostra prodotti"

#: inc/options/woocommerce/archive-main.php:246
msgid "Shop page display"
msgstr "Visualizza la pagina del negozio"

#: inc/options/woocommerce/archive-main.php:239
msgid "Product Catalog"
msgstr "Catalogo dei prodotti"

#: inc/options/general/posts-listing.php:1572
msgid "Number of Posts"
msgstr "Numero di articoli"

#: inc/options/single-elements/related-posts.php:543
msgid "After Comments"
msgstr "Dopo i commenti"

#: inc/options/single-elements/related-posts.php:542
msgid "Before Comments"
msgstr "Prima dei commenti"

#: inc/components/single/content-helpers.php:351
#: inc/options/single-elements/post-tags.php:52
msgid "Tags"
msgstr "Tag"

#: inc/options/single-elements/related-posts.php:118
msgid "Related Criteria"
msgstr "Criteri correlati"

#: inc/options/single-elements/related-posts.php:19
#: inc/options/single-elements/related-posts.php:168 inc/template-tags.php:517
msgid "Related Posts"
msgstr "Articoli correlati"

#: inc/options/meta/post.php:6 inc/options/posts/post.php:6
msgid "Post Title"
msgstr "Titolo dell'articolo"

#: inc/options/single-elements/post-nav.php:19
msgid "Posts Navigation"
msgstr "Navigazione degli articoli"

#: inc/options/single-elements/author-box.php:26
msgid "Box Type"
msgstr "Tipo di box"

#: inc/options/single-elements/author-box.php:11
msgid "Author Box"
msgstr "Riquadro autore"

#: inc/options/single-elements/post-share-box.php:375
msgid "Bottom Box Spacing"
msgstr "Spaziatura del box inferiore"

#: inc/options/single-elements/post-share-box.php:352
msgid "Top Box Spacing"
msgstr "Spaziatura del box superiore"

#: inc/options/single-elements/post-share-box.php:69
msgid "Share Networks"
msgstr "Condividi in rete"

#: inc/options/general/sidebar.php:260
#: inc/options/single-elements/post-share-box.php:224
#: inc/options/woocommerce/general/store-notice.php:48
#: inc/panel-builder/header/cart/options.php:143
#: inc/panel-builder/header/search/options.php:130
#: inc/panel-builder/header/trigger/options.php:144
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/ct-spacing/input.js:150
msgid "Bottom"
msgstr "Inferiore"

#: inc/options/general/sidebar.php:259
#: inc/options/single-elements/post-share-box.php:223
#: inc/options/woocommerce/general/store-notice.php:47
#: inc/panel-builder/header/logo/options.php:282
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/ct-spacing/input.js:149
msgid "Top"
msgstr "Superiore"

#: inc/options/single-elements/post-share-box.php:214
msgid "Box Location"
msgstr "Posizione del box"

#: inc/components/blocks/blocks-fallback.php:40
#: inc/options/single-elements/post-share-box.php:648
#: inc/options/single-elements/post-share-box.php:664
msgid "Share Box"
msgstr "Box Condividi"

#: inc/options/meta/default.php:191 inc/options/meta/post.php:172
#: inc/options/posts/post.php:41
msgid "Post Elements"
msgstr "Elementi dell'articolo"

#: inc/options/general/sidebar-particular.php:30
msgid "Sidebar Position"
msgstr "Posizione della barra laterale"

#: inc/options/customizer.php:73 inc/options/general/sidebar-particular.php:20
msgid "Sidebar"
msgstr "Barra laterale"

#: inc/init.php:408
msgid "Header Menu 2"
msgstr "Menu dell'header 2"

#: inc/components/social-box.php:622 inc/components/social-box.php:1648
msgid "VK"
msgstr "VK"

#: inc/options/single-elements/post-share-box.php:100
msgid "Pinterest"
msgstr "Pinterest"

#: inc/panel-builder/header/text/options.php:12
#: inc/panel-builder/header/text/view.php:46
msgid "Sample text"
msgstr "Testo di esempio"

#: inc/components/blocks/legacy/legacy-socials-transformer.php:29
#: inc/options/single-elements/author-box.php:124
msgid "Social Icons"
msgstr "Icone Social"

#: inc/init.php:406
msgid "Footer Menu"
msgstr "Menu del footer"

#: inc/options/customizer.php:145
msgid "Search Page"
msgstr "Pagina di ricerca"

#: inc/classes/screen-manager.php:253
#: inc/components/single/content-helpers.php:298 inc/options/customizer.php:133
#: inc/panel-builder/header/search/options.php:7
msgid "Pages"
msgstr "Pagine"

#: inc/options/customizer.php:109 inc/options/posts/post.php:7
msgid "Single Post"
msgstr "Articolo singolo"

#: inc/options/general/general.php:108
msgid "Color scheme"
msgstr "Schema colore"

#: inc/options/general/layout.php:76
msgid "Wide Alignment Offset"
msgstr "Allineamento offset ampio"

#: inc/options/general/layout.php:66
msgid "Narrow Container Max Width"
msgstr "Larghezza massima del contenitore stretto"

#: inc/options/general/layout.php:28
msgid "Content Area Spacing"
msgstr "Spaziatura dell'area del contenuto"

#: inc/options/general/layout.php:19
msgid "Maximum Site Width"
msgstr "Larghezza massima del sito"

#: inc/options/general/sidebar.php:91
#: inc/options/single-elements/author-box.php:196
#: inc/options/single-elements/related-posts.php:909
msgid "Container Inner Spacing"
msgstr "Spaziatura interna del contenitore"

#: inc/options/general/back-to-top.php:251
msgid "Shape Background Color"
msgstr "Colore di sfondo della forma"

#: inc/options/general/back-to-top.php:142
msgid "Circle"
msgstr "Cerchio"

#: inc/options/general/back-to-top.php:141
#: inc/panel-builder/footer/socials/options.php:111
#: inc/panel-builder/header/socials/options.php:110
msgid "Square"
msgstr "Quadrato"

#: inc/options/general/back-to-top.php:133
msgid "Button Shape"
msgstr "Forma del pulsante"

#: inc/options/general/back-to-top.php:19
msgid "Scroll to Top"
msgstr "Torna in alto"

#: inc/panel-builder/header/menu/options.php:661
msgid "Reveal Effect"
msgstr "Effetto rivela"

#: inc/options/general/page-title.php:862
#: inc/options/general/posts-listing.php:837
#: inc/options/integrations/tutorlms-single.php:17
#: inc/options/woocommerce/single-product-elements.php:149
msgid "Title Font Color"
msgstr "Colore del font del titolo"

#: inc/options/general/sidebar.php:166
msgid "Sticky Sidebar"
msgstr "Barra laterale fissa"

#: inc/options/general/sidebar.php:152
msgid "Separate Widgets"
msgstr "Widget separati"

#: inc/options/general/sidebar.php:108
msgid "Widgets Vertical Spacing"
msgstr "Spaziatura verticale dei widget"

#: inc/options/general/sidebar.php:64
msgid "Sidebar Gap"
msgstr "Spaziatura della barra laterale"

#: inc/options/general/sidebar.php:53
msgid "Sidebar Width"
msgstr "Larghezza della barra laterale"

#: inc/options/general/page-title.php:1249
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/background/ImagePicker.js:184
msgid "Image Overlay Color"
msgstr "Colore della sovrapposizione dell'immagine"

#: inc/panel-builder/header/text/options.php:362
#: inc/panel-builder/header/text/options.php:384
#: inc/panel-builder/header/text/options.php:405
msgid "Initial Color"
msgstr "Colore iniziale"

#: inc/options/general/page-title.php:772
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/background/ImagePicker.js:28
msgid "Change Image"
msgstr "Cambia immagine"

#: inc/options/general/page-title.php:771
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/background/ImagePicker.js:27
msgid "Select Image"
msgstr "Seleziona immagine"

#: inc/options/general/page-title.php:766
msgid "Custom Image"
msgstr "Immagine personalizzata"

#: inc/options/general/page-title.php:827
msgid "Container Min Height"
msgstr "Altezza minima del contenitore"

#: inc/components/hero/elements.php:353
msgid "This is where you can add new products to your store."
msgstr "Qui puoi aggiungere nuovi prodotti al tuo negozio."

#: inc/options/general/page-title.php:99
msgid "Description"
msgstr "Descrizione"

#: inc/components/breadcrumbs.php:54 inc/components/hero/elements.php:201
#: inc/options/general/breadcrumbs.php:164
#: inc/options/general/page-title.php:123
#: inc/options/general/page-title.php:364
#: admin/dashboard/static/bundle/main.js:6
#: admin/dashboard/static/js/Navigation.js:21
msgid "Home"
msgstr "Home"

#: inc/classes/screen-manager.php:254 inc/components/hero/elements.php:201
#: inc/panel-builder/header/search/options.php:16
msgid "Products"
msgstr "Prodotti"

#: inc/options/woocommerce/single-product-layers.php:285
msgid "Meta"
msgstr "Meta"

#: inc/options/single-elements/post-share-box.php:400
#: inc/options/single-elements/post-tags.php:124
#: inc/options/woocommerce/card-product-elements.php:641
#: inc/panel-builder/header/logo/options.php:300
#: inc/panel-builder/header/text/options.php:57
msgid "Content Alignment"
msgstr "Allineamento del contenuto"

#: inc/options/general/page-title.php:196 static/bundle/options.js:49
#: static/js/backend/woo-attributes/AfterHeading.js:53
msgid "Type"
msgstr "Tipo"

#: inc/options/general/buttons.php:145 inc/options/general/form-elements.php:85
#: inc/options/general/pagination.php:281
#: inc/options/general/posts-listing.php:1464
#: inc/options/general/sidebar.php:480
#: inc/options/single-elements/author-box.php:506
#: inc/options/single-elements/featured-image.php:143
#: inc/options/single-elements/post-tags.php:223
#: inc/options/woocommerce/single-product-gallery.php:277
#: inc/options/woocommerce/single-product-gallery.php:373
#: inc/panel-builder/header/button/options.php:834
#: inc/panel-builder/header/cart/options.php:1126
#: inc/panel-builder/header/middle-row/options.php:590
#: inc/panel-builder/header/offcanvas/options.php:428
#: inc/panel-builder/header/search/options.php:857
#: inc/panel-builder/header/trigger/options.php:606
msgid "Border Radius"
msgstr "Raggio del bordo"

#: inc/panel-builder/header/button/options.php:125
msgid "Open in new tab"
msgstr "Apri in una nuova scheda"

#: inc/panel-builder/header/button/options.php:50
#: inc/panel-builder/header/button/view.php:82
#: admin/dashboard/static/bundle/main.js:6
#: admin/dashboard/static/js/components/SinglePremiumPlugin.js:110
msgid "Download"
msgstr "Scarica"

#: inc/helpers.php:206 inc/panel-builder/header/button/options.php:40
msgid "Large"
msgstr "Grande"

#: inc/helpers.php:204 inc/panel-builder/header/button/options.php:39
msgid "Medium"
msgstr "Medio"

#: inc/panel-builder/header/button/options.php:38
msgid "Small"
msgstr "Piccolo"

#: inc/options/general/breadcrumbs.php:147
#: inc/options/general/breadcrumbs.php:308 inc/options/general/colors.php:581
#: inc/options/general/page-title.php:1208
#: inc/options/general/posts-listing.php:506
#: inc/options/single-elements/author-box.php:320
#: inc/options/woocommerce/general/product-badges.php:216
#: inc/options/woocommerce/general/product-badges.php:247
#: inc/options/woocommerce/single-product-elements.php:250
#: inc/panel-builder/header/cart/options.php:502
#: inc/panel-builder/header/cart/options.php:533
#: inc/panel-builder/header/cart/options.php:563
msgid "Text"
msgstr "Testo"

#: inc/options/general/colors.php:586 inc/options/general/posts-listing.php:496
#: inc/options/woocommerce/general/product-badges.php:222
#: inc/options/woocommerce/general/product-badges.php:252
#: inc/options/woocommerce/single-product-tabs.php:319
#: inc/panel-builder/header/cart/options.php:496
#: inc/panel-builder/header/cart/options.php:528
#: inc/panel-builder/header/cart/options.php:558
#: inc/panel-builder/header/cart/options.php:994
#: inc/panel-builder/header/middle-row/options.php:154
#: inc/panel-builder/header/middle-row/options.php:181
#: inc/panel-builder/header/middle-row/options.php:190
#: inc/panel-builder/header/middle-row/options.php:205
#: inc/panel-builder/header/offcanvas/options.php:291
#: inc/panel-builder/header/search/options.php:725
msgid "Background"
msgstr "Sfondo"

#: inc/panel-builder/header/cart/options.php:444
#: inc/panel-builder/header/cart/options.php:479
#: inc/panel-builder/header/cart/options.php:510
#: inc/panel-builder/header/cart/options.php:540
msgid "Badge Color"
msgstr "Colore del badge"

#: inc/options/pages/search-page.php:18
#: inc/panel-builder/header/search/options.php:439
msgid "Search Results"
msgstr "Risultati della ricerca"

#: inc/classes/screen-manager.php:217
#: inc/components/builder/header-elements.php:196
#: inc/options/pages/search-page.php:13
#: inc/panel-builder/header/search/config.php:4
#: inc/panel-builder/header/search/options.php:139
#: inc/panel-builder/header/search/options.php:452
#: inc/panel-builder/header/search/view.php:28
#: woocommerce/product-searchform.php:28
msgid "Search"
msgstr "Cerca"

#: inc/options/general/back-to-top.php:222
#: inc/options/woocommerce/card-product-elements.php:79
#: inc/panel-builder/header/cart/options.php:324
#: inc/panel-builder/header/cart/options.php:352
#: inc/panel-builder/header/cart/options.php:382
#: inc/panel-builder/header/cart/options.php:411
#: inc/panel-builder/header/cart/options.php:1000
#: inc/panel-builder/header/offcanvas/options.php:308
#: inc/panel-builder/header/search/options.php:305
#: inc/panel-builder/header/search/options.php:332
#: inc/panel-builder/header/search/options.php:364
#: inc/panel-builder/header/search/options.php:394
#: inc/panel-builder/header/search/options.php:742
#: inc/panel-builder/header/trigger/options.php:319
#: inc/panel-builder/header/trigger/options.php:348
#: inc/panel-builder/header/trigger/options.php:379
#: inc/panel-builder/header/trigger/options.php:408
msgid "Icon Color"
msgstr "Colore dell'Icona"

#: inc/panel-builder/header/cart/options.php:623
msgid "Off Canvas"
msgstr "Off Canvas"

#: inc/panel-builder/header/offcanvas/options.php:16
msgid "Modal"
msgstr "Modal"

#: inc/panel-builder/header/cart/options.php:633
msgid "Dropdown Top Offset"
msgstr "Offset superiore del menu a discesa"

#: inc/panel-builder/header/menu/options.php:672
msgid "Opacity"
msgstr "Opacità"

#: inc/panel-builder/header/menu/options.php:671
msgid "Inner Reveal"
msgstr "Rivela dall'interno"

#: inc/panel-builder/header/menu/options.php:589
msgid "Dropdown Options"
msgstr "Opzioni del menu a discesa"

#: inc/components/menus.php:477 inc/helpers/cpt.php:28
#: inc/options/general/back-to-top.php:37
#: inc/options/general/breadcrumbs.php:12
#: inc/options/general/breadcrumbs.php:61 inc/options/general/meta.php:163
#: inc/options/general/meta.php:372 inc/options/general/page-title.php:732
#: inc/options/general/posts-listing.php:450
#: inc/options/single-elements/featured-image.php:81
#: inc/options/single-elements/post-nav.php:41
#: inc/options/single-elements/related-posts.php:145
#: inc/options/woocommerce/card-product-elements.php:501
#: inc/options/woocommerce/card-product-elements.php:535
#: inc/options/woocommerce/general/product-badges.php:90
#: inc/options/woocommerce/single-product-layers.php:61
#: inc/options/woocommerce/single-product-layers.php:106
#: inc/options/woocommerce/single-product-layers.php:372
#: inc/options/woocommerce/single-product-tabs.php:91
#: inc/panel-builder/footer/middle-row/options.php:435
#: inc/panel-builder/footer/middle-row/options.php:626
#: inc/panel-builder/footer/middle-row/options.php:667
#: inc/panel-builder/footer/options.php:12
#: inc/panel-builder/header/button/options.php:17
#: inc/panel-builder/header/button/options.php:37
#: inc/panel-builder/header/menu/options.php:84
#: inc/panel-builder/header/menu/options.php:670
#: inc/panel-builder/header/middle-row/options.php:41
#: inc/panel-builder/header/middle-row/options.php:382
#: inc/panel-builder/header/middle-row/options.php:489
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/bundle/options.js:49 static/js/backend/woo-variation.js:46
#: static/js/options/components/InputWithValidCssExpression.js:9
#: static/js/options/options/typography/helpers.js:67
#: static/js/options/options/typography/helpers.js:131
msgid "Default"
msgstr "Predefinito"

#: inc/options/general/meta.php:447
#: inc/panel-builder/header/menu/options.php:1033
#: inc/panel-builder/header/mobile-menu/options.php:291
msgid "Items Divider"
msgstr "Divisore degli elementi"

#: inc/options/woocommerce/single-product-tabs.php:165
#: inc/panel-builder/footer/menu/options.php:51
#: inc/panel-builder/header/menu/options.php:95
#: inc/panel-builder/header/menu/options.php:651
#: inc/panel-builder/header/mobile-menu/options.php:120
msgid "Items Spacing"
msgstr "Spaziatura degli elementi"

#: inc/panel-builder/header/button/options.php:75
msgid "Text Alignment"
msgstr "Allineamento del testo"

#: inc/options/general/back-to-top.php:108
#: inc/options/general/content-elements.php:50
#: inc/options/general/sidebar.php:46
#: inc/options/woocommerce/single-product-tabs.php:44
#: inc/panel-builder/header/cart/options.php:47
#: inc/panel-builder/header/menu/options.php:65
msgid "Type 4"
msgstr "Tipo 4"

#: inc/options/general/back-to-top.php:103
#: inc/options/general/breadcrumbs.php:98
#: inc/options/general/content-elements.php:49
#: inc/options/general/sidebar.php:40
#: inc/options/woocommerce/general/product-badges.php:47
#: inc/options/woocommerce/single-product-tabs.php:39
#: inc/panel-builder/header/cart/options.php:42
#: inc/panel-builder/header/menu/options.php:60
#: inc/panel-builder/header/mobile-menu/options.php:70
#: inc/panel-builder/header/trigger/options.php:32
msgid "Type 3"
msgstr "Tipo 3"

#: inc/panel-builder/header/cart/options.php:7
#: inc/panel-builder/header/menu/options.php:30
msgid "Top Level Options"
msgstr "Opzione del livello superiore"

#: inc/components/builder/header-elements.php:132
#: inc/panel-builder/footer/menu/config.php:4
#: inc/panel-builder/header/offcanvas/options.php:111
#: inc/panel-builder/header/trigger/options.php:153
#: inc/panel-builder/header/trigger/view.php:11
msgid "Menu"
msgstr "Menu"

#: inc/panel-builder/header/logo/config.php:4
#: inc/panel-builder/header/logo/options.php:10
#: inc/panel-builder/header/offcanvas-logo/options.php:11
msgid "Logo"
msgstr "Logo"

#: inc/panel-builder/header/middle-row/options.php:396
#: inc/panel-builder/header/middle-row/options.php:423
#: inc/panel-builder/header/middle-row/options.php:438
#: inc/panel-builder/header/middle-row/options.php:453
msgid "Bottom Border"
msgstr "Bordo inferiore"

#: inc/panel-builder/header/middle-row/options.php:289
#: inc/panel-builder/header/middle-row/options.php:316
#: inc/panel-builder/header/middle-row/options.php:330
#: inc/panel-builder/header/middle-row/options.php:345
msgid "Top Border"
msgstr "Bordo superiore"

#: inc/options/general/back-to-top.php:98
#: inc/options/general/breadcrumbs.php:93
#: inc/options/general/content-elements.php:48
#: inc/options/general/page-title.php:212 inc/options/general/sidebar.php:35
#: inc/options/single-elements/author-box.php:49
#: inc/options/single-elements/post-share-box.php:181
#: inc/options/woocommerce/archive-main.php:71
#: inc/options/woocommerce/general/product-badges.php:42
#: inc/options/woocommerce/general/quantity-input.php:50
#: inc/options/woocommerce/single-product-tabs.php:34
#: inc/panel-builder/header/cart/options.php:37
#: inc/panel-builder/header/menu/options.php:55
#: inc/panel-builder/header/mobile-menu/options.php:65
#: inc/panel-builder/header/trigger/options.php:27
msgid "Type 2"
msgstr "Tipo 2"

#: inc/options/general/back-to-top.php:93
#: inc/options/general/breadcrumbs.php:88
#: inc/options/general/content-elements.php:47
#: inc/options/general/page-title.php:207 inc/options/general/sidebar.php:30
#: inc/options/single-elements/author-box.php:44
#: inc/options/single-elements/post-share-box.php:176
#: inc/options/woocommerce/archive-main.php:66
#: inc/options/woocommerce/general/product-badges.php:37
#: inc/options/woocommerce/general/quantity-input.php:49
#: inc/options/woocommerce/single-product-tabs.php:29
#: inc/panel-builder/header/cart/options.php:32
#: inc/panel-builder/header/menu/options.php:50
#: inc/panel-builder/header/mobile-menu/options.php:60
#: inc/panel-builder/header/trigger/options.php:22
msgid "Type 1"
msgstr "Tipo 1"

#: inc/options/customizer.php:50
msgid "General Options"
msgstr "Opzioni generali"

#: inc/components/woocommerce/single/additional-actions-layer.php:100
#: inc/options/general/back-to-top.php:207
#: inc/options/general/page-title.php:315
#: inc/options/general/page-title.php:514
#: inc/options/general/page-title.php:814 inc/options/general/pagination.php:85
#: inc/options/general/pagination.php:104 inc/options/general/sidebar.php:245
#: inc/options/single-elements/author-box.php:223
#: inc/options/single-elements/featured-image.php:126
#: inc/options/single-elements/post-nav.php:120
#: inc/options/single-elements/post-nav.php:139
#: inc/options/single-elements/post-nav.php:156
#: inc/options/single-elements/post-share-box.php:429
#: inc/options/single-elements/post-tags.php:151
#: inc/options/single-elements/related-posts.php:607
#: inc/options/woocommerce/products-sorting.php:28
#: inc/options/woocommerce/related-upsells.php:112
#: inc/options/woocommerce/related-upsells.php:134
#: inc/options/woocommerce/results-count.php:34
#: inc/panel-builder/footer/copyright/options.php:77
#: inc/panel-builder/footer/menu/options.php:135
#: inc/panel-builder/footer/middle-row/options.php:452
#: inc/panel-builder/footer/options.php:51
#: inc/panel-builder/footer/socials/options.php:197
#: inc/panel-builder/footer/socials/options.php:215
#: inc/panel-builder/header/button/options.php:257
#: inc/panel-builder/header/cart/options.php:87
#: inc/panel-builder/header/cart/options.php:114
#: inc/panel-builder/header/logo/options.php:180
#: inc/panel-builder/header/logo/options.php:225
#: inc/panel-builder/header/logo/options.php:365
#: inc/panel-builder/header/search/options.php:84
#: inc/panel-builder/header/search/options.php:103
#: inc/panel-builder/header/socials/options.php:151
#: inc/panel-builder/header/text/options.php:122
#: inc/panel-builder/header/trigger/options.php:61
#: inc/panel-builder/header/trigger/options.php:117
msgid "Desktop"
msgstr "Desktop"

#: inc/options/general/back-to-top.php:197
#: inc/options/general/page-title.php:306
#: inc/options/general/page-title.php:505
#: inc/options/single-elements/author-box.php:213
#: inc/options/single-elements/post-share-box.php:419
#: inc/options/single-elements/post-tags.php:140
#: inc/options/single-elements/related-posts.php:596
#: inc/options/woocommerce/products-sorting.php:16
#: inc/options/woocommerce/results-count.php:21
msgid "Visibility"
msgstr "Visibilità"

#: inc/components/woocommerce/single/additional-actions-layer.php:102
#: inc/options/general/back-to-top.php:209
#: inc/options/general/page-title.php:317
#: inc/options/general/page-title.php:516
#: inc/options/general/page-title.php:816 inc/options/general/pagination.php:87
#: inc/options/general/pagination.php:106 inc/options/general/sidebar.php:247
#: inc/options/single-elements/author-box.php:225
#: inc/options/single-elements/featured-image.php:128
#: inc/options/single-elements/post-nav.php:122
#: inc/options/single-elements/post-nav.php:141
#: inc/options/single-elements/post-nav.php:158
#: inc/options/single-elements/post-share-box.php:431
#: inc/options/single-elements/post-tags.php:153
#: inc/options/single-elements/related-posts.php:609
#: inc/options/woocommerce/products-sorting.php:30
#: inc/options/woocommerce/related-upsells.php:114
#: inc/options/woocommerce/related-upsells.php:136
#: inc/options/woocommerce/results-count.php:36
#: inc/panel-builder/footer/copyright/options.php:79
#: inc/panel-builder/footer/menu/options.php:137
#: inc/panel-builder/footer/middle-row/options.php:454
#: inc/panel-builder/footer/options.php:53
#: inc/panel-builder/footer/socials/options.php:199
#: inc/panel-builder/footer/socials/options.php:217
#: inc/panel-builder/header/button/options.php:202
#: inc/panel-builder/header/button/options.php:259
#: inc/panel-builder/header/cart/options.php:89
#: inc/panel-builder/header/cart/options.php:116
#: inc/panel-builder/header/cart/options.php:1174
#: inc/panel-builder/header/logo/options.php:182
#: inc/panel-builder/header/logo/options.php:227
#: inc/panel-builder/header/logo/options.php:367
#: inc/panel-builder/header/middle-row/options.php:137
#: inc/panel-builder/header/mobile-menu/options.php:173
#: inc/panel-builder/header/search/options.php:86
#: inc/panel-builder/header/search/options.php:105
#: inc/panel-builder/header/search/options.php:894
#: inc/panel-builder/header/socials/options.php:153
#: inc/panel-builder/header/socials/options.php:178
#: inc/panel-builder/header/text/options.php:124
#: inc/panel-builder/header/text/options.php:453
#: inc/panel-builder/header/trigger/options.php:63
#: inc/panel-builder/header/trigger/options.php:119
#: inc/panel-builder/header/trigger/options.php:650
msgid "Mobile"
msgstr "Mobile"

#: inc/components/woocommerce/single/additional-actions-layer.php:101
#: inc/options/general/back-to-top.php:208
#: inc/options/general/page-title.php:316
#: inc/options/general/page-title.php:515
#: inc/options/general/page-title.php:815 inc/options/general/pagination.php:86
#: inc/options/general/pagination.php:105 inc/options/general/sidebar.php:246
#: inc/options/single-elements/author-box.php:224
#: inc/options/single-elements/featured-image.php:127
#: inc/options/single-elements/post-nav.php:121
#: inc/options/single-elements/post-nav.php:140
#: inc/options/single-elements/post-nav.php:157
#: inc/options/single-elements/post-share-box.php:430
#: inc/options/single-elements/post-tags.php:152
#: inc/options/single-elements/related-posts.php:608
#: inc/options/woocommerce/products-sorting.php:29
#: inc/options/woocommerce/related-upsells.php:113
#: inc/options/woocommerce/related-upsells.php:135
#: inc/options/woocommerce/results-count.php:35
#: inc/panel-builder/footer/copyright/options.php:78
#: inc/panel-builder/footer/menu/options.php:136
#: inc/panel-builder/footer/middle-row/options.php:453
#: inc/panel-builder/footer/options.php:52
#: inc/panel-builder/footer/socials/options.php:198
#: inc/panel-builder/footer/socials/options.php:216
#: inc/panel-builder/header/button/options.php:201
#: inc/panel-builder/header/button/options.php:258
#: inc/panel-builder/header/cart/options.php:88
#: inc/panel-builder/header/cart/options.php:115
#: inc/panel-builder/header/cart/options.php:1173
#: inc/panel-builder/header/logo/options.php:181
#: inc/panel-builder/header/logo/options.php:226
#: inc/panel-builder/header/logo/options.php:366
#: inc/panel-builder/header/middle-row/options.php:136
#: inc/panel-builder/header/mobile-menu/options.php:172
#: inc/panel-builder/header/search/options.php:85
#: inc/panel-builder/header/search/options.php:104
#: inc/panel-builder/header/search/options.php:893
#: inc/panel-builder/header/socials/options.php:152
#: inc/panel-builder/header/socials/options.php:177
#: inc/panel-builder/header/text/options.php:123
#: inc/panel-builder/header/text/options.php:452
#: inc/panel-builder/header/trigger/options.php:62
#: inc/panel-builder/header/trigger/options.php:118
#: inc/panel-builder/header/trigger/options.php:649
msgid "Tablet"
msgstr "Tablet"

#: inc/options/general/page-title.php:725
#: inc/options/woocommerce/single-product-gallery.php:87
#: inc/panel-builder/footer/middle-row/options.php:427
msgid "Container Width"
msgstr "Larghezza del contenitore"

#: inc/options/general/posts-listing.php:716
msgid "Card Inner Spacing"
msgstr "Spaziatura interna della scheda"

#: inc/options/general/posts-listing.php:1413
#: inc/options/woocommerce/card-product-elements.php:1184
msgid "Card Background Color"
msgstr "Colore di sfondo della scheda"

#: inc/options/general/posts-listing.php:913
msgid "Excerpt Color"
msgstr "Colore del riassunto"

#: inc/options/general/posts-listing.php:665
msgid "Cards Gap"
msgstr "Spazio tra le schede"

#: inc/options/general/meta.php:215 inc/options/general/page-title.php:403
#: inc/options/woocommerce/general/account-page.php:39
msgid "Avatar Size"
msgstr "Dimensione dell'avatar"

#: inc/options/general/meta.php:374
#: inc/options/woocommerce/card-product-elements.php:503
#: inc/panel-builder/footer/widget-area-1/options.php:111
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/typography/FontOptions.js:242
msgid "Underline"
msgstr "Sottolinea"

#: inc/components/single/single-helpers.php:414
#: inc/options/general/comments-single.php:15 inc/options/general/meta.php:270
#: inc/options/general/page-title.php:545
msgid "Comments"
msgstr "Commenti"

#: inc/classes/screen-manager.php:215 inc/options/customizer.php:118
#: inc/options/posts/categories.php:6 inc/options/posts/categories.php:11
msgid "Categories"
msgstr "Categorie"

#: inc/classes/screen-manager.php:216 inc/options/general/meta.php:202
#: inc/options/pages/author-page.php:13
msgid "Author"
msgstr "Autore"

#: inc/options/general/page-title.php:528
#: inc/options/general/posts-listing.php:539
#: inc/options/single-elements/related-posts.php:453
msgid "Post Meta"
msgstr "Metadati dell'articolo"

#: inc/options/general/posts-listing.php:461
#: inc/options/woocommerce/card-product-elements.php:546
msgid "Length"
msgstr "Lunghezza"

#: inc/options/general/page-title.php:110
#: inc/options/general/posts-listing.php:442
msgid "Excerpt"
msgstr "Riassunto"

#: inc/options/general/posts-listing.php:292
#: inc/options/single-elements/featured-image.php:28
#: inc/options/single-elements/related-posts.php:315
#: inc/options/woocommerce/card-product-elements.php:299
#: inc/options/woocommerce/general/cart-page.php:29
#: inc/options/woocommerce/single-product-gallery.php:101
#: inc/panel-builder/header/cart/options.php:708
msgid "Image Ratio"
msgstr "Rapporto dell'immagine"

#: inc/options/general/posts-listing.php:270
#: inc/options/single-elements/featured-image.php:11
#: inc/options/single-elements/related-posts.php:310
msgid "Featured Image"
msgstr "Immagine in evidenza"

#: inc/options/general/page-title.php:333
msgid "Heading tag"
msgstr "Tag dell'intestazione"

#: inc/options/general/posts-listing.php:88
#: inc/options/single-elements/related-posts.php:222
msgid "Card Elements"
msgstr "Elementi delle schede"

#: inc/options/general/posts-listing.php:67 inc/options/meta/default.php:112
#: inc/options/meta/page.php:97 inc/options/meta/post.php:93
#: inc/options/single-elements/structure.php:86
#: inc/panel-builder/footer/options.php:13
#: inc/panel-builder/header/middle-row/options.php:42
msgid "Boxed"
msgstr "Boxed"

#: inc/options/general/posts-listing.php:60
msgid "Card Type"
msgstr "Tipo di scheda"

#: inc/options/general/posts-listing.php:1665
#: inc/options/woocommerce/card-product-elements.php:202
msgid "Card Options"
msgstr "Opzioni delle schede"

#: inc/options/general/posts-listing.php:1522
msgid "Grid"
msgstr "Griglia"

#: inc/options/general/meta.php:438 inc/options/general/posts-listing.php:66
#: inc/options/general/posts-listing.php:495
#: inc/options/general/posts-listing.php:1512
#: inc/panel-builder/header/cart/options.php:992
#: inc/panel-builder/header/menu/options.php:643
#: inc/panel-builder/header/menu/options.php:673
#: inc/panel-builder/header/mobile-menu/options.php:84
#: inc/panel-builder/header/offcanvas/options.php:289
#: inc/panel-builder/header/search/options.php:723
#: inc/panel-builder/header/trigger/options.php:76
msgid "Simple"
msgstr "Semplice"

#. translators: placeholder here means the actual structure title.
#: inc/options/general/posts-listing.php:1490
msgid "Set the %s entries default structure."
msgstr "Imposta %s come struttura predefinita dei contenuti."

#. translators: placeholder here means the actual structure title.
#: inc/options/general/posts-listing.php:1485 inc/options/meta/default.php:37
#: inc/options/posts/custom-post-type-single.php:38
msgid "%s Structure"
msgstr "Struttura di %s"

#: inc/options/general/colors.php:767
msgid "Site Background"
msgstr "Sfondo del sito"

#: inc/options/general/pagination.php:226
#: inc/options/general/posts-listing.php:1202
#: inc/panel-builder/header/button/options.php:712
#: inc/panel-builder/header/button/options.php:741
#: inc/panel-builder/header/button/options.php:771
#: inc/panel-builder/header/button/options.php:799
msgid "Button Color"
msgstr "Colore del pulsante"

#: inc/options/general/colors.php:508
msgid "Global Colors"
msgstr "Colori globali"

#: inc/options/customizer.php:79 inc/options/general/form-elements.php:217
#: inc/options/general/pagination.php:145
msgid "Colors"
msgstr "Colori"

#: inc/options/general/form-elements.php:213
msgid "Radio & Checkbox"
msgstr "Radio e Checkbox"

#: inc/options/general/form-elements.php:238
#: inc/options/general/form-elements.php:285
#: inc/options/general/form-elements.php:315
#: inc/options/woocommerce/general/account-page.php:101
#: inc/options/woocommerce/general/account-page.php:131
#: inc/options/woocommerce/general/star-rating.php:36
#: inc/options/woocommerce/single-product-tabs.php:231
#: inc/options/woocommerce/single-product-tabs.php:287
#: inc/panel-builder/footer/menu/options.php:194
#: inc/panel-builder/header/menu/options.php:245
#: inc/panel-builder/header/menu/options.php:258
#: inc/panel-builder/header/menu/options.php:307
#: inc/panel-builder/header/menu/options.php:320
#: inc/panel-builder/header/menu/options.php:369
#: inc/panel-builder/header/menu/options.php:382
#: inc/panel-builder/header/menu/options.php:467
#: inc/panel-builder/header/menu/options.php:505
#: inc/panel-builder/header/menu/options.php:543
#: inc/panel-builder/header/menu/options.php:817
#: inc/panel-builder/header/menu/options.php:856
#: inc/panel-builder/header/menu/options.php:895
#: inc/panel-builder/header/mobile-menu/options.php:236
#: inc/panel-builder/header/mobile-menu/options.php:283
#: inc/panel-builder/header/mobile-menu/options.php:408
#: inc/panel-builder/header/mobile-menu/options.php:447
#: inc/panel-builder/header/mobile-menu/options.php:486
msgid "Active"
msgstr "Attivo"

#: inc/panel-builder/header/mobile-menu/options.php:252
msgid "Dropdown Font Color"
msgstr "Colore del font del menu a discesa"

#: inc/options/general/form-elements.php:60
msgid "Textarea Height"
msgstr "Altezza dell'area di testo"

#: inc/options/general/form-elements.php:70
msgid "Border Size"
msgstr "DImensione del bordo"

#: inc/options/general/buttons.php:80 inc/options/general/form-elements.php:181
#: inc/options/general/form-elements.php:293
#: inc/options/general/sidebar.php:385
#: inc/options/single-elements/author-box.php:454
#: inc/options/single-elements/post-share-box.php:600
#: inc/options/woocommerce/card-product-elements.php:126
#: inc/options/woocommerce/card-product-elements.php:1095
#: inc/options/woocommerce/general/messages.php:53
#: inc/options/woocommerce/general/messages.php:169
#: inc/options/woocommerce/general/messages.php:283
#: inc/panel-builder/header/cart/options.php:799
#: inc/panel-builder/header/cart/options.php:1075
#: inc/panel-builder/header/menu/options.php:400
#: inc/panel-builder/header/menu/options.php:441
#: inc/panel-builder/header/menu/options.php:480
#: inc/panel-builder/header/menu/options.php:518
#: inc/panel-builder/header/offcanvas/options.php:387
#: inc/panel-builder/header/search/options.php:818
#: inc/panel-builder/header/trigger/options.php:446
#: inc/panel-builder/header/trigger/options.php:488
#: inc/panel-builder/header/trigger/options.php:528
#: inc/panel-builder/header/trigger/options.php:566
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/background/BackgroundModal.js:144
msgid "Background Color"
msgstr "Colore di sfondo"

#: inc/options/general/form-elements.php:135
#: inc/options/general/form-elements.php:169
#: inc/options/general/form-elements.php:202
#: inc/panel-builder/header/search/options.php:603
#: inc/panel-builder/header/search/options.php:633
msgid "Focus"
msgstr "Focus"

#: inc/options/general/form-elements.php:38
msgid "Modern"
msgstr "Moderno"

#: inc/options/general/form-elements.php:33
#: inc/options/general/posts-listing.php:1517
msgid "Classic"
msgstr "Classico"

#: inc/options/general/form-elements.php:148
#: inc/options/single-elements/author-box.php:421
#: inc/options/woocommerce/single-product-tabs.php:245
#: inc/panel-builder/header/cart/options.php:1035
#: inc/panel-builder/header/offcanvas/options.php:346
#: inc/panel-builder/header/search/options.php:779
#: inc/panel-builder/header/trigger/options.php:442
#: inc/panel-builder/header/trigger/options.php:484
#: inc/panel-builder/header/trigger/options.php:524
#: inc/panel-builder/header/trigger/options.php:562
msgid "Border Color"
msgstr "Colore del bordo"

#: inc/options/general/back-to-top.php:244
#: inc/options/general/back-to-top.php:274 inc/options/general/buttons.php:73
#: inc/options/general/buttons.php:103 inc/options/general/colors.php:556
#: inc/options/general/comments-single.php:168
#: inc/options/general/comments-single.php:198
#: inc/options/general/page-title.php:966
#: inc/options/general/page-title.php:1002
#: inc/options/general/page-title.php:1033
#: inc/options/general/page-title.php:1130
#: inc/options/general/page-title.php:1161
#: inc/options/general/pagination.php:218
#: inc/options/general/pagination.php:248
#: inc/options/general/posts-listing.php:884
#: inc/options/general/posts-listing.php:979
#: inc/options/general/posts-listing.php:1015
#: inc/options/general/posts-listing.php:1046
#: inc/options/general/posts-listing.php:1091
#: inc/options/general/posts-listing.php:1137
#: inc/options/general/posts-listing.php:1183
#: inc/options/general/posts-listing.php:1225
#: inc/options/integrations/tutorlms-single.php:75
#: inc/options/integrations/tutorlms-single.php:120
#: inc/options/single-elements/author-box.php:370
#: inc/options/single-elements/author-box.php:403
#: inc/options/single-elements/post-nav.php:191
#: inc/options/single-elements/post-nav.php:211
#: inc/options/single-elements/post-share-box.php:535
#: inc/options/single-elements/post-share-box.php:593
#: inc/options/single-elements/post-share-box.php:622
#: inc/options/single-elements/related-posts.php:743
#: inc/options/single-elements/related-posts.php:794
#: inc/options/single-elements/related-posts.php:834
#: inc/options/single-elements/related-posts.php:865
#: inc/options/woocommerce/card-product-elements.php:779
#: inc/options/woocommerce/card-product-elements.php:924
#: inc/options/woocommerce/card-product-elements.php:962
#: inc/options/woocommerce/card-product-elements.php:994
#: inc/options/woocommerce/card-product-elements.php:1087
#: inc/options/woocommerce/card-product-elements.php:1118
#: inc/options/woocommerce/card-product-elements.php:1161
#: inc/options/woocommerce/general/messages.php:46
#: inc/options/woocommerce/general/messages.php:95
#: inc/options/woocommerce/general/messages.php:125
#: inc/options/woocommerce/general/messages.php:161
#: inc/options/woocommerce/general/messages.php:211
#: inc/options/woocommerce/general/messages.php:241
#: inc/options/woocommerce/general/messages.php:276
#: inc/options/woocommerce/general/messages.php:324
#: inc/options/woocommerce/general/messages.php:352
#: inc/options/woocommerce/general/quantity-input.php:89
#: inc/options/woocommerce/general/quantity-input.php:130
#: inc/options/woocommerce/single-product-elements.php:302
#: inc/options/woocommerce/single-product-elements.php:346
#: inc/options/woocommerce/single-product-elements.php:383
#: inc/options/woocommerce/single-product-elements.php:414
#: inc/options/woocommerce/single-product-elements.php:456
#: inc/options/woocommerce/single-product-elements.php:487
#: inc/options/woocommerce/single-product-gallery.php:170
#: inc/options/woocommerce/single-product-gallery.php:200
#: inc/options/woocommerce/single-product-gallery.php:236
#: inc/options/woocommerce/single-product-gallery.php:266
#: inc/options/woocommerce/single-product-tabs.php:224
#: inc/panel-builder/footer/menu/options.php:188
#: inc/panel-builder/footer/socials/options.php:279
#: inc/panel-builder/footer/socials/options.php:318
#: inc/panel-builder/footer/socials/options.php:366
#: inc/panel-builder/header/button/options.php:386
#: inc/panel-builder/header/button/options.php:399
#: inc/panel-builder/header/button/options.php:437
#: inc/panel-builder/header/button/options.php:449
#: inc/panel-builder/header/button/options.php:487
#: inc/panel-builder/header/button/options.php:499
#: inc/panel-builder/header/button/options.php:585
#: inc/panel-builder/header/button/options.php:597
#: inc/panel-builder/header/button/options.php:635
#: inc/panel-builder/header/button/options.php:647
#: inc/panel-builder/header/button/options.php:685
#: inc/panel-builder/header/button/options.php:697
#: inc/panel-builder/header/button/options.php:763
#: inc/panel-builder/header/button/options.php:792
#: inc/panel-builder/header/button/options.php:820
#: inc/panel-builder/header/cart/options.php:249
#: inc/panel-builder/header/cart/options.php:279
#: inc/panel-builder/header/cart/options.php:308
#: inc/panel-builder/header/cart/options.php:374
#: inc/panel-builder/header/cart/options.php:404
#: inc/panel-builder/header/cart/options.php:433
#: inc/panel-builder/header/cart/options.php:1023
#: inc/panel-builder/header/cart/options.php:1059
#: inc/panel-builder/header/cart/options.php:1099
#: inc/panel-builder/header/logo/options.php:476
#: inc/panel-builder/header/logo/options.php:507
#: inc/panel-builder/header/logo/options.php:537
#: inc/panel-builder/header/menu/options.php:238
#: inc/panel-builder/header/menu/options.php:252
#: inc/panel-builder/header/menu/options.php:301
#: inc/panel-builder/header/menu/options.php:314
#: inc/panel-builder/header/menu/options.php:363
#: inc/panel-builder/header/menu/options.php:376
#: inc/panel-builder/header/menu/options.php:461
#: inc/panel-builder/header/menu/options.php:500
#: inc/panel-builder/header/menu/options.php:538
#: inc/panel-builder/header/menu/options.php:605
#: inc/panel-builder/header/menu/options.php:811
#: inc/panel-builder/header/menu/options.php:851
#: inc/panel-builder/header/menu/options.php:890
#: inc/panel-builder/header/mobile-menu/options.php:230
#: inc/panel-builder/header/mobile-menu/options.php:277
#: inc/panel-builder/header/mobile-menu/options.php:402
#: inc/panel-builder/header/mobile-menu/options.php:442
#: inc/panel-builder/header/mobile-menu/options.php:481
#: inc/panel-builder/header/offcanvas/options.php:333
#: inc/panel-builder/header/offcanvas/options.php:371
#: inc/panel-builder/header/offcanvas/options.php:412
#: inc/panel-builder/header/search/options.php:230
#: inc/panel-builder/header/search/options.php:260
#: inc/panel-builder/header/search/options.php:289
#: inc/panel-builder/header/search/options.php:356
#: inc/panel-builder/header/search/options.php:387
#: inc/panel-builder/header/search/options.php:417
#: inc/panel-builder/header/search/options.php:573
#: inc/panel-builder/header/search/options.php:663
#: inc/panel-builder/header/search/options.php:692
#: inc/panel-builder/header/search/options.php:766
#: inc/panel-builder/header/search/options.php:802
#: inc/panel-builder/header/search/options.php:841
#: inc/panel-builder/header/socials/options.php:273
#: inc/panel-builder/header/socials/options.php:303
#: inc/panel-builder/header/socials/options.php:332
#: inc/panel-builder/header/socials/options.php:402
#: inc/panel-builder/header/socials/options.php:434
#: inc/panel-builder/header/socials/options.php:465
#: inc/panel-builder/header/socials/options.php:554
#: inc/panel-builder/header/socials/options.php:593
#: inc/panel-builder/header/socials/options.php:632
#: inc/panel-builder/header/trigger/options.php:244
#: inc/panel-builder/header/trigger/options.php:274
#: inc/panel-builder/header/trigger/options.php:303
#: inc/panel-builder/header/trigger/options.php:371
#: inc/panel-builder/header/trigger/options.php:401
#: inc/panel-builder/header/trigger/options.php:430
#: inc/panel-builder/header/trigger/options.php:514
#: inc/panel-builder/header/trigger/options.php:553
#: inc/panel-builder/header/trigger/options.php:591
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/ct-border.js:115
msgid "Hover"
msgstr "Hover"

#: inc/options/general/buttons.php:50 inc/options/general/form-elements.php:113
#: inc/options/general/form-elements.php:263
#: inc/options/general/pagination.php:196
#: inc/options/single-elements/post-nav.php:171
#: inc/options/woocommerce/card-product-elements.php:727
#: inc/options/woocommerce/card-product-elements.php:807
#: inc/options/woocommerce/card-product-elements.php:851
#: inc/options/woocommerce/card-product-elements.php:901
#: inc/options/woocommerce/card-product-elements.php:1025
#: inc/options/woocommerce/card-product-elements.php:1063
#: inc/options/woocommerce/results-count.php:61
#: inc/options/woocommerce/single-product-tabs.php:199
#: inc/panel-builder/footer/copyright/options.php:103
#: inc/panel-builder/footer/menu/options.php:162
#: inc/panel-builder/footer/socials/options.php:256
#: inc/panel-builder/footer/widget-area-1/options.php:62
#: inc/panel-builder/header/cart/options.php:226
#: inc/panel-builder/header/cart/options.php:257
#: inc/panel-builder/header/cart/options.php:286
#: inc/panel-builder/header/cart/options.php:740
#: inc/panel-builder/header/menu/options.php:175
#: inc/panel-builder/header/menu/options.php:204
#: inc/panel-builder/header/menu/options.php:267
#: inc/panel-builder/header/menu/options.php:329
#: inc/panel-builder/header/menu/options.php:754
#: inc/panel-builder/header/menu/options.php:784
#: inc/panel-builder/header/menu/options.php:825
#: inc/panel-builder/header/menu/options.php:864
#: inc/panel-builder/header/mobile-menu/options.php:205
#: inc/panel-builder/header/mobile-menu/options.php:347
#: inc/panel-builder/header/mobile-menu/options.php:376
#: inc/panel-builder/header/mobile-menu/options.php:416
#: inc/panel-builder/header/mobile-menu/options.php:455
#: inc/panel-builder/header/search/options.php:207
#: inc/panel-builder/header/search/options.php:238
#: inc/panel-builder/header/search/options.php:267
#: inc/panel-builder/header/search/options.php:550
#: inc/panel-builder/header/socials/options.php:250
#: inc/panel-builder/header/socials/options.php:281
#: inc/panel-builder/header/socials/options.php:310
#: inc/panel-builder/header/text/options.php:166
#: inc/panel-builder/header/text/options.php:195
#: inc/panel-builder/header/text/options.php:237
#: inc/panel-builder/header/text/options.php:276
#: inc/panel-builder/header/trigger/options.php:221
#: inc/panel-builder/header/trigger/options.php:252
#: inc/panel-builder/header/trigger/options.php:281
msgid "Font Color"
msgstr "Colore del font"

#: inc/options/general/back-to-top.php:217
#: inc/options/general/breadcrumbs.php:269 inc/options/general/buttons.php:45
#: inc/options/general/comments-single.php:141
#: inc/options/general/form-elements.php:101
#: inc/options/general/page-title.php:1309
#: inc/options/general/page-title.php:1411
#: inc/options/general/pagination.php:133
#: inc/options/general/posts-listing.php:812
#: inc/options/general/posts-listing.php:1710
#: inc/options/general/sidebar.php:269
#: inc/options/integrations/the-events-calendar/archive.php:37
#: inc/options/integrations/the-events-calendar/single.php:39
#: inc/options/integrations/tutorlms-archive.php:40
#: inc/options/integrations/tutorlms-single.php:220
#: inc/options/meta/default.php:176 inc/options/meta/page.php:160
#: inc/options/meta/post.php:159 inc/options/pages/page.php:35
#: inc/options/posts/custom-post-type-single.php:56
#: inc/options/posts/post.php:27 inc/options/single-elements/author-box.php:233
#: inc/options/single-elements/featured-image.php:138
#: inc/options/single-elements/post-nav.php:166
#: inc/options/single-elements/post-share-box.php:639
#: inc/options/single-elements/post-tags.php:161
#: inc/options/single-elements/related-posts.php:617
#: inc/options/woocommerce/archive-main.php:167
#: inc/options/woocommerce/card-product-elements.php:701
#: inc/options/woocommerce/general/account-page.php:74
#: inc/options/woocommerce/general/cart-page.php:58
#: inc/options/woocommerce/general/checkout-page.php:200
#: inc/options/woocommerce/general/product-badges.php:182
#: inc/options/woocommerce/general/quantity-input.php:61
#: inc/options/woocommerce/general/store-notice.php:56
#: inc/options/woocommerce/related-upsells.php:144
#: inc/options/woocommerce/results-count.php:44
#: inc/options/woocommerce/single-main.php:62
#: inc/options/woocommerce/single-product-elements.php:129
#: inc/options/woocommerce/single-product-gallery.php:143
#: inc/options/woocommerce/single-product-gallery.php:368
#: inc/options/woocommerce/single-product-tabs.php:182
#: inc/panel-builder/footer/copyright/options.php:87
#: inc/panel-builder/footer/menu/options.php:145
#: inc/panel-builder/footer/middle-row/options.php:462
#: inc/panel-builder/footer/socials/options.php:225
#: inc/panel-builder/footer/widget-area-1/options.php:57
#: inc/panel-builder/header/button/options.php:312
#: inc/panel-builder/header/cart/options.php:172
#: inc/panel-builder/header/cart/options.php:730
#: inc/panel-builder/header/logo/options.php:402
#: inc/panel-builder/header/menu/options.php:152
#: inc/panel-builder/header/menu/options.php:733
#: inc/panel-builder/header/middle-row/options.php:148
#: inc/panel-builder/header/mobile-menu/options.php:185
#: inc/panel-builder/header/offcanvas-logo/options.php:71
#: inc/panel-builder/header/offcanvas/options.php:154
#: inc/panel-builder/header/search/options.php:152
#: inc/panel-builder/header/search/options.php:534
#: inc/panel-builder/header/socials/options.php:189
#: inc/panel-builder/header/text/options.php:150
#: inc/panel-builder/header/trigger/options.php:166
msgid "Design"
msgstr "Design"

#: inc/options/general/pagination.php:118
msgid "Pagination Top Spacing"
msgstr "Spaziatura superiore della paginazione"

#: inc/options/general/pagination.php:39
msgid "Infinite Scroll"
msgstr "Scroll infinito"

#: inc/components/pagination.php:109 inc/options/general/pagination.php:38
#: inc/options/general/pagination.php:61
msgid "Load More"
msgstr "Carica altro"

#: inc/options/general/pagination.php:37
msgid "Next/Prev"
msgstr "Succ/Prec"

#: inc/options/general/pagination.php:36
msgid "Standard"
msgstr "Standard"

#: inc/options/general/pagination.php:29
msgid "Pagination Type"
msgstr "Tipo di paginazione"

#: inc/options/customizer.php:55 inc/options/customizer.php:161
#: inc/options/general/back-to-top.php:26
#: inc/options/general/breadcrumbs.php:48 inc/options/general/buttons.php:19
#: inc/options/general/comments-single.php:25
#: inc/options/general/form-elements.php:19
#: inc/options/general/page-title.php:1303
#: inc/options/general/page-title.php:1384
#: inc/options/general/pagination.php:24
#: inc/options/general/posts-listing.php:29
#: inc/options/general/posts-listing.php:1496
#: inc/options/general/sidebar.php:17
#: inc/options/integrations/the-events-calendar/archive.php:26
#: inc/options/integrations/the-events-calendar/single.php:28
#: inc/options/integrations/tutorlms-archive.php:25
#: inc/options/integrations/tutorlms-single.php:208
#: inc/options/meta/default.php:43 inc/options/meta/page.php:27
#: inc/options/meta/post.php:24 inc/options/pages/page.php:24
#: inc/options/posts/custom-post-type-single.php:44
#: inc/options/posts/post.php:17 inc/options/single-elements/author-box.php:21
#: inc/options/single-elements/featured-image.php:21
#: inc/options/single-elements/post-nav.php:29
#: inc/options/single-elements/post-share-box.php:633
#: inc/options/single-elements/post-tags.php:68
#: inc/options/single-elements/related-posts.php:29
#: inc/options/woocommerce/archive-main.php:49
#: inc/options/woocommerce/card-product-elements.php:209
#: inc/options/woocommerce/general/account-page.php:19
#: inc/options/woocommerce/general/cart-page.php:19
#: inc/options/woocommerce/general/checkout-page.php:52
#: inc/options/woocommerce/general/product-badges.php:19
#: inc/options/woocommerce/general/quantity-input.php:19
#: inc/options/woocommerce/general/store-notice.php:24
#: inc/options/woocommerce/related-upsells.php:16
#: inc/options/woocommerce/results-count.php:16
#: inc/options/woocommerce/single-main.php:50
#: inc/options/woocommerce/single-product-elements.php:14
#: inc/options/woocommerce/single-product-gallery.php:10
#: inc/options/woocommerce/single-product-gallery.php:305
#: inc/options/woocommerce/single-product-tabs.php:16
#: inc/panel-builder/footer/copyright/options.php:5
#: inc/panel-builder/footer/menu/options.php:33
#: inc/panel-builder/footer/middle-row/options.php:23
#: inc/panel-builder/footer/socials/options.php:5
#: inc/panel-builder/footer/widget-area-1/options.php:9
#: inc/panel-builder/header/button/options.php:5
#: inc/panel-builder/header/cart/options.php:11
#: inc/panel-builder/header/cart/options.php:602
#: inc/panel-builder/header/logo/options.php:5
#: inc/panel-builder/header/menu/options.php:34
#: inc/panel-builder/header/menu/options.php:593
#: inc/panel-builder/header/middle-row/options.php:27
#: inc/panel-builder/header/mobile-menu/options.php:25
#: inc/panel-builder/header/offcanvas-logo/options.php:6
#: inc/panel-builder/header/offcanvas/options.php:5
#: inc/panel-builder/header/search/options.php:53
#: inc/panel-builder/header/search/options.php:443
#: inc/panel-builder/header/socials/options.php:5
#: inc/panel-builder/header/text/options.php:5
#: inc/panel-builder/header/trigger/options.php:5
msgid "General"
msgstr "Generale"

#: inc/options/general/back-to-top.php:239
#: inc/options/general/back-to-top.php:268 inc/options/general/buttons.php:68
#: inc/options/general/buttons.php:98 inc/options/general/colors.php:526
#: inc/options/general/colors.php:551 inc/options/general/colors.php:606
#: inc/options/general/colors.php:628 inc/options/general/colors.php:649
#: inc/options/general/colors.php:671 inc/options/general/colors.php:693
#: inc/options/general/colors.php:715 inc/options/general/colors.php:737
#: inc/options/general/colors.php:759
#: inc/options/general/comments-single.php:162
#: inc/options/general/comments-single.php:192
#: inc/options/general/form-elements.php:129
#: inc/options/general/form-elements.php:164
#: inc/options/general/form-elements.php:197
#: inc/options/general/form-elements.php:233
#: inc/options/general/form-elements.php:279
#: inc/options/general/form-elements.php:309 inc/options/general/general.php:65
#: inc/options/general/page-title.php:875
#: inc/options/general/page-title.php:960
#: inc/options/general/page-title.php:996
#: inc/options/general/page-title.php:1027
#: inc/options/general/page-title.php:1089
#: inc/options/general/page-title.php:1124
#: inc/options/general/page-title.php:1155
#: inc/options/general/pagination.php:212
#: inc/options/general/pagination.php:242
#: inc/options/general/posts-listing.php:854
#: inc/options/general/posts-listing.php:926
#: inc/options/general/posts-listing.php:973
#: inc/options/general/posts-listing.php:1009
#: inc/options/general/posts-listing.php:1040
#: inc/options/general/posts-listing.php:1085
#: inc/options/general/posts-listing.php:1131
#: inc/options/general/posts-listing.php:1177
#: inc/options/general/posts-listing.php:1219
#: inc/options/general/sidebar.php:297 inc/options/general/sidebar.php:398
#: inc/options/integrations/tutorlms-single.php:30
#: inc/options/integrations/tutorlms-single.php:69
#: inc/options/integrations/tutorlms-single.php:114
#: inc/options/integrations/tutorlms-single.php:154
#: inc/options/integrations/tutorlms-single.php:176
#: inc/options/single-elements/author-box.php:259
#: inc/options/single-elements/author-box.php:364
#: inc/options/single-elements/author-box.php:397
#: inc/options/single-elements/author-box.php:436
#: inc/options/single-elements/post-nav.php:186
#: inc/options/single-elements/post-share-box.php:465
#: inc/options/single-elements/post-share-box.php:529
#: inc/options/single-elements/post-share-box.php:588
#: inc/options/single-elements/post-share-box.php:617
#: inc/options/single-elements/post-tags.php:187
#: inc/options/single-elements/related-posts.php:640
#: inc/options/single-elements/related-posts.php:709
#: inc/options/single-elements/related-posts.php:788
#: inc/options/single-elements/related-posts.php:828
#: inc/options/single-elements/related-posts.php:859
#: inc/options/woocommerce/card-product-elements.php:102
#: inc/options/woocommerce/card-product-elements.php:113
#: inc/options/woocommerce/card-product-elements.php:149
#: inc/options/woocommerce/card-product-elements.php:160
#: inc/options/woocommerce/card-product-elements.php:745
#: inc/options/woocommerce/card-product-elements.php:821
#: inc/options/woocommerce/card-product-elements.php:865
#: inc/options/woocommerce/card-product-elements.php:919
#: inc/options/woocommerce/card-product-elements.php:956
#: inc/options/woocommerce/card-product-elements.php:988
#: inc/options/woocommerce/card-product-elements.php:1039
#: inc/options/woocommerce/card-product-elements.php:1081
#: inc/options/woocommerce/card-product-elements.php:1112
#: inc/options/woocommerce/card-product-elements.php:1156
#: inc/options/woocommerce/card-product-elements.php:1198
#: inc/options/woocommerce/general/account-page.php:95
#: inc/options/woocommerce/general/account-page.php:125
#: inc/options/woocommerce/general/account-page.php:151
#: inc/options/woocommerce/general/messages.php:41
#: inc/options/woocommerce/general/messages.php:66
#: inc/options/woocommerce/general/messages.php:89
#: inc/options/woocommerce/general/messages.php:119
#: inc/options/woocommerce/general/messages.php:155
#: inc/options/woocommerce/general/messages.php:182
#: inc/options/woocommerce/general/messages.php:205
#: inc/options/woocommerce/general/messages.php:235
#: inc/options/woocommerce/general/messages.php:271
#: inc/options/woocommerce/general/messages.php:296
#: inc/options/woocommerce/general/messages.php:319
#: inc/options/woocommerce/general/messages.php:347
#: inc/options/woocommerce/general/quantity-input.php:82
#: inc/options/woocommerce/general/quantity-input.php:117
#: inc/options/woocommerce/general/quantity-input.php:124
#: inc/options/woocommerce/general/store-notice.php:76
#: inc/options/woocommerce/general/store-notice.php:97
#: inc/options/woocommerce/related-upsells.php:171
#: inc/options/woocommerce/results-count.php:74
#: inc/options/woocommerce/single-product-elements.php:162
#: inc/options/woocommerce/single-product-elements.php:203
#: inc/options/woocommerce/single-product-elements.php:296
#: inc/options/woocommerce/single-product-elements.php:330
#: inc/options/woocommerce/single-product-elements.php:338
#: inc/options/woocommerce/single-product-elements.php:377
#: inc/options/woocommerce/single-product-elements.php:408
#: inc/options/woocommerce/single-product-elements.php:450
#: inc/options/woocommerce/single-product-elements.php:481
#: inc/options/woocommerce/single-product-elements.php:549
#: inc/options/woocommerce/single-product-gallery.php:164
#: inc/options/woocommerce/single-product-gallery.php:194
#: inc/options/woocommerce/single-product-gallery.php:230
#: inc/options/woocommerce/single-product-gallery.php:260
#: inc/options/woocommerce/single-product-tabs.php:219
#: inc/options/woocommerce/single-product-tabs.php:258
#: inc/panel-builder/footer/copyright/options.php:125
#: inc/panel-builder/footer/menu/options.php:183
#: inc/panel-builder/footer/middle-row/options.php:494
#: inc/panel-builder/footer/socials/options.php:273
#: inc/panel-builder/footer/socials/options.php:312
#: inc/panel-builder/footer/socials/options.php:361
#: inc/panel-builder/footer/widget-area-1/options.php:84
#: inc/panel-builder/header/button/options.php:379
#: inc/panel-builder/header/button/options.php:393
#: inc/panel-builder/header/button/options.php:431
#: inc/panel-builder/header/button/options.php:443
#: inc/panel-builder/header/button/options.php:481
#: inc/panel-builder/header/button/options.php:493
#: inc/panel-builder/header/button/options.php:579
#: inc/panel-builder/header/button/options.php:591
#: inc/panel-builder/header/button/options.php:629
#: inc/panel-builder/header/button/options.php:641
#: inc/panel-builder/header/button/options.php:679
#: inc/panel-builder/header/button/options.php:691
#: inc/panel-builder/header/button/options.php:757
#: inc/panel-builder/header/button/options.php:787
#: inc/panel-builder/header/button/options.php:815
#: inc/panel-builder/header/cart/options.php:243
#: inc/panel-builder/header/cart/options.php:274
#: inc/panel-builder/header/cart/options.php:303
#: inc/panel-builder/header/cart/options.php:369
#: inc/panel-builder/header/cart/options.php:399
#: inc/panel-builder/header/cart/options.php:428
#: inc/panel-builder/header/cart/options.php:792
#: inc/panel-builder/header/cart/options.php:813
#: inc/panel-builder/header/cart/options.php:1018
#: inc/panel-builder/header/cart/options.php:1053
#: inc/panel-builder/header/cart/options.php:1093
#: inc/panel-builder/header/logo/options.php:471
#: inc/panel-builder/header/logo/options.php:502
#: inc/panel-builder/header/logo/options.php:532
#: inc/panel-builder/header/logo/options.php:613
#: inc/panel-builder/header/logo/options.php:635
#: inc/panel-builder/header/logo/options.php:656
#: inc/panel-builder/header/menu/options.php:233
#: inc/panel-builder/header/menu/options.php:296
#: inc/panel-builder/header/menu/options.php:358
#: inc/panel-builder/header/menu/options.php:806
#: inc/panel-builder/header/menu/options.php:846
#: inc/panel-builder/header/menu/options.php:885
#: inc/panel-builder/header/menu/options.php:955
#: inc/panel-builder/header/menu/options.php:987
#: inc/panel-builder/header/menu/options.php:1017
#: inc/panel-builder/header/mobile-menu/options.php:225
#: inc/panel-builder/header/mobile-menu/options.php:272
#: inc/panel-builder/header/mobile-menu/options.php:397
#: inc/panel-builder/header/mobile-menu/options.php:437
#: inc/panel-builder/header/mobile-menu/options.php:476
#: inc/panel-builder/header/offcanvas/options.php:327
#: inc/panel-builder/header/offcanvas/options.php:365
#: inc/panel-builder/header/offcanvas/options.php:406
#: inc/panel-builder/header/search/options.php:224
#: inc/panel-builder/header/search/options.php:255
#: inc/panel-builder/header/search/options.php:284
#: inc/panel-builder/header/search/options.php:350
#: inc/panel-builder/header/search/options.php:382
#: inc/panel-builder/header/search/options.php:412
#: inc/panel-builder/header/search/options.php:568
#: inc/panel-builder/header/search/options.php:598
#: inc/panel-builder/header/search/options.php:628
#: inc/panel-builder/header/search/options.php:658
#: inc/panel-builder/header/search/options.php:687
#: inc/panel-builder/header/search/options.php:760
#: inc/panel-builder/header/search/options.php:796
#: inc/panel-builder/header/search/options.php:835
#: inc/panel-builder/header/socials/options.php:267
#: inc/panel-builder/header/socials/options.php:298
#: inc/panel-builder/header/socials/options.php:327
#: inc/panel-builder/header/socials/options.php:396
#: inc/panel-builder/header/socials/options.php:429
#: inc/panel-builder/header/socials/options.php:460
#: inc/panel-builder/header/socials/options.php:549
#: inc/panel-builder/header/socials/options.php:588
#: inc/panel-builder/header/socials/options.php:627
#: inc/panel-builder/header/trigger/options.php:238
#: inc/panel-builder/header/trigger/options.php:269
#: inc/panel-builder/header/trigger/options.php:298
#: inc/panel-builder/header/trigger/options.php:365
#: inc/panel-builder/header/trigger/options.php:396
#: inc/panel-builder/header/trigger/options.php:425
#: inc/panel-builder/header/trigger/options.php:508
#: inc/panel-builder/header/trigger/options.php:548
#: inc/panel-builder/header/trigger/options.php:586
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/background/BackgroundModal.js:154
#: static/js/options/options/background/ImagePicker.js:190
#: static/js/options/options/background/PatternPicker.js:170
#: static/js/options/options/ct-border.js:94
msgid "Initial"
msgstr "Iniziale"

#: inc/options/general/cards-reveal-effect.php:13
msgid "Cards Reveal Effect"
msgstr "Effetto rivela per le schede"

#: inc/options/general/performance.php:41
msgid "Lazy Load Images"
msgstr "Lazy Load delle immagini"

#: inc/options/general/page-title.php:19
#: inc/options/general/page-title.php:1388 inc/options/meta/page.php:8
#: inc/options/pages/author-page.php:26 inc/options/pages/page.php:99
#: inc/options/pages/search-page.php:30 inc/options/posts/blog.php:32
#: inc/options/posts/categories.php:19
#: inc/options/posts/custom-post-type-archive.php:30
#: inc/options/posts/custom-post-type-single.php:70
#: inc/options/posts/post.php:92 inc/options/woocommerce/archive-main.php:18
#: inc/options/woocommerce/single-main.php:19
msgid "Page Title"
msgstr "Titolo della pagina"

#: inc/options/general/sidebar-particular.php:44
#: inc/options/meta/default.php:79 inc/options/meta/page.php:64
#: inc/options/meta/post.php:60 inc/options/single-elements/structure.php:47
msgid "Right Sidebar"
msgstr "Barra laterale destra"

#: inc/options/general/sidebar-particular.php:39
#: inc/options/meta/default.php:74 inc/options/meta/page.php:59
#: inc/options/meta/post.php:55 inc/options/single-elements/structure.php:42
msgid "Left Sidebar"
msgstr "Barra laterale sinistra"

#: inc/options/meta/default.php:69 inc/options/meta/page.php:54
#: inc/options/meta/post.php:50 inc/options/single-elements/structure.php:37
msgid "Normal Width"
msgstr "Larghezza normale"

#: inc/options/meta/default.php:64 inc/options/meta/page.php:49
#: inc/options/meta/post.php:45 inc/options/single-elements/structure.php:32
msgid "Narrow Width"
msgstr "Larghezza stretta"

#: inc/options/meta/default.php:59 inc/options/meta/page.php:44
#: inc/options/meta/post.php:40
msgid "Inherit from customizer"
msgstr "Eredita dal personalizzatore"

#: inc/options/meta/page.php:23 inc/options/pages/page.php:19
#: inc/options/pages/page.php:121
#: inc/options/posts/custom-post-type-single.php:92
#: inc/options/posts/post.php:114 inc/options/woocommerce/single-main.php:46
msgid "Page Structure"
msgstr "Struttura della pagina"

#: inc/integrations/beaver-themer.php:8 inc/options/customizer.php:61
msgid "Header"
msgstr "Header"

#: inc/components/back-to-top.php:54
msgid "Go to top"
msgstr "Torna su"

#: inc/options/single-elements/post-share-box.php:104
msgid "LinkedIn"
msgstr "LinkedIn"

#: inc/options/general/page-title.php:329
#: inc/options/general/page-title.php:362
#: inc/options/general/posts-listing.php:221
#: inc/options/single-elements/related-posts.php:402
#: inc/options/woocommerce/card-product-elements.php:388
#: inc/options/woocommerce/card-product-elements.php:713
#: inc/options/woocommerce/single-product-layers.php:83
#: inc/options/woocommerce/single-product-layers.php:150
#: inc/options/woocommerce/single-product-layers.php:246
#: inc/options/woocommerce/single-product-layers.php:309
#: inc/options/woocommerce/single-product-layers.php:399
msgid "Title"
msgstr "Titolo"

#. Translators: %s is the theme name.
#: admin/helpers/meta-boxes.php:144
msgid "%s Settings"
msgstr "Impostazioni %s"

#: inc/components/pagination.php:39
msgid "No more posts to load"
msgstr "Non ci sono più articoli da caricare"

#: inc/options/single-elements/post-share-box.php:92
msgid "Facebook"
msgstr "Facebook"

#: inc/components/single/comments.php:136
msgid "Website"
msgstr "Sito web"

#: woocommerce/product-searchform.php:26
msgid "Search products&hellip;"
msgstr "Cerca prodotti&hellip;"

#: woocommerce/product-searchform.php:24
msgid "Search for:"
msgstr "Ricerca per:"

#: woocommerce/cart/cart.php:41 woocommerce/cart/cart.php:188
msgid "Quantity"
msgstr "Quantità"

#: woocommerce/cart/mini-cart.php:148
msgid "No products in the cart."
msgstr "Nessun prodotto nel carrello."

#: woocommerce/cart/cart.php:42 woocommerce/cart/cart.php:210
msgid "Subtotal"
msgstr "Subtotale"

#: searchform.php:10
msgctxt "placeholder"
msgid "Search"
msgstr "Cerca"

#: template-parts/404.php:11
msgid "It looks like nothing was found at this location. Maybe try to search for something else?"
msgstr "Sembra che non sia stato trovato nulla in questa posizione. Vuoi provare a cercare qualcos'altro?"

#: template-parts/404.php:7
msgid "Oops! That page can&rsquo;t be found."
msgstr "Oops! La pagina non è stata trovata."

#: admin/notices/templates.php:45
msgid "This way you will have access to custom extensions, demo templates and many other awesome features"
msgstr "In questo modo avrai accesso ad estensioni personalizzate, template delle demo e molte altre fantastiche funzionalità"

#: admin/notices/templates.php:43 admin/dashboard/static/bundle/main.js:6
#: admin/dashboard/static/js/screens/Home.js:459
msgid "Blocksy Companion"
msgstr "Blocksy Companion"

#: admin/dashboard/core.php:189
msgid "You do not have sufficient permissions to access this page."
msgstr "Non disponi di autorizzazioni sufficienti per accedere a questa pagina."

#: inc/options/general/page-title.php:1357
#: inc/options/general/page-title.php:1398
#: inc/options/general/posts-listing.php:1693 inc/options/meta/default.php:157
#: inc/options/meta/page.php:142 inc/options/meta/post.php:141
#: inc/options/single-elements/structure.php:114
#: inc/options/woocommerce/archive-main.php:150
#: static/bundle/customizer-controls.js:13 static/bundle/options.js:13
#: static/js/options/options/background/ImagePicker.js:151
msgid "Disabled"
msgstr "Disabilitato"

#: inc/components/archive/archive-card.php:257
#: inc/options/general/posts-listing.php:509
msgid "Read More"
msgstr "Leggi tutto"

#. Author URI of the theme
#: style.css
#, gp-priority: low
msgid "https://creativethemes.com"
msgstr "https://creativethemes.com"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "CreativeThemes"
msgstr "CreativeThemes"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://creativethemes.com/blocksy/"
msgstr "https://creativethemes.com/blocksy/"