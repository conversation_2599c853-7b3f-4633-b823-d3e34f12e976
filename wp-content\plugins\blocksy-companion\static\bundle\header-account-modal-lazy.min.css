/**
 *  - v2.1.2
 * 
 * Copyright (c) 2025
 * Licensed GPLv2+
 */

#account-modal{background-color:rgba(18,21,25,.6)}#account-modal .ct-panel-content{padding:35px}.ct-account-modal{display:flex;flex-direction:column;overflow:hidden;width:100%;margin:auto;font-size:14px;color:var(--theme-text-color);background-color:#fff;--theme-link-initial-color: var(--theme-text-color);border-radius:3px;box-shadow:var(--theme-box-shadow, 0px 0px 70px rgba(0, 0, 0, 0.35));--theme-content-spacing: 1em}@media(max-width: 479.98px){.ct-account-modal{max-width:88vw}}@media(min-width: 480px){.ct-account-modal{max-width:480px}}.ct-account-modal>ul{display:flex}.ct-account-modal>ul li{width:50%;display:flex;align-items:center;justify-content:center;position:relative;height:60px;font-size:12px;font-weight:700;text-transform:uppercase;letter-spacing:.03em;cursor:pointer;border-bottom:1px solid rgba(0,0,0,.1);transition:opacity .1s ease,background .1s ease}.ct-account-modal>ul li.active{border-bottom-color:rgba(0,0,0,0)}.ct-account-modal>ul li:not(.active){opacity:.5;background:rgba(0,0,0,.04)}.ct-account-modal>ul li:not(.active):hover{opacity:.8}.ct-account-modal>ul li:first-child{border-right:1px solid rgba(0,0,0,.1)}.ct-account-modal .ct-account-forms{overflow-y:auto}@media(max-width: 479.98px){.ct-account-modal .ct-account-forms{padding:40px 30px}}@media(min-width: 480px){.ct-account-modal .ct-account-forms{padding:60px 70px}}.ct-account-modal .ct-account-forms>*:not(.active){display:none}.ct-account-modal form>*{--theme-content-spacing: 20px}.ct-account-modal form>*:last-of-type{margin-bottom:0}.ct-account-modal label{display:inline-block;font-size:14px}.ct-account-modal .button,.ct-account-modal .ct-button{width:100%}.ct-account-modal .ct-button{gap:10px}.ct-account-modal .ct-button:not(.ct-loading) .ct-button-loader{display:none}.ct-account-modal .col-2{display:flex;justify-content:space-between}@media(max-width: 689.98px){.ct-account-modal .col-2{flex-direction:column;row-gap:10px}}.ct-account-modal #reg_passmail,.ct-account-modal .ct-back-to-login{text-align:center}.ct-account-modal .ct-back-to-login{display:block;margin-top:20px}.ct-account-modal .login-remember span{display:flex}.ct-account-modal .login-remember input{--top: 3px}.ct-account-modal [class*=ct-form-notification]{margin-bottom:25px;border-radius:3px;padding:10px 15px;background:rgba(0,0,0,.04)}.ct-account-modal [class*=ct-form-notification] a{color:inherit}.ct-account-modal [class*=ct-form-notification] a.showlogin{font-size:inherit;font-weight:inherit;padding:0;border:none;min-height:initial;background:rgba(0,0,0,0)}.ct-account-modal .ct-form-notification-error{color:rgba(255,255,255,.95);background:rgba(218,0,28,.7)}.ct-account-modal [class*=ct-form-notification] a:not(:hover),.ct-account-modal .woocommerce-privacy-policy-text a:not(:hover){text-decoration:underline}@media(prefers-reduced-motion: no-preference){.ct-account-modal.ct-error{animation:shake .82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;transform:translate3d(0, 0, 0);-webkit-backface-visibility:hidden;backface-visibility:hidden;perspective:1000px}}.ct-account-modal div.g-recaptcha,.ct-account-modal .anr_captcha_field{margin:var(--theme-content-spacing) 0 !important}#wfls-prompt-overlay-blocksy{margin-bottom:var(--theme-content-spacing)}#wfls-prompt-overlay-blocksy #wfls-prompt-wrapper input+label{margin-top:var(--theme-content-spacing)}#wfls-prompt-overlay-blocksy~*:not(.login-submit){display:none}.ct-account-modal [name=validate_2fa_form]+[name=loginform]{display:none}