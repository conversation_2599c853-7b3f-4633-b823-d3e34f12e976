/*! elementor - v3.30.0 - 01-07-2025 */
"use strict";(self.webpackChunkelementorFrontend=self.webpackChunkelementorFrontend||[]).push([[234],{9754:(e,r,s)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0,s(4846),s(6211),s(9655);class Progress extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{progressNumber:".elementor-progress-bar"}}}getDefaultElements(){const e=this.getSettings("selectors");return{$progressNumber:this.$element.find(e.progressNumber)}}onInit(){super.onInit();this.createObserver().observe(this.elements.$progressNumber[0])}createObserver(){return new IntersectionObserver((e=>{e.forEach((e=>{if(e.isIntersecting){const e=this.elements.$progressNumber;e.css("width",e.data("max")+"%")}}))}),{root:null,threshold:0,rootMargin:"0px"})}}r.default=Progress}}]);