/*! For license information please see editor-elements.js.LICENSE.txt */
!function(){"use strict";var e={"@elementor/editor-props":function(e){e.exports=window.elementorV2.editorProps},"@elementor/editor-styles":function(e){e.exports=window.elementorV2.editorStyles},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/utils":function(e){e.exports=window.elementorV2.utils}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var s=t[r]={exports:{}};return e[r](s,s.exports,n),s.exports}n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};!function(){n.r(r),n.d(r,{ELEMENT_STYLE_CHANGE_EVENT:function(){return T},createElementStyle:function(){return O},deleteElementStyle:function(){return V},getAnchoredAncestorId:function(){return A},getAnchoredDescendantId:function(){return P},getContainer:function(){return l},getCurrentDocumentId:function(){return b},getElementLabel:function(){return h},getElementSetting:function(){return u},getElementStyles:function(){return g},getElements:function(){return S},getLinkInLinkRestriction:function(){return L},getSelectedElements:function(){return f},getWidgetsCache:function(){return m},isElementAnchored:function(){return R},selectElement:function(){return i},styleRerenderEvents:function(){return C},updateElementSettings:function(){return I},updateElementStyle:function(){return j},useElementSetting:function(){return c},useElementSettings:function(){return d},useElementType:function(){return a},useParentElement:function(){return E},useSelectedElement:function(){return p}});var e=n("@elementor/editor-v1-adapters"),t=n("@elementor/utils"),o=n("@elementor/editor-props"),s=n("@elementor/editor-styles");function l(e){const t=window,n=t.elementor?.getContainer?.(e);return n??null}var i=t=>{try{const n=l(t);(0,e.__privateRunCommand)("document/elements/select",{container:n})}catch{}},u=(e,t)=>{const n=l(e),r=n?.settings?.get(t);return r??null},c=(t,n)=>(0,e.__privateUseListenTo)((0,e.commandEndEvent)("document/elements/set-settings"),(()=>u(t,n)),[t,n]),d=(t,n)=>(0,e.__privateUseListenTo)((0,e.commandEndEvent)("document/elements/set-settings"),(()=>n.reduce(((e,n)=>{const r=u(t,n);return null!==r&&(e[n]=r),e}),{})),[t,...n]);function m(){const e=window;return e?.elementor?.widgetsCache||null}function a(t){return(0,e.__privateUseListenTo)((0,e.commandEndEvent)("editor/documents/load"),(()=>{if(!t)return null;const e=m(),n=e?.[t];return n?.atomic_controls&&n?.atomic_props_schema?{key:t,controls:n.atomic_controls,propsSchema:n.atomic_props_schema,title:n.title}:null}),[t])}function f(){const e=window;return(e.elementor?.selection?.getElements?.()??[]).reduce(((e,t)=>{const n=t.model.get("widgetType")||t.model.get("elType");return n&&e.push({id:t.model.get("id"),type:n}),e}),[])}function p(){const t=(0,e.__privateUseListenTo)([(0,e.commandEndEvent)("document/elements/select"),(0,e.commandEndEvent)("document/elements/deselect"),(0,e.commandEndEvent)("document/elements/select-all"),(0,e.commandEndEvent)("document/elements/deselect-all")],f),[n]=t,r=a(n?.type);return 1===t.length&&r?{element:n,elementType:r}:{element:null,elementType:null}}function E(t){return(0,e.__privateUseListenTo)([(0,e.commandEndEvent)("document/elements/create")],(()=>{if(!t)return null;const e=window,n=e?.elementor?.getContainer?.(t);return n?n.parent:null}),[t])}var g=e=>{const t=l(e);return t?.model.get("styles")||null},y=(0,t.createError)({code:"element_not_found",message:"Element not found."}),w=(0,t.createError)({code:"style_not_found",message:"Style not found."}),v=(0,t.createError)({code:"element_type_not_exists",message:"Element type does not exist."}),_=(0,t.createError)({code:"element_label_not_exists",message:"Element label does not exist."});function h(e){const t=l(e),n=t?.model.get("widgetType")||t?.model.get("elType");if(!n)throw new v({context:{elementId:e}});const r=m()?.[n]?.title;if(!r)throw new _({context:{elementType:n}});return r}function S(e){const t=e?l(e):function(){const e=window;return e.elementor?.documents?.getCurrent?.()?.container??null}();if(!t)return[];const n=[...t.model.get("elements")??[]].flatMap((e=>S(e.get("id"))));return[t,...n]}function b(){const e=window;return e.elementor?.documents?.getCurrentId?.()??null}var I=({id:t,props:n,withHistory:r=!0})=>{const o={container:l(t),settings:{...n}};r?(0,e.__privateRunCommandSync)("document/elements/settings",o):(0,e.__privateRunCommandSync)("document/elements/set-settings",o,{internal:!0})},T="elementor/editor-v2/editor-elements/style",C=[(0,e.commandEndEvent)("document/elements/create"),(0,e.commandEndEvent)("document/elements/duplicate"),(0,e.commandEndEvent)("document/elements/import"),(0,e.commandEndEvent)("document/elements/paste"),(0,e.windowEvent)(T)];function x(t,n){const r=l(t);if(!r)throw new y({context:{elementId:t}});const s=Object.keys(r.model.get("styles")??{}),i=function(e,t){const n=structuredClone(e.model.get("styles"))??{},r=Object.entries(t(n)).map((([e,t])=>(t.variants=function(e){return e.variants.filter((({props:e})=>Object.keys(e).length>0))}(t),[e,t]))).filter((([,e])=>!function(e){return 0===e.variants.length}(e))),o=Object.fromEntries(r);return e.model.set("styles",o),o}(r,n);return function(e,{oldIds:t,newIds:n}){const r=t.filter((e=>!n.includes(e))),s=structuredClone(function(e){return Object.entries(e.settings.toJSON()).filter((e=>{const[,t]=e;return o.classesPropTypeUtil.isValid(t)}))}(e));s.forEach((([,e])=>{e.value=e.value.filter((e=>!r.includes(e)))})),I({id:e.id,props:Object.fromEntries(s),withHistory:!1})}(r,{oldIds:s,newIds:Object.keys(i)}),window.dispatchEvent(new CustomEvent(T)),(0,e.__privateRunCommandSync)("document/save/set-is-modified",{status:!0},{internal:!0}),i}function O({styleId:e,elementId:t,classesProp:n,label:r,meta:l,props:i,additionalVariants:c=[]}){let d=e;return x(t,(e=>{d??=(0,s.generateId)(`e-${t}-`,Object.keys(e));const m=[{meta:l,props:i},...c];return e[d]={id:d,label:r,type:"class",variants:m},function(e,t,n){const r=u(e,t),s=o.classesPropTypeUtil.create((e=>[...e??[],n]),{base:r});I({id:e,props:{[t]:s},withHistory:!1})}(t,n,d),e})),d}function j(e){x(e.elementId,(t=>{const n=t[e.styleId];if(!n)throw new w({context:{styleId:e.styleId}});const r=(0,s.getVariantByMeta)(n,e.meta);return r?r.props=(0,o.mergeProps)(r.props,e.props):n.variants.push({meta:e.meta,props:e.props}),t}))}function V(e,t){x(e,(e=>(delete e[t],e)))}function L(e){const t=P(e);if(t)return{shouldRestrict:!0,reason:"descendant",elementId:t};const n=A(e);return n?{shouldRestrict:!0,reason:"ancestor",elementId:n}:{shouldRestrict:!1}}function P(e){const t=N(e);if(!t)return null;for(const n of Array.from(t.querySelectorAll("a"))){const t=U(n);if(t!==e)return t}return null}function A(e){const t=N(e);if(!t||null===t.parentElement)return null;const n=t.parentElement.closest("a");return n?U(n):null}function R(e){const t=N(e);return!!t&&(!!M(t.tagName)||k(t))}function k(e){for(const t of e.children)if(!H(t)){if(M(t.tagName))return!0;if(k(t))return!0}return!1}function U(e){return e.closest("[data-id]")?.dataset.id||null}function N(e){try{return l(e)?.view?.el||null}catch{return null}}function M(e){return"a"===e.toLowerCase()}function H(e){return e.hasAttribute("data-id")}}(),(window.elementorV2=window.elementorV2||{}).editorElements=r}(),window.elementorV2.editorElements?.init?.();