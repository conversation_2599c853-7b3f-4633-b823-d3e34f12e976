!function(){"use strict";var r={d:function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o:function(r,e){return Object.prototype.hasOwnProperty.call(r,e)},r:function(r){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})}},e={};r.r(e),r.d(e,{CLASSES_PROP_KEY:function(){return s},backgroundColorOverlayPropTypeUtil:function(){return j},backgroundGradientOverlayPropTypeUtil:function(){return w},backgroundImageOverlayPropTypeUtil:function(){return k},backgroundImagePositionOffsetPropTypeUtil:function(){return E},backgroundImageSizeScalePropTypeUtil:function(){return A},backgroundOverlayPropTypeUtil:function(){return S},backgroundPropTypeUtil:function(){return U},blurFilterPropTypeUtil:function(){return C},booleanPropTypeUtil:function(){return $},borderRadiusPropTypeUtil:function(){return c},borderWidthPropTypeUtil:function(){return l},boxShadowPropTypeUtil:function(){return u},brightnessFilterPropTypeUtil:function(){return I},classesPropTypeUtil:function(){return p},colorPropTypeUtil:function(){return f},colorStopPropTypeUtil:function(){return x},createArrayPropUtils:function(){return o},createPropUtils:function(){return n},dimensionsPropTypeUtil:function(){return m},evaluateTerm:function(){return L},filterEmptyValues:function(){return H},filterPropTypeUtil:function(){return R},gradientColorStopPropTypeUtil:function(){return V},imageAttachmentIdPropType:function(){return y},imagePropTypeUtil:function(){return b},imageSrcPropTypeUtil:function(){return d},isEmpty:function(){return Q},isTransformable:function(){return Y},keyValuePropTypeUtil:function(){return q},layoutDirectionPropTypeUtil:function(){return v},linkPropTypeUtil:function(){return O},mergeProps:function(){return J},moveTransformPropTypeUtil:function(){return F},numberPropTypeUtil:function(){return z},positionPropTypeUtil:function(){return _},shadowPropTypeUtil:function(){return a},shouldApplyEffect:function(){return K},sizePropTypeUtil:function(){return g},stringPropTypeUtil:function(){return h},strokePropTypeUtil:function(){return P},transformPropTypeUtil:function(){return G},urlPropTypeUtil:function(){return T}});var t=window.elementorV2.schema;function n(r,e){const n=t.z.strictObject({$$type:t.z.literal(r),value:e,disabled:t.z.boolean().optional()});function o(r){return n.safeParse(r).success}return{extract:function(r){return o(r)?r.value:null},isValid:o,create:function(e,t){const n="function"==typeof e?e:()=>e,{base:i,disabled:a}=t||{};if(!i)return{$$type:r,value:n(),...a&&{disabled:a}};if(!o(i))throw new Error(`Cannot create prop based on invalid value: ${JSON.stringify(i)}`);return{$$type:r,value:n(i.value),...a&&{disabled:a}}},schema:n,key:r}}function o(r,e){return n(`${r}-array`,t.z.array(e))}var i=t.z.any().nullable(),a=n("shadow",t.z.strictObject({position:i,hOffset:i,vOffset:i,blur:i,spread:i,color:i})),u=n("box-shadow",t.z.array(a.schema)),c=n("border-radius",t.z.strictObject({"start-start":i,"start-end":i,"end-start":i,"end-end":i})),l=n("border-width",t.z.strictObject({"block-start":i,"block-end":i,"inline-start":i,"inline-end":i})),s="classes",p=n(s,t.z.array(t.z.string().regex(/^[a-z][a-z-_0-9]*$/i))),f=n("color",t.z.string()),b=n("image",t.z.strictObject({src:i,size:i})),y=n("image-attachment-id",t.z.number()),d=n("image-src",t.z.strictObject({id:i,url:t.z.null()}).or(t.z.strictObject({id:t.z.null(),url:i}))),m=n("dimensions",t.z.strictObject({"block-start":i,"block-end":i,"inline-start":i,"inline-end":i})),z=n("number",t.z.number().nullable()),g=n("size",t.z.strictObject({unit:t.z.enum(["px","em","rem","%","vw","vh"]),size:t.z.number()}).or(t.z.strictObject({unit:t.z.literal("auto"),size:t.z.literal("")})).or(t.z.strictObject({unit:t.z.literal("custom"),size:t.z.string()}))),h=n("string",t.z.string().nullable()),P=n("stroke",t.z.strictObject({color:i,width:i})),T=n("url",t.z.string().nullable()),v=n("layout-direction",t.z.object({row:t.z.any(),column:t.z.any()})),O=n("link",t.z.strictObject({destination:i,label:i,isTargetBlank:i})),U=n("background",t.z.strictObject({color:i,"background-overlay":i})),j=n("background-color-overlay",i),w=n("background-gradient-overlay",i),k=n("background-image-overlay",i),N=j.schema.or(w.schema).or(k.schema),S=n("background-overlay",t.z.array(N)),E=n("background-image-position-offset",i),A=n("background-image-size-scale",i),$=n("boolean",t.z.boolean().nullable()),x=n("color-stop",t.z.strictObject({color:i,offset:i})),V=n("gradient-color-stop",t.z.array(x.schema)),q=n("key-value",t.z.strictObject({key:i,value:i})),_=n("object-position",t.z.strictObject({x:g.schema.nullable(),y:g.schema.nullable()})),C=n("blur",i),I=n("brightness",i),M=C.schema.or(I.schema),R=n("filter",t.z.array(M)),F=n("transform-move",t.z.strictObject({x:g.schema.nullable(),y:g.schema.nullable(),z:g.schema.nullable()})),B=n("scale",t.z.strictObject({x:g.schema.nullable(),y:g.schema.nullable(),z:g.schema.nullable()})),D=F.schema.or(B.schema),G=n("transform",t.z.array(D));function J(r,e){const t=structuredClone(r);return Object.entries(e).forEach((([r,e])=>{null==e?delete t[r]:t[r]=e})),t}function K({relation:r,terms:e},t){if(!e.length)return!1;const n=function(r){switch(r){case"or":return"some";case"and":return"every";default:throw new Error(`Relation not supported ${r}`)}}(r);return e[n]((r=>{return function(r){return"relation"in r}(r)?K(r,t):L(r,(e=r.path,n=t,e.reduce(((r,e)=>"object"==typeof r&&null!==r&&e in r?r[e]?.value:null),n)));var e,n}))}function L(r,e){const{value:t,operator:n}=r;switch(n){case"eq":case"ne":return e===t==("eq"===n);case"gt":case"lte":if(isNaN(Number(e))||isNaN(Number(t)))throw new Error("Mathematical comparison requires numeric values.");return Number(e)>Number(t)==("gt"===n);case"lt":case"gte":if(isNaN(Number(e))||isNaN(Number(t)))throw new Error("Mathematical comparison requires numeric values.");return Number(e)<Number(t)==("lt"===n);case"in":case"nin":if(!Array.isArray(t))throw new Error('The "in" and "nin" operators require an array for comparison.');return t.includes(e)===("in"===n);case"contains":case"ncontains":if(("string"!=typeof e||"string"!=typeof t)&&!Array.isArray(e))throw new Error('The "contains" and "ncontains" operators require a string or an array for comparison.');return"contains"===n===e.includes(t);case"exists":case"not_exist":return"exists"===n==(!!e||0===e||!1===e);default:return!1}}var W=t.z.object({$$type:t.z.string(),value:t.z.any(),disabled:t.z.boolean().optional()}),Y=r=>W.safeParse(r).success,H=r=>Q(r)?null:Array.isArray(r)?r.map(H).filter((r=>!Q(r))):"object"==typeof r?Object.fromEntries(Object.entries(r).map((([r,e])=>[r,H(e)])).filter((([,r])=>!Q(r)))):r,Q=r=>r&&Y(r)?Q(r.value):X(r)||Z(r)||rr(r),X=r=>null==r||""===r,Z=r=>Array.isArray(r)&&r.every(Q),rr=r=>"object"==typeof r&&Z(Object.values(r));(window.elementorV2=window.elementorV2||{}).editorProps=e}(),window.elementorV2.editorProps?.init?.();