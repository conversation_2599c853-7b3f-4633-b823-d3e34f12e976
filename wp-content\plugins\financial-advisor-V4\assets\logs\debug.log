[2025-05-28 11:17:32] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-28 11:17:32] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-28 11:17:32] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-28 11:17:32] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-28 11:17:32] [GENERAL] Utente WordPress ID: 2, <PERSON><PERSON><PERSON>: Administrator, Costo per token: 0.01
[2025-05-28 11:17:32] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-28 11:17:47] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-28 11:17:47] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-28 11:17:47] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-28 11:17:47] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-28 11:17:47] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-28 11:17:47] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-28 11:18:02] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-28 11:18:02] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-28 11:18:02] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-28 11:18:02] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-28 11:18:02] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-28 11:18:02] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-28 11:19:34] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-28 11:19:34] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-28 11:19:34] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-28 11:19:34] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-28 11:19:34] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-28 11:19:34] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-28 11:20:11] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-28 11:20:11] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-28 11:20:11] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-28 11:20:11] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-28 11:20:11] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-28 11:20:11] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-28 11:22:08] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-28 11:22:08] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-28 11:22:08] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-28 11:22:09] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-28 11:22:09] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-28 11:22:09] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-28 11:25:42] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-28 11:25:42] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-28 11:25:42] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-28 11:25:42] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-28 11:25:42] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-28 11:25:42] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-28 11:36:05] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-28 11:36:05] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-28 11:36:05] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-28 11:36:05] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-28 11:36:05] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-28 11:36:05] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-28 11:38:20] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-28 11:38:20] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-28 11:38:20] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-28 11:38:20] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-28 11:38:20] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-28 11:38:20] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-28 11:38:47] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-28 11:38:47] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-28 11:38:47] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-28 11:38:48] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-28 11:38:48] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-28 11:38:48] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-28 11:39:01] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-28 11:39:01] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-28 11:39:01] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-28 11:39:01] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-28 11:39:01] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-28 11:39:01] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-28 11:56:07] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 66.43
[2025-05-28 11:56:07] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 66.43
[2025-05-28 11:56:15] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 66.43
[2025-05-28 11:56:15] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 66.43
[2025-05-28 12:14:58] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 66.43
[2025-05-28 12:14:58] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 66.43
[2025-05-28 12:18:54] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 66.43
[2025-05-28 12:18:54] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 66.43
[2025-05-28 13:27:38] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-28 13:27:38] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-28 13:27:38] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-28 13:27:39] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-28 13:27:39] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-28 13:27:39] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-28 13:28:05] [GENERAL] Upload directory: /home2/glopvhmy/public_html/website_7af41fa0/wp-content/uploads/document-viewer
[2025-05-28 13:28:05] [GENERAL] Upload directory exists: yes
[2025-05-28 13:28:05] [GENERAL] Upload directory writable: yes
[2025-05-28 13:28:05] [GENERAL] Received file upload: Array
(
    [name] => a_test_pdf.pdf
    [full_path] => a_test_pdf.pdf
    [type] => application/pdf
    [tmp_name] => /tmp/php7ZFFSS
    [error] => 0
    [size] => 474569
)

[2025-05-28 13:28:05] [ANALYSIS API] File details - Name: a_test_pdf.pdf, Type: application/pdf, Size: 474569 bytes
[2025-05-28 13:28:05] [GENERAL] File moved successfully to: /home2/glopvhmy/public_html/website_7af41fa0/wp-content/uploads/document-viewer/doc_68370f65c1cdc_a_test_pdf.pdf
[2025-05-28 13:28:05] [GENERAL] File exists at destination: yes
[2025-05-28 13:28:05] [GENERAL] File is readable: yes
[2025-05-28 13:28:05] [GENERAL] File size: 474569 bytes
[2025-05-28 13:28:05] [ANALYSIS API] Avvio estrazione contenuto da file PDF
[2025-05-28 13:28:05] [ANALYSIS API] Estrazione testo PDF: inizio processo per doc_68370f65c1cdc_a_test_pdf.pdf (463.45 KB)
[2025-05-28 13:28:05] [ANALYSIS API] PDF: inizializzazione estrazione testo
[2025-05-28 13:28:05] [ANALYSIS API] PDF: tentativo di estrazione diretto in corso...
[2025-05-28 13:28:05] [ANALYSIS API] PDF: estrazione diretta completata con successo
[2025-05-28 13:28:05] [ANALYSIS API] PDF: estrazione completata. Anteprima del testo estratto: 
Current ratio ATTIVITA' CORRENTI 
Indice di liquidità 
corrente PASSIVITA' CORRENTI 
 
 
 Actual PY Delta % 
Attività correnti 8.371.670 9.231.067 -9,31% 
Passività correnti 3.886.086 5.720.675 -32,07% 
Current ratio 2,15 1,61 33,50%
[2025-05-28 13:28:05] [ANALYSIS API] PDF: lunghezza totale del testo estratto: 236 caratteri
[2025-05-28 13:28:05] [ANALYSIS API] PDF extraction complete. Content length: 236
[2025-05-28 13:28:05] [ANALYSIS API] Temporary file removed
[2025-05-28 13:28:05] [GENERAL] Content extracted successfully, length: 236
[2025-05-28 13:28:16] [GENERAL] Upload directory: /home2/glopvhmy/public_html/website_7af41fa0/wp-content/uploads/document-viewer
[2025-05-28 13:28:16] [GENERAL] Upload directory exists: yes
[2025-05-28 13:28:16] [GENERAL] Upload directory writable: yes
[2025-05-28 13:28:16] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione con modello AI
[2025-05-28 13:28:16] [ANALYSIS API] Invio richiesta di analisi a: https://openrouter.ai/api/v1/chat/completions
[2025-05-28 13:28:16] [ANALYSIS API] Utilizzo modello: deepseek/deepseek-chat-v3-0324:free
[2025-05-28 13:28:16] [ANALYSIS API] FASE DI ANALISI: richiesta inviata al servizio AI, in attesa di risposta...
[2025-05-28 13:28:31] [ANALYSIS API] FASE DI ANALISI: risposta API ricevuta, codice: 200
[2025-05-28 13:28:31] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione risposta
[2025-05-28 13:28:31] [ANALYSIS API] FASE DI ANALISI: Inizio estrazione risultati dall'API response
[2025-05-28 13:28:31] [ANALYSIS API] FASE DI ANALISI: Contenuto estratto dal formato ChatGPT/GPT-4
[2025-05-28 13:28:31] [ANALYSIS API] FASE DI ANALISI: Anteprima del contenuto estratto: ### **Sintesi per Investitori: Analisi del Current Ratio e Opportunità di Investimento**  

#### **Panoramica della Liquidità Aziendale**  
L'azienda mostra un **miglioramento significativo** nella sua posizione di liquidità, come evidenziato dal **current ratio**, che è passato da **1,61 nell'a...
[2025-05-28 13:28:31] [ANALYSIS API] FASE DI ANALISI: Estrazione risultati completata, lunghezza: 2183 caratteri
[2025-05-28 13:28:31] [ANALYSIS API] FASE DI ANALISI: elaborazione completata con successo.
[2025-05-28 13:28:32] [GENERAL] Salvataggio analisi recente: user_id=2, analysis_id=1748438912431, title=a_test_pdf, tokens=58, salvataggio_esplicito=no
[2025-05-28 13:28:32] [GENERAL] save_recent_analysis: Tipo utente identificato: wordpress (ID: 2)
[2025-05-28 13:28:32] [GENERAL] Utente WordPress ID: 2 - utilizzando cost_per_token: 0.01
[2025-05-28 13:28:32] [GENERAL] Statistiche aggiornate per utente WordPress 2: tokens=4728, tot_cost=7.02, credits=65.85, actual_cost=0.58, analyses=29, cost_per_token=0.01000
[2025-05-28 13:28:33] [GENERAL] Richiesta analisi recenti per utente ID: 2
[2025-05-28 13:28:33] [GENERAL] Trovate 5 analisi recenti per utente ID: 2
[2025-05-28 13:28:34] [GENERAL] Richiesta analisi recenti per utente ID: 2
[2025-05-28 13:28:34] [GENERAL] Trovate 5 analisi recenti per utente ID: 2
[2025-05-28 13:29:27] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-28 13:29:27] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-28 13:29:27] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-28 13:29:27] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-28 13:29:27] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-28 13:29:27] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-28 13:29:33] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-28 13:29:33] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-28 13:29:33] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-28 13:29:33] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-28 13:29:33] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-28 13:29:33] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-28 13:36:38] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-28 13:36:38] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-28 13:36:38] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-28 13:36:39] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-28 13:36:39] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-28 13:36:39] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-28 13:36:45] [GENERAL] Sending chat message to API: Quali sono i vantaggi e gli svantaggi dei fondi ETF?
[2025-05-28 13:36:58] [GENERAL] Chat API response received, status code: 200
[2025-05-28 13:36:58] [GENERAL] Chat AI reply extracted successfully, length: 1114
[2025-05-28 13:37:39] [GENERAL] Sending chat message to API: quali sono i migliori etf bilanciati
[2025-05-28 13:38:39] [GENERAL] Chat API request error: cURL error 28: Operation timed out after 60000 milliseconds with 1422 bytes received
[2025-05-28 13:39:09] [GENERAL] Sending chat message to API: cerca i migliori etf sul mercato per performace
[2025-05-28 13:40:09] [GENERAL] Chat API request error: cURL error 28: Operation timed out after 60001 milliseconds with 1412 bytes received
[2025-05-28 13:40:25] [GENERAL] Sending chat message to API: cerca i migliori etf sul mercato per performace
[2025-05-28 13:40:42] [GENERAL] Chat API response received, status code: 200
[2025-05-28 13:40:42] [GENERAL] Chat AI reply extracted successfully, length: 1957
[2025-05-28 14:37:39] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-28 14:37:39] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-28 14:37:39] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-28 14:37:40] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-28 14:37:40] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-28 14:37:40] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-28 14:38:35] [GENERAL] Upload directory: /home2/glopvhmy/public_html/website_7af41fa0/wp-content/uploads/document-viewer
[2025-05-28 14:38:35] [GENERAL] Upload directory exists: yes
[2025-05-28 14:38:35] [GENERAL] Upload directory writable: yes
[2025-05-28 14:38:35] [GENERAL] Received file upload: Array
(
    [name] => Bilancio Nice.pdf
    [full_path] => Bilancio Nice.pdf
    [type] => application/pdf
    [tmp_name] => /tmp/phpSx16oT
    [error] => 0
    [size] => 519908
)

[2025-05-28 14:38:35] [ANALYSIS API] File details - Name: Bilancio Nice.pdf, Type: application/pdf, Size: 519908 bytes
[2025-05-28 14:38:35] [GENERAL] File moved successfully to: /home2/glopvhmy/public_html/website_7af41fa0/wp-content/uploads/document-viewer/doc_68371feb7dddf_Bilancio-Nice.pdf
[2025-05-28 14:38:35] [GENERAL] File exists at destination: yes
[2025-05-28 14:38:35] [GENERAL] File is readable: yes
[2025-05-28 14:38:35] [GENERAL] File size: 519908 bytes
[2025-05-28 14:38:35] [ANALYSIS API] Avvio estrazione contenuto da file PDF
[2025-05-28 14:38:35] [ANALYSIS API] Estrazione testo PDF: inizio processo per doc_68371feb7dddf_Bilancio-Nice.pdf (507.72 KB)
[2025-05-28 14:38:35] [ANALYSIS API] PDF: inizializzazione estrazione testo
[2025-05-28 14:38:35] [ANALYSIS API] PDF: tentativo di estrazione diretto in corso...
[2025-05-28 14:38:35] [ANALYSIS API] PDF: estrazione diretta completata con successo
[2025-05-28 14:38:35] [ANALYSIS API] PDF: estrazione completata. Anteprima del testo estratto: 
NICE spa 
Stato Patrimoniale 
Stato Patrimoniale 
 Valore Integrato PY 
 Delta Value 
% 
 Delta 
Value 
10 ATTIVITA' 
 
05 CREDITI DIVERSI 0 0 0,00% 0 
02 ISTITUTI DI CREDITO -2.193.029 -1.709.351 28,30% 483.677 
03 PRESENTAZIONI SBF 2.265.695 1.826.277 24,06% 439.418 
07 IVA CONTO ERARIO 18.775 675...
[2025-05-28 14:38:35] [ANALYSIS API] PDF: lunghezza totale del testo estratto: 4347 caratteri
[2025-05-28 14:38:35] [ANALYSIS API] PDF extraction complete. Content length: 4347
[2025-05-28 14:38:35] [ANALYSIS API] Temporary file removed
[2025-05-28 14:38:35] [GENERAL] Content extracted successfully, length: 4347
[2025-05-28 14:39:00] [GENERAL] Upload directory: /home2/glopvhmy/public_html/website_7af41fa0/wp-content/uploads/document-viewer
[2025-05-28 14:39:00] [GENERAL] Upload directory exists: yes
[2025-05-28 14:39:00] [GENERAL] Upload directory writable: yes
[2025-05-28 14:39:00] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione con modello AI
[2025-05-28 14:39:00] [ANALYSIS API] Invio richiesta di analisi a: https://openrouter.ai/api/v1/chat/completions
[2025-05-28 14:39:00] [ANALYSIS API] Utilizzo modello: deepseek/deepseek-chat-v3-0324:free
[2025-05-28 14:39:00] [ANALYSIS API] FASE DI ANALISI: richiesta inviata al servizio AI, in attesa di risposta...
[2025-05-28 14:41:00] [ANALYSIS API] FASE DI ANALISI: errore nella richiesta API: cURL error 28: Operation timed out after 120001 milliseconds with 2842 bytes received
[2025-05-28 14:41:50] [GENERAL] Upload directory: /home2/glopvhmy/public_html/website_7af41fa0/wp-content/uploads/document-viewer
[2025-05-28 14:41:50] [GENERAL] Upload directory exists: yes
[2025-05-28 14:41:50] [GENERAL] Upload directory writable: yes
[2025-05-28 14:41:50] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione con modello AI
[2025-05-28 14:41:50] [ANALYSIS API] Invio richiesta di analisi a: https://openrouter.ai/api/v1/chat/completions
[2025-05-28 14:41:50] [ANALYSIS API] Utilizzo modello: deepseek/deepseek-chat-v3-0324:free
[2025-05-28 14:41:50] [ANALYSIS API] FASE DI ANALISI: richiesta inviata al servizio AI, in attesa di risposta...
[2025-05-28 14:42:26] [ANALYSIS API] FASE DI ANALISI: risposta API ricevuta, codice: 200
[2025-05-28 14:42:26] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione risposta
[2025-05-28 14:42:26] [ANALYSIS API] FASE DI ANALISI: Inizio estrazione risultati dall'API response
[2025-05-28 14:42:26] [ANALYSIS API] FASE DI ANALISI: Contenuto estratto dal formato ChatGPT/GPT-4
[2025-05-28 14:42:26] [ANALYSIS API] FASE DI ANALISI: Anteprima del contenuto estratto: ### Analisi del Coefficiente di Indebitamento e Sostenibilità a Lungo Termine

#### 1. **Calcolo del Coefficiente di Indebitamento**
Il **coefficiente di indebitamento** (o leverage) si calcola come:  
\[ \text{Indebitamento} = \frac{\text{Passività Totali}}{\text{Capitale Netto}} \]  

Dai dati f...
[2025-05-28 14:42:26] [ANALYSIS API] FASE DI ANALISI: Estrazione risultati completata, lunghezza: 3529 caratteri
[2025-05-28 14:42:26] [ANALYSIS API] FASE DI ANALISI: elaborazione completata con successo.
[2025-05-28 14:42:27] [GENERAL] Salvataggio analisi recente: user_id=2, analysis_id=1748443347433, title=Bilancio Nice, tokens=1087, salvataggio_esplicito=no
[2025-05-28 14:42:27] [GENERAL] save_recent_analysis: Tipo utente identificato: wordpress (ID: 2)
[2025-05-28 14:42:27] [GENERAL] Utente WordPress ID: 2 - utilizzando cost_per_token: 0.01
[2025-05-28 14:42:27] [GENERAL] Statistiche aggiornate per utente WordPress 2: tokens=5815, tot_cost=17.89, credits=54.98, actual_cost=10.87, analyses=30, cost_per_token=0.01000
[2025-05-28 14:42:28] [GENERAL] Richiesta analisi recenti per utente ID: 2
[2025-05-28 14:42:28] [GENERAL] Trovate 5 analisi recenti per utente ID: 2
[2025-05-28 14:42:29] [GENERAL] Richiesta analisi recenti per utente ID: 2
[2025-05-28 14:42:29] [GENERAL] Trovate 5 analisi recenti per utente ID: 2
[2025-05-28 15:16:51] [GENERAL] Saving model list. Raw data: Array
(
    [0] => deepseek/deepseek-chat-v3-0324:free
    [1] => google/gemini-2.5-pro-exp-03-25:free
    [2] => meta-llama/llama-4-maverick:free
)

[2025-05-28 15:16:51] [GENERAL] Sanitized models: Array
(
    [0] => deepseek/deepseek-chat-v3-0324:free
    [1] => google/gemini-2.5-pro-exp-03-25:free
    [2] => meta-llama/llama-4-maverick:free
)

[2025-05-28 15:16:51] [GENERAL] Existing models before update: Array
(
    [0] => openai/gpt-3.5-turbo
    [1] => deepseek/deepseek-chat-v3-0324:free
    [2] => google/gemma-3-27b-it
    [3] => google/gemini-2.5-pro-exp-03-25:free
)

[2025-05-28 15:16:51] [GENERAL] Model list saved successfully to database
[2025-05-29 10:25:59] [GENERAL] Cleared  old errors (older than 7 days)
[2025-05-29 10:25:59] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-05-29 10:33:50] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:33:50] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:33:50] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:33:55] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:33:56] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:33:56] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:33:56] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:33:56] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:33:57] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:42:59] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:42:59] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:42:59] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:43:00] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:43:01] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:43:01] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:43:03] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:43:04] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:43:04] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:43:07] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:43:08] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:43:08] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:43:08] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:43:08] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:43:09] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:43:21] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:43:21] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:43:21] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:43:24] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:43:25] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:43:26] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:43:26] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:43:27] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:43:28] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:44:13] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:44:14] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:44:15] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:44:17] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:44:17] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:44:17] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:44:17] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:44:18] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:44:18] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:48:28] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-29 10:48:28] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-29 10:48:28] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-29 10:48:28] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-29 10:48:28] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-29 10:48:28] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-29 10:48:33] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-29 10:48:33] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-29 10:48:33] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-29 10:48:33] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-29 10:48:33] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-29 10:48:33] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-29 10:48:43] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-29 10:48:43] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-29 10:48:43] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-29 10:48:43] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-29 10:48:43] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-29 10:48:43] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-29 10:50:24] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:50:25] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:50:25] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:50:28] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:50:29] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:50:29] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:50:47] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:50:48] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-05-29 10:50:48] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-05-29 10:51:46] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-29 10:51:46] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-29 12:21:58] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-29 12:21:59] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-29 12:22:48] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-29 12:22:48] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-29 12:44:16] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-29 12:44:16] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-29 12:45:15] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-29 12:45:15] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-29 12:50:04] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-29 12:50:04] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-29 13:27:41] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-29 13:27:41] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-30 07:09:08] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-30 07:09:08] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-30 07:26:31] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-30 07:26:31] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.98
[2025-05-30 08:59:45] [GENERAL] Esecuzione pulizia dati al logout per utente ID: 1
[2025-05-30 08:59:45] [GENERAL] Pulizia analisi recenti completata per utente ID: 1. Record eliminati: 0
[2025-05-30 08:59:45] [GENERAL] Azzeramento valori di spesa completato per utente ID: 1. Crediti preservati. Risultato: successo
[2025-05-30 08:59:45] [GENERAL] Esecuzione pulizia dati al logout per utente ID: 1
[2025-05-30 08:59:45] [GENERAL] Pulizia analisi recenti completata per utente ID: 1. Record eliminati: 0
[2025-05-30 08:59:45] [GENERAL] Azzeramento valori di spesa completato per utente ID: 1. Crediti preservati. Risultato: successo
[2025-05-30 09:00:47] [GENERAL] Recupero statistiche per utente WordPress ID: 1
[2025-05-30 09:00:47] [GENERAL] Creato nuovo record statistiche per utente 1 con 10.00 crediti
[2025-05-30 09:00:47] [GENERAL] Utente WordPress ID: 1, Ruolo: Administrator, Costo per token: 0.01
[2025-05-30 09:00:47] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-30 09:00:47] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=1, Tipo=wordpress
[2025-05-30 09:00:47] [GENERAL] SQL Update eseguito con successo: 0 righe aggiornate
[2025-05-30 09:00:47] [GENERAL] Spesa effettiva resettata con successo per utente ID: 1 (spesa totale preservata)
[2025-05-30 09:00:51] [GENERAL] Esecuzione pulizia dati al logout per utente ID: 1
[2025-05-30 09:00:51] [GENERAL] Pulizia analisi recenti completata per utente ID: 1. Record eliminati: 0
[2025-05-30 09:00:51] [GENERAL] Azzeramento valori di spesa completato per utente ID: 1. Crediti preservati. Risultato: successo
[2025-05-30 09:00:51] [GENERAL] Esecuzione pulizia dati al logout per utente ID: 1
[2025-05-30 09:00:51] [GENERAL] Pulizia analisi recenti completata per utente ID: 1. Record eliminati: 0
[2025-05-30 09:00:51] [GENERAL] Azzeramento valori di spesa completato per utente ID: 1. Crediti preservati. Risultato: successo
[2025-05-30 09:01:25] [GENERAL] Utente non WordPress rilevato, verifica se è un sottoscrittore esterno
[2025-05-30 09:01:25] [GENERAL] Nessun utente valido identificato, invio statistiche vuote
[2025-05-30 09:01:38] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-05-30 09:01:38] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-05-30 09:01:38] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-05-30 09:01:38] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-05-30 09:01:38] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-05-30 09:01:38] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-05-30 11:28:52] [GENERAL] Cleared  old errors (older than 7 days)
[2025-05-30 11:28:52] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-06-01 12:04:21] [GENERAL] Cleared  old errors (older than 7 days)
[2025-06-01 12:04:21] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-06-02 13:19:05] [GENERAL] Cleared  old errors (older than 7 days)
[2025-06-02 13:19:05] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-06-03 06:22:02] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-06-03 06:22:03] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-06-03 06:22:03] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-06-03 06:22:09] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-06-03 06:22:09] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-06-03 06:22:09] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-06-03 06:51:33] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-06-03 06:51:33] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-06-03 06:51:33] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-06-03 12:03:47] [GENERAL] Cleared  old errors (older than 7 days)
[2025-06-03 12:03:47] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-06-04 10:27:23] [GENERAL] Cleared  old errors (older than 7 days)
[2025-06-04 10:27:23] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-06-05 19:30:06] [GENERAL] Cleared  old errors (older than 7 days)
[2025-06-05 19:30:06] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-06-06 11:26:46] [GENERAL] Cleared  old errors (older than 7 days)
[2025-06-06 11:26:46] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-06-07 11:57:49] [GENERAL] Cleared  old errors (older than 7 days)
[2025-06-07 11:57:49] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-06-13 08:24:19] [GENERAL] Utente non WordPress rilevato, verifica se è un sottoscrittore esterno
[2025-06-13 08:24:19] [GENERAL] Nessun utente valido identificato, invio statistiche vuote
[2025-06-13 09:12:25] [GENERAL] Utente non WordPress rilevato, verifica se è un sottoscrittore esterno
[2025-06-13 09:12:25] [GENERAL] Nessun utente valido identificato, invio statistiche vuote
[2025-06-13 13:13:55] [GENERAL] Cleared  old errors (older than 7 days)
[2025-06-13 13:13:55] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-06-13 13:14:53] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-13 13:14:53] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-13 13:14:53] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-13 13:15:04] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-13 13:15:04] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-13 13:15:04] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-13 13:27:21] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-13 13:27:21] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-13 13:27:21] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-13 13:27:36] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-13 13:27:36] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-13 13:27:36] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-13 13:37:20] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-13 13:37:20] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-13 13:37:20] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-13 13:37:30] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-13 13:37:30] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-13 13:37:30] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-13 13:37:54] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-13 13:37:54] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-13 13:37:54] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-13 13:38:02] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-13 13:38:03] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-13 13:38:03] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-13 13:38:35] [GENERAL] Upload directory: /var/www/html/claryfin/wp-content/uploads/document-viewer
[2025-06-13 13:38:35] [GENERAL] Upload directory exists: yes
[2025-06-13 13:38:35] [GENERAL] Upload directory writable: yes
[2025-06-13 13:38:35] [GENERAL] Received file upload: Array
(
    [name] => a_test_pdf.pdf
    [type] => application/pdf
    [tmp_name] => /tmp/phpve2YPM
    [error] => 0
    [size] => 474569
)

[2025-06-13 13:38:35] [ANALYSIS API] File details - Name: a_test_pdf.pdf, Type: application/pdf, Size: 474569 bytes
[2025-06-13 13:38:35] [GENERAL] File moved successfully to: /var/www/html/claryfin/wp-content/uploads/document-viewer/doc_684c29dbdc11d_a_test_pdf.pdf
[2025-06-13 13:38:35] [GENERAL] File exists at destination: yes
[2025-06-13 13:38:35] [GENERAL] File is readable: yes
[2025-06-13 13:38:35] [GENERAL] File size: 474569 bytes
[2025-06-13 13:38:35] [ANALYSIS API] Avvio estrazione contenuto da file PDF
[2025-06-13 13:38:35] [ANALYSIS API] Estrazione testo PDF: inizio processo per doc_684c29dbdc11d_a_test_pdf.pdf (463.45 KB)
[2025-06-13 13:38:35] [ANALYSIS API] PDF: inizializzazione estrazione testo
[2025-06-13 13:38:36] [ANALYSIS API] PDF: tentativo di estrazione diretto in corso...
[2025-06-13 13:38:36] [ANALYSIS API] PDF: estrazione diretta completata con successo
[2025-06-13 13:38:36] [ANALYSIS API] PDF: estrazione completata. Anteprima del testo estratto: 
Current ratio ATTIVITA' CORRENTI 
Indice di liquidità 
corrente PASSIVITA' CORRENTI 
 
 
 Actual PY Delta % 
Attività correnti 8.371.670 9.231.067 -9,31% 
Passività correnti 3.886.086 5.720.675 -32,07% 
Current ratio 2,15 1,61 33,50%
[2025-06-13 13:38:36] [ANALYSIS API] PDF: lunghezza totale del testo estratto: 236 caratteri
[2025-06-13 13:38:36] [ANALYSIS API] PDF extraction complete. Content length: 236
[2025-06-13 13:38:36] [ANALYSIS API] Temporary file removed
[2025-06-13 13:38:36] [GENERAL] Content extracted successfully, length: 236
[2025-06-13 13:39:03] [GENERAL] Upload directory: /var/www/html/claryfin/wp-content/uploads/document-viewer
[2025-06-13 13:39:03] [GENERAL] Upload directory exists: yes
[2025-06-13 13:39:03] [GENERAL] Upload directory writable: yes
[2025-06-13 13:39:03] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione con modello AI
[2025-06-13 13:39:03] [ANALYSIS API] Invio richiesta di analisi a: https://openrouter.ai/api/v1/chat/completions
[2025-06-13 13:39:03] [ANALYSIS API] Utilizzo modello: meta-llama/llama-4-maverick:free
[2025-06-13 13:39:03] [ANALYSIS API] FASE DI ANALISI: richiesta inviata al servizio AI, in attesa di risposta...
[2025-06-13 13:39:08] [ANALYSIS API] FASE DI ANALISI: risposta API ricevuta, codice: 200
[2025-06-13 13:39:08] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione risposta
[2025-06-13 13:39:08] [ANALYSIS API] FASE DI ANALISI: Inizio estrazione risultati dall'API response
[2025-06-13 13:39:08] [ANALYSIS API] FASE DI ANALISI: Contenuto estratto dal formato ChatGPT/GPT-4
[2025-06-13 13:39:08] [ANALYSIS API] FASE DI ANALISI: Anteprima del contenuto estratto: **Analisi dei Rischi**

Il documento fornito contiene informazioni relative ai dati finanziari di un'azienda, in particolare riguardo alle attività e passività correnti e al current ratio. L'analisi di questi dati può aiutare a identificare potenziali rischi per l'azienda.

**Identificazione dei ...
[2025-06-13 13:39:08] [ANALYSIS API] FASE DI ANALISI: Estrazione risultati completata, lunghezza: 2748 caratteri
[2025-06-13 13:39:08] [ANALYSIS API] FASE DI ANALISI: elaborazione completata con successo.
[2025-06-13 13:39:16] [GENERAL] Salvataggio analisi recente: user_id=2, analysis_id=1749821948561, title=a_test_pdf, tokens=58, salvataggio_esplicito=no
[2025-06-13 13:39:16] [GENERAL] save_recent_analysis: Tipo utente identificato: wordpress (ID: 2)
[2025-06-13 13:39:16] [GENERAL] Utente WordPress ID: 2 - utilizzando cost_per_token: 0.01
[2025-06-13 13:39:16] [GENERAL] Statistiche aggiornate per utente WordPress 2: tokens=5873, tot_cost=18.47, credits=54.40, actual_cost=0.58, analyses=31, cost_per_token=0.01000
[2025-06-13 13:39:26] [GENERAL] Richiesta analisi recenti per utente ID: 2
[2025-06-13 13:39:26] [GENERAL] Trovate 5 analisi recenti per utente ID: 2
[2025-06-13 13:39:33] [GENERAL] Richiesta analisi recenti per utente ID: 2
[2025-06-13 13:39:33] [GENERAL] Trovate 5 analisi recenti per utente ID: 2
[2025-06-13 13:41:00] [GENERAL] Avvio salvataggio analisi documento
[2025-06-13 13:41:00] [GENERAL] Documento salvato con successo: /var/www/html/claryfin/wp-content/uploads/document-viewer/saved/doc_684c2a6cce15a_a_test_pdf.pdf
[2025-06-13 13:41:00] [GENERAL] Preparazione inserimento dati nel DB: Array
(
    [user_id] => 2
    [title] => a_test_pdf
    [query] => Identifica e analizza tutti i potenziali rischi menzionati in questo documento, classificandoli per probabilità e impatto potenziale.
    [analysis_results] => Analisi dei Rischi<br><br><br><br>Il documento fornito contiene informazioni relative ai dati finanziari di<br><br>un\'azienda, in particolare riguardo alle attività e passività correnti e al<br><br>current ratio. L\'analisi di questi dati può aiutare a identificare potenziali<br><br>rischi per l\'azienda.<br><br><br><br>Identificazione dei Rischi<br><br><br><br>Dai dati forniti, possiamo identificare i seguenti potenziali rischi:<br><br><br><br>1. Rischio di Liquidità: Il current ratio è un indicatore della capacità<br><br>dell\'azienda di far fronte alle proprie passività correnti con le attività<br><br>correnti. Un current ratio superiore a 1 indica una buona capacità di far<br><br>fronte alle passività. Nel caso in esame, il current ratio è passato da 1,61 a<br><br>2,15, indicando un miglioramento nella capacità dell\'azienda di gestire le<br><br>proprie passività correnti.<br><br>2. Rischio di Riduzione delle Attività Correnti: La riduzione delle<br><br>attività correnti del 9,31% potrebbe indicare un rischio di riduzione della<br><br>capacità dell\'azienda di generare liquidità.<br><br>3. Rischio di Dipendenza dalle Passività Correnti: La riduzione delle<br><br>passività correnti del 32,07% potrebbe essere un indicatore positivo, ma<br><br>potrebbe anche nascondere un rischio se l\'azienda non è in grado di gestire<br><br>efficacemente le proprie passività a lungo termine.<br><br><br><br>Classificazione dei Rischi per Probabilità e Impatto Potenziale<br><br><br><br>Per classificare i rischi identificati, è necessario valutare la probabilità<br><br>che si verifichino e il loro potenziale impatto sull\'azienda.<br><br><br><br>1. Rischio di Liquidità:<br><br>Probabilità: Bassa (il current ratio è migliorato)<br><br>Impatto Potenziale: Alto (se l\'azienda non fosse in grado di far fronte alle<br><br>proprie passività correnti)<br><br>Classificazione: Basso Rischio<br><br>2. Rischio di Riduzione delle Attività Correnti:<br><br>Probabilità: Media (la riduzione delle attività correnti è stata<br><br>significativa)<br><br>Impatto Potenziale: Medio (potrebbe influire sulla capacità dell\'azienda di<br><br>generare liquidità)<br><br>Classificazione: Rischio Moderato<br><br>3. Rischio di Dipendenza dalle Passività Correnti:<br><br>Probabilità: Bassa (la riduzione delle passività correnti è stata<br><br>significativa)<br><br>Impatto Potenziale: Alto (se l\'azienda non gestisce efficacemente le proprie<br><br>passività a lungo termine)<br><br>Classificazione: Rischio Moderato<br><br><br><br>Conclusione<br><br><br><br>In base all\'analisi dei dati forniti, i rischi identificati sono stati<br><br>classificati per probabilità e impatto potenziale. Il rischio di liquidità è<br><br>considerato a basso rischio a causa del miglioramento del current ratio. I<br><br>rischi di riduzione delle attività correnti e di dipendenza dalle passività<br><br>correnti sono considerati a rischio moderato. È importante che l\'azienda<br><br>monitori attentamente questi rischi e adotti strategie per mitigarli.<br><br>
    [created_at] => 2025-06-13 13:41:00
    [document_path] => /var/www/html/claryfin/wp-content/uploads/document-viewer/saved/doc_684c2a6cce15a_a_test_pdf.pdf
)

[2025-06-13 13:41:00] [GENERAL] Analisi salvata con successo. ID: 45
[2025-06-13 13:41:00] [GENERAL] Stima token utilizzati per l'analisi: 659.75
[2025-06-13 13:41:00] [GENERAL] Aggiornamento statistiche per utente: ID=2, Tipo=wordpress
[2025-06-13 13:41:00] [GENERAL] Salvataggio esplicito: no
[2025-06-13 13:41:00] [GENERAL] Statistiche aggiornate con successo per utente WordPress ID: 2
[2025-06-13 13:41:00] [GENERAL] Salvataggio analisi recente: user_id=2, analysis_id=45, title=a_test_pdf, tokens=659, salvataggio_esplicito=no
[2025-06-13 13:41:00] [GENERAL] save_recent_analysis: Tipo utente identificato: wordpress (ID: 2)
[2025-06-13 13:41:01] [GENERAL] Aggiornamento statistiche per utente: ID=2, Tipo=wordpress
[2025-06-13 13:41:01] [GENERAL] Salvataggio esplicito: no
[2025-06-13 13:41:01] [GENERAL] Statistiche aggiornate con successo per utente WordPress ID: 2
[2025-06-13 13:41:01] [GENERAL] Salvataggio analisi recente: user_id=2, analysis_id=45, title=a_test_pdf, tokens=659, salvataggio_esplicito=no
[2025-06-13 13:41:01] [GENERAL] save_recent_analysis: Tipo utente identificato: wordpress (ID: 2)
[2025-06-13 13:41:19] [GENERAL] export_analysis_pdf called with the following parameters:
[2025-06-13 13:41:19] [GENERAL] - include_query: yes
[2025-06-13 13:41:19] [GENERAL] - include_full_analysis: yes
[2025-06-13 13:41:19] [GENERAL] - include_key_points: yes
[2025-06-13 13:41:19] [GENERAL] - include_original_doc: yes
[2025-06-13 13:41:19] [GENERAL] - using analysis_html param: yes
[2025-06-13 13:41:19] [GENERAL] - using analysis param: no
[2025-06-13 13:41:19] [GENERAL] - content length: 3087
[2025-06-13 13:41:19] [GENERAL] - is_preview: false
[2025-06-13 13:41:22] [GENERAL] Processing SVG for PDF export, HTML length: 3087
[2025-06-13 13:41:22] [GENERAL] SVG processing for PDF completed
[2025-06-13 13:41:22] [GENERAL] Processing SVG for PDF export, HTML length: 526
[2025-06-13 13:41:22] [GENERAL] SVG processing for PDF completed
[2025-06-13 13:41:23] [GENERAL] PDF added with 1 pages
[2025-06-13 13:54:53] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-13 13:54:53] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-13 13:54:53] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-13 13:55:04] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-13 13:55:04] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-13 13:55:04] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-13 13:56:31] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-13 13:56:31] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-13 13:56:31] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-13 13:56:41] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-13 13:56:41] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-13 13:56:41] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-13 13:59:58] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-13 13:59:58] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-13 13:59:58] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-13 14:00:08] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-13 14:00:08] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-13 14:00:08] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-16 08:35:59] [GENERAL] Cleared  old errors (older than 7 days)
[2025-06-16 08:35:59] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-06-16 08:36:12] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-16 08:36:12] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-16 08:36:12] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-16 08:36:21] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-16 08:36:21] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-16 08:36:21] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-16 08:38:04] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-16 08:38:04] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-16 08:38:04] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-16 08:38:11] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-16 08:38:11] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-16 08:38:11] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-16 08:38:44] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-16 08:38:44] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-16 08:38:44] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-16 08:38:50] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-16 08:38:50] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-16 08:38:50] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-16 08:39:31] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-16 08:39:31] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-16 08:39:31] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-16 08:39:40] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-16 08:39:40] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-16 08:39:40] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-16 08:40:31] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-16 08:40:31] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-16 08:40:31] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-16 08:40:41] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-16 08:40:41] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-16 08:40:41] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-16 08:42:42] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-16 08:42:42] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-16 08:42:42] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-16 08:42:49] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-16 08:42:49] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-16 08:42:49] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-16 08:44:16] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-16 08:44:16] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-16 08:44:16] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-16 08:44:24] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-16 08:44:24] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-16 08:44:24] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-16 11:26:45] [GENERAL] Cleared  old errors (older than 7 days)
[2025-06-16 11:26:45] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-06-16 11:27:15] [GENERAL] Utente non WordPress rilevato, verifica se è un sottoscrittore esterno
[2025-06-16 11:27:15] [GENERAL] Nessun utente valido identificato, invio statistiche vuote
[2025-06-16 11:28:20] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-16 11:28:20] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-16 11:28:20] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-16 11:28:22] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-16 11:28:22] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-16 11:28:22] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-16 11:29:32] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-16 11:29:32] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-16 11:29:32] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-16 11:29:34] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-16 11:29:34] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-16 11:29:34] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 07:51:42] [GENERAL] Cleared  old errors (older than 7 days)
[2025-06-30 07:51:42] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-06-30 07:51:53] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 07:51:53] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 07:51:53] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 07:51:56] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 07:51:56] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 07:51:56] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 07:53:35] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 07:53:35] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 07:53:35] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 07:53:38] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 07:53:38] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 07:53:38] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 08:02:06] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 08:02:06] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 08:02:06] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 08:02:09] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 08:02:09] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 08:02:09] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 08:03:33] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 08:03:33] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 08:03:33] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 08:03:35] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 08:03:35] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 08:03:35] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 08:03:50] [GENERAL] Utente non WordPress rilevato, verifica se è un sottoscrittore esterno
[2025-06-30 08:03:50] [GENERAL] Nessun utente valido identificato, invio statistiche vuote
[2025-06-30 08:07:14] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 08:07:14] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 08:07:14] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 08:07:17] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 08:07:17] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 08:07:17] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 08:07:33] [GENERAL] Utente non WordPress rilevato, verifica se è un sottoscrittore esterno
[2025-06-30 08:07:33] [GENERAL] Nessun utente valido identificato, invio statistiche vuote
[2025-06-30 08:10:53] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 08:10:53] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 08:10:53] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 08:10:56] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 08:10:56] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 08:10:56] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 08:11:11] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 08:11:11] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 08:11:11] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 08:11:14] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 08:11:14] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 08:11:14] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 08:11:20] [GENERAL] Sending chat message to API: Cos\'?? un portafoglio d\'investimento diversificato?
[2025-06-30 08:11:22] [GENERAL] Chat API response received, status code: 200
[2025-06-30 08:11:22] [GENERAL] Chat AI reply extracted successfully, length: 460
[2025-06-30 08:12:29] [GENERAL] Sending chat message to API: Come funzionano i fondi comuni di investimento?
[2025-06-30 08:12:39] [GENERAL] Chat API response received, status code: 200
[2025-06-30 08:12:39] [GENERAL] Chat AI reply extracted successfully, length: 1054
[2025-06-30 08:13:20] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 08:13:20] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 08:13:20] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 08:13:25] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 08:13:25] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 08:13:25] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 08:13:35] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 08:13:35] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 08:13:35] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 08:13:39] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 08:13:39] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 08:13:39] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 08:34:03] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-06-30 08:34:06] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-06-30 08:34:12] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-06-30 08:40:58] [GENERAL] Imported 55 new educational questions to Financial Academy
[2025-06-30 08:47:40] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 08:47:40] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 08:47:40] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 08:47:43] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 08:47:43] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 08:47:43] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 08:52:25] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 08:52:25] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 08:52:25] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 08:52:27] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 08:52:28] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 08:52:28] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 08:55:20] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 08:55:20] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 08:55:20] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 08:55:22] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 08:55:23] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 08:55:23] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 08:56:13] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 08:56:13] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 08:56:13] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 08:56:16] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 08:56:16] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 08:56:16] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 08:56:38] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 08:56:38] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 08:56:38] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 08:56:40] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 08:56:40] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 08:56:40] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 08:57:13] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 08:57:13] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 08:57:13] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 08:57:15] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 08:57:15] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 08:57:15] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:08:33] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:08:33] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:08:33] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:08:36] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:08:36] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:08:36] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:10:05] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:10:05] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:10:05] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:10:07] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:10:07] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:10:07] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:10:59] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:10:59] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:10:59] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:11:01] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:11:01] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:11:01] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:12:34] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:12:34] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:12:34] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:12:36] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:12:36] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:12:36] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:17:08] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:17:08] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:17:08] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:17:10] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:17:10] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:17:10] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:20:09] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:20:09] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:20:09] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:20:11] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:20:11] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:20:11] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:20:27] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:20:27] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:20:27] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:20:29] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:20:29] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:20:29] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:21:52] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:21:52] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:21:52] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:21:54] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:21:54] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:21:54] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:30:54] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:30:54] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:30:54] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:30:56] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:30:56] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:30:56] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:31:18] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:31:18] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:31:18] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:31:20] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:31:20] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:31:20] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:36:15] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:36:15] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:36:15] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:36:17] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:36:17] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:36:17] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:40:42] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:40:42] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:40:42] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:40:44] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:40:44] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:40:44] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:41:59] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:41:59] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:41:59] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:42:02] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:42:02] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:42:02] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:45:51] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:45:51] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:45:51] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:45:53] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:45:53] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:45:53] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:46:05] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:46:05] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:46:05] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:46:08] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:46:08] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:46:08] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:46:40] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:46:40] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:46:40] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:46:42] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:46:42] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:46:42] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:47:01] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:47:01] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:47:01] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:47:03] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:47:03] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:47:03] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:48:25] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:48:25] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:48:25] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:48:27] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:48:27] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:48:27] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:49:28] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:49:28] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:49:28] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:49:30] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:49:30] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:49:30] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:50:48] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:50:48] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:50:48] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:50:51] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:50:51] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:50:51] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:51:39] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:51:39] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:51:39] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:51:43] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:51:43] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:51:43] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:57:46] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 09:57:46] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 09:57:46] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 09:57:50] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 09:57:50] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 09:57:50] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 09:58:37] [GENERAL] Sending chat message to API: Cos\'è il ROE (Return on Equity) e come si interpreta?
[2025-06-30 09:58:47] [GENERAL] Chat API response received, status code: 200
[2025-06-30 09:58:47] [GENERAL] Chat AI reply extracted successfully, length: 793
[2025-06-30 10:04:23] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 10:04:24] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 10:04:24] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 10:04:30] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 10:04:30] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 10:04:30] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 10:04:57] [GENERAL] Sending chat message to API: Cos\'è il ROE (Return on Equity) e come si interpreta?
[2025-06-30 10:05:11] [GENERAL] Chat API response received, status code: 200
[2025-06-30 10:05:11] [GENERAL] Chat AI reply extracted successfully, length: 836
[2025-06-30 10:10:02] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 10:10:02] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 10:10:02] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 10:10:04] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 10:10:04] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 10:10:04] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 10:10:34] [GENERAL] Sending chat message to API: Cos\'è il ROE (Return on Equity) e come si interpreta?
[2025-06-30 10:10:57] [GENERAL] Chat API response received, status code: 200
[2025-06-30 10:10:57] [GENERAL] Chat AI reply extracted successfully, length: 1058
[2025-06-30 10:35:53] [GENERAL] Cleared  old errors (older than 7 days)
[2025-06-30 10:35:53] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-06-30 10:35:56] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 10:35:56] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 10:35:56] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 10:35:59] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 10:35:59] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 10:35:59] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 10:36:40] [GENERAL] Upload directory: /var/www/html/claryfin/wp-content/uploads/document-viewer
[2025-06-30 10:36:40] [GENERAL] Upload directory exists: yes
[2025-06-30 10:36:40] [GENERAL] Upload directory writable: yes
[2025-06-30 10:36:40] [GENERAL] Received file upload: Array
(
    [name] => a_test_pdf.pdf
    [type] => application/pdf
    [tmp_name] => /tmp/php8FpMTJ
    [error] => 0
    [size] => 474569
)

[2025-06-30 10:36:40] [ANALYSIS API] File details - Name: a_test_pdf.pdf, Type: application/pdf, Size: 474569 bytes
[2025-06-30 10:36:40] [GENERAL] File moved successfully to: /var/www/html/claryfin/wp-content/uploads/document-viewer/doc_686268b8c7603_a_test_pdf.pdf
[2025-06-30 10:36:40] [GENERAL] File exists at destination: yes
[2025-06-30 10:36:40] [GENERAL] File is readable: yes
[2025-06-30 10:36:40] [GENERAL] File size: 474569 bytes
[2025-06-30 10:36:40] [ANALYSIS API] Avvio estrazione contenuto da file PDF
[2025-06-30 10:36:40] [ANALYSIS API] Estrazione testo PDF: inizio processo per doc_686268b8c7603_a_test_pdf.pdf (463.45 KB)
[2025-06-30 10:36:40] [ANALYSIS API] PDF: inizializzazione estrazione testo
[2025-06-30 10:36:40] [ANALYSIS API] PDF: tentativo di estrazione diretto in corso...
[2025-06-30 10:36:41] [ANALYSIS API] PDF: estrazione diretta completata con successo
[2025-06-30 10:36:41] [ANALYSIS API] PDF: estrazione completata. Anteprima del testo estratto: 
Current ratio ATTIVITA' CORRENTI 
Indice di liquidità 
corrente PASSIVITA' CORRENTI 
 
 
 Actual PY Delta % 
Attività correnti 8.371.670 9.231.067 -9,31% 
Passività correnti 3.886.086 5.720.675 -32,07% 
Current ratio 2,15 1,61 33,50%
[2025-06-30 10:36:41] [ANALYSIS API] PDF: lunghezza totale del testo estratto: 236 caratteri
[2025-06-30 10:36:41] [ANALYSIS API] PDF extraction complete. Content length: 236
[2025-06-30 10:36:41] [ANALYSIS API] Temporary file removed
[2025-06-30 10:36:41] [GENERAL] Content extracted successfully, length: 236
[2025-06-30 10:40:42] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 10:40:42] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 10:40:42] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 10:40:44] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 10:40:44] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 10:40:44] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-06-30 10:41:03] [GENERAL] Upload directory: /var/www/html/claryfin/wp-content/uploads/document-viewer
[2025-06-30 10:41:03] [GENERAL] Upload directory exists: yes
[2025-06-30 10:41:03] [GENERAL] Upload directory writable: yes
[2025-06-30 10:41:03] [GENERAL] Received file upload: Array
(
    [name] => a_test_pdf.pdf
    [type] => application/pdf
    [tmp_name] => /tmp/phpef4qDI
    [error] => 0
    [size] => 474569
)

[2025-06-30 10:41:03] [ANALYSIS API] File details - Name: a_test_pdf.pdf, Type: application/pdf, Size: 474569 bytes
[2025-06-30 10:41:03] [GENERAL] File moved successfully to: /var/www/html/claryfin/wp-content/uploads/document-viewer/doc_686269bf43a73_a_test_pdf.pdf
[2025-06-30 10:41:03] [GENERAL] File exists at destination: yes
[2025-06-30 10:41:03] [GENERAL] File is readable: yes
[2025-06-30 10:41:03] [GENERAL] File size: 474569 bytes
[2025-06-30 10:41:03] [ANALYSIS API] Avvio estrazione contenuto da file PDF
[2025-06-30 10:41:03] [ANALYSIS API] Estrazione testo PDF: inizio processo per doc_686269bf43a73_a_test_pdf.pdf (463.45 KB)
[2025-06-30 10:41:03] [ANALYSIS API] PDF: inizializzazione estrazione testo
[2025-06-30 10:41:03] [ANALYSIS API] PDF: tentativo di estrazione diretto in corso...
[2025-06-30 10:41:03] [ANALYSIS API] PDF: estrazione diretta completata con successo
[2025-06-30 10:41:03] [ANALYSIS API] PDF: estrazione completata. Anteprima del testo estratto: 
Current ratio ATTIVITA' CORRENTI 
Indice di liquidità 
corrente PASSIVITA' CORRENTI 
 
 
 Actual PY Delta % 
Attività correnti 8.371.670 9.231.067 -9,31% 
Passività correnti 3.886.086 5.720.675 -32,07% 
Current ratio 2,15 1,61 33,50%
[2025-06-30 10:41:03] [ANALYSIS API] PDF: lunghezza totale del testo estratto: 236 caratteri
[2025-06-30 10:41:03] [ANALYSIS API] PDF extraction complete. Content length: 236
[2025-06-30 10:41:03] [ANALYSIS API] Temporary file removed
[2025-06-30 10:41:03] [GENERAL] Content extracted successfully, length: 236
[2025-06-30 10:41:26] [GENERAL] Upload directory: /var/www/html/claryfin/wp-content/uploads/document-viewer
[2025-06-30 10:41:26] [GENERAL] Upload directory exists: yes
[2025-06-30 10:41:26] [GENERAL] Upload directory writable: yes
[2025-06-30 10:41:26] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione con modello AI
[2025-06-30 10:41:26] [ANALYSIS API] Invio richiesta di analisi a: https://openrouter.ai/api/v1/chat/completions
[2025-06-30 10:41:26] [ANALYSIS API] Utilizzo modello: deepseek/deepseek-chat-v3-0324:free
[2025-06-30 10:41:26] [ANALYSIS API] FASE DI ANALISI: richiesta inviata al servizio AI, in attesa di risposta...
[2025-06-30 10:42:09] [ANALYSIS API] FASE DI ANALISI: risposta API ricevuta, codice: 200
[2025-06-30 10:42:09] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione risposta
[2025-06-30 10:42:09] [ANALYSIS API] FASE DI ANALISI: Inizio estrazione risultati dall'API response
[2025-06-30 10:42:09] [ANALYSIS API] FASE DI ANALISI: Contenuto estratto dal formato ChatGPT/GPT-4
[2025-06-30 10:42:09] [ANALYSIS API] FASE DI ANALISI: Anteprima del contenuto estratto: # **Sintesi per Investitori: Analisi del Current Ratio e Opportunità di Investimento**  

## **Panoramica della Situazione Finanziaria**  
Il documento presenta un'analisi del **current ratio** (indice di liquidità corrente) di un'azienda, confrontando i dati attuali con quelli dell'anno precedent...
[2025-06-30 10:42:09] [ANALYSIS API] FASE DI ANALISI: Estrazione risultati completata, lunghezza: 3210 caratteri
[2025-06-30 10:42:09] [ANALYSIS API] FASE DI ANALISI: elaborazione completata con successo.
[2025-06-30 10:42:16] [GENERAL] Salvataggio analisi recente: user_id=2, analysis_id=1751280129744, title=a_test_pdf, tokens=58, salvataggio_esplicito=no
[2025-06-30 10:42:16] [GENERAL] save_recent_analysis: Tipo utente identificato: wordpress (ID: 2)
[2025-06-30 10:42:16] [GENERAL] Utente WordPress ID: 2 - utilizzando cost_per_token: 0.01
[2025-06-30 10:42:16] [GENERAL] Statistiche aggiornate per utente WordPress 2: tokens=5873, tot_cost=18.47, credits=54.40, actual_cost=0.58, analyses=31, cost_per_token=0.01000
[2025-06-30 10:42:19] [GENERAL] Richiesta analisi recenti per utente ID: 2
[2025-06-30 10:42:19] [GENERAL] Trovate 5 analisi recenti per utente ID: 2
[2025-06-30 10:42:23] [GENERAL] Richiesta analisi recenti per utente ID: 2
[2025-06-30 10:42:23] [GENERAL] Trovate 5 analisi recenti per utente ID: 2
[2025-06-30 10:43:57] [GENERAL] export_analysis_pdf called with the following parameters:
[2025-06-30 10:43:57] [GENERAL] - include_query: yes
[2025-06-30 10:43:57] [GENERAL] - include_full_analysis: yes
[2025-06-30 10:43:57] [GENERAL] - include_key_points: yes
[2025-06-30 10:43:57] [GENERAL] - include_original_doc: yes
[2025-06-30 10:43:57] [GENERAL] - using analysis_html param: yes
[2025-06-30 10:43:57] [GENERAL] - using analysis param: no
[2025-06-30 10:43:57] [GENERAL] - content length: 3492
[2025-06-30 10:43:57] [GENERAL] - is_preview: false
[2025-06-30 10:43:58] [GENERAL] Processing SVG for PDF export, HTML length: 3492
[2025-06-30 10:43:58] [GENERAL] SVG processing for PDF completed
[2025-06-30 10:43:58] [GENERAL] Processing SVG for PDF export, HTML length: 485
[2025-06-30 10:43:58] [GENERAL] SVG processing for PDF completed
[2025-06-30 10:43:59] [GENERAL] PDF added with 1 pages
[2025-06-30 10:51:27] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-06-30 10:51:27] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-06-30 10:51:27] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-06-30 10:51:29] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-06-30 10:51:29] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-06-30 10:51:29] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-01 06:47:22] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-01 06:47:22] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-01 06:47:22] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-01 06:47:25] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-01 06:47:25] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-01 06:47:25] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-01 06:47:58] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-01 06:47:58] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-01 06:47:58] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-01 06:48:01] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-01 06:48:01] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-01 06:48:01] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-04 08:12:06] [GENERAL] Cleared  old errors (older than 7 days)
[2025-07-04 08:12:07] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-07-04 10:26:25] [GENERAL] Cleared  old errors (older than 7 days)
[2025-07-04 10:26:25] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-07-04 11:58:40] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-07-04 11:58:43] [GENERAL] Cache set for key: predefined_queries (type: queries, duration: 600s)
[2025-07-04 11:58:47] [GENERAL] Cache set for key: office_addin_settings (type: settings, duration: 300s)
[2025-07-04 12:16:40] [GENERAL] User subscription table created or updated
[2025-07-04 12:16:40] [GENERAL] Subscription types table created or updated
[2025-07-04 12:16:41] [GENERAL] Database tables initialized for non-WP users statistics
[2025-07-04 12:16:41] [GENERAL] Database tables initialized for non-WP users statistics
[2025-07-04 12:16:41] [GENERAL] Tabella statistiche già popolata, salto la migrazione
[2025-07-04 12:16:41] [GENERAL] Tabella statistiche già popolata, salto la migrazione
[2025-07-04 12:39:59] [GENERAL] User subscription table created or updated
[2025-07-04 12:39:59] [GENERAL] Subscription types table created or updated
[2025-07-04 12:39:59] [GENERAL] Database tables initialized for non-WP users statistics
[2025-07-04 12:39:59] [GENERAL] Database tables initialized for non-WP users statistics
[2025-07-04 12:39:59] [GENERAL] Tabella statistiche già popolata, salto la migrazione
[2025-07-04 12:39:59] [GENERAL] Tabella statistiche già popolata, salto la migrazione
[2025-07-05 07:46:30] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 07:46:30] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 07:46:30] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 07:46:33] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 07:46:33] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 07:46:33] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 07:47:00] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 07:47:00] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 07:47:00] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 07:47:01] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 07:47:01] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 07:47:01] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 07:48:21] [GENERAL] Sending chat message to API: Come si costruisce un fondo di emergenza?
[2025-07-05 07:48:27] [GENERAL] Chat API response received, status code: 200
[2025-07-05 07:48:27] [GENERAL] Chat AI reply extracted successfully, length: 1157
[2025-07-05 07:51:31] [GENERAL] Sending chat message to API: Come si sceglie il giusto KPI per misurare la performance aziendale?
[2025-07-05 07:52:30] [GENERAL] Chat API response received, status code: 200
[2025-07-05 07:52:30] [GENERAL] Chat AI reply extracted successfully, length: 1503
[2025-07-05 09:28:40] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 09:28:40] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 09:28:40] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 09:28:44] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 09:28:44] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 09:28:44] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 10:26:39] [GENERAL] Cleared  old errors (older than 7 days)
[2025-07-05 10:26:39] [GENERAL] Scheduled cleanup: deleted  old errors
[2025-07-05 10:26:45] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 10:26:45] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 10:26:45] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 10:26:47] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 10:26:47] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 10:26:47] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 10:34:24] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 10:34:25] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 10:34:25] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 10:34:26] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 10:34:26] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 10:34:26] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 10:40:31] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 10:40:31] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 10:40:32] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 10:40:36] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 10:40:36] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 10:40:36] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 10:42:17] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 10:42:17] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 10:42:17] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 10:42:19] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 10:42:19] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 10:42:19] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 10:44:22] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 10:44:22] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 10:44:22] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 10:44:24] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 10:44:24] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 10:44:24] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 10:49:06] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 10:49:07] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 10:49:07] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 10:49:09] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 10:49:09] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 10:49:09] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 10:49:33] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 10:49:33] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 10:49:33] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 10:49:39] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 10:49:39] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 10:49:39] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 10:50:07] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 10:50:07] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 10:50:07] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 10:50:18] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 10:50:18] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 10:50:18] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 10:53:37] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 10:53:37] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 10:53:37] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 10:53:47] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 10:53:47] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 10:53:47] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 10:53:52] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 10:53:52] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 10:53:52] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 10:53:59] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 10:53:59] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 10:53:59] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 10:54:25] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 10:54:25] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 10:54:25] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 10:54:27] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 10:54:27] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 10:54:27] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 10:54:32] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 10:54:32] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 10:54:32] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 10:54:35] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 10:54:35] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 10:54:35] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 10:55:19] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 10:55:19] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 10:55:19] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 10:55:21] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 10:55:21] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 10:55:21] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 11:04:49] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 11:04:49] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 11:04:49] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 11:04:54] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 11:04:54] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 11:04:54] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 11:05:48] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 11:05:48] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 11:05:48] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 11:05:50] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 11:05:51] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 11:05:51] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 11:06:25] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 11:06:25] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 11:06:25] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 11:06:28] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 11:06:28] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 11:06:28] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 11:10:07] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 11:10:08] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 11:10:08] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 11:10:13] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 11:10:13] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 11:10:13] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 11:25:47] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 11:25:47] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 11:25:47] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 11:25:52] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 11:25:52] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 11:25:52] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 11:29:03] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 11:29:03] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 11:29:03] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 11:29:05] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 11:29:05] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 11:29:05] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 11:29:59] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 11:29:59] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 11:29:59] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 11:30:05] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 11:30:05] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 11:30:05] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 11:31:55] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 11:31:55] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 11:31:55] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 11:31:58] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 11:31:58] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 11:31:58] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 11:33:43] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 11:33:43] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 11:33:43] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 11:33:45] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 11:33:45] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 11:33:45] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 11:44:35] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 11:44:35] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 11:44:35] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 11:44:38] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 11:44:38] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 11:44:38] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 11:51:52] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 11:51:52] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 11:51:52] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 11:51:56] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 11:51:56] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 11:51:56] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 11:52:18] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 11:52:18] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 11:52:18] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 11:52:20] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 11:52:20] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 11:52:20] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 11:53:14] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 11:53:14] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 11:53:14] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 11:53:16] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 11:53:16] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 11:53:16] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 11:53:52] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 11:53:52] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 11:53:52] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 11:53:54] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 11:53:54] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 11:53:54] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 11:56:17] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 11:56:17] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 11:56:17] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 11:56:19] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 11:56:19] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 11:56:19] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 12:02:24] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 12:02:24] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 12:02:24] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 12:02:26] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 12:02:26] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 12:02:26] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 12:03:37] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 12:03:37] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 12:03:37] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 12:03:40] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 12:03:40] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 12:03:40] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 12:06:42] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 12:06:42] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 12:06:42] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 12:06:45] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 12:06:45] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 12:06:45] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 12:09:41] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 12:09:41] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 12:09:41] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 12:09:43] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 12:09:43] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 12:09:43] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 12:13:10] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 12:13:10] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 12:13:10] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 12:13:12] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 12:13:12] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 12:13:12] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 12:16:47] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 12:16:47] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 12:16:47] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 12:16:52] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 12:16:52] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 12:16:52] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 12:32:12] [GENERAL] User subscription table created or updated
[2025-07-05 12:32:12] [GENERAL] Subscription types table created or updated
[2025-07-05 12:32:12] [GENERAL] Database tables initialized for non-WP users statistics
[2025-07-05 12:32:12] [GENERAL] Database tables initialized for non-WP users statistics
[2025-07-05 12:32:12] [GENERAL] Tabella statistiche già popolata, salto la migrazione
[2025-07-05 12:32:12] [GENERAL] Tabella statistiche già popolata, salto la migrazione
[2025-07-05 12:42:29] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 12:42:29] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 12:42:29] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 12:42:32] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 12:42:32] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 12:42:32] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 12:44:42] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 12:44:42] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 12:44:42] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 12:44:44] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 12:44:44] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 12:44:44] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 12:47:52] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 12:47:52] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 12:47:53] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 12:47:54] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 12:47:54] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 12:47:55] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 14:30:15] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 14:30:15] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 14:30:15] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 14:30:18] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 14:30:18] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 14:30:18] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 14:33:54] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 14:33:54] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 14:33:54] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 14:33:58] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 14:33:58] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 14:33:58] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 14:34:42] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 14:34:42] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 14:34:42] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 14:34:44] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 14:34:44] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 14:34:44] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 14:45:46] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 14:45:46] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 14:45:46] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 14:45:51] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 14:45:51] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 14:45:51] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 14:50:17] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 14:50:17] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 14:50:17] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 14:50:19] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 14:50:19] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 14:50:19] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 14:53:01] [GENERAL] Sending chat message to API: Come funziona il concetto di duration nelle obbligazioni?
[2025-07-05 14:54:02] [GENERAL] Chat API request error: cURL error 28: Operation timed out after 60000 milliseconds with 1134 bytes received
[2025-07-05 14:54:51] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 14:54:51] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 14:54:51] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 14:54:54] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 14:54:54] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 14:54:54] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:03:53] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:03:53] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:03:53] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:03:56] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:03:56] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:03:56] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:09:30] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:09:30] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:09:30] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:09:32] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:09:32] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:09:32] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:10:58] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:10:58] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:10:58] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:11:00] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:11:00] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:11:00] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:11:53] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:11:53] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:11:53] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:11:57] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:11:57] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:11:58] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:13:21] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:13:21] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:13:21] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:13:24] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:13:24] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:13:24] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:17:45] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:17:45] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:17:45] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:17:48] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:17:48] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:17:48] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:18:18] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:18:18] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:18:18] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:18:20] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:18:20] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:18:20] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:21:26] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:21:26] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:21:26] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:21:30] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:21:30] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:21:30] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:25:14] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:25:14] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:25:14] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:25:16] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:25:16] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:25:16] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:26:50] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:26:50] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:26:50] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:26:52] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:26:52] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:26:52] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:28:18] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:28:18] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:28:18] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:28:20] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:28:20] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:28:20] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:28:57] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:28:57] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:28:57] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:28:59] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:28:59] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:28:59] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:34:53] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:34:53] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:34:53] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:34:55] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:34:55] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:34:55] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:36:07] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:36:07] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:36:07] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:36:09] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:36:09] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:36:09] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:39:15] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:39:15] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:39:15] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:39:17] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:39:17] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:39:17] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:40:13] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:40:13] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:40:13] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:40:15] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:40:15] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:40:15] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:42:14] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:42:14] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:42:14] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:42:16] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:42:16] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:42:16] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:43:00] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:43:00] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:43:01] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:43:03] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:43:03] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:43:03] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:43:06] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:43:06] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:43:06] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:43:11] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:43:11] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:43:11] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:43:29] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:43:29] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:43:29] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:43:31] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:43:31] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:43:31] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:44:16] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:44:16] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:44:16] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 15:44:18] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:44:18] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:44:18] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:48:42] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 15:48:42] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 15:48:42] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 15:48:44] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 15:48:44] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 15:48:44] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 16:01:49] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 16:01:49] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 16:01:49] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 16:01:52] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 16:01:52] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 16:01:52] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 16:06:39] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 16:06:39] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 16:06:39] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 16:06:41] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 16:06:41] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 16:06:41] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 16:20:45] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 16:20:45] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 16:20:45] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 16:20:49] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 16:20:49] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 16:20:49] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 16:38:58] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 16:38:58] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 16:38:58] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 16:39:01] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 16:39:01] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 16:39:01] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 16:42:49] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 16:42:49] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 16:42:49] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 16:42:52] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 16:42:52] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 16:42:52] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 16:49:03] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 16:49:03] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 16:49:03] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 16:49:06] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 16:49:06] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 16:49:06] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-05 16:49:34] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-05 16:49:34] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-05 16:49:34] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-05 16:49:37] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-05 16:49:37] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-05 16:49:37] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 06:06:23] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 06:06:23] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 06:06:23] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 06:06:31] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 06:06:31] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 06:06:31] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 06:06:36] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:07:22] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:09:11] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 06:09:11] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 06:09:11] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 06:09:14] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 06:09:14] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 06:09:14] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 06:09:16] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:09:40] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:11:22] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 06:11:22] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 06:11:22] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 06:11:25] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 06:11:25] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 06:11:25] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 06:11:32] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:11:57] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:12:14] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:12:43] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:13:14] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:13:43] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:14:19] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:15:28] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 06:15:28] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 06:15:28] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 06:15:33] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 06:15:33] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 06:15:33] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 06:15:41] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:16:05] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:16:51] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:18:38] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 06:18:38] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 06:18:38] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 06:18:40] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 06:18:40] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 06:18:40] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 06:18:47] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:19:17] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:19:41] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:20:10] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:20:40] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:21:39] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:21:49] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:22:12] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:22:44] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:27:20] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 06:27:20] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 06:27:21] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 06:27:23] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 06:27:23] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 06:27:23] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 06:27:25] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:27:27] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 06:27:27] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 06:27:27] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 06:27:32] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 06:27:32] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 06:27:32] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 06:27:34] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:27:39] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:27:54] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:28:05] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:28:25] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:28:34] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:28:54] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:29:43] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:29:47] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:30:17] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:30:34] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:30:47] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:31:05] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:31:35] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:31:44] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:32:06] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:32:35] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:32:59] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:33:11] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 06:33:11] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 06:33:11] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 06:33:13] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 06:33:13] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 06:33:13] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 06:33:15] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:33:49] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:34:20] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 06:34:20] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 06:34:20] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 06:34:22] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 06:34:22] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 06:34:22] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 06:34:24] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:34:46] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:34:51] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:35:23] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:35:46] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:35:55] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:36:46] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:36:49] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:37:43] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:37:45] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:38:46] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:38:49] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:39:44] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:39:46] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:40:05] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:40:17] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 06:40:17] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 06:40:17] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 06:40:19] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 06:40:19] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 06:40:19] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 06:40:21] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:40:43] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:40:49] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:41:50] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:42:05] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 06:42:05] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 06:42:05] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 06:42:11] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 06:42:11] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 06:42:11] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 06:42:16] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:42:28] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:42:47] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:43:11] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:43:24] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:44:25] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:45:56] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 06:45:56] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 06:45:56] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 06:46:08] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 06:46:08] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 06:46:08] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 06:46:12] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:46:22] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 06:46:22] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 06:46:22] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 06:46:27] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 06:46:27] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 06:46:27] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 06:46:31] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:46:39] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:47:05] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:47:09] [GENERAL] Inizializzazione processo OCR per current_ratio.png (5.1 KB)
[2025-07-06 06:47:11] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:47:21] [GENERAL] Tesseract.js caricato con successo
[2025-07-06 06:47:25] [GENERAL] Avvio riconoscimento OCR con lingue: ita, eng
[2025-07-06 06:47:29] [GENERAL] OCR fase: loading tesseract core
[2025-07-06 06:47:33] [GENERAL] OCR fase: loading tesseract core
[2025-07-06 06:47:37] [GENERAL] OCR fase: initializing tesseract
[2025-07-06 06:47:41] [GENERAL] OCR fase: initialized tesseract
[2025-07-06 06:47:45] [GENERAL] OCR fase: loading language traineddata
[2025-07-06 06:47:49] [GENERAL] OCR fase: loaded language traineddata
[2025-07-06 06:47:53] [GENERAL] OCR fase: initializing api
[2025-07-06 06:47:56] [GENERAL] OCR fase: initialized api
[2025-07-06 06:48:00] [GENERAL] Progresso OCR: 0%
[2025-07-06 06:48:04] [GENERAL] Progresso OCR: 80%
[2025-07-06 06:48:07] [GENERAL] Progresso OCR: 80%
[2025-07-06 06:48:11] [GENERAL] Progresso OCR: 100%
[2025-07-06 06:48:15] [GENERAL] Completato processo OCR. Estratti 158 caratteri.
[2025-07-06 06:48:19] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:48:23] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:48:26] [GENERAL] Upload directory: /var/www/html/claryfin/wp-content/uploads/document-viewer
[2025-07-06 06:48:26] [GENERAL] Upload directory exists: yes
[2025-07-06 06:48:26] [GENERAL] Upload directory writable: yes
[2025-07-06 06:48:26] [ANALYSIS API] No document content available for analysis
[2025-07-06 06:48:31] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:48:35] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:48:43] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:48:51] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:48:57] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:49:12] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:49:16] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:49:44] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:50:08] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:50:39] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:51:08] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:51:38] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:52:45] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:53:07] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:53:37] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:54:08] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:54:45] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:55:46] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:56:47] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:58:00] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:58:48] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 06:59:52] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:00:45] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:01:43] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:02:07] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:02:25] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 07:02:25] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 07:02:25] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 07:02:32] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 07:02:32] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 07:02:32] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 07:02:34] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:02:38] [GENERAL] Upload directory: /var/www/html/claryfin/wp-content/uploads/document-viewer
[2025-07-06 07:02:38] [GENERAL] Upload directory exists: yes
[2025-07-06 07:02:38] [GENERAL] Upload directory writable: yes
[2025-07-06 07:02:38] [GENERAL] Received file upload: Array
(
    [name] => a_test_pdf.pdf
    [type] => application/pdf
    [tmp_name] => /tmp/phpJo7QXq
    [error] => 0
    [size] => 474569
)

[2025-07-06 07:02:39] [ANALYSIS API] File details - Name: a_test_pdf.pdf, Type: application/pdf, Size: 474569 bytes
[2025-07-06 07:02:39] [GENERAL] File moved successfully to: /var/www/html/claryfin/wp-content/uploads/document-viewer/doc_686a1f8f054a3_a_test_pdf.pdf
[2025-07-06 07:02:39] [GENERAL] File exists at destination: yes
[2025-07-06 07:02:39] [GENERAL] File is readable: yes
[2025-07-06 07:02:39] [GENERAL] File size: 474569 bytes
[2025-07-06 07:02:39] [ANALYSIS API] Avvio estrazione contenuto da file PDF
[2025-07-06 07:02:39] [ANALYSIS API] Estrazione testo PDF: inizio processo per doc_686a1f8f054a3_a_test_pdf.pdf (463.45 KB)
[2025-07-06 07:02:39] [ANALYSIS API] PDF: inizializzazione estrazione testo
[2025-07-06 07:02:39] [ANALYSIS API] PDF: tentativo di estrazione diretto in corso...
[2025-07-06 07:02:39] [ANALYSIS API] PDF: estrazione diretta completata con successo
[2025-07-06 07:02:39] [ANALYSIS API] PDF: estrazione completata. Anteprima del testo estratto: 
Current ratio ATTIVITA' CORRENTI 
Indice di liquidità 
corrente PASSIVITA' CORRENTI 
 
 
 Actual PY Delta % 
Attività correnti 8.371.670 9.231.067 -9,31% 
Passività correnti 3.886.086 5.720.675 -32,07% 
Current ratio 2,15 1,61 33,50%
[2025-07-06 07:02:39] [ANALYSIS API] PDF: lunghezza totale del testo estratto: 236 caratteri
[2025-07-06 07:02:39] [ANALYSIS API] PDF extraction complete. Content length: 236
[2025-07-06 07:02:39] [ANALYSIS API] Temporary file removed
[2025-07-06 07:02:39] [GENERAL] Content extracted successfully, length: 236
[2025-07-06 07:02:56] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:03:00] [GENERAL] Upload directory: /var/www/html/claryfin/wp-content/uploads/document-viewer
[2025-07-06 07:03:00] [GENERAL] Upload directory exists: yes
[2025-07-06 07:03:00] [GENERAL] Upload directory writable: yes
[2025-07-06 07:03:00] [ANALYSIS API] No document content available for analysis
[2025-07-06 07:03:26] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:03:56] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:04:43] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:05:46] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:06:35] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:07:04] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 07:07:04] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 07:07:04] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 07:07:07] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 07:07:07] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 07:07:07] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 07:07:09] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:07:14] [GENERAL] Upload directory: /var/www/html/claryfin/wp-content/uploads/document-viewer
[2025-07-06 07:07:14] [GENERAL] Upload directory exists: yes
[2025-07-06 07:07:14] [GENERAL] Upload directory writable: yes
[2025-07-06 07:07:14] [GENERAL] Received file upload: Array
(
    [name] => a_test_pdf.pdf
    [type] => application/pdf
    [tmp_name] => /tmp/phpkHf9nB
    [error] => 0
    [size] => 474569
)

[2025-07-06 07:07:14] [ANALYSIS API] File details - Name: a_test_pdf.pdf, Type: application/pdf, Size: 474569 bytes
[2025-07-06 07:07:14] [GENERAL] File moved successfully to: /var/www/html/claryfin/wp-content/uploads/document-viewer/doc_686a20a21f293_a_test_pdf.pdf
[2025-07-06 07:07:14] [GENERAL] File exists at destination: yes
[2025-07-06 07:07:14] [GENERAL] File is readable: yes
[2025-07-06 07:07:14] [GENERAL] File size: 474569 bytes
[2025-07-06 07:07:14] [ANALYSIS API] Avvio estrazione contenuto da file PDF
[2025-07-06 07:07:14] [ANALYSIS API] Estrazione testo PDF: inizio processo per doc_686a20a21f293_a_test_pdf.pdf (463.45 KB)
[2025-07-06 07:07:14] [ANALYSIS API] PDF: inizializzazione estrazione testo
[2025-07-06 07:07:14] [ANALYSIS API] PDF: tentativo di estrazione diretto in corso...
[2025-07-06 07:07:14] [ANALYSIS API] PDF: estrazione diretta completata con successo
[2025-07-06 07:07:14] [ANALYSIS API] PDF: estrazione completata. Anteprima del testo estratto: 
Current ratio ATTIVITA' CORRENTI 
Indice di liquidità 
corrente PASSIVITA' CORRENTI 
 
 
 Actual PY Delta % 
Attività correnti 8.371.670 9.231.067 -9,31% 
Passività correnti 3.886.086 5.720.675 -32,07% 
Current ratio 2,15 1,61 33,50%
[2025-07-06 07:07:14] [ANALYSIS API] PDF: lunghezza totale del testo estratto: 236 caratteri
[2025-07-06 07:07:14] [ANALYSIS API] PDF extraction complete. Content length: 236
[2025-07-06 07:07:14] [ANALYSIS API] Temporary file removed
[2025-07-06 07:07:14] [GENERAL] Content extracted successfully, length: 236
[2025-07-06 07:07:31] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:07:38] [GENERAL] Upload directory: /var/www/html/claryfin/wp-content/uploads/document-viewer
[2025-07-06 07:07:38] [GENERAL] Upload directory exists: yes
[2025-07-06 07:07:38] [GENERAL] Upload directory writable: yes
[2025-07-06 07:07:38] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione con modello AI
[2025-07-06 07:07:38] [ANALYSIS API] Invio richiesta di analisi a: https://openrouter.ai/api/v1/chat/completions
[2025-07-06 07:07:38] [ANALYSIS API] Utilizzo modello: deepseek/deepseek-chat-v3-0324:free
[2025-07-06 07:07:38] [ANALYSIS API] FASE DI ANALISI: richiesta inviata al servizio AI, in attesa di risposta...
[2025-07-06 07:08:49] [ANALYSIS API] FASE DI ANALISI: risposta API ricevuta, codice: 200
[2025-07-06 07:08:49] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione risposta
[2025-07-06 07:09:00] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:09:05] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:09:18] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:09:35] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:10:02] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:10:49] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:11:45] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:12:19] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:12:31] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:12:48] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 07:12:48] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 07:12:48] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 07:12:50] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 07:12:50] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 07:12:51] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 07:12:55] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:13:00] [GENERAL] Upload directory: /var/www/html/claryfin/wp-content/uploads/document-viewer
[2025-07-06 07:13:00] [GENERAL] Upload directory exists: yes
[2025-07-06 07:13:00] [GENERAL] Upload directory writable: yes
[2025-07-06 07:13:00] [GENERAL] Received file upload: Array
(
    [name] => a_test_pdf.pdf
    [type] => application/pdf
    [tmp_name] => /tmp/phpE3GhKr
    [error] => 0
    [size] => 474569
)

[2025-07-06 07:13:00] [ANALYSIS API] File details - Name: a_test_pdf.pdf, Type: application/pdf, Size: 474569 bytes
[2025-07-06 07:13:00] [GENERAL] File moved successfully to: /var/www/html/claryfin/wp-content/uploads/document-viewer/doc_686a21fc45f5b_a_test_pdf.pdf
[2025-07-06 07:13:00] [GENERAL] File exists at destination: yes
[2025-07-06 07:13:00] [GENERAL] File is readable: yes
[2025-07-06 07:13:00] [GENERAL] File size: 474569 bytes
[2025-07-06 07:13:00] [ANALYSIS API] Avvio estrazione contenuto da file PDF
[2025-07-06 07:13:00] [ANALYSIS API] Estrazione testo PDF: inizio processo per doc_686a21fc45f5b_a_test_pdf.pdf (463.45 KB)
[2025-07-06 07:13:00] [ANALYSIS API] PDF: inizializzazione estrazione testo
[2025-07-06 07:13:00] [ANALYSIS API] PDF: tentativo di estrazione diretto in corso...
[2025-07-06 07:13:00] [ANALYSIS API] PDF: estrazione diretta completata con successo
[2025-07-06 07:13:00] [ANALYSIS API] PDF: estrazione completata. Anteprima del testo estratto: 
Current ratio ATTIVITA' CORRENTI 
Indice di liquidità 
corrente PASSIVITA' CORRENTI 
 
 
 Actual PY Delta % 
Attività correnti 8.371.670 9.231.067 -9,31% 
Passività correnti 3.886.086 5.720.675 -32,07% 
Current ratio 2,15 1,61 33,50%
[2025-07-06 07:13:00] [ANALYSIS API] PDF: lunghezza totale del testo estratto: 236 caratteri
[2025-07-06 07:13:00] [ANALYSIS API] PDF extraction complete. Content length: 236
[2025-07-06 07:13:00] [ANALYSIS API] Temporary file removed
[2025-07-06 07:13:00] [GENERAL] Content extracted successfully, length: 236
[2025-07-06 07:13:17] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:13:25] [GENERAL] Upload directory: /var/www/html/claryfin/wp-content/uploads/document-viewer
[2025-07-06 07:13:25] [GENERAL] Upload directory exists: yes
[2025-07-06 07:13:25] [GENERAL] Upload directory writable: yes
[2025-07-06 07:13:25] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione con modello AI
[2025-07-06 07:13:25] [ANALYSIS API] Invio richiesta di analisi a: https://openrouter.ai/api/v1/chat/completions
[2025-07-06 07:13:25] [ANALYSIS API] Utilizzo modello: deepseek/deepseek-chat-v3-0324:free
[2025-07-06 07:13:25] [ANALYSIS API] FASE DI ANALISI: richiesta inviata al servizio AI, in attesa di risposta...
[2025-07-06 07:14:00] [ANALYSIS API] FASE DI ANALISI: risposta API ricevuta, codice: 200
[2025-07-06 07:14:00] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione risposta
[2025-07-06 07:14:09] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:14:14] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:15:07] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 07:15:07] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 07:15:07] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 07:15:10] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 07:15:10] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 07:15:10] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 07:15:13] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:15:17] [GENERAL] Upload directory: /var/www/html/claryfin/wp-content/uploads/document-viewer
[2025-07-06 07:15:17] [GENERAL] Upload directory exists: yes
[2025-07-06 07:15:17] [GENERAL] Upload directory writable: yes
[2025-07-06 07:15:17] [GENERAL] Received file upload: Array
(
    [name] => a_test_pdf.pdf
    [type] => application/pdf
    [tmp_name] => /tmp/phpAtwThS
    [error] => 0
    [size] => 474569
)

[2025-07-06 07:15:17] [ANALYSIS API] File details - Name: a_test_pdf.pdf, Type: application/pdf, Size: 474569 bytes
[2025-07-06 07:15:17] [GENERAL] File moved successfully to: /var/www/html/claryfin/wp-content/uploads/document-viewer/doc_686a22855cee8_a_test_pdf.pdf
[2025-07-06 07:15:17] [GENERAL] File exists at destination: yes
[2025-07-06 07:15:17] [GENERAL] File is readable: yes
[2025-07-06 07:15:17] [GENERAL] File size: 474569 bytes
[2025-07-06 07:15:17] [ANALYSIS API] Avvio estrazione contenuto da file PDF
[2025-07-06 07:15:17] [ANALYSIS API] Estrazione testo PDF: inizio processo per doc_686a22855cee8_a_test_pdf.pdf (463.45 KB)
[2025-07-06 07:15:17] [ANALYSIS API] PDF: inizializzazione estrazione testo
[2025-07-06 07:15:17] [ANALYSIS API] PDF: tentativo di estrazione diretto in corso...
[2025-07-06 07:15:17] [ANALYSIS API] PDF: estrazione diretta completata con successo
[2025-07-06 07:15:17] [ANALYSIS API] PDF: estrazione completata. Anteprima del testo estratto: 
Current ratio ATTIVITA' CORRENTI 
Indice di liquidità 
corrente PASSIVITA' CORRENTI 
 
 
 Actual PY Delta % 
Attività correnti 8.371.670 9.231.067 -9,31% 
Passività correnti 3.886.086 5.720.675 -32,07% 
Current ratio 2,15 1,61 33,50%
[2025-07-06 07:15:17] [ANALYSIS API] PDF: lunghezza totale del testo estratto: 236 caratteri
[2025-07-06 07:15:17] [ANALYSIS API] PDF extraction complete. Content length: 236
[2025-07-06 07:15:17] [ANALYSIS API] Temporary file removed
[2025-07-06 07:15:17] [GENERAL] Content extracted successfully, length: 236
[2025-07-06 07:15:32] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:15:34] [GENERAL] Upload directory: /var/www/html/claryfin/wp-content/uploads/document-viewer
[2025-07-06 07:15:34] [GENERAL] Upload directory exists: yes
[2025-07-06 07:15:34] [GENERAL] Upload directory writable: yes
[2025-07-06 07:15:34] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione con modello AI
[2025-07-06 07:15:34] [ANALYSIS API] Invio richiesta di analisi a: https://openrouter.ai/api/v1/chat/completions
[2025-07-06 07:15:34] [ANALYSIS API] Utilizzo modello: deepseek/deepseek-chat-v3-0324:free
[2025-07-06 07:15:34] [ANALYSIS API] FASE DI ANALISI: richiesta inviata al servizio AI, in attesa di risposta...
[2025-07-06 07:16:40] [ANALYSIS API] FASE DI ANALISI: risposta API ricevuta, codice: 200
[2025-07-06 07:16:40] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione risposta
[2025-07-06 07:16:48] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:16:56] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:17:08] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:17:37] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:18:48] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:19:46] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:20:46] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:21:04] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:22:08] [GENERAL] Reset statistiche al caricamento del widget per utente: ID=2, Tipo=wordpress
[2025-07-06 07:22:08] [GENERAL] SQL Update eseguito con successo: 1 righe aggiornate
[2025-07-06 07:22:08] [GENERAL] Spesa effettiva resettata con successo per utente ID: 2 (spesa totale preservata)
[2025-07-06 07:22:11] [GENERAL] Recupero statistiche per utente WordPress ID: 2
[2025-07-06 07:22:11] [GENERAL] Utente WordPress ID: 2, Ruolo: Administrator, Costo per token: 0.01
[2025-07-06 07:22:11] [GENERAL] Statistiche recuperate con successo per utente WordPress
[2025-07-06 07:22:13] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:22:17] [GENERAL] Upload directory: /var/www/html/claryfin/wp-content/uploads/document-viewer
[2025-07-06 07:22:17] [GENERAL] Upload directory exists: yes
[2025-07-06 07:22:17] [GENERAL] Upload directory writable: yes
[2025-07-06 07:22:17] [GENERAL] Received file upload: Array
(
    [name] => a_test_pdf.pdf
    [type] => application/pdf
    [tmp_name] => /tmp/phptYnX3e
    [error] => 0
    [size] => 474569
)

[2025-07-06 07:22:17] [ANALYSIS API] File details - Name: a_test_pdf.pdf, Type: application/pdf, Size: 474569 bytes
[2025-07-06 07:22:17] [GENERAL] File moved successfully to: /var/www/html/claryfin/wp-content/uploads/document-viewer/doc_686a242970d61_a_test_pdf.pdf
[2025-07-06 07:22:17] [GENERAL] File exists at destination: yes
[2025-07-06 07:22:17] [GENERAL] File is readable: yes
[2025-07-06 07:22:17] [GENERAL] File size: 474569 bytes
[2025-07-06 07:22:17] [ANALYSIS API] Avvio estrazione contenuto da file PDF
[2025-07-06 07:22:17] [ANALYSIS API] Estrazione testo PDF: inizio processo per doc_686a242970d61_a_test_pdf.pdf (463.45 KB)
[2025-07-06 07:22:17] [ANALYSIS API] PDF: inizializzazione estrazione testo
[2025-07-06 07:22:17] [ANALYSIS API] PDF: tentativo di estrazione diretto in corso...
[2025-07-06 07:22:17] [ANALYSIS API] PDF: estrazione diretta completata con successo
[2025-07-06 07:22:17] [ANALYSIS API] PDF: estrazione completata. Anteprima del testo estratto: 
Current ratio ATTIVITA' CORRENTI 
Indice di liquidità 
corrente PASSIVITA' CORRENTI 
 
 
 Actual PY Delta % 
Attività correnti 8.371.670 9.231.067 -9,31% 
Passività correnti 3.886.086 5.720.675 -32,07% 
Current ratio 2,15 1,61 33,50%
[2025-07-06 07:22:17] [ANALYSIS API] PDF: lunghezza totale del testo estratto: 236 caratteri
[2025-07-06 07:22:17] [ANALYSIS API] PDF extraction complete. Content length: 236
[2025-07-06 07:22:17] [ANALYSIS API] Temporary file removed
[2025-07-06 07:22:17] [GENERAL] Content extracted successfully, length: 236
[2025-07-06 07:22:33] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:22:43] [GENERAL] Document Analizer: richiesta di analisi ricevuta
[2025-07-06 07:22:43] [GENERAL] Upload directory: /var/www/html/claryfin/wp-content/uploads/document-viewer
[2025-07-06 07:22:43] [GENERAL] Upload directory exists: yes
[2025-07-06 07:22:43] [GENERAL] Upload directory writable: yes
[2025-07-06 07:22:43] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione con modello AI
[2025-07-06 07:22:43] [ANALYSIS API] Invio richiesta di analisi a: https://openrouter.ai/api/v1/chat/completions
[2025-07-06 07:22:43] [ANALYSIS API] Utilizzo modello: deepseek/deepseek-chat-v3-0324:free
[2025-07-06 07:22:43] [ANALYSIS API] FASE DI ANALISI: richiesta inviata al servizio AI, in attesa di risposta...
[2025-07-06 07:23:30] [ANALYSIS API] FASE DI ANALISI: risposta API ricevuta, codice: 200
[2025-07-06 07:23:30] [ANALYSIS API] FASE DI ANALISI: inizio elaborazione risposta
[2025-07-06 07:23:43] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:23:46] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
[2025-07-06 07:24:02] [GENERAL] Credito sincronizzato dal database per utente ID: 2, Tipo: wordpress, Credito: 54.4
