!function(){"use strict";var e={d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{ELEMENT_STYLE_CHANGE_EVENT:function(){return T},createElementStyle:function(){return j},deleteElementStyle:function(){return L},getAnchoredAncestorId:function(){return R},getAnchoredDescendantId:function(){return A},getContainer:function(){return l},getCurrentDocumentId:function(){return b},getElementLabel:function(){return h},getElementSetting:function(){return i},getElementStyles:function(){return g},getElements:function(){return S},getLinkInLinkRestriction:function(){return P},getSelectedElements:function(){return f},getWidgetsCache:function(){return m},isElementAnchored:function(){return x},selectElement:function(){return c},styleRerenderEvents:function(){return C},updateElementSettings:function(){return I},updateElementStyle:function(){return V},useElementSetting:function(){return u},useElementSettings:function(){return d},useElementType:function(){return a},useParentElement:function(){return E},useSelectedElement:function(){return p}});var n=window.elementorV2.editorV1Adapters,r=window.elementorV2.utils,o=window.elementorV2.editorProps,s=window.elementorV2.editorStyles;function l(e){const t=window,n=t.elementor?.getContainer?.(e);return n??null}var c=e=>{try{const t=l(e);(0,n.__privateRunCommand)("document/elements/select",{container:t})}catch{}},i=(e,t)=>{const n=l(e),r=n?.settings?.get(t);return r??null},u=(e,t)=>(0,n.__privateUseListenTo)((0,n.commandEndEvent)("document/elements/set-settings"),(()=>i(e,t)),[e,t]),d=(e,t)=>(0,n.__privateUseListenTo)((0,n.commandEndEvent)("document/elements/set-settings"),(()=>t.reduce(((t,n)=>{const r=i(e,n);return null!==r&&(t[n]=r),t}),{})),[e,...t]);function m(){const e=window;return e?.elementor?.widgetsCache||null}function a(e){return(0,n.__privateUseListenTo)((0,n.commandEndEvent)("editor/documents/load"),(()=>{if(!e)return null;const t=m(),n=t?.[e];return n?.atomic_controls&&n?.atomic_props_schema?{key:e,controls:n.atomic_controls,propsSchema:n.atomic_props_schema,title:n.title}:null}),[e])}function f(){const e=window;return(e.elementor?.selection?.getElements?.()??[]).reduce(((e,t)=>{const n=t.model.get("widgetType")||t.model.get("elType");return n&&e.push({id:t.model.get("id"),type:n}),e}),[])}function p(){const e=(0,n.__privateUseListenTo)([(0,n.commandEndEvent)("document/elements/select"),(0,n.commandEndEvent)("document/elements/deselect"),(0,n.commandEndEvent)("document/elements/select-all"),(0,n.commandEndEvent)("document/elements/deselect-all")],f),[t]=e,r=a(t?.type);return 1===e.length&&r?{element:t,elementType:r}:{element:null,elementType:null}}function E(e){return(0,n.__privateUseListenTo)([(0,n.commandEndEvent)("document/elements/create")],(()=>{if(!e)return null;const t=window,n=t?.elementor?.getContainer?.(e);return n?n.parent:null}),[e])}var g=e=>{const t=l(e);return t?.model.get("styles")||null},y=(0,r.createError)({code:"element_not_found",message:"Element not found."}),w=(0,r.createError)({code:"style_not_found",message:"Style not found."}),v=(0,r.createError)({code:"element_type_not_exists",message:"Element type does not exist."}),_=(0,r.createError)({code:"element_label_not_exists",message:"Element label does not exist."});function h(e){const t=l(e),n=t?.model.get("widgetType")||t?.model.get("elType");if(!n)throw new v({context:{elementId:e}});const r=m()?.[n]?.title;if(!r)throw new _({context:{elementType:n}});return r}function S(e){const t=e?l(e):function(){const e=window;return e.elementor?.documents?.getCurrent?.()?.container??null}();if(!t)return[];const n=[...t.model.get("elements")??[]].flatMap((e=>S(e.get("id"))));return[t,...n]}function b(){const e=window;return e.elementor?.documents?.getCurrentId?.()??null}var I=({id:e,props:t,withHistory:r=!0})=>{const o={container:l(e),settings:{...t}};r?(0,n.__privateRunCommandSync)("document/elements/settings",o):(0,n.__privateRunCommandSync)("document/elements/set-settings",o,{internal:!0})},T="elementor/editor-v2/editor-elements/style",C=[(0,n.commandEndEvent)("document/elements/create"),(0,n.commandEndEvent)("document/elements/duplicate"),(0,n.commandEndEvent)("document/elements/import"),(0,n.commandEndEvent)("document/elements/paste"),(0,n.windowEvent)(T)];function O(e,t){const r=l(e);if(!r)throw new y({context:{elementId:e}});const s=Object.keys(r.model.get("styles")??{}),c=function(e,t){const n=structuredClone(e.model.get("styles"))??{},r=Object.entries(t(n)).map((([e,t])=>(t.variants=function(e){return e.variants.filter((({props:e})=>Object.keys(e).length>0))}(t),[e,t]))).filter((([,e])=>!function(e){return 0===e.variants.length}(e))),o=Object.fromEntries(r);return e.model.set("styles",o),o}(r,t);return function(e,{oldIds:t,newIds:n}){const r=t.filter((e=>!n.includes(e))),s=structuredClone(function(e){return Object.entries(e.settings.toJSON()).filter((e=>{const[,t]=e;return o.classesPropTypeUtil.isValid(t)}))}(e));s.forEach((([,e])=>{e.value=e.value.filter((e=>!r.includes(e)))})),I({id:e.id,props:Object.fromEntries(s),withHistory:!1})}(r,{oldIds:s,newIds:Object.keys(c)}),window.dispatchEvent(new CustomEvent(T)),(0,n.__privateRunCommandSync)("document/save/set-is-modified",{status:!0},{internal:!0}),c}function j({styleId:e,elementId:t,classesProp:n,label:r,meta:l,props:c,additionalVariants:u=[]}){let d=e;return O(t,(e=>{d??=(0,s.generateId)(`e-${t}-`,Object.keys(e));const m=[{meta:l,props:c},...u];return e[d]={id:d,label:r,type:"class",variants:m},function(e,t,n){const r=i(e,t),s=o.classesPropTypeUtil.create((e=>[...e??[],n]),{base:r});I({id:e,props:{[t]:s},withHistory:!1})}(t,n,d),e})),d}function V(e){O(e.elementId,(t=>{const n=t[e.styleId];if(!n)throw new w({context:{styleId:e.styleId}});const r=(0,s.getVariantByMeta)(n,e.meta);return r?r.props=(0,o.mergeProps)(r.props,e.props):n.variants.push({meta:e.meta,props:e.props}),t}))}function L(e,t){O(e,(e=>(delete e[t],e)))}function P(e){const t=A(e);if(t)return{shouldRestrict:!0,reason:"descendant",elementId:t};const n=R(e);return n?{shouldRestrict:!0,reason:"ancestor",elementId:n}:{shouldRestrict:!1}}function A(e){const t=N(e);if(!t)return null;for(const n of Array.from(t.querySelectorAll("a"))){const t=U(n);if(t!==e)return t}return null}function R(e){const t=N(e);if(!t||null===t.parentElement)return null;const n=t.parentElement.closest("a");return n?U(n):null}function x(e){const t=N(e);return!!t&&(!!M(t.tagName)||k(t))}function k(e){for(const t of e.children)if(!H(t)){if(M(t.tagName))return!0;if(k(t))return!0}return!1}function U(e){return e.closest("[data-id]")?.dataset.id||null}function N(e){try{return l(e)?.view?.el||null}catch{return null}}function M(e){return"a"===e.toLowerCase()}function H(e){return e.hasAttribute("data-id")}(window.elementorV2=window.elementorV2||{}).editorElements=t}(),window.elementorV2.editorElements?.init?.();