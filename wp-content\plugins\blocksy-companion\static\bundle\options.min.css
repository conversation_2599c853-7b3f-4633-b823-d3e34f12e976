/**
 *  - v2.1.2
 * 
 * Copyright (c) 2025
 * Licensed GPLv2+
 */

#adminmenu #toplevel_page_ct-dashboard .wp-menu-image.svg{background-size:18px auto}#adminmenu #toplevel_page_ct-dashboard .wp-menu-image img{max-width:18px;height:auto;padding:8px 0 0 0}#fs_account tr.fs-field-plan form.button-group input.button{border-radius:3px}.fs-field-beta_program,[data-slug=blocksy-companion] .upgrade,#fs_account tr.fs-field-plan form.button-group .fs-change-plan,#adminmenu #toplevel_page_ct-dashboard a[href*=ct-dashboard-pricing]{display:none}.ct-import-export input[type=file]{display:none}.ct-import-export button{width:100%}.ct-import-export button.button-primary{display:flex;align-items:center;justify-content:center;gap:.5em}.ct-import-export .ct-attachment{margin-bottom:20px}.ct-option-position{display:grid;grid-template-columns:repeat(3, 1fr);grid-column-gap:7px;grid-row-gap:7px;margin:0}.ct-option-position li{height:40px;margin:0;cursor:pointer;border:1px solid var(--optionBorderColor);transition:border-color .1s ease,background .1s ease}.ct-option-position li.active{background:var(--ui-accent-color);border-color:var(--ui-accent-color)}.ct-option-position li:not(.active){background:rgba(255,255,255,.5)}.ct-option-position li:hover{border-color:var(--ui-accent-color)}.ct-option-position li:nth-child(1){border-top-left-radius:4px}.ct-option-position li:nth-child(3){border-top-right-radius:4px}.ct-option-position li:nth-child(7){border-bottom-left-radius:4px}.ct-option-position li:nth-child(9){border-bottom-right-radius:4px}.ct-condition-location .ct-controls-group{--options-vertical-spacing: 0;--options-horizontal-spacing: 0;--x-select-dropdown-width: calc(100% + 100px)}.ct-condition-location .ct-controls-group>section{position:relative;display:grid;grid-template-columns:1fr 85px;grid-column-gap:10px;grid-row-gap:20px;margin-bottom:20px;padding-bottom:20px;border-bottom:1px dashed rgba(0,0,0,.1)}.ct-condition-location .ct-controls-group>section .ct-remove-condition-location{position:absolute;width:26px;height:30px;top:0;inset-inline-end:-26px}.ct-condition-location .ct-controls-group>section .ct-control{padding-top:20px;border-top:1px dashed rgba(0,0,0,.1)}.ct-condition-location .ct-control{margin-top:0;grid-column:1/-1}.ct-condition-location .ct-control:not(:last-child){margin-bottom:20px;padding-bottom:20px;border-bottom:1px dashed rgba(0,0,0,.1)}.ct-condition-location .ct-control[data-design=inline] input{text-align:center;--input-width: 85px}.ct-condition-location .button{width:100%}.form-table .ct-term-screen-edit th label{display:flex;align-items:center;position:relative;pointer-events:none;--revert-button-offset: 17px}.form-table .ct-term-screen-edit .ct-attachment,.form-table .ct-term-screen-edit .ct-select-input,.form-table .ct-term-screen-edit .ct-option-input,.form-table .ct-term-screen-edit .ct-radio-option,.form-table .ct-term-screen-edit .ct-color-picker-container{max-width:95%;margin-bottom:8px}.form-table .ct-term-screen-edit .ct-color-picker-container{justify-content:flex-start}.form-table .ct-term-screen-edit .ct-option-switch{margin-inline-start:initial}#reviews-filter table.product-reviews .column-comment .ct-review-title{color:#50575e;font-size:13px;margin:0 0 10px 0}#reviews-filter table.product-reviews .column-comment .ct-review-images{display:flex;align-items:center;flex-wrap:wrap;gap:10px;margin-block:15px 10px}#reviews-filter table.product-reviews .column-comment .ct-review-images figure{width:40px;height:40px;margin:0}#reviews-filter table.product-reviews .column-comment .ct-review-images figure img{aspect-ratio:1/1;height:auto;object-fit:cover;border-radius:2px}.ct-builder-conditions-modal{--modal-width: 700px}.ct-builder-conditions-modal .ct-modal-scroll{padding-block:30px;border-top:1px solid var(--optionBorderColor)}.block-editor-page.post-type-ct_size_guide [data-reach-dialog-overlay],.block-editor-page.post-type-ct_product_tab [data-reach-dialog-overlay],.block-editor-page.post-type-ct_content_block [data-reach-dialog-overlay]{--modal-left: 0}.ct-condition-group{position:relative;display:grid;grid-template-columns:var(--grid-template-columns);align-items:center;gap:7px;padding:6px;padding-inline-end:35px;border-radius:5px;background:rgba(240,240,240,.5);border:1px solid rgba(240,240,240,.5);box-shadow:inset 0px 0px 0px 1px rgba(0,0,0,0);transition:border-color var(--condition-ui-transition, 0.1s ease),box-shadow var(--condition-ui-transition, 0.1s ease)}.ct-condition-group.ct-cols-2{--grid-template-columns: 110px 1fr}.ct-condition-group.ct-cols-2 .ct-select-input:nth-child(2):before{content:"ref-width"}.ct-condition-group.ct-cols-3{--grid-template-columns: 110px 1fr 1fr}.ct-condition-group.ct-cols-3 .ct-select-input:nth-child(2):before{content:"ref-width:right"}.ct-condition-group.ct-cols-3 .ct-select-input:nth-child(3):before{content:"ref-width:left"}.ct-condition-group.ct-cols-3 .ct-select-input.ct-dropdown-normal-width:before{content:"ref-width"}.ct-condition-type span{display:flex;align-items:center;justify-content:center;position:absolute;top:calc(50% - 9px);inset-inline-start:6px;width:18px;height:18px;background:#eee;border-radius:2px}.ct-condition-type.ct-select-input input{padding-inline-end:8px;padding-inline-start:30px}[class*=ct-remove-condition]{position:absolute;inset-inline-end:0px;display:flex;align-items:center;justify-content:center;width:35px;height:35px;padding:0;opacity:.5;border:none;cursor:pointer;-webkit-appearance:none;appearance:none;background:rgba(0,0,0,0)}[class*=ct-remove-condition]:focus{outline:none}[class*=ct-remove-condition]:hover{opacity:1;color:#a00}.ct-condition-relation{display:flex;align-items:center;justify-content:center;position:relative;height:40px}.ct-condition-relation:before{position:absolute;content:"";width:1px;height:100%;background:var(--optionBorderColor);transition:width var(--condition-ui-transition, 0.1s ease),background var(--condition-ui-transition, 0.1s ease)}.ct-condition-relation ul{display:grid;grid-template-columns:repeat(2, 1fr);z-index:1;overflow:clip;font-size:9px;font-weight:700;line-height:1;margin:0;background:#fff;border-radius:20px;box-shadow:0px 0px 0px 1px var(--optionBorderColor);transition:box-shadow var(--condition-ui-transition, 0.1s ease),background var(--condition-ui-transition, 0.1s ease)}.ct-condition-relation ul li{margin:0;padding:3px}.ct-condition-relation ul li span{display:flex;align-items:center;justify-content:center;height:18px;padding:0 8px;cursor:pointer;border-radius:20px;background:rgba(240,240,240,.8);transition:color var(--condition-ui-transition, 0.1s ease),opacity var(--condition-ui-transition, 0.1s ease),background var(--condition-ui-transition, 0.1s ease)}.ct-condition-relation ul li:not(.active) span{opacity:.5;background:rgba(0,0,0,0)}.ct-add-and-relation{height:33px;align-items:flex-end}.ct-add-and-relation span{display:flex;align-items:center;justify-content:center;z-index:1;width:23px;height:23px;cursor:pointer;border-radius:100%;background:#fff;border:1px solid var(--optionBorderColor);transition:all .15s ease}.ct-add-and-relation span:hover{color:#fff;background:var(--ui-accent-color);border-color:var(--ui-accent-color)}.ct-relation-group[data-relation=AND]{padding:10px;margin-inline:-10px;border-radius:5px;border:1px solid var(--optionBorderColor);box-shadow:inset 0px 0px 0px 1px rgba(0,0,0,0);transition:border-color var(--condition-ui-transition, 0.1s ease),box-shadow var(--condition-ui-transition, 0.1s ease)}.ct-relation-group .ct-hovered.ct-condition-group,.ct-relation-group .ct-hovered.ct-relation-group[data-relation=AND]{border-color:var(--ui-accent-color);box-shadow:inset 0px 0px 0px 1px var(--ui-accent-color)}.ct-relation-group .ct-hovered.ct-condition-relation:before{width:2px;background:var(--ui-accent-color)}.ct-relation-group .ct-hovered.ct-condition-relation ul{background:var(--ui-accent-color);box-shadow:0px 0px 0px 1px var(--ui-accent-color)}.ct-relation-group .ct-hovered.ct-condition-relation ul li.active span{background:#fff}.ct-relation-group .ct-hovered.ct-condition-relation ul li:not(.active){color:#fff}.ct-relation-group .ct-hovered.ct-condition-relation ul li:not(.active):hover span{opacity:.85}.ct-display-conditions{display:flex;flex-direction:column;gap:15px;--condition-ui-transition: 0.15s ease}.ct-conditions-actions{display:flex;align-items:center;justify-content:space-between}.ct-conditions-actions:not(:only-child){margin-top:10px;padding-top:25px;border-top:1px dashed var(--optionBorderColor)}.ct-conditions-actions span{display:flex;align-items:center;gap:10px;font-size:13px;font-weight:500}.ct-display-conditions .ct-option-input{position:relative}.ct-display-conditions .ct-option-input .ct-condition-info{display:flex;align-items:center;justify-content:center;position:absolute;top:calc(50% - 8px);inset-inline-end:7px}.ct-display-conditions .ct-option-input .ct-condition-info svg{fill:#cecece}.ct-display-conditions .ct-option-input .ct-condition-info .ct-tooltip{--tooltip-width: 160px}