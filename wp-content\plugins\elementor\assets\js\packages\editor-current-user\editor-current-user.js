/*! For license information please see editor-current-user.js.LICENSE.txt */
!function(){"use strict";var e={"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/http-client":function(e){e.exports=window.elementorV2.httpClient},"@elementor/query":function(e){e.exports=window.elementorV2.query}},t={};function r(n){var s=t[n];if(void 0!==s)return s.exports;var o=t[n]={exports:{}};return e[n](o,o.exports,r),o.exports}r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};!function(){r.r(n),r.d(n,{ensureCurrentUser:function(){return f},getCurrentUser:function(){return p},useCurrentUserCapabilities:function(){return m},useSuppressedMessage:function(){return y}});var e=r("@elementor/query"),t=r("@elementor/http-client"),s=r("@elementor/editor-v1-adapters"),o="/users/me",i={params:{context:"edit"}},u={get:()=>(0,t.httpService)().get("wp/v2"+o,i).then((e=>a(e.data))),update:e=>(0,t.httpService)().patch("wp/v2"+o,c(e))},a=e=>({suppressedMessages:Object.entries(e.elementor_introduction).filter((([,e])=>e)).map((([e])=>e)),capabilities:Object.keys(e.capabilities)}),c=e=>({elementor_introduction:e.suppressedMessages?.reduce(((e,t)=>(e[t]=!0,e)),{})}),p=()=>u.get(),d="editor-current-user",l=()=>(0,e.useQuery)({queryKey:[d],queryFn:p}),y=t=>{const{data:r}=l(),{mutate:n}=(()=>{const t=(0,e.useQueryClient)();return(0,e.useMutation)({mutationFn:u.update,onSuccess:()=>t.invalidateQueries({queryKey:[d]})})})(),s=!!r?.suppressedMessages.includes(t);return[s,()=>{s||n({suppressedMessages:[...r?.suppressedMessages??[],t]})}]},m=()=>{const{data:e}=l();return{canUser:t=>Boolean(e?.capabilities.includes(t)),capabilities:e?.capabilities}};function f({queryClient:e}){return(0,s.registerDataHook)("after","editor/documents/attach-preview",(async()=>{await e.ensureQueryData({queryKey:[d],queryFn:u.get})})),e.getQueryData([d])}}(),(window.elementorV2=window.elementorV2||{}).editorCurrentUser=n}(),window.elementorV2.editorCurrentUser?.init?.();