/*! For license information please see frontend-handlers.js.LICENSE.txt */
!function(){"use strict";var e={d:function(t,n){for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{init:function(){return a},register:function(){return o},unregister:function(){return r}});var n=new Map,o=({elementType:e,id:t,callback:o})=>{n.has(e)||n.set(e,new Map),n.get(e)?.has(t)||n.get(e)?.set(t,o)},r=({elementType:e,id:t})=>{n.has(e)&&(t?(n.get(e)?.delete(t),0===n.get(e)?.size&&n.delete(e)):n.delete(e))},l=new Map,d=({element:e,elementType:t,elementId:o})=>{const r=new AbortController,d=[];n.has(t)&&(Array.from(n.get(t)?.values()??[]).forEach((t=>{const n=t({element:e,signal:r.signal});"function"==typeof n&&d.push(n)})),l.has(t)||l.set(t,new Map),l.get(t)?.set(o,(()=>{r.abort(),d.forEach((e=>e()))})))},i=({elementType:e,elementId:t})=>{const n=l.get(e)?.get(t);n&&(n(),l.get(e)?.delete(t),0===l.get(e)?.size&&l.delete(e))};function a(){window.addEventListener("elementor/element/render",(e=>{const t=e,{id:n,type:o,element:r}=t.detail;i({elementType:o,elementId:n}),d({element:r,elementType:o,elementId:n})})),window.addEventListener("elementor/element/destroy",(e=>{const t=e,{id:n,type:o}=t.detail;i({elementType:o,elementId:n})})),document.addEventListener("DOMContentLoaded",(()=>{document.querySelectorAll("[data-e-type]").forEach((e=>{const t=e,{eType:n,id:o}=t.dataset;n&&o&&window.dispatchEvent(new CustomEvent("elementor/element/render",{detail:{id:o,type:n,element:e}}))}))}))}(window.elementorV2=window.elementorV2||{}).frontendHandlers=t}(),window.elementorV2.frontendHandlers?.init?.();