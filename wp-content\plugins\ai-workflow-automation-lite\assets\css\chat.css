.wp-ai-workflows-chat-container {
    --chat-primary: #1677ff;
    --chat-bg: #ffffff;
    --chat-text: #000000;
    --chat-secondary: #f5f5f5;
    --chat-radius: 12px;
    --chat-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.wp-ai-workflows-chat-container.dark {
    --chat-primary: #177ddc;
    --chat-bg: #1f1f1f;
    --chat-text: #ffffff;
    --chat-secondary: #2f2f2f;
}

.wp-ai-workflows-chat-widget {
    position: fixed;
    z-index: 9999;
    max-width: 400px;
    width: 100%;
    background: var(--chat-bg);
    border-radius: var(--chat-radius);
    box-shadow: var(--chat-shadow);
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

/* Position variants */
.wp-ai-workflows-chat-widget.bottom-right {
    bottom: 20px;
    right: 20px;
}

.wp-ai-workflows-chat-widget.bottom-left {
    bottom: 20px;
    left: 20px;
}

.wp-ai-workflows-chat-widget.top-right {
    top: 20px;
    right: 20px;
}

.wp-ai-workflows-chat-widget.top-left {
    top: 20px;
    left: 20px;
}

.chat-header {
    padding: 16px;
    background: var(--chat-primary);
    color: #ffffff;
    border-top-left-radius: var(--chat-radius);
    border-top-right-radius: var(--chat-radius);
    cursor: pointer;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.chat-message {
    display: flex;
    margin-bottom: 8px;
}

.chat-message.user {
    justify-content: flex-end;
}

.message-content {
    padding: 8px 12px;
    border-radius: 12px;
    max-width: 80%;
    word-wrap: break-word;
}

.chat-message.user .message-content {
    background: var(--chat-primary);
    color: #ffffff;
}

.chat-message.assistant .message-content {
    background: var(--chat-secondary);
    color: var(--chat-text);
}

.chat-input {
    padding: 16px;
    border-top: 1px solid var(--chat-secondary);
    display: flex;
    gap: 8px;
}

.chat-input input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--chat-secondary);
    border-radius: 6px;
    outline: none;
    background: var(--chat-bg);
    color: var(--chat-text);
}

.chat-input button {
    padding: 8px 16px;
    background: var(--chat-primary);
    color: #ffffff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: opacity 0.2s;
}

.chat-input button:hover {
    opacity: 0.9;
}

/* Launcher button */
.chat-launcher {
    position: fixed;
    z-index: 9998;
    width: 56px;
    height: 56px;
    border-radius: 28px;
    background: var(--chat-primary);
    color: #ffffff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--chat-shadow);
    transition: transform 0.3s ease;
}

.chat-launcher:hover {
    transform: scale(1.05);
}

.chat-launcher.bottom-right {
    bottom: 20px;
    right: 20px;
}

.chat-launcher.bottom-left {
    bottom: 20px;
    left: 20px;
}

.chat-launcher.top-right {
    top: 20px;
    right: 20px;
}

.chat-launcher.top-left {
    top: 20px;
    left: 20px;
}

/* Mobile responsiveness */
@media (max-width: 480px) {
    .wp-ai-workflows-chat-widget {
        width: 100%;
        height: 100%;
        max-width: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 0;
    }

    .chat-header {
        border-radius: 0;
    }
}

/* Animations */
.chat-widget-enter {
    opacity: 0;
    transform: translateY(20px);
}

.chat-widget-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.chat-widget-exit {
    opacity: 1;
    transform: translateY(0);
}

.chat-widget-exit-active {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Typing indicator */
.typing-indicator {
    display: flex;
    gap: 4px;
    padding: 8px 12px;
    background: var(--chat-secondary);
    border-radius: 12px;
    width: fit-content;
}

.typing-dot {
    width: 6px;
    height: 6px;
    background: var(--chat-text);
    border-radius: 50%;
    opacity: 0.6;
    animation: typingAnimation 1.4s infinite;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingAnimation {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-4px);
    }
}

/* Sound toggle button */
.sound-toggle {
    position: absolute;
    top: 12px;
    right: 12px;
    padding: 4px;
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: color 0.2s;
}

.sound-toggle:hover {
    color: #ffffff;
}

/* Error message styles */
.error-message {
    padding: 8px 12px;
    background: #fff2f0;
    border: 1px solid #ffccc7;
    border-radius: 6px;
    color: #ff4d4f;
    margin: 8px 0;
}

/* Custom scrollbar */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.dark .chat-messages::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
}

/* Code block styling */
.message-content pre {
    background: rgba(0, 0, 0, 0.05);
    padding: 8px;
    border-radius: 4px;
    overflow-x: auto;
}

.dark .message-content pre {
    background: rgba(255, 255, 255, 0.1);
}

/* Markdown styling */
.message-content p {
    margin: 0 0 8px 0;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content ul, 
.message-content ol {
    margin: 8px 0;
    padding-left: 20px;
}

.message-content code {
    background: rgba(0, 0, 0, 0.05);
    padding: 2px 4px;
    border-radius: 4px;
    font-family: monospace;
}

.dark .message-content code {
    background: rgba(255, 255, 255, 0.1);
}

/* File upload area */
.file-upload-area {
    border: 2px dashed var(--chat-secondary);
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    margin: 8px 0;
    cursor: pointer;
    transition: border-color 0.2s;
}

.file-upload-area:hover {
    border-color: var(--chat-primary);
}

.file-upload-area.dragging {
    background: rgba(var(--chat-primary-rgb), 0.1);
    border-color: var(--chat-primary);
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

/* Focus styles */
.chat-input input:focus,
.chat-input button:focus,
.sound-toggle:focus {
    outline: 2px solid var(--chat-primary);
    outline-offset: 2px;
}

/* CSS for minimized state */
.wp-ai-workflows-chat-widget.minimized {
    height: auto;
    min-height: unset;
}

.wp-ai-workflows-chat-widget.minimized .chat-messages,
.wp-ai-workflows-chat-widget.minimized .chat-input {
    display: none;
}

