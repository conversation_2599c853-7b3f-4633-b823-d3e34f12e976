/*! elementor - v3.30.0 - 01-07-2025 */
(self["webpackChunkelementor"] = self["webpackChunkelementor"] || []).push([["kit-library"],{

/***/ "../app/modules/kit-library/assets/js/components/badge.scss":
/*!******************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/badge.scss ***!
  \******************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/collapse.scss":
/*!*********************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/collapse.scss ***!
  \*********************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/envato-promotion.scss":
/*!*****************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/envato-promotion.scss ***!
  \*****************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/error-screen.scss":
/*!*************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/error-screen.scss ***!
  \*************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/favorites-actions.scss":
/*!******************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/favorites-actions.scss ***!
  \******************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/filter-indication-text.scss":
/*!***********************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/filter-indication-text.scss ***!
  \***********************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/item-header.scss":
/*!************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/item-header.scss ***!
  \************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/kit-list-item.scss":
/*!**************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/kit-list-item.scss ***!
  \**************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/layout/header-back-button.scss":
/*!**************************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/layout/header-back-button.scss ***!
  \**************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/page-loader.scss":
/*!************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/page-loader.scss ***!
  \************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/search-input.scss":
/*!*************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/search-input.scss ***!
  \*************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/sort-select.scss":
/*!************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/sort-select.scss ***!
  \************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/tags-filter.scss":
/*!************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/tags-filter.scss ***!
  \************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/index/index-header.scss":
/*!**************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/index/index-header.scss ***!
  \**************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/index/index.scss":
/*!*******************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/index/index.scss ***!
  \*******************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/overview/overview-sidebar.scss":
/*!*********************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/overview/overview-sidebar.scss ***!
  \*********************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/overview/overview.scss":
/*!*************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/overview/overview.scss ***!
  \*************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/preview/preview-responsive-controls.scss":
/*!*******************************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/preview/preview-responsive-controls.scss ***!
  \*******************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/preview/preview.scss":
/*!***********************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/preview/preview.scss ***!
  \***********************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app/assets/js/ui/popover-dialog/popover-dialog.js":
/*!************************************************************!*\
  !*** ../app/assets/js/ui/popover-dialog/popover-dialog.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = PopoverDialog;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function PopoverDialog(props) {
  var targetRef = props.targetRef,
    offsetTop = props.offsetTop,
    offsetLeft = props.offsetLeft,
    wrapperClass = props.wrapperClass,
    trigger = props.trigger,
    hideAfter = props.hideAfter,
    popoverRef = (0, _react.useCallback)(function (popoverEl) {
      var target = targetRef === null || targetRef === void 0 ? void 0 : targetRef.current;

      // If the target or the popover element does not exist on the page anymore after a re-render, do nothing.
      if (!target || !popoverEl) {
        return;
      }

      /**
       * Show Popover
       */
      var showPopover = function showPopover() {
        popoverEl.style.display = 'block';
        popoverEl.setAttribute('aria-expanded', true);
        var targetRect = target.getBoundingClientRect(),
          popoverRect = popoverEl.getBoundingClientRect(),
          widthDifference = popoverRect.width - targetRect.width;
        popoverEl.style.top = targetRect.bottom + offsetTop + 'px';
        popoverEl.style.left = targetRect.left - widthDifference / 2 - offsetLeft + 'px';

        // 16px to compensate for the arrow width.
        popoverEl.style.setProperty('--popover-arrow-offset-end', (popoverRect.width - 16) / 2 + 'px');
      };

      /**
       * Hide Popover
       */
      var hidePopover = function hidePopover() {
        popoverEl.style.display = 'none';
        popoverEl.setAttribute('aria-expanded', false);
      };

      /**
       * Handle the Popover's hover functionality
       */
      var handlePopoverHover = function handlePopoverHover() {
        var hideOnMouseOut = true,
          timeOut = null;

        // Show popover on hover of the target
        target.addEventListener('mouseover', function () {
          hideOnMouseOut = true;
          showPopover();
        });

        // Hide popover when not overing over the target or the popover itself
        target.addEventListener('mouseleave', function () {
          timeOut = setTimeout(function () {
            if (hideOnMouseOut) {
              if ('block' === popoverEl.style.display) {
                hidePopover();
              }
            }
          }, hideAfter);
        });

        // Don't hide the popover if the user is still hovering over it.
        popoverEl.addEventListener('mouseover', function () {
          hideOnMouseOut = false;
          if (timeOut) {
            clearTimeout(timeOut);
            timeOut = null;
          }
        });

        // Once the user stops hovering over the popover, hide it.
        popoverEl.addEventListener('mouseleave', function () {
          timeOut = setTimeout(function () {
            if (hideOnMouseOut) {
              if ('block' === popoverEl.style.display) {
                hidePopover();
              }
            }
          }, hideAfter);
          hideOnMouseOut = true;
        });
      };

      /**
       * Handle the Popover's click functionality
       */
      var handlePopoverClick = function handlePopoverClick() {
        var popoverIsActive = false;
        target.addEventListener('click', function (e) {
          e.preventDefault();
          e.stopPropagation();
          if (popoverIsActive) {
            hidePopover();
            popoverIsActive = false;
          } else {
            showPopover();
            popoverIsActive = true;
          }
        });

        // Make sure the popover doesn't close when it is clicked on.
        popoverEl.addEventListener('click', function (e) {
          e.stopPropagation();
        });

        // Hide the popover when clicking outside of it.
        document.body.addEventListener('click', function () {
          if (popoverIsActive) {
            hidePopover();
            popoverIsActive = false;
          }
        });
      };
      if ('hover' === trigger) {
        handlePopoverHover();
      } else if ('click' === trigger) {
        handlePopoverClick();
      }
    }, [targetRef]);
  var wrapperClasses = 'e-app__popover';
  if (wrapperClass) {
    wrapperClasses += ' ' + wrapperClass;
  }
  return /*#__PURE__*/_react.default.createElement("div", {
    className: wrapperClasses,
    ref: popoverRef
  }, props.children);
}
PopoverDialog.propTypes = {
  targetRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({
    current: PropTypes.any
  })]).isRequired,
  trigger: PropTypes.string,
  direction: PropTypes.string,
  offsetTop: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  offsetLeft: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  wrapperClass: PropTypes.string,
  children: PropTypes.any,
  hideAfter: PropTypes.number
};
PopoverDialog.defaultProps = {
  direction: 'bottom',
  trigger: 'hover',
  offsetTop: 10,
  offsetLeft: 0,
  hideAfter: 300
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/app.js":
/*!***************************************************!*\
  !*** ../app/modules/kit-library/assets/js/app.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = App;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _favorites = _interopRequireDefault(__webpack_require__(/*! ./pages/favorites/favorites */ "../app/modules/kit-library/assets/js/pages/favorites/favorites.js"));
var _index = _interopRequireDefault(__webpack_require__(/*! ./pages/index */ "../app/modules/kit-library/assets/js/pages/index/index.js"));
var _cloud = _interopRequireDefault(__webpack_require__(/*! ./pages/cloud/cloud */ "../app/modules/kit-library/assets/js/pages/cloud/cloud.js"));
var _overview = _interopRequireDefault(__webpack_require__(/*! ./pages/overview/overview */ "../app/modules/kit-library/assets/js/pages/overview/overview.js"));
var _preview = _interopRequireDefault(__webpack_require__(/*! ./pages/preview/preview */ "../app/modules/kit-library/assets/js/pages/preview/preview.js"));
var _lastFilterContext = __webpack_require__(/*! ./context/last-filter-context */ "../app/modules/kit-library/assets/js/context/last-filter-context.js");
var _reactQuery = __webpack_require__(/*! react-query */ "../node_modules/react-query/es/index.js");
var _devtools = __webpack_require__(/*! react-query/devtools */ "../node_modules/react-query/devtools/index.js");
var _router = __webpack_require__(/*! @reach/router */ "../node_modules/@reach/router/es/index.js");
var _settingsContext = __webpack_require__(/*! ./context/settings-context */ "../app/modules/kit-library/assets/js/context/settings-context.js");
var _connectStateContext = __webpack_require__(/*! ./context/connect-state-context */ "../app/modules/kit-library/assets/js/context/connect-state-context.js");
var queryClient = new _reactQuery.QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: false,
      staleTime: 1000 * 60 * 30 // 30 minutes
    }
  }
});
function AppContent() {
  return /*#__PURE__*/_react.default.createElement(_settingsContext.SettingsProvider, {
    value: elementorAppConfig['kit-library']
  }, /*#__PURE__*/_react.default.createElement(_connectStateContext.ConnectStateProvider, null, /*#__PURE__*/_react.default.createElement(_lastFilterContext.LastFilterProvider, null, /*#__PURE__*/_react.default.createElement(_router.Router, null, /*#__PURE__*/_react.default.createElement(_index.default, {
    path: "/"
  }), /*#__PURE__*/_react.default.createElement(_favorites.default, {
    path: "/favorites"
  }), /*#__PURE__*/_react.default.createElement(_preview.default, {
    path: "/preview/:id"
  }), /*#__PURE__*/_react.default.createElement(_overview.default, {
    path: "/overview/:id"
  }), /*#__PURE__*/_react.default.createElement(_cloud.default, {
    path: "/cloud"
  })))));
}
function App() {
  return /*#__PURE__*/_react.default.createElement("div", {
    className: "e-kit-library"
  }, /*#__PURE__*/_react.default.createElement(_reactQuery.QueryClientProvider, {
    client: queryClient
  }, /*#__PURE__*/_react.default.createElement(AppContent, null), elementorCommon.config.isElementorDebug && /*#__PURE__*/_react.default.createElement(_devtools.ReactQueryDevtools, {
    initialIsOpen: false
  })));
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/apply-kit-dialog.js":
/*!***************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/apply-kit-dialog.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = ApplyKitDialog;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _router = __webpack_require__(/*! @reach/router */ "../node_modules/@reach/router/es/index.js");
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function ApplyKitDialog(props) {
  var navigate = (0, _router.useNavigate)();
  var startImportProcess = (0, _react.useCallback)(function () {
    var applyAll = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
    var url = '/import/process' + "?id=".concat(props.id) + "&file_url=".concat(encodeURIComponent(props.downloadLink)) + "&nonce=".concat(props.nonce, "&referrer=kit-library");
    if (applyAll) {
      url += '&action_type=apply-all';
    }
    navigate(url);
  }, [props.downloadLink, props.nonce]);
  return /*#__PURE__*/_react.default.createElement(_appUi.Dialog
  // Translators: %s is the kit name.
  , {
    title: __('Apply %s?', 'elementor').replace('%s', props.title),
    text: /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, __('You can use everything in this kit, or Customize to only include some items.', 'elementor'), /*#__PURE__*/_react.default.createElement("br", null), /*#__PURE__*/_react.default.createElement("br", null), __('By applying the entire kit, you\'ll override any styles, settings or content already on your site.', 'elementor')),
    approveButtonText: __('Apply All', 'elementor'),
    approveButtonColor: "primary",
    approveButtonOnClick: function approveButtonOnClick() {
      return startImportProcess(true);
    },
    dismissButtonText: __('Customize', 'elementor'),
    dismissButtonOnClick: function dismissButtonOnClick() {
      return startImportProcess(false);
    },
    onClose: props.onClose
  });
}
ApplyKitDialog.propTypes = {
  id: PropTypes.string.isRequired,
  downloadLink: PropTypes.string.isRequired,
  nonce: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string
};
ApplyKitDialog.defaultProps = {
  title: 'Kit'
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/badge.js":
/*!****************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/badge.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = Badge;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
__webpack_require__(/*! ./badge.scss */ "../app/modules/kit-library/assets/js/components/badge.scss");
function Badge(props) {
  return /*#__PURE__*/_react.default.createElement("span", {
    className: "eps-badge eps-badge--".concat(props.variant, " ").concat(props.className),
    style: props.style
  }, props.children);
}
Badge.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  style: PropTypes.object,
  variant: PropTypes.oneOf(['sm', 'md'])
};
Badge.defaultProps = {
  className: '',
  style: {},
  variant: 'md'
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/collapse.js":
/*!*******************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/collapse.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = Collapse;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
__webpack_require__(/*! ./collapse.scss */ "../app/modules/kit-library/assets/js/components/collapse.scss");
function Collapse(props) {
  // The state of the collapse managed by the parent component to let the parent control if the collapse is open or closed by default.
  return /*#__PURE__*/_react.default.createElement("div", {
    className: "eps-collapse ".concat(props.className),
    "data-open": props.isOpen || undefined /* Set `undefined` when 'isOpen' equals `false` to avoid showing the attr "data-open" */
  }, /*#__PURE__*/_react.default.createElement("button", {
    className: "eps-collapse__title",
    onClick: function onClick() {
      var _props$onClick;
      props.onChange(function (value) {
        return !value;
      });
      (_props$onClick = props.onClick) === null || _props$onClick === void 0 || _props$onClick.call(props, props.isOpen, props.title);
    }
  }, /*#__PURE__*/_react.default.createElement("span", null, props.title), /*#__PURE__*/_react.default.createElement("i", {
    className: "eicon-chevron-right eps-collapse__icon"
  })), /*#__PURE__*/_react.default.createElement("div", {
    className: "eps-collapse__content"
  }, props.children));
}
Collapse.propTypes = {
  isOpen: PropTypes.bool,
  onChange: PropTypes.func,
  className: PropTypes.string,
  title: PropTypes.node,
  onClick: PropTypes.func,
  children: PropTypes.oneOfType([PropTypes.node, PropTypes.arrayOf(PropTypes.node)])
};
Collapse.defaultProps = {
  className: '',
  isOpen: false
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/connect-dialog.js":
/*!*************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/connect-dialog.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = ConnectDialog;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _settingsContext = __webpack_require__(/*! ../context/settings-context */ "../app/modules/kit-library/assets/js/context/settings-context.js");
var _React = _react.default,
  useEffect = _React.useEffect,
  useRef = _React.useRef;
function ConnectDialog(props) {
  var _useSettingsContext = (0, _settingsContext.useSettingsContext)(),
    settings = _useSettingsContext.settings;
  var approveButtonRef = useRef();
  useEffect(function () {
    jQuery(approveButtonRef.current).elementorConnect({
      success: function success(e, data) {
        return props.onSuccess(data);
      },
      error: function error() {
        return props.onError(__('Unable to connect', 'elementor'));
      },
      parseUrl: function parseUrl(url) {
        return url.replace('%%page%%', props.pageId);
      }
    });
  }, []);
  return /*#__PURE__*/_react.default.createElement(_appUi.Dialog, {
    title: __('Connect to Template Library', 'elementor'),
    text: __('Access this template and our entire library by creating a free personal account', 'elementor'),
    approveButtonText: __('Get Started', 'elementor'),
    approveButtonUrl: settings.library_connect_url,
    approveButtonOnClick: function approveButtonOnClick() {
      return props.onClose();
    },
    approveButtonColor: "primary",
    approveButtonRef: approveButtonRef,
    dismissButtonText: __('Cancel', 'elementor'),
    dismissButtonOnClick: function dismissButtonOnClick() {
      return props.onClose();
    },
    onClose: function onClose() {
      return props.onClose();
    }
  });
}
ConnectDialog.propTypes = {
  onClose: PropTypes.func.isRequired,
  onError: PropTypes.func.isRequired,
  onSuccess: PropTypes.func.isRequired,
  pageId: PropTypes.string
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/envato-promotion.js":
/*!***************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/envato-promotion.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = EnvatoPromotion;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
__webpack_require__(/*! ./envato-promotion.scss */ "../app/modules/kit-library/assets/js/components/envato-promotion.scss");
function EnvatoPromotion(props) {
  var eventTracking = function eventTracking(command) {
    var eventType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'click';
    (0, _appsEventTracking.appsEventTrackingDispatch)(command, {
      page_source: 'home page',
      element_position: 'library_bottom_promotion',
      category: props.category && ('/favorites' === props.category ? 'favorites' : 'all kits'),
      event_type: eventType
    });
  };
  return /*#__PURE__*/_react.default.createElement(_appUi.Text, {
    className: "e-kit-library-promotion",
    variant: "xl"
  }, __('Looking for more Website Templates?', 'elementor'), " ", ' ', /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    variant: "underlined",
    color: "link",
    url: "https://go.elementor.com/app-envato-kits/",
    target: "_blank",
    rel: "noreferrer",
    text: __('Check out Elementor Website Templates on ThemeForest', 'elementor'),
    onClick: function onClick() {
      return eventTracking('kit-library/check-kits-on-theme-forest');
    }
  }));
}
EnvatoPromotion.propTypes = {
  category: PropTypes.string
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/error-screen.js":
/*!***********************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/error-screen.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = ErrorScreen;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _propTypes = _interopRequireDefault(__webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js"));
__webpack_require__(/*! ./error-screen.scss */ "../app/modules/kit-library/assets/js/components/error-screen.scss");
/* eslint-disable jsx-a11y/alt-text */

var ErrorScreenButton = function ErrorScreenButton(props) {
  var onClick = function onClick() {
    if (props.action) {
      props.action();
    }
  };
  return /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    text: props.text,
    onClick: onClick,
    url: props.url,
    target: props.target,
    color: props.color || 'link',
    variant: props.variant || ''
  });
};
ErrorScreenButton.propTypes = {
  text: _propTypes.default.string,
  action: _propTypes.default.func,
  url: _propTypes.default.string,
  target: _propTypes.default.string,
  color: _propTypes.default.oneOf(['primary', 'secondary', 'cta', 'link', 'disabled']),
  variant: _propTypes.default.oneOf(['contained', 'underlined', 'outlined', ''])
};
function ErrorScreen(props) {
  return /*#__PURE__*/_react.default.createElement(_appUi.Grid, {
    container: true,
    alignItems: "center",
    justify: "center",
    direction: "column",
    className: "e-kit-library__error-screen"
  }, /*#__PURE__*/_react.default.createElement("img", {
    src: "".concat(elementorAppConfig.assets_url, "images/no-search-results.svg")
  }), /*#__PURE__*/_react.default.createElement(_appUi.Heading, {
    tag: "h3",
    variant: "display-1",
    className: "e-kit-library__error-screen-title"
  }, props.title), /*#__PURE__*/_react.default.createElement(_appUi.Text, {
    variant: "xl",
    className: "e-kit-library__error-screen-description"
  }, props.description, " ", ' ', !props.newLineButton && /*#__PURE__*/_react.default.createElement(ErrorScreenButton, props.button)), props.newLineButton && /*#__PURE__*/_react.default.createElement(ErrorScreenButton, props.button));
}
ErrorScreen.propTypes = {
  title: _propTypes.default.string,
  description: _propTypes.default.string,
  newLineButton: _propTypes.default.bool,
  button: _propTypes.default.shape({
    text: _propTypes.default.string,
    action: _propTypes.default.func,
    url: _propTypes.default.string,
    target: _propTypes.default.string,
    category: _propTypes.default.string,
    color: _propTypes.default.oneOf(['primary', 'secondary', 'cta', 'link', 'disabled']),
    variant: _propTypes.default.oneOf(['contained', 'underlined', 'outlined', ''])
  })
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/favorites-actions.js":
/*!****************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/favorites-actions.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = FavoritesActions;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _useKitFavoritesMutations = __webpack_require__(/*! ../hooks/use-kit-favorites-mutations */ "../app/modules/kit-library/assets/js/hooks/use-kit-favorites-mutations.js");
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
__webpack_require__(/*! ./favorites-actions.scss */ "../app/modules/kit-library/assets/js/components/favorites-actions.scss");
function FavoritesActions(props) {
  var _useKitFavoritesMutat = (0, _useKitFavoritesMutations.useKitFavoritesMutations)(),
    addToFavorites = _useKitFavoritesMutat.addToFavorites,
    removeFromFavorites = _useKitFavoritesMutat.removeFromFavorites,
    isLoading = _useKitFavoritesMutat.isLoading;
  var loadingClasses = isLoading ? 'e-kit-library__kit-favorite-actions--loading' : '';
  var eventTracking = function eventTracking(kitName, source, action) {
    var gridLocation = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;
    var searchTerm = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : null;
    (0, _appsEventTracking.appsEventTrackingDispatch)('kit-library/favorite-icon', {
      grid_location: gridLocation,
      search_term: searchTerm,
      kit_name: kitName,
      page_source: source && ('/' === source ? 'home page' : 'overview'),
      element_location: source && 'overview' === source ? 'app_sidebar' : null,
      action: action
    });
  };
  return props.isFavorite ? /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    text: __('Remove from Favorites', 'elementor'),
    hideText: true,
    icon: "eicon-heart",
    className: "e-kit-library__kit-favorite-actions e-kit-library__kit-favorite-actions--active ".concat(loadingClasses),
    onClick: function onClick() {
      // eslint-disable-next-line no-unused-expressions
      !isLoading && removeFromFavorites.mutate(props.id);
      eventTracking(props === null || props === void 0 ? void 0 : props.name, props === null || props === void 0 ? void 0 : props.source, 'uncheck');
    }
  }) : /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    text: __('Add to Favorites', 'elementor'),
    hideText: true,
    icon: "eicon-heart-o",
    className: "e-kit-library__kit-favorite-actions ".concat(loadingClasses),
    onClick: function onClick() {
      // eslint-disable-next-line no-unused-expressions
      !isLoading && addToFavorites.mutate(props.id);
      eventTracking(props === null || props === void 0 ? void 0 : props.name, props === null || props === void 0 ? void 0 : props.source, 'check', props === null || props === void 0 ? void 0 : props.index, props === null || props === void 0 ? void 0 : props.queryParams);
    }
  });
}
FavoritesActions.propTypes = {
  isFavorite: PropTypes.bool,
  id: PropTypes.string,
  name: PropTypes.string,
  source: PropTypes.string,
  index: PropTypes.number,
  queryParams: PropTypes.string
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/filter-indication-text.js":
/*!*********************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/filter-indication-text.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = FilterIndicationText;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _useSelectedTaxonomies = _interopRequireDefault(__webpack_require__(/*! ../hooks/use-selected-taxonomies */ "../app/modules/kit-library/assets/js/hooks/use-selected-taxonomies.js"));
var _badge = _interopRequireDefault(__webpack_require__(/*! ./badge */ "../app/modules/kit-library/assets/js/components/badge.js"));
var _i18n = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
__webpack_require__(/*! ./filter-indication-text.scss */ "../app/modules/kit-library/assets/js/components/filter-indication-text.scss");
var _taxonomyTransformer = __webpack_require__(/*! ../models/taxonomy-transformer */ "../app/modules/kit-library/assets/js/models/taxonomy-transformer.js");
function FilterIndicationText(props) {
  var selectedTaxonomies = (0, _useSelectedTaxonomies.default)(props.queryParams.taxonomies);
  var eventTracking = function eventTracking(taxonomy) {
    var eventType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'click';
    (0, _appsEventTracking.appsEventTrackingDispatch)('kit-library/clear-filter', {
      tag: taxonomy,
      page_source: 'home page',
      event_type: eventType
    });
  };
  return /*#__PURE__*/_react.default.createElement(_appUi.Grid, {
    container: true,
    className: "e-kit-library__filter-indication"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Text, {
    className: "e-kit-library__filter-indication-text"
  },
  // Translators: %s is the number of kits in the results
  (0, _i18n.sprintf)((0, _i18n._n)('Showing %s result for', 'Showing %s results for', props.resultCount, 'elementor'), !props.resultCount ? __('no', 'elementor') : props.resultCount), ' ', props.queryParams.search && "\"".concat(props.queryParams.search, "\""), ' ', selectedTaxonomies.length > 0 && /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, selectedTaxonomies.map(function (taxonomy) {
    return /*#__PURE__*/_react.default.createElement(_badge.default, {
      key: taxonomy,
      className: "e-kit-library__filter-indication-badge"
    }, _taxonomyTransformer.NewPlanTexts[taxonomy] || taxonomy, /*#__PURE__*/_react.default.createElement(_appUi.Button, {
      text: __('Remove', 'elementor'),
      hideText: true,
      icon: "eicon-editor-close",
      className: "e-kit-library__filter-indication-badge-remove",
      onClick: function onClick() {
        eventTracking(taxonomy);
        props.onRemoveTag(taxonomy);
      }
    }));
  }))), /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    className: "e-kit-library__filter-indication-button",
    text: __('Clear all', 'elementor'),
    variant: "underlined",
    onClick: function onClick() {
      eventTracking('all');
      props.onClear();
    }
  }));
}
FilterIndicationText.propTypes = {
  queryParams: PropTypes.shape({
    search: PropTypes.string,
    taxonomies: PropTypes.objectOf(PropTypes.arrayOf(PropTypes.string)),
    favorite: PropTypes.bool
  }),
  resultCount: PropTypes.number.isRequired,
  onClear: PropTypes.func.isRequired,
  onRemoveTag: PropTypes.func.isRequired
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/item-header.js":
/*!**********************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/item-header.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = ItemHeader;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _extends2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/extends */ "../node_modules/@babel/runtime/helpers/extends.js"));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ "../node_modules/@babel/runtime/helpers/toConsumableArray.js"));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _kitDialog = _interopRequireDefault(__webpack_require__(/*! ./kit-dialog */ "../app/modules/kit-library/assets/js/components/kit-dialog.js"));
var _connectDialog = _interopRequireDefault(__webpack_require__(/*! ./connect-dialog */ "../app/modules/kit-library/assets/js/components/connect-dialog.js"));
var _header = _interopRequireDefault(__webpack_require__(/*! ./layout/header */ "../app/modules/kit-library/assets/js/components/layout/header.js"));
var _headerBackButton = _interopRequireDefault(__webpack_require__(/*! ./layout/header-back-button */ "../app/modules/kit-library/assets/js/components/layout/header-back-button.js"));
var _kit = _interopRequireDefault(__webpack_require__(/*! ../models/kit */ "../app/modules/kit-library/assets/js/models/kit.js"));
var _useDownloadLinkMutation = _interopRequireDefault(__webpack_require__(/*! ../hooks/use-download-link-mutation */ "../app/modules/kit-library/assets/js/hooks/use-download-link-mutation.js"));
var _useKitCallToAction2 = _interopRequireWildcard(__webpack_require__(/*! ../hooks/use-kit-call-to-action */ "../app/modules/kit-library/assets/js/hooks/use-kit-call-to-action.js"));
var _useAddKitPromotionUtm = _interopRequireDefault(__webpack_require__(/*! ../hooks/use-add-kit-promotion-utm */ "../app/modules/kit-library/assets/js/hooks/use-add-kit-promotion-utm.js"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _settingsContext = __webpack_require__(/*! ../context/settings-context */ "../app/modules/kit-library/assets/js/context/settings-context.js");
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
var _tiers = __webpack_require__(/*! elementor-utils/tiers */ "../assets/dev/js/utils/tiers.js");
__webpack_require__(/*! ./item-header.scss */ "../app/modules/kit-library/assets/js/components/item-header.scss");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * Returns the right call to action button.
 *
 * @param {Kit}      model
 * @param {Object}   root0
 * @param {Function} root0.apply
 * @param {Function} root0.onConnect
 * @param {Function} root0.onClick
 * @param {boolean}  root0.isApplyLoading
 * @return {Object} result
 */
function useKitCallToActionButton(model, _ref) {
  var apply = _ref.apply,
    isApplyLoading = _ref.isApplyLoading,
    onConnect = _ref.onConnect,
    _onClick = _ref.onClick;
  var _useKitCallToAction = (0, _useKitCallToAction2.default)(model.accessTier),
    type = _useKitCallToAction.type,
    subscriptionPlan = _useKitCallToAction.subscriptionPlan;
  var promotionUrl = (0, _useAddKitPromotionUtm.default)(subscriptionPlan.promotion_url, model.id, model.title);
  var _useSettingsContext = (0, _settingsContext.useSettingsContext)(),
    settings = _useSettingsContext.settings;
  return (0, _react.useMemo)(function () {
    if (type === _useKitCallToAction2.TYPE_CONNECT) {
      return {
        id: 'connect',
        text: __('Apply', 'elementor'),
        // The label is Apply kit but the this is connect button
        hideText: false,
        variant: 'contained',
        color: 'primary',
        size: 'sm',
        onClick: function onClick(e) {
          onConnect(e);
          _onClick === null || _onClick === void 0 || _onClick(e);
        },
        includeHeaderBtnClass: false
      };
    }
    if (type === _useKitCallToAction2.TYPE_PROMOTION && subscriptionPlan) {
      return {
        id: 'promotion',
        text: settings.is_pro ? 'Upgrade' : "Go ".concat(subscriptionPlan.label),
        hideText: false,
        variant: 'contained',
        color: 'cta',
        size: 'sm',
        url: promotionUrl,
        target: '_blank',
        includeHeaderBtnClass: false
      };
    }
    return {
      id: 'apply',
      text: __('Apply', 'elementor'),
      className: 'e-kit-library__apply-button',
      icon: isApplyLoading ? 'eicon-loading eicon-animation-spin' : '',
      hideText: false,
      variant: 'contained',
      color: isApplyLoading ? 'disabled' : 'primary',
      size: 'sm',
      onClick: function onClick(e) {
        if (!isApplyLoading) {
          apply(e);
        }
        _onClick === null || _onClick === void 0 || _onClick(e);
      },
      includeHeaderBtnClass: false
    };
  }, [type, subscriptionPlan, isApplyLoading, apply]);
}
function ItemHeader(props) {
  var _useSettingsContext2 = (0, _settingsContext.useSettingsContext)(),
    updateSettings = _useSettingsContext2.updateSettings;
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isConnectDialogOpen = _useState2[0],
    setIsConnectDialogOpen = _useState2[1];
  var _useState3 = (0, _react.useState)(null),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    downloadLinkData = _useState4[0],
    setDownloadLinkData = _useState4[1];
  var _useState5 = (0, _react.useState)(false),
    _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
    error = _useState6[0],
    setError = _useState6[1];
  var kitData = {
    kitName: props.model.title,
    pageId: props.pageId
  };
  var _useDownloadLinkMutat = (0, _useDownloadLinkMutation.default)(props.model, {
      onSuccess: function onSuccess(_ref2) {
        var data = _ref2.data;
        return setDownloadLinkData(data);
      },
      onError: function onError(errorResponse) {
        if (401 === errorResponse.code) {
          elementorCommon.config.library_connect.is_connected = false;
          elementorCommon.config.library_connect.current_access_level = 0;
          elementorCommon.config.library_connect.current_access_tier = _tiers.TIERS.free;
          updateSettings({
            is_library_connected: false,
            access_level: 0,
            access_tier: _tiers.TIERS.free
          });
          setIsConnectDialogOpen(true);
          return;
        }
        setError({
          code: errorResponse.code,
          message: __('Something went wrong.', 'elementor')
        });
      }
    }),
    apply = _useDownloadLinkMutat.mutate,
    isApplyLoading = _useDownloadLinkMutat.isLoading;
  var applyButton = useKitCallToActionButton(props.model, {
    onConnect: function onConnect() {
      return setIsConnectDialogOpen(true);
    },
    apply: apply,
    isApplyLoading: isApplyLoading,
    onClick: function onClick() {
      return (0, _appsEventTracking.appsEventTrackingDispatch)('kit-library/apply-kit', {
        kit_name: props.model.title,
        element_position: 'app_header',
        page_source: props.pageId,
        event_type: 'click'
      });
    }
  });
  var buttons = (0, _react.useMemo)(function () {
    return [applyButton].concat((0, _toConsumableArray2.default)(props.buttons));
  }, [props.buttons, applyButton]);
  return /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, error && /*#__PURE__*/_react.default.createElement(_appUi.Dialog, {
    title: error.message,
    text: __('Go to the pages screen to make sure your kit pages have been imported successfully. If not, try again.', 'elementor'),
    approveButtonText: __('Go to pages', 'elementor'),
    approveButtonColor: "primary",
    approveButtonUrl: elementorAppConfig.admin_url + 'edit.php?post_type=page',
    approveButtonOnClick: function approveButtonOnClick() {
      return setError(false);
    },
    dismissButtonText: __('Got it', 'elementor'),
    dismissButtonOnClick: function dismissButtonOnClick() {
      return setError(false);
    },
    onClose: function onClose() {
      return setError(false);
    }
  }), downloadLinkData && /*#__PURE__*/_react.default.createElement(_kitDialog.default, {
    id: props.model.id,
    downloadLinkData: downloadLinkData,
    onClose: function onClose() {
      return setDownloadLinkData(null);
    }
  }), isConnectDialogOpen && /*#__PURE__*/_react.default.createElement(_connectDialog.default, {
    pageId: props.pageId,
    onClose: function onClose() {
      return setIsConnectDialogOpen(false);
    },
    onSuccess: function onSuccess(data) {
      var accessLevel = data.kits_access_level || data.access_level || 0;
      var accessTier = data.access_tier;
      elementorCommon.config.library_connect.is_connected = true;
      elementorCommon.config.library_connect.current_access_level = accessLevel;
      elementorCommon.config.library_connect.current_access_tier = accessTier;
      updateSettings({
        is_library_connected: true,
        access_level: accessLevel,
        // BC: Check for 'access_level' prop
        access_tier: accessTier
      });
      if (data.access_level < props.model.accessLevel) {
        return;
      }
      if (!(0, _tiers.isTierAtLeast)(accessTier, props.model.accessTier)) {
        return;
      }
      apply();
    },
    onError: function onError(message) {
      return setError({
        message: message
      });
    }
  }), /*#__PURE__*/_react.default.createElement(_header.default, (0, _extends2.default)({
    startColumn: /*#__PURE__*/_react.default.createElement(_headerBackButton.default, kitData),
    centerColumn: props.centerColumn,
    buttons: buttons
  }, kitData)));
}
ItemHeader.propTypes = {
  model: PropTypes.instanceOf(_kit.default).isRequired,
  centerColumn: PropTypes.node,
  buttons: PropTypes.arrayOf(PropTypes.object),
  pageId: PropTypes.string
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/kit-already-applied-dialog.js":
/*!*************************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/kit-already-applied-dialog.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = KitAlreadyAppliedDialog;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
function KitAlreadyAppliedDialog(props) {
  var getRemoveKitUrl = function getRemoveKitUrl() {
    var elementorToolsUrl = elementorAppConfig['import-export'].tools_url;
    var url = new URL(elementorToolsUrl);
    url.searchParams.append('referrer_kit', props.id);
    url.hash = 'tab-import-export-kit';
    return url.toString();
  };
  return /*#__PURE__*/_react.default.createElement(_appUi.Dialog, {
    title: __('You\'ve already applied a Website Templates.', 'elementor'),
    text: /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, __('Applying two Website Templates on the same website will mix global styles and colors and hurt your site\'s performance.', 'elementor'), /*#__PURE__*/_react.default.createElement("br", null), /*#__PURE__*/_react.default.createElement("br", null), __('Remove the existing Website Template before applying a new one.', 'elementor')),
    approveButtonText: __('Remove existing', 'elementor'),
    approveButtonColor: "primary",
    approveButtonOnClick: function approveButtonOnClick() {
      return location.href = getRemoveKitUrl();
    },
    dismissButtonText: __('Apply anyway', 'elementor'),
    dismissButtonOnClick: props.dismissButtonOnClick,
    onClose: props.onClose
  });
}
KitAlreadyAppliedDialog.propTypes = {
  id: PropTypes.string.isRequired,
  dismissButtonOnClick: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/kit-cloud-delete-dialog.js":
/*!**********************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/kit-cloud-delete-dialog.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = KitCloudDeleteDialog;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _i18n = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _propTypes = _interopRequireDefault(__webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js"));
function KitCloudDeleteDialog(_ref) {
  var kit = _ref.kit,
    show = _ref.show,
    onCancelClick = _ref.onCancelClick,
    onDeleteClick = _ref.onDeleteClick,
    isLoading = _ref.isLoading;
  if (!kit || !show) {
    return null;
  }
  var handleDeleteClick = function handleDeleteClick() {
    if (!isLoading) {
      onDeleteClick();
    }
  };
  var handleCancelClick = function handleCancelClick() {
    if (!isLoading) {
      onCancelClick();
    }
  };
  return /*#__PURE__*/_react.default.createElement(_appUi.Dialog, {
    title: __('Delete this Website Template?', 'elementor'),
    text: /* Translators: %s: Kit title. */(0, _i18n.sprintf)(__('Removing "%s" will permanently delete this website template from your library.', 'elementor'), (kit === null || kit === void 0 ? void 0 : kit.title) || ''),
    onClose: handleCancelClick,
    dismissButtonText: __('Cancel', 'elementor'),
    dismissButtonOnClick: handleCancelClick,
    approveButtonText: isLoading ? '' : __('Delete', 'elementor'),
    approveButtonOnClick: handleDeleteClick,
    approveButtonColor: "danger"
  });
}
KitCloudDeleteDialog.propTypes = {
  onDeleteClick: _propTypes.default.func.isRequired,
  onCancelClick: _propTypes.default.func.isRequired,
  show: _propTypes.default.bool.isRequired,
  isLoading: _propTypes.default.bool.isRequired,
  kit: _propTypes.default.shape({
    id: _propTypes.default.string,
    title: _propTypes.default.string
  })
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/kit-dialog.js":
/*!*********************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/kit-dialog.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = KitDialog;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _kitAlreadyAppliedDialog = _interopRequireDefault(__webpack_require__(/*! ./kit-already-applied-dialog */ "../app/modules/kit-library/assets/js/components/kit-already-applied-dialog.js"));
var _applyKitDialog = _interopRequireDefault(__webpack_require__(/*! ./apply-kit-dialog */ "../app/modules/kit-library/assets/js/components/apply-kit-dialog.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function KitDialog(props) {
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    applyAnyway = _useState2[0],
    setApplyAnyway = _useState2[1];
  var kitAlreadyApplied = !!elementorAppConfig['import-export'].lastImportedSession.session_id;
  if (kitAlreadyApplied && !applyAnyway) {
    return /*#__PURE__*/_react.default.createElement(_kitAlreadyAppliedDialog.default, {
      id: props.id,
      dismissButtonOnClick: function dismissButtonOnClick() {
        return setApplyAnyway(true);
      },
      onClose: props.onClose
    });
  }
  return /*#__PURE__*/_react.default.createElement(_applyKitDialog.default, {
    id: props.id,
    downloadLink: props.downloadLinkData.data.download_link,
    nonce: props.downloadLinkData.meta.nonce,
    onClose: props.onClose
  });
}
KitDialog.propTypes = {
  id: PropTypes.string.isRequired,
  downloadLinkData: PropTypes.object.isRequired,
  onClose: PropTypes.func.isRequired
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/kit-list-cloud-item.js":
/*!******************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/kit-list-cloud-item.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _i18n = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
var _router = __webpack_require__(/*! @reach/router */ "../node_modules/@reach/router/es/index.js");
var _propTypes = _interopRequireDefault(__webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js"));
var _kit = _interopRequireDefault(__webpack_require__(/*! ../models/kit */ "../app/modules/kit-library/assets/js/models/kit.js"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
var _useKit = __webpack_require__(/*! ../../../../import-export/assets/js/hooks/use-kit */ "../app/modules/import-export/assets/js/hooks/use-kit.js");
__webpack_require__(/*! ./kit-list-item.scss */ "../app/modules/kit-library/assets/js/components/kit-list-item.scss");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var PopoverItem = function PopoverItem(_ref) {
  var _ref$className = _ref.className,
    className = _ref$className === void 0 ? '' : _ref$className,
    icon = _ref.icon,
    title = _ref.title,
    onClick = _ref.onClick;
  var handleClick = function handleClick() {
    onClick();
  };
  var handleKeyDown = function handleKeyDown(event) {
    if ('Enter' === event.key || ' ' === event.key) {
      event.preventDefault();
      onClick();
    }
  };
  return /*#__PURE__*/_react.default.createElement("div", {
    className: "e-kit-library__kit-item-actions-popover-item ".concat(className),
    role: "button",
    tabIndex: 0,
    onClick: handleClick,
    onKeyDown: handleKeyDown
  }, /*#__PURE__*/_react.default.createElement("i", {
    className: icon
  }), /*#__PURE__*/_react.default.createElement("span", null, title));
};
PopoverItem.propTypes = {
  className: _propTypes.default.string,
  icon: _propTypes.default.string.isRequired,
  title: _propTypes.default.string.isRequired,
  onClick: _propTypes.default.func.isRequired
};
var KitActionsPopover = function KitActionsPopover(_ref2) {
  var isOpen = _ref2.isOpen,
    onClose = _ref2.onClose,
    onDelete = _ref2.onDelete,
    _ref2$className = _ref2.className,
    className = _ref2$className === void 0 ? 'e-kit-library__kit-item-actions-popover' : _ref2$className;
  if (!isOpen) {
    return null;
  }
  return /*#__PURE__*/_react.default.createElement(_appUi.Popover, {
    className: className,
    closeFunction: onClose,
    arrowPosition: "none"
  }, /*#__PURE__*/_react.default.createElement(PopoverItem, {
    className: "e-kit-library__kit-item-actions-popover-item--danger",
    icon: "eicon-library-delete",
    title: (0, _i18n.__)('Delete', 'elementor'),
    onClick: onDelete
  }));
};
KitActionsPopover.propTypes = {
  isOpen: _propTypes.default.bool.isRequired,
  onClose: _propTypes.default.func.isRequired,
  onDelete: _propTypes.default.func.isRequired,
  className: _propTypes.default.string
};
var KitListCloudItem = function KitListCloudItem(props) {
  var navigate = (0, _router.useNavigate)();
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isPopoverOpen = _useState2[0],
    setIsPopoverOpen = _useState2[1];
  var eventTracking = function eventTracking(command) {
    (0, _appsEventTracking.appsEventTrackingDispatch)(command, {
      kit_name: props.model.title,
      grid_location: props.index,
      page_source: 'cloud'
    });
  };
  var handleDelete = function handleDelete() {
    setIsPopoverOpen(false);
    eventTracking('kit-library/cloud-delete');
    props.onDelete();
  };
  return /*#__PURE__*/_react.default.createElement(_appUi.Card, {
    className: "e-kit-library__kit-item"
  }, /*#__PURE__*/_react.default.createElement(_appUi.CardHeader, {
    className: "e-kit-library__kit-item-header"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Heading, {
    tag: "h3",
    title: props.model.title,
    variant: "h5",
    className: "eps-card__headline"
  }, props.model.title), /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    text: (0, _i18n.__)('Actions', 'elementor'),
    hideText: true,
    icon: "eicon-ellipsis-v",
    className: "e-kit-library__kit-item-actions-menu",
    onClick: function onClick(event) {
      event.stopPropagation();
      setIsPopoverOpen(true);
    }
  }), /*#__PURE__*/_react.default.createElement(KitActionsPopover, {
    isOpen: isPopoverOpen,
    onClose: function onClose() {
      return setIsPopoverOpen(false);
    },
    onDelete: handleDelete
  })), /*#__PURE__*/_react.default.createElement(_appUi.CardBody, null, /*#__PURE__*/_react.default.createElement(_appUi.CardImage, {
    alt: props.model.title,
    src: props.model.thumbnailUrl || ''
  }, /*#__PURE__*/_react.default.createElement(_appUi.CardOverlay, null, /*#__PURE__*/_react.default.createElement(_appUi.Grid, {
    container: true,
    direction: "column",
    className: "e-kit-library__kit-item-cloud-overlay"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    className: "eps-button e-kit-library__kit-item-cloud-overlay-import-button eps-button--primary eps-button--sm eps-button--contained",
    text: (0, _i18n.__)('Apply', 'elementor'),
    icon: "eicon-library-download",
    onClick: function onClick() {
      eventTracking('kit-library/cloud-import');
      navigate("import?referrer=kit-library&source=".concat(_useKit.KIT_SOURCE_MAP.CLOUD, "&kit_id=").concat(props.model.id), {
        replace: true
      });
    }
  }))))));
};
KitListCloudItem.propTypes = {
  model: _propTypes.default.instanceOf(_kit.default).isRequired,
  index: _propTypes.default.number,
  source: _propTypes.default.string,
  onDelete: _propTypes.default.func.isRequired
};
var _default = exports["default"] = _react.default.memo(KitListCloudItem);

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/kit-list-cloud.js":
/*!*************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/kit-list-cloud.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = KitListCloud;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ "../node_modules/@babel/runtime/regenerator/index.js"));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ "../node_modules/@babel/runtime/helpers/asyncToGenerator.js"));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _kit = _interopRequireDefault(__webpack_require__(/*! ../models/kit */ "../app/modules/kit-library/assets/js/models/kit.js"));
var _kitListCloudItem = _interopRequireDefault(__webpack_require__(/*! ./kit-list-cloud-item */ "../app/modules/kit-library/assets/js/components/kit-list-cloud-item.js"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _useKitCloudMutation = __webpack_require__(/*! ../hooks/use-kit-cloud-mutation */ "../app/modules/kit-library/assets/js/hooks/use-kit-cloud-mutation.js");
var _kitCloudDeleteDialog = _interopRequireDefault(__webpack_require__(/*! ./kit-cloud-delete-dialog */ "../app/modules/kit-library/assets/js/components/kit-cloud-delete-dialog.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function KitListCloud(props) {
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isDeleteModalOpen = _useState2[0],
    setIsDeleteModalOpen = _useState2[1];
  var _useKitCloudMutations = (0, _useKitCloudMutation.useKitCloudMutations)(),
    remove = _useKitCloudMutations.remove,
    isLoading = _useKitCloudMutations.isLoading;
  var _useState3 = (0, _react.useState)(),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    kit = _useState4[0],
    setKit = _useState4[1];
  var resetKit = (0, _react.useCallback)(function () {
    setKit(null);
    setIsDeleteModalOpen(false);
  }, []);
  var handleDelete = (0, _react.useCallback)(/*#__PURE__*/(0, _asyncToGenerator2.default)(/*#__PURE__*/_regenerator.default.mark(function _callee() {
    return _regenerator.default.wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.prev = 0;
          _context.next = 3;
          return remove.mutate(kit.id);
        case 3:
          _context.prev = 3;
          resetKit();
          return _context.finish(3);
        case 6:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[0,, 3, 6]]);
  })), [kit, remove, resetKit]);
  return /*#__PURE__*/_react.default.createElement(_appUi.CssGrid, {
    spacing: 24,
    colMinWidth: 290
  }, /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, props.data.map(function (model, index) {
    return /*#__PURE__*/_react.default.createElement(_kitListCloudItem.default, {
      key: model.id,
      model: model,
      index: index,
      source: props.source,
      onDelete: function onDelete() {
        setKit(model);
        setIsDeleteModalOpen(true);
      }
    });
  })), /*#__PURE__*/_react.default.createElement(_kitCloudDeleteDialog.default, {
    kit: kit,
    show: isDeleteModalOpen,
    onDeleteClick: handleDelete,
    onCancelClick: resetKit,
    isLoading: isLoading
  }));
}
KitListCloud.propTypes = {
  data: PropTypes.arrayOf(PropTypes.instanceOf(_kit.default)),
  source: PropTypes.string
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/kit-list-item.js":
/*!************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/kit-list-item.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _badge = _interopRequireDefault(__webpack_require__(/*! ./badge */ "../app/modules/kit-library/assets/js/components/badge.js"));
var _favoritesActions = _interopRequireDefault(__webpack_require__(/*! ../components/favorites-actions */ "../app/modules/kit-library/assets/js/components/favorites-actions.js"));
var _kit = _interopRequireDefault(__webpack_require__(/*! ../models/kit */ "../app/modules/kit-library/assets/js/models/kit.js"));
var _useKitCallToAction2 = _interopRequireWildcard(__webpack_require__(/*! ../hooks/use-kit-call-to-action */ "../app/modules/kit-library/assets/js/hooks/use-kit-call-to-action.js"));
var _useAddKitPromotionUtm = _interopRequireDefault(__webpack_require__(/*! ../hooks/use-add-kit-promotion-utm */ "../app/modules/kit-library/assets/js/hooks/use-add-kit-promotion-utm.js"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
var _i18n = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
__webpack_require__(/*! ./kit-list-item.scss */ "../app/modules/kit-library/assets/js/components/kit-list-item.scss");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var KitListItem = function KitListItem(props) {
  var _useKitCallToAction = (0, _useKitCallToAction2.default)(props.model.accessTier),
    type = _useKitCallToAction.type,
    subscriptionPlan = _useKitCallToAction.subscriptionPlan;
  var promotionUrl = (0, _useAddKitPromotionUtm.default)(subscriptionPlan.promotion_url, props.model.id, props.model.title);
  var ctaText = (0, _i18n.__)('Upgrade', 'elementor');
  var showPromotion = _useKitCallToAction2.TYPE_PROMOTION === type;
  var eventTracking = function eventTracking(command) {
    (0, _appsEventTracking.appsEventTrackingDispatch)(command, {
      kit_name: props.model.title,
      grid_location: props.index,
      search_term: props.queryParams,
      page_source: props.source && '/' === props.source ? 'all kits' : 'favorites'
    });
  };
  return /*#__PURE__*/_react.default.createElement(_appUi.Card, {
    className: "e-kit-library__kit-item"
  }, /*#__PURE__*/_react.default.createElement(_appUi.CardHeader, null, /*#__PURE__*/_react.default.createElement(_appUi.Heading, {
    tag: "h3",
    title: props.model.title,
    variant: "h5",
    className: "eps-card__headline"
  }, props.model.title), /*#__PURE__*/_react.default.createElement(_favoritesActions.default, {
    id: props.model.id,
    isFavorite: props.model.isFavorite,
    index: props.index,
    name: props.model.title,
    queryParams: props.queryParams,
    source: props.source
  })), /*#__PURE__*/_react.default.createElement(_appUi.CardBody, null, /*#__PURE__*/_react.default.createElement(_appUi.CardImage, {
    alt: props.model.title,
    src: props.model.thumbnailUrl || ''
  }, /*#__PURE__*/_react.default.createElement(_badge.default, {
    variant: "sm",
    className: "e-kit-library__kit-item-subscription-plan-badge ".concat(subscriptionPlan.isPromoted ? 'promoted' : '')
  }, subscriptionPlan.label), /*#__PURE__*/_react.default.createElement(_appUi.CardOverlay, null, /*#__PURE__*/_react.default.createElement(_appUi.Grid, {
    container: true,
    direction: "column",
    className: "e-kit-library__kit-item-overlay"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    className: "e-kit-library__kit-item-overlay-overview-button",
    text: (0, _i18n.__)('View Demo', 'elementor'),
    icon: "eicon-preview-medium",
    url: "/kit-library/preview/".concat(props.model.id),
    onClick: function onClick() {
      return eventTracking('kit-library/check-out-kit');
    }
  }), showPromotion && /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    className: "e-kit-library__kit-item-overlay-promotion-button",
    text: ctaText,
    icon: "eicon-external-link-square",
    url: promotionUrl,
    target: "_blank"
  }))))));
};
KitListItem.propTypes = {
  model: PropTypes.instanceOf(_kit.default).isRequired,
  index: PropTypes.number,
  queryParams: PropTypes.string,
  source: PropTypes.string
};
var _default = exports["default"] = _react.default.memo(KitListItem);

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/kit-list.js":
/*!*******************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/kit-list.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = KitList;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _router = __webpack_require__(/*! @reach/router */ "../node_modules/@reach/router/es/index.js");
var _kit = _interopRequireDefault(__webpack_require__(/*! ../models/kit */ "../app/modules/kit-library/assets/js/models/kit.js"));
var _kitListItem = _interopRequireDefault(__webpack_require__(/*! ./kit-list-item */ "../app/modules/kit-library/assets/js/components/kit-list-item.js"));
var _newPageKitListItem = _interopRequireDefault(__webpack_require__(/*! ../../../../onboarding/assets/js/components/new-page-kit-list-item */ "../app/modules/onboarding/assets/js/components/new-page-kit-list-item.js"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
function KitList(props) {
  var _location$pathname$sp;
  var location = (0, _router.useLocation)();
  var referrer = new URLSearchParams((_location$pathname$sp = location.pathname.split('?')) === null || _location$pathname$sp === void 0 ? void 0 : _location$pathname$sp[1]).get('referrer');
  return /*#__PURE__*/_react.default.createElement(_appUi.CssGrid, {
    spacing: 24,
    colMinWidth: 290
  }, 'onboarding' === referrer && /*#__PURE__*/_react.default.createElement(_newPageKitListItem.default, null), props.data.map(function (model, index) {
    var _props$queryParams;
    return (
      /*#__PURE__*/
      // The + 1 was added in order to start the map.index from 1 and not from 0.
      _react.default.createElement(_kitListItem.default, {
        key: model.id,
        model: model,
        index: index + 1,
        queryParams: (_props$queryParams = props.queryParams) === null || _props$queryParams === void 0 ? void 0 : _props$queryParams.search,
        source: props.source
      })
    );
  }));
}
KitList.propTypes = {
  data: PropTypes.arrayOf(PropTypes.instanceOf(_kit.default)),
  queryParams: PropTypes.shape({
    search: PropTypes.string
  }),
  source: PropTypes.string
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/layout/header-back-button.js":
/*!************************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/layout/header-back-button.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = HeaderBackButton;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _lastFilterContext = __webpack_require__(/*! ../../context/last-filter-context */ "../app/modules/kit-library/assets/js/context/last-filter-context.js");
var _router = __webpack_require__(/*! @reach/router */ "../node_modules/@reach/router/es/index.js");
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
__webpack_require__(/*! ./header-back-button.scss */ "../app/modules/kit-library/assets/js/components/layout/header-back-button.scss");
function HeaderBackButton(props) {
  var navigate = (0, _router.useNavigate)(),
    _useLastFilterContext = (0, _lastFilterContext.useLastFilterContext)(),
    lastFilter = _useLastFilterContext.lastFilter,
    eventTracking = function eventTracking(command) {
      var eventType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'click';
      (0, _appsEventTracking.appsEventTrackingDispatch)(command, {
        page_source: props.pageId,
        kit_name: props.kitName,
        element_position: 'app_header',
        event_type: eventType
      });
    };
  return /*#__PURE__*/_react.default.createElement("div", {
    className: "e-kit-library__header-back-container"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    className: "e-kit-library__header-back",
    icon: "eicon-chevron-left",
    text: __('Back to Library', 'elementor'),
    onClick: function onClick() {
      eventTracking('kit-library/back-to-library');
      navigate(wp.url.addQueryArgs('/kit-library', lastFilter));
    }
  }));
}
HeaderBackButton.propTypes = {
  pageId: PropTypes.string.isRequired,
  kitName: PropTypes.string.isRequired
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/layout/header.js":
/*!************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/layout/header.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = Header;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _headerButtons = _interopRequireDefault(__webpack_require__(/*! ../../../../../../assets/js/layout/header-buttons */ "../app/assets/js/layout/header-buttons.js"));
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
function Header(props) {
  var eventTracking = function eventTracking(command) {
      var source = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'home page';
      var kitName = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
      var eventType = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'click';
      return (0, _appsEventTracking.appsEventTrackingDispatch)(command, {
        page_source: source,
        element_position: 'app_header',
        kit_name: kitName,
        event_type: eventType
      });
    },
    onClose = function onClose() {
      eventTracking('kit-library/close', props === null || props === void 0 ? void 0 : props.pageId, props === null || props === void 0 ? void 0 : props.kitName);
      window.top.location = elementorAppConfig.admin_url;
    };
  return /*#__PURE__*/_react.default.createElement(_appUi.Grid, {
    container: true,
    alignItems: "center",
    justify: "space-between",
    className: "eps-app__header"
  }, props.startColumn || /*#__PURE__*/_react.default.createElement("a", {
    className: "eps-app__logo-title-wrapper",
    href: "#/kit-library",
    onClick: function onClick() {
      return eventTracking('kit-library/logo');
    }
  }, /*#__PURE__*/_react.default.createElement("i", {
    className: "eps-app__logo eicon-elementor"
  }), /*#__PURE__*/_react.default.createElement("h1", {
    className: "eps-app__title"
  }, __('Website Templates', 'elementor'))), props.centerColumn || /*#__PURE__*/_react.default.createElement("span", null), props.endColumn || /*#__PURE__*/_react.default.createElement("div", {
    style: {
      flex: 1
    }
  }, /*#__PURE__*/_react.default.createElement(_headerButtons.default, {
    buttons: props.buttons,
    onClose: onClose
  })));
}
Header.propTypes = {
  startColumn: PropTypes.node,
  endColumn: PropTypes.node,
  centerColumn: PropTypes.node,
  buttons: PropTypes.arrayOf(PropTypes.object),
  kitName: PropTypes.string,
  pageId: PropTypes.string
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/layout/index.js":
/*!***********************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/layout/index.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = Index;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _sidebar = _interopRequireDefault(__webpack_require__(/*! ../../../../../../assets/js/layout/sidebar */ "../app/assets/js/layout/sidebar.js"));
function Index(props) {
  return /*#__PURE__*/_react.default.createElement("div", {
    className: "eps-app__lightbox"
  }, /*#__PURE__*/_react.default.createElement("div", {
    className: "eps-app"
  }, props.header, /*#__PURE__*/_react.default.createElement("div", {
    className: "eps-app__main"
  }, props.sidebar && /*#__PURE__*/_react.default.createElement(_sidebar.default, null, props.sidebar), props.children)));
}
Index.propTypes = {
  header: PropTypes.node,
  sidebar: PropTypes.node,
  children: PropTypes.node
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/page-loader.js":
/*!**********************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/page-loader.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = PageLoader;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
__webpack_require__(/*! ./page-loader.scss */ "../app/modules/kit-library/assets/js/components/page-loader.scss");
function PageLoader(props) {
  return /*#__PURE__*/_react.default.createElement("div", {
    className: "e-kit-library__page-loader ".concat(props.className)
  }, /*#__PURE__*/_react.default.createElement(_appUi.Icon, {
    className: "eicon-loading eicon-animation-spin"
  }));
}
PageLoader.propTypes = {
  className: PropTypes.string
};
PageLoader.defaultProps = {
  className: ''
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/search-input.js":
/*!***********************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/search-input.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = SearchInput;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _useDebouncedCallback = _interopRequireDefault(__webpack_require__(/*! ../hooks/use-debounced-callback */ "../app/modules/kit-library/assets/js/hooks/use-debounced-callback.js"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
__webpack_require__(/*! ./search-input.scss */ "../app/modules/kit-library/assets/js/components/search-input.scss");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function SearchInput(props) {
  var _useState = (0, _react.useState)(props.value || ''),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    localValue = _useState2[0],
    setLocalValue = _useState2[1];
  var debouncedOnChange = (0, _useDebouncedCallback.default)(function (value) {
    return props.onChange(value);
  }, props.debounceTimeout);
  (0, _react.useEffect)(function () {
    if (props.value !== localValue) {
      setLocalValue(props.value);
    }
  }, [props.value]);
  return /*#__PURE__*/_react.default.createElement("div", {
    className: "eps-search-input__container ".concat(props.className)
  }, /*#__PURE__*/_react.default.createElement("input", {
    className: "eps-search-input eps-search-input--".concat(props.size),
    placeholder: props.placeholder,
    value: localValue,
    onChange: function onChange(e) {
      setLocalValue(e.target.value);
      debouncedOnChange(e.target.value);
    }
  }), /*#__PURE__*/_react.default.createElement(_appUi.Icon, {
    className: "eicon-search-bold eps-search-input__icon eps-search-input__icon--".concat(props.size)
  }), props.value && /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    text: __('Clear', 'elementor'),
    hideText: true,
    className: "eicon-close-circle eps-search-input__clear-icon eps-search-input__clear-icon--".concat(props.size),
    onClick: function onClick() {
      return props.onChange('');
    }
  }));
}
SearchInput.propTypes = {
  placeholder: PropTypes.string,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  className: PropTypes.string,
  size: PropTypes.oneOf(['md', 'sm']),
  debounceTimeout: PropTypes.number
};
SearchInput.defaultProps = {
  className: '',
  size: 'md',
  debounceTimeout: 300
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/sort-select.js":
/*!**********************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/sort-select.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = SortSelect;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
__webpack_require__(/*! ./sort-select.scss */ "../app/modules/kit-library/assets/js/components/sort-select.scss");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function SortSelect(props) {
  var getSelectedOptionDetails = function getSelectedOptionDetails(value) {
    return props.options.find(function (option) {
      return option.value === value;
    });
  };
  var _useState = (0, _react.useState)(getSelectedOptionDetails(props.value.by)),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    selectedSortBy = _useState2[0],
    setSelectedSortBy = _useState2[1];
  (0, _react.useEffect)(function () {
    var _selectedSortBy$defau;
    props.onChange({
      by: selectedSortBy.value,
      direction: (_selectedSortBy$defau = selectedSortBy.defaultOrder) !== null && _selectedSortBy$defau !== void 0 ? _selectedSortBy$defau : props.value.direction
    });
  }, [selectedSortBy]);
  return /*#__PURE__*/_react.default.createElement("div", {
    className: "eps-sort-select"
  }, /*#__PURE__*/_react.default.createElement("div", {
    className: "eps-sort-select__select-wrapper"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Select, {
    options: props.options,
    value: props.value.by,
    onChange: function onChange(e) {
      var _props$onChangeSortVa;
      var value = e.target.value;
      setSelectedSortBy(getSelectedOptionDetails(value));
      (_props$onChangeSortVa = props.onChangeSortValue) === null || _props$onChangeSortVa === void 0 || _props$onChangeSortVa.call(props, value);
    },
    className: "eps-sort-select__select",
    onClick: function onClick() {
      var _props$onSortSelectOp;
      props.onChange({
        by: props.value.by,
        direction: props.value.direction
      });
      (_props$onSortSelectOp = props.onSortSelectOpen) === null || _props$onSortSelectOp === void 0 || _props$onSortSelectOp.call(props);
    }
  })), !selectedSortBy.orderDisabled && /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    text: 'asc' === props.value.direction ? __('Sort Descending', 'elementor') : __('Sort Ascending', 'elementor'),
    hideText: true,
    icon: 'asc' === props.value.direction ? 'eicon-arrow-up' : 'eicon-arrow-down',
    className: "eps-sort-select__button",
    onClick: function onClick() {
      var direction = props.value.direction && 'asc' === props.value.direction ? 'desc' : 'asc';
      if (props.onChangeSortDirection) {
        props.onChangeSortDirection(direction);
      }
      props.onChange({
        by: props.value.by,
        direction: direction
      });
    }
  }));
}
SortSelect.propTypes = {
  options: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string.isRequired,
    value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired
  })).isRequired,
  value: PropTypes.shape({
    direction: PropTypes.oneOf(['asc', 'desc']).isRequired,
    by: PropTypes.string.isRequired
  }).isRequired,
  onChange: PropTypes.func.isRequired,
  onChangeSortValue: PropTypes.func,
  onSortSelectOpen: PropTypes.func,
  onChangeSortDirection: PropTypes.func
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/taxonomies-filter-list.js":
/*!*********************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/taxonomies-filter-list.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ "../node_modules/@babel/runtime/helpers/toConsumableArray.js"));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _taxonomy = _interopRequireDefault(__webpack_require__(/*! ../models/taxonomy */ "../app/modules/kit-library/assets/js/models/taxonomy.js"));
var _collapse = _interopRequireDefault(__webpack_require__(/*! ./collapse */ "../app/modules/kit-library/assets/js/components/collapse.js"));
var _searchInput = _interopRequireDefault(__webpack_require__(/*! ./search-input */ "../app/modules/kit-library/assets/js/components/search-input.js"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _i18n = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var MIN_TAGS_LENGTH_FOR_SEARCH_INPUT = 15;
var TaxonomiesFilterList = function TaxonomiesFilterList(props) {
  var _useState = (0, _react.useState)(props.taxonomiesByType.isOpenByDefault),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isOpen = _useState2[0],
    setIsOpen = _useState2[1];
  var _useState3 = (0, _react.useState)(''),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    search = _useState4[0],
    setSearch = _useState4[1];
  var taxonomies = (0, _react.useMemo)(function () {
    if (!search) {
      return props.taxonomiesByType.data;
    }
    var lowerCaseSearch = search.toLowerCase();
    return props.taxonomiesByType.data.filter(function (tag) {
      return tag.text.toLowerCase().includes(lowerCaseSearch);
    });
  }, [props.taxonomiesByType.data, search]);
  var eventTracking = function eventTracking(command, section, action, item) {
    var category = props.category && ('/favorites' === props.category ? 'favorites' : 'all kits');
    (0, _appsEventTracking.appsEventTrackingDispatch)(command, {
      page_source: 'home page',
      element_location: 'app_sidebar',
      category: category,
      section: section,
      item: item,
      action: action ? 'checked' : 'unchecked'
    });
  };
  return /*#__PURE__*/_react.default.createElement(_collapse.default, {
    className: "e-kit-library__tags-filter-list",
    title: props.taxonomiesByType.label,
    isOpen: isOpen,
    onChange: setIsOpen,
    onClick: function onClick(collapseState, title) {
      var _props$onCollapseChan;
      (_props$onCollapseChan = props.onCollapseChange) === null || _props$onCollapseChan === void 0 || _props$onCollapseChan.call(props, collapseState, title);
    }
  }, props.taxonomiesByType.data.length >= MIN_TAGS_LENGTH_FOR_SEARCH_INPUT && /*#__PURE__*/_react.default.createElement(_searchInput.default, {
    size: "sm",
    className: "e-kit-library__tags-filter-list-search"
    // Translators: %s is the taxonomy type.
    ,
    placeholder: (0, _i18n.sprintf)(__('Search %s...', 'elementor'), props.taxonomiesByType.label),
    value: search,
    onChange: function onChange(searchTerm) {
      setSearch(searchTerm);
      if (searchTerm) {
        var _props$onChange;
        (_props$onChange = props.onChange) === null || _props$onChange === void 0 || _props$onChange.call(props, searchTerm);
      }
    }
  }), /*#__PURE__*/_react.default.createElement("div", {
    className: "e-kit-library__tags-filter-list-container"
  }, 0 === taxonomies.length && /*#__PURE__*/_react.default.createElement(_appUi.Text, null, __('No Results Found', 'elementor')), taxonomies.map(function (taxonomy) {
    var _props$selected$taxon;
    return (
      /*#__PURE__*/
      // eslint-disable-next-line jsx-a11y/label-has-associated-control
      _react.default.createElement("label", {
        key: taxonomy.text,
        className: "e-kit-library__tags-filter-list-item"
      }, /*#__PURE__*/_react.default.createElement(_appUi.Checkbox, {
        checked: !!((_props$selected$taxon = props.selected[taxonomy.type]) !== null && _props$selected$taxon !== void 0 && _props$selected$taxon.includes(taxonomy.id || taxonomy.text)),
        onChange: function onChange(e) {
          var checked = e.target.checked;
          eventTracking('kit-library/filter', taxonomy.type, checked, taxonomy.text);
          props.onSelect(taxonomy.type, function (prev) {
            return checked ? [].concat((0, _toConsumableArray2.default)(prev), [taxonomy.id || taxonomy.text]) : prev.filter(function (tagId) {
              return ![taxonomy.id, taxonomy.text].includes(tagId);
            });
          });
        }
      }), taxonomy.text)
    );
  })));
};
TaxonomiesFilterList.propTypes = {
  taxonomiesByType: PropTypes.shape({
    key: PropTypes.string,
    label: PropTypes.string,
    data: PropTypes.arrayOf(PropTypes.instanceOf(_taxonomy.default)),
    isOpenByDefault: PropTypes.bool
  }),
  selected: PropTypes.objectOf(PropTypes.arrayOf(PropTypes.string)),
  onSelect: PropTypes.func,
  onCollapseChange: PropTypes.func,
  category: PropTypes.string,
  onChange: PropTypes.func
};
var _default = exports["default"] = _react.default.memo(TaxonomiesFilterList);

/***/ }),

/***/ "../app/modules/kit-library/assets/js/components/taxonomies-filter.js":
/*!****************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/components/taxonomies-filter.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = TaxonomiesFilter;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _taxonomiesFilterList = _interopRequireDefault(__webpack_require__(/*! ./taxonomies-filter-list */ "../app/modules/kit-library/assets/js/components/taxonomies-filter-list.js"));
var _taxonomy = _interopRequireDefault(__webpack_require__(/*! ../models/taxonomy */ "../app/modules/kit-library/assets/js/models/taxonomy.js"));
var _taxonomyTransformer = __webpack_require__(/*! ../models/taxonomy-transformer */ "../app/modules/kit-library/assets/js/models/taxonomy-transformer.js");
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
__webpack_require__(/*! ./tags-filter.scss */ "../app/modules/kit-library/assets/js/components/tags-filter.scss");
var _React = _react.default,
  useMemo = _React.useMemo;
function TaxonomiesFilter(props) {
  var taxonomiesByType = useMemo(function () {
      return (0, _taxonomyTransformer.getTaxonomyFilterItems)(props.taxonomies);
    }, [props.taxonomies]),
    eventTracking = function eventTracking(command, search, section) {
      var eventType = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'click';
      return (0, _appsEventTracking.appsEventTrackingDispatch)(command, {
        page_source: 'home page',
        element_location: 'app_sidebar',
        category: props.category && ('/favorites' === props.category ? 'favorites' : 'all kits'),
        section: section,
        search_term: search,
        event_type: eventType
      });
    };
  return /*#__PURE__*/_react.default.createElement("div", {
    className: "e-kit-library__tags-filter"
  }, taxonomiesByType.map(function (group) {
    return /*#__PURE__*/_react.default.createElement(_taxonomiesFilterList.default, {
      key: group.key,
      taxonomiesByType: group,
      selected: props.selected,
      onSelect: props.onSelect,
      onCollapseChange: function onCollapseChange(collapseState, title) {
        var command = collapseState ? 'kit-library/collapse' : 'kit-library/expand';
        eventTracking(command, null, title);
      },
      onChange: function onChange(search) {
        eventTracking('kit-library/filter', search, group.label, 'search');
      },
      category: props.category
    });
  }));
}
TaxonomiesFilter.propTypes = {
  selected: PropTypes.objectOf(PropTypes.arrayOf(PropTypes.string)),
  onSelect: PropTypes.func,
  taxonomies: PropTypes.arrayOf(PropTypes.instanceOf(_taxonomy.default)),
  category: PropTypes.string
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/context/connect-state-context.js":
/*!*****************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/context/connect-state-context.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.ConnectStateContext = void 0;
exports.ConnectStateProvider = ConnectStateProvider;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _propTypes = _interopRequireDefault(__webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var ConnectStateContext = exports.ConnectStateContext = (0, _react.createContext)();
function ConnectStateProvider(_ref) {
  var children = _ref.children;
  var _useState = (0, _react.useState)(elementorCommon.config.library_connect.is_connected),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isConnected = _useState2[0],
    setIsConnected = _useState2[1];
  var _useState3 = (0, _react.useState)(false),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    isConnecting = _useState4[0],
    setIsConnecting = _useState4[1];
  var handleConnectSuccess = (0, _react.useCallback)(function (callback) {
    setIsConnecting(true);
    setIsConnected(true);
    elementorCommon.config.library_connect.is_connected = true;
    if (callback) {
      callback();
    }
  }, []);
  var handleConnectError = (0, _react.useCallback)(function (callback) {
    setIsConnected(false);
    setIsConnecting(false);
    elementorCommon.config.library_connect.is_connected = false;
    if (callback) {
      callback();
    }
  }, []);
  var setConnecting = (0, _react.useCallback)(function (connecting) {
    setIsConnecting(connecting);
  }, []);
  var value = {
    isConnected: isConnected,
    isConnecting: isConnecting,
    setConnecting: setConnecting,
    handleConnectSuccess: handleConnectSuccess,
    handleConnectError: handleConnectError
  };
  return /*#__PURE__*/_react.default.createElement(ConnectStateContext.Provider, {
    value: value
  }, children);
}
ConnectStateProvider.propTypes = {
  children: _propTypes.default.node.isRequired
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/context/last-filter-context.js":
/*!***************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/context/last-filter-context.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.LastFilterProvider = LastFilterProvider;
exports.useLastFilterContext = useLastFilterContext;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var LastFilterContext = (0, _react.createContext)({});

/**
 * Consume the context
 *
 * @return {{}} context value
 */
function useLastFilterContext() {
  return (0, _react.useContext)(LastFilterContext);
}

/**
 * Settings Provider
 *
 * @param {*} props
 * @return {JSX.Element} element
 * @function Object() { [native code] }
 */
function LastFilterProvider(props) {
  var _useState = (0, _react.useState)({}),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    lastFilter = _useState2[0],
    setLastFilter = _useState2[1];
  return /*#__PURE__*/_react.default.createElement(LastFilterContext.Provider, {
    value: {
      lastFilter: lastFilter,
      setLastFilter: setLastFilter
    }
  }, props.children);
}
LastFilterProvider.propTypes = {
  children: PropTypes.any
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/context/settings-context.js":
/*!************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/context/settings-context.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.SettingsProvider = SettingsProvider;
exports.useSettingsContext = useSettingsContext;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ "../node_modules/@babel/runtime/helpers/defineProperty.js"));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
var SettingsContext = (0, _react.createContext)({});

/**
 * Consume the context
 *
 * @return {{emptyTrashDays: number}} context value
 */
function useSettingsContext() {
  return (0, _react.useContext)(SettingsContext);
}

/**
 * Settings Provider
 *
 * @param {*} props
 * @return {JSX.Element} element
 * @function Object() { [native code] }
 */
function SettingsProvider(props) {
  var _useState = (0, _react.useState)({}),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    settings = _useState2[0],
    setSettings = _useState2[1];
  var updateSettings = (0, _react.useCallback)(function (newSettings) {
    setSettings(function (prev) {
      return _objectSpread(_objectSpread({}, prev), newSettings);
    });
  }, [setSettings]);
  (0, _react.useEffect)(function () {
    setSettings(props.value);
  }, [setSettings]);
  return /*#__PURE__*/_react.default.createElement(SettingsContext.Provider, {
    value: {
      settings: settings,
      setSettings: setSettings,
      updateSettings: updateSettings
    }
  }, props.children);
}
SettingsProvider.propTypes = {
  children: PropTypes.any,
  value: PropTypes.object.isRequired
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/hooks/use-add-kit-promotion-utm.js":
/*!*******************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/hooks/use-add-kit-promotion-utm.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = useAddKitPromotionUTM;
function useAddKitPromotionUTM(promotionUrl, kitId, kitTitle) {
  if (!promotionUrl) {
    return '';
  }
  var url;
  try {
    url = new URL(promotionUrl);
  } catch (e) {
    return '';
  }
  if (kitTitle && 'string' === typeof kitTitle) {
    var cleanTitle = kitTitle.trim().replace(/\s+/g, '-').replace(/[^\w-]/g, '').toLowerCase();
    url.searchParams.set('utm_term', cleanTitle);
  }
  if (kitId && 'string' === typeof kitId) {
    url.searchParams.set('utm_content', kitId);
  }
  return url.toString();
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/hooks/use-cloud-kits.js":
/*!********************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/hooks/use-cloud-kits.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.KEY = void 0;
exports["default"] = useCloudKits;
exports.defaultQueryParams = void 0;
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ "../node_modules/@babel/runtime/helpers/toConsumableArray.js"));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ "../node_modules/@babel/runtime/helpers/defineProperty.js"));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _kit = _interopRequireDefault(__webpack_require__(/*! ../models/kit */ "../app/modules/kit-library/assets/js/models/kit.js"));
var _reactQuery = __webpack_require__(/*! react-query */ "../node_modules/react-query/es/index.js");
var _react = __webpack_require__(/*! react */ "react");
var _utils = __webpack_require__(/*! ../utils */ "../app/modules/kit-library/assets/js/utils.js");
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
var KEY = exports.KEY = 'cloud-kits';

/**
 * The default query params
 *
 * @type {Object}
 */
var defaultQueryParams = exports.defaultQueryParams = {
  search: '',
  referrer: null
};
var kitsPipeFunctions = {
  /**
   * Filter by search term.
   *
   * @param {Array<*>} data
   * @param {*}        queryParams
   * @return {Array} filtered data
   */
  searchFilter: function searchFilter(data, queryParams) {
    if (!queryParams.search) {
      return data;
    }
    return data.filter(function (item) {
      var keywords = [item.title];
      var searchTerm = queryParams.search.toLowerCase();
      return keywords.some(function (keyword) {
        return keyword.toLowerCase().includes(searchTerm);
      });
    });
  }
};

/**
 * Fetch kits
 *
 * @return {*} kits
 */
function fetchKits() {
  return $e.data.get('cloud-kits/index', {}, {
    refresh: true
  }).then(function (response) {
    return response.data;
  }).then(function (_ref) {
    var data = _ref.data;
    return data.map(function (item) {
      return _kit.default.createFromResponse(item);
    });
  });
}

/**
 * Main function.
 *
 * @param {*} initialQueryParams
 * @return {Object} query
 */
function useCloudKits() {
  var initialQueryParams = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    force = _useState2[0],
    setForce = _useState2[1];
  var _useState3 = (0, _react.useState)(function () {
      return _objectSpread(_objectSpread({
        ready: false
      }, defaultQueryParams), initialQueryParams);
    }),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    queryParams = _useState4[0],
    setQueryParams = _useState4[1];
  var forceRefetch = (0, _react.useCallback)(function () {
    return setForce(true);
  }, [setForce]);
  var clearQueryParams = (0, _react.useCallback)(function () {
    return setQueryParams(_objectSpread(_objectSpread({
      ready: true
    }, defaultQueryParams), initialQueryParams));
  }, [setQueryParams]);
  var query = (0, _reactQuery.useQuery)([KEY], function () {
    return fetchKits(force);
  });
  var data = (0, _react.useMemo)(function () {
    return !query.data ? [] : _utils.pipe.apply(void 0, (0, _toConsumableArray2.default)(Object.values(kitsPipeFunctions)))((0, _toConsumableArray2.default)(query.data), queryParams);
  }, [query.data, queryParams]);
  var isFilterActive = (0, _react.useMemo)(function () {
    return !!queryParams.search;
  }, [queryParams]);
  (0, _react.useEffect)(function () {
    if (!force) {
      return;
    }
    query.refetch().then(function () {
      return setForce(false);
    });
  }, [force]);
  return _objectSpread(_objectSpread({}, query), {}, {
    data: data,
    queryParams: queryParams,
    setQueryParams: setQueryParams,
    clearQueryParams: clearQueryParams,
    forceRefetch: forceRefetch,
    isFilterActive: isFilterActive
  });
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/hooks/use-connect-state.js":
/*!***********************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/hooks/use-connect-state.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = useConnectState;
var _react = __webpack_require__(/*! react */ "react");
var _connectStateContext = __webpack_require__(/*! ../context/connect-state-context */ "../app/modules/kit-library/assets/js/context/connect-state-context.js");
function useConnectState() {
  var context = (0, _react.useContext)(_connectStateContext.ConnectStateContext);
  if (!context) {
    throw new Error('useConnectState must be used within a ConnectStateProvider');
  }
  return context;
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/hooks/use-content-types.js":
/*!***********************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/hooks/use-content-types.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.KEY = void 0;
exports["default"] = useContentTypes;
var _contentType = _interopRequireDefault(__webpack_require__(/*! ../models/content-type */ "../app/modules/kit-library/assets/js/models/content-type.js"));
var _reactQuery = __webpack_require__(/*! react-query */ "../node_modules/react-query/es/index.js");
var _settingsContext = __webpack_require__(/*! ../context/settings-context */ "../app/modules/kit-library/assets/js/context/settings-context.js");
var _tiers = __webpack_require__(/*! elementor-utils/tiers */ "../assets/dev/js/utils/tiers.js");
var KEY = exports.KEY = 'content-types';

/**
 * The data should come from the server, this is a temp solution that helps to demonstrate that data comes from the server
 * but for now this is a local data.
 *
 * @return {import('react-query').UseQueryResult<Promise.constructor, unknown>} result
 */
function useContentTypes() {
  var _useSettingsContext = (0, _settingsContext.useSettingsContext)(),
    settings = _useSettingsContext.settings;
  return (0, _reactQuery.useQuery)([KEY, settings], function () {
    return fetchContentTypes(settings);
  });
}

/**
 * @param {Object} settings - Current settings
 *
 * @return {Promise.constructor} content types
 */
function fetchContentTypes(settings) {
  var contentTypes = [{
    id: 'page',
    label: __('Pages', 'elementor'),
    doc_types: ['wp-page'],
    order: 0
  }, {
    id: 'site-parts',
    label: __('Site Parts', 'elementor'),
    doc_types: ['archive', 'error-404', 'footer', 'header', 'search-results', 'single-page', 'single-post',
    // WooCommerce types
    'product', 'product-archive',
    // Legacy Types
    '404', 'single'],
    order: 1
  }];

  // BC: When user has old Pro version which doesn't override the `free` access_tier.
  var userAccessTier = settings.access_tier;
  var hasActiveProLicense = settings.is_pro && settings.is_library_connected;
  var shouldFallbackToLegacy = hasActiveProLicense && userAccessTier === _tiers.TIERS.free;

  // Fallback to the last access_tier before the new tiers were introduced.
  // TODO: Remove when Pro with the new tiers is stable.
  if (shouldFallbackToLegacy) {
    userAccessTier = _tiers.TIERS['essential-oct2023'];
  }
  var tierThatSupportsPopups = _tiers.TIERS['essential-oct2023'];
  if ((0, _tiers.isTierAtLeast)(userAccessTier, tierThatSupportsPopups)) {
    contentTypes.push({
      id: 'popup',
      label: __('Popups', 'elementor'),
      doc_types: ['popup'],
      order: 2
    });
  }
  return Promise.resolve(contentTypes).then(function (data) {
    return data.map(function (contentType) {
      return _contentType.default.createFromResponse(contentType);
    });
  });
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/hooks/use-debounced-callback.js":
/*!****************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/hooks/use-debounced-callback.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = useDebouncedCallback;
var _react = __webpack_require__(/*! react */ "react");
function useDebouncedCallback(callback, wait) {
  var timeout = (0, _react.useRef)();
  return (0, _react.useCallback)(function () {
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    var later = function later() {
      clearTimeout(timeout.current);
      callback.apply(void 0, args);
    };
    clearTimeout(timeout.current);
    timeout.current = setTimeout(later, wait);
  }, [callback, wait]);
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/hooks/use-download-link-mutation.js":
/*!********************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/hooks/use-download-link-mutation.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = useDownloadLinkMutation;
var _react = __webpack_require__(/*! react */ "react");
var _reactQuery = __webpack_require__(/*! react-query */ "../node_modules/react-query/es/index.js");
function useDownloadLinkMutation(model, _ref) {
  var onError = _ref.onError,
    onSuccess = _ref.onSuccess;
  var downloadLink = (0, _react.useCallback)(function () {
    return $e.data.get('kits/download-link', {
      id: model.id
    }, {
      refresh: true
    });
  }, [model]);
  return (0, _reactQuery.useMutation)(downloadLink, {
    onSuccess: onSuccess,
    onError: onError
  });
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/hooks/use-kit-call-to-action.js":
/*!****************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/hooks/use-kit-call-to-action.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.TYPE_PROMOTION = exports.TYPE_CONNECT = exports.TYPE_APPLY = void 0;
exports["default"] = useKitCallToAction;
var _react = __webpack_require__(/*! react */ "react");
var _settingsContext = __webpack_require__(/*! ../context/settings-context */ "../app/modules/kit-library/assets/js/context/settings-context.js");
var _tiers = __webpack_require__(/*! elementor-utils/tiers */ "../assets/dev/js/utils/tiers.js");
var _taxonomyTransformer = __webpack_require__(/*! ../models/taxonomy-transformer */ "../app/modules/kit-library/assets/js/models/taxonomy-transformer.js");
var TYPE_CONNECT = exports.TYPE_CONNECT = 'connect';
var TYPE_PROMOTION = exports.TYPE_PROMOTION = 'promotion';
var TYPE_APPLY = exports.TYPE_APPLY = 'apply';
function useKitCallToAction(kitAccessTier) {
  var _useSettingsContext = (0, _settingsContext.useSettingsContext)(),
    settings = _useSettingsContext.settings;

  // BC: When user has old Pro version which doesn't override the `free` access_tier.
  var userAccessTier = settings.access_tier;
  var tierKey = _taxonomyTransformer.TierToKeyMap[kitAccessTier];
  var hasActiveProLicense = settings.is_pro && settings.is_library_connected;
  var shouldFallbackToLegacy = hasActiveProLicense && userAccessTier === _tiers.TIERS.free;

  // Fallback to the last access_tier before the new tiers were introduced.
  // TODO: Remove when Pro with the new tiers is stable.
  if (shouldFallbackToLegacy) {
    userAccessTier = _tiers.TIERS['essential-oct2023'];
  }

  // SubscriptionPlan can be null when the context is not filled (can be happened when using back button in the browser.)
  var subscriptionPlan = (0, _react.useMemo)(function () {
    var _settings$subscriptio;
    return (_settings$subscriptio = settings.subscription_plans) === null || _settings$subscriptio === void 0 ? void 0 : _settings$subscriptio[kitAccessTier];
  }, [settings, kitAccessTier]);
  subscriptionPlan.label = _taxonomyTransformer.PromotionChipText[tierKey];
  subscriptionPlan.isPromoted = _tiers.TIERS.free !== kitAccessTier;
  var type = (0, _react.useMemo)(function () {
    // The user can apply this kit (the user access level is equal or greater then the kit access level).
    var isAuthorizeToApplyKit = (0, _tiers.isTierAtLeast)(userAccessTier, kitAccessTier);

    // The user in not connected and has pro plugin or the kit is a free kit.
    if (!settings.is_library_connected && (settings.is_pro || isAuthorizeToApplyKit)) {
      return TYPE_CONNECT;
    }

    // The user is connected or has only core plugin and cannot access this kit.
    if (!isAuthorizeToApplyKit) {
      return TYPE_PROMOTION;
    }

    // The user is connected and can access the kit.
    return TYPE_APPLY;
  }, [settings, kitAccessTier]);
  return {
    type: type,
    subscriptionPlan: subscriptionPlan
  };
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/hooks/use-kit-cloud-mutation.js":
/*!****************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/hooks/use-kit-cloud-mutation.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.useKitCloudMutations = useKitCloudMutations;
var _reactQuery = __webpack_require__(/*! react-query */ "../node_modules/react-query/es/index.js");
var _useCloudKits = __webpack_require__(/*! ../hooks/use-cloud-kits */ "../app/modules/kit-library/assets/js/hooks/use-cloud-kits.js");
function useKitCloudMutations() {
  var queryClient = (0, _reactQuery.useQueryClient)();
  var remove = (0, _reactQuery.useMutation)(function (id) {
    return $e.data.delete('cloud-kits/index', {
      id: id
    });
  }, {
    onSuccess: function onSuccess() {
      return queryClient.invalidateQueries(_useCloudKits.KEY);
    }
  });
  return {
    remove: remove,
    isLoading: remove.isLoading
  };
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/hooks/use-kit-document-by-type.js":
/*!******************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/hooks/use-kit-document-by-type.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = useKitDocumentByType;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ "../node_modules/@babel/runtime/helpers/defineProperty.js"));
var _useContentTypes = _interopRequireDefault(__webpack_require__(/*! ./use-content-types */ "../app/modules/kit-library/assets/js/hooks/use-content-types.js"));
var _react = __webpack_require__(/*! react */ "react");
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function useKitDocumentByType(kit) {
  var contentTypesQuery = (0, _useContentTypes.default)();
  var data = (0, _react.useMemo)(function () {
    if (!kit || !contentTypesQuery.data) {
      return [];
    }
    return kit.getDocumentsByTypes(contentTypesQuery.data).sort(function (a, b) {
      return a.order - b.order;
    });
  }, [kit, contentTypesQuery.data]);
  return _objectSpread(_objectSpread({}, contentTypesQuery), {}, {
    data: data
  });
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/hooks/use-kit-favorites-mutations.js":
/*!*********************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/hooks/use-kit-favorites-mutations.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.useKitFavoritesMutations = useKitFavoritesMutations;
var _react = __webpack_require__(/*! react */ "react");
var _useKits = __webpack_require__(/*! ../hooks/use-kits */ "../app/modules/kit-library/assets/js/hooks/use-kits.js");
var _useKit = __webpack_require__(/*! ../hooks/use-kit */ "../app/modules/kit-library/assets/js/hooks/use-kit.js");
var _reactQuery = __webpack_require__(/*! react-query */ "../node_modules/react-query/es/index.js");
function useKitFavoritesMutations() {
  var queryClient = (0, _reactQuery.useQueryClient)();
  var onSuccess = (0, _react.useCallback)(function (_ref) {
    var data = _ref.data;
    var id = data.data.id;
    var isFavorite = data.data.is_favorite;

    // Update the kit list if the list exists.
    if (queryClient.getQueryData([_useKits.KEY])) {
      queryClient.setQueryData([_useKits.KEY], function (kits) {
        if (!kits) {
          return kits;
        }
        return kits.map(function (item) {
          if (item.id === id) {
            item.isFavorite = isFavorite;

            // Should return a new kit to trigger rerender.
            return item.clone();
          }
          return item;
        });
      });
    }

    // Update specific kit if the kit exists
    if (queryClient.getQueryData([_useKit.KEY, id])) {
      queryClient.setQueryData([_useKit.KEY, id], function (currentKit) {
        currentKit.isFavorite = isFavorite;

        // Should return a new kit to trigger rerender.
        return currentKit.clone();
      });
    }
  }, [queryClient]);
  var addToFavorites = (0, _reactQuery.useMutation)(function (id) {
    return $e.data.create('kits/favorites', {}, {
      id: id
    });
  }, {
    onSuccess: onSuccess
  });
  var removeFromFavorites = (0, _reactQuery.useMutation)(function (id) {
    return $e.data.delete('kits/favorites', {
      id: id
    });
  }, {
    onSuccess: onSuccess
  });
  return {
    addToFavorites: addToFavorites,
    removeFromFavorites: removeFromFavorites,
    isLoading: addToFavorites.isLoading || removeFromFavorites.isLoading
  };
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/hooks/use-kit.js":
/*!*************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/hooks/use-kit.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.KEY = void 0;
exports["default"] = useKit;
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _kit = _interopRequireDefault(__webpack_require__(/*! ../models/kit */ "../app/modules/kit-library/assets/js/models/kit.js"));
var _useKits = __webpack_require__(/*! ./use-kits */ "../app/modules/kit-library/assets/js/hooks/use-kits.js");
var _react = __webpack_require__(/*! react */ "react");
var _reactQuery = __webpack_require__(/*! react-query */ "../node_modules/react-query/es/index.js");
var KEY = exports.KEY = 'kit';
function useKit(id) {
  // A function that returns existing data from the kit list for a placeholder data before the kit request will resolved.
  var placeholderDataCallback = usePlaceholderDataCallback(id);
  return (0, _reactQuery.useQuery)([KEY, id], fetchKitItem, {
    placeholderData: placeholderDataCallback
  });
}

/**
 * Return placeholder function for kit query.
 *
 * @param {*} id
 * @return {function(): (undefined|*)} placeholder
 */
function usePlaceholderDataCallback(id) {
  var queryClient = (0, _reactQuery.useQueryClient)();
  return (0, _react.useCallback)(function () {
    var _queryClient$getQuery;
    var placeholder = (_queryClient$getQuery = queryClient.getQueryData(_useKits.KEY)) === null || _queryClient$getQuery === void 0 ? void 0 : _queryClient$getQuery.find(function (kit) {
      return kit.id === id;
    });
    if (!placeholder) {
      return;
    }
    return placeholder;
  }, [queryClient, id]);
}

/**
 * Fetch kit
 *
 * @param {Object} root0
 * @param {Object} root0.queryKey
 * @param {*}      root0.queryKey.0
 * @param {string} root0.queryKey.1
 * @return {Promise<Kit>} kit
 */
// eslint-disable-next-line no-unused-vars
function fetchKitItem(_ref) {
  var _ref$queryKey = (0, _slicedToArray2.default)(_ref.queryKey, 2),
    _ = _ref$queryKey[0],
    id = _ref$queryKey[1];
  return $e.data.get('kits/index', {
    id: id
  }, {
    refresh: true
  }).then(function (response) {
    return response.data;
  }).then(function (_ref2) {
    var data = _ref2.data;
    return _kit.default.createFromResponse(data);
  });
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/hooks/use-kits.js":
/*!**************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/hooks/use-kits.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.KEY = void 0;
exports["default"] = useKits;
exports.defaultQueryParams = void 0;
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ "../node_modules/@babel/runtime/helpers/toConsumableArray.js"));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ "../node_modules/@babel/runtime/helpers/defineProperty.js"));
var _kit = _interopRequireDefault(__webpack_require__(/*! ../models/kit */ "../app/modules/kit-library/assets/js/models/kit.js"));
var _useSelectedTaxonomies = _interopRequireDefault(__webpack_require__(/*! ./use-selected-taxonomies */ "../app/modules/kit-library/assets/js/hooks/use-selected-taxonomies.js"));
var _taxonomy = __webpack_require__(/*! ../models/taxonomy */ "../app/modules/kit-library/assets/js/models/taxonomy.js");
var _taxonomyTransformer = __webpack_require__(/*! ../models/taxonomy-transformer */ "../app/modules/kit-library/assets/js/models/taxonomy-transformer.js");
var _reactQuery = __webpack_require__(/*! react-query */ "../node_modules/react-query/es/index.js");
var _react = __webpack_require__(/*! react */ "react");
var _utils = __webpack_require__(/*! ../utils */ "../app/modules/kit-library/assets/js/utils.js");
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
var KEY = exports.KEY = 'kits';

/**
 * The default query params
 *
 * @type {Object}
 */
var defaultQueryParams = exports.defaultQueryParams = {
  favorite: false,
  search: '',
  taxonomies: _taxonomy.TaxonomyTypes.reduce(function (current, _ref) {
    var key = _ref.key;
    return _objectSpread(_objectSpread({}, current), {}, (0, _defineProperty2.default)({}, key, []));
  }, {}),
  order: {
    direction: 'asc',
    by: 'featuredIndex'
  },
  referrer: null
};
var kitsPipeFunctions = {
  /**
   * Filter by favorite
   *
   * @param {Array<*>} data
   * @param {*}        queryParams
   * @return {Array} filtered data
   */
  favoriteFilter: function favoriteFilter(data, queryParams) {
    if (!queryParams.favorite) {
      return data;
    }
    return data.filter(function (item) {
      return item.isFavorite;
    });
  },
  /**
   * Filter by search term.
   *
   * @param {Array<*>} data
   * @param {*}        queryParams
   * @return {Array} filtered data
   */
  searchFilter: function searchFilter(data, queryParams) {
    if (!queryParams.search) {
      return data;
    }
    return data.filter(function (item) {
      var keywords = [].concat((0, _toConsumableArray2.default)(item.keywords), (0, _toConsumableArray2.default)(item.taxonomies), [item.title]);
      var searchTerm = queryParams.search.toLowerCase();
      return keywords.some(function (keyword) {
        return keyword.toLowerCase().includes(searchTerm);
      });
    });
  },
  /**
   * Filter by taxonomies.
   * In each taxonomy type it use the OR operator and between types it uses the AND operator.
   *
   * @param {Array<*>} data
   * @param {*}        queryParams
   * @return {Array} filtered data
   */
  taxonomiesFilter: function taxonomiesFilter(data, queryParams) {
    var taxonomyTypes = Object.keys(queryParams.taxonomies).filter(function (taxonomyType) {
      return queryParams.taxonomies[taxonomyType].length;
    });
    return !taxonomyTypes.length ? data : data.filter(function (kit) {
      return taxonomyTypes.some(function (taxonomyType) {
        return (0, _taxonomyTransformer.isKitInTaxonomy)(kit, taxonomyType, queryParams.taxonomies[taxonomyType]);
      });
    });
  },
  /**
   * Sort all the data by the "order" query param
   *
   * @param {Array<*>} data
   * @param {*}        queryParams
   * @return {Array} sorted data
   */
  sort: function sort(data, queryParams) {
    var order = queryParams.order;
    return data.sort(function (item1, item2) {
      if ('asc' === order.direction) {
        return item1[order.by] - item2[order.by];
      }
      return item2[order.by] - item1[order.by];
    });
  }
};

/**
 * Fetch kits
 *
 * @param {boolean} force
 * @return {*} kits
 */
function fetchKits(force) {
  return $e.data.get('kits/index', {
    force: force ? 1 : undefined
  }, {
    refresh: true
  }).then(function (response) {
    return response.data;
  }).then(function (_ref2) {
    var data = _ref2.data;
    return data.map(function (item) {
      return _kit.default.createFromResponse(item);
    });
  });
}

/**
 * Main function.
 *
 * @param {*} initialQueryParams
 * @return {Object} query
 */
function useKits() {
  var initialQueryParams = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    force = _useState2[0],
    setForce = _useState2[1];
  var _useState3 = (0, _react.useState)(function () {
      return _objectSpread(_objectSpread({
        ready: false
      }, defaultQueryParams), initialQueryParams);
    }),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    queryParams = _useState4[0],
    setQueryParams = _useState4[1];
  var forceRefetch = (0, _react.useCallback)(function () {
    return setForce(true);
  }, [setForce]);
  var clearQueryParams = (0, _react.useCallback)(function () {
    return setQueryParams(_objectSpread(_objectSpread({
      ready: true
    }, defaultQueryParams), initialQueryParams));
  }, [setQueryParams]);
  var query = (0, _reactQuery.useQuery)([KEY], function () {
    return fetchKits(force);
  });
  var data = (0, _react.useMemo)(function () {
    return !query.data ? [] : _utils.pipe.apply(void 0, (0, _toConsumableArray2.default)(Object.values(kitsPipeFunctions)))((0, _toConsumableArray2.default)(query.data), queryParams);
  }, [query.data, queryParams]);
  var selectedTaxonomies = (0, _useSelectedTaxonomies.default)(queryParams.taxonomies);
  var isFilterActive = (0, _react.useMemo)(function () {
    return !!queryParams.search || !!selectedTaxonomies.length;
  }, [queryParams]);
  (0, _react.useEffect)(function () {
    if (!force) {
      return;
    }
    query.refetch().then(function () {
      return setForce(false);
    });
  }, [force]);
  return _objectSpread(_objectSpread({}, query), {}, {
    data: data,
    queryParams: queryParams,
    setQueryParams: setQueryParams,
    clearQueryParams: clearQueryParams,
    forceRefetch: forceRefetch,
    isFilterActive: isFilterActive
  });
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/hooks/use-menu-items.js":
/*!********************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/hooks/use-menu-items.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = useMenuItems;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _useCloudKitsEligibility = _interopRequireDefault(__webpack_require__(/*! elementor-app/hooks/use-cloud-kits-eligibility */ "../app/assets/js/hooks/use-cloud-kits-eligibility.js"));
var _useConnectState2 = _interopRequireDefault(__webpack_require__(/*! ./use-connect-state */ "../app/modules/kit-library/assets/js/hooks/use-connect-state.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * Generate the menu items for the kit library pages.
 *
 * @param {string} path - The current page path
 * @return {Array} menu items
 */
function useMenuItems(path) {
  var _useConnectState = (0, _useConnectState2.default)(),
    isConnected = _useConnectState.isConnected;
  var _useCloudKitsEligibil = (0, _useCloudKitsEligibility.default)({
      enabled: isConnected
    }),
    cloudKitsData = _useCloudKitsEligibil.data;
  var isCloudKitsAvailable = cloudKitsData === null || cloudKitsData === void 0 ? void 0 : cloudKitsData.is_eligible;
  return (0, _react.useMemo)(function () {
    var page = path.replace('/', '');
    var myWebsiteTemplatesLabel = __('My Website Templates', 'elementor');
    if (!isConnected) {
      myWebsiteTemplatesLabel = /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, __('My Website Templates', 'elementor'), /*#__PURE__*/_react.default.createElement("span", {
        className: "connect-badge"
      }, __('Connect', 'elementor')));
    } else if (isConnected && false === isCloudKitsAvailable) {
      myWebsiteTemplatesLabel = /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, __('My Website Templates', 'elementor'), /*#__PURE__*/_react.default.createElement("span", {
        className: "upgrade-badge"
      }, __('Upgrade', 'elementor')));
    }
    var menuItems = [{
      label: __('All Website Templates', 'elementor'),
      icon: 'eicon-filter',
      isActive: !page,
      url: '/kit-library',
      trackEventData: {
        command: 'kit-library/select-organizing-category',
        category: 'all'
      }
    }, {
      label: myWebsiteTemplatesLabel,
      icon: 'eicon-library-cloud-empty',
      isActive: 'cloud' === page,
      url: '/kit-library/cloud',
      trackEventData: {
        command: 'kit-library/select-organizing-category',
        category: 'cloud'
      }
    }, {
      label: __('Favorites', 'elementor'),
      icon: 'eicon-heart-o',
      isActive: 'favorites' === page,
      url: '/kit-library/favorites',
      trackEventData: {
        command: 'kit-library/select-organizing-category',
        category: 'favorites'
      }
    }];
    return menuItems;
  }, [path, isConnected, isCloudKitsAvailable]);
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/hooks/use-selected-taxonomies.js":
/*!*****************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/hooks/use-selected-taxonomies.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = useSelectedTaxonomies;
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ "../node_modules/@babel/runtime/helpers/toConsumableArray.js"));
var _react = __webpack_require__(/*! react */ "react");
function useSelectedTaxonomies(taxonomiesFilter) {
  return (0, _react.useMemo)(function () {
    return Object.values(taxonomiesFilter).reduce(function (current, groupedTaxonomies) {
      return [].concat((0, _toConsumableArray2.default)(current), (0, _toConsumableArray2.default)(groupedTaxonomies));
    });
  }, [taxonomiesFilter]);
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/hooks/use-taxonomies.js":
/*!********************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/hooks/use-taxonomies.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.KEY = void 0;
exports["default"] = useTaxonomies;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ "../node_modules/@babel/runtime/helpers/defineProperty.js"));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _taxonomy = _interopRequireDefault(__webpack_require__(/*! ../models/taxonomy */ "../app/modules/kit-library/assets/js/models/taxonomy.js"));
var _reactQuery = __webpack_require__(/*! react-query */ "../node_modules/react-query/es/index.js");
var _react = __webpack_require__(/*! react */ "react");
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
var KEY = exports.KEY = 'tags';
function useTaxonomies() {
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    force = _useState2[0],
    setForce = _useState2[1];
  var forceRefetch = (0, _react.useCallback)(function () {
    return setForce(true);
  }, [setForce]);
  var query = (0, _reactQuery.useQuery)([KEY], function () {
    return fetchTaxonomies(force);
  });
  (0, _react.useEffect)(function () {
    if (!force) {
      return;
    }
    query.refetch().then(function () {
      return setForce(false);
    });
  }, [force]);
  return _objectSpread(_objectSpread({}, query), {}, {
    forceRefetch: forceRefetch
  });
}
function fetchTaxonomies(force) {
  return $e.data.get('kit-taxonomies/index', {
    force: force ? 1 : undefined
  }, {
    refresh: true
  }).then(function (response) {
    return response.data;
  }).then(function (_ref) {
    var data = _ref.data;
    return data.map(function (taxonomy) {
      return _taxonomy.default.createFromResponse(taxonomy);
    });
  });
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/models/base-model.js":
/*!*****************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/models/base-model.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ "../node_modules/@babel/runtime/helpers/classCallCheck.js"));
var _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ "../node_modules/@babel/runtime/helpers/createClass.js"));
var BaseModel = exports["default"] = /*#__PURE__*/function () {
  function BaseModel() {
    (0, _classCallCheck2.default)(this, BaseModel);
  }
  return (0, _createClass2.default)(BaseModel, [{
    key: "clone",
    value:
    /**
     * Clone to object to avoid changing the reference.
     *
     * @return {BaseModel} cloned model
     */
    function clone() {
      var _this = this;
      var instance = new this.constructor();
      Object.keys(this).forEach(function (key) {
        instance[key] = _this[key];
      });
      return instance;
    }

    /**
     * Using init and not the default constructor because there is a problem to fill the instance
     * dynamically in the constructor.
     *
     * @param {*} data
     * @return {BaseModel} model
     */
  }, {
    key: "init",
    value: function init() {
      var _this2 = this;
      var data = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      Object.entries(data).forEach(function (_ref) {
        var _ref2 = (0, _slicedToArray2.default)(_ref, 2),
          key = _ref2[0],
          value = _ref2[1];
        _this2[key] = value;
      });
      return this;
    }
  }]);
}();

/***/ }),

/***/ "../app/modules/kit-library/assets/js/models/content-type.js":
/*!*******************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/models/content-type.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
var _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ "../node_modules/@babel/runtime/helpers/classCallCheck.js"));
var _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ "../node_modules/@babel/runtime/helpers/createClass.js"));
var _possibleConstructorReturn2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ "../node_modules/@babel/runtime/helpers/possibleConstructorReturn.js"));
var _getPrototypeOf2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ "../node_modules/@babel/runtime/helpers/getPrototypeOf.js"));
var _inherits2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/inherits */ "../node_modules/@babel/runtime/helpers/inherits.js"));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ "../node_modules/@babel/runtime/helpers/defineProperty.js"));
var _baseModel = _interopRequireDefault(__webpack_require__(/*! ./base-model */ "../app/modules/kit-library/assets/js/models/base-model.js"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var ContentType = exports["default"] = /*#__PURE__*/function (_BaseModel) {
  function ContentType() {
    var _this;
    (0, _classCallCheck2.default)(this, ContentType);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _callSuper(this, ContentType, [].concat(args));
    (0, _defineProperty2.default)(_this, "id", '');
    (0, _defineProperty2.default)(_this, "label", '');
    (0, _defineProperty2.default)(_this, "documentTypes", []);
    (0, _defineProperty2.default)(_this, "documents", []);
    (0, _defineProperty2.default)(_this, "order", 0);
    return _this;
  }
  (0, _inherits2.default)(ContentType, _BaseModel);
  return (0, _createClass2.default)(ContentType, null, [{
    key: "createFromResponse",
    value: function createFromResponse(documentType) {
      return new ContentType().init({
        id: documentType.id,
        label: documentType.label,
        documentTypes: documentType.doc_types,
        order: documentType.order,
        documents: []
      });
    }
  }]);
}(_baseModel.default);

/***/ }),

/***/ "../app/modules/kit-library/assets/js/models/document.js":
/*!***************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/models/document.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
var _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ "../node_modules/@babel/runtime/helpers/classCallCheck.js"));
var _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ "../node_modules/@babel/runtime/helpers/createClass.js"));
var _possibleConstructorReturn2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ "../node_modules/@babel/runtime/helpers/possibleConstructorReturn.js"));
var _getPrototypeOf2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ "../node_modules/@babel/runtime/helpers/getPrototypeOf.js"));
var _inherits2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/inherits */ "../node_modules/@babel/runtime/helpers/inherits.js"));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ "../node_modules/@babel/runtime/helpers/defineProperty.js"));
var _baseModel = _interopRequireDefault(__webpack_require__(/*! ./base-model */ "../app/modules/kit-library/assets/js/models/base-model.js"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var Document = exports["default"] = /*#__PURE__*/function (_BaseModel) {
  function Document() {
    var _this;
    (0, _classCallCheck2.default)(this, Document);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _callSuper(this, Document, [].concat(args));
    (0, _defineProperty2.default)(_this, "id", '');
    (0, _defineProperty2.default)(_this, "title", '');
    (0, _defineProperty2.default)(_this, "documentType", '');
    (0, _defineProperty2.default)(_this, "thumbnailUrl", '');
    (0, _defineProperty2.default)(_this, "previewUrl", '');
    return _this;
  }
  (0, _inherits2.default)(Document, _BaseModel);
  return (0, _createClass2.default)(Document, null, [{
    key: "createFromResponse",
    value:
    /**
     * Create a tag from server response
     *
     * @param {Document} document
     */
    function createFromResponse(document) {
      return new Document().init({
        id: document.id,
        title: document.title,
        documentType: document.doc_type,
        thumbnailUrl: document.thumbnail_url,
        previewUrl: document.preview_url
      });
    }
  }]);
}(_baseModel.default);

/***/ }),

/***/ "../app/modules/kit-library/assets/js/models/kit.js":
/*!**********************************************************!*\
  !*** ../app/modules/kit-library/assets/js/models/kit.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
var _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ "../node_modules/@babel/runtime/helpers/classCallCheck.js"));
var _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ "../node_modules/@babel/runtime/helpers/createClass.js"));
var _possibleConstructorReturn2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ "../node_modules/@babel/runtime/helpers/possibleConstructorReturn.js"));
var _getPrototypeOf2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ "../node_modules/@babel/runtime/helpers/getPrototypeOf.js"));
var _inherits2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/inherits */ "../node_modules/@babel/runtime/helpers/inherits.js"));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ "../node_modules/@babel/runtime/helpers/defineProperty.js"));
var _baseModel = _interopRequireDefault(__webpack_require__(/*! ./base-model */ "../app/modules/kit-library/assets/js/models/base-model.js"));
var _document = _interopRequireDefault(__webpack_require__(/*! ./document */ "../app/modules/kit-library/assets/js/models/document.js"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
/**
 * @typedef {import('./content-type')} ContentType
 */
var Kit = exports["default"] = /*#__PURE__*/function (_BaseModel) {
  function Kit() {
    var _this;
    (0, _classCallCheck2.default)(this, Kit);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _callSuper(this, Kit, [].concat(args));
    (0, _defineProperty2.default)(_this, "id", '');
    (0, _defineProperty2.default)(_this, "title", '');
    (0, _defineProperty2.default)(_this, "description", '');
    (0, _defineProperty2.default)(_this, "isFavorite", false);
    (0, _defineProperty2.default)(_this, "thumbnailUrl", null);
    (0, _defineProperty2.default)(_this, "previewUrl", '');
    (0, _defineProperty2.default)(_this, "accessLevel", 0);
    (0, _defineProperty2.default)(_this, "trendIndex", null);
    (0, _defineProperty2.default)(_this, "popularityIndex", null);
    (0, _defineProperty2.default)(_this, "featuredIndex", null);
    (0, _defineProperty2.default)(_this, "createdAt", null);
    (0, _defineProperty2.default)(_this, "updatedAt", null);
    (0, _defineProperty2.default)(_this, "keywords", []);
    (0, _defineProperty2.default)(_this, "taxonomies", []);
    (0, _defineProperty2.default)(_this, "documents", []);
    return _this;
  }
  (0, _inherits2.default)(Kit, _BaseModel);
  return (0, _createClass2.default)(Kit, [{
    key: "getDocumentsByTypes",
    value:
    /**
     * Get content types as param and group all the documents based on it.
     *
     * @param {ContentType[]} contentTypes
     * @return {ContentType[]} content types
     */
    function getDocumentsByTypes(contentTypes) {
      var _this2 = this;
      return contentTypes.map(function (contentType) {
        contentType = contentType.clone();
        contentType.documents = _this2.documents.filter(function (document) {
          return contentType.documentTypes.includes(document.documentType);
        });
        return contentType;
      });
    }
  }], [{
    key: "createFromResponse",
    value:
    /**
     * Create a kit from server response
     *
     * @param {Kit} kit
     */
    function createFromResponse(kit) {
      return new Kit().init({
        id: kit.id,
        title: kit.title,
        description: kit.description,
        isFavorite: kit.is_favorite,
        thumbnailUrl: kit.thumbnail_url,
        previewUrl: kit.preview_url,
        accessLevel: kit.access_level,
        accessTier: kit.access_tier,
        trendIndex: kit.trend_index,
        popularityIndex: kit.popularity_index,
        featuredIndex: kit.featured_index,
        // TODO: Remove when the API is stable (when date params always exists)
        createdAt: kit.created_at ? new Date(kit.created_at) : null,
        updatedAt: kit.updated_at ? new Date(kit.updated_at) : null,
        //
        keywords: kit.keywords,
        taxonomies: kit.taxonomies,
        documents: kit.documents ? kit.documents.map(function (document) {
          return _document.default.createFromResponse(document);
        }) : []
      });
    }
  }]);
}(_baseModel.default);

/***/ }),

/***/ "../app/modules/kit-library/assets/js/models/taxonomy-transformer.js":
/*!***************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/models/taxonomy-transformer.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.TierToKeyMap = exports.PromotionChipText = exports.OldPlanTexts = exports.NewPlanTexts = void 0;
exports.getTaxonomyFilterItems = getTaxonomyFilterItems;
exports.isKitInTaxonomy = isKitInTaxonomy;
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ "../node_modules/@babel/runtime/helpers/toConsumableArray.js"));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ "../node_modules/@babel/runtime/helpers/defineProperty.js"));
var _tiers = __webpack_require__(/*! elementor-utils/tiers */ "../assets/dev/js/utils/tiers.js");
var _i18n = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
var _taxonomy = _interopRequireWildcard(__webpack_require__(/*! ./taxonomy */ "../app/modules/kit-library/assets/js/models/taxonomy.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
var FREE = 'free',
  ESSENTIAL = 'essential',
  ADVANCED = 'advanced',
  PRO = 'pro',
  EXPERT = 'expert,',
  AGENCY = 'agency';
var OldPlanTexts = exports.OldPlanTexts = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, FREE, (0, _i18n.__)('Free', 'elementor')), PRO, (0, _i18n.__)('Pro', 'elementor')), ADVANCED, (0, _i18n.__)('Advanced', 'elementor')), EXPERT, (0, _i18n.__)('Expert', 'elementor')), AGENCY, (0, _i18n.__)('Agency', 'elementor'));
var NewPlanTexts = exports.NewPlanTexts = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, FREE, (0, _i18n.__)('Free', 'elementor')), ESSENTIAL, (0, _i18n.__)('Essential', 'elementor')), ADVANCED, (0, _i18n.__)('Advanced & Higher', 'elementor'));
var TaxonomyTransformMap = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, PRO, ESSENTIAL), EXPERT, ADVANCED), AGENCY, ADVANCED);
var TierToKeyMap = exports.TierToKeyMap = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, _tiers.TIERS.free, FREE), _tiers.TIERS.essential, ESSENTIAL), _tiers.TIERS['essential-oct2023'], ADVANCED), _tiers.TIERS.expert, ADVANCED), _tiers.TIERS.agency, ADVANCED);
var PromotionChipText = exports.PromotionChipText = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, FREE, (0, _i18n.__)('Free', 'elementor')), ESSENTIAL, (0, _i18n.__)('Essential', 'elementor')), ADVANCED, (0, _i18n.__)('Advanced', 'elementor'));
function getTaxonomyFilterItems(taxonomies) {
  taxonomies = taxonomies ? (0, _toConsumableArray2.default)(taxonomies) : [];
  var taxonomyFilterItems = taxonomies.reduce(function (map, taxonomy) {
    var formattedTaxonomy = _getFormattedTaxonomyItem(taxonomy),
      taxonomyType = _taxonomy.TaxonomyTypes.find(function (_ref) {
        var key = _ref.key;
        return key === formattedTaxonomy.type;
      });
    if (!taxonomyType) {
      return map;
    }
    if (!map[formattedTaxonomy.type]) {
      map[formattedTaxonomy.type] = _objectSpread({}, taxonomyType);
    }
    var data = map[formattedTaxonomy.type].data;
    if (!data.find(function (_ref2) {
      var text = _ref2.text;
      return text === formattedTaxonomy.text;
    })) {
      map[formattedTaxonomy.type].data.push(formattedTaxonomy);
    }
    return map;
  }, {});
  return _taxonomy.TaxonomyTypes.reduce(function (formattedTaxonomies, taxonomyItem) {
    var _taxonomyFilterItems$;
    if ((_taxonomyFilterItems$ = taxonomyFilterItems[taxonomyItem.key]) !== null && _taxonomyFilterItems$ !== void 0 && (_taxonomyFilterItems$ = _taxonomyFilterItems$.data) !== null && _taxonomyFilterItems$ !== void 0 && _taxonomyFilterItems$.length) {
      formattedTaxonomies.push(taxonomyFilterItems[taxonomyItem.key]);
    }
    return formattedTaxonomies;
  }, []);
}
function isKitInTaxonomy(kit, taxonomyType, taxonomies) {
  return _taxonomy.SUBSCRIPTION_PLAN === taxonomyType ? taxonomies.includes(TierToKeyMap[kit.accessTier]) : taxonomies.some(function (taxonomy) {
    return kit.taxonomies.includes(taxonomy);
  });
}
function _getFormattedTaxonomyItem(taxonomy) {
  switch (taxonomy.type) {
    case _taxonomy.SUBSCRIPTION_PLAN:
      return _getFormattedSubscriptionByPlanTaxonomy(taxonomy);
    default:
      return taxonomy;
  }
}
function _getTaxonomyIdByText(taxonomyText) {
  return Object.keys(OldPlanTexts).find(function (id) {
    return OldPlanTexts[id] === taxonomyText;
  });
}
function _getFormattedTaxonomyId(taxonomyId) {
  return TaxonomyTransformMap[taxonomyId] || taxonomyId;
}
function _getFormattedSubscriptionByPlanTaxonomy(taxonomy) {
  var transformedTaxonomy = new _taxonomy.default();
  transformedTaxonomy.id = _getFormattedTaxonomyId(_getTaxonomyIdByText(taxonomy.text));
  transformedTaxonomy.text = NewPlanTexts[transformedTaxonomy.id] || taxonomy.text;
  transformedTaxonomy.type = taxonomy.type;
  return transformedTaxonomy;
}

/***/ }),

/***/ "../app/modules/kit-library/assets/js/models/taxonomy.js":
/*!***************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/models/taxonomy.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = exports.TaxonomyTypes = exports.TAG = exports.SUBSCRIPTION_PLAN = exports.FEATURE = exports.CATEGORY = void 0;
var _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ "../node_modules/@babel/runtime/helpers/classCallCheck.js"));
var _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ "../node_modules/@babel/runtime/helpers/createClass.js"));
var _possibleConstructorReturn2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ "../node_modules/@babel/runtime/helpers/possibleConstructorReturn.js"));
var _getPrototypeOf2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ "../node_modules/@babel/runtime/helpers/getPrototypeOf.js"));
var _inherits2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/inherits */ "../node_modules/@babel/runtime/helpers/inherits.js"));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ "../node_modules/@babel/runtime/helpers/defineProperty.js"));
var _baseModel = _interopRequireDefault(__webpack_require__(/*! ./base-model */ "../app/modules/kit-library/assets/js/models/base-model.js"));
var _i18n = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var CATEGORY = exports.CATEGORY = 'categories',
  TAG = exports.TAG = 'tags',
  FEATURE = exports.FEATURE = 'features',
  SUBSCRIPTION_PLAN = exports.SUBSCRIPTION_PLAN = 'subscription_plans';
var TaxonomyTypes = exports.TaxonomyTypes = [{
  key: 'categories',
  label: (0, _i18n.__)('Categories', 'elementor'),
  isOpenByDefault: true,
  data: []
}, {
  key: 'tags',
  label: (0, _i18n.__)('Tags', 'elementor'),
  data: []
}, {
  key: 'features',
  label: (0, _i18n.__)('Features', 'elementor'),
  data: []
}, {
  key: SUBSCRIPTION_PLAN,
  label: (0, _i18n.__)('Plan', 'elementor'),
  data: []
}];
var Taxonomy = exports["default"] = /*#__PURE__*/function (_BaseModel) {
  function Taxonomy() {
    var _this;
    (0, _classCallCheck2.default)(this, Taxonomy);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _callSuper(this, Taxonomy, [].concat(args));
    (0, _defineProperty2.default)(_this, "text", '');
    (0, _defineProperty2.default)(_this, "type", 'tag');
    (0, _defineProperty2.default)(_this, "id", null);
    return _this;
  }
  (0, _inherits2.default)(Taxonomy, _BaseModel);
  return (0, _createClass2.default)(Taxonomy, null, [{
    key: "createFromResponse",
    value:
    /**
     * Create a tag from server response
     *
     * @param {Taxonomy} taxonomy
     */
    function createFromResponse(taxonomy) {
      return new Taxonomy().init({
        text: taxonomy.text,
        type: taxonomy.type,
        id: taxonomy.id || null
      });
    }
  }]);
}(_baseModel.default);

/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/cloud/cloud.js":
/*!*****************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/cloud/cloud.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = Cloud;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ "../node_modules/@babel/runtime/helpers/defineProperty.js"));
var _content = _interopRequireDefault(__webpack_require__(/*! ../../../../../../assets/js/layout/content */ "../app/assets/js/layout/content.js"));
var _errorScreen = _interopRequireDefault(__webpack_require__(/*! ../../components/error-screen */ "../app/modules/kit-library/assets/js/components/error-screen.js"));
var _indexHeader = _interopRequireDefault(__webpack_require__(/*! ../index/index-header */ "../app/modules/kit-library/assets/js/pages/index/index-header.js"));
var _indexSidebar = _interopRequireDefault(__webpack_require__(/*! ../index/index-sidebar */ "../app/modules/kit-library/assets/js/pages/index/index-sidebar.js"));
var _kitListCloud = _interopRequireDefault(__webpack_require__(/*! ../../components/kit-list-cloud */ "../app/modules/kit-library/assets/js/components/kit-list-cloud.js"));
var _layout = _interopRequireDefault(__webpack_require__(/*! ../../components/layout */ "../app/modules/kit-library/assets/js/components/layout/index.js"));
var _searchInput = _interopRequireDefault(__webpack_require__(/*! ../../components/search-input */ "../app/modules/kit-library/assets/js/components/search-input.js"));
var _useCloudKits2 = _interopRequireDefault(__webpack_require__(/*! ../../hooks/use-cloud-kits */ "../app/modules/kit-library/assets/js/hooks/use-cloud-kits.js"));
var _useCloudKitsEligibility = _interopRequireDefault(__webpack_require__(/*! elementor-app/hooks/use-cloud-kits-eligibility */ "../app/assets/js/hooks/use-cloud-kits-eligibility.js"));
var _useMenuItems = _interopRequireDefault(__webpack_require__(/*! ../../hooks/use-menu-items */ "../app/modules/kit-library/assets/js/hooks/use-menu-items.js"));
var _useConnectState2 = _interopRequireDefault(__webpack_require__(/*! ../../hooks/use-connect-state */ "../app/modules/kit-library/assets/js/hooks/use-connect-state.js"));
var _usePageTitle = _interopRequireDefault(__webpack_require__(/*! elementor-app/hooks/use-page-title */ "../app/assets/js/hooks/use-page-title.js"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
var _propTypes = _interopRequireDefault(__webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js"));
var _connectScreen = _interopRequireDefault(__webpack_require__(/*! ./connect-screen */ "../app/modules/kit-library/assets/js/pages/cloud/connect-screen.js"));
var _upgradeScreen = _interopRequireDefault(__webpack_require__(/*! ./upgrade-screen */ "../app/modules/kit-library/assets/js/pages/cloud/upgrade-screen.js"));
var _fullPageLoader = _interopRequireDefault(__webpack_require__(/*! ./full-page-loader */ "../app/modules/kit-library/assets/js/pages/cloud/full-page-loader.js"));
__webpack_require__(/*! ../index/index.scss */ "../app/modules/kit-library/assets/js/pages/index/index.scss");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function Cloud(_ref) {
  var _ref$path = _ref.path,
    path = _ref$path === void 0 ? '' : _ref$path,
    _ref$renderNoResultsC = _ref.renderNoResultsComponent,
    renderNoResultsComponent = _ref$renderNoResultsC === void 0 ? function (_ref2) {
      var defaultComponent = _ref2.defaultComponent;
      return defaultComponent;
    } : _ref$renderNoResultsC;
  (0, _usePageTitle.default)({
    title: __('Website Templates', 'elementor')
  });
  var _useConnectState = (0, _useConnectState2.default)(),
    isConnected = _useConnectState.isConnected,
    isConnecting = _useConnectState.isConnecting,
    setConnecting = _useConnectState.setConnecting,
    handleConnectSuccess = _useConnectState.handleConnectSuccess,
    handleConnectError = _useConnectState.handleConnectError;
  var _useCloudKits = (0, _useCloudKits2.default)(),
    data = _useCloudKits.data,
    isSuccess = _useCloudKits.isSuccess,
    isLoading = _useCloudKits.isLoading,
    isFetching = _useCloudKits.isFetching,
    isError = _useCloudKits.isError,
    queryParams = _useCloudKits.queryParams,
    setQueryParams = _useCloudKits.setQueryParams,
    clearQueryParams = _useCloudKits.clearQueryParams,
    forceRefetch = _useCloudKits.forceRefetch,
    isFilterActive = _useCloudKits.isFilterActive;
  var _useCloudKitsEligibil = (0, _useCloudKitsEligibility.default)({
      enabled: isConnected
    }),
    cloudKitsData = _useCloudKitsEligibil.data,
    isCheckingEligibility = _useCloudKitsEligibil.isLoading,
    refetchEligibility = _useCloudKitsEligibil.refetch;
  var isCloudKitsAvailable = (cloudKitsData === null || cloudKitsData === void 0 ? void 0 : cloudKitsData.is_eligible) || false;
  var menuItems = (0, _useMenuItems.default)(path);
  var eventTracking = function eventTracking(command, elementPosition) {
    var search = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
    var direction = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;
    var sortType = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : null;
    var action = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : null;
    var eventType = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : 'click';
    (0, _appsEventTracking.appsEventTrackingDispatch)(command, {
      page_source: 'cloud page',
      element_position: elementPosition,
      search_term: search,
      sort_direction: direction,
      sort_type: sortType,
      event_type: eventType,
      action: action
    });
  };
  var onConnectSuccess = function onConnectSuccess() {
    refetchEligibility();
    forceRefetch();
    handleConnectSuccess();
  };
  var onConnectError = function onConnectError() {
    handleConnectError();
  };
  var shouldShowLoading = isConnecting || isCheckingEligibility || isConnected && isLoading;
  (0, _react.useEffect)(function () {
    if (isConnecting && !isCheckingEligibility && !isLoading) {
      setConnecting(false);
    }
  }, [isConnecting, isCheckingEligibility, isLoading, setConnecting]);
  if (!isConnected) {
    return /*#__PURE__*/_react.default.createElement(_connectScreen.default, {
      onConnectSuccess: onConnectSuccess,
      onConnectError: onConnectError,
      menuItems: menuItems,
      forceRefetch: forceRefetch,
      isFetching: isFetching
    });
  }
  if (shouldShowLoading) {
    return /*#__PURE__*/_react.default.createElement(_fullPageLoader.default, {
      menuItems: menuItems,
      forceRefetch: forceRefetch,
      isFetching: isFetching
    });
  }
  if (!isCloudKitsAvailable && !shouldShowLoading) {
    return /*#__PURE__*/_react.default.createElement(_upgradeScreen.default, {
      menuItems: menuItems,
      forceRefetch: forceRefetch,
      isFetching: isFetching,
      cloudKitsData: cloudKitsData
    });
  }
  return /*#__PURE__*/_react.default.createElement(_layout.default, {
    sidebar: /*#__PURE__*/_react.default.createElement(_indexSidebar.default, {
      menuItems: menuItems
    }),
    header: /*#__PURE__*/_react.default.createElement(_indexHeader.default, {
      refetch: function refetch() {
        forceRefetch();
      },
      isFetching: isFetching
    })
  }, /*#__PURE__*/_react.default.createElement("div", {
    className: "e-kit-library__index-layout-container"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Grid, {
    container: true,
    className: "e-kit-library__index-layout-heading"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Grid, {
    item: true,
    className: "e-kit-library__index-layout-heading-search"
  }, /*#__PURE__*/_react.default.createElement(_searchInput.default
  // eslint-disable-next-line @wordpress/i18n-ellipsis
  , {
    placeholder: __('Search my Website Templates...', 'elementor'),
    value: queryParams.search,
    onChange: function onChange(value) {
      setQueryParams(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          search: value
        });
      });
      eventTracking('kit-library/kit-free-search', 'top_area_search', value, null, null, null, 'search');
    }
  }))), /*#__PURE__*/_react.default.createElement(_content.default, {
    className: "e-kit-library__index-layout-main"
  }, /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, isError && /*#__PURE__*/_react.default.createElement(_errorScreen.default, {
    title: __('Something went wrong.', 'elementor'),
    description: __('Nothing to worry about, use 🔄 on the top corner to try again. If the problem continues, head over to the Help Center.', 'elementor'),
    button: {
      text: __('Learn More', 'elementor'),
      url: 'https://go.elementor.com/app-kit-library-error/',
      target: '_blank'
    }
  }), isSuccess && 0 < data.length && /*#__PURE__*/_react.default.createElement(_kitListCloud.default, {
    data: data,
    source: path
  }), isSuccess && 0 === data.length && (queryParams.search ? /*#__PURE__*/_react.default.createElement(_errorScreen.default, {
    title: __('No Website Templates found for your search', 'elementor'),
    description: __('Try different keywords or ', 'elementor'),
    button: {
      text: __('Continue browsing.', 'elementor'),
      action: clearQueryParams
    }
  }) : renderNoResultsComponent({
    defaultComponent: /*#__PURE__*/_react.default.createElement(_errorScreen.default, {
      title: __('No Website Templates to show here yet', 'elementor'),
      description: __("Once you export a Website to the cloud, you'll find it here and be able to use it on all your sites.", 'elementor'),
      newLineButton: true,
      button: {
        text: __('Export this site', 'elementor'),
        url: elementorAppConfig.base_url + '#/export',
        target: '_blank',
        variant: 'contained',
        color: 'primary'
      }
    }),
    isFilterActive: isFilterActive
  }))))));
}
Cloud.propTypes = {
  path: _propTypes.default.string,
  renderNoResultsComponent: _propTypes.default.func
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/cloud/connect-screen.js":
/*!**************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/cloud/connect-screen.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = ConnectScreen;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _propTypes = _interopRequireDefault(__webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js"));
var _content = _interopRequireDefault(__webpack_require__(/*! ../../../../../../assets/js/layout/content */ "../app/assets/js/layout/content.js"));
var _indexHeader = _interopRequireDefault(__webpack_require__(/*! ../index/index-header */ "../app/modules/kit-library/assets/js/pages/index/index-header.js"));
var _indexSidebar = _interopRequireDefault(__webpack_require__(/*! ../index/index-sidebar */ "../app/modules/kit-library/assets/js/pages/index/index-sidebar.js"));
var _layout = _interopRequireDefault(__webpack_require__(/*! ../../components/layout */ "../app/modules/kit-library/assets/js/components/layout/index.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function ConnectScreen(_ref) {
  var _elementorAppConfig, _elementorAppConfig2, _elementorAppConfig3, _elementorAppConfig4;
  var onConnectSuccess = _ref.onConnectSuccess,
    onConnectError = _ref.onConnectError,
    menuItems = _ref.menuItems,
    forceRefetch = _ref.forceRefetch,
    isFetching = _ref.isFetching;
  var connectButtonRef = (0, _react.useRef)();
  (0, _react.useEffect)(function () {
    if (!connectButtonRef.current) {
      return;
    }
    jQuery(connectButtonRef.current).elementorConnect({
      popup: {
        width: 600,
        height: 700
      },
      success: function success(data) {
        elementorCommon.config.library_connect.is_connected = true;
        elementorCommon.config.library_connect.current_access_level = data.kits_access_level || data.access_level || 0;
        elementorCommon.config.library_connect.current_access_tier = data.access_tier;
        onConnectSuccess === null || onConnectSuccess === void 0 || onConnectSuccess();
      },
      error: function error() {
        elementorCommon.config.library_connect.is_connected = false;
        elementorCommon.config.library_connect.current_access_level = 0;
        elementorCommon.config.library_connect.current_access_tier = '';
        onConnectError === null || onConnectError === void 0 || onConnectError();
      }
    });
  }, [onConnectSuccess, onConnectError]);
  return /*#__PURE__*/_react.default.createElement(_layout.default, {
    sidebar: /*#__PURE__*/_react.default.createElement(_indexSidebar.default, {
      menuItems: menuItems
    }),
    header: /*#__PURE__*/_react.default.createElement(_indexHeader.default, {
      refetch: function refetch() {
        forceRefetch();
      },
      isFetching: isFetching
    })
  }, /*#__PURE__*/_react.default.createElement("div", {
    className: "e-kit-library__index-layout-container"
  }, /*#__PURE__*/_react.default.createElement(_content.default, {
    className: "e-kit-library__index-layout-main e-kit-library__connect-container"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Grid, {
    container: true,
    alignItems: "center",
    justify: "center",
    direction: "column",
    className: "e-kit-library__error-screen"
  }, /*#__PURE__*/_react.default.createElement("i", {
    className: "eicon-library-cloud-connect",
    "aria-hidden": "true"
  }), /*#__PURE__*/_react.default.createElement(_appUi.Heading, {
    tag: "h3",
    variant: "display-1",
    className: "e-kit-library__error-screen-title"
  }, (_elementorAppConfig = elementorAppConfig) === null || _elementorAppConfig === void 0 || (_elementorAppConfig = _elementorAppConfig['cloud-library']) === null || _elementorAppConfig === void 0 ? void 0 : _elementorAppConfig.library_connect_title_copy), /*#__PURE__*/_react.default.createElement(_appUi.Text, {
    variant: "xl",
    className: "e-kit-library__error-screen-description"
  }, (_elementorAppConfig2 = elementorAppConfig) === null || _elementorAppConfig2 === void 0 || (_elementorAppConfig2 = _elementorAppConfig2['cloud-library']) === null || _elementorAppConfig2 === void 0 || (_elementorAppConfig2 = _elementorAppConfig2.library_connect_sub_title_copy) === null || _elementorAppConfig2 === void 0 ? void 0 : _elementorAppConfig2.replace(/<br\s*\/?>/gi, '\n')), /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    elRef: connectButtonRef,
    text: (_elementorAppConfig3 = elementorAppConfig) === null || _elementorAppConfig3 === void 0 || (_elementorAppConfig3 = _elementorAppConfig3['cloud-library']) === null || _elementorAppConfig3 === void 0 || (_elementorAppConfig3 = _elementorAppConfig3.library_connect_button_copy) === null || _elementorAppConfig3 === void 0 ? void 0 : _elementorAppConfig3.replace(/&amp;/g, '&'),
    url: (_elementorAppConfig4 = elementorAppConfig) === null || _elementorAppConfig4 === void 0 || (_elementorAppConfig4 = _elementorAppConfig4['cloud-library']) === null || _elementorAppConfig4 === void 0 || (_elementorAppConfig4 = _elementorAppConfig4.library_connect_url) === null || _elementorAppConfig4 === void 0 ? void 0 : _elementorAppConfig4.replace(/&#038;/g, '&'),
    className: "e-kit-library__connect-button"
  })))));
}
ConnectScreen.propTypes = {
  onConnectSuccess: _propTypes.default.func,
  onConnectError: _propTypes.default.func,
  menuItems: _propTypes.default.array.isRequired,
  forceRefetch: _propTypes.default.func.isRequired,
  isFetching: _propTypes.default.bool.isRequired
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/cloud/full-page-loader.js":
/*!****************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/cloud/full-page-loader.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = FullPageLoader;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _propTypes = _interopRequireDefault(__webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js"));
var _content = _interopRequireDefault(__webpack_require__(/*! ../../../../../../assets/js/layout/content */ "../app/assets/js/layout/content.js"));
var _indexHeader = _interopRequireDefault(__webpack_require__(/*! ../index/index-header */ "../app/modules/kit-library/assets/js/pages/index/index-header.js"));
var _indexSidebar = _interopRequireDefault(__webpack_require__(/*! ../index/index-sidebar */ "../app/modules/kit-library/assets/js/pages/index/index-sidebar.js"));
var _layout = _interopRequireDefault(__webpack_require__(/*! ../../components/layout */ "../app/modules/kit-library/assets/js/components/layout/index.js"));
var _pageLoader = _interopRequireDefault(__webpack_require__(/*! ../../components/page-loader */ "../app/modules/kit-library/assets/js/components/page-loader.js"));
function FullPageLoader(_ref) {
  var menuItems = _ref.menuItems,
    forceRefetch = _ref.forceRefetch,
    isFetching = _ref.isFetching;
  return /*#__PURE__*/_react.default.createElement(_layout.default, {
    sidebar: /*#__PURE__*/_react.default.createElement(_indexSidebar.default, {
      menuItems: menuItems
    }),
    header: /*#__PURE__*/_react.default.createElement(_indexHeader.default, {
      refetch: function refetch() {
        forceRefetch();
      },
      isFetching: isFetching
    })
  }, /*#__PURE__*/_react.default.createElement("div", {
    className: "e-kit-library__index-layout-container"
  }, /*#__PURE__*/_react.default.createElement(_content.default, {
    className: "e-kit-library__index-layout-main"
  }, /*#__PURE__*/_react.default.createElement(_pageLoader.default, null))));
}
FullPageLoader.propTypes = {
  menuItems: _propTypes.default.array.isRequired,
  forceRefetch: _propTypes.default.func.isRequired,
  isFetching: _propTypes.default.bool.isRequired
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/cloud/upgrade-screen.js":
/*!**************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/cloud/upgrade-screen.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = UpgradeScreen;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _propTypes = _interopRequireDefault(__webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js"));
var _content = _interopRequireDefault(__webpack_require__(/*! ../../../../../../assets/js/layout/content */ "../app/assets/js/layout/content.js"));
var _indexHeader = _interopRequireDefault(__webpack_require__(/*! ../index/index-header */ "../app/modules/kit-library/assets/js/pages/index/index-header.js"));
var _indexSidebar = _interopRequireDefault(__webpack_require__(/*! ../index/index-sidebar */ "../app/modules/kit-library/assets/js/pages/index/index-sidebar.js"));
var _layout = _interopRequireDefault(__webpack_require__(/*! ../../components/layout */ "../app/modules/kit-library/assets/js/components/layout/index.js"));
function UpgradeScreen(_ref) {
  var menuItems = _ref.menuItems,
    forceRefetch = _ref.forceRefetch,
    isFetching = _ref.isFetching,
    cloudKitsData = _ref.cloudKitsData;
  var hasSubscription = '' !== (cloudKitsData === null || cloudKitsData === void 0 ? void 0 : cloudKitsData.subscription_id);
  var url = hasSubscription ? 'https://go.elementor.com/go-pro-cloud-website-templates-library-advanced/' : 'https://go.elementor.com/go-pro-cloud-website-templates-library/';
  return /*#__PURE__*/_react.default.createElement(_layout.default, {
    sidebar: /*#__PURE__*/_react.default.createElement(_indexSidebar.default, {
      menuItems: menuItems
    }),
    header: /*#__PURE__*/_react.default.createElement(_indexHeader.default, {
      refetch: function refetch() {
        forceRefetch();
      },
      isFetching: isFetching
    })
  }, /*#__PURE__*/_react.default.createElement("div", {
    className: "e-kit-library__index-layout-container"
  }, /*#__PURE__*/_react.default.createElement(_content.default, {
    className: "e-kit-library__index-layout-main e-kit-library__connect-container"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Grid, {
    container: true,
    alignItems: "center",
    justify: "center",
    direction: "column",
    className: "e-kit-library__error-screen"
  }, /*#__PURE__*/_react.default.createElement("i", {
    className: "eicon-library-subscription-upgrade",
    "aria-hidden": "true"
  }), /*#__PURE__*/_react.default.createElement(_appUi.Heading, {
    tag: "h3",
    variant: "display-1",
    className: "e-kit-library__error-screen-title"
  }, __('It\'s time to level up', 'elementor')), /*#__PURE__*/_react.default.createElement(_appUi.Text, {
    variant: "xl",
    className: "e-kit-library__error-screen-description"
  }, __('Upgrade to Elementor Pro to import your own website template and save templates that you can reuse on any of your connected websites.', 'elementor')), /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    text: __('Upgrade now', 'elementor'),
    url: url,
    target: "_blank",
    className: "e-kit-library__upgrade-button"
  })))));
}
UpgradeScreen.propTypes = {
  menuItems: _propTypes.default.array.isRequired,
  forceRefetch: _propTypes.default.func.isRequired,
  isFetching: _propTypes.default.bool.isRequired,
  cloudKitsData: _propTypes.default.object.isRequired
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/favorites/favorites.js":
/*!*************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/favorites/favorites.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = Favorites;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _index = _interopRequireDefault(__webpack_require__(/*! ../index/index */ "../app/modules/kit-library/assets/js/pages/index/index.js"));
var _errorScreen = _interopRequireDefault(__webpack_require__(/*! ../../components/error-screen */ "../app/modules/kit-library/assets/js/components/error-screen.js"));
var _router = __webpack_require__(/*! @reach/router */ "../node_modules/@reach/router/es/index.js");
var _propTypes = _interopRequireDefault(__webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js"));
function Favorites(props) {
  var navigate = (0, _router.useNavigate)();
  var indexNotResultsFavorites = /*#__PURE__*/_react.default.createElement(_errorScreen.default
  // eslint-disable-next-line @wordpress/i18n-ellipsis
  , {
    title: __('No favorites here yet...', 'elementor'),
    description: __('Use the heart icon to save Website Templates that inspire you. You\'ll be able to find them here.', 'elementor'),
    button: {
      text: __('Continue browsing.', 'elementor'),
      action: function action() {
        return navigate('/kit-library');
      }
    }
  });
  return /*#__PURE__*/_react.default.createElement(_index.default, {
    path: props.path,
    initialQueryParams: {
      favorite: true
    },
    renderNoResultsComponent: function renderNoResultsComponent(_ref) {
      var defaultComponent = _ref.defaultComponent,
        isFilterActive = _ref.isFilterActive;
      if (!isFilterActive) {
        return indexNotResultsFavorites;
      }
      return defaultComponent;
    }
  });
}
Favorites.propTypes = {
  path: _propTypes.default.string
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/index/index-header.js":
/*!************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/index/index-header.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = IndexHeader;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _header = _interopRequireDefault(__webpack_require__(/*! ../../components/layout/header */ "../app/modules/kit-library/assets/js/components/layout/header.js"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _router = __webpack_require__(/*! @reach/router */ "../node_modules/@reach/router/es/index.js");
var _popoverDialog = _interopRequireDefault(__webpack_require__(/*! elementor-app/ui/popover-dialog/popover-dialog */ "../app/assets/js/ui/popover-dialog/popover-dialog.js"));
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
__webpack_require__(/*! ./index-header.scss */ "../app/modules/kit-library/assets/js/pages/index/index-header.scss");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function IndexHeader(props) {
  var _elementorAppConfig$u, _elementorAppConfig$u2;
  var navigate = (0, _router.useNavigate)();
  var _useState = (0, _react.useState)(false),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isInfoModalOpen = _useState2[0],
    setIsInfoModalOpen = _useState2[1];
  var importRef = (0, _react.useRef)();
  var eventTracking = function eventTracking(command) {
    var element = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    var eventType = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'click';
    var modalType = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;
    (0, _appsEventTracking.appsEventTrackingDispatch)(command, {
      element: element,
      event_type: eventType,
      page_source: 'home page',
      element_position: 'app_header',
      modal_type: modalType
    });
  };
  var _onClose = function onClose(e) {
    var element = e.target.classList.contains('eps-modal__overlay') ? 'overlay' : 'x';
    eventTracking('kit-library/modal-close', element, null, 'info');
  };
  var shouldShowImportButton = elementorAppConfig.user.is_administrator || ((_elementorAppConfig$u = (_elementorAppConfig$u2 = elementorAppConfig.user.restrictions) === null || _elementorAppConfig$u2 === void 0 ? void 0 : _elementorAppConfig$u2.includes('json-upload')) !== null && _elementorAppConfig$u !== void 0 ? _elementorAppConfig$u : false);
  var buttons = (0, _react.useMemo)(function () {
    return [{
      id: 'info',
      text: __('Info', 'elementor'),
      hideText: true,
      icon: 'eicon-info-circle-o',
      onClick: function onClick() {
        eventTracking('kit-library/seek-more-info');
        setIsInfoModalOpen(true);
      }
    }, {
      id: 'refetch',
      text: __('Refetch', 'elementor'),
      hideText: true,
      icon: "eicon-sync ".concat(props.isFetching ? 'eicon-animation-spin' : ''),
      onClick: function onClick() {
        eventTracking('kit-library/refetch');
        props.refetch();
      }
    }, shouldShowImportButton && {
      id: 'import',
      text: __('Import', 'elementor'),
      hideText: true,
      icon: 'eicon-upload-circle-o',
      elRef: importRef,
      onClick: function onClick() {
        eventTracking('kit-library/kit-import');
        navigate('/import?referrer=kit-library');
      }
    }];
  }, [props.isFetching, props.refetch, shouldShowImportButton]);
  return /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/_react.default.createElement(_header.default, {
    buttons: buttons
  }), /*#__PURE__*/_react.default.createElement(_popoverDialog.default, {
    targetRef: importRef,
    wrapperClass: "e-kit-library__tooltip"
  }, __('Import Website Template', 'elementor')), /*#__PURE__*/_react.default.createElement(_appUi.ModalProvider, {
    title: __('Welcome to the Library', 'elementor'),
    show: isInfoModalOpen,
    setShow: setIsInfoModalOpen,
    onOpen: function onOpen() {
      return eventTracking('kit-library/modal-open', null, 'load', 'info');
    },
    onClose: function onClose(e) {
      return _onClose(e);
    }
  }, /*#__PURE__*/_react.default.createElement("div", {
    className: "e-kit-library-header-info-modal-container"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Heading, {
    tag: "h3",
    variant: "h3"
  }, __('What\'s a Website Template?', 'elementor')), /*#__PURE__*/_react.default.createElement(_appUi.Text, null, __('A Website Template is full, ready-made design that you can apply to your site. It includes all the pages, parts, settings and content that you\'d expect in a fully functional website.', 'elementor'))), /*#__PURE__*/_react.default.createElement("div", {
    className: "e-kit-library-header-info-modal-container"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Heading, {
    tag: "h3",
    variant: "h3"
  }, __('What\'s going on in the Website Templates Library?', 'elementor')), /*#__PURE__*/_react.default.createElement(_appUi.Text, null, __('Search & filter for website templates by category and tags, or browse through individual website templates to see what\'s inside.', 'elementor'), /*#__PURE__*/_react.default.createElement("br", null), __('Once you\'ve picked a winner, apply it to your site!', 'elementor'))), /*#__PURE__*/_react.default.createElement("div", null, /*#__PURE__*/_react.default.createElement(_appUi.Heading, {
    tag: "h3",
    variant: "h3"
  }, __('Happy browsing!', 'elementor')), /*#__PURE__*/_react.default.createElement(_appUi.Text, null, /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    url: "https://go.elementor.com/app-kit-library-how-to-use-kits/",
    target: "_blank",
    rel: "noreferrer",
    text: __('Learn more', 'elementor'),
    color: "link",
    onClick: function onClick() {
      eventTracking('kit-library/seek-more-info', 'text link', null, 'info');
    }
  }), ' ', __('about using templates', 'elementor')))));
}
IndexHeader.propTypes = {
  refetch: PropTypes.func.isRequired,
  isFetching: PropTypes.bool
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/index/index-sidebar.js":
/*!*************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/index/index-sidebar.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = IndexSidebar;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
function IndexSidebar(props) {
  var eventTracking = function eventTracking(command, category, source) {
    var eventType = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'click';
    return (0, _appsEventTracking.appsEventTrackingDispatch)(command, {
      category: category,
      source: source,
      element_location: 'app_sidebar',
      event_type: eventType
    });
  };
  return /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, props.menuItems.map(function (item) {
    return /*#__PURE__*/_react.default.createElement(_appUi.MenuItem, {
      key: item.label,
      text: item.label,
      className: "eps-menu-item__link ".concat(item.isActive ? 'eps-menu-item--active' : ''),
      icon: item.icon,
      url: item.url,
      onClick: function onClick() {
        return eventTracking(item.trackEventData.command, item.trackEventData.category, 'home page');
      }
    });
  }), props.tagsFilterSlot);
}
IndexSidebar.propTypes = {
  tagsFilterSlot: PropTypes.node,
  menuItems: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string,
    icon: PropTypes.string,
    isActive: PropTypes.bool,
    url: PropTypes.string
  }))
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/index/index.js":
/*!*****************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/index/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = Index;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ "../node_modules/@babel/runtime/helpers/toConsumableArray.js"));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ "../node_modules/@babel/runtime/helpers/defineProperty.js"));
var _content = _interopRequireDefault(__webpack_require__(/*! ../../../../../../assets/js/layout/content */ "../app/assets/js/layout/content.js"));
var _envatoPromotion = _interopRequireDefault(__webpack_require__(/*! ../../components/envato-promotion */ "../app/modules/kit-library/assets/js/components/envato-promotion.js"));
var _errorScreen = _interopRequireDefault(__webpack_require__(/*! ../../components/error-screen */ "../app/modules/kit-library/assets/js/components/error-screen.js"));
var _filterIndicationText = _interopRequireDefault(__webpack_require__(/*! ../../components/filter-indication-text */ "../app/modules/kit-library/assets/js/components/filter-indication-text.js"));
var _indexHeader = _interopRequireDefault(__webpack_require__(/*! ./index-header */ "../app/modules/kit-library/assets/js/pages/index/index-header.js"));
var _indexSidebar = _interopRequireDefault(__webpack_require__(/*! ./index-sidebar */ "../app/modules/kit-library/assets/js/pages/index/index-sidebar.js"));
var _kitList = _interopRequireDefault(__webpack_require__(/*! ../../components/kit-list */ "../app/modules/kit-library/assets/js/components/kit-list.js"));
var _layout = _interopRequireDefault(__webpack_require__(/*! ../../components/layout */ "../app/modules/kit-library/assets/js/components/layout/index.js"));
var _pageLoader = _interopRequireDefault(__webpack_require__(/*! ../../components/page-loader */ "../app/modules/kit-library/assets/js/components/page-loader.js"));
var _searchInput = _interopRequireDefault(__webpack_require__(/*! ../../components/search-input */ "../app/modules/kit-library/assets/js/components/search-input.js"));
var _sortSelect = _interopRequireDefault(__webpack_require__(/*! ../../components/sort-select */ "../app/modules/kit-library/assets/js/components/sort-select.js"));
var _taxonomiesFilter = _interopRequireDefault(__webpack_require__(/*! ../../components/taxonomies-filter */ "../app/modules/kit-library/assets/js/components/taxonomies-filter.js"));
var _useKits2 = _interopRequireWildcard(__webpack_require__(/*! ../../hooks/use-kits */ "../app/modules/kit-library/assets/js/hooks/use-kits.js"));
var _useMenuItems = _interopRequireDefault(__webpack_require__(/*! ../../hooks/use-menu-items */ "../app/modules/kit-library/assets/js/hooks/use-menu-items.js"));
var _usePageTitle = _interopRequireDefault(__webpack_require__(/*! elementor-app/hooks/use-page-title */ "../app/assets/js/hooks/use-page-title.js"));
var _useTaxonomies2 = _interopRequireDefault(__webpack_require__(/*! ../../hooks/use-taxonomies */ "../app/modules/kit-library/assets/js/hooks/use-taxonomies.js"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _lastFilterContext = __webpack_require__(/*! ../../context/last-filter-context */ "../app/modules/kit-library/assets/js/context/last-filter-context.js");
var _router = __webpack_require__(/*! @reach/router */ "../node_modules/@reach/router/es/index.js");
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
__webpack_require__(/*! ./index.scss */ "../app/modules/kit-library/assets/js/pages/index/index.scss");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Generate select and unselect taxonomy functions.
 *
 * @param {Function} setQueryParams
 * @return {((function(*, *): *)|(function(*=): *))[]} taxonomy functions
 */
function useTaxonomiesSelection(setQueryParams) {
  var selectTaxonomy = (0, _react.useCallback)(function (type, callback) {
    return setQueryParams(function (prev) {
      var taxonomies = _objectSpread({}, prev.taxonomies);
      taxonomies[type] = callback(prev.taxonomies[type]);
      return _objectSpread(_objectSpread({}, prev), {}, {
        taxonomies: taxonomies
      });
    });
  }, [setQueryParams]);
  var unselectTaxonomy = (0, _react.useCallback)(function (taxonomy) {
    return setQueryParams(function (prev) {
      var taxonomies = Object.entries(prev.taxonomies).reduce(function (current, _ref) {
        var _ref2 = (0, _slicedToArray2.default)(_ref, 2),
          key = _ref2[0],
          groupedTaxonomies = _ref2[1];
        return _objectSpread(_objectSpread({}, current), {}, (0, _defineProperty2.default)({}, key, groupedTaxonomies.filter(function (item) {
          return item !== taxonomy;
        })));
      }, {});
      return _objectSpread(_objectSpread({}, prev), {}, {
        taxonomies: taxonomies
      });
    });
  }, [setQueryParams]);
  return [selectTaxonomy, unselectTaxonomy];
}

/**
 * Update and read the query param from the url
 *
 * @param {*}             queryParams
 * @param {*}             setQueryParams
 * @param {Array<string>} exclude
 */
function useRouterQueryParams(queryParams, setQueryParams) {
  var exclude = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];
  var location = (0, _router.useLocation)(),
    _useLastFilterContext = (0, _lastFilterContext.useLastFilterContext)(),
    setLastFilter = _useLastFilterContext.setLastFilter;
  (0, _react.useEffect)(function () {
    var filteredQueryParams = Object.fromEntries(Object.entries(queryParams).filter(function (_ref3) {
      var _ref4 = (0, _slicedToArray2.default)(_ref3, 2),
        key = _ref4[0],
        item = _ref4[1];
      return !exclude.includes(key) && item;
    }));
    setLastFilter(filteredQueryParams);
    history.replaceState(null, '', decodeURI("#".concat(wp.url.addQueryArgs(location.pathname.split('?')[0] || '/', filteredQueryParams))));
  }, [queryParams]);
  (0, _react.useEffect)(function () {
    var routerQueryParams = Object.keys(_useKits2.defaultQueryParams).reduce(function (current, key) {
      // TODO: Replace with `wp.url.getQueryArgs` when WordPress 5.7 is the min version
      var queryArg = wp.url.getQueryArg(location.pathname, key);
      if (!queryArg) {
        return current;
      }
      return _objectSpread(_objectSpread({}, current), {}, (0, _defineProperty2.default)({}, key, queryArg));
    }, {});
    setQueryParams(function (prev) {
      return _objectSpread(_objectSpread(_objectSpread({}, prev), routerQueryParams), {}, {
        taxonomies: _objectSpread(_objectSpread({}, prev.taxonomies), routerQueryParams.taxonomies),
        ready: true
      });
    });
  }, []);
}
function Index(props) {
  (0, _usePageTitle.default)({
    title: __('Website Templates', 'elementor')
  });
  var menuItems = (0, _useMenuItems.default)(props.path);
  var _useKits = (0, _useKits2.default)(props.initialQueryParams),
    data = _useKits.data,
    isSuccess = _useKits.isSuccess,
    isLoading = _useKits.isLoading,
    isFetching = _useKits.isFetching,
    isError = _useKits.isError,
    queryParams = _useKits.queryParams,
    setQueryParams = _useKits.setQueryParams,
    clearQueryParams = _useKits.clearQueryParams,
    forceRefetch = _useKits.forceRefetch,
    isFilterActive = _useKits.isFilterActive;
  useRouterQueryParams(queryParams, setQueryParams, ['ready'].concat((0, _toConsumableArray2.default)(Object.keys(props.initialQueryParams))));
  var _useTaxonomies = (0, _useTaxonomies2.default)(),
    taxonomiesData = _useTaxonomies.data,
    forceRefetchTaxonomies = _useTaxonomies.forceRefetch,
    isFetchingTaxonomies = _useTaxonomies.isFetching;
  var _useTaxonomiesSelecti = useTaxonomiesSelection(setQueryParams),
    _useTaxonomiesSelecti2 = (0, _slicedToArray2.default)(_useTaxonomiesSelecti, 2),
    selectTaxonomy = _useTaxonomiesSelecti2[0],
    unselectTaxonomy = _useTaxonomiesSelecti2[1];
  var eventTracking = function eventTracking(command, elementPosition) {
    var search = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
    var direction = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;
    var sortType = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : null;
    var action = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : null;
    var eventType = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : 'click';
    (0, _appsEventTracking.appsEventTrackingDispatch)(command, {
      page_source: 'home page',
      element_position: elementPosition,
      search_term: search,
      sort_direction: direction,
      sort_type: sortType,
      event_type: eventType,
      action: action
    });
  };
  return /*#__PURE__*/_react.default.createElement(_layout.default, {
    sidebar: /*#__PURE__*/_react.default.createElement(_indexSidebar.default, {
      tagsFilterSlot: /*#__PURE__*/_react.default.createElement(_taxonomiesFilter.default, {
        selected: queryParams.taxonomies,
        onSelect: selectTaxonomy,
        taxonomies: taxonomiesData,
        category: props.path
      }),
      menuItems: menuItems
    }),
    header: /*#__PURE__*/_react.default.createElement(_indexHeader.default, {
      refetch: function refetch() {
        forceRefetch();
        forceRefetchTaxonomies();
      },
      isFetching: isFetching || isFetchingTaxonomies
    })
  }, /*#__PURE__*/_react.default.createElement("div", {
    className: "e-kit-library__index-layout-container"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Grid, {
    container: true,
    className: "e-kit-library__index-layout-heading"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Grid, {
    item: true,
    className: "e-kit-library__index-layout-heading-search"
  }, /*#__PURE__*/_react.default.createElement(_searchInput.default
  // eslint-disable-next-line @wordpress/i18n-ellipsis
  , {
    placeholder: __('Search all Website Templates...', 'elementor'),
    value: queryParams.search,
    onChange: function onChange(value) {
      setQueryParams(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          search: value
        });
      });
      eventTracking('kit-library/kit-free-search', 'top_area_search', value, null, null, null, 'search');
    }
  }), isFilterActive && /*#__PURE__*/_react.default.createElement(_filterIndicationText.default, {
    queryParams: queryParams,
    resultCount: data.length || 0,
    onClear: clearQueryParams,
    onRemoveTag: unselectTaxonomy
  })), /*#__PURE__*/_react.default.createElement(_appUi.Grid, {
    item: true,
    className: "e-kit-library__index-layout-heading-sort"
  }, /*#__PURE__*/_react.default.createElement(_sortSelect.default, {
    options: [{
      label: __('Featured', 'elementor'),
      value: 'featuredIndex',
      defaultOrder: 'asc',
      orderDisabled: true
    }, {
      label: __('New', 'elementor'),
      value: 'createdAt',
      defaultOrder: 'desc'
    }, {
      label: __('Popular', 'elementor'),
      value: 'popularityIndex',
      defaultOrder: 'desc'
    }, {
      label: __('Trending', 'elementor'),
      value: 'trendIndex',
      defaultOrder: 'desc'
    }],
    value: queryParams.order,
    onChange: function onChange(order) {
      return setQueryParams(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          order: order
        });
      });
    },
    onChangeSortDirection: function onChangeSortDirection(direction) {
      return eventTracking('kit-library/change-sort-direction', 'top_area_sort', null, direction);
    },
    onChangeSortValue: function onChangeSortValue(value) {
      return eventTracking('kit-library/change-sort-value', 'top_area_sort', null, null, value);
    },
    onSortSelectOpen: function onSortSelectOpen() {
      return eventTracking('kit-library/change-sort-type', 'top_area_sort', null, null, null, 'expand');
    }
  }))), /*#__PURE__*/_react.default.createElement(_content.default, {
    className: "e-kit-library__index-layout-main"
  }, /*#__PURE__*/_react.default.createElement(_react.default.Fragment, null, isLoading && /*#__PURE__*/_react.default.createElement(_pageLoader.default, null), isError && /*#__PURE__*/_react.default.createElement(_errorScreen.default, {
    title: __('Something went wrong.', 'elementor'),
    description: __('Nothing to worry about, use 🔄 on the top corner to try again. If the problem continues, head over to the Help Center.', 'elementor'),
    button: {
      text: __('Learn More', 'elementor'),
      url: 'https://go.elementor.com/app-kit-library-error/',
      target: '_blank'
    }
  }), isSuccess && 0 < data.length && queryParams.ready && /*#__PURE__*/_react.default.createElement(_kitList.default, {
    data: data,
    queryParams: queryParams,
    source: props.path
  }), isSuccess && 0 === data.length && queryParams.ready && props.renderNoResultsComponent({
    defaultComponent: /*#__PURE__*/_react.default.createElement(_errorScreen.default, {
      title: __('No results matched your search.', 'elementor'),
      description: __('Try different keywords or ', 'elementor'),
      button: {
        text: __('Continue browsing.', 'elementor'),
        action: clearQueryParams,
        category: props.path
      }
    }),
    isFilterActive: isFilterActive
  }), /*#__PURE__*/_react.default.createElement(_envatoPromotion.default, {
    category: props.path
  })))));
}
Index.propTypes = {
  path: PropTypes.string,
  initialQueryParams: PropTypes.object,
  renderNoResultsComponent: PropTypes.func
};
Index.defaultProps = {
  initialQueryParams: {},
  renderNoResultsComponent: function renderNoResultsComponent(_ref5) {
    var defaultComponent = _ref5.defaultComponent;
    return defaultComponent;
  }
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/overview/overview-content-group-item.js":
/*!******************************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/overview/overview-content-group-item.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = OverviewContentGroupItem;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _document = _interopRequireDefault(__webpack_require__(/*! ../../models/document */ "../app/modules/kit-library/assets/js/models/document.js"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
function OverviewContentGroupItem(props) {
  var eventTracking = function eventTracking(command) {
    var eventType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'click';
    (0, _appsEventTracking.appsEventTrackingDispatch)(command, {
      kit_name: props.kitTitle,
      document_type: props.groupData.id,
      document_name: "".concat(props.groupData.label, "-").concat(props.document.title),
      page_source: 'overview',
      element_position: 'content_overview',
      event_type: eventType
    });
  };
  return /*#__PURE__*/_react.default.createElement(_appUi.Card, null, /*#__PURE__*/_react.default.createElement(_appUi.CardHeader, null, /*#__PURE__*/_react.default.createElement(_appUi.Heading, {
    tag: "h3",
    title: props.document.title,
    variant: "h5",
    className: "eps-card__headline"
  }, props.document.title)), /*#__PURE__*/_react.default.createElement(_appUi.CardBody, null, /*#__PURE__*/_react.default.createElement(_appUi.CardImage, {
    alt: props.document.title,
    src: props.document.thumbnailUrl || ''
  }, props.document.previewUrl && /*#__PURE__*/_react.default.createElement(_appUi.CardOverlay, null, /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    className: "e-kit-library__kit-item-overlay-overview-button",
    text: __('View Demo', 'elementor'),
    icon: "eicon-preview-medium",
    url: "/kit-library/preview/".concat(props.kitId, "?document_id=").concat(props.document.id),
    onClick: function onClick() {
      return eventTracking('kit-library/view-demo-part');
    }
  })))));
}
OverviewContentGroupItem.propTypes = {
  document: PropTypes.instanceOf(_document.default).isRequired,
  kitId: PropTypes.string.isRequired,
  kitTitle: PropTypes.string.isRequired,
  groupData: PropTypes.shape({
    label: PropTypes.string,
    id: PropTypes.string
  }).isRequired
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/overview/overview-content-group.js":
/*!*************************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/overview/overview-content-group.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = OverviewContentGroup;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _contentType = _interopRequireDefault(__webpack_require__(/*! ../../models/content-type */ "../app/modules/kit-library/assets/js/models/content-type.js"));
var _overviewContentGroupItem = _interopRequireDefault(__webpack_require__(/*! ./overview-content-group-item */ "../app/modules/kit-library/assets/js/pages/overview/overview-content-group-item.js"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
function OverviewContentGroup(props) {
  var _props$contentType;
  if (((_props$contentType = props.contentType) === null || _props$contentType === void 0 || (_props$contentType = _props$contentType.documents) === null || _props$contentType === void 0 ? void 0 : _props$contentType.length) <= 0) {
    return '';
  }
  return /*#__PURE__*/_react.default.createElement("div", {
    className: "e-kit-library__content-overview-group-item"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Heading, {
    tag: "h3",
    variant: "h3",
    className: "e-kit-library__content-overview-group-title"
  }, props.contentType.label), /*#__PURE__*/_react.default.createElement(_appUi.CssGrid, {
    spacing: 24,
    colMinWidth: 250
  }, props.contentType.documents.map(function (document) {
    return /*#__PURE__*/_react.default.createElement(_overviewContentGroupItem.default, {
      key: document.id,
      document: document,
      kitId: props.kitId,
      kitTitle: props.kitTitle,
      groupData: props.contentType
    });
  })));
}
OverviewContentGroup.propTypes = {
  contentType: PropTypes.instanceOf(_contentType.default),
  kitId: PropTypes.string.isRequired,
  kitTitle: PropTypes.string.isRequired
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/overview/overview-sidebar.js":
/*!*******************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/overview/overview-sidebar.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = OverviewSidebar;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _collapse = _interopRequireDefault(__webpack_require__(/*! ../../components/collapse */ "../app/modules/kit-library/assets/js/components/collapse.js"));
var _contentType = _interopRequireDefault(__webpack_require__(/*! ../../models/content-type */ "../app/modules/kit-library/assets/js/models/content-type.js"));
var _favoritesActions = _interopRequireDefault(__webpack_require__(/*! ../../components/favorites-actions */ "../app/modules/kit-library/assets/js/components/favorites-actions.js"));
var _kit = _interopRequireDefault(__webpack_require__(/*! ../../models/kit */ "../app/modules/kit-library/assets/js/models/kit.js"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
__webpack_require__(/*! ./overview-sidebar.scss */ "../app/modules/kit-library/assets/js/pages/overview/overview-sidebar.scss");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function OverviewSidebar(props) {
  var _props$groupedKitCont;
  var _useState = (0, _react.useState)(true),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isInformationCollapseOpen = _useState2[0],
    setIsInformationCollapseOpen = _useState2[1];
  var eventTracking = function eventTracking(command) {
    var section = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    var kitName = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
    var tag = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;
    var isCollapsed = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : null;
    var eventType = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 'click';
    var action = isCollapsed && isCollapsed ? 'collapse' : 'expand';
    if ('boolean' === typeof isCollapsed) {
      command = "kit-library/".concat(action);
    }
    (0, _appsEventTracking.appsEventTrackingDispatch)(command, {
      page_source: 'overview',
      element_location: 'app_sidebar',
      kit_name: kitName,
      tag: tag,
      section: section,
      event_type: eventType
    });
  };
  return /*#__PURE__*/_react.default.createElement("div", {
    className: "e-kit-library__item-sidebar"
  }, /*#__PURE__*/_react.default.createElement("div", {
    className: "e-kit-library__item-sidebar-header"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Heading, {
    tag: "h1",
    variant: "h5",
    className: "e-kit-library__item-sidebar-header-title"
  }, props.model.title), /*#__PURE__*/_react.default.createElement(_favoritesActions.default, {
    isFavorite: props.model.isFavorite,
    id: props.model.id
  })), /*#__PURE__*/_react.default.createElement(_appUi.CardImage, {
    className: "e-kit-library__item-sidebar-thumbnail",
    alt: props.model.title,
    src: props.model.thumbnailUrl || ''
  }), /*#__PURE__*/_react.default.createElement(_appUi.Text, {
    className: "e-kit-library__item-sidebar-description"
  }, props.model.description || ''), ((_props$groupedKitCont = props.groupedKitContent) === null || _props$groupedKitCont === void 0 ? void 0 : _props$groupedKitCont.length) > 0 && props.model.documents.length > 0 && /*#__PURE__*/_react.default.createElement(_collapse.default, {
    isOpen: isInformationCollapseOpen,
    onChange: setIsInformationCollapseOpen,
    title: __('WHAT\'S INSIDE', 'elementor'),
    className: "e-kit-library__item-sidebar-collapse-info",
    onClick: function onClick(collapseState, title) {
      eventTracking(null, title, null, null, collapseState);
    }
  }, props.groupedKitContent.map(function (contentType) {
    if (contentType.documents <= 0) {
      return '';
    }
    return /*#__PURE__*/_react.default.createElement(_appUi.Text, {
      className: "e-kit-library__item-information-text",
      key: contentType.id
    }, contentType.documents.length, " ", contentType.label);
  })));
}
OverviewSidebar.propTypes = {
  model: PropTypes.instanceOf(_kit.default).isRequired,
  index: PropTypes.number,
  groupedKitContent: PropTypes.arrayOf(PropTypes.instanceOf(_contentType.default))
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/overview/overview.js":
/*!***********************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/overview/overview.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = Overview;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _content = _interopRequireDefault(__webpack_require__(/*! elementor-app/layout/content */ "../app/assets/js/layout/content.js"));
var _elementorLoading = _interopRequireDefault(__webpack_require__(/*! elementor-app/molecules/elementor-loading */ "../app/assets/js/molecules/elementor-loading.js"));
var _itemHeader = _interopRequireDefault(__webpack_require__(/*! ../../components/item-header */ "../app/modules/kit-library/assets/js/components/item-header.js"));
var _layout = _interopRequireDefault(__webpack_require__(/*! ../../components/layout */ "../app/modules/kit-library/assets/js/components/layout/index.js"));
var _overviewContentGroup = _interopRequireDefault(__webpack_require__(/*! ./overview-content-group */ "../app/modules/kit-library/assets/js/pages/overview/overview-content-group.js"));
var _overviewSidebar = _interopRequireDefault(__webpack_require__(/*! ./overview-sidebar */ "../app/modules/kit-library/assets/js/pages/overview/overview-sidebar.js"));
var _useKit2 = _interopRequireDefault(__webpack_require__(/*! ../../hooks/use-kit */ "../app/modules/kit-library/assets/js/hooks/use-kit.js"));
var _useKitDocumentByType2 = _interopRequireDefault(__webpack_require__(/*! ../../hooks/use-kit-document-by-type */ "../app/modules/kit-library/assets/js/hooks/use-kit-document-by-type.js"));
var _usePageTitle = _interopRequireDefault(__webpack_require__(/*! elementor-app/hooks/use-page-title */ "../app/assets/js/hooks/use-page-title.js"));
var _router = __webpack_require__(/*! @reach/router */ "../node_modules/@reach/router/es/index.js");
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
__webpack_require__(/*! ./overview.scss */ "../app/modules/kit-library/assets/js/pages/overview/overview.scss");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function useHeaderButtons(id, kitName) {
  var navigate = (0, _router.useNavigate)();
  return (0, _react.useMemo)(function () {
    return [{
      id: 'view-demo',
      text: __('View Demo', 'elementor'),
      hideText: false,
      variant: 'outlined',
      color: 'secondary',
      size: 'sm',
      onClick: function onClick() {
        (0, _appsEventTracking.appsEventTrackingDispatch)('kit-library/view-demo-page', {
          kit_name: kitName,
          page_source: 'overview',
          element_position: 'app_header',
          view_type_clicked: 'demo'
        });
        navigate("/kit-library/preview/".concat(id));
      },
      includeHeaderBtnClass: false
    }];
  }, [id]);
}
function Overview(props) {
  var _useKit = (0, _useKit2.default)(props.id),
    kit = _useKit.data,
    isError = _useKit.isError,
    isLoading = _useKit.isLoading;
  var _useKitDocumentByType = (0, _useKitDocumentByType2.default)(kit),
    documentsByType = _useKitDocumentByType.data;
  var headerButtons = useHeaderButtons(props.id, kit && kit.title);
  (0, _usePageTitle.default)({
    title: kit ? "".concat(__('Kit Library', 'elementor'), " | ").concat(kit.title) // eslint-disable-next-line @wordpress/i18n-ellipsis
    : __('Loading...', 'elementor')
  });
  if (isError) {
    // Will be caught by the App error boundary.
    throw new Error();
  }
  if (isLoading) {
    return /*#__PURE__*/_react.default.createElement(_elementorLoading.default, null);
  }
  return /*#__PURE__*/_react.default.createElement(_layout.default, {
    header: /*#__PURE__*/_react.default.createElement(_itemHeader.default, {
      model: kit,
      buttons: headerButtons,
      pageId: "overview"
    }),
    sidebar: /*#__PURE__*/_react.default.createElement(_overviewSidebar.default, {
      model: kit,
      groupedKitContent: documentsByType
    })
  }, documentsByType.length > 0 && /*#__PURE__*/_react.default.createElement(_content.default, null, documentsByType.map(function (contentType) {
    return /*#__PURE__*/_react.default.createElement(_overviewContentGroup.default, {
      key: contentType.id,
      contentType: contentType,
      kitId: props.id,
      kitTitle: kit.title
    });
  })));
}
Overview.propTypes = {
  id: PropTypes.string
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/preview/preview-iframe.js":
/*!****************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/preview/preview-iframe.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.PreviewIframe = PreviewIframe;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/* eslint-disable jsx-a11y/iframe-has-title */

function PreviewIframe(props) {
  var ref = (0, _react.useRef)();
  (0, _react.useEffect)(function () {
    if (!ref.current) {
      return;
    }
    var listener = function listener() {
      return props.onLoaded();
    };
    ref.current.addEventListener('load', listener);
    return function () {
      return ref.current && ref.current.removeEventListener('load', listener);
    };
  }, [ref.current, props.previewUrl]);
  return /*#__PURE__*/_react.default.createElement(_appUi.Grid, {
    container: true,
    justify: "center",
    className: "e-kit-library__preview-iframe-container"
  }, /*#__PURE__*/_react.default.createElement("iframe", {
    className: "e-kit-library__preview-iframe",
    src: props.previewUrl,
    style: props.style,
    ref: ref
  }));
}
PreviewIframe.propTypes = {
  previewUrl: PropTypes.string.isRequired,
  style: PropTypes.object,
  onLoaded: PropTypes.func
};
PreviewIframe.defaultProps = {
  style: {
    width: '100%',
    height: '100%'
  },
  onLoaded: function onLoaded() {}
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/preview/preview-responsive-controls.js":
/*!*****************************************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/preview/preview-responsive-controls.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = PreviewResponsiveControls;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _preview = __webpack_require__(/*! ./preview */ "../app/modules/kit-library/assets/js/pages/preview/preview.js");
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
__webpack_require__(/*! ./preview-responsive-controls.scss */ "../app/modules/kit-library/assets/js/pages/preview/preview-responsive-controls.scss");
function PreviewResponsiveControls(props) {
  return /*#__PURE__*/_react.default.createElement(_appUi.Grid, {
    container: true,
    alignItems: "center",
    justify: "center",
    className: "e-kit-library__preview-responsive-controls"
  }, _preview.breakpoints.map(function (_ref) {
    var label = _ref.label,
      value = _ref.value;
    var className = 'e-kit-library__preview-responsive-controls-item';
    if (props.active === value) {
      className += ' e-kit-library__preview-responsive-controls-item--active';
    }
    return /*#__PURE__*/_react.default.createElement(_appUi.Button, {
      key: value,
      text: label,
      hideText: true,
      className: className,
      icon: "eicon-device-".concat(value),
      onClick: function onClick() {
        return props.onChange(value);
      }
    });
  }));
}
PreviewResponsiveControls.propTypes = {
  active: PropTypes.string,
  onChange: PropTypes.func.isRequired
};
PreviewResponsiveControls.defaultProps = {
  active: 'desktop'
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/pages/preview/preview.js":
/*!*********************************************************************!*\
  !*** ../app/modules/kit-library/assets/js/pages/preview/preview.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];
/* provided dependency */ var PropTypes = __webpack_require__(/*! prop-types */ "../node_modules/prop-types/index.js");


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
var _typeof = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "../node_modules/@babel/runtime/helpers/typeof.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.breakpoints = void 0;
exports["default"] = Preview;
var _react = _interopRequireWildcard(__webpack_require__(/*! react */ "react"));
var _slicedToArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/slicedToArray */ "../node_modules/@babel/runtime/helpers/slicedToArray.js"));
var _elementorLoading = _interopRequireDefault(__webpack_require__(/*! elementor-app/molecules/elementor-loading */ "../app/assets/js/molecules/elementor-loading.js"));
var _itemHeader = _interopRequireDefault(__webpack_require__(/*! ../../components/item-header */ "../app/modules/kit-library/assets/js/components/item-header.js"));
var _layout = _interopRequireDefault(__webpack_require__(/*! ../../components/layout */ "../app/modules/kit-library/assets/js/components/layout/index.js"));
var _pageLoader = _interopRequireDefault(__webpack_require__(/*! ../../components/page-loader */ "../app/modules/kit-library/assets/js/components/page-loader.js"));
var _previewResponsiveControls = _interopRequireDefault(__webpack_require__(/*! ./preview-responsive-controls */ "../app/modules/kit-library/assets/js/pages/preview/preview-responsive-controls.js"));
var _useKit2 = _interopRequireDefault(__webpack_require__(/*! ../../hooks/use-kit */ "../app/modules/kit-library/assets/js/hooks/use-kit.js"));
var _usePageTitle = _interopRequireDefault(__webpack_require__(/*! elementor-app/hooks/use-page-title */ "../app/assets/js/hooks/use-page-title.js"));
var _previewIframe = __webpack_require__(/*! ./preview-iframe */ "../app/modules/kit-library/assets/js/pages/preview/preview-iframe.js");
var _router = __webpack_require__(/*! @reach/router */ "../node_modules/@reach/router/es/index.js");
var _appsEventTracking = __webpack_require__(/*! elementor-app/event-track/apps-event-tracking */ "../app/assets/js/event-track/apps-event-tracking.js");
__webpack_require__(/*! ./preview.scss */ "../app/modules/kit-library/assets/js/pages/preview/preview.scss");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != _typeof(e) && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var breakpoints = exports.breakpoints = [{
  value: 'desktop',
  label: __('Desktop', 'elementor'),
  style: {
    width: '100%',
    height: '100%'
  }
}, {
  value: 'tablet',
  label: __('Tablet', 'elementor'),
  style: {
    marginBlockStart: '30px',
    marginBlockEnd: '30px',
    width: '768px',
    height: '1024px'
  }
}, {
  value: 'mobile',
  label: __('Mobile', 'elementor'),
  style: {
    marginBlockStart: '30px',
    marginBlockEnd: '30px',
    width: '375px',
    height: '667px'
  }
}];
function useHeaderButtons(id, kitName) {
  var navigate = (0, _router.useNavigate)();
  var eventTracking = function eventTracking(command, viewTypeClicked) {
    var eventType = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'click';
    (0, _appsEventTracking.appsEventTrackingDispatch)(command, {
      kit_name: kitName,
      element_position: 'app_header',
      page_source: 'view demo',
      view_type_clicked: viewTypeClicked,
      event_type: eventType
    });
  };
  return (0, _react.useMemo)(function () {
    return [{
      id: 'overview',
      text: __('Overview', 'elementor'),
      hideText: false,
      variant: 'outlined',
      color: 'secondary',
      size: 'sm',
      onClick: function onClick() {
        eventTracking('kit-library/view-overview-page', 'overview');
        navigate("/kit-library/overview/".concat(id));
      },
      includeHeaderBtnClass: false
    }];
  }, [id]);
}

/**
 * Get preview url.
 *
 * @param {*} data
 * @return {null|string} Preview URL
 */
function usePreviewUrl(data) {
  var location = (0, _router.useLocation)();
  return (0, _react.useMemo)(function () {
    var _location$pathname$sp, _data$documents$find;
    if (!data) {
      return null;
    }
    var documentId = new URLSearchParams((_location$pathname$sp = location.pathname.split('?')) === null || _location$pathname$sp === void 0 ? void 0 : _location$pathname$sp[1]).get('document_id'),
      utm = '?utm_source=kit-library&utm_medium=wp-dash&utm_campaign=preview',
      previewUrl = data.previewUrl ? data.previewUrl + utm : data.previewUrl;
    if (!documentId) {
      return previewUrl;
    }
    var documentPreviewUrl = ((_data$documents$find = data.documents.find(function (item) {
      return item.id === parseInt(documentId);
    })) === null || _data$documents$find === void 0 ? void 0 : _data$documents$find.previewUrl) || previewUrl;
    return documentPreviewUrl ? documentPreviewUrl + utm : documentPreviewUrl;
  }, [location, data]);
}
function Preview(props) {
  var _useKit = (0, _useKit2.default)(props.id),
    data = _useKit.data,
    isError = _useKit.isError,
    isLoading = _useKit.isLoading;
  var _useState = (0, _react.useState)(true),
    _useState2 = (0, _slicedToArray2.default)(_useState, 2),
    isIframeLoading = _useState2[0],
    setIsIframeLoading = _useState2[1];
  var headersButtons = useHeaderButtons(props.id, data && data.title);
  var previewUrl = usePreviewUrl(data);
  var _useState3 = (0, _react.useState)('desktop'),
    _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
    activeDevice = _useState4[0],
    setActiveDevice = _useState4[1];
  var iframeStyle = (0, _react.useMemo)(function () {
    return breakpoints.find(function (_ref) {
      var value = _ref.value;
      return value === activeDevice;
    }).style;
  }, [activeDevice]);
  var eventTracking = function eventTracking(command, layout) {
    var elementPosition = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
    var eventType = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'click';
    (0, _appsEventTracking.appsEventTrackingDispatch)(command, {
      kit_name: data.title,
      page_source: 'view demo',
      layout: layout,
      element_position: elementPosition,
      event_type: eventType
    });
  };
  var _onChange = function onChange(device) {
    setActiveDevice(device);
    eventTracking('kit-library/responsive-controls', device, 'app_header');
  };
  (0, _usePageTitle.default)({
    title: data ? "".concat(__('Kit Library', 'elementor'), " | ").concat(data.title) // eslint-disable-next-line @wordpress/i18n-ellipsis
    : __('Loading...', 'elementor')
  });
  if (isError) {
    // Will be caught by the App error boundary.
    throw new Error();
  }
  if (isLoading) {
    return /*#__PURE__*/_react.default.createElement(_elementorLoading.default, null);
  }
  return /*#__PURE__*/_react.default.createElement(_layout.default, {
    header: /*#__PURE__*/_react.default.createElement(_itemHeader.default, {
      model: data,
      buttons: headersButtons,
      centerColumn: /*#__PURE__*/_react.default.createElement(_previewResponsiveControls.default, {
        active: activeDevice,
        onChange: function onChange(device) {
          return _onChange(device);
        },
        kitName: data.title
      }),
      pageId: "demo"
    })
  }, isIframeLoading && /*#__PURE__*/_react.default.createElement(_pageLoader.default, {
    className: "e-kit-library__preview-loader"
  }), previewUrl && /*#__PURE__*/_react.default.createElement(_previewIframe.PreviewIframe, {
    previewUrl: previewUrl,
    style: iframeStyle,
    onLoaded: function onLoaded() {
      return setIsIframeLoading(false);
    }
  }));
}
Preview.propTypes = {
  id: PropTypes.string
};

/***/ }),

/***/ "../app/modules/kit-library/assets/js/utils.js":
/*!*****************************************************!*\
  !*** ../app/modules/kit-library/assets/js/utils.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.pipe = pipe;
/**
 * A util function to transform data throw transform functions
 *
 * @param {Array<Function>} functions
 * @return {function(*=, ...[*]): *} function
 */
function pipe() {
  for (var _len = arguments.length, functions = new Array(_len), _key = 0; _key < _len; _key++) {
    functions[_key] = arguments[_key];
  }
  return function (value) {
    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
      args[_key2 - 1] = arguments[_key2];
    }
    return functions.reduce(function (currentValue, currentFunction) {
      return currentFunction.apply(void 0, [currentValue].concat(args));
    }, value);
  };
}

/***/ }),

/***/ "../app/modules/onboarding/assets/js/components/new-page-kit-list-item.js":
/*!********************************************************************************!*\
  !*** ../app/modules/onboarding/assets/js/components/new-page-kit-list-item.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
/* provided dependency */ var __ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n")["__"];


var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ "../node_modules/@babel/runtime/helpers/interopRequireDefault.js");
Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
var _react = _interopRequireDefault(__webpack_require__(/*! react */ "react"));
var _appUi = __webpack_require__(/*! @elementor/app-ui */ "@elementor/app-ui");
__webpack_require__(/*! ../../../../kit-library/assets/js/components/kit-list-item.scss */ "../app/modules/kit-library/assets/js/components/kit-list-item.scss");
var NewPageKitListItem = function NewPageKitListItem() {
  return /*#__PURE__*/_react.default.createElement(_appUi.Card, {
    className: "e-onboarding__kit-library-card e-kit-library__kit-item"
  }, /*#__PURE__*/_react.default.createElement(_appUi.CardHeader, null, /*#__PURE__*/_react.default.createElement(_appUi.Heading, {
    tag: "h3",
    title: __('Blank Canvas', 'elementor'),
    variant: "h5",
    className: "eps-card__headline"
  }, __('Blank Canvas', 'elementor'))), /*#__PURE__*/_react.default.createElement(_appUi.CardBody, null, /*#__PURE__*/_react.default.createElement(_appUi.CardImage, {
    alt: __('Blank Canvas', 'elementor'),
    src: elementorCommon.config.urls.assets + 'images/app/onboarding/Blank_Preview.jpg' || 0
  }, /*#__PURE__*/_react.default.createElement(_appUi.CardOverlay, null, /*#__PURE__*/_react.default.createElement(_appUi.Grid, {
    container: true,
    direction: "column",
    className: "e-kit-library__kit-item-overlay"
  }, /*#__PURE__*/_react.default.createElement(_appUi.Button, {
    className: "e-kit-library__kit-item-overlay-overview-button",
    text: __('Create New Elementor Page', 'elementor'),
    icon: "eicon-single-page",
    url: elementorAppConfig.onboarding.urls.createNewPage
  }))))));
};
var _default = exports["default"] = _react.default.memo(NewPageKitListItem);

/***/ }),

/***/ "../assets/dev/js/utils/tiers.js":
/*!***************************************!*\
  !*** ../assets/dev/js/utils/tiers.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.isTierAtLeast = exports.TIERS_PRIORITY = exports.TIERS = void 0;
var TIERS_PRIORITY = exports.TIERS_PRIORITY = Object.freeze(['free', 'essential', 'essential-oct2023', 'advanced', 'expert', 'agency']);

/**
 * @type {Readonly<{
 *     free: string;
 *     essential: string;
 *     'essential-oct2023': string;
 *     advanced: string;
 *     expert: string;
 *     agency: string;
 * }>}
 */
var TIERS = exports.TIERS = Object.freeze(TIERS_PRIORITY.reduce(function (acc, tier) {
  acc[tier] = tier;
  return acc;
}, {}));
var isTierAtLeast = exports.isTierAtLeast = function isTierAtLeast(currentTier, expectedTier) {
  var currentTierIndex = TIERS_PRIORITY.indexOf(currentTier);
  var expectedTierIndex = TIERS_PRIORITY.indexOf(expectedTier);
  if (-1 === currentTierIndex || -1 === expectedTierIndex) {
    return false;
  }
  return currentTierIndex >= expectedTierIndex;
};

/***/ })

}]);
//# sourceMappingURL=kit-library.f3e637c5acf9b98d8334.bundle.js.map