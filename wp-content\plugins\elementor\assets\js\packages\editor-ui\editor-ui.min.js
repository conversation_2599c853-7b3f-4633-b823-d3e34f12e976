!function(){"use strict";var e={d:function(t,n){for(var i in n)e.o(n,i)&&!e.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:n[i]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{EditableField:function(){return M},EllipsisWithTooltip:function(){return S},ITEM_HEIGHT:function(){return V},InfoAlert:function(){return A},InfoTipCard:function(){return P},IntroductionModal:function(){return T},MenuItemInfotip:function(){return D},MenuListItem:function(){return W},PopoverHeader:function(){return j},PopoverMenuList:function(){return H},PopoverScrollableContent:function(){return N},PopoverSearch:function(){return Y},StyledMenuList:function(){return $},ThemeProvider:function(){return _},WarningInfotip:function(){return F},useEditable:function(){return q}});var n=window.React,i=window.elementorV2.ui,s=window.wp.i18n,o=window.elementorV2.editorV1Adapters,r=window.elementorV2.icons,l=window.ReactDOM;function a(e,t,n){let i,s=n.initialDeps??[];function o(){var o,r,l,a;let c;n.key&&(null==(o=n.debug)?void 0:o.call(n))&&(c=Date.now());const h=e();if(h.length===s.length&&!h.some(((e,t)=>s[t]!==e)))return i;let u;if(s=h,n.key&&(null==(r=n.debug)?void 0:r.call(n))&&(u=Date.now()),i=t(...h),n.key&&(null==(l=n.debug)?void 0:l.call(n))){const e=Math.round(100*(Date.now()-c))/100,t=Math.round(100*(Date.now()-u))/100,i=t/16,s=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${s(t,5)} /${s(e,5)} ms`,`\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(0,Math.min(120-120*i,120))}deg 100% 31%);`,null==n?void 0:n.key)}return null==(a=null==n?void 0:n.onChange)||a.call(n,i),i}return o.updateDeps=e=>{s=e},o}function c(e,t){if(void 0===e)throw new Error("Unexpected undefined"+(t?`: ${t}`:""));return e}const h=(e,t,n)=>{let i;return function(...s){e.clearTimeout(i),i=e.setTimeout((()=>t.apply(this,s)),n)}},u=e=>{const{offsetWidth:t,offsetHeight:n}=e;return{width:t,height:n}},d=e=>e,m=e=>{const t=Math.max(e.startIndex-e.overscan,0),n=Math.min(e.endIndex+e.overscan,e.count-1),i=[];for(let e=t;e<=n;e++)i.push(e);return i},f=(e,t)=>{const n=e.scrollElement;if(!n)return;const i=e.targetWindow;if(!i)return;const s=e=>{const{width:n,height:i}=e;t({width:Math.round(n),height:Math.round(i)})};if(s(u(n)),!i.ResizeObserver)return()=>{};const o=new i.ResizeObserver((t=>{const i=()=>{const e=t[0];if(null==e?void 0:e.borderBoxSize){const t=e.borderBoxSize[0];if(t)return void s({width:t.inlineSize,height:t.blockSize})}s(u(n))};e.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(i):i()}));return o.observe(n,{box:"border-box"}),()=>{o.unobserve(n)}},p={passive:!0},g="undefined"==typeof window||"onscrollend"in window,v=(e,t)=>{const n=e.scrollElement;if(!n)return;const i=e.targetWindow;if(!i)return;let s=0;const o=e.options.useScrollendEvent&&g?()=>{}:h(i,(()=>{t(s,!1)}),e.options.isScrollingResetDelay),r=i=>()=>{const{horizontal:r,isRtl:l}=e.options;s=r?n.scrollLeft*(l?-1:1):n.scrollTop,o(),t(s,i)},l=r(!0),a=r(!1);a(),n.addEventListener("scroll",l,p);const c=e.options.useScrollendEvent&&g;return c&&n.addEventListener("scrollend",a,p),()=>{n.removeEventListener("scroll",l),c&&n.removeEventListener("scrollend",a)}},b=(e,t,n)=>{if(null==t?void 0:t.borderBoxSize){const e=t.borderBoxSize[0];if(e)return Math.round(e[n.options.horizontal?"inlineSize":"blockSize"])}return e[n.options.horizontal?"offsetWidth":"offsetHeight"]},x=(e,{adjustments:t=0,behavior:n},i)=>{var s,o;const r=e+t;null==(o=null==(s=i.scrollElement)?void 0:s.scrollTo)||o.call(s,{[i.options.horizontal?"left":"top"]:r,behavior:n})};class E{constructor(e){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let e=null;const t=()=>e||(this.targetWindow&&this.targetWindow.ResizeObserver?e=new this.targetWindow.ResizeObserver((e=>{e.forEach((e=>{const t=()=>{this._measureElement(e.target,e)};this.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(t):t()}))})):null);return{disconnect:()=>{var n;null==(n=t())||n.disconnect(),e=null},observe:e=>{var n;return null==(n=t())?void 0:n.observe(e,{box:"border-box"})},unobserve:e=>{var n;return null==(n=t())?void 0:n.unobserve(e)}}})(),this.range=null,this.setOptions=e=>{Object.entries(e).forEach((([t,n])=>{void 0===n&&delete e[t]})),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:d,rangeExtractor:m,onChange:()=>{},measureElement:b,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!1,useAnimationFrameWithResizeObserver:!1,...e}},this.notify=e=>{var t,n;null==(n=(t=this.options).onChange)||n.call(t,this,e)},this.maybeNotify=a((()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null])),(e=>{this.notify(e)}),{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach((e=>e())),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var e;const t=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==t){if(this.cleanup(),!t)return void this.maybeNotify();this.scrollElement=t,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=(null==(e=this.scrollElement)?void 0:e.window)??null,this.elementsCache.forEach((e=>{this.observer.observe(e)})),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,(e=>{this.scrollRect=e,this.maybeNotify()}))),this.unsubs.push(this.options.observeElementOffset(this,((e,t)=>{this.scrollAdjustments=0,this.scrollDirection=t?this.getScrollOffset()<e?"forward":"backward":null,this.scrollOffset=e,this.isScrolling=t,this.maybeNotify()})))}},this.getSize=()=>this.options.enabled?(this.scrollRect=this.scrollRect??this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0),this.getScrollOffset=()=>this.options.enabled?(this.scrollOffset=this.scrollOffset??("function"==typeof this.options.initialOffset?this.options.initialOffset():this.options.initialOffset),this.scrollOffset):(this.scrollOffset=null,0),this.getFurthestMeasurement=(e,t)=>{const n=new Map,i=new Map;for(let s=t-1;s>=0;s--){const t=e[s];if(n.has(t.lane))continue;const o=i.get(t.lane);if(null==o||t.end>o.end?i.set(t.lane,t):t.end<o.end&&n.set(t.lane,!0),n.size===this.options.lanes)break}return i.size===this.options.lanes?Array.from(i.values()).sort(((e,t)=>e.end===t.end?e.index-t.index:e.end-t.end))[0]:void 0},this.getMeasurementOptions=a((()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled]),((e,t,n,i,s)=>(this.pendingMeasuredCacheIndexes=[],{count:e,paddingStart:t,scrollMargin:n,getItemKey:i,enabled:s})),{key:!1}),this.getMeasurements=a((()=>[this.getMeasurementOptions(),this.itemSizeCache]),(({count:e,paddingStart:t,scrollMargin:n,getItemKey:i,enabled:s},o)=>{if(!s)return this.measurementsCache=[],this.itemSizeCache.clear(),[];0===this.measurementsCache.length&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach((e=>{this.itemSizeCache.set(e.key,e.size)})));const r=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];const l=this.measurementsCache.slice(0,r);for(let s=r;s<e;s++){const e=i(s),r=1===this.options.lanes?l[s-1]:this.getFurthestMeasurement(l,s),a=r?r.end+this.options.gap:t+n,c=o.get(e),h="number"==typeof c?c:this.options.estimateSize(s),u=a+h,d=r?r.lane:s%this.options.lanes;l[s]={index:s,start:a,size:h,end:u,key:e,lane:d}}return this.measurementsCache=l,l}),{key:!1,debug:()=>this.options.debug}),this.calculateRange=a((()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset(),this.options.lanes]),((e,t,n,i)=>this.range=e.length>0&&t>0?function({measurements:e,outerSize:t,scrollOffset:n,lanes:i}){const s=e.length-1;if(e.length<=i)return{startIndex:0,endIndex:s};let o=y(0,s,(t=>e[t].start),n),r=o;if(1===i)for(;r<s&&e[r].end<n+t;)r++;else if(i>1){const l=Array(i).fill(0);for(;r<s&&l.some((e=>e<n+t));){const t=e[r];l[t.lane]=t.end,r++}const a=Array(i).fill(n+t);for(;o>=0&&a.some((e=>e>=n));){const t=e[o];a[t.lane]=t.start,o--}o=Math.max(0,o-o%i),r=Math.min(s,r+(i-1-r%i))}return{startIndex:o,endIndex:r}}({measurements:e,outerSize:t,scrollOffset:n,lanes:i}):null),{key:!1,debug:()=>this.options.debug}),this.getVirtualIndexes=a((()=>{let e=null,t=null;const n=this.calculateRange();return n&&(e=n.startIndex,t=n.endIndex),this.maybeNotify.updateDeps([this.isScrolling,e,t]),[this.options.rangeExtractor,this.options.overscan,this.options.count,e,t]}),((e,t,n,i,s)=>null===i||null===s?[]:e({startIndex:i,endIndex:s,overscan:t,count:n})),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=e=>{const t=this.options.indexAttribute,n=e.getAttribute(t);return n?parseInt(n,10):(console.warn(`Missing attribute name '${t}={index}' on measured element.`),-1)},this._measureElement=(e,t)=>{const n=this.indexFromElement(e),i=this.measurementsCache[n];if(!i)return;const s=i.key,o=this.elementsCache.get(s);o!==e&&(o&&this.observer.unobserve(o),this.observer.observe(e),this.elementsCache.set(s,e)),e.isConnected&&this.resizeItem(n,this.options.measureElement(e,t,this))},this.resizeItem=(e,t)=>{const n=this.measurementsCache[e];if(!n)return;const i=t-(this.itemSizeCache.get(n.key)??n.size);0!==i&&((void 0!==this.shouldAdjustScrollPositionOnItemSizeChange?this.shouldAdjustScrollPositionOnItemSizeChange(n,i,this):n.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=i,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(n.index),this.itemSizeCache=new Map(this.itemSizeCache.set(n.key,t)),this.notify(!1))},this.measureElement=e=>{e?this._measureElement(e,void 0):this.elementsCache.forEach(((e,t)=>{e.isConnected||(this.observer.unobserve(e),this.elementsCache.delete(t))}))},this.getVirtualItems=a((()=>[this.getVirtualIndexes(),this.getMeasurements()]),((e,t)=>{const n=[];for(let i=0,s=e.length;i<s;i++){const s=t[e[i]];n.push(s)}return n}),{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=e=>{const t=this.getMeasurements();if(0!==t.length)return c(t[y(0,t.length-1,(e=>c(t[e]).start),e)])},this.getOffsetForAlignment=(e,t,n=0)=>{const i=this.getSize(),s=this.getScrollOffset();"auto"===t&&(t=e>=s+i?"end":"start"),"center"===t?e+=(n-i)/2:"end"===t&&(e-=i);const o=this.getTotalSize()-i;return Math.max(Math.min(o,e),0)},this.getOffsetForIndex=(e,t="auto")=>{e=Math.max(0,Math.min(e,this.options.count-1));const n=this.measurementsCache[e];if(!n)return;const i=this.getSize(),s=this.getScrollOffset();if("auto"===t)if(n.end>=s+i-this.options.scrollPaddingEnd)t="end";else{if(!(n.start<=s+this.options.scrollPaddingStart))return[s,t];t="start"}const o="end"===t?n.end+this.options.scrollPaddingEnd:n.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(o,t,n.size),t]},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{null!==this.scrollToIndexTimeoutId&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(e,{align:t="start",behavior:n}={})=>{this.cancelScrollToIndex(),"smooth"===n&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(e,t),{adjustments:void 0,behavior:n})},this.scrollToIndex=(e,{align:t="auto",behavior:n}={})=>{e=Math.max(0,Math.min(e,this.options.count-1)),this.cancelScrollToIndex(),"smooth"===n&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");const i=this.getOffsetForIndex(e,t);if(!i)return;const[s,o]=i;this._scrollToOffset(s,{adjustments:void 0,behavior:n}),"smooth"!==n&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout((()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(e))){const s=this.getOffsetForIndex(e,o);if(!s)return;const[r]=s;t=r,i=this.getScrollOffset(),Math.abs(t-i)<=1||this.scrollToIndex(e,{align:o,behavior:n})}else this.scrollToIndex(e,{align:o,behavior:n});var t,i})))},this.scrollBy=(e,{behavior:t}={})=>{this.cancelScrollToIndex(),"smooth"===t&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+e,{adjustments:void 0,behavior:t})},this.getTotalSize=()=>{var e;const t=this.getMeasurements();let n;if(0===t.length)n=this.options.paddingStart;else if(1===this.options.lanes)n=(null==(e=t[t.length-1])?void 0:e.end)??0;else{const e=Array(this.options.lanes).fill(null);let i=t.length-1;for(;i>=0&&e.some((e=>null===e));){const n=t[i];null===e[n.lane]&&(e[n.lane]=n.end),i--}n=Math.max(...e.filter((e=>null!==e)))}return Math.max(n-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(e,{adjustments:t,behavior:n})=>{this.options.scrollToFn(e,{behavior:n,adjustments:t},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(e)}}const y=(e,t,n,i)=>{for(;e<=t;){const s=(e+t)/2|0,o=n(s);if(o<i)e=s+1;else{if(!(o>i))return s;t=s-1}}return e>0?e-1:0},w="undefined"!=typeof document?n.useLayoutEffect:n.useEffect;var S=({maxWidth:e,title:t,as:s,...o})=>{const[r,l]=C();return l?n.createElement(i.Tooltip,{title:t,placement:"top"},n.createElement(I,{maxWidth:e,ref:r,as:s,...o},t)):n.createElement(I,{maxWidth:e,ref:r,as:s,...o},t)},I=n.forwardRef((({maxWidth:e,as:t=i.Box,...s},o)=>n.createElement(t,{ref:o,position:"relative",...s,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:e}}))),C=()=>{const[e,t]=(0,n.useState)(null),[i,s]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{const t=new ResizeObserver((([{target:e}])=>{s(e.scrollWidth>e.clientWidth)}));return e&&t.observe(e),()=>{t.disconnect()}}),[e]),[t,i]},M=(0,n.forwardRef)((({value:e,error:t,as:s="span",sx:o,...r},l)=>n.createElement(i.Tooltip,{title:t,open:!!t,placement:"top"},n.createElement(z,{ref:l,component:s,...r},e)))),z=(0,i.styled)(i.Box)`
	width: 100%;
	&:focus {
		outline: none;
	}
`,T=({open:e,handleClose:t,title:o,children:r})=>{const[l,a]=(0,n.useState)(!0);return n.createElement(i.Dialog,{open:e,onClose:t,maxWidth:"sm",TransitionComponent:O},o&&n.createElement(i.DialogHeader,{logo:!1},n.createElement(i.DialogTitle,null,o)),r,n.createElement(i.DialogActions,null,n.createElement(i.FormControlLabel,{sx:{marginRight:"auto"},control:n.createElement(i.Checkbox,{checked:!l,onChange:()=>a(!l)}),label:n.createElement(i.Typography,{variant:"body2"},(0,s.__)("Don't show this again","elementor"))}),n.createElement(i.Button,{size:"medium",variant:"contained",sx:{minWidth:"135px"},onClick:()=>t(l)},(0,s.__)("Got it","elementor"))))},O=n.forwardRef(((e,t)=>n.createElement(i.Fade,{ref:t,...e,timeout:{enter:1e3,exit:200}})));function R(){return window.elementor?.getPreferences?.("ui_theme")||"auto"}var k="unstable";function _({children:e}){const t=function(){const[e,t]=(0,n.useState)((()=>R()));return(0,n.useEffect)((()=>(0,o.__privateListenTo)((0,o.v1ReadyEvent)(),(()=>t(R())))),[]),(0,n.useEffect)((()=>(0,o.__privateListenTo)((0,o.commandEndEvent)("document/elements/settings"),(e=>{const n=e;n.args?.settings&&"ui_theme"in n.args.settings&&t(R())}))),[]),e}();return n.createElement(i.ThemeProvider,{colorScheme:t,palette:k},e)}var A=e=>n.createElement(i.Alert,{icon:n.createElement(r.InfoCircleFilledIcon,{fontSize:"small",color:"secondary"}),variant:"standard",color:"secondary",elevation:0,size:"small",...e}),W=({children:e,...t})=>n.createElement(i.MenuItem,{dense:!0,...t,sx:{...t.sx??{}}},n.createElement(i.MenuItemText,{primary:e,primaryTypographyProps:{variant:"caption"}})),D=(0,n.forwardRef)((({showInfoTip:e=!1,children:t,content:s},o)=>e?n.createElement(i.Infotip,{ref:o,placement:"right",arrow:!1,content:n.createElement(A,{sx:{maxWidth:325}},s)},n.createElement("div",{style:{pointerEvents:"initial",width:"100%"},onClick:e=>e.stopPropagation()},t)):n.createElement(n.Fragment,null,t))),P=({content:e,svgIcon:t,learnMoreButton:s,ctaButton:o})=>n.createElement(i.Card,{elevation:0,sx:{width:320}},n.createElement(i.CardContent,{sx:{pb:0}},n.createElement(i.Box,{display:"flex",alignItems:"start"},n.createElement(i.SvgIcon,{fontSize:"tiny",sx:{mr:.5}},t),n.createElement(i.Typography,{variant:"body2"},e))),(o||s)&&n.createElement(i.CardActions,null,s&&n.createElement(i.Button,{size:"small",color:"warning",href:s.href,target:"_blank"},s.label),o&&n.createElement(i.Button,{size:"small",color:"warning",variant:"contained",onClick:o.onClick},o.label))),F=(0,n.forwardRef)((({children:e,open:t,title:s,text:o,placement:r,width:l,offset:a},c)=>n.createElement(i.Infotip,{ref:c,open:t,placement:r,PopperProps:{sx:{width:l||"initial",".MuiTooltip-tooltip":{marginLeft:0,marginRight:0}},modifiers:a?[{name:"offset",options:{offset:a}}]:[]},arrow:!1,content:n.createElement(i.Alert,{color:"error",severity:"warning",variant:"standard",size:"small"},s?n.createElement(i.AlertTitle,null,s):null,o)},e))),B=(0,o.isExperimentActive)("e_v_3_30"),j=({title:e,onClose:t,icon:s,actions:o})=>{const r=B?{pl:2,pr:1,py:1.5,maxHeight:36}:{pl:1.5,pr:.5,py:1.5};return n.createElement(i.Stack,{direction:"row",alignItems:"center",...r,sx:{columnGap:.5}},s,n.createElement(i.Typography,{variant:"subtitle2",sx:B?{fontSize:"12px",mt:.25}:void 0},e),n.createElement(i.Stack,{direction:"row",sx:{ml:"auto"}},o,n.createElement(i.CloseButton,{slotProps:{icon:{fontSize:"tiny"}},sx:{ml:"auto"},onClick:t})))},L=(0,o.isExperimentActive)("e_v_3_30"),V=32,H=({items:e,onSelect:t,onClose:s,selectedValue:o,itemStyle:r,onChange:a,"data-testid":c,menuItemContentTemplate:h,noResultsComponent:u,menuListTemplate:d})=>{const m=(0,n.useRef)(null),p=(({containerRef:e})=>{const[t,i]=(0,n.useState)(0);return(0,n.useEffect)((()=>{const t=e.current;if(!t)return;const n=()=>{i(t.scrollTop)};return t.addEventListener("scroll",n),()=>t.removeEventListener("scroll",n)}),[e]),t})({containerRef:m}),g=d||$,b=(0,n.useMemo)((()=>e.reduce(((e,t,n)=>("category"===t.type&&e.push(n),e)),[])),[e]),y=(S={count:e.length,getScrollElement:()=>m.current,estimateSize:()=>V,overscan:6,rangeExtractor:e=>{const t=[];for(let n=e.startIndex;n<=e.endIndex;n++)t.push(n);const n=((e,t)=>[...e.filter((e=>e<t.startIndex)).slice(-2),...e.filter((e=>e>t.endIndex)).slice(0,2)])(b,e);return n.forEach((e=>{t.includes(e)||t.push(e)})),t.sort(((e,t)=>e-t))},onChange:a},function(e){const t=n.useReducer((()=>({})),{})[1],i={...e,onChange:(n,i)=>{var s;i?(0,l.flushSync)(t):t(),null==(s=e.onChange)||s.call(e,n,i)}},[s]=n.useState((()=>new E(i)));return s.setOptions(i),w((()=>s._didMount()),[]),w((()=>s._willUpdate())),s}({observeElementRect:f,observeElementOffset:v,scrollToFn:x,...S}));var S;return(({selectedValue:e,items:t,virtualizer:i})=>{(0,n.useEffect)((()=>{if(!e||0===t.length)return;const n=t.findIndex((t=>t.value===e));-1!==n&&i.scrollToIndex(n,{align:"center"})}),[e,t,i])})({selectedValue:o,items:e,virtualizer:y}),n.createElement(i.Box,{ref:m},0===e.length&&u?u:n.createElement(g,{role:"listbox",style:{height:`${y.getTotalSize()}px`},"data-testid":c},y.getVirtualItems().map((l=>{const a=e[l.index],c=l.index===e.length-1,u="category"===e[0]?.type?1===l.index:0===l.index,d=o===a.value,m=o?-1:0;if(!a)return null;if("category"===a.type){const e=l.start+8<=p;return n.createElement(i.MenuSubheader,{key:l.key,style:e?{}:(f=l.start,{position:"absolute",transform:`translateY(${f+8}px)`}),sx:L?{fontWeight:"400",color:"text.tertiary"}:void 0},a.label||a.value)}var f;return n.createElement("li",{key:l.key,role:"option","aria-selected":d,onClick:e=>{e.target.closest("button")||(t(a.value),s())},onKeyDown:e=>{"Enter"===e.key&&(t(a.value),s()),"ArrowDown"===e.key&&c&&(e.preventDefault(),e.stopPropagation()),"ArrowUp"===e.key&&u&&(e.preventDefault(),e.stopPropagation())},tabIndex:d?0:m,style:{transform:`translateY(${l.start+8}px)`,...r?r(a):{}}},h?h(a):a.label||a.value)}))))},$=(0,i.styled)(i.MenuList)((({theme:e})=>({"& > li":{height:V,width:"100%",display:"flex",alignItems:"center"},'& > [role="option"]':{...e.typography.caption,lineHeight:"inherit",padding:e.spacing(.75,2,.75,4),"&:hover, &:focus":{backgroundColor:e.palette.action.hover},'&[aria-selected="true"]':{backgroundColor:e.palette.action.selected},cursor:"pointer",textOverflow:"ellipsis",position:"absolute",top:0,left:0},width:"100%",position:"relative"}))),K=(0,o.isExperimentActive)("e_v_3_30"),N=n.forwardRef((({children:e,height:t=260,width:s=220},o)=>n.createElement(i.Box,{ref:o,sx:{overflowY:"auto",height:t,width:(K?s-32:220)+"px",maxWidth:496}},e))),U=(0,o.isExperimentActive)("e_v_3_30"),G="tiny",Y=({value:e,onSearch:t,placeholder:o})=>{const l=(0,n.useRef)(null),a=U?{px:2,pb:1.5}:{px:1.5,pb:1};return n.createElement(i.Box,{...a},n.createElement(i.TextField,{autoFocus:!0,fullWidth:!0,size:G,value:e,inputRef:l,onChange:e=>{t(e.target.value)},placeholder:o,InputProps:{startAdornment:n.createElement(i.InputAdornment,{position:"start"},n.createElement(r.SearchIcon,{fontSize:G})),endAdornment:e&&n.createElement(i.IconButton,{size:G,onClick:()=>{t(""),l.current?.focus()},"aria-label":(0,s.__)("Clear","elementor")},n.createElement(r.XIcon,{color:"action",fontSize:G}))}}))},q=({value:e,onSubmit:t,validation:i,onClick:s,onError:o})=>{const[r,l]=(0,n.useState)(!1),[a,c]=(0,n.useState)(null),h=X(r),u=t=>t!==e,d=()=>{h.current?.blur(),c(null),o?.(null),l(!1)},m={onClick:e=>{r&&e.stopPropagation(),s?.(e)},onKeyDown:e=>(e.stopPropagation(),["Escape"].includes(e.key)?d():["Enter"].includes(e.key)?(e.preventDefault(),(e=>{if(u(e)&&!a)try{t(e)}finally{d()}})(e.target.innerText)):void 0),onInput:e=>{const{innerText:t}=e.target;if(i){const e=u(t)?i(t):null;c(e),o?.(e)}},onBlur:d},f={value:e,role:"textbox",contentEditable:r,...r&&{suppressContentEditableWarning:!0}};return{ref:h,isEditing:r,openEditMode:()=>{l(!0)},closeEditMode:d,value:e,error:a,getProps:()=>({...m,...f})}},X=e=>{const t=(0,n.useRef)(null);return(0,n.useEffect)((()=>{e&&J(t.current)}),[e]),t},J=e=>{const t=getSelection();if(!t||!e)return;const n=document.createRange();n.selectNodeContents(e),t.removeAllRanges(),t.addRange(n)};(window.elementorV2=window.elementorV2||{}).editorUi=t}(),window.elementorV2.editorUi?.init?.();