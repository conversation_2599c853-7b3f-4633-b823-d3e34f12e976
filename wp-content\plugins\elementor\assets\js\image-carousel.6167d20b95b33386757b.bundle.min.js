/*! elementor - v3.30.0 - 01-07-2025 */
"use strict";(self.webpackChunkelementorFrontend=self.webpackChunkelementorFrontend||[]).push([[177],{4315:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;class ImageCarousel extends elementorModules.frontend.handlers.CarouselBase{getDefaultSettings(){const e=super.getDefaultSettings();return e.selectors.carousel=".elementor-image-carousel-wrapper",e}}t.default=ImageCarousel}}]);