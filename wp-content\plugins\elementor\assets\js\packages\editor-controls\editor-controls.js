/*! For license information please see editor-controls.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React},"@elementor/editor-current-user":function(e){e.exports=window.elementorV2.editorCurrentUser},"@elementor/editor-elements":function(e){e.exports=window.elementorV2.editorElements},"@elementor/editor-props":function(e){e.exports=window.elementorV2.editorProps},"@elementor/editor-responsive":function(e){e.exports=window.elementorV2.editorResponsive},"@elementor/editor-ui":function(e){e.exports=window.elementorV2.editorUi},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/env":function(e){e.exports=window.elementorV2.env},"@elementor/http-client":function(e){e.exports=window.elementorV2.httpClient},"@elementor/icons":function(e){e.exports=window.elementorV2.icons},"@elementor/locations":function(e){e.exports=window.elementorV2.locations},"@elementor/query":function(e){e.exports=window.elementorV2.query},"@elementor/session":function(e){e.exports=window.elementorV2.session},"@elementor/ui":function(e){e.exports=window.elementorV2.ui},"@elementor/utils":function(e){e.exports=window.elementorV2.utils},"@elementor/wp-media":function(e){e.exports=window.elementorV2.wpMedia},"@wordpress/i18n":function(e){e.exports=window.wp.i18n}},t={};function n(l){var r=t[l];if(void 0!==r)return r.exports;var a=t[l]={exports:{}};return e[l](a,a.exports,n),a.exports}n.d=function(e,t){for(var l in t)n.o(t,l)&&!n.o(e,l)&&Object.defineProperty(e,l,{enumerable:!0,get:t[l]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var l={};!function(){n.r(l),n.d(l,{AspectRatioControl:function(){return jt},BackgroundControl:function(){return Mn},BoxShadowRepeaterControl:function(){return We},ColorControl:function(){return he},ControlActionsProvider:function(){return N},ControlAdornments:function(){return Ie},ControlAdornmentsProvider:function(){return Se},ControlFormLabel:function(){return U},ControlReplacementsProvider:function(){return G},ControlToggleButtonGroup:function(){return st},EqualUnequalSizesControl:function(){return gt},FilterRepeaterControl:function(){return et},FontFamilyControl:function(){return wt},FontFamilySelector:function(){return _t},GapControl:function(){return At},ImageControl:function(){return Q},KeyValueControl:function(){return Qn},LinkControl:function(){return Gt},LinkedDimensionsControl:function(){return ht},NumberControl:function(){return vt},PopoverContent:function(){return Ce},PositionControl:function(){return Jn},PropKeyProvider:function(){return S},PropProvider:function(){return C},RepeatableControl:function(){return jn},SelectControl:function(){return Y},SizeControl:function(){return pe},StrokeControl:function(){return xe},SvgMediaControl:function(){return cn},SwitchControl:function(){return $t},TextAreaControl:function(){return te},TextControl:function(){return ee},ToggleControl:function(){return dt},TransformRepeaterControl:function(){return sl},UrlControl:function(){return St},createControlReplacementsRegistry:function(){return L},injectIntoRepeaterItemIcon:function(){return Pe},injectIntoRepeaterItemLabel:function(){return $e},useBoundProp:function(){return P},useControlActions:function(){return K},useSyncExternalState:function(){return ue}});var e=n("react"),t=n("@elementor/editor-props"),r=n("@elementor/ui"),a=n("@wordpress/i18n"),o=n("@elementor/utils"),i=n("@elementor/query"),c=n("@elementor/http-client"),s=n("@elementor/icons"),u=n("@elementor/wp-media"),m=n("@elementor/editor-ui"),d=n("@elementor/editor-responsive"),p=n("@elementor/editor-v1-adapters"),E=n("@elementor/locations"),v=n("@elementor/editor-elements"),b=n("@elementor/session"),g=n("@elementor/editor-current-user"),f=n("@elementor/env"),h=(0,o.createError)({code:"missing_prop_provider_prop_type",message:"Prop type is missing"}),y=(0,o.createError)({code:"unsupported_prop_provider_prop_type",message:"Parent prop type is not supported"}),x=(0,o.createError)({code:"hook_outside_provider",message:"Hook used outside of provider"}),_=(0,e.createContext)(null),C=({children:t,value:n,setValue:l,propType:r,placeholder:a,isDisabled:o})=>e.createElement(_.Provider,{value:{value:n,propType:r,setValue:l,placeholder:a,isDisabled:o}},t),T=()=>{const t=(0,e.useContext)(_);if(!t)throw new x({context:{hook:"usePropContext",provider:"PropProvider"}});return t},w=(0,e.createContext)(null),S=({children:t,bind:n})=>{const{propType:l}=T();if(!l)throw new h({context:{bind:n}});if("array"===l.kind)return e.createElement(I,{bind:n},t);if("object"===l.kind)return e.createElement(z,{bind:n},t);throw new y({context:{propType:l}})},z=({children:t,bind:n})=>{const l=T(),{path:r}=(0,e.useContext)(w)??{},a=l.value?.[n],o=l.placeholder?.[n],i=l.propType.shape[n];return e.createElement(w.Provider,{value:{...l,value:a,setValue:(e,t,r)=>{const a={...l.value,[n]:e};return l?.setValue(a,t,{...r,bind:n})},placeholder:o,bind:n,propType:i,path:[...r??[],n]}},t)},I=({children:t,bind:n})=>{const l=T(),{path:r}=(0,e.useContext)(w)??{},a=l.value?.[Number(n)],o=l.propType.item_prop_type;return e.createElement(w.Provider,{value:{...l,value:a,setValue:(e,t)=>{const r=[...l.value??[]];return r[Number(n)]=e,l?.setValue(r,t,{bind:n})},bind:n,propType:o,path:[...r??[],n]}},t)},k=()=>{const t=(0,e.useContext)(w);if(!t)throw new x({context:{hook:"usePropKeyContext",provider:"PropKeyProvider"}});return t};function P(e){const t=k(),{isValid:n,validate:l,restoreValue:r}=V(t.propType),a=t.isDisabled?.(t.propType);if(!e)return{...t,disabled:a};const o=$(t.propType,e.key),i=e.extract(t.value??o.default??null),c=e.extract(t.placeholder??null);return{...t,propType:o,setValue:function(n,r,a){if(l(n))return null===n?t?.setValue(null,r,a):t?.setValue(e?.create(n,r),{},a)},value:n?i:null,restoreValue:r,placeholder:c,disabled:a}}var V=t=>{const[n,l]=(0,e.useState)(!0);return{isValid:n,setIsValid:l,validate:e=>{let n=!0;return t.settings.required&&null===e&&(n=!1),l(n),n},restoreValue:()=>l(!0)}},$=(e,t)=>{let n=e;if("union"===e.kind&&(n=e.prop_types[t]),!n)throw new h({context:{key:t}});return n},U=t=>e.createElement(r.FormLabel,{size:"tiny",...t}),R=(0,e.createContext)([]),G=({replacements:t,children:n})=>e.createElement(R.Provider,{value:t},n),L=()=>{const e=[];return{registerControlReplacement:function(t){e.push(t)},getControlReplacements:function(){return e}}};function O(t){return n=>{const l=(t=>{const{value:n}=P(),l=(0,e.useContext)(R);try{const e=l.find((e=>e.condition({value:n})));return e?.component??t}catch{return t}})(t);return e.createElement(r.ErrorBoundary,{fallback:null},e.createElement(l,{...n}))}}Symbol("control");var F="elementor/v1/settings",B=e=>e.data.value,A="elementor_unfiltered_files_upload",M={queryKey:[A]},W=()=>(0,i.useQuery)({...M,queryFn:()=>{return(e=A,(0,c.httpService)().get(`${F}/${e}`).then((e=>B(e.data)))).then((e=>D(e)));var e},staleTime:1/0}),D=e=>Boolean("1"===e),j=(0,e.createContext)(null),N=({children:t,items:n})=>e.createElement(j.Provider,{value:{items:n}},t),K=()=>{const t=(0,e.useContext)(j);if(!t)throw new Error("useControlActions must be used within a ControlActionsProvider");return t},H=(0,r.styled)("span")`
	display: contents;

	.MuiFloatingActionBar-popper:has( .MuiFloatingActionBar-actions:empty ) {
		display: none;
	}

	.MuiFloatingActionBar-popper {
		z-index: 1000;
	}
`;function q({children:t}){const{items:n}=K(),{disabled:l}=P();if(0===n.length||l)return t;const a=n.map((({MenuItem:t,id:n})=>e.createElement(t,{key:n})));return e.createElement(H,null,e.createElement(r.UnstableFloatingActionBar,{actions:a},t))}var X=O((({mediaTypes:n=["image"]})=>{const{value:l,setValue:o}=P(t.imageSrcPropTypeUtil),{id:i,url:c}=l??{},{data:m,isFetching:d}=(0,u.useWpMediaAttachment)(i?.value||null),p=m?.url??c?.value??null,{open:E}=(0,u.useWpMediaFrame)({mediaTypes:n,multiple:!1,selected:i?.value||null,onSelect:e=>{o({id:{$$type:"image-attachment-id",value:e.id},url:null})}});return e.createElement(q,null,e.createElement(r.Card,{variant:"outlined"},e.createElement(r.CardMedia,{image:p,sx:{height:150}},d?e.createElement(r.Stack,{justifyContent:"center",alignItems:"center",width:"100%",height:"100%"},e.createElement(r.CircularProgress,null)):e.createElement(e.Fragment,null)),e.createElement(r.CardOverlay,null,e.createElement(r.Stack,{gap:1},e.createElement(r.Button,{size:"tiny",color:"inherit",variant:"outlined",onClick:()=>E({mode:"browse"})},(0,a.__)("Select image","elementor")),e.createElement(r.Button,{size:"tiny",variant:"text",color:"inherit",startIcon:e.createElement(s.UploadIcon,null),onClick:()=>E({mode:"upload"})},(0,a.__)("Upload","elementor"))))))})),Y=O((({options:n,onChange:l})=>{const{value:a,setValue:o,disabled:i,placeholder:c}=P(t.stringPropTypeUtil);return e.createElement(q,null,e.createElement(r.Select,{sx:{overflow:"hidden"},displayEmpty:!0,size:"tiny",renderValue:t=>{const l=e=>n.find((t=>t.value===e));if(!t||""===t){if(c){const t=l(c),n=t?.label||c;return e.createElement(r.Typography,{component:"span",variant:"caption",color:"text.tertiary"},n)}return""}const a=l(t);return a?.label||t},value:a??"",onChange:e=>{const t=e.target.value||null;l?.(t,a),o(t)},disabled:i,fullWidth:!0},n.map((({label:t,...n})=>e.createElement(m.MenuListItem,{key:n.value,...n,value:n.value??""},t)))))})),Q=O((({sizes:n,showMode:l="all"})=>{const o=P(t.imagePropTypeUtil);let i;switch(l){case"media":i=e.createElement(Z,null);break;case"sizes":i=e.createElement(J,{sizes:n});break;default:i=e.createElement(r.Stack,{gap:1.5},e.createElement(U,null,(0,a.__)("Image","elementor")),e.createElement(Z,null),e.createElement(r.Grid,{container:!0,gap:1.5,alignItems:"center",flexWrap:"nowrap"},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(U,null,(0,a.__)("Resolution","elementor"))),e.createElement(r.Grid,{item:!0,xs:6,sx:{overflow:"hidden"}},e.createElement(J,{sizes:n}))))}return e.createElement(C,{...o},i)})),Z=()=>{const{data:t}=W(),n=t?["image","svg"]:["image"];return e.createElement(S,{bind:"src"},e.createElement(X,{mediaTypes:n}))},J=({sizes:t})=>e.createElement(S,{bind:"size"},e.createElement(Y,{options:t})),ee=O((({placeholder:n})=>{const{value:l,setValue:a,disabled:o}=P(t.stringPropTypeUtil);return e.createElement(q,null,e.createElement(r.TextField,{size:"tiny",fullWidth:!0,disabled:o,value:l??"",onChange:e=>a(e.target.value),placeholder:n}))})),te=O((({placeholder:n})=>{const{value:l,setValue:a,disabled:o}=P(t.stringPropTypeUtil);return e.createElement(q,null,e.createElement(r.TextField,{size:"tiny",multiline:!0,fullWidth:!0,minRows:5,disabled:o,value:l??"",onChange:e=>{a(e.target.value)},placeholder:n}))})),ne=["px","%","em","rem","vw","vh"],le=["auto","custom"];function re(e){return le.includes(e)}var ae=(0,e.forwardRef)((({placeholder:t,type:n,value:l,onChange:a,onBlur:o,onKeyDown:i,onKeyUp:c,shouldBlockInput:s=!1,inputProps:u,disabled:m},d)=>e.createElement(r.TextField,{ref:d,sx:{input:{cursor:s?"default !important":void 0}},size:"tiny",fullWidth:!0,type:s?void 0:n,value:l,onChange:s?void 0:a,onKeyDown:s?void 0:i,onKeyUp:s?void 0:c,disabled:m,onBlur:o,placeholder:t,InputProps:u}))),oe=({options:t,alternativeOptionLabels:n={},onClick:l,value:a,menuItemsAttributes:o={},disabled:i})=>{const c=(0,r.usePopupState)({variant:"popover",popupId:(0,e.useId)()});return e.createElement(r.InputAdornment,{position:"end"},e.createElement(r.Button,{size:"small",color:"secondary",disabled:i,sx:{font:"inherit",minWidth:"initial",textTransform:"uppercase"},...(0,r.bindTrigger)(c)},n[a]??a),e.createElement(r.Menu,{MenuListProps:{dense:!0},...(0,r.bindMenu)(c)},t.map(((r,a)=>e.createElement(m.MenuListItem,{key:r,onClick:()=>(e=>{l(t[e]),c.close()})(a),...o?.[r]},n[r]??r.toUpperCase())))))},ie=["e","E","+","-"],ce=({units:t,handleUnitChange:n,handleSizeChange:l,placeholder:a,startIcon:o,onBlur:i,onFocus:c,onClick:u,size:m,unit:d,popupState:p,disabled:E})=>{const v=(0,e.useRef)(""),b=re(d)?"text":"number",g=!re(d)&&Number.isNaN(m)?"":m??"",f={"aria-controls":p.isOpen?p.popupId:void 0,"aria-haspopup":!0},h={...f,autoComplete:"off",onClick:u,onFocus:c,startAdornment:o?e.createElement(r.InputAdornment,{position:"start",disabled:E},o):void 0,endAdornment:e.createElement(oe,{disabled:E,options:t,onClick:n,value:d,alternativeOptionLabels:{custom:e.createElement(s.PencilIcon,{fontSize:"small"})},menuItemsAttributes:t.includes("custom")?{custom:f}:void 0})};return e.createElement(q,null,e.createElement(r.Box,null,e.createElement(ae,{disabled:E,placeholder:a,type:b,value:g,onChange:l,onKeyDown:e=>{ie.includes(e.key)&&e.preventDefault()},onKeyUp:e=>{const{key:l}=e;if(!/^[a-zA-Z%]$/.test(l))return;e.preventDefault();const r=l.toLowerCase(),a=(v.current+r).slice(-3);v.current=a;const o=t.find((e=>e.includes(a)))||t.find((e=>e.startsWith(r)))||t.find((e=>e.includes(r)));o&&n(o)},onBlur:i,shouldBlockInput:re(d),inputProps:h})))},se=t=>{const{popupState:n,restoreValue:l,anchorRef:a,value:o,onChange:i}=t;return e.createElement(r.Popover,{disablePortal:!0,slotProps:{paper:{sx:{borderRadius:2,width:a.current?.offsetWidth+"px",p:1.5}}},...(0,r.bindPopover)(n),anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},onClose:()=>{l(),n.close()}},e.createElement(r.TextField,{value:o,onChange:i,size:"tiny",type:"text",fullWidth:!0,inputProps:{autoFocus:!0}}))},ue=({external:t,setExternal:n,persistWhen:l,fallback:r})=>{function a(e,t){return e||r(t)}const[o,i]=(0,e.useState)(a(t,null));return(0,e.useEffect)((()=>{i((e=>a(t,e)))}),[t]),[o,e=>{const t=("function"==typeof e?e:()=>e)(o);var r;i(t),n(l(r=t)?r:null)}]},me="px",de=NaN,pe=O((n=>{const l=n.defaultUnit??me,{units:a=[...ne],placeholder:o,startIcon:i,anchorRef:c}=n,{value:s,setValue:u,disabled:m,restoreValue:E}=P(t.sizePropTypeUtil),[v,b]=(0,e.useState)(ve(s,l)),g=(0,d.useActiveBreakpoint)(),f=(h=n.extendedOptions||[],y=n.disableCustom??!1,(0,e.useMemo)((()=>{const e=!(0,p.isExperimentActive)("e_v_3_30")||y,t=[...h];return e||t.includes("custom")?h.includes("custom")&&t.splice(t.indexOf("custom"),1):t.push("custom"),t}),[h,y]));var h,y;const x=(0,r.usePopupState)({variant:"popover"}),[_,C]=ue({external:v,setExternal:e=>u(be(e)),persistWhen:e=>!!e?.unit&&(re(e.unit)?"auto"===e.unit||!!e.custom:!!e?.numeric||0===e?.numeric),fallback:e=>({unit:e?.unit??n.defaultUnit??me,numeric:e?.numeric??de,custom:e?.custom??""})}),{size:T=de,unit:w=me}=be(_)||{},S=e=>{const{value:t}=e.target;C("auto"!==w?e=>({...e,["custom"===w?"custom":"numeric"]:Ee(t,w),unit:w}):e=>({...e,unit:w}))};return(0,e.useEffect)((()=>{const e=ve(s,l),t=re(_.unit)?"custom":"numeric",n={..._,[t]:e[t]};"auto"!==n.unit&&ge(_,n)||(_.unit!==e.unit?C(e):b(n))}),[s]),(0,e.useEffect)((()=>{const e=ve(s,l);g&&!ge(e,_)&&C(e)}),[g]),e.createElement(e.Fragment,null,e.createElement(ce,{disabled:m,size:T,unit:w,units:[...a,...f||[]],placeholder:o,startIcon:i,handleSizeChange:S,handleUnitChange:e=>{"custom"===e&&x.open(c?.current),C((t=>({...t,unit:e})))},onBlur:E,onFocus:e=>{re(_.unit)&&e.target?.blur()},onClick:e=>{e.target.closest("input")&&"custom"===_.unit&&x.open(c?.current)},popupState:x}),c?.current&&e.createElement(se,{popupState:x,anchorRef:c,restoreValue:E,value:T,onChange:S}))}));function Ee(e,t){return re(t)?"auto"===t?"":String(e??""):e||0===e?Number(e):NaN}function ve(e,t){const n=e?.unit??t,l=e?.size??"";return{numeric:re(n)||isNaN(Number(l))||!l&&0!==l?de:Number(l),custom:"custom"===n?String(l):"",unit:n}}function be(e){if(!e)return null;if(!e?.unit)return{size:de,unit:me};const{unit:t}=e;return"auto"===t?{size:"",unit:t}:{size:e["custom"===t?"custom":"numeric"],unit:t}}function ge(e,t){return e.unit===t.unit&&e.custom===t.custom&&(re(e.unit)?e.custom===t.custom:e.numeric===t.numeric||isNaN(e.numeric)&&isNaN(t.numeric))}var fe=({gap:t=2,sx:n,children:l})=>e.createElement(r.Stack,{gap:t,sx:{...n}},l),he=O((({propTypeUtil:n=t.colorPropTypeUtil,anchorEl:l,slotProps:a={},...o})=>{const{value:i,setValue:c,disabled:s}=P(n);return e.createElement(q,null,e.createElement(r.UnstableColorField,{size:"tiny",fullWidth:!0,value:i??"",onChange:e=>{c(e||null)},...o,disabled:s,slotProps:{...a,colorPicker:{anchorEl:l,anchorOrigin:{vertical:"top",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:-10}}}}))})),ye=["px","em","rem"],xe=O((()=>{const n=P(t.strokePropTypeUtil),l=(0,e.useRef)(null);return e.createElement(C,{...n},e.createElement(fe,null,e.createElement(_e,{bind:"width",label:(0,a.__)("Stroke width","elementor"),ref:l},e.createElement(pe,{units:ye,anchorRef:l})),e.createElement(_e,{bind:"color",label:(0,a.__)("Stroke color","elementor")},e.createElement(he,null))))})),_e=(0,e.forwardRef)((({bind:t,label:n,children:l},a)=>e.createElement(S,{bind:t},e.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap",ref:a},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(U,null,n)),e.createElement(r.Grid,{item:!0,xs:6},l))))),Ce=({gap:t=1.5,children:n,...l})=>e.createElement(r.Stack,{...l,gap:t},n),Te=(0,e.forwardRef)((({gap:t=1.5,alignItems:n="center",flexWrap:l="nowrap",children:a},o)=>e.createElement(r.Grid,{container:!0,gap:t,alignItems:n,flexWrap:l,ref:o},a))),we=(0,e.createContext)(null),Se=({children:t,items:n})=>e.createElement(we.Provider,{value:{items:n}},t),ze=()=>{const t=(0,e.useContext)(we);return t?.items??[]};function Ie(){const t=ze();return 0===t?.length?null:e.createElement(e.Fragment,null,t.map((({Adornment:t,id:n})=>e.createElement(t,{key:n}))))}var{Slot:ke,inject:Pe}=(0,E.createReplaceableLocation)(),{Slot:Ve,inject:$e}=(0,E.createReplaceableLocation)(),Ue=t=>e.createElement(r.List,{sx:{p:0,my:-.5,mx:0}},e.createElement(r.UnstableSortableProvider,{restrictAxis:!0,disableDragOverlay:!1,variant:"static",...t})),Re=({id:t,children:n,disabled:l})=>e.createElement(r.UnstableSortableItem,{id:t,disabled:l,render:({itemProps:t,triggerProps:r,itemStyle:a,triggerStyle:o,showDropIndication:i,dropIndicationStyle:c})=>e.createElement(Ge,{...t,style:a},!l&&e.createElement(Le,{...r,style:o}),n,i&&e.createElement(Oe,{style:c}))}),Ge=(0,r.styled)(r.ListItem)`
	position: relative;
	margin-inline: 0px;
	padding-inline: 0px;
	padding-block: ${({theme:e})=>e.spacing(.5)};

	& .class-item-sortable-trigger {
		color: ${({theme:e})=>e.palette.action.active};
		height: 100%;
		display: flex;
		align-items: center;
		visibility: hidden;
		position: absolute;
		top: 50%;
		padding-inline-end: ${({theme:e})=>e.spacing(.5)};
		transform: translate( -75%, -50% );
	}

	&[aria-describedby=''] > .MuiTag-root {
		background-color: ${({theme:e})=>e.palette.background.paper};
		box-shadow: ${({theme:e})=>e.shadows[3]};
	}

	&:hover {
		& .class-item-sortable-trigger {
			visibility: visible;
		}
	}
`,Le=t=>e.createElement("div",{...t,role:"button",className:"class-item-sortable-trigger"},e.createElement(s.GripVerticalIcon,{fontSize:"tiny"})),Oe=(0,r.styled)(r.Divider)`
	height: 0px;
	border: none;
	overflow: visible;

	&:after {
		--height: 2px;
		content: '';
		display: block;
		width: 100%;
		height: var( --height );
		margin-block: calc( -1 * var( --height ) / 2 );
		border-radius: ${({theme:e})=>e.spacing(.5)};
		background-color: ${({theme:e})=>e.palette.text.primary};
	}
`,Fe="tiny",Be=({label:t,itemSettings:n,disabled:l=!1,openOnAdd:o=!1,addToBottom:i=!1,values:c=[],setValues:u,showDuplicate:m=!0,showToggle:d=!0,isSortable:p=!0})=>{const[E,v]=(0,e.useState)(-1),[b,g]=ue({external:c,setExternal:u,persistWhen:()=>!0}),[f,h]=(0,e.useState)(b.map(((e,t)=>t))),y=e=>1+Math.max(0,...e);return e.createElement(fe,null,e.createElement(r.Stack,{direction:"row",justifyContent:"start",alignItems:"center",gap:1,sx:{marginInlineEnd:-.75}},e.createElement(r.Typography,{component:"label",variant:"caption",color:"text.secondary"},t),e.createElement(Ie,null),e.createElement(r.IconButton,{size:Fe,sx:{ml:"auto"},disabled:l,onClick:()=>{const e=structuredClone(n.initialValues),t=y(f);i?(g([...b,e]),h([...f,t])):(g([e,...b]),h([t,...f])),o&&v(t)},"aria-label":(0,a.__)("Add item","elementor")},e.createElement(s.PlusIcon,{fontSize:Fe}))),0<f.length&&e.createElement(Ue,{value:f,onChange:e=>{h(e),g((t=>e.map((e=>{const n=f.indexOf(e);return t[n]}))))}},f.map(((t,r)=>{const a=b[r];return a?e.createElement(Re,{id:t,key:`sortable-${t}`,disabled:!p},e.createElement(Ae,{disabled:l,propDisabled:a?.disabled,label:e.createElement(Ve,{value:a},e.createElement(n.Label,{value:a})),startIcon:e.createElement(ke,{value:a},e.createElement(n.Icon,{value:a})),removeItem:()=>(e=>{h(f.filter(((t,n)=>n!==e))),g(b.filter(((t,n)=>n!==e)))})(r),duplicateItem:()=>(e=>{const t=structuredClone(b[e]),n=y(f),l=1+e;g([...b.slice(0,l),t,...b.slice(l)]),h([...f.slice(0,l),n,...f.slice(l)])})(r),toggleDisableItem:()=>(e=>{g(b.map(((t,n)=>{if(n===e){const{disabled:e,...n}=t;return{...n,...e?{}:{disabled:!0}}}return t})))})(r),openOnMount:o&&E===t,onOpen:()=>v(-1),showDuplicate:m,showToggle:d},(t=>e.createElement(n.Content,{...t,value:a,bind:String(r)})))):null}))))},Ae=({label:t,propDisabled:n,startIcon:l,children:o,removeItem:i,duplicateItem:c,toggleDisableItem:u,openOnMount:m,onOpen:d,showDuplicate:p,showToggle:E,disabled:v})=>{const[b,g]=(0,e.useState)(null),{popoverState:f,popoverProps:h,ref:y,setRef:x}=Me(m,d),_=(0,a.__)("Duplicate","elementor"),C=n?(0,a.__)("Show","elementor"):(0,a.__)("Hide","elementor"),T=(0,a.__)("Remove","elementor");return e.createElement(e.Fragment,null,e.createElement(r.UnstableTag,{disabled:v,label:t,showActionsOnHover:!0,fullWidth:!0,ref:x,variant:"outlined","aria-label":(0,a.__)("Open item","elementor"),...(0,r.bindTrigger)(f),startIcon:l,actions:e.createElement(e.Fragment,null,p&&e.createElement(r.Tooltip,{title:_,placement:"top"},e.createElement(r.IconButton,{size:Fe,onClick:c,"aria-label":_},e.createElement(s.CopyIcon,{fontSize:Fe}))),E&&e.createElement(r.Tooltip,{title:C,placement:"top"},e.createElement(r.IconButton,{size:Fe,onClick:u,"aria-label":C},n?e.createElement(s.EyeOffIcon,{fontSize:Fe}):e.createElement(s.EyeIcon,{fontSize:Fe}))),e.createElement(r.Tooltip,{title:T,placement:"top"},e.createElement(r.IconButton,{size:Fe,onClick:i,"aria-label":T},e.createElement(s.XIcon,{fontSize:Fe}))))}),e.createElement(r.Popover,{disablePortal:!0,slotProps:{paper:{ref:g,sx:{mt:.5,width:y?.getBoundingClientRect().width}}},anchorOrigin:{vertical:"bottom",horizontal:"left"},...h,anchorEl:y},e.createElement(r.Box,null,o({anchorEl:b}))))},Me=(t,n)=>{const[l,a]=(0,e.useState)(null),o=(0,r.usePopupState)({variant:"popover"}),i=(0,r.bindPopover)(o);return(0,e.useEffect)((()=>{t&&l&&(o.open(l),n?.())}),[l]),{popoverState:o,ref:l,setRef:a,popoverProps:i}},We=O((()=>{const{propType:n,value:l,setValue:r,disabled:o}=P(t.boxShadowPropTypeUtil);return e.createElement(C,{propType:n,value:l,setValue:r,isDisabled:()=>o},e.createElement(Be,{openOnAdd:!0,disabled:o,values:l??[],setValues:r,label:(0,a.__)("Box shadow","elementor"),itemSettings:{Icon:De,Label:He,Content:je,initialValues:qe}}))})),De=({value:t})=>e.createElement(r.UnstableColorIndicator,{size:"inherit",component:"span",value:t.value.color?.value}),je=({anchorEl:t,bind:n})=>e.createElement(S,{bind:n},e.createElement(Ne,{anchorEl:t})),Ne=({anchorEl:n})=>{const l=P(t.shadowPropTypeUtil),r=[(0,e.useRef)(null),(0,e.useRef)(null)];return e.createElement(C,{...l},e.createElement(Ce,{p:1.5},e.createElement(Te,null,e.createElement(Ke,{bind:"color",label:(0,a.__)("Color","elementor")},e.createElement(he,{anchorEl:n})),e.createElement(Ke,{bind:"position",label:(0,a.__)("Position","elementor"),sx:{overflow:"hidden"}},e.createElement(Y,{options:[{label:(0,a.__)("Inset","elementor"),value:"inset"},{label:(0,a.__)("Outset","elementor"),value:null}]}))),e.createElement(Te,{ref:r[0]},e.createElement(Ke,{bind:"hOffset",label:(0,a.__)("Horizontal","elementor")},e.createElement(pe,{anchorRef:r[0]})),e.createElement(Ke,{bind:"vOffset",label:(0,a.__)("Vertical","elementor")},e.createElement(pe,{anchorRef:r[0]}))),e.createElement(Te,{ref:r[1]},e.createElement(Ke,{bind:"blur",label:(0,a.__)("Blur","elementor")},e.createElement(pe,{anchorRef:r[1]})),e.createElement(Ke,{bind:"spread",label:(0,a.__)("Spread","elementor")},e.createElement(pe,{anchorRef:r[1]})))))},Ke=({label:t,bind:n,children:l,sx:a})=>e.createElement(S,{bind:n},e.createElement(r.Grid,{item:!0,xs:6,sx:a},e.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(r.FormLabel,{size:"tiny"},t)),e.createElement(r.Grid,{item:!0,xs:12},l)))),He=({value:t})=>{const{position:n,hOffset:l,vOffset:r,blur:a,spread:o}=t.value,{size:i="",unit:c=""}=a?.value||{},{size:s="",unit:u=""}=o?.value||{},{size:m="unset",unit:d=""}=l?.value||{},{size:p="unset",unit:E=""}=r?.value||{},v=n?.value||"outset",b=[m+d,p+E,i+c,s+u].join(" ");return e.createElement("span",{style:{textTransform:"capitalize"}},v,": ",b)},qe={$$type:"shadow",value:{hOffset:{$$type:"size",value:{unit:"px",size:0}},vOffset:{$$type:"size",value:{unit:"px",size:0}},blur:{$$type:"size",value:{unit:"px",size:10}},spread:{$$type:"size",value:{unit:"px",size:0}},color:{$$type:"color",value:"rgba(0, 0, 0, 1)"},position:null}},Xe=({children:t})=>e.createElement(r.Stack,{direction:"row",alignItems:"center",justifyItems:"start",gap:.25},e.createElement(U,null,t),e.createElement(Ie,null)),Ye="blur",Qe={blur:{defaultValue:{$$type:"radius",radius:{$$type:"size",value:{size:0,unit:"px"}}},name:(0,a.__)("Blur","elementor"),valueName:(0,a.__)("Radius","elementor"),propType:t.blurFilterPropTypeUtil,units:ne.filter((e=>"%"!==e))},brightness:{defaultValue:{$$type:"amount",amount:{$$type:"size",value:{size:100,unit:"%"}}},name:(0,a.__)("Brightness","elementor"),valueName:(0,a.__)("Amount","elementor"),propType:t.brightnessFilterPropTypeUtil,units:["%"]}},Ze=Object.keys(Qe),Je=Ze.filter((e=>{const t=Qe[e].defaultValue;return"size"===t[t.$$type].$$type})),et=O((()=>{const{propType:n,value:l,setValue:r,disabled:o}=P(t.filterPropTypeUtil);return e.createElement(C,{propType:n,value:l,setValue:r},e.createElement(Be,{openOnAdd:!0,disabled:o,values:l??[],setValues:r,label:(0,a.__)("Filter","elementor"),itemSettings:{Icon:tt,Label:nt,Content:rt,initialValues:{$$type:Ye,value:Qe[Ye].defaultValue}}}))})),tt=()=>e.createElement(e.Fragment,null),nt=t=>{const{$$type:n}=t.value;return Je.includes(n)&&e.createElement(lt,{value:t.value})},lt=({value:t})=>{const{$$type:n,value:l}=t,{$$type:a}=Qe[n].defaultValue,o=Qe[n].defaultValue[a].value.unit,{unit:i,size:c}=l[a]?.value??{unit:o,size:0},s=e.createElement(r.Box,{component:"span",style:{textTransform:"capitalize"}},t.$$type,":");return e.createElement(r.Box,{component:"span"},s,"custom"!==i?` ${c??0}${i??o}`:c)},rt=({bind:n})=>{const{value:l,setValue:o}=P(t.filterPropTypeUtil),i=parseInt(n,10),c=l?.[i];return e.createElement(S,{bind:n},e.createElement(Ce,{p:1.5},e.createElement(Te,null,e.createElement(r.Grid,{item:!0,xs:6},e.createElement(Xe,null,(0,a.__)("Filter","elementor"))),e.createElement(r.Grid,{item:!0,xs:6},e.createElement(r.Select,{sx:{overflow:"hidden"},size:"tiny",value:c?.$$type??Ye,onChange:e=>{const t=[...l],n=e.target.value;t[i]={$$type:n,value:Qe[n].defaultValue},o(t)},fullWidth:!0},Ze.map((t=>e.createElement(m.MenuListItem,{key:t,value:t},Qe[t].name)))))),e.createElement(at,{filterType:c?.$$type})))},at=({filterType:t})=>Je.includes(t)&&e.createElement(ot,{filterType:t}),ot=({filterType:t})=>{const{propType:n,valueName:l,defaultValue:a,units:o}=Qe[t],{$$type:i}=a,c=P(n),s=(0,e.useRef)(null);return e.createElement(C,{...c},e.createElement(S,{bind:i},e.createElement(Te,{ref:s},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(Xe,null,l)),e.createElement(r.Grid,{item:!0,xs:6},e.createElement(pe,{anchorRef:s,units:o})))))},it=({showTooltip:t,children:n,label:l})=>t&&l?e.createElement(r.Tooltip,{title:l,disableFocusListener:!0,placement:"top"},n):n,ct=(0,r.styled)(r.ToggleButtonGroup)`
	${({justify:e})=>`justify-content: ${e};`}
	button:not( :last-of-type ) {
		border-start-end-radius: 0;
		border-end-end-radius: 0;
	}
	button:not( :first-of-type ) {
		border-start-start-radius: 0;
		border-end-start-radius: 0;
	}
	button:last-of-type {
		border-start-end-radius: 8px;
		border-end-end-radius: 8px;
	}
`,st=({justify:t="end",size:n="tiny",value:l,onChange:a,items:o,maxItems:i,exclusive:c=!1,fullWidth:s=!1,disabled:u})=>{const m=c&&void 0!==i&&o.length>i,d=m?o.slice(i-1):[],p=m?o.slice(0,i-1):o,E="rtl"===(0,r.useTheme)().direction,v=(0,e.useMemo)((()=>{const e=d?.length;return`repeat(${e?p.length+1:p.length}, minmax(0, 25%)) ${e?"auto":""}`}),[d?.length,p.length]);return e.createElement(q,null,e.createElement(ct,{justify:t,value:l,onChange:(e,t)=>{a(t)},exclusive:c,disabled:u,sx:{direction:E?"rtl /* @noflip */":"ltr /* @noflip */",display:"grid",gridTemplateColumns:v,width:"100%"}},p.map((({label:t,value:l,renderContent:a,showTooltip:o})=>e.createElement(it,{key:l,label:t,showTooltip:o||!1},e.createElement(r.ToggleButton,{value:l,"aria-label":t,size:n,fullWidth:s},e.createElement(a,{size:n}))))),d.length&&c&&e.createElement(ut,{size:n,value:l||null,onChange:a,items:d,fullWidth:s})))},ut=({size:t="tiny",onChange:n,items:l,fullWidth:a,value:o})=>{const i=mt(l,o),[c,u]=(0,e.useState)(!1),m=(0,e.useRef)(null),d=e=>{u(!1),p(e)},p=e=>{n(e===o?null:e)};return e.createElement(e.Fragment,null,e.createElement(r.ToggleButton,{value:i.value,"aria-label":i.label,size:t,fullWidth:a,onClick:e=>{e.preventDefault(),d(i.value)},ref:m},i.renderContent({size:t})),e.createElement(r.ToggleButton,{size:t,"aria-expanded":c?"true":void 0,"aria-haspopup":"menu","aria-pressed":void 0,onClick:e=>{u((e=>!e)),e.preventDefault()},ref:m,value:"__chevron-icon-button__"},e.createElement(s.ChevronDownIcon,{fontSize:t})),e.createElement(r.Menu,{open:c,onClose:()=>u(!1),anchorEl:m.current,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},sx:{mt:.5}},l.map((({label:t,value:n})=>e.createElement(r.MenuItem,{key:n,selected:n===o,onClick:()=>d(n)},e.createElement(r.ListItemText,null,e.createElement(r.Typography,{sx:{fontSize:"14px"}},t)))))))},mt=(t,n)=>{const[l,r]=(0,e.useState)(t.find((e=>e.value===n))??t[0]);return(0,e.useEffect)((()=>{const e=t.find((e=>e.value===n));e&&r(e)}),[t,n]),l},dt=O((({options:n,fullWidth:l=!1,size:r="tiny",exclusive:a=!0,maxItems:o})=>{const{value:i,setValue:c,placeholder:s,disabled:u}=P(t.stringPropTypeUtil),m=n.filter((e=>e.exclusive)).map((e=>e.value)),d={items:n,maxItems:o,fullWidth:l,size:r};return a?e.createElement(st,{...d,value:i??s??null,onChange:c,disabled:u,exclusive:!0}):e.createElement(st,{...d,value:(i??s)?.split(" ")??[],onChange:e=>{const t=e[e.length-1],n=m.includes(t)?[t]:e?.filter((e=>!m.includes(e)));c(n?.join(" ")||null)},disabled:u,exclusive:!1})})),pt=e=>null==e||""===e||Number.isNaN(Number(e)),Et=["e","E","+","-"],vt=O((({placeholder:n,max:l=Number.MAX_VALUE,min:a=-Number.MAX_VALUE,step:o=1,shouldForceInt:i=!1})=>{const{value:c,setValue:s,placeholder:u,disabled:m}=P(t.numberPropTypeUtil);return e.createElement(q,null,e.createElement(r.TextField,{size:"tiny",type:"number",fullWidth:!0,disabled:m,value:pt(c)?"":c,onChange:e=>{const t=e.target.value;if(pt(t))return void s(null);const n=i?+parseInt(t):Number(t);s(Math.min(Math.max(n,a),l))},placeholder:n??(u?String(u):""),inputProps:{step:o},onKeyDown:e=>{Et.includes(e.key)&&e.preventDefault()}}))})),bt=(e,t)=>{const n=Object.values(e);if(n.length!==t.length)return!1;const[l,...r]=n;return r.every((e=>e?.value?.size===l?.value?.size&&e?.value?.unit===l?.value?.unit))};function gt({label:n,icon:l,tooltipLabel:o,items:i,multiSizePropTypeUtil:c}){const s=(0,e.useId)(),u=(0,r.usePopupState)({variant:"popover",popupId:s}),{propType:m,value:d,setValue:E,disabled:v}=P(c),{value:b,setValue:g}=P(t.sizePropTypeUtil),f=[(0,e.useRef)(null),(0,e.useRef)(null)],h=()=>b?i.reduce(((e,{bind:n})=>({...e,[n]:t.sizePropTypeUtil.create(b)})),{}):null,y=!(0,p.isExperimentActive)("e_v_3_30")||!u.isOpen,x=!!d;return e.createElement(e.Fragment,null,e.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap",ref:f[0]},e.createElement(r.Grid,{item:!0,xs:6},y?e.createElement(Xe,null,n):e.createElement(U,null,n)),e.createElement(r.Grid,{item:!0,xs:6},e.createElement(r.Stack,{direction:"row",alignItems:"center",gap:1},e.createElement(pe,{placeholder:x?(0,a.__)("Mixed","elementor"):void 0,anchorRef:f[0]}),e.createElement(r.Tooltip,{title:o,placement:"top"},e.createElement(r.ToggleButton,{size:"tiny",value:"check",sx:{marginLeft:"auto"},...(0,r.bindToggle)(u),selected:u.isOpen,"aria-label":o},l))))),e.createElement(r.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},...(0,r.bindPopover)(u),slotProps:{paper:{sx:{mt:.5,width:f[0].current?.getBoundingClientRect().width}}}},e.createElement(C,{propType:m,value:d||(h()??null),setValue:e=>{const t={...d??h(),...e};if(bt(t,i))return g(Object.values(t)[0]?.value);E(t)},isDisabled:()=>v},e.createElement(Ce,{p:1.5},e.createElement(Te,{ref:f[1]},e.createElement(ft,{item:i[0],rowRef:f[1]}),e.createElement(ft,{item:i[1],rowRef:f[1]})),e.createElement(Te,{ref:f[2]},e.createElement(ft,{item:i[2],rowRef:f[2]}),e.createElement(ft,{item:i[3],rowRef:f[2]}))))))}var ft=({item:t,rowRef:n})=>{const l=(0,p.isExperimentActive)("e_v_3_30");return e.createElement(S,{bind:t.bind},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},e.createElement(r.Grid,{item:!0,xs:12},l?e.createElement(Xe,null,t.label):e.createElement(U,null,t.label)),e.createElement(r.Grid,{item:!0,xs:12},e.createElement(pe,{startIcon:t.icon,anchorRef:n})))))},ht=O((({label:n,isSiteRtl:l=!1,extendedOptions:o})=>{const{value:i,setValue:c,disabled:u}=P(t.sizePropTypeUtil),m=[(0,e.useRef)(null),(0,e.useRef)(null)],{value:d,setValue:E,propType:v,disabled:b}=P(t.dimensionsPropTypeUtil),g=!d&&!i||!!i,f=(0,p.isExperimentActive)("e_v_3_30"),h=n.toLowerCase(),y=g?s.LinkIcon:s.DetachIcon,x=(0,a.__)("Link %s","elementor").replace("%s",h),_=(0,a.__)("Unlink %s","elementor").replace("%s",h),T=u||b;return e.createElement(C,{propType:v,value:d,setValue:E,isDisabled:()=>T},e.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},f?e.createElement(U,null,n):e.createElement(Xe,null,n),e.createElement(r.Tooltip,{title:g?_:x,placement:"top"},e.createElement(r.ToggleButton,{"aria-label":g?_:x,size:"tiny",value:"check",selected:g,sx:{marginLeft:"auto"},onChange:()=>{if(!g)return void c(d["block-start"]?.value??null);const e=i?t.sizePropTypeUtil.create(i):null;E({"block-start":e,"block-end":e,"inline-start":e,"inline-end":e})},disabled:T},e.createElement(y,{fontSize:"tiny"})))),function(t){return[[{bind:"block-start",label:(0,a.__)("Top","elementor"),icon:e.createElement(s.SideTopIcon,{fontSize:"tiny"})},{bind:"inline-end",label:t?(0,a.__)("Left","elementor"):(0,a.__)("Right","elementor"),icon:t?e.createElement(s.SideLeftIcon,{fontSize:"tiny"}):e.createElement(s.SideRightIcon,{fontSize:"tiny"})}],[{bind:"block-end",label:(0,a.__)("Bottom","elementor"),icon:e.createElement(s.SideBottomIcon,{fontSize:"tiny"})},{bind:"inline-start",label:t?(0,a.__)("Right","elementor"):(0,a.__)("Left","elementor"),icon:t?e.createElement(s.SideRightIcon,{fontSize:"tiny"}):e.createElement(s.SideLeftIcon,{fontSize:"tiny"})}]]}(l).map(((t,n)=>e.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap",key:n,ref:m[n]},t.map((({icon:t,...l})=>e.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center",key:l.bind},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(xt,{...l})),e.createElement(r.Grid,{item:!0,xs:12},e.createElement(yt,{bind:l.bind,startIcon:t,isLinked:g,extendedOptions:o,anchorRef:m[n]})))))))))})),yt=({bind:t,startIcon:n,isLinked:l,extendedOptions:r,anchorRef:a})=>l?e.createElement(pe,{startIcon:n,extendedOptions:r,anchorRef:a}):e.createElement(S,{bind:t},e.createElement(pe,{startIcon:n,extendedOptions:r,anchorRef:a})),xt=({label:t,bind:n})=>(0,p.isExperimentActive)("e_v_3_30")?e.createElement(S,{bind:n},e.createElement(Xe,null,t)):e.createElement(U,null,t),_t=({fontFamilies:t,fontFamily:n,onFontFamilyChange:l,onClose:o,sectionWidth:i})=>{const[c,u]=(0,e.useState)(""),d=((e,t)=>e.reduce(((e,n)=>{const l=n.fonts.filter((e=>e.toLowerCase().includes(t.toLowerCase())));return l.length&&(e.push({type:"category",value:n.label}),l.forEach((t=>{e.push({type:"font",value:t})}))),e}),[]))(t,c),p=()=>{u(""),o()};return e.createElement(r.Stack,null,e.createElement(m.PopoverHeader,{title:(0,a.__)("Font Family","elementor"),onClose:p,icon:e.createElement(s.TextIcon,{fontSize:"tiny"})}),e.createElement(m.PopoverSearch,{value:c,onSearch:e=>{u(e)},placeholder:(0,a.__)("Search","elementor")}),e.createElement(r.Divider,null),e.createElement(m.PopoverScrollableContent,{width:i},d.length>0?e.createElement(Ct,{fontListItems:d,setFontFamily:l,handleClose:p,fontFamily:n}):e.createElement(r.Stack,{alignItems:"center",justifyContent:"center",height:"100%",p:2.5,gap:1.5,overflow:"hidden"},e.createElement(s.TextIcon,{fontSize:"large"}),e.createElement(r.Box,{sx:{maxWidth:160,overflow:"hidden"}},e.createElement(r.Typography,{align:"center",variant:"subtitle2",color:"text.secondary"},(0,a.__)("Sorry, nothing matched","elementor")),e.createElement(r.Typography,{variant:"subtitle2",color:"text.secondary",sx:{display:"flex",width:"100%",justifyContent:"center"}},e.createElement("span",null,"“"),e.createElement("span",{style:{maxWidth:"80%",overflow:"hidden",textOverflow:"ellipsis"}},c),e.createElement("span",null,"”."))),e.createElement(r.Typography,{align:"center",variant:"caption",color:"text.secondary",sx:{display:"flex",flexDirection:"column"}},(0,a.__)("Try something else.","elementor"),e.createElement(r.Link,{color:"secondary",variant:"caption",component:"button",onClick:()=>u("")},(0,a.__)("Clear & try again","elementor"))))))},Ct=({fontListItems:t,setFontFamily:n,handleClose:l,fontFamily:r})=>{const a=t.find((e=>e.value===r)),o=Tt((({getVirtualIndexes:e})=>{e().forEach((e=>{const n=t[e];n&&"font"===n.type&&((e,t="editor")=>{const n=window;n.elementor?.helpers?.enqueueFont?.(e,t)})(n.value)}))}),100);return e.createElement(m.PopoverMenuList,{items:t,selectedValue:a?.value,onChange:o,onSelect:n,onClose:l,itemStyle:e=>({fontFamily:e.value}),"data-testid":"font-list"})},Tt=(t,n)=>{const[l]=(0,e.useState)((()=>(0,o.debounce)(t,n)));return(0,e.useEffect)((()=>()=>l.cancel()),[l]),l},wt=O((({fontFamilies:n,sectionWidth:l})=>{const{value:a,setValue:o,disabled:i,placeholder:c}=P(t.stringPropTypeUtil),u=(0,r.usePopupState)({variant:"popover"}),m=!a&&c;return e.createElement(e.Fragment,null,e.createElement(q,null,e.createElement(r.UnstableTag,{variant:"outlined",label:a||c,endIcon:e.createElement(s.ChevronDownIcon,{fontSize:"tiny"}),...(0,r.bindTrigger)(u),fullWidth:!0,disabled:i,sx:m?{"& .MuiTag-label":{color:e=>e.palette.text.tertiary},textTransform:"capitalize"}:void 0})),e.createElement(r.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},sx:{my:1.5},...(0,r.bindPopover)(u)},e.createElement(_t,{fontFamilies:n,fontFamily:a,onFontFamilyChange:o,onClose:u.close,sectionWidth:l})))})),St=O((({placeholder:n})=>{const{value:l,setValue:a,disabled:o}=P(t.urlPropTypeUtil);return e.createElement(q,null,e.createElement(r.TextField,{size:"tiny",fullWidth:!0,value:l??"",disabled:o,onChange:e=>a(e.target.value),placeholder:n}))})),zt=(0,e.forwardRef)(((t,n)=>{const{options:l,onOptionChange:a,onTextChange:o,allowCustomValues:i=!1,placeholder:c="",minInputLength:s=2,value:u="",...m}=t,d=function(e,t,n){if(null===e)return t;const l=String(e||"")?.toLowerCase();return l.length<n?new Array(0):t.filter((e=>String(e.id).toLowerCase().includes(l)||e.label.toLowerCase().includes(l)))}(u,l,s).map((({id:e})=>e)),p=!!u,E=i||u?.toString()?.length?void 0:()=>!0,v="number"==typeof u&&!!Pt(l,u);return e.createElement(r.Autocomplete,{...m,ref:n,forcePopupIcon:!1,disableClearable:!0,freeSolo:i,value:u?.toString()||"",size:"tiny",onChange:(e,t)=>a(Number(t)),readOnly:v,options:d,getOptionKey:e=>Pt(l,e)?.id||e,getOptionLabel:e=>Pt(l,e)?.label||e.toString(),groupBy:Vt(l)?e=>Pt(l,e)?.groupLabel||e:void 0,isOptionEqualToValue:E,filterOptions:()=>d,renderOption:(t,n)=>e.createElement(r.Box,{component:"li",...t,key:t.id},Pt(l,n)?.label??n),renderInput:t=>e.createElement(It,{params:t,handleChange:e=>o?.(e),allowClear:p,placeholder:c,hasSelectedValue:v})})})),It=({params:t,allowClear:n,placeholder:l,handleChange:a,hasSelectedValue:o})=>e.createElement(r.TextField,{...t,placeholder:l,onChange:e=>{a(e.target.value)},sx:{"& .MuiInputBase-input":{cursor:o?"default":void 0}},InputProps:{...t.InputProps,endAdornment:e.createElement(kt,{params:t,allowClear:n,handleChange:a})}}),kt=({allowClear:t,handleChange:n,params:l})=>e.createElement(r.InputAdornment,{position:"end"},t&&e.createElement(r.IconButton,{size:l.size,onClick:()=>n(null),sx:{cursor:"pointer"}},e.createElement(s.XIcon,{fontSize:l.size})));function Pt(e,t=null){const n=(t||"").toString();return e.find((({id:e})=>n===e.toString()))}function Vt(e){return e.every((e=>"groupLabel"in e))}var $t=O((()=>{const{value:n,setValue:l,disabled:a}=P(t.booleanPropTypeUtil);return e.createElement("div",{style:{display:"flex",justifyContent:"flex-end"}},e.createElement(r.Switch,{checked:!!n,onChange:e=>{l(e.target.checked)},size:"small",disabled:a,inputProps:{...a?{style:{opacity:0}}:{}}}))})),Ut="tiny",Rt={label:(0,a.__)("Learn More","elementor"),href:"https://go.elementor.com/element-link-inside-link-infotip"},Gt=O((n=>{const{value:l,path:i,setValue:s,...u}=P(t.linkPropTypeUtil),[m,d]=(0,b.useSessionStorage)(i.join("/")),[p,E]=(0,e.useState)(!!l),{allowCustomValues:g,queryOptions:{endpoint:f="",requestParams:h={}},placeholder:y,minInputLength:x=2,context:{elementId:_},label:T=(0,a.__)("Link","elementor")}=n||{},[w,z]=(0,e.useState)((0,v.getLinkInLinkRestriction)(_)),[I,k]=(0,e.useState)(function(e){const t=e?.destination?.value,n=e?.label?.value;return t&&n&&"number"===(e?.destination?.$$type||"url")?[{id:t.toString(),label:n}]:[]}(l)),V=!p&&w.shouldRestrict,$=e=>{s(e),d({...m,value:e})},R=(0,e.useMemo)((()=>(0,o.debounce)((e=>async function(e,t){if(!t||!e)return[];try{const{data:n}=await(0,c.httpService)().get(e,{params:t});return n.data.value}catch{return[]}}(f,e).then((e=>{k(function(e){const t=Vt(e)?"groupLabel":"label";return e.sort(((e,n)=>e[t]&&n[t]?e[t].localeCompare(n[t]):0))}(e))}))),400)),[f]);return e.createElement(C,{...u,value:l,setValue:s},e.createElement(r.Stack,{gap:1.5},e.createElement(r.Stack,{direction:"row",sx:{justifyContent:"space-between",alignItems:"center",marginInlineEnd:-.75}},e.createElement(U,null,T),e.createElement(Ft,{isVisible:!p,linkInLinkRestriction:w},e.createElement(Lt,{disabled:V,active:p,onIconClick:()=>{if(z((0,v.getLinkInLinkRestriction)(_)),w.shouldRestrict&&!p)return;const e=!p;E(e),e||null===l||s(null),e&&m?.value&&s(m.value),d({value:m?.value,meta:{isEnabled:e}})},label:(0,a.__)("Toggle link","elementor")}))),e.createElement(r.Collapse,{in:p,timeout:"auto",unmountOnExit:!0},e.createElement(r.Stack,{gap:1.5},e.createElement(S,{bind:"destination"},e.createElement(q,null,e.createElement(zt,{options:I,allowCustomValues:g,placeholder:y,value:l?.destination?.value?.settings?.label||l?.destination?.value,onOptionChange:e=>{const n=e?{...l,destination:t.numberPropTypeUtil.create(e),label:t.stringPropTypeUtil.create(Pt(I,e)?.label||null)}:null;$(n)},onTextChange:e=>{const n=(e=e?.trim()||"")?{...l,destination:t.urlPropTypeUtil.create(e),label:t.stringPropTypeUtil.create("")}:null;$(n),(e=>{k([]),!e||!f||e.length<x||R({...h,term:e})})(e)},minInputLength:x}))),e.createElement(S,{bind:"isTargetBlank"},e.createElement(r.Grid,{container:!0,alignItems:"center",flexWrap:"nowrap",justifyContent:"space-between"},e.createElement(r.Grid,{item:!0},e.createElement(U,null,(0,a.__)("Open in a new tab","elementor"))),e.createElement(r.Grid,{item:!0,sx:{marginInlineEnd:-1}},e.createElement(Ot,{disabled:u.disabled||!l}))))))))})),Lt=({disabled:t,active:n,onIconClick:l,label:a})=>e.createElement(r.IconButton,{size:Ut,onClick:l,"aria-label":a,disabled:t},n?e.createElement(s.MinusIcon,{fontSize:Ut}):e.createElement(s.PlusIcon,{fontSize:Ut})),Ot=({disabled:n})=>{const{value:l,setValue:a}=P(t.booleanPropTypeUtil);return(0,p.isExperimentActive)("e_v_3_31")?e.createElement($t,null):e.createElement(r.Switch,{checked:l??!1,onClick:()=>{a(!l)},disabled:n,inputProps:{...n?{style:{opacity:0}}:{}}})},Ft=({linkInLinkRestriction:t,isVisible:n,children:l})=>{const{shouldRestrict:o,reason:i,elementId:c}=t;return o&&n?e.createElement(r.Infotip,{placement:"right",content:e.createElement(m.InfoTipCard,{content:Bt[i],svgIcon:e.createElement(s.AlertTriangleIcon,null),learnMoreButton:Rt,ctaButton:{label:(0,a.__)("Take me there","elementor"),onClick:()=>{c&&(0,v.selectElement)(c)}}})},e.createElement(r.Box,null,l)):e.createElement(e.Fragment,null,l)},Bt={descendant:e.createElement(e.Fragment,null,(0,a.__)("To add a link to this container,","elementor"),e.createElement("br",null),(0,a.__)("first remove the link from the elements inside of it.","elementor")),ancestor:e.createElement(e.Fragment,null,(0,a.__)("To add a link to this element,","elementor"),e.createElement("br",null),(0,a.__)("first remove the link from its parent container.","elementor"))},At=O((({label:n})=>{const{value:l,setValue:o,propType:i,disabled:c}=P(t.layoutDirectionPropTypeUtil),u=(0,e.useRef)(null),{value:m,setValue:d,disabled:p}=P(t.sizePropTypeUtil),E=!l&&!m||!!m,v=n.toLowerCase(),b=E?s.LinkIcon:s.DetachIcon,g=(0,a.__)("Link %s","elementor").replace("%s",v),f=(0,a.__)("Unlink %s","elementor").replace("%s",v),h=p||c;return e.createElement(C,{propType:i,value:l,setValue:o},e.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},e.createElement(Xe,null,n),e.createElement(r.Tooltip,{title:E?f:g,placement:"top"},e.createElement(r.ToggleButton,{"aria-label":E?f:g,size:"tiny",value:"check",selected:E,sx:{marginLeft:"auto"},onChange:()=>{if(!E)return void d(l?.column?.value??null);const e=m?t.sizePropTypeUtil.create(m):null;o({row:e,column:e})},disabled:h},e.createElement(b,{fontSize:"tiny"})))),e.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap",ref:u},e.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(U,null,(0,a.__)("Column","elementor"))),e.createElement(r.Grid,{item:!0,xs:12},e.createElement(Mt,{bind:"column",isLinked:E,anchorRef:u}))),e.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(U,null,(0,a.__)("Row","elementor"))),e.createElement(r.Grid,{item:!0,xs:12},e.createElement(Mt,{bind:"row",isLinked:E,anchorRef:u})))))})),Mt=({bind:t,isLinked:n,anchorRef:l})=>n?e.createElement(pe,{anchorRef:l}):e.createElement(S,{bind:t},e.createElement(pe,{anchorRef:l})),Wt=[{label:(0,a.__)("Auto","elementor"),value:"auto"},{label:"1/1",value:"1/1"},{label:"4/3",value:"4/3"},{label:"3/4",value:"3/4"},{label:"16/9",value:"16/9"},{label:"9/16",value:"9/16"},{label:"3/2",value:"3/2"},{label:"2/3",value:"2/3"}],Dt="custom",jt=O((({label:n})=>{const{value:l,setValue:o,disabled:i}=P(t.stringPropTypeUtil),c=l&&!Wt.some((e=>e.value===l)),[u,d]=c?l.split("/"):["",""],[p,E]=(0,e.useState)(c),[v,b]=(0,e.useState)(u),[g,f]=(0,e.useState)(d),[h,y]=(0,e.useState)(c?Dt:l||"");return(0,e.useEffect)((()=>{if(l&&!Wt.some((e=>e.value===l))){const[e,t]=l.split("/");b(e||""),f(t||""),y(Dt),E(!0)}else y(l||""),E(!1),b(""),f("")}),[l]),e.createElement(q,null,e.createElement(r.Stack,{direction:"column",gap:2},e.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(Xe,null,n)),e.createElement(r.Grid,{item:!0,xs:6},e.createElement(r.Select,{size:"tiny",displayEmpty:!0,sx:{overflow:"hidden"},disabled:i,value:h,onChange:e=>{const t=e.target.value,n=t===Dt;E(n),y(t),n||o(t)},fullWidth:!0},[...Wt,{label:(0,a.__)("Custom","elementor"),value:Dt}].map((({label:t,...n})=>e.createElement(m.MenuListItem,{key:n.value,...n,value:n.value??""},t)))))),p&&e.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(r.TextField,{size:"tiny",type:"number",fullWidth:!0,disabled:i,value:v,onChange:e=>{const t=e.target.value;b(t),t&&g&&o(`${t}/${g}`)},InputProps:{startAdornment:e.createElement(s.ArrowsMoveHorizontalIcon,{fontSize:"tiny"})}})),e.createElement(r.Grid,{item:!0,xs:6},e.createElement(r.TextField,{size:"tiny",type:"number",fullWidth:!0,disabled:i,value:g,onChange:e=>{const t=e.target.value;f(t),v&&t&&o(`${v}/${t}`)},InputProps:{startAdornment:e.createElement(s.ArrowsMoveVerticalIcon,{fontSize:"tiny"})}})))))})),Nt=(0,a.__)("Enable Unfiltered Uploads","elementor"),Kt=(0,a.__)("Before you enable unfiltered files upload, note that such files include a security risk. Elementor does run a process to remove possible malicious code, but there is still risk involved when using such files.","elementor"),Ht=(0,a.__)("Sorry, you can't upload that file yet","elementor"),qt=(0,a.__)("This is because this file type may pose a security risk. To upload them anyway, ask the site administrator to enable unfiltered file uploads.","elementor"),Xt=(0,a.__)("Failed to enable unfiltered files upload.","elementor"),Yt=(0,a.__)("You can try again, if the problem persists, please contact support.","elementor"),Qt=t=>{const{mutateAsync:n,isPending:l}=function(){const e=(0,i.useQueryClient)();return(0,i.useMutation)({mutationFn:({allowUnfilteredFilesUpload:e})=>{return t=A,n=e?"1":"0",(0,c.httpService)().put(`${F}/${t}`,{value:n});var t,n},onSuccess:()=>e.invalidateQueries(M)})}(),{canUser:r}=(0,g.useCurrentUserCapabilities)(),[a,o]=(0,e.useState)(!1),s=r("manage_options"),u={...t,isPending:l,handleEnable:async()=>{try{const e=await n({allowUnfilteredFilesUpload:!0});!1===e?.data?.success?o(!0):t.onClose(!0)}catch{o(!0)}},isError:a,onClose:e=>{t.onClose(e),setTimeout((()=>o(!1)),300)}};return s?e.createElement(Zt,{...u}):e.createElement(Jt,{...u})},Zt=({open:t,onClose:n,handleEnable:l,isPending:o,isError:i})=>e.createElement(r.Dialog,{open:t,maxWidth:"sm",onClose:()=>n(!1)},e.createElement(r.DialogHeader,{logo:!1},e.createElement(r.DialogTitle,null,Nt)),e.createElement(r.Divider,null),e.createElement(r.DialogContent,null,e.createElement(r.DialogContentText,null,i?e.createElement(e.Fragment,null,Xt," ",e.createElement("br",null)," ",Yt):Kt)),e.createElement(r.DialogActions,null,e.createElement(r.Button,{size:"medium",color:"secondary",onClick:()=>n(!1)},(0,a.__)("Cancel","elementor")),e.createElement(r.Button,{size:"medium",onClick:()=>l(),variant:"contained",color:"primary",disabled:o},o?e.createElement(r.CircularProgress,{size:24}):(0,a.__)("Enable","elementor")))),Jt=({open:t,onClose:n})=>e.createElement(r.Dialog,{open:t,maxWidth:"sm",onClose:()=>n(!1)},e.createElement(r.DialogHeader,{logo:!1},e.createElement(r.DialogTitle,null,Ht)),e.createElement(r.Divider,null),e.createElement(r.DialogContent,null,e.createElement(r.DialogContentText,null,qt)),e.createElement(r.DialogActions,null,e.createElement(r.Button,{size:"medium",onClick:()=>n(!1),variant:"contained",color:"primary"},(0,a.__)("Got it","elementor")))),en="transparent",tn="#c1c1c1",nn=`linear-gradient(45deg, ${tn} 25%, ${en} 0, ${en} 75%, ${tn} 0, ${tn})`,ln=(0,r.styled)(r.Card)`
	background-color: white;
	background-image: ${nn}, ${nn};
	background-size: ${8}px ${8}px;
	background-position:
		0 0,
		${4}px ${4}px;
	border: none;
`,rn=(0,r.styled)(r.Stack)`
	position: relative;
	height: 140px;
	object-fit: contain;
	padding: 5px;
	justify-content: center;
	align-items: center;
	background-color: rgba( 255, 255, 255, 0.37 );
`,an={mode:"browse"},on={mode:"upload"},cn=O((()=>{const{value:n,setValue:l}=P(t.imageSrcPropTypeUtil),{id:o,url:i}=n??{},{data:c,isFetching:m}=(0,u.useWpMediaAttachment)(o?.value||null),d=c?.url??i?.value??null,{data:p}=W(),[E,v]=(0,e.useState)(!1),{open:b}=(0,u.useWpMediaFrame)({mediaTypes:["svg"],multiple:!1,selected:o?.value||null,onSelect:e=>{l({id:{$$type:"image-attachment-id",value:e.id},url:null})}}),g=e=>{p||e!==on?b(e):v(!0)};return e.createElement(r.Stack,{gap:1},e.createElement(Qt,{open:E,onClose:e=>{v(!1),e&&b(on)}}),e.createElement(q,null,e.createElement(ln,{variant:"outlined"},e.createElement(rn,null,m?e.createElement(r.CircularProgress,{role:"progressbar"}):e.createElement(r.CardMedia,{component:"img",image:d,alt:(0,a.__)("Preview SVG","elementor"),sx:{maxHeight:"140px",width:"50px"}})),e.createElement(r.CardOverlay,{sx:{"&:hover":{backgroundColor:"rgba( 0, 0, 0, 0.75 )"}}},e.createElement(r.Stack,{gap:1},e.createElement(r.Button,{size:"tiny",color:"inherit",variant:"outlined",onClick:()=>g(an)},(0,a.__)("Select SVG","elementor")),e.createElement(r.Button,{size:"tiny",variant:"text",color:"inherit",startIcon:e.createElement(s.UploadIcon,null),onClick:()=>g(on)},(0,a.__)("Upload","elementor")))))))})),{env:sn}=(0,f.parseEnv)("@elementor/editor-controls"),un=O((()=>{const{value:n,setValue:l}=P(t.backgroundGradientOverlayPropTypeUtil);return e.createElement(q,null,e.createElement(r.UnstableGradientBox,{sx:{width:"auto",padding:1.5},value:(()=>{if(!n)return;const{type:e,angle:t,stops:l,positions:r}=n;return{type:e.value,angle:t.value,stops:l.value.map((({value:{color:e,offset:t}})=>({color:e.value,offset:t.value}))),positions:r?.value.split(" ")}})(),onChange:e=>{const n=(e=>({...e,type:t.stringPropTypeUtil.create(e.type),angle:t.numberPropTypeUtil.create(e.angle),stops:t.gradientColorStopPropTypeUtil.create(e.stops.map((({color:e,offset:n})=>t.colorStopPropTypeUtil.create({color:t.colorPropTypeUtil.create(e),offset:t.numberPropTypeUtil.create(n)}))))}))(e);n.positions&&(n.positions=t.stringPropTypeUtil.create(e.positions.join(" "))),l(n)}}))})),mn=t.backgroundGradientOverlayPropTypeUtil.create({type:t.stringPropTypeUtil.create("linear"),angle:t.numberPropTypeUtil.create(180),stops:t.gradientColorStopPropTypeUtil.create([t.colorStopPropTypeUtil.create({color:t.colorPropTypeUtil.create("rgb(0,0,0)"),offset:t.numberPropTypeUtil.create(0)}),t.colorStopPropTypeUtil.create({color:t.colorPropTypeUtil.create("rgb(255,255,255)"),offset:t.numberPropTypeUtil.create(100)})])}),dn=[{value:"fixed",label:(0,a.__)("Fixed","elementor"),renderContent:({size:t})=>e.createElement(s.PinIcon,{fontSize:t}),showTooltip:!0},{value:"scroll",label:(0,a.__)("Scroll","elementor"),renderContent:({size:t})=>e.createElement(s.PinnedOffIcon,{fontSize:t}),showTooltip:!0}],pn=()=>e.createElement(Te,null,e.createElement(r.Grid,{item:!0,xs:6},e.createElement(U,null,(0,a.__)("Attachment","elementor"))),e.createElement(r.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end",overflow:"hidden"}},e.createElement(dt,{options:dn}))),En=[{label:(0,a.__)("Center center","elementor"),value:"center center"},{label:(0,a.__)("Center left","elementor"),value:"center left"},{label:(0,a.__)("Center right","elementor"),value:"center right"},{label:(0,a.__)("Top center","elementor"),value:"top center"},{label:(0,a.__)("Top left","elementor"),value:"top left"},{label:(0,a.__)("Top right","elementor"),value:"top right"},{label:(0,a.__)("Bottom center","elementor"),value:"bottom center"},{label:(0,a.__)("Bottom left","elementor"),value:"bottom left"},{label:(0,a.__)("Bottom right","elementor"),value:"bottom right"},{label:(0,a.__)("Custom","elementor"),value:"custom"}],vn=()=>{const n=P(t.backgroundImagePositionOffsetPropTypeUtil),l=P(t.stringPropTypeUtil),o=!!n.value,i=(0,e.useRef)(null);return e.createElement(r.Grid,{container:!0,spacing:1.5},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(Te,null,e.createElement(r.Grid,{item:!0,xs:6},e.createElement(U,null,(0,a.__)("Position","elementor"))),e.createElement(r.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end",overflow:"hidden"}},e.createElement(r.Select,{fullWidth:!0,size:"tiny",onChange:e=>{const t=e.target.value||null;"custom"===t?n.setValue({x:null,y:null}):l.setValue(t)},disabled:l.disabled,value:(n.value?"custom":l.value)??""},En.map((({label:t,value:n})=>e.createElement(m.MenuListItem,{key:n,value:n??""},t))))))),o?e.createElement(C,{...n},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(r.Grid,{container:!0,spacing:1.5,ref:i},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(S,{bind:"x"},e.createElement(pe,{startIcon:e.createElement(s.LetterXIcon,{fontSize:"tiny"}),anchorRef:i}))),e.createElement(r.Grid,{item:!0,xs:6},e.createElement(S,{bind:"y"},e.createElement(pe,{startIcon:e.createElement(s.LetterYIcon,{fontSize:"tiny"}),anchorRef:i})))))):null)},bn=[{value:"repeat",label:(0,a.__)("Repeat","elementor"),renderContent:({size:t})=>e.createElement(s.GridDotsIcon,{fontSize:t}),showTooltip:!0},{value:"repeat-x",label:(0,a.__)("Repeat-x","elementor"),renderContent:({size:t})=>e.createElement(s.DotsHorizontalIcon,{fontSize:t}),showTooltip:!0},{value:"repeat-y",label:(0,a.__)("Repeat-y","elementor"),renderContent:({size:t})=>e.createElement(s.DotsVerticalIcon,{fontSize:t}),showTooltip:!0},{value:"no-repeat",label:(0,a.__)("No-repeat","elementor"),renderContent:({size:t})=>e.createElement(s.XIcon,{fontSize:t}),showTooltip:!0}],gn=()=>e.createElement(Te,null,e.createElement(r.Grid,{item:!0,xs:6},e.createElement(U,null,(0,a.__)("Repeat","elementor"))),e.createElement(r.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end"}},e.createElement(dt,{options:bn}))),fn=[{value:"auto",label:(0,a.__)("Auto","elementor"),renderContent:({size:t})=>e.createElement(s.LetterAIcon,{fontSize:t}),showTooltip:!0},{value:"cover",label:(0,a.__)("Cover","elementor"),renderContent:({size:t})=>e.createElement(s.ArrowsMaximizeIcon,{fontSize:t}),showTooltip:!0},{value:"contain",label:(0,a.__)("Contain","elementor"),renderContent:({size:t})=>e.createElement(s.ArrowBarBothIcon,{fontSize:t}),showTooltip:!0},{value:"custom",label:(0,a.__)("Custom","elementor"),renderContent:({size:t})=>e.createElement(s.PencilIcon,{fontSize:t}),showTooltip:!0}],hn=()=>{const n=P(t.backgroundImageSizeScalePropTypeUtil),l=P(t.stringPropTypeUtil),o=!!n.value,i=(0,e.useRef)(null);return e.createElement(r.Grid,{container:!0,spacing:1.5},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(Te,null,e.createElement(r.Grid,{item:!0,xs:6},e.createElement(U,null,(0,a.__)("Size","elementor"))),e.createElement(r.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end"}},e.createElement(st,{exclusive:!0,items:fn,onChange:e=>{"custom"===e?n.setValue({width:null,height:null}):l.setValue(e)},disabled:l.disabled,value:n.value?"custom":l.value})))),o?e.createElement(C,{...n},e.createElement(r.Grid,{item:!0,xs:12,ref:i},e.createElement(Te,null,e.createElement(r.Grid,{item:!0,xs:6},e.createElement(S,{bind:"width"},e.createElement(pe,{startIcon:e.createElement(s.ArrowsMoveHorizontalIcon,{fontSize:"tiny"}),extendedOptions:["auto"],anchorRef:i}))),e.createElement(r.Grid,{item:!0,xs:6},e.createElement(S,{bind:"height"},e.createElement(pe,{startIcon:e.createElement(s.ArrowsMoveVerticalIcon,{fontSize:"tiny"}),extendedOptions:["auto"],anchorRef:i})))))):null)},yn=t.backgroundColorOverlayPropTypeUtil.create({color:t.colorPropTypeUtil.create("#00000033")}),xn=()=>({$$type:"background-image-overlay",value:{image:{$$type:"image",value:{src:{$$type:"image-src",value:{url:{$$type:"url",value:sn.background_placeholder_image},id:null}},size:{$$type:"string",value:"large"}}}}}),_n=[{label:(0,a.__)("Thumbnail - 150 x 150","elementor"),value:"thumbnail"},{label:(0,a.__)("Medium - 300 x 300","elementor"),value:"medium"},{label:(0,a.__)("Large 1024 x 1024","elementor"),value:"large"},{label:(0,a.__)("Full","elementor"),value:"full"}],Cn=O((()=>{const{propType:n,value:l,setValue:r,disabled:o}=P(t.backgroundOverlayPropTypeUtil);return e.createElement(C,{propType:n,value:l,setValue:r,isDisabled:()=>o},e.createElement(Be,{openOnAdd:!0,disabled:o,values:l??[],setValues:r,label:(0,a.__)("Overlay","elementor"),itemSettings:{Icon:Sn,Label:Vn,Content:Tn,initialValues:xn()}}))})),Tn=({anchorEl:t=null,bind:n})=>e.createElement(S,{bind:n},e.createElement(wn,{anchorEl:t})),wn=({anchorEl:n})=>{const{getTabsProps:l,getTabProps:o,getTabPanelProps:i}=(({color:n,image:l,gradient:a})=>{const{value:o,setValue:i}=P(t.backgroundImageOverlayPropTypeUtil),{value:c,setValue:s}=P(t.backgroundColorOverlayPropTypeUtil),{value:u,setValue:m}=P(t.backgroundGradientOverlayPropTypeUtil),{getTabsProps:d,getTabProps:p,getTabPanelProps:E}=(0,r.useTabs)(c?"color":u?"gradient":"image"),v=(0,e.useRef)({image:l,color:n,gradient:a}),b=(e,t)=>{t&&(v.current[e]=t)},g=(e,t)=>{switch(t){case"image":i(v.current.image),b("color",c),b("gradient",u);break;case"gradient":m(v.current.gradient),b("color",c),b("image",o);break;case"color":s(v.current.color),b("image",o),b("gradient",u)}return d().onChange(e,t)};return{getTabProps:p,getTabPanelProps:E,getTabsProps:()=>({...d(),onChange:g})}})({image:xn().value,color:yn.value,gradient:mn.value});return e.createElement(r.Box,{sx:{width:"100%"}},e.createElement(r.Box,{sx:{borderBottom:1,borderColor:"divider"}},e.createElement(r.Tabs,{size:"small",variant:"fullWidth",...l(),"aria-label":(0,a.__)("Background Overlay","elementor")},e.createElement(r.Tab,{label:(0,a.__)("Image","elementor"),...o("image")}),e.createElement(r.Tab,{label:(0,a.__)("Gradient","elementor"),...o("gradient")}),e.createElement(r.Tab,{label:(0,a.__)("Color","elementor"),...o("color")}))),e.createElement(r.TabPanel,{sx:{p:1.5},...i("image")},e.createElement(Ce,null,e.createElement(Ln,null))),e.createElement(r.TabPanel,{sx:{p:1.5},...i("gradient")},e.createElement(un,null)),e.createElement(r.TabPanel,{sx:{p:1.5},...i("color")},e.createElement(Ce,null,e.createElement(Gn,{anchorEl:n}))))},Sn=({value:t})=>{switch(t.$$type){case"background-image-overlay":return e.createElement(kn,{value:t});case"background-color-overlay":return e.createElement(In,{value:t});case"background-gradient-overlay":return e.createElement(Pn,{value:t});default:return null}},zn=e=>e?.value?.color?.value?e.value.color.value:"",In=({value:t})=>{const n=zn(t);return e.createElement(On,{size:"inherit",component:"span",value:n})},kn=({value:t})=>{const{imageUrl:n}=Fn(t);return e.createElement(r.CardMedia,{image:n,sx:e=>({height:"1em",width:"1em",borderRadius:e.shape.borderRadius/2+"px",outline:`1px solid ${e.palette.action.disabled}`})})},Pn=({value:t})=>{const n=An(t);return e.createElement(On,{size:"inherit",component:"span",value:n})},Vn=({value:t})=>{switch(t.$$type){case"background-image-overlay":return e.createElement(Un,{value:t});case"background-color-overlay":return e.createElement($n,{value:t});case"background-gradient-overlay":return e.createElement(Rn,{value:t});default:return null}},$n=({value:t})=>{const n=zn(t);return e.createElement("span",null,n)},Un=({value:t})=>{const{imageTitle:n}=Fn(t);return e.createElement("span",null,n)},Rn=({value:t})=>"linear"===t.value.type.value?e.createElement("span",null,(0,a.__)("Linear Gradient","elementor")):e.createElement("span",null,(0,a.__)("Radial Gradient","elementor")),Gn=({anchorEl:n})=>{const l=P(t.backgroundColorOverlayPropTypeUtil);return e.createElement(C,{...l},e.createElement(S,{bind:"color"},e.createElement(he,{anchorEl:n})))},Ln=()=>{const n=P(t.backgroundImageOverlayPropTypeUtil);return e.createElement(C,{...n},e.createElement(S,{bind:"image"},e.createElement(Q,{sizes:_n})),e.createElement(S,{bind:"position"},e.createElement(vn,null)),e.createElement(S,{bind:"repeat"},e.createElement(gn,null)),e.createElement(S,{bind:"size"},e.createElement(hn,null)),e.createElement(S,{bind:"attachment"},e.createElement(pn,null)))},On=(0,r.styled)(r.UnstableColorIndicator)((({theme:e})=>({borderRadius:e.shape.borderRadius/2+"px"}))),Fn=e=>{let t,n=null;const l=e?.value.image.value?.src.value,{data:r}=(0,u.useWpMediaAttachment)(l.id?.value||null);if(l.id){const e=Bn(r?.filename);t=`${r?.title}${e}`||null,n=r?.url||null}else l.url&&(n=l.url.value,t=n?.substring(n.lastIndexOf("/")+1)||null);return{imageTitle:t,imageUrl:n}},Bn=e=>e?`.${e.substring(e.lastIndexOf(".")+1)}`:"",An=e=>{const t=e.value,n=t.stops.value?.map((({value:{color:e,offset:t}})=>`${e.value} ${t.value??0}%`))?.join(",");return"linear"===t.type.value?`linear-gradient(${t.angle.value}deg, ${n})`:`radial-gradient(circle at ${t.positions.value}, ${n})`},Mn=O((()=>{const n=P(t.backgroundPropTypeUtil),l=(0,p.isExperimentActive)("e_v_3_30"),o=(0,a.__)("Color","elementor");return e.createElement(C,{...n},e.createElement(S,{bind:"background-overlay"},e.createElement(Cn,null)),e.createElement(S,{bind:"color"},e.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},e.createElement(r.Grid,{item:!0,xs:6},l?e.createElement(Xe,null,o):e.createElement(U,null,o)),e.createElement(r.Grid,{item:!0,xs:6},e.createElement(he,null)))))})),Wn=(0,e.createContext)(void 0),Dn=()=>{const t=(0,e.useContext)(Wn);if(!t)throw new Error("useRepeatableControlContext must be used within RepeatableControl");return t},jn=O((({repeaterLabel:n,childControlConfig:l,showDuplicate:r,showToggle:a,initialValues:o,patternLabel:i,placeholder:c})=>{const{propTypeUtil:s}=l;if(!s)return null;const u=(0,e.useMemo)((()=>(0,t.createArrayPropUtils)(s.key,s.schema)),[s.key,s.schema]),m=(0,e.useMemo)((()=>({...l,placeholder:c||"",patternLabel:i||""})),[l,c,i]),{propType:d,value:p,setValue:E}=P(u);return e.createElement(C,{propType:d,value:p,setValue:E},e.createElement(Wn.Provider,{value:m},e.createElement(Be,{openOnAdd:!0,values:p??[],setValues:E,label:n,isSortable:!1,itemSettings:{Icon:Kn,Label:Xn,Content:Nn,initialValues:s.create(o||null)},showDuplicate:r,showToggle:a})))})),Nn=({bind:t})=>e.createElement(S,{bind:t},e.createElement(Hn,null)),Kn=()=>e.createElement(e.Fragment,null),Hn=()=>{const{component:t,props:n={}}=Dn();return e.createElement(Ce,{p:1.5},e.createElement(Te,null,e.createElement(t,{...n})))},qn=e=>"string"==typeof e?""===e.trim():!!Number.isNaN(e)||(Array.isArray(e)?0===e.length:"object"==typeof e&&null!==e&&0===Object.keys(e).length),Xn=({value:t})=>{const{placeholder:n,patternLabel:l}=Dn(),a=((e,t)=>{const n=Yn(e).map((e=>((e,t)=>t.split(".").reduce(((e,t)=>e?.[t]),e))(t,e)));return!(0===n.length||!n.some((e=>null==e))&&!n.every(qn))})(l,t)?n:(o=l,(i=t)?new Function(...Object.keys(i),`return \`${o}\`;`)(...Object.values(i)):o);var o,i;return e.createElement(r.Box,{component:"span",color:"text.tertiary"},a)},Yn=e=>e.match(/\$\{([^}]+)\}/g)?.map((e=>e.slice(2,-1)))||[],Qn=O(((n={})=>{const{value:l,setValue:o}=P(t.keyValuePropTypeUtil),[i,c]=(0,e.useState)(null),[s,u]=(0,e.useState)(null),[m,d]=(0,e.useState)({key:l?.key?.value||"",value:l?.value?.value||""}),p=n.keyName||(0,a.__)("Key","elementor"),E=n.valueName||(0,a.__)("Value","elementor"),[v,b,g]=(0,e.useMemo)((()=>[n.regexKey?new RegExp(n.regexKey):void 0,n.regexValue?new RegExp(n.regexValue):void 0,n.validationErrorMessage||(0,a.__)("Invalid Format","elementor")]),[n.regexKey,n.regexValue,n.validationErrorMessage]),f=(e,t)=>{const n=e.target.value;d((e=>({...e,[t]:n}))),((e,t)=>{if("key"===t&&v){const t=v.test(e);return c(t?null:g),t}if("value"===t&&b){const t=b.test(e);return u(t?null:g),t}return!0})(n,t)?o({...l,[t]:{value:n,$$type:"string"}}):o({...l,[t]:{value:"",$$type:"string"}})},h=null!==i,y=null!==s;return e.createElement(q,null,e.createElement(r.Grid,{container:!0,gap:1.5},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(r.FormLabel,{size:"tiny"},p),e.createElement(r.TextField,{autoFocus:!0,sx:{pt:1},size:"tiny",fullWidth:!0,value:m.key,onChange:e=>f(e,"key"),error:h}),h&&e.createElement(r.FormHelperText,{error:!0},i)),e.createElement(r.Grid,{item:!0,xs:12},e.createElement(r.FormLabel,{size:"tiny"},E),e.createElement(r.TextField,{sx:{pt:1},size:"tiny",fullWidth:!0,value:m.value,onChange:e=>f(e,"value"),disabled:h,error:y}),y&&e.createElement(r.FormHelperText,{error:!0},s))))})),Zn=[{label:(0,a.__)("Center center","elementor"),value:"center center"},{label:(0,a.__)("Center left","elementor"),value:"center left"},{label:(0,a.__)("Center right","elementor"),value:"center right"},{label:(0,a.__)("Top center","elementor"),value:"top center"},{label:(0,a.__)("Top left","elementor"),value:"top left"},{label:(0,a.__)("Top right","elementor"),value:"top right"},{label:(0,a.__)("Bottom center","elementor"),value:"bottom center"},{label:(0,a.__)("Bottom left","elementor"),value:"bottom left"},{label:(0,a.__)("Bottom right","elementor"),value:"bottom right"}],Jn=()=>{const n=P(t.positionPropTypeUtil),l=P(t.stringPropTypeUtil),o=(0,p.isExperimentActive)("e_v_3_31"),i=!!n.value&&o,c=(0,e.useMemo)((()=>{const e=[...Zn];return o&&e.push({label:(0,a.__)("Custom","elementor"),value:"custom"}),e}),[o]);return e.createElement(r.Grid,{container:!0,spacing:1.5},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(U,null,(0,a.__)("Object position","elementor"))),e.createElement(r.Grid,{item:!0,xs:6,sx:{overflow:"hidden"}},e.createElement(r.Select,{size:"tiny",disabled:l.disabled,value:(n.value?"custom":l.value)??"",onChange:e=>{const t=e.target.value||null;"custom"===t&&o?n.setValue({x:null,y:null}):l.setValue(t)},fullWidth:!0},c.map((({label:t,value:n})=>e.createElement(m.MenuListItem,{key:n,value:n??""},t))))))),i&&e.createElement(C,{...n},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(r.Grid,{container:!0,spacing:1.5},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(S,{bind:"x"},e.createElement(pe,{startIcon:e.createElement(s.LetterXIcon,{fontSize:"tiny"})}))),e.createElement(r.Grid,{item:!0,xs:6},e.createElement(S,{bind:"y"},e.createElement(pe,{startIcon:e.createElement(s.LetterYIcon,{fontSize:"tiny"})})))))))},el=({label:t,bindValue:n,startIcon:l,anchorRef:a})=>e.createElement(r.Grid,{item:!0,xs:12},e.createElement(Te,{ref:a},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(Xe,null,t)),e.createElement(r.Grid,{item:!0,xs:6},e.createElement(S,{bind:n},e.createElement(pe,{anchorRef:a,startIcon:l}))))),tl=[{label:(0,a.__)("Move X","elementor"),bindValue:"x",startIcon:e.createElement(s.ArrowRightIcon,{fontSize:"tiny"})},{label:(0,a.__)("Move Y","elementor"),bindValue:"y",startIcon:e.createElement(s.ArrowDownSmallIcon,{fontSize:"tiny"})},{label:(0,a.__)("Move Z","elementor"),bindValue:"z",startIcon:e.createElement(s.ArrowDownLeftIcon,{fontSize:"tiny"})}],nl=()=>{const n=P(t.moveTransformPropTypeUtil),l=(0,e.useRef)(null);return e.createElement(r.Grid,{container:!0,spacing:1.5},e.createElement(C,{...n},e.createElement(S,{bind:"transform-move"},tl.map((t=>e.createElement(el,{key:t.bindValue,...t,anchorRef:l}))))))},ll=({bind:t})=>{const{getTabsProps:n,getTabProps:l,getTabPanelProps:o}=(0,r.useTabs)("transform-move");return e.createElement(S,{bind:t},e.createElement(Ce,null,e.createElement(r.Box,{sx:{width:"100%"}},e.createElement(r.Box,{sx:{borderBottom:1,borderColor:"divider"}},e.createElement(r.Tabs,{size:"small",variant:"fullWidth",...n(),"aria-label":(0,a.__)("Transform","elementor")},e.createElement(r.Tab,{label:(0,a.__)("Move","elementor"),...l("transform-move")}))),e.createElement(r.TabPanel,{sx:{p:1.5},...o("transform-move")},e.createElement(nl,null)))))},rl=({value:t})=>"transform-move"===t.$$type?e.createElement(s.ArrowsMaximizeIcon,{fontSize:"tiny"}):null,al=e=>Object.values(e).map((e=>`${e?.value.size}${e?.value.unit}`)).join(", "),ol=t=>{const{$$type:n,value:l}=t.value;return"transform-move"===n?e.createElement(il,{label:(0,a.__)("Move","elementor"),value:al(l)}):""},il=({label:t,value:n})=>e.createElement(r.Box,{component:"span"},t,": ",n),cl={$$type:"transform-move",value:{x:{$$type:"size",value:{size:0,unit:"px"}},y:{$$type:"size",value:{size:0,unit:"px"}},z:{$$type:"size",value:{size:0,unit:"px"}}}},sl=O((()=>{const{propType:n,value:l,setValue:r,disabled:o}=P(t.transformPropTypeUtil);return e.createElement(C,{propType:n,value:l,setValue:r},e.createElement(Be,{openOnAdd:!0,disabled:o,values:l??[],setValues:r,label:(0,a.__)("Transform","elementor"),showDuplicate:!1,itemSettings:{Icon:rl,Label:ol,Content:ll,initialValues:cl}}))}))}(),(window.elementorV2=window.elementorV2||{}).editorControls=l}(),window.elementorV2.editorControls?.init?.();