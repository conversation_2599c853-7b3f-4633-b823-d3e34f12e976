!function(){var e={184:function(e,t){var n;
/*!
  Copyright (c) 2017 <PERSON>.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var i=typeof n;if("string"===i||"number"===i)e.push(n);else if(Array.isArray(n)&&n.length){var a=o.apply(null,n);a&&e.push(a)}else if("object"===i)for(var s in n)r.call(n,s)&&n[s]&&e.push(s)}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()},703:function(e,t,n){"use strict";var r=n(414);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,i,a){if(a!==r){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var n={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return n.PropTypes=n,n}},697:function(e,t,n){e.exports=n(703)()},414:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},921:function(e,t){"use strict";var n="function"==typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,i=n?Symbol.for("react.fragment"):60107,a=n?Symbol.for("react.strict_mode"):60108,s=n?Symbol.for("react.profiler"):60114,l=n?Symbol.for("react.provider"):60109,c=n?Symbol.for("react.context"):60110,u=n?Symbol.for("react.async_mode"):60111,p=n?Symbol.for("react.concurrent_mode"):60111,d=n?Symbol.for("react.forward_ref"):60112,f=n?Symbol.for("react.suspense"):60113,h=n?Symbol.for("react.suspense_list"):60120,m=n?Symbol.for("react.memo"):60115,g=n?Symbol.for("react.lazy"):60116,y=n?Symbol.for("react.block"):60121,v=n?Symbol.for("react.fundamental"):60117,b=n?Symbol.for("react.responder"):60118,w=n?Symbol.for("react.scope"):60119;
/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function _(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case u:case p:case i:case s:case a:case f:return e;default:switch(e=e&&e.$$typeof){case c:case d:case g:case m:case l:return e;default:return t}}case o:return t}}}function I(e){return _(e)===p}},864:function(e,t,n){"use strict";n(921)},61:function(e,t,n){var r=n(698).default;function o(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e.exports=o=function(){return n},e.exports.__esModule=!0,e.exports.default=e.exports;var t,n={},i=Object.prototype,a=i.hasOwnProperty,s=Object.defineProperty||function(e,t,n){e[t]=n.value},l="function"==typeof Symbol?Symbol:{},c=l.iterator||"@@iterator",u=l.asyncIterator||"@@asyncIterator",p=l.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(t){d=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var o=t&&t.prototype instanceof w?t:w,i=Object.create(o.prototype),a=new H(r||[]);return s(i,"_invoke",{value:C(e,n,a)}),i}function h(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=f;var m="suspendedStart",g="suspendedYield",y="executing",v="completed",b={};function w(){}function _(){}function I(){}var S={};d(S,c,(function(){return this}));var x=Object.getPrototypeOf,E=x&&x(x(j([])));E&&E!==i&&a.call(E,c)&&(S=E);var O=I.prototype=w.prototype=Object.create(S);function k(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function P(e,t){function n(o,i,s,l){var c=h(e[o],e,i);if("throw"!==c.type){var u=c.arg,p=u.value;return p&&"object"==r(p)&&a.call(p,"__await")?t.resolve(p.__await).then((function(e){n("next",e,s,l)}),(function(e){n("throw",e,s,l)})):t.resolve(p).then((function(e){u.value=e,s(u)}),(function(e){return n("throw",e,s,l)}))}l(c.arg)}var o;s(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,o){n(e,r,t,o)}))}return o=o?o.then(i,i):i()}})}function C(e,n,r){var o=m;return function(i,a){if(o===y)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var s=r.delegate;if(s){var l=D(s,r);if(l){if(l===b)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===m)throw o=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=y;var c=h(e,n,r);if("normal"===c.type){if(o=r.done?v:g,c.arg===b)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(o=v,r.method="throw",r.arg=c.arg)}}}function D(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,D(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),b;var i=h(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,b;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,b):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,b)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function M(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function H(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function j(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(a.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(r(e)+" is not iterable")}return _.prototype=I,s(O,"constructor",{value:I,configurable:!0}),s(I,"constructor",{value:_,configurable:!0}),_.displayName=d(I,p,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,I):(e.__proto__=I,d(e,p,"GeneratorFunction")),e.prototype=Object.create(O),e},n.awrap=function(e){return{__await:e}},k(P.prototype),d(P.prototype,u,(function(){return this})),n.AsyncIterator=P,n.async=function(e,t,r,o,i){void 0===i&&(i=Promise);var a=new P(f(e,t,r,o),i);return n.isGeneratorFunction(t)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(O),d(O,p,"Generator"),d(O,c,(function(){return this})),d(O,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},n.values=j,H.prototype={constructor:H,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(M),!e)for(var n in this)"t"===n.charAt(0)&&a.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(r,o){return s.type="throw",s.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var l=a.call(i,"catchLoc"),c=a.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,b):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),b},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),M(n),b}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;M(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:j(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),b}},n}e.exports=o,e.exports.__esModule=!0,e.exports.default=e.exports},698:function(e){function t(n){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},687:function(e,t,n){var r=n(61)();e.exports=r;try{regeneratorRuntime=r}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,n||"default");if("object"!=e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}function r(e,n,r){return(n=t(n))in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}var o=window.wp.element,i=window.wp.hooks;function a(e,t,n,r,o,i,a){try{var s=e[i](a),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,o)}function s(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function s(e){a(i,r,o,s,l,"next",e)}function l(e){a(i,r,o,s,l,"throw",e)}s(void 0)}))}}var l=n(687),c=n.n(l),u=window.wp.i18n,p=n(184),d=n.n(p);function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function h(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,s=[],l=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return f(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function m(){return m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},m.apply(null,arguments)}function g(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}function y(){return y=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},y.apply(this,arguments)}function v(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}var b=n(697),w=n.n(b),_=window.React;n(864);function I(e){return null!=e&&"object"==typeof e&&1===e.nodeType}function S(e,t){return(!t||"hidden"!==e)&&("visible"!==e&&"clip"!==e)}function x(e,t){if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){var n=getComputedStyle(e,null);return S(n.overflowY,t)||S(n.overflowX,t)||function(e){var t=function(e){return e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView.frameElement:null}(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)}(e)}return!1}function E(e,t,n,r,o,i,a,s){return i<e&&a>t||i>e&&a<t?0:i<=e&&s<=n||a>=t&&s>=n?i-e-r:a>t&&s<n||i<e&&s>n?a-t+o:0}var O=0;function k(e){return"function"==typeof e?e:P}function P(){}function C(e,t){if(null!==e){var n=function(e,t){var n=t.scrollMode,r=t.block,o=t.inline,i=t.boundary,a=t.skipOverflowHiddenElements,s="function"==typeof i?i:function(e){return e!==i};if(!I(e))throw new TypeError("Invalid target");for(var l=document.scrollingElement||document.documentElement,c=[],u=e;I(u)&&s(u);){if((u=u.parentNode)===l){c.push(u);break}u===document.body&&x(u)&&!x(document.documentElement)||x(u,a)&&c.push(u)}for(var p=window.visualViewport?visualViewport.width:innerWidth,d=window.visualViewport?visualViewport.height:innerHeight,f=window.scrollX||pageXOffset,h=window.scrollY||pageYOffset,m=e.getBoundingClientRect(),g=m.height,y=m.width,v=m.top,b=m.right,w=m.bottom,_=m.left,S="start"===r||"nearest"===r?v:"end"===r?w:v+g/2,O="center"===o?_+y/2:"end"===o?b:_,k=[],P=0;P<c.length;P++){var C=c[P],D=C.getBoundingClientRect(),L=D.height,M=D.width,H=D.top,j=D.right,T=D.bottom,A=D.left;if("if-needed"===n&&v>=0&&_>=0&&w<=d&&b<=p&&v>=H&&w<=T&&_>=A&&b<=j)return k;var N=getComputedStyle(C),R=parseInt(N.borderLeftWidth,10),V=parseInt(N.borderTopWidth,10),K=parseInt(N.borderRightWidth,10),z=parseInt(N.borderBottomWidth,10),B=0,F=0,U="offsetWidth"in C?C.offsetWidth-C.clientWidth-R-K:0,W="offsetHeight"in C?C.offsetHeight-C.clientHeight-V-z:0;if(l===C)B="start"===r?S:"end"===r?S-d:"nearest"===r?E(h,h+d,d,V,z,h+S,h+S+g,g):S-d/2,F="start"===o?O:"center"===o?O-p/2:"end"===o?O-p:E(f,f+p,p,R,K,f+O,f+O+y,y),B=Math.max(0,B+h),F=Math.max(0,F+f);else{B="start"===r?S-H-V:"end"===r?S-T+z+W:"nearest"===r?E(H,T,L,V,z+W,S,S+g,g):S-(H+L/2)+W/2,F="start"===o?O-A-R:"center"===o?O-(A+M/2)+U/2:"end"===o?O-j+K+U:E(A,j,M,R,K+U,O,O+y,y);var G=C.scrollLeft,q=C.scrollTop;S+=q-(B=Math.max(0,Math.min(q+B,C.scrollHeight-L+W))),O+=G-(F=Math.max(0,Math.min(G+F,C.scrollWidth-M+U)))}k.push({el:C,top:B,left:F})}return k}(e,{boundary:t,block:"nearest",scrollMode:"if-needed"});n.forEach((function(e){var t=e.el,n=e.top,r=e.left;t.scrollTop=n,t.scrollLeft=r}))}}function D(e,t){return e===t||e.contains&&e.contains(t)}function L(e,t){var n;function r(){n&&clearTimeout(n)}function o(){for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];r(),n=setTimeout((function(){n=null,e.apply(void 0,i)}),t)}return o.cancel=r,o}function M(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return t.some((function(t){return t&&t.apply(void 0,[e].concat(r)),e.preventDownshiftDefault||e.hasOwnProperty("nativeEvent")&&e.nativeEvent.preventDownshiftDefault}))}}function H(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){t.forEach((function(t){"function"==typeof t?t(e):t&&(t.current=e)}))}}function j(e){var t=e.isOpen,n=e.selectedItem,r=e.resultCount,o=e.previousResultCount,i=e.itemToString;return t?r?r!==o?r+" result"+(1===r?" is":"s are")+" available, use up and down arrow keys to navigate. Press Enter key to select.":"":"No results are available.":n?i(n):""}function T(e,t){return!(e=Array.isArray(e)?e[0]:e)&&t?t:e}function A(e){return"string"==typeof e.type}function N(e){return e.props}var R=["highlightedIndex","inputValue","isOpen","selectedItem","type"];function V(e){void 0===e&&(e={});var t={};return R.forEach((function(n){e.hasOwnProperty(n)&&(t[n]=e[n])})),t}function K(e){var t=e.key,n=e.keyCode;return n>=37&&n<=40&&0!==t.indexOf("Arrow")?"Arrow"+t:t}function z(e,t,n){var r=n-1;("number"!=typeof t||t<0||t>=n)&&(t=e>0?-1:r+1);var o=t+e;return o<0?o=r:o>r&&(o=0),o}var B=L((function(){U().textContent=""}),500);function F(e,t){var n=U(t);e&&(n.textContent=e,B())}function U(e){void 0===e&&(e=document);var t=e.getElementById("a11y-status-message");return t||((t=e.createElement("div")).setAttribute("id","a11y-status-message"),t.setAttribute("role","status"),t.setAttribute("aria-live","polite"),t.setAttribute("aria-relevant","additions text"),Object.assign(t.style,{border:"0",clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:"0",position:"absolute",width:"1px"}),e.body.appendChild(t),t)}var W=Object.freeze({__proto__:null,unknown:0,mouseUp:1,itemMouseEnter:2,keyDownArrowUp:3,keyDownArrowDown:4,keyDownEscape:5,keyDownEnter:6,keyDownHome:7,keyDownEnd:8,clickItem:9,blurInput:10,changeInput:11,keyDownSpaceButton:12,clickButton:13,blurButton:14,controlledPropUpdatedSelectedItem:15,touchEnd:16}),G=function(){var e=function(e){var t,n;function r(t){var n=e.call(this,t)||this;n.id=n.props.id||"downshift-"+String(O++),n.menuId=n.props.menuId||n.id+"-menu",n.labelId=n.props.labelId||n.id+"-label",n.inputId=n.props.inputId||n.id+"-input",n.getItemId=n.props.getItemId||function(e){return n.id+"-item-"+e},n.input=null,n.items=[],n.itemCount=null,n.previousResultCount=0,n.timeoutIds=[],n.internalSetTimeout=function(e,t){var r=setTimeout((function(){n.timeoutIds=n.timeoutIds.filter((function(e){return e!==r})),e()}),t);n.timeoutIds.push(r)},n.setItemCount=function(e){n.itemCount=e},n.unsetItemCount=function(){n.itemCount=null},n.setHighlightedIndex=function(e,t){void 0===e&&(e=n.props.defaultHighlightedIndex),void 0===t&&(t={}),t=V(t),n.internalSetState(y({highlightedIndex:e},t))},n.clearSelection=function(e){n.internalSetState({selectedItem:null,inputValue:"",highlightedIndex:n.props.defaultHighlightedIndex,isOpen:n.props.defaultIsOpen},e)},n.selectItem=function(e,t,r){t=V(t),n.internalSetState(y({isOpen:n.props.defaultIsOpen,highlightedIndex:n.props.defaultHighlightedIndex,selectedItem:e,inputValue:n.props.itemToString(e)},t),r)},n.selectItemAtIndex=function(e,t,r){var o=n.items[e];null!=o&&n.selectItem(o,t,r)},n.selectHighlightedItem=function(e,t){return n.selectItemAtIndex(n.getState().highlightedIndex,e,t)},n.internalSetState=function(e,t){var r,o,i={},a="function"==typeof e;return!a&&e.hasOwnProperty("inputValue")&&n.props.onInputValueChange(e.inputValue,y({},n.getStateAndHelpers(),{},e)),n.setState((function(t){t=n.getState(t);var s=a?e(t):e;s=n.props.stateReducer(t,s),r=s.hasOwnProperty("selectedItem");var l={},c={};return r&&s.selectedItem!==t.selectedItem&&(o=s.selectedItem),s.type=s.type||0,Object.keys(s).forEach((function(e){t[e]!==s[e]&&(i[e]=s[e]),"type"!==e&&(c[e]=s[e],n.isControlledProp(e)||(l[e]=s[e]))})),a&&s.hasOwnProperty("inputValue")&&n.props.onInputValueChange(s.inputValue,y({},n.getStateAndHelpers(),{},s)),l}),(function(){k(t)(),Object.keys(i).length>1&&n.props.onStateChange(i,n.getStateAndHelpers()),r&&n.props.onSelect(e.selectedItem,n.getStateAndHelpers()),void 0!==o&&n.props.onChange(o,n.getStateAndHelpers()),n.props.onUserAction(i,n.getStateAndHelpers())}))},n.rootRef=function(e){return n._rootNode=e},n.getRootProps=function(e,t){var r,o=void 0===e?{}:e,i=o.refKey,a=void 0===i?"ref":i,s=o.ref,l=g(o,["refKey","ref"]),c=(void 0===t?{}:t).suppressRefError,u=void 0!==c&&c;n.getRootProps.called=!0,n.getRootProps.refKey=a,n.getRootProps.suppressRefError=u;var p=n.getState().isOpen;return y(((r={})[a]=H(s,n.rootRef),r.role="combobox",r["aria-expanded"]=p,r["aria-haspopup"]="listbox",r["aria-owns"]=p?n.menuId:null,r["aria-labelledby"]=n.labelId,r),l)},n.keyDownHandlers={ArrowDown:function(e){var t=this;if(e.preventDefault(),this.getState().isOpen){var n=e.shiftKey?5:1;this.moveHighlightedIndex(n,{type:4})}else this.internalSetState({isOpen:!0,type:4},(function(){var e=t.getItemCount();e>0&&t.setHighlightedIndex(z(1,t.getState().highlightedIndex,e),{type:4})}))},ArrowUp:function(e){var t=this;if(e.preventDefault(),this.getState().isOpen){var n=e.shiftKey?-5:-1;this.moveHighlightedIndex(n,{type:3})}else this.internalSetState({isOpen:!0,type:3},(function(){var e=t.getItemCount();e>0&&t.setHighlightedIndex(z(-1,t.getState().highlightedIndex,e),{type:4})}))},Enter:function(e){var t=this.getState(),n=t.isOpen,r=t.highlightedIndex;if(n&&null!=r){e.preventDefault();var o=this.items[r],i=this.getItemNodeFromIndex(r);if(null==o||i&&i.hasAttribute("disabled"))return;this.selectHighlightedItem({type:6})}},Escape:function(e){e.preventDefault(),this.reset({type:5,selectedItem:null,inputValue:""})}},n.buttonKeyDownHandlers=y({},n.keyDownHandlers,{" ":function(e){e.preventDefault(),this.toggleMenu({type:12})}}),n.inputKeyDownHandlers=y({},n.keyDownHandlers,{Home:function(e){this.highlightFirstOrLastIndex(e,!0,{type:7})},End:function(e){this.highlightFirstOrLastIndex(e,!1,{type:8})}}),n.getToggleButtonProps=function(e){var t=void 0===e?{}:e,r=t.onClick,o=(t.onPress,t.onKeyDown),i=t.onKeyUp,a=t.onBlur,s=g(t,["onClick","onPress","onKeyDown","onKeyUp","onBlur"]),l=n.getState().isOpen,c={onClick:M(r,n.buttonHandleClick),onKeyDown:M(o,n.buttonHandleKeyDown),onKeyUp:M(i,n.buttonHandleKeyUp),onBlur:M(a,n.buttonHandleBlur)};return y({type:"button",role:"button","aria-label":l?"close menu":"open menu","aria-haspopup":!0,"data-toggle":!0},s.disabled?{}:c,{},s)},n.buttonHandleKeyUp=function(e){e.preventDefault()},n.buttonHandleKeyDown=function(e){var t=K(e);n.buttonKeyDownHandlers[t]&&n.buttonKeyDownHandlers[t].call(v(n),e)},n.buttonHandleClick=function(e){e.preventDefault(),n.props.environment.document.activeElement===n.props.environment.document.body&&e.target.focus(),n.internalSetTimeout((function(){return n.toggleMenu({type:13})}))},n.buttonHandleBlur=function(e){var t=e.target;n.internalSetTimeout((function(){n.isMouseDown||null!=n.props.environment.document.activeElement&&n.props.environment.document.activeElement.id===n.inputId||n.props.environment.document.activeElement===t||n.reset({type:14})}))},n.getLabelProps=function(e){return y({htmlFor:n.inputId,id:n.labelId},e)},n.getInputProps=function(e){var t=void 0===e?{}:e,r=t.onKeyDown,o=t.onBlur,i=t.onChange,a=t.onInput,s=(t.onChangeText,g(t,["onKeyDown","onBlur","onChange","onInput","onChangeText"])),l={};var c,u=n.getState(),p=u.inputValue,d=u.isOpen,f=u.highlightedIndex;s.disabled||((c={}).onChange=M(i,a,n.inputHandleChange),c.onKeyDown=M(r,n.inputHandleKeyDown),c.onBlur=M(o,n.inputHandleBlur),l=c);return y({"aria-autocomplete":"list","aria-activedescendant":d&&"number"==typeof f&&f>=0?n.getItemId(f):null,"aria-controls":d?n.menuId:null,"aria-labelledby":n.labelId,autoComplete:"off",value:p,id:n.inputId},l,{},s)},n.inputHandleKeyDown=function(e){var t=K(e);t&&n.inputKeyDownHandlers[t]&&n.inputKeyDownHandlers[t].call(v(n),e)},n.inputHandleChange=function(e){n.internalSetState({type:11,isOpen:!0,inputValue:e.target.value,highlightedIndex:n.props.defaultHighlightedIndex})},n.inputHandleBlur=function(){n.internalSetTimeout((function(){var e=n.props.environment.document&&!!n.props.environment.document.activeElement&&!!n.props.environment.document.activeElement.dataset&&n.props.environment.document.activeElement.dataset.toggle&&n._rootNode&&n._rootNode.contains(n.props.environment.document.activeElement);n.isMouseDown||e||n.reset({type:10})}))},n.menuRef=function(e){n._menuNode=e},n.getMenuProps=function(e,t){var r,o=void 0===e?{}:e,i=o.refKey,a=void 0===i?"ref":i,s=o.ref,l=g(o,["refKey","ref"]),c=(void 0===t?{}:t).suppressRefError,u=void 0!==c&&c;return n.getMenuProps.called=!0,n.getMenuProps.refKey=a,n.getMenuProps.suppressRefError=u,y(((r={})[a]=H(s,n.menuRef),r.role="listbox",r["aria-labelledby"]=l&&l["aria-label"]?null:n.labelId,r.id=n.menuId,r),l)},n.getItemProps=function(e){var t,r=void 0===e?{}:e,o=r.onMouseMove,i=r.onMouseDown,a=r.onClick,s=(r.onPress,r.index),l=r.item,c=void 0===l?void 0:l,u=g(r,["onMouseMove","onMouseDown","onClick","onPress","index","item"]);void 0===s?(n.items.push(c),s=n.items.indexOf(c)):n.items[s]=c;var p=a,d=((t={onMouseMove:M(o,(function(){s!==n.getState().highlightedIndex&&(n.setHighlightedIndex(s,{type:2}),n.avoidScrolling=!0,n.internalSetTimeout((function(){return n.avoidScrolling=!1}),250))})),onMouseDown:M(i,(function(e){e.preventDefault()}))}).onClick=M(p,(function(){n.selectItemAtIndex(s,{type:9})})),t),f=u.disabled?{onMouseDown:d.onMouseDown}:d;return y({id:n.getItemId(s),role:"option","aria-selected":n.getState().highlightedIndex===s},f,{},u)},n.clearItems=function(){n.items=[]},n.reset=function(e,t){void 0===e&&(e={}),e=V(e),n.internalSetState((function(t){var r=t.selectedItem;return y({isOpen:n.props.defaultIsOpen,highlightedIndex:n.props.defaultHighlightedIndex,inputValue:n.props.itemToString(r)},e)}),t)},n.toggleMenu=function(e,t){void 0===e&&(e={}),e=V(e),n.internalSetState((function(t){var r=t.isOpen;return y({isOpen:!r},r&&{highlightedIndex:n.props.defaultHighlightedIndex},{},e)}),(function(){var r=n.getState(),o=r.isOpen,i=r.highlightedIndex;o&&n.getItemCount()>0&&"number"==typeof i&&n.setHighlightedIndex(i,e),k(t)()}))},n.openMenu=function(e){n.internalSetState({isOpen:!0},e)},n.closeMenu=function(e){n.internalSetState({isOpen:!1},e)},n.updateStatus=L((function(){var e=n.getState(),t=n.items[e.highlightedIndex],r=n.getItemCount(),o=n.props.getA11yStatusMessage(y({itemToString:n.props.itemToString,previousResultCount:n.previousResultCount,resultCount:r,highlightedItem:t},e));n.previousResultCount=r,F(o,n.props.environment.document)}),200);var r=n.props,o=r.defaultHighlightedIndex,i=r.initialHighlightedIndex,a=void 0===i?o:i,s=r.defaultIsOpen,l=r.initialIsOpen,c=void 0===l?s:l,u=r.initialInputValue,p=void 0===u?"":u,d=r.initialSelectedItem,f=void 0===d?null:d,h=n.getState({highlightedIndex:a,isOpen:c,inputValue:p,selectedItem:f});return null!=h.selectedItem&&void 0===n.props.initialInputValue&&(h.inputValue=n.props.itemToString(h.selectedItem)),n.state=h,n}n=e,(t=r).prototype=Object.create(n.prototype),t.prototype.constructor=t,t.__proto__=n;var o=r.prototype;return o.internalClearTimeouts=function(){this.timeoutIds.forEach((function(e){clearTimeout(e)})),this.timeoutIds=[]},o.getState=function(e){var t=this;return void 0===e&&(e=this.state),Object.keys(e).reduce((function(n,r){return n[r]=t.isControlledProp(r)?t.props[r]:e[r],n}),{})},o.isControlledProp=function(e){return void 0!==this.props[e]},o.getItemCount=function(){var e=this.items.length;return null!=this.itemCount?e=this.itemCount:void 0!==this.props.itemCount&&(e=this.props.itemCount),e},o.getItemNodeFromIndex=function(e){return this.props.environment.document.getElementById(this.getItemId(e))},o.scrollHighlightedItemIntoView=function(){var e=this.getItemNodeFromIndex(this.getState().highlightedIndex);this.props.scrollIntoView(e,this._menuNode)},o.moveHighlightedIndex=function(e,t){var n=this.getItemCount();if(n>0){var r=z(e,this.getState().highlightedIndex,n);this.setHighlightedIndex(r,t)}},o.highlightFirstOrLastIndex=function(e,t,n){var r=this.getItemCount()-1;r<0||!this.getState().isOpen||(e.preventDefault(),this.setHighlightedIndex(t?0:r,n))},o.getStateAndHelpers=function(){var e=this.getState(),t=e.highlightedIndex,n=e.inputValue,r=e.selectedItem,o=e.isOpen,i=this.props.itemToString,a=this.id,s=this.getRootProps,l=this.getToggleButtonProps,c=this.getLabelProps,u=this.getMenuProps,p=this.getInputProps,d=this.getItemProps,f=this.openMenu,h=this.closeMenu,m=this.toggleMenu,g=this.selectItem,y=this.selectItemAtIndex,v=this.selectHighlightedItem,b=this.setHighlightedIndex,w=this.clearSelection,_=this.clearItems;return{getRootProps:s,getToggleButtonProps:l,getLabelProps:c,getMenuProps:u,getInputProps:p,getItemProps:d,reset:this.reset,openMenu:f,closeMenu:h,toggleMenu:m,selectItem:g,selectItemAtIndex:y,selectHighlightedItem:v,setHighlightedIndex:b,clearSelection:w,clearItems:_,setItemCount:this.setItemCount,unsetItemCount:this.unsetItemCount,setState:this.internalSetState,itemToString:i,id:a,highlightedIndex:t,inputValue:n,isOpen:o,selectedItem:r}},o.componentDidMount=function(){var e=this;var t=function(t,n){void 0===n&&(n=!0);var r=e.props.environment.document;return[e._rootNode,e._menuNode].some((function(e){return e&&(D(e,t)||n&&D(e,r.activeElement))}))},n=function(){e.isMouseDown=!0},r=function(n){e.isMouseDown=!1,!t(n.target)&&e.getState().isOpen&&e.reset({type:1},(function(){return e.props.onOuterClick(e.getStateAndHelpers())}))},o=function(){e.isTouchMove=!1},i=function(){e.isTouchMove=!0},a=function(n){var r=t(n.target,!1);e.isTouchMove||r||!e.getState().isOpen||e.reset({type:16},(function(){return e.props.onOuterClick(e.getStateAndHelpers())}))},s=this.props.environment;s.addEventListener("mousedown",n),s.addEventListener("mouseup",r),s.addEventListener("touchstart",o),s.addEventListener("touchmove",i),s.addEventListener("touchend",a),this.cleanup=function(){e.internalClearTimeouts(),e.updateStatus.cancel(),s.removeEventListener("mousedown",n),s.removeEventListener("mouseup",r),s.removeEventListener("touchstart",o),s.removeEventListener("touchmove",i),s.removeEventListener("touchend",a)}},o.shouldScroll=function(e,t){var n=(void 0===this.props.highlightedIndex?this.getState():this.props).highlightedIndex,r=(void 0===t.highlightedIndex?e:t).highlightedIndex;return n&&this.getState().isOpen&&!e.isOpen||n!==r},o.componentDidUpdate=function(e,t){this.isControlledProp("selectedItem")&&this.props.selectedItemChanged(e.selectedItem,this.props.selectedItem)&&this.internalSetState({type:15,inputValue:this.props.itemToString(this.props.selectedItem)}),!this.avoidScrolling&&this.shouldScroll(t,e)&&this.scrollHighlightedItemIntoView(),this.updateStatus()},o.componentWillUnmount=function(){this.cleanup()},o.render=function(){var e=T(this.props.children,P);this.clearItems(),this.getRootProps.called=!1,this.getRootProps.refKey=void 0,this.getRootProps.suppressRefError=void 0,this.getMenuProps.called=!1,this.getMenuProps.refKey=void 0,this.getMenuProps.suppressRefError=void 0,this.getLabelProps.called=!1,this.getInputProps.called=!1;var t=T(e(this.getStateAndHelpers()));return t?this.getRootProps.called||this.props.suppressRefError?t:A(t)?(0,_.cloneElement)(t,this.getRootProps(N(t))):void 0:null},r}(_.Component);return e.defaultProps={defaultHighlightedIndex:null,defaultIsOpen:!1,getA11yStatusMessage:j,itemToString:function(e){return null==e?"":String(e)},onStateChange:P,onInputValueChange:P,onUserAction:P,onChange:P,onSelect:P,onOuterClick:P,selectedItemChanged:function(e,t){return e!==t},environment:"undefined"==typeof window?{}:window,stateReducer:function(e,t){return t},suppressRefError:!1,scrollIntoView:C},e.stateChangeTypes=W,e}();w().array.isRequired,w().func,w().func,w().func,w().bool,w().number,w().number,w().number,w().bool,w().bool,w().bool,w().any,w().any,w().any,w().string,w().string,w().string,w().func,w().string,w().func,w().func,w().func,w().func,w().func,w().shape({addEventListener:w().func,removeEventListener:w().func,document:w().shape({getElementById:w().func,activeElement:w().any,body:w().any})});"undefined"==typeof window||window;var q=G;function Y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function $(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var X=function(e){var t=e.listId,n=e.provider,r=e.apiKey,i=e.apiUrl,a=e.onChange,l=h((0,o.useState)([]),2),p=l[0],f=l[1],g=h((0,o.useState)(!1),2),y=g[0],v=g[1],b=h((0,o.useState)({controller:null}),2),w=b[0].controller,_=b[1],I=function(){var e=s(c().mark((function e(){var t,o,a;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return w&&w.abort(),v(!0),"AbortController"in window&&(w=new AbortController,_({controller:w})),(t=new FormData).append("api_key",r),t.append("api_url",i),t.append("provider",n),t.append("action","blocksy_ext_newsletter_subscribe_maybe_get_lists"),t.append("nonce",ctDashboardLocalizations.dashboard_actions_nonce),e.prev=9,e.next=12,fetch(ctDashboardLocalizations.ajax_url,{method:"POST",signal:w.signal,body:t});case 12:if(200!==(o=e.sent).status){e.next=22;break}return e.next=16,o.json();case 16:if(!(a=e.sent).success){e.next=22;break}if("api_key_invalid"===a.data.result){e.next=22;break}return v(!1),f(a.data.result.map((function(e){return $($({},e),{},{id:e.id.toString()})}))),e.abrupt("return");case 22:e.next=26;break;case 24:e.prev=24,e.t0=e.catch(9);case 26:f([]),v(!1);case 28:case"end":return e.stop()}}),e,null,[[9,24]])})));return function(){return e.apply(this,arguments)}}();return(0,o.useEffect)((function(){r||["mailpoet","fluentcrm"].includes(n)?I():f([])}),[n,r,i]),0===p.length?(0,o.createElement)("div",{className:d()("ct-select-input","ct-no-results")},(0,o.createElement)("input",{disabled:!0,placeholder:y?(0,u.__)("Fetching...","blocksy-companion"):""})):(0,o.createElement)(q,{selectedItem:p.find((function(e){return e.id===t}))?t:"",onChange:function(e){return a(e)},itemToString:function(e){return e?(p.find((function(t){return t.id===e}))||{}).name:""}},(function(e){var t=e.getInputProps,n=e.getItemProps,r=(e.getLabelProps,e.getMenuProps),i=e.isOpen,a=(e.inputValue,e.highlightedIndex),s=e.selectedItem,l=e.openMenu;return(0,o.createElement)("div",{className:"ct-select-input"},(0,o.createElement)("input",m({},t({onFocus:function(){return l()},onClick:function(){return l()}}),{placeholder:(0,u.__)("Select list...","blocksy-companion"),readOnly:!0})),i&&(0,o.createElement)("div",r({className:"ct-select-dropdown"}),p.map((function(e,t){return(0,o.createElement)("div",n({key:e.id,index:t,item:e.id,className:d()("ct-select-dropdown-item",{active:a===t,selected:s===e.id})}),e.name)}))))}))},J=window.blocksyOptions,Q=(window.ctDashboardLocalizations||{}).DashboardContext,Z=((Q||{}).Provider,(Q||{}).Consumer,Q),ee=((0,u.__)("Free","blocksy-companion"),(0,u.__)("Personal","blocksy-companion"),(0,u.__)("Business","blocksy-companion"),(0,u.__)("Agency","blocksy-companion"),(0,u.__)("Personal","blocksy-companion"),(0,u.__)("Business","blocksy-companion"),(0,u.__)("Agency","blocksy-companion"),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.currentPlan,e.requiredPlan),n=void 0===t?"personal":t,r=e.personal,i=void 0===r?{title:(0,u.__)("This is a Pro feature","blocksy-companion"),description:(0,u.__)("Upgrade to any pro plan and get instant access to this and many other feature.","blocksy-companion")}:r,a=e.professional,s=void 0===a?{description:(0,u.__)("Upgrade to the business or agency plan and get instant access to this and many other features.","blocksy-companion")}:a,l=e.agency,c=void 0===l?{description:(0,u.__)("Upgrade to the agency plan and get instant access to this and many other features.","blocksy-companion")}:l,p=h((0,o.useState)(!1),2),d=p[0],f=p[1];return{showNotice:function(e){f(e||!0)},content:(0,o.createElement)(J.Overlay,{items:d,className:"ct-onboarding-modal",onDismiss:function(){return f(!1)},render:function(){return(0,o.createElement)("div",{className:"ct-modal-content"},(0,o.createElement)("svg",{width:"55",height:"55",viewBox:"0 0 40.5 48.3"},(0,o.createElement)("path",{fill:"#2d82c8",d:"M33.4 29.4l7.1 12.3-7.4.6-4 6-7.3-12.9"}),(0,o.createElement)("path",{d:"M33.5 29.6L26 42.7l-4.2-7.3 11.6-6 .1.2zM0 41.7l7.5.6 3.9 6 7.2-12.4-11-7.3L0 41.7z",fill:"#2271b1"}),(0,o.createElement)("path",{d:"M39.5 18.7c0 1.6-2.4 2.8-2.7 4.3-.4 1.5 1 3.8.2 5.1-.8 1.3-3.4 1.2-4.5 2.3-1.1 1.1-1 3.7-2.3 4.5-1.3.8-3.6-.6-5.1-.2-1.5.4-2.7 2.7-4.3 2.7S18 35 16.5 34.7c-1.5-.4-3.8 1-5.1.2s-1.2-3.4-2.3-4.5-3.7-1-4.5-2.3.6-3.6.2-5.1-2.7-2.7-2.7-4.3 2.4-2.8 2.7-4.3c.4-1.5-1-3.8-.2-5.1C5.4 8 8.1 8.1 9.1 7c1.1-1.1 1-3.7 2.3-4.5s3.6.6 5.1.2C18 2.4 19.2 0 20.8 0c1.6 0 2.8 2.4 4.3 2.7 1.5.4 3.8-1 5.1-.2 1.3.8 1.2 3.4 2.3 4.5 1.1 1.1 3.7 1 4.5 2.3s-.6 3.6-.2 5.1c.3 1.5 2.7 2.7 2.7 4.3z",fill:"#599fd9"}),(0,o.createElement)("path",{d:"M23.6 7c-6.4-1.5-12.9 2.5-14.4 8.9-.7 3.1-.2 6.3 1.5 9.1 1.7 2.7 4.3 4.6 7.4 5.4.9.2 1.9.3 2.8.3 2.2 0 4.4-.6 6.3-1.8 2.7-1.7 4.6-4.3 5.4-7.5C34 15 30 8.5 23.6 7zm7 14c-.6 2.6-2.2 4.8-4.5 6.2-2.3 1.4-5 1.8-7.6 1.2-2.6-.6-4.8-2.2-6.2-4.5-1.4-2.3-1.8-5-1.2-7.6.6-2.6 2.2-4.8 4.5-6.2 1.6-1 3.4-1.5 5.2-1.5.8 0 1.5.1 2.3.3 5.4 1.3 8.7 6.7 7.5 12.1zm-8.2-4.5l3.7.5-2.7 2.7.7 3.7-3.4-1.8-3.3 1.8.6-3.7-2.7-2.7 3.8-.5 1.6-3.4 1.7 3.4z",fill:"#fff"})),"personal"===n&&(0,o.createElement)(o.Fragment,null,(0,o.createElement)("h2",{className:"ct-modal-title"},i.title),(0,o.createElement)("p",null,i.description)),"professional"===n&&(0,o.createElement)(o.Fragment,null,(0,o.createElement)("span",null,"Required plan"),(0,o.createElement)("h2",{className:"ct-modal-title"},(0,u.__)("Business or Agency","blocksy-companion")),(0,o.createElement)("p",null,s.description)),"agency"===n&&(0,o.createElement)(o.Fragment,null,(0,o.createElement)("span",null,"Required plan"),(0,o.createElement)("h2",{className:"ct-modal-title"},(0,u.__)("Agency","blocksy-companion")),(0,o.createElement)("p",null,c.description)),(0,o.createElement)("div",{className:"ct-modal-actions has-divider","data-buttons":"2"},(0,o.createElement)("a",{href:ctDashboardLocalizations.plugin_data?ctDashboardLocalizations.plugin_data.modal_links["compare-plans"]:"https://creativethemes.com/blocksy/pricing/#comparison-free-vs-pro",target:"_blank",className:"button"},(0,u.__)("Compare Plans","blocksy-companion")),(0,o.createElement)("a",{href:ctDashboardLocalizations.plugin_data.modal_links?ctDashboardLocalizations.plugin_data.modal_links.pricing:"https://creativethemes.com/blocksy/pricing/",target:"_blank",className:"button button-primary"},(0,u.__)("Upgrade Now","blocksy-companion"))))}})}});function te(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ne(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?te(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):te(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var re=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t=ne({strategy:"pro-ext",modalTitle:(0,u.__)("This is a Pro extension","blocksy-companion")},t);var n=h((0,o.useState)(!1),2),r=(n[0],n[1],ctDashboardLocalizations.plugin_data.is_pro),i=((0,o.useContext)(Z).history,!r&&e.config.pro),a=ctDashboardLocalizations.plugin_data.current_plan,s="personal";e.config.plans&&(e.config.plans.includes("agency_v2")&&(s="agency"),e.config.plans.includes("professional_v2")&&(s="professional"));var l=ee(ne({currentPlan:a,requiredPlan:s},t)),c=l.content,p=l.showNotice;return{isPro:r,isProInFree:!r&&e.config.pro||e.config.plans&&e.config.plans.length&&!e.config.plans.includes(a),showNotice:p,content:"pro-ext"===t.strategy&&i||"pro"===t.strategy&&!r||e.config.plans&&e.config.plans.length&&!e.config.plans.includes(a)?c:null}},oe=["mailchimp","demo"],ie=function(e){var t=e.extension,n=e.onCredentialsValidated,r=h((0,o.useState)(t.data.provider),2),i=r[0],a=r[1],l=h((0,o.useState)(t.data.api_key),2),p=l[0],f=l[1],m=h((0,o.useState)(t.data.api_url),2),g=m[0],y=m[1],v=h((0,o.useState)(t.data.list_id),2),b=v[0],w=v[1],_=h((0,o.useState)(!1),2),I=_[0],S=_[1],x=re(t,{strategy:"pro"}),E=x.isPro,O=x.showNotice,k=x.content;(0,o.useEffect)((function(){!t.data||oe.includes(t.data.provider)||E||a(oe[0])}),[]);var P=function(){var e=s(c().mark((function e(){var t,r,o;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return(t=new FormData).append("provider",i),t.append("api_key",p),t.append("api_url",g),t.append("list_id",b),t.append("action","blocksy_ext_newsletter_subscribe_maybe_save_credentials"),t.append("nonce",ctDashboardLocalizations.dashboard_actions_nonce),S(!0),e.prev=8,e.next=11,fetch(ctDashboardLocalizations.ajax_url,{method:"POST",body:t});case 11:if(200!==(r=e.sent).status){e.next=17;break}return e.next=15,r.json();case 15:(o=e.sent).success&&"api_key_invalid"!==o.data.result&&n();case 17:e.next=21;break;case 19:e.prev=19,e.t0=e.catch(8);case 21:return e.next=23,new Promise((function(e){return setTimeout((function(){return e()}),1e3)}));case 23:S(!1);case 24:case"end":return e.stop()}}),e,null,[[8,19]])})));return function(){return e.apply(this,arguments)}}();return(0,o.createElement)("div",{className:d()("ct-extension-options ct-newsletter-subscribe-options")},(0,o.createElement)("h4",null,(0,u.__)("Connect Newsletter Provider","blocksy-companion")),(0,o.createElement)("div",{className:"ct-newsletter-credentials","data-columns":i.indexOf("mailerlite")>-1||"activecampaign"===i?4:["mailpoet","fluentcrm"].includes(i)?2:3},(0,o.createElement)("section",null,(0,o.createElement)("label",null,(0,u.__)("Provider","blocksy-companion")),(0,o.createElement)(J.Select,{onChange:function(e){if(console.log(e,t.data.providers),!E&&!oe.includes(e))return a(e),setTimeout((function(){a(oe[0])})),void O();a(e)},option:{placeholder:(0,u.__)("Pick Mailing Service","blocksy-companion"),choices:t.data.providers},value:i.indexOf("mailerlite")>-1?"mailerlite-new":i})),i.indexOf("activecampaign")>-1&&(0,o.createElement)("section",null,(0,o.createElement)("label",null,(0,u.__)("API URL","blocksy-companion")),(0,o.createElement)("div",{className:"ct-option-input"},(0,o.createElement)("input",{type:"text",onChange:function(e){var t=e.target.value;return y(t)},value:g||""}))),i.indexOf("mailerlite")>-1&&(0,o.createElement)("section",null,(0,o.createElement)("label",null,(0,u.__)("API Version","blocksy-companion")),(0,o.createElement)(J.Select,{onChange:function(e){a("new"===e?"mailerlite-new":"mailerlite")},option:{placeholder:(0,u.__)("Pick Mailing Service","blocksy-companion"),choices:[{key:"new",value:"New"},{key:"classic",value:"Classic"}]},value:"mailerlite-new"===i?"new":"classic"})),(oe.includes(i)||ctDashboardLocalizations.plugin_data.is_pro)&&(0,o.createElement)(o.Fragment,null,["mailpoet","fluentcrm"].includes(i)?null:(0,o.createElement)("section",null,(0,o.createElement)("label",null,(0,u.__)("API Key","blocksy-companion")),(0,o.createElement)("div",{className:"ct-option-input"},(0,o.createElement)("input",{type:"text",onChange:function(e){var t=e.target.value;return f(t)},value:p||""}))),(0,o.createElement)("section",null,(0,o.createElement)("label",null,(0,u.__)("List ID","blocksy-companion")),(0,o.createElement)(X,{listId:b,onChange:function(e){return w(e)},provider:i,apiUrl:g,apiKey:p})))),"mailchimp"===i&&(0,o.createElement)("span",{className:"ct-option-description",dangerouslySetInnerHTML:{__html:(0,u.sprintf)((0,u.__)("More information on how to generate an API key for Mailchimp can be found %shere%s.","blocksy-companion"),'<a target="_blank" href="https://mailchimp.com/help/about-api-keys/">',"</a>")}}),ctDashboardLocalizations.plugin_data.is_pro&&i.indexOf("mailerlite")>-1&&(0,o.createElement)("span",{className:"ct-option-description",dangerouslySetInnerHTML:{__html:(0,u.sprintf)((0,u.__)("More information on how to generate an API key for Mailerlite can be found %shere%s. Please note that it is required at least one group to be created in your account for the integration to work. More info on how to create a group %shere%s.","blocksy-companion"),'<a target="_blank" href="https://www.mailerlite.com/help/where-to-find-the-mailerlite-api-key-and-documentation">',"</a>",'<a target="_blank" href="https://www.mailerlite.com/help/how-to-create-and-use-groups">',"</a>")}}),i.indexOf("demo")>-1&&(0,o.createElement)("span",{className:"ct-option-description",dangerouslySetInnerHTML:{__html:(0,u.__)("This provider is used only for testing purposes. It doesnt register any real subscribers.","blocksy-companion")}}),ctDashboardLocalizations.plugin_data.is_pro&&"brevo"===i&&(0,o.createElement)("span",{className:"ct-option-description",dangerouslySetInnerHTML:{__html:(0,u.sprintf)((0,u.__)("More information on how to generate an API key for Brevo can be found %shere%s.","blocksy-companion"),'<a target="_blank" href="https://help.brevo.com/hc/en-us/articles/*********-Create-and-manage-your-API-keys">',"</a>")}}),ctDashboardLocalizations.plugin_data.is_pro&&"convertkit"===i&&(0,o.createElement)("span",{className:"ct-option-description",dangerouslySetInnerHTML:{__html:(0,u.sprintf)((0,u.__)("More information on how to generate an API key for ConvertKit can be found %shere%s.","blocksy-companion"),'<a target="_blank" href="https://developers.convertkit.com/#api-basics">',"</a>")}}),ctDashboardLocalizations.plugin_data.is_pro&&"activecampaign"===i&&(0,o.createElement)("span",{className:"ct-option-description",dangerouslySetInnerHTML:{__html:(0,u.sprintf)((0,u.__)("More information on how to generate an API key for ActiveCampaign can be found %shere%s.","blocksy-companion"),'<a target="_blank" href="https://developers.activecampaign.com/reference/overview">',"</a>")}}),ctDashboardLocalizations.plugin_data.is_pro&&"campaignmonitor"===i&&(0,o.createElement)("span",{className:"ct-option-description",dangerouslySetInnerHTML:{__html:(0,u.sprintf)((0,u.__)("More information on how to generate an API key for Campaign Monitor can be found %shere%s.","blocksy-companion"),'<a target="_blank" href="https://help.campaignmonitor.com/api-keys">',"</a>")}}),ctDashboardLocalizations.plugin_data.is_pro&&"emailoctopus"===i&&(0,o.createElement)("span",{className:"ct-option-description",dangerouslySetInnerHTML:{__html:(0,u.sprintf)((0,u.__)("More information on how to generate an API key for EmailOctopus can be found %shere%s.","blocksy-companion"),'<a target="_blank" href="https://emailoctopus.com/api-documentation/v2">',"</a>")}}),ctDashboardLocalizations.plugin_data.is_pro&&"mailpoet"===i&&(0,o.createElement)("span",{className:"ct-option-description",dangerouslySetInnerHTML:{__html:(0,u.sprintf)((0,u.__)("More information on how to create a list in MailPoet can be found %shere%s.","blocksy-companion"),'<a target="_blank" href="https://kb.mailpoet.com/article/282-create-a-list">',"</a>")}}),ctDashboardLocalizations.plugin_data.is_pro&&"fluentcrm"===i&&(0,o.createElement)("span",{className:"ct-option-description",dangerouslySetInnerHTML:{__html:(0,u.sprintf)((0,u.__)("More information on how to create a list in fluentcrm can be found %shere%s.","blocksy-companion"),'<a target="_blank" href="https://kb.fluentcrm.com/article/282-create-a-list">',"</a>")}}),(0,o.createElement)("button",{className:"ct-button-primary",disabled:!p&&!["mailpoet","fluentcrm"].includes(i)||!b||I||!g&&"activecampaign"===i,onClick:function(){return P()}},I?(0,u.__)("Loading...","blocksy-companion"):(0,u.__)("Save Settings","blocksy-companion")),k)},ae=function(e){var t=e.extension,n=e.onExtsSync,r=function(){var e=s(c().mark((function e(){var r;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return(r=new FormData).append("ext",t.name),r.append("action",t.__object?"blocksy_extension_deactivate":"blocksy_extension_activate"),r.append("nonce",ctDashboardLocalizations.dashboard_actions_nonce),e.prev=4,e.next=7,fetch(ctDashboardLocalizations.ajax_url,{method:"POST",body:r});case 7:n(),e.next=12;break;case 10:e.prev=10,e.t0=e.catch(4);case 12:case"end":return e.stop()}}),e,null,[[4,10]])})));return function(){return e.apply(this,arguments)}}();return(0,o.createElement)(ie,{extension:t,onCredentialsValidated:function(){t.__object||r()}})};function se(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function le(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?se(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):se(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}(0,i.addFilter)("blocksy.extensions.current_extension_content","blocksy",(function(e,t){var n=t.extension,r=t.onExtsSync;return"newsletter-subscribe"!==n.name?e:le(le(le({},e),n.data.api_key?{}:{activationStrategy:"from-custom-content"}),{},{content:(0,o.createElement)(ae,{extension:n,onExtsSync:r})})}))}()}();