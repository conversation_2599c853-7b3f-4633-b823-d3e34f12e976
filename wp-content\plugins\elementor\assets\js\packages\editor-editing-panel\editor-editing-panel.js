/*! For license information please see editor-editing-panel.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React},"@elementor/editor":function(e){e.exports=window.elementorV2.editor},"@elementor/editor-canvas":function(e){e.exports=window.elementorV2.editorCanvas},"@elementor/editor-controls":function(e){e.exports=window.elementorV2.editorControls},"@elementor/editor-documents":function(e){e.exports=window.elementorV2.editorDocuments},"@elementor/editor-elements":function(e){e.exports=window.elementorV2.editorElements},"@elementor/editor-panels":function(e){e.exports=window.elementorV2.editorPanels},"@elementor/editor-props":function(e){e.exports=window.elementorV2.editorProps},"@elementor/editor-responsive":function(e){e.exports=window.elementorV2.editorResponsive},"@elementor/editor-styles":function(e){e.exports=window.elementorV2.editorStyles},"@elementor/editor-styles-repository":function(e){e.exports=window.elementorV2.editorStylesRepository},"@elementor/editor-ui":function(e){e.exports=window.elementorV2.editorUi},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/icons":function(e){e.exports=window.elementorV2.icons},"@elementor/locations":function(e){e.exports=window.elementorV2.locations},"@elementor/menus":function(e){e.exports=window.elementorV2.menus},"@elementor/schema":function(e){e.exports=window.elementorV2.schema},"@elementor/session":function(e){e.exports=window.elementorV2.session},"@elementor/ui":function(e){e.exports=window.elementorV2.ui},"@elementor/utils":function(e){e.exports=window.elementorV2.utils},"@elementor/wp-media":function(e){e.exports=window.elementorV2.wpMedia},"@wordpress/i18n":function(e){e.exports=window.wp.i18n}},t={};function n(l){var r=t[l];if(void 0!==r)return r.exports;var o=t[l]={exports:{}};return e[l](o,o.exports,n),o.exports}n.d=function(e,t){for(var l in t)n.o(t,l)&&!n.o(e,l)&&Object.defineProperty(e,l,{enumerable:!0,get:t[l]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var l={};!function(){n.r(l),n.d(l,{EXPERIMENTAL_FEATURES:function(){return x},PopoverScrollableContent:function(){return bo},controlActionsMenu:function(){return Ce},init:function(){return ua},injectIntoClassSelectorActions:function(){return ge},registerControlReplacement:function(){return C},registerStyleProviderToColors:function(){return z},useBoundProp:function(){return e.useBoundProp},useFontFamilies:function(){return hr},usePanelActions:function(){return Eo},usePanelStatus:function(){return fo},useSectionWidth:function(){return Ue}});var e=n("@elementor/editor-controls"),t=n("react"),r=n("@elementor/editor-elements"),o=n("@elementor/editor-styles-repository"),a=n("@elementor/editor-ui"),i=n("@elementor/icons"),s=n("@elementor/locations"),c=n("@elementor/ui"),m=n("@wordpress/i18n"),u=n("@elementor/utils"),p=n("@elementor/editor-documents"),d=n("@elementor/editor-props"),E=n("@elementor/editor-v1-adapters"),f=n("@elementor/editor-panels"),b=n("@elementor/session"),y=n("@elementor/menus"),v=n("@elementor/editor-responsive"),g=n("@elementor/editor-styles"),_=n("@elementor/editor-canvas"),h=n("@elementor/editor"),w=n("@elementor/schema"),S=n("@elementor/wp-media"),x={V_3_30:"e_v_3_30",V_3_31:"e_v_3_31"},{registerControlReplacement:C,getControlReplacements:T}=(0,e.createControlReplacementsRegistry)(),I={name:"default",getThemeColor:null},k=new Map,z=(e,t)=>{k.set(e,t)},P=e=>k.get(e)??I,D=(0,t.createContext)(null);function R({children:e,prop:n}){return t.createElement(D.Provider,{value:{prop:n}},e)}function L(){const e=(0,t.useContext)(D);if(!e)throw new Error("useClassesProp must be used within a ClassesPropProvider");return e.prop}var V=(0,t.createContext)(null);function A({children:e,element:n,elementType:l}){return t.createElement(V.Provider,{value:{element:n,elementType:l}},e)}function N(){const e=(0,t.useContext)(V);if(!e)throw new Error("useElement must be used within a ElementProvider");return e}var B=(0,u.createError)({code:"control_type_not_found",message:"Control type not found."}),O=(0,u.createError)({code:"provider_not_found",message:"Styles provider not found."}),M=(0,u.createError)({code:"provider_cannot_update_props",message:"Styles provider doesn't support updating props."}),F=(0,u.createError)({code:"style_not_found_under_provider",message:"Style not found under the provider."}),$=(0,t.createContext)(null);function j({children:e,...n}){const l=null===n.id?null:W(n.id),{userCan:r}=(0,o.useUserStylesCapability)();if(n.id&&!l)throw new O({context:{styleId:n.id}});const a=r(l?.getKey()??"").updateProps;return t.createElement($.Provider,{value:{...n,provider:l,canEdit:a}},e)}function U(){const e=(0,t.useContext)($);if(!e)throw new Error("useStyle must be used within a StyleProvider");return e}function W(e){return o.stylesRepository.getProviders().find((t=>t.actions.all().find((t=>t.id===e))))??null}var G=e=>e&&e!==o.ELEMENTS_BASE_STYLES_PROVIDER_KEY?(0,o.isElementsStylesProvider)(e)?"accent":P(e).name:"default",K=e=>e&&e!==o.ELEMENTS_BASE_STYLES_PROVIDER_KEY?(0,o.isElementsStylesProvider)(e)?e=>e.palette.accent.main:P(e).getThemeColor:null;function H(e){const{_group:t,_action:n,...l}=e;return l}var J=t.forwardRef((function({selected:e,options:n,entityName:l,onSelect:r,placeholder:o,onCreate:a,validate:i,renderEmptyState:s,...m},u){const{inputValue:p,setInputValue:d,error:E,setError:f,inputHandlers:b}=function(e){const[n,l]=(0,t.useState)(""),[r,o]=(0,t.useState)(null);return{inputValue:n,setInputValue:l,error:r,setError:o,inputHandlers:{onChange:t=>{const{value:n}=t.target;if(l(n),!e)return;if(!n)return void o(null);const{isValid:r,errorMessage:a}=e(n,"inputChange");o(r?null:a)},onBlur:()=>{l(""),o(null)}}}}(i),{open:y,openDropdown:v,closeDropdown:g}=function(e=!1){const[n,l]=(0,t.useState)(e);return{open:n,openDropdown:()=>l(!0),closeDropdown:()=>l(!1)}}(m.open),{createOption:_,loading:h}=function(e){const{onCreate:n,validate:l,setInputValue:r,setError:o,closeDropdown:a}=e,[i,s]=(0,t.useState)(!1);return n?{createOption:async e=>{if(s(!0),l){const{isValid:t,errorMessage:n}=l(e,"create");if(!t)return o(n),void s(!1)}try{r(""),a(),await n(e)}catch{}finally{s(!1)}},loading:i}:{createOption:null,loading:!1}}({onCreate:a,validate:i,setInputValue:d,setError:f,closeDropdown:g}),[w,S]=(0,t.useMemo)((()=>[n,e].map((e=>function(e,t){return e.map((e=>({...e,_group:`Existing ${t??"options"}`})))}(e,l?.plural)))),[n,e,l?.plural]),x=function(e){const{options:t,onSelect:n,createOption:l,setInputValue:r,closeDropdown:o}=e;if(n||l)return async(e,n,i,s)=>{const c=s?.option;if(!c||"object"==typeof c&&c.fixed)return;const m=n.filter((e=>"string"!=typeof e));switch(i){case"removeOption":a(m,"removeOption",c);break;case"selectOption":{const e=c;if("create"===e._action){const t=e.value;return l?.(t)}a(m,"selectOption",e);break}case"createOption":{const e=c,n=t.find((t=>t.label.toLocaleLowerCase()===e.toLocaleLowerCase()));if(!n)return l?.(e);m.push(n),a(m,"selectOption",n);break}}r(""),o()};function a(e,t,l){n?.(e.map((e=>H(e))),t,H(l))}}({options:w,onSelect:r,createOption:_,setInputValue:d,closeDropdown:g}),C=function(e){const{options:t,selected:n,onCreate:l,entityName:r}=e,o=(0,c.createFilterOptions)();return(e,a)=>{const i=n.map((e=>e.value)),s=o(e.filter((e=>!i.includes(e.value))),a),c=t.some((e=>a.inputValue===e.label));return Boolean(l)&&""!==a.inputValue&&!i.includes(a.inputValue)&&!c&&s.unshift({label:`Create "${a.inputValue}"`,value:a.inputValue,_group:`Create a new ${r?.singular??"option"}`,key:`create-${a.inputValue}`,_action:"create"}),s}}({options:n,selected:e,onCreate:a,entityName:l}),T=Boolean(a)||p.length<2||void 0;return t.createElement(c.Autocomplete,{renderTags:(e,n)=>e.map(((e,l)=>t.createElement(c.Chip,{size:"tiny",...n({index:l}),key:e.key??e.value??e.label,label:e.label}))),...m,ref:u,freeSolo:T,forcePopupIcon:!1,multiple:!0,clearOnBlur:!0,selectOnFocus:!0,disableClearable:!0,handleHomeEndKeys:!0,disabled:h,open:y,onOpen:v,onClose:g,disableCloseOnSelect:!0,value:S,options:w,ListboxComponent:E?t.forwardRef(((e,n)=>t.createElement(q,{ref:n,error:E}))):void 0,renderGroup:e=>t.createElement(Y,{...e}),inputValue:p,renderInput:e=>t.createElement(c.TextField,{...e,error:Boolean(E),placeholder:o,...b,sx:e=>({".MuiAutocomplete-inputRoot.MuiInputBase-adornedStart":{paddingLeft:e.spacing(.25),paddingRight:e.spacing(.25)}})}),onChange:x,getOptionLabel:e=>"string"==typeof e?e:e.label,getOptionKey:e=>"string"==typeof e?e:e.key??e.value??e.label,filterOptions:C,groupBy:e=>e._group??"",renderOption:(e,n)=>{const{_group:l,label:r}=n;return t.createElement("li",{...e,style:{display:"block",textOverflow:"ellipsis"},"data-group":l},r)},noOptionsText:s?.({searchValue:p,onClear:()=>{d(""),g()}}),isOptionEqualToValue:(e,t)=>"string"==typeof e?e===t:e.value===t.value})})),Y=e=>{const n=`combobox-group-${(0,t.useId)().replace(/:/g,"_")}`;return t.createElement(X,{role:"group","aria-labelledby":n},t.createElement(Z,{id:n}," ",e.group),t.createElement(Q,{role:"listbox"},e.children))},q=t.forwardRef((({error:e="error"},n)=>t.createElement(c.Box,{ref:n,sx:e=>({padding:e.spacing(2)})},t.createElement(c.Typography,{variant:"caption",sx:{color:"error.main",display:"inline-block"}},e)))),X=(0,c.styled)("li")`
	&:not( :last-of-type ) {
		border-bottom: 1px solid ${({theme:e})=>e.palette.divider};
	}
`,Z=(0,c.styled)(c.Box)((({theme:e})=>({position:"sticky",top:"-8px",padding:e.spacing(1,2),color:e.palette.text.tertiary,backgroundColor:e.palette.primary.contrastText}))),Q=(0,c.styled)("ul")`
	padding: 0;
`,ee=(0,t.createContext)(null),te=()=>{const e=(0,t.useContext)(ee);if(!e)throw new Error("useCssClass must be used within a CssClassProvider");return e};function ne({children:e,...n}){return t.createElement(ee.Provider,{value:n},e)}var le=(0,c.styled)("div",{shouldForwardProp:e=>!["isOverridden","getColor"].includes(e)})`
	width: 5px;
	height: 5px;
	border-radius: 50%;
	background-color: ${({theme:e,isOverridden:t,getColor:n})=>{if(t)return e.palette.warning.light;const l=n?.(e);return l??e.palette.text.disabled}};
`;function re(){const{id:e,setId:n}=U(),{element:l}=N(),o=(0,E.isExperimentActive)(x.V_3_30),a=oe(),i=ae(),s=(0,t.useMemo)((()=>(0,E.undoable)({do:({classId:t})=>{const n=e;return i(t),n},undo:({classId:e},t)=>{a(e),n(t)}},{title:(0,r.getElementLabel)(l.id),subtitle:({classLabel:e})=>(0,m.__)("class %s removed","elementor").replace("%s",e)})),[e,a,l.id,i,n]),c=(0,t.useCallback)((({classId:e})=>{i(e)}),[i]);return o?s:c}function oe(){const{element:e}=N(),{setId:n}=U(),{setClasses:l,getAppliedClasses:r}=ie();return(0,t.useCallback)((t=>{const o=r();if(o.includes(t))throw new Error(`Class ${t} is already applied to element ${e.id}, cannot re-apply.`);const a=[...o,t];l(a),n(t)}),[e.id,r,n,l])}function ae(){const{element:e}=N(),{id:n,setId:l}=U(),{setClasses:r,getAppliedClasses:o}=ie();return(0,t.useCallback)((t=>{const a=o();if(!a.includes(t))throw new Error(`Class ${t} is not applied to element ${e.id}, cannot unapply it.`);const i=a.filter((e=>e!==t));r(i),n===t&&l(i[0]??null)}),[n,e.id,o,l,r])}function ie(){const{element:e}=N(),n=L(),l=(0,E.isExperimentActive)(x.V_3_30);return(0,t.useMemo)((()=>({setClasses:t=>{(0,r.updateElementSettings)({id:e.id,props:{[n]:d.classesPropTypeUtil.create(t)},withHistory:!l}),l&&(0,p.setDocumentModifiedStatus)(!0)},getAppliedClasses:()=>(0,r.getElementSetting)(e.id,n)?.value||[]})),[n,e.id,l])}var se=[{key:"normal",value:null},{key:"hover",value:"hover"},{key:"focus",value:"focus"},{key:"active",value:"active"}];function ce({popupState:e,anchorEl:n,fixed:l}){const{provider:r}=te();return t.createElement(c.Menu,{MenuListProps:{dense:!0,sx:{minWidth:"160px"}},...(0,c.bindMenu)(e),anchorEl:n,anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{horizontal:"left",vertical:-4},onKeyDown:e=>{e.stopPropagation()},disableAutoFocusItem:!0},function({provider:e,closeMenu:n,fixed:l}){if(!e)return[];const r=o.stylesRepository.getProviderByKey(e),a=r?.actions,i=a?.update,s=!l,m=[i&&t.createElement(pe,{key:"rename-class",closeMenu:n}),s&&t.createElement(ue,{key:"unapply-class",closeMenu:n})].filter(Boolean);return m.length&&(m.unshift(t.createElement(c.MenuSubheader,{key:"provider-label",sx:{typography:"caption",color:"text.secondary",pb:.5,pt:1,textTransform:"capitalize"}},r?.labels?.singular)),m.push(t.createElement(c.Divider,{key:"provider-actions-divider"}))),m}({provider:r,closeMenu:e.close,fixed:l}),t.createElement(c.MenuSubheader,{sx:{typography:"caption",color:"text.secondary",pb:.5,pt:1}},(0,m.__)("States","elementor")),se.map((n=>t.createElement(me,{key:n.key,state:n.value,closeMenu:e.close}))))}function me({state:e,closeMenu:n,...l}){const{id:r,provider:i}=te(),{id:s,setId:u,setMetaState:p,meta:d}=U(),{state:E}=d,{userCan:f}=(0,o.useUserStylesCapability)(),b=function(e){const{meta:t}=U(),n=o.stylesRepository.all().find((t=>t.id===e));return Object.fromEntries(n?.variants.filter((e=>t.breakpoint===e.meta.breakpoint)).map((e=>[e.meta.state??"normal",!0]))??[])}(r),y=!e||f(i??"").updateProps,v=b[e??"normal"]??!1,g=!y&&!v,_=r===s,h=e===E&&_;return t.createElement(a.MenuListItem,{...l,selected:h,disabled:g,sx:{textTransform:"capitalize"},onClick:()=>{_||u(r),p(e),n()}},t.createElement(a.MenuItemInfotip,{showInfoTip:g,content:(0,m.__)("With your current role, you can only use existing states.","elementor")},t.createElement(c.Stack,{gap:.75,direction:"row",alignItems:"center"},v&&t.createElement(le,{"aria-label":(0,m.__)("Has style","elementor"),getColor:K(i??"")}),e??"normal")))}function ue({closeMenu:e,...n}){const{id:l,label:r}=te(),o=re();return l?t.createElement(a.MenuListItem,{...n,onClick:()=>{o({classId:l,classLabel:r}),e()}},(0,m.__)("Remove","elementor")):null}function pe({closeMenu:e}){const{handleRename:n,provider:l}=te(),{userCan:r}=(0,o.useUserStylesCapability)();if(!l)return null;const i=r(l).update;return t.createElement(a.MenuListItem,{disabled:!i,onClick:()=>{e(),n()}},t.createElement(a.MenuItemInfotip,{showInfoTip:!i,content:(0,m.__)("With your current role, you can use existing classes but can’t modify them.","elementor")},(0,m.__)("Rename","elementor")))}var de="tiny";function Ee(e){const{chipProps:n,icon:l,color:r,fixed:s,...u}=e,{id:p,provider:d,label:E,isActive:f,onClickActive:b,renameLabel:y,setError:v}=u,{meta:g,setMetaState:_}=U(),h=(0,c.usePopupState)({variant:"popover"}),[w,S]=(0,t.useState)(null),{onDelete:x,...C}=n,{userCan:T}=(0,o.useUserStylesCapability)(),{ref:I,isEditing:k,openEditMode:z,error:P,getProps:D}=(0,a.useEditable)({value:E,onSubmit:y,validation:fe,onError:v}),R=P?"error":r,L=d?o.stylesRepository.getProviderByKey(d)?.actions:null,V=Boolean(L?.update)&&T(d??"")?.update,A=f&&g.state;return t.createElement(t.Fragment,null,t.createElement(c.UnstableChipGroup,{ref:S,...C,"aria-label":`Edit ${E}`,role:"group",sx:e=>({"&.MuiChipGroup-root.MuiAutocomplete-tag":{margin:e.spacing(.125)}})},t.createElement(c.Chip,{size:de,label:k?t.createElement(a.EditableField,{ref:I,...D()}):t.createElement(a.EllipsisWithTooltip,{maxWidth:"10ch",title:E,as:"div"}),variant:!f||g.state||k?"standard":"filled",shape:"rounded",icon:l,color:R,onClick:()=>{A?_(null):V&&f?z():b(p)},"aria-pressed":f,sx:e=>({lineHeight:1,cursor:f&&V&&!A?"text":"pointer",borderRadius:.75*e.shape.borderRadius+"px","&.Mui-focusVisible":{boxShadow:"none !important"}})}),!k&&t.createElement(c.Chip,{icon:A?void 0:t.createElement(i.DotsVerticalIcon,{fontSize:"tiny"}),size:de,label:A?t.createElement(c.Stack,{direction:"row",gap:.5,alignItems:"center"},t.createElement(c.Typography,{variant:"inherit"},g.state),t.createElement(i.DotsVerticalIcon,{fontSize:"tiny"})):void 0,variant:"filled",shape:"rounded",color:R,...(0,c.bindTrigger)(h),"aria-label":(0,m.__)("Open CSS Class Menu","elementor"),sx:e=>({borderRadius:.75*e.shape.borderRadius+"px",paddingRight:0,...A?{}:{paddingLeft:0},".MuiChip-label":A?{paddingRight:0}:{padding:0}})})),t.createElement(ne,{...u,handleRename:z},t.createElement(ce,{popupState:h,anchorEl:w,fixed:s})))}var fe=e=>{const t=(0,o.validateStyleLabel)(e,"rename");return t.isValid?null:t.errorMessage},be="elementor-css-class-selector",ye={label:(0,m.__)("local","elementor"),value:null,fixed:!0,color:"accent",icon:t.createElement(i.MapPinIcon,null),provider:null},{Slot:ve,inject:ge}=(0,s.createLocation)();function _e(){const e=function(){const{element:e}=N();return(0,o.useProviders)().filter((e=>!!e.actions.updateProps)).flatMap((n=>{const l=(0,o.isElementsStylesProvider)(n.getKey()),r=n.actions.all({elementId:e.id});return l&&0===r.length?[ye]:r.map((e=>({label:e.label,value:e.id,fixed:l,color:G(n.getKey()),icon:l?t.createElement(i.MapPinIcon,null):null,provider:n.getKey()})))}))}(),{id:n,setId:l}=U(),s=(0,t.useRef)(null),[u,p]=(0,t.useState)(null),d=function(){const e=function(){const{id:e,setId:n}=U(),{element:l}=N(),o=(0,E.isExperimentActive)(x.V_3_30),a=oe(),i=ae(),s=(0,t.useMemo)((()=>(0,E.undoable)({do:({classId:t})=>{const n=e;return a(t),n},undo:({classId:e},t)=>{i(e),n(t)}},{title:(0,r.getElementLabel)(l.id),subtitle:({classLabel:e})=>(0,m.__)("class %s applied","elementor").replace("%s",e)})),[e,a,l.id,i,n]),c=(0,t.useCallback)((({classId:e})=>{a(e)}),[a]);return o?s:c}(),n=re();return(t,l,r)=>{if(r.value)switch(l){case"selectOption":e({classId:r.value,classLabel:r.label});break;case"removeOption":n({classId:r.value,classLabel:r.label})}}}(),{create:f,validate:b,entityName:y}=function(){const[e,n]=function(){const{id:e,setId:n}=U(),l=(0,E.isExperimentActive)(x.V_3_30),[r,a]=(0,o.useGetStylesRepositoryCreateAction)()??[null,null],i=r?.actions.delete,s=oe(),c=ae(),u=(0,t.useMemo)((()=>{if(r&&a)return(0,E.undoable)({do:({classLabel:t})=>{const n=e,l=a(t);return s(l),{prevActiveId:n,createdId:l}},undo:(e,{prevActiveId:t,createdId:l})=>{c(l),i?.(l),n(t)}},{title:(0,m.__)("Class","elementor"),subtitle:({classLabel:e})=>(0,m.__)("%s created","elementor").replace("%s",e)})}),[e,s,a,i,r,n,c]),p=(0,t.useCallback)((({classLabel:e})=>{if(!a)return;const t=a(e);s(t)}),[s,a]);return r&&u?l?[r,u]:[r,p]:[null,null]}();if(!e||!n)return{};return{create:e=>{n({classLabel:e})},validate:(t,n)=>function(e){return e.actions.all().length>=e.limit}(e)?{isValid:!1,errorMessage:(0,m.__)("You’ve reached the limit of 50 classes. Please remove an existing one to create a new class.","elementor")}:(0,o.validateStyleLabel)(t,n),entityName:e.labels.singular&&e.labels.plural?e.labels:void 0}}(),v=function(e){const{element:t}=N(),n=L(),l=(0,r.useElementSetting)(t.id,n)?.value||[],a=e.filter((e=>e.value&&l.includes(e.value)));return a.some((e=>e.provider&&(0,o.isElementsStylesProvider)(e.provider)))||a.unshift(ye),a}(e),g=v.find((e=>e.value===n))??ye,_=v.every((({fixed:e})=>e)),{userCan:h}=(0,o.useUserStylesCapability)(),w=!g.provider||h(g.provider).updateProps;return t.createElement(c.Stack,{p:2},t.createElement(c.Stack,{direction:"row",gap:1,alignItems:"center",justifyContent:"space-between"},t.createElement(c.FormLabel,{htmlFor:be,size:"small"},(0,m.__)("Classes","elementor")),t.createElement(c.Stack,{direction:"row",gap:1},t.createElement(ve,null))),t.createElement(a.WarningInfotip,{open:Boolean(u),text:u??"",placement:"bottom",width:s.current?.getBoundingClientRect().width,offset:[0,-15]},t.createElement(J,{id:be,ref:s,size:"tiny",placeholder:_?(0,m.__)("Type class name","elementor"):void 0,options:e,selected:v,entityName:y,onSelect:d,onCreate:f??void 0,validate:b??void 0,limitTags:50,renderEmptyState:he,getLimitTagsText:e=>t.createElement(c.Chip,{size:"tiny",variant:"standard",label:`+${e}`,clickable:!0}),renderTags:(e,n)=>e.map(((e,r)=>{const o=n({index:r}),a=e.value===g?.value;return t.createElement(Ee,{key:o.key,fixed:e.fixed,label:e.label,provider:e.provider,id:e.value,isActive:a,color:a&&e.color?e.color:"default",icon:e.icon,chipProps:o,onClickActive:()=>l(e.value),renameLabel:t=>{if(!e.value)throw new Error("Cannot rename a class without style id");return we(e.provider,{label:t,id:e.value})},setError:p})}))})),!w&&t.createElement(a.InfoAlert,{sx:{mt:1}},(0,m.__)("With your current role, you can use existing classes but can’t modify them.","elementor")))}var he=({searchValue:e,onClear:n})=>t.createElement(c.Box,{sx:{py:4}},t.createElement(c.Stack,{gap:1,alignItems:"center",color:"text.secondary",justifyContent:"center",sx:{px:2,m:"auto",maxWidth:"236px"}},t.createElement(i.ColorSwatchIcon,{sx:{transform:"rotate(90deg)"},fontSize:"large"}),t.createElement(c.Typography,{align:"center",variant:"subtitle2"},(0,m.__)("Sorry, nothing matched","elementor"),t.createElement("br",null),"“",e,"”."),t.createElement(c.Typography,{align:"center",variant:"caption",sx:{mb:2}},(0,m.__)("With your current role,","elementor"),t.createElement("br",null),(0,m.__)("you can only use existing classes.","elementor")),t.createElement(c.Link,{color:"text.secondary",variant:"caption",component:"button",onClick:n},(0,m.__)("Clear & try again","elementor")))),we=(e,t)=>{if(!e)return;const n=o.stylesRepository.getProviderByKey(e);return n?n.actions.update?.(t):void 0},Se="tiny",xe="tiny",Ce=(0,y.createMenu)({components:{Action:function({title:e,visible:n=!0,icon:l,onClick:r}){return n?t.createElement(c.Tooltip,{placement:"top",title:e,arrow:!0},t.createElement(c.IconButton,{"aria-label":e,size:Se,onClick:r},t.createElement(l,{fontSize:Se}))):null},PopoverAction:function({title:e,visible:n=!0,icon:l,content:r}){const o=(0,t.useId)(),a=(0,c.usePopupState)({variant:"popover",popupId:`elementor-popover-action-${o}`});return n?t.createElement(t.Fragment,null,t.createElement(c.Tooltip,{placement:"top",title:e},t.createElement(c.IconButton,{"aria-label":e,key:o,size:xe,...(0,c.bindToggle)(a)},t.createElement(l,{fontSize:xe}))),t.createElement(c.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},PaperProps:{sx:{my:2.5}},...(0,c.bindPopover)(a)},t.createElement(r,{close:a.close}))):null}}});function Te(){return t.createElement(c.Box,{role:"alert",sx:{minHeight:"100%",p:2}},t.createElement(c.Alert,{severity:"error",sx:{mb:2,maxWidth:400,textAlign:"center"}},t.createElement("strong",null,"Something went wrong")))}var Ie=(0,t.createContext)(void 0),ke=(0,c.styled)("div")`
	height: 100%;
	overflow-y: auto;
`;function ze({children:e}){const[n,l]=(0,t.useState)("up"),r=(0,t.useRef)(null),o=(0,t.useRef)(0);return(0,t.useEffect)((()=>{const e=r.current;if(!e)return;const t=()=>{const{scrollTop:t}=e;t>o.current?l("down"):t<o.current&&l("up"),o.current=t};return e.addEventListener("scroll",t),()=>{e.removeEventListener("scroll",t)}})),t.createElement(Ie.Provider,{value:{direction:n}},t.createElement(ke,{ref:r},e))}var Pe={defaultSectionsExpanded:{settings:["Content","Settings"],style:[]},defaultTab:"settings"},De=(0,t.createContext)({"e-div-block":{defaultSectionsExpanded:Pe.defaultSectionsExpanded,defaultTab:"style"},"e-flexbox":{defaultSectionsExpanded:Pe.defaultSectionsExpanded,defaultTab:"style"},"e-divider":{defaultSectionsExpanded:Pe.defaultSectionsExpanded,defaultTab:"style"}}),Re=()=>{const{element:e}=N();return(0,t.useContext)(De)[e.type]||Pe},Le=(e,n)=>{const{element:l}=N(),r=(0,E.isExperimentActive)(x.V_3_30),o=`elementor/editor-state/${l.id}/${e}`,a=r?(0,b.getSessionStorageItem)(o):n,[i,s]=(0,t.useState)(a??n);return[i,e=>{(0,b.setSessionStorageItem)(o,e),s(e)}]},Ve={image:{component:e.ImageControl,layout:"full",propTypeUtil:d.imagePropTypeUtil},"svg-media":{component:e.SvgMediaControl,layout:"full",propTypeUtil:d.imageSrcPropTypeUtil},text:{component:e.TextControl,layout:"full",propTypeUtil:d.stringPropTypeUtil},textarea:{component:e.TextAreaControl,layout:"full",propTypeUtil:d.stringPropTypeUtil},size:{component:e.SizeControl,layout:"two-columns",propTypeUtil:d.sizePropTypeUtil},select:{component:e.SelectControl,layout:"two-columns",propTypeUtil:d.stringPropTypeUtil},link:{component:e.LinkControl,layout:"custom",propTypeUtil:d.linkPropTypeUtil},url:{component:e.UrlControl,layout:"full",propTypeUtil:d.stringPropTypeUtil},switch:{component:e.SwitchControl,layout:"two-columns",propTypeUtil:d.booleanPropTypeUtil},repeatable:{component:e.RepeatableControl,layout:"full",propTypeUtil:void 0},"key-value":{component:e.KeyValueControl,layout:"full",propTypeUtil:d.keyValuePropTypeUtil}},Ae=e=>Ve[e]?.component,Ne=({props:e,type:n})=>{const l=Ae(n),{element:r}=N();if(!l)throw new B({context:{controlType:n}});return t.createElement(l,{...e,context:{elementId:r.id}})},Be=({children:e,layout:n})=>"custom"===n?e:t.createElement(Oe,{layout:n},e),Oe=(0,c.styled)(c.Box,{shouldForwardProp:e=>!["layout"].includes(e)})((({layout:e,theme:t})=>({display:"grid",gridGap:t.spacing(1),...Me(e)}))),Me=e=>({justifyContent:"space-between",gridTemplateColumns:{full:"minmax(0, 1fr)","two-columns":"repeat(2, minmax(0, 1fr))"}[e]}),Fe=({schema:e})=>({key:"",kind:"object",meta:{},settings:{},default:null,shape:e}),$e=({bind:n,children:l,propDisplayName:o})=>{const{element:a,elementType:i}=N(),s=(0,r.useElementSettings)(a.id,Object.keys(i.propsSchema)),c={[n]:s?.[n]},u=Fe({schema:i.propsSchema}),f=function({propKey:e,elementId:n,propDisplayName:l}){return(0,t.useMemo)((()=>(0,E.undoable)({do:({newValue:t})=>{const l=(0,r.getElementSetting)(n,e);return(0,r.updateElementSettings)({id:n,props:{...t},withHistory:!1}),(0,p.setDocumentModifiedStatus)(!0),{[e]:l}},undo:({},e)=>{(0,r.updateElementSettings)({id:n,props:e,withHistory:!1})}},{title:(0,r.getElementLabel)(n),subtitle:(0,m.__)("%s edited","elementor").replace("%s",l)})),[e,n,l])}({propKey:n,elementId:a.id,propDisplayName:o});return t.createElement(e.PropProvider,{propType:u,value:c,setValue:e=>{(0,E.isExperimentActive)(x.V_3_31)?f({newValue:e}):(0,r.updateElementSettings)({id:a.id,props:e})},isDisabled:e=>function(e,t){const n=e.dependencies?.filter((({effect:e})=>"disable"===e))||[];if(!n.length)return!1;if(n.length>1)throw new Error("Multiple disabling dependencies are not supported.");return(0,d.shouldApplyEffect)(n[0],t)}(e,s)},t.createElement(e.PropKeyProvider,{bind:n},l))},je=(0,t.createContext)(null),Ue=()=>{const e=(0,t.useContext)(je);return e?.current?.offsetWidth??320},We=(0,c.styled)(i.ChevronDownIcon,{shouldForwardProp:e=>"open"!==e})((({theme:e,open:t})=>({transform:t?"rotate(180deg)":"rotate(0deg)",transition:e.transitions.create("transform",{duration:e.transitions.duration.standard})}))),Ge=(0,c.styled)("div")`
	position: absolute;
	top: 0;
	right: ${({theme:e})=>e.spacing(3)};
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
`,Ke=({children:e,defaultOpen:n=!1,titleEnd:l=null})=>{const[r,o]=(0,t.useState)(n);return t.createElement(c.Stack,null,t.createElement(c.Stack,{sx:{position:"relative"}},t.createElement(c.Button,{fullWidth:!0,size:"small",color:"secondary",variant:"outlined",onClick:()=>{o((e=>!e))},endIcon:t.createElement(We,{open:r}),sx:{my:.5}},r?(0,m.__)("Show less","elementor"):(0,m.__)("Show more","elementor")),l&&t.createElement(Ge,null,He(l,r))),t.createElement(c.Collapse,{in:r,timeout:"auto",unmountOnExit:!0},e))};function He(e,t){return"function"==typeof e?e(t):e}function Je({title:e,children:n,defaultExpanded:l=!1,titleEnd:r}){const[o,a]=Le(e,!!l),i=(0,t.useRef)(null),s=(0,t.useId)(),m=`label-${s}`,u=`content-${s}`,p=(0,E.isExperimentActive)(x.V_3_30);return t.createElement(t.Fragment,null,t.createElement(c.ListItemButton,{id:m,"aria-controls":u,onClick:()=>{a(!o)},sx:{"&:hover":{backgroundColor:"transparent"}}},t.createElement(c.Stack,{direction:"row",alignItems:"center",justifyItems:"start",flexGrow:1,gap:.5},t.createElement(c.ListItemText,{secondary:e,secondaryTypographyProps:{color:"text.primary",variant:"caption",fontWeight:"bold"},sx:{flexGrow:0,flexShrink:1,marginInlineEnd:1}}),p?He(r,o):null),t.createElement(We,{open:o,color:"secondary",fontSize:"tiny"})),t.createElement(c.Collapse,{id:u,"aria-labelledby":m,in:o,timeout:"auto",unmountOnExit:!0},t.createElement(je.Provider,{value:i},t.createElement(c.Stack,{ref:i,gap:2.5,p:2},n))),t.createElement(c.Divider,null))}function Ye(e){return t.createElement(c.List,{disablePadding:!0,component:"div",...e})}var qe=()=>{const{elementType:e,element:n}=N(),l=Re();return t.createElement(b.SessionStorageProvider,{prefix:n.id},t.createElement(Ye,null,e.controls.map((({type:e,value:n},r)=>{return"control"===e?t.createElement(Xe,{key:n.bind,control:n}):"section"===e?t.createElement(Je,{title:n.label,key:e+"."+r,defaultExpanded:(o=n.label,!(0,E.isExperimentActive)(x.V_3_30)||l.defaultSectionsExpanded.settings?.includes(o))},n.items?.map((e=>"control"===e.type?t.createElement(Xe,{key:e.value.bind,control:e.value}):null))):null;var o}))))},Xe=({control:n})=>{if(!Ae(n.type))return null;const l=n.meta?.layout||(r=n.type,Ve[r].layout);var r;const o=function(e){if(e.childControlType){const t=Ae(e.childControlType),n=(e=>Ve[e]?.propTypeUtil)(e.childControlType);e={...e,childControlConfig:{component:t,props:e.childControlProps||{},propTypeUtil:n}}}return e}(n.props);return"custom"===l&&(o.label=n.label),t.createElement($e,{bind:n.bind,propDisplayName:n.label||n.bind},n.meta?.topDivider&&t.createElement(c.Divider,null),t.createElement(Be,{layout:l},n.label&&"custom"!==l?t.createElement(e.ControlFormLabel,null,n.label):null,t.createElement(Ne,{type:n.type,props:o})))},Ze=()=>{const{provider:e}=U(),[,n]=(0,t.useReducer)((e=>!e),!1);(0,t.useEffect)((()=>e?.subscribe(n)),[e])},Qe="normal",et=e=>e??Qe,tt=e=>e??"desktop";function nt(e,t){const n=function(e){const t={},n=(e,l)=>{const{id:r,children:o}=e;t[r]=l?[...l]:[],o?.forEach((e=>{n(e,[...t[r]??[],r])}))};return n(e),t}(t),l={};return t=>{const{breakpoint:r,state:o}=t,a=et(o),i=tt(r);if(l[i]?.[a])return l[i][a].snapshot;const s=[...n[i],r];return s.forEach(((t,n)=>{const r=n>0?s[n-1]:null;((t,n,r)=>{const o=tt(t),a=et(r);l[o]||(l[o]={[Qe]:lt(e({breakpoint:t,state:null}),n,{},null)}),r&&!l[o][a]&&(l[o][a]=lt(e({breakpoint:t,state:r}),n,l[o],r))})(t,r?l[r]:void 0,o)})),l[i]?.[a]?.snapshot}}function lt(e,t,n,l){const r=function(e){const t={};return e.forEach((e=>{const{variant:{props:n}}=e;Object.entries(n).forEach((([n,l])=>{const r=(0,d.filterEmptyValues)(l);if(null===r)return;t[n]||(t[n]=[]);const o={...e,value:r};t[n].push(o)}))})),{snapshot:t,stateSpecificSnapshot:t}}(e);return l?{snapshot:rt([r.snapshot,t?.[l]?.stateSpecificSnapshot,n[Qe]?.snapshot]),stateSpecificSnapshot:rt([r.stateSpecificSnapshot,t?.[l]?.stateSpecificSnapshot])}:{snapshot:rt([r.snapshot,t?.[Qe]?.snapshot]),stateSpecificSnapshot:void 0}}function rt(e){const t={};return e.filter(Boolean).forEach((e=>Object.entries(e).forEach((([e,n])=>{t[e]||(t[e]=[]),t[e]=t[e].concat(n)})))),t}function ot(e,t,n){return e&&"object"==typeof e?function(e,t){return!!e&&(0,d.isTransformable)(t)&&e.key!==t.$$type}(n,e)?e:t.reduce(((e,t)=>e?(0,d.isTransformable)(e)?e.value?.[t]??null:"object"==typeof e?e[t]??null:null:null),e):null}var at=(e,t)=>e&&"union"===e.kind?Object.values(e.prop_types).find((e=>!!t.reduce(((e,t)=>{if("object"!==e?.kind)return null;const{shape:n}=e;return n[t]?n[t]:null}),e)))??null:null,it=(0,t.createContext)(null);function st({children:e}){const n=ut(),l=(0,v.getBreakpointsTree)(),{getSnapshot:r,getInheritanceChain:o}=function(e,t){const n=function(e){const t={};return e.forEach((e=>{const n=W(e.id)?.getKey()??null;e.variants.forEach((l=>{const{meta:r}=l,{state:o,breakpoint:a}=r,i=tt(a),s=et(o);t[i]||(t[i]={});const c=t[i];c[s]||(c[s]=[]),c[s].push({style:e,variant:l,provider:n})}))})),t}(e);return{getSnapshot:nt((({breakpoint:e,state:t})=>n?.[tt(e)]?.[et(t)]??[]),t),getInheritanceChain:(e,t,n)=>{const[l,...r]=t;let o=e[l]??[];if(r.length>0){const e=at(n,r);o=o.map((({value:t,...n})=>({...n,value:ot(t,r,e)}))).filter((({value:e})=>!(0,d.isEmpty)(e)))}return o}}}(n,l);return t.createElement(it.Provider,{value:{getSnapshot:r,getInheritanceChain:o}},e)}function ct(){const e=(0,t.useContext)(it),{meta:n}=U();if(!e)throw new Error("useStylesInheritanceSnapshot must be used within a StyleInheritanceProvider");return n?e.getSnapshot(n)??null:null}function mt(e){const n=(0,t.useContext)(it);if(!n)throw new Error("useStylesInheritanceChain must be used within a StyleInheritanceProvider");const l=(0,g.getStylesSchema)(),r=l?.[e[0]],o=ct();return o?n.getInheritanceChain(o,e,r):[]}var ut=()=>{const{element:e}=N(),t=L(),n=pt();Ze();const l=(0,r.useElementSetting)(e.id,t),a=d.classesPropTypeUtil.extract(l)??[];return o.stylesRepository.all().filter((e=>[...n,...a].includes(e.id)))},pt=()=>{const{elementType:e}=N(),t=(0,r.getWidgetsCache)(),n=t?.[e.key];return Object.keys(n?.base_styles??{})};function dt(e){const{element:{id:n}}=N(),{id:l,meta:a,provider:i,canEdit:s}=U(),c=function({elementId:e,meta:n}){return(0,t.useMemo)((()=>{const t=(0,E.isExperimentActive)(x.V_3_31);return(0,E.undoable)({do:({provider:t,styleId:l,props:r})=>{if(!t.actions.updateProps)throw new M({context:{providerKey:t.getKey()}});const o=function(e,t){if(!e)return{};const n=(0,g.getVariantByMeta)(e,t);return structuredClone(n?.props??{})}(t.actions.get(l,{elementId:e}),n);return t.actions.updateProps({id:l,meta:n,props:r},{elementId:e}),o},undo:({provider:t,styleId:l},r)=>{t.actions.updateProps?.({id:l,meta:n,props:r},{elementId:e})}},{title:({provider:n})=>t?(0,o.isElementsStylesProvider)(n.getKey())?bt.title({elementId:e}):ft.title({provider:n}):Et.title({elementId:e}),subtitle:({provider:n,styleId:l,propDisplayName:r})=>t?(0,o.isElementsStylesProvider)(n.getKey())?bt.subtitle({propDisplayName:r}):ft.subtitle({provider:n,styleId:l,elementId:e,propDisplayName:r}):Et.subtitle})}),[e,n])}({elementId:n,meta:a}),m=function({elementId:e,meta:n}){const l=L();return(0,t.useMemo)((()=>{const t=(0,E.isExperimentActive)(x.V_3_31),a={elementId:e,classesProp:l,meta:n,label:o.ELEMENTS_STYLES_RESERVED_LABEL};return(0,E.undoable)({do:({props:e})=>(0,r.createElementStyle)({...a,props:e}),undo:(t,n)=>{(0,r.deleteElementStyle)(e,n)},redo:({props:e},t)=>(0,r.createElementStyle)({...a,props:e,styleId:t})},{title:()=>t?bt.title({elementId:e}):Et.title({elementId:e}),subtitle:({propDisplayName:e})=>t?bt.subtitle({propDisplayName:e}):Et.subtitle})}),[l,e,n])}({elementId:n,meta:a});Ze();const u=function({styleId:e,elementId:t,provider:n,meta:l,propNames:r}){if(!n||!e)return null;const o=n.actions.get(e,{elementId:t});if(!o)throw new F({context:{styleId:e,providerKey:n.getKey()}});const a=(0,g.getVariantByMeta)(o,l);return Object.fromEntries(r.map((e=>[e,a?.props[e]??null])))}({elementId:n,styleId:l,provider:i,meta:a,propNames:e});return{values:u,setValues:(e,{history:{propDisplayName:t}})=>{null===l?m({props:e,propDisplayName:t}):c({provider:i,styleId:l,props:e,propDisplayName:t})},canEdit:s}}var Et={title:({elementId:e})=>(0,r.getElementLabel)(e),subtitle:(0,m.__)("Style edited","elementor")},ft={title:({provider:e})=>{const t=e.labels?.singular;return t?(n=t).charAt(0).toUpperCase()+n.slice(1):(0,m.__)("Style","elementor");var n},subtitle:({provider:e,styleId:t,elementId:n,propDisplayName:l})=>{const r=e.actions.get(t,{elementId:n})?.label;if(!r)throw new Error(`Style ${t} not found`);return(0,m.__)("%s$1 %s$2 edited","elementor").replace("%s$1",r).replace("%s$2",l)}},bt={title:({elementId:e})=>(0,r.getElementLabel)(e),subtitle:({propDisplayName:e})=>(0,m.__)("%s edited","elementor").replace("%s",e)};function yt(e,t){const{values:n,setValues:l,canEdit:r}=dt([e]);return{value:n?.[e]??null,setValue:n=>{l({[e]:n},t)},canEdit:r}}var vt=new Set(["background-color-overlay","background-image-overlay","background-gradient-overlay","gradient-color-stop","color-stop","background-image-position-offset","background-image-size-scale","image-src","image","background-overlay"]),gt=()=>(0,E.isExperimentActive)("e_v_3_30");function _t(){const e="rtl"===(0,c.useTheme)().direction;return{isSiteRtl:!!(()=>{const e=window;return e.elementorFrontend?.config??{}})()?.is_rtl,isUiRtl:e}}var ht=async(e,n,l)=>{try{const r=await l({props:{[n]:e.value}}),o=r?.[n]??r;return(0,t.isValidElement)(o)?o:"object"==typeof o?JSON.stringify(o):String(o)}catch{return""}},wt=(0,_.createTransformersRegistry)(),St={widescreen:i.WidescreenIcon,desktop:i.DesktopIcon,laptop:i.LaptopIcon,tablet_extra:i.TabletLandscapeIcon,tablet:i.TabletPortraitIcon,mobile_extra:i.MobileLandscapeIcon,mobile:i.MobilePortraitIcon},xt=({breakpoint:e})=>{const n=(0,v.useBreakpoints)(),l=e||"desktop",r=St[l];if(!r)return null;const o=n.find((e=>e.id===l))?.label;return t.createElement(c.Tooltip,{title:o,placement:"top"},t.createElement(r,{fontSize:"tiny",sx:{mt:"2px"}}))},Ct="tiny",Tt=({displayLabel:e,provider:n})=>{const l=n===o.ELEMENTS_BASE_STYLES_PROVIDER_KEY?t.createElement(c.Tooltip,{title:(0,m.__)("Inherited from base styles","elementor"),placement:"top"},t.createElement(i.InfoCircleIcon,{fontSize:Ct})):void 0;return t.createElement(c.Chip,{label:e,size:Ct,color:G(n),variant:"standard",state:"enabled",icon:l,sx:e=>({lineHeight:1,flexWrap:"nowrap",alignItems:"center",borderRadius:.75*e.shape.borderRadius+"px",flexDirection:"row-reverse",".MuiChip-label":{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}})})},It=({index:e,value:n})=>t.createElement(c.Typography,{variant:"caption",color:"text.tertiary",sx:{mt:"1px",textDecoration:0===e?"none":"line-through",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},n),kt=()=>t.createElement(c.Box,{display:"flex",gap:.5,alignItems:"center"}),zt=({inheritanceChain:e,propType:n,path:l,label:r,children:i})=>{const[s,u]=(0,t.useState)(!1),p=()=>u(!1),d=l.join("."),E=Ue()+32,f=(0,t.useMemo)((()=>(0,_.createPropsResolver)({transformers:wt,schema:{[d]:n}})),[d,n]),b=((e,n,l)=>{const[r,a]=(0,t.useState)([]);return(0,t.useEffect)((()=>{(async()=>{const t=(await Promise.all(e.filter((({style:e})=>e)).map(((e,t)=>(async(e,t,n,l)=>{const{variant:{meta:{state:r,breakpoint:o}},style:{label:a,id:i}}=e,s=`${a}${r?":"+r:""}`;return{id:i?i+(r??""):t,provider:e.provider||"",breakpoint:o??"desktop",displayLabel:s,value:await ht(e,n,l)}})(e,t,n,l))))).map((e=>({...e,displayLabel:o.ELEMENTS_BASE_STYLES_PROVIDER_KEY!==e.provider?e.displayLabel:(0,m.__)("Base","elementor")}))).filter((e=>!e.value||""!==e.displayLabel)).slice(0,2);a(t)})()}),[e,n,l]),r})(e,d,f),y=t.createElement(c.ClickAwayListener,{onClickAway:p},t.createElement(c.Card,{elevation:0,sx:{width:E-32+"px",maxWidth:496,overflowX:"hidden"}},t.createElement(c.CardContent,{sx:{display:"flex",gap:.5,flexDirection:"column",p:0,"&:last-child":{pb:0}}},t.createElement(a.PopoverHeader,{title:(0,m.__)("Style origin","elementor"),onClose:p}),t.createElement(c.Stack,{gap:1.5,sx:{pl:2,pr:1,pb:2,overflowX:"hidden",overflowY:"auto"},role:"list"},b.map(((e,n)=>t.createElement(c.Box,{key:e.id,display:"flex",gap:.5,role:"listitem","aria-label":(0,m.__)("Inheritance item: %s","elementor").replace("%s",e.displayLabel)},t.createElement(c.Box,{display:"flex",gap:.5,sx:{flexWrap:"wrap",width:"100%"}},t.createElement(xt,{breakpoint:e.breakpoint}),t.createElement(Tt,{displayLabel:e.displayLabel,provider:e.provider}),t.createElement(It,{index:n,value:e.value})),t.createElement(kt,null))))))));return t.createElement(Pt,{showInfotip:s,onClose:p,infotipContent:y},t.createElement(c.IconButton,{onClick:()=>u((e=>!e)),"aria-label":r,sx:{my:"-1px"}},i))};function Pt({children:e,showInfotip:n,onClose:l,infotipContent:r}){const{isSiteRtl:o}=_t(),a=o?9999999:-9999999;return n?t.createElement(t.Fragment,null,t.createElement(c.Backdrop,{open:n,onClick:l,sx:{backgroundColor:"transparent",zIndex:e=>e.zIndex.modal-1}}),t.createElement(c.Infotip,{placement:"top",content:r,open:n,onClose:l,disableHoverListener:!0,componentsProps:{tooltip:{sx:{mx:2}}},slotProps:{popper:{modifiers:[{name:"offset",options:{offset:[a,0]}}]}}},e)):t.createElement(c.Tooltip,{title:(0,m.__)("Style origin","elementor"),placement:"top"},e)}var Dt=()=>{const{path:n,propType:l}=(0,e.useBoundProp)(),r=(0,E.isExperimentActive)(x.V_3_30)?n:n.slice(0,1),o=mt(r);return o.length?t.createElement(Rt,{inheritanceChain:o,path:r,propType:l}):null},Rt=({inheritanceChain:e,path:n,propType:l})=>{const{id:r,provider:a,meta:i}=U(),s=r?((e,t,n)=>e.find((({style:e,variant:{meta:{breakpoint:l,state:r}}})=>e.id===t&&l===n.breakpoint&&r===n.state)))(e,r,i):null,u=!(0,d.isEmpty)(s?.value),[p]=e;if(!(0,E.isExperimentActive)(x.V_3_31)&&p.provider===o.ELEMENTS_BASE_STYLES_PROVIDER_KEY)return null;const f=s===p,b=Lt({isFinalValue:f,hasValue:u}),y={getColor:f&&a?K(a.getKey()):void 0,isOverridden:!(!u||f)||void 0};return gt()?t.createElement(zt,{inheritanceChain:e,path:n,propType:l,label:b},t.createElement(le,{...y})):t.createElement(c.Tooltip,{title:(0,m.__)("Style origin","elementor"),placement:"top"},t.createElement(le,{...y,"aria-label":b}))},Lt=({isFinalValue:e,hasValue:t})=>e?(0,m.__)("This is the final value","elementor"):t?(0,m.__)("This value is overridden by another style","elementor"):(0,m.__)("This has value from another style","elementor"),Vt=({bind:n,placeholder:l,propDisplayName:r,children:o})=>{const{value:a,setValue:i,canEdit:s}=yt(n,{history:{propDisplayName:r}}),c=(0,E.isExperimentActive)("e_v_3_31"),m=mt([n]),u=(0,g.getStylesSchema)(),p=Fe({schema:u}),d={[n]:a},[f]=m,b={[n]:c?f?.value:l};return t.createElement(e.ControlAdornmentsProvider,{items:[{id:"styles-inheritance",Adornment:Dt}]},t.createElement(e.PropProvider,{propType:p,value:d,setValue:e=>{i(e[n])},placeholder:b,isDisabled:()=>!s},t.createElement(e.PropKeyProvider,{bind:n},o)))},At=({gap:e=2,sx:n,children:l})=>t.createElement(c.Stack,{gap:e,sx:{...n}},l),Nt=(0,m.__)("Background","elementor"),Bt=()=>t.createElement(At,null,t.createElement(Vt,{bind:"background",propDisplayName:Nt},t.createElement(e.BackgroundControl,null))),Ot=()=>t.createElement(c.Divider,{sx:{my:.5}}),Mt="tiny",Ft=({isAdded:e,onAdd:n,onRemove:l,children:r,disabled:o,renderLabel:a})=>t.createElement(At,null,t.createElement(c.Stack,{direction:"row",sx:{justifyContent:"space-between",alignItems:"center",marginInlineEnd:-.75}},a(),e?t.createElement(c.IconButton,{size:Mt,onClick:l,"aria-label":"Remove",disabled:o},t.createElement(i.MinusIcon,{fontSize:Mt})):t.createElement(c.IconButton,{size:Mt,onClick:n,"aria-label":"Add",disabled:o},t.createElement(i.PlusIcon,{fontSize:Mt}))),t.createElement(c.Collapse,{in:e,unmountOnExit:!0},t.createElement(At,null,r))),$t=({children:n})=>t.createElement(c.Stack,{direction:"row",alignItems:"center",justifyItems:"start",gap:.25},t.createElement(e.ControlFormLabel,null,n),t.createElement(e.ControlAdornments,null)),jt=t.forwardRef(((e,n)=>{const{direction:l="row",children:r,label:o}=e,a="row"===l?Ut:Wt;return t.createElement(a,{label:o,ref:n,children:r})})),Ut=t.forwardRef((({label:e,children:n},l)=>t.createElement(c.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap",ref:l},t.createElement(c.Grid,{item:!0,xs:6},t.createElement($t,null,e)),t.createElement(c.Grid,{item:!0,xs:6,sx:e=>({width:`calc(50% - ${e.spacing(2)})`})},n)))),Wt=t.forwardRef((({label:e,children:n},l)=>t.createElement(c.Stack,{gap:.75,ref:l},t.createElement($t,null,e),n))),Gt=(0,m.__)("Border color","elementor"),Kt=()=>t.createElement(Vt,{bind:"border-color",propDisplayName:Gt},t.createElement(jt,{label:Gt},t.createElement(e.ColorControl,null))),Ht=(0,m.__)("Border type","elementor"),Jt=[{value:"none",label:(0,m.__)("None","elementor")},{value:"solid",label:(0,m.__)("Solid","elementor")},{value:"dashed",label:(0,m.__)("Dashed","elementor")},{value:"dotted",label:(0,m.__)("Dotted","elementor")},{value:"double",label:(0,m.__)("Double","elementor")},{value:"groove",label:(0,m.__)("Groove","elementor")},{value:"ridge",label:(0,m.__)("Ridge","elementor")},{value:"inset",label:(0,m.__)("Inset","elementor")},{value:"outset",label:(0,m.__)("Outset","elementor")}],Yt=()=>t.createElement(Vt,{bind:"border-style",propDisplayName:Ht},t.createElement(jt,{label:Ht},t.createElement(e.SelectControl,{options:Jt}))),qt=(0,m.__)("Border width","elementor"),Xt=(0,c.withDirection)(i.SideRightIcon),Zt=(0,c.withDirection)(i.SideLeftIcon),Qt=e=>[{label:(0,m.__)("Top","elementor"),icon:t.createElement(i.SideTopIcon,{fontSize:"tiny"}),bind:"block-start"},{label:e?(0,m.__)("Left","elementor"):(0,m.__)("Right","elementor"),icon:t.createElement(Xt,{fontSize:"tiny"}),bind:"inline-end"},{label:(0,m.__)("Bottom","elementor"),icon:t.createElement(i.SideBottomIcon,{fontSize:"tiny"}),bind:"block-end"},{label:e?(0,m.__)("Right","elementor"):(0,m.__)("Left","elementor"),icon:t.createElement(Zt,{fontSize:"tiny"}),bind:"inline-start"}],en=()=>{const{isSiteRtl:n}=_t();return t.createElement(Vt,{bind:"border-width",propDisplayName:qt},t.createElement(e.EqualUnequalSizesControl,{items:Qt(n),label:qt,icon:t.createElement(i.SideAllIcon,{fontSize:"tiny"}),tooltipLabel:(0,m.__)("Adjust borders","elementor"),multiSizePropTypeUtil:d.borderWidthPropTypeUtil}))},tn=(0,m.__)("Border","elementor"),nn={"border-width":{$$type:"size",value:{size:1,unit:"px"}},"border-color":{$$type:"color",value:"#000000"},"border-style":{$$type:"string",value:"solid"}},ln=()=>{const{values:n,setValues:l,canEdit:r}=dt(Object.keys(nn)),o={history:{propDisplayName:tn}},a=Object.values(n??{}).some(Boolean);return t.createElement(Ft,{isAdded:a,onAdd:()=>{l(nn,o)},onRemove:()=>{l({"border-width":null,"border-color":null,"border-style":null},o)},disabled:!r,renderLabel:()=>t.createElement(e.ControlFormLabel,null,tn)},t.createElement(en,null),t.createElement(Kt,null),t.createElement(Yt,null))},rn=({children:e})=>{const{isSiteRtl:n}=_t();return t.createElement(c.DirectionProvider,{rtl:n},t.createElement(c.ThemeProvider,null,e))},on=(0,m.__)("Border radius","elementor"),an=(0,c.withDirection)(i.RadiusTopLeftIcon),sn=(0,c.withDirection)(i.RadiusTopRightIcon),cn=(0,c.withDirection)(i.RadiusBottomLeftIcon),mn=(0,c.withDirection)(i.RadiusBottomRightIcon),un=e=>e?(0,m.__)("Top right","elementor"):(0,m.__)("Top left","elementor"),pn=e=>e?(0,m.__)("Top left","elementor"):(0,m.__)("Top right","elementor"),dn=e=>e?(0,m.__)("Bottom right","elementor"):(0,m.__)("Bottom left","elementor"),En=e=>e?(0,m.__)("Bottom left","elementor"):(0,m.__)("Bottom right","elementor"),fn=e=>[{label:un(e),icon:t.createElement(an,{fontSize:"tiny"}),bind:"start-start"},{label:pn(e),icon:t.createElement(sn,{fontSize:"tiny"}),bind:"start-end"},{label:dn(e),icon:t.createElement(cn,{fontSize:"tiny"}),bind:"end-start"},{label:En(e),icon:t.createElement(mn,{fontSize:"tiny"}),bind:"end-end"}],bn=()=>{const{isSiteRtl:n}=_t();return t.createElement(rn,null,t.createElement(Vt,{bind:"border-radius",propDisplayName:on},t.createElement(e.EqualUnequalSizesControl,{items:fn(n),label:on,icon:t.createElement(i.BorderCornersIcon,{fontSize:"tiny"}),tooltipLabel:(0,m.__)("Adjust corners","elementor"),multiSizePropTypeUtil:d.borderRadiusPropTypeUtil})))},yn=()=>t.createElement(At,null,t.createElement(bn,null),t.createElement(Ot,null),t.createElement(ln,null)),vn=(0,m.__)("Opacity","elementor"),gn=()=>{const n=(0,t.useRef)(null);return t.createElement(Vt,{bind:"opacity",propDisplayName:vn},t.createElement(jt,{ref:n,label:vn},t.createElement(e.SizeControl,{units:["%"],anchorRef:n,defaultUnit:"%"})))},_n=(0,m.__)("Box shadow","elementor"),hn=(0,m.__)("Filter","elementor"),wn=(0,m.__)("Transform","elementor"),Sn=()=>{const n=(0,E.isExperimentActive)(x.V_3_31);return t.createElement(At,null,n&&t.createElement(t.Fragment,null,t.createElement(gn,null),t.createElement(Ot,null)),t.createElement(Vt,{bind:"box-shadow",propDisplayName:_n},t.createElement(e.BoxShadowRepeaterControl,null)),n&&t.createElement(t.Fragment,null,t.createElement(Ot,null),t.createElement(Vt,{bind:"transform",propDisplayName:wn},t.createElement(e.TransformRepeaterControl,null)),t.createElement(Ot,null),t.createElement(Vt,{bind:"filter",propDisplayName:hn},t.createElement(e.FilterRepeaterControl,null))))},xn=(0,m.__)("Flex direction","elementor"),Cn={row:0,column:90,"row-reverse":180,"column-reverse":270},Tn={row:0,column:-90,"row-reverse":-180,"column-reverse":-270},In=({icon:e,size:n,isClockwise:l=!0,offset:r=0,disableRotationForReversed:o=!1})=>{const a=(0,t.useRef)(kn(l,r,o));return a.current=kn(l,r,o,a),t.createElement(e,{fontSize:n,sx:{transition:".3s",rotate:`${a.current}deg`}})},kn=(e,t,n,l)=>{const{value:r}=yt("flex-direction",{history:{propDisplayName:xn}}),o="rtl"===(0,c.useTheme)().direction?-1:1,a=e?Cn:Tn,i=r?.value||"row",s=l?l.current*o:a[i]+t,m=((a[i]+t-s+360)%360+180)%360-180;return n&&["row-reverse","column-reverse"].includes(i)?0:(s+m)*o},zn=(0,m.__)("Align content","elementor"),Pn=(0,c.withDirection)(i.JustifyTopIcon),Dn=(0,c.withDirection)(i.JustifyBottomIcon),Rn={isClockwise:!1,offset:0,disableRotationForReversed:!0},Ln=[{value:"start",label:(0,m.__)("Start","elementor"),renderContent:({size:e})=>t.createElement(In,{icon:Pn,size:e,...Rn}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:e})=>t.createElement(In,{icon:i.JustifyCenterIcon,size:e,...Rn}),showTooltip:!0},{value:"end",label:(0,m.__)("End","elementor"),renderContent:({size:e})=>t.createElement(In,{icon:Dn,size:e,...Rn}),showTooltip:!0},{value:"space-between",label:(0,m.__)("Space between","elementor"),renderContent:({size:e})=>t.createElement(In,{icon:i.JustifySpaceBetweenVerticalIcon,size:e,...Rn}),showTooltip:!0},{value:"space-around",label:(0,m.__)("Space around","elementor"),renderContent:({size:e})=>t.createElement(In,{icon:i.JustifySpaceAroundVerticalIcon,size:e,...Rn}),showTooltip:!0},{value:"space-evenly",label:(0,m.__)("Space evenly","elementor"),renderContent:({size:e})=>t.createElement(In,{icon:i.JustifyDistributeVerticalIcon,size:e,...Rn}),showTooltip:!0}],Vn=()=>t.createElement(Vt,{bind:"align-content",propDisplayName:zn},t.createElement(rn,null,t.createElement(jt,{label:zn,direction:"column"},t.createElement(e.ToggleControl,{options:Ln,fullWidth:!0})))),An=(0,m.__)("Align items","elementor"),Nn=(0,c.withDirection)(i.LayoutAlignLeftIcon),Bn=(0,c.withDirection)(i.LayoutAlignRightIcon),On={isClockwise:!1,offset:90},Mn=[{value:"start",label:(0,m.__)("Start","elementor"),renderContent:({size:e})=>t.createElement(In,{icon:Nn,size:e,...On}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:e})=>t.createElement(In,{icon:i.LayoutAlignCenterIcon,size:e,...On}),showTooltip:!0},{value:"end",label:(0,m.__)("End","elementor"),renderContent:({size:e})=>t.createElement(In,{icon:Bn,size:e,...On}),showTooltip:!0},{value:"stretch",label:(0,m.__)("Stretch","elementor"),renderContent:({size:e})=>t.createElement(In,{icon:i.LayoutDistributeVerticalIcon,size:e,...On}),showTooltip:!0}],Fn=()=>t.createElement(rn,null,t.createElement(Vt,{bind:"align-items",propDisplayName:An},t.createElement(jt,{label:An},t.createElement(e.ToggleControl,{options:Mn})))),$n=(0,m.__)("Align self","elementor"),jn={row:90,"row-reverse":90,column:0,"column-reverse":0},Un=(0,c.withDirection)(i.LayoutAlignLeftIcon),Wn=(0,c.withDirection)(i.LayoutAlignRightIcon),Gn={isClockwise:!1},Kn=e=>[{value:"start",label:(0,m.__)("Start","elementor"),renderContent:({size:n})=>t.createElement(In,{icon:Un,size:n,offset:jn[e],...Gn}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:n})=>t.createElement(In,{icon:i.LayoutAlignCenterIcon,size:n,offset:jn[e],...Gn}),showTooltip:!0},{value:"end",label:(0,m.__)("End","elementor"),renderContent:({size:n})=>t.createElement(In,{icon:Wn,size:n,offset:jn[e],...Gn}),showTooltip:!0},{value:"stretch",label:(0,m.__)("Stretch","elementor"),renderContent:({size:n})=>t.createElement(In,{icon:i.LayoutDistributeVerticalIcon,size:n,offset:jn[e],...Gn}),showTooltip:!0}],Hn=({parentStyleDirection:n})=>t.createElement(Vt,{bind:"align-self",propDisplayName:$n},t.createElement(rn,null,t.createElement(jt,{label:$n},t.createElement(e.ToggleControl,{options:Kn(n)})))),Jn=(0,m.__)("Display","elementor"),Yn=[{value:"block",renderContent:()=>(0,m.__)("Block","elementor"),label:(0,m.__)("Block","elementor"),showTooltip:!0},{value:"flex",renderContent:()=>(0,m.__)("Flex","elementor"),label:(0,m.__)("Flex","elementor"),showTooltip:!0},{value:"inline-block",renderContent:()=>(0,m.__)("In-blk","elementor"),label:(0,m.__)("Inline-block","elementor"),showTooltip:!0}],qn=()=>{const n=(0,E.isExperimentActive)(x.V_3_30),l=[...Yn];n&&l.push({value:"none",renderContent:()=>(0,m.__)("None","elementor"),label:(0,m.__)("None","elementor"),showTooltip:!0}),l.push({value:"inline-flex",renderContent:()=>(0,m.__)("In-flx","elementor"),label:(0,m.__)("Inline-flex","elementor"),showTooltip:!0});const r=Xn();return t.createElement(Vt,{bind:"display",propDisplayName:Jn,placeholder:r},t.createElement(jt,{label:Jn,direction:"column"},t.createElement(e.ToggleControl,{options:l,maxItems:4,fullWidth:!0})))},Xn=()=>mt(["display"])[0]?.value??void 0,Zn=(0,m.__)("Direction","elementor"),Qn=[{value:"row",label:(0,m.__)("Row","elementor"),renderContent:({size:e})=>{const n=(0,c.withDirection)(i.ArrowRightIcon);return t.createElement(n,{fontSize:e})},showTooltip:!0},{value:"column",label:(0,m.__)("Column","elementor"),renderContent:({size:e})=>t.createElement(i.ArrowDownSmallIcon,{fontSize:e}),showTooltip:!0},{value:"row-reverse",label:(0,m.__)("Reversed row","elementor"),renderContent:({size:e})=>{const n=(0,c.withDirection)(i.ArrowLeftIcon);return t.createElement(n,{fontSize:e})},showTooltip:!0},{value:"column-reverse",label:(0,m.__)("Reversed column","elementor"),renderContent:({size:e})=>t.createElement(i.ArrowUpSmallIcon,{fontSize:e}),showTooltip:!0}],el=()=>t.createElement(Vt,{bind:"flex-direction",propDisplayName:Zn},t.createElement(rn,null,t.createElement(jt,{label:Zn},t.createElement(e.ToggleControl,{options:Qn})))),tl=(0,m.__)("Order","elementor"),nl=-99999,ll="first",rl="last",ol="custom",al={[ll]:nl,[rl]:99999},il=[{value:ll,label:(0,m.__)("First","elementor"),renderContent:({size:e})=>t.createElement(i.ArrowUpSmallIcon,{fontSize:e}),showTooltip:!0},{value:rl,label:(0,m.__)("Last","elementor"),renderContent:({size:e})=>t.createElement(i.ArrowDownSmallIcon,{fontSize:e}),showTooltip:!0},{value:ol,label:(0,m.__)("Custom","elementor"),renderContent:({size:e})=>t.createElement(i.PencilIcon,{fontSize:e}),showTooltip:!0}],sl=()=>{const{value:n,setValue:l,canEdit:r}=yt("order",{history:{propDisplayName:tl}}),[o,a]=(0,t.useState)(cl(n?.value||null));return t.createElement(Vt,{bind:"order",propDisplayName:tl},t.createElement(rn,null,t.createElement(At,null,t.createElement(jt,{label:tl},t.createElement(e.ControlToggleButtonGroup,{items:il,value:o,onChange:e=>{a(e),l(e&&e!==ol?{$$type:"number",value:al[e]}:null)},exclusive:!0,disabled:!r})),ol===o&&t.createElement(c.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(c.Grid,{item:!0,xs:6},t.createElement($t,null,(0,m.__)("Custom order","elementor"))),t.createElement(c.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},t.createElement(e.NumberControl,{min:-99998,max:99998,shouldForceInt:!0}))))))},cl=e=>99999===e?rl:nl===e?ll:0===e||e?ol:null,ml=(0,m.__)("Flex Size","elementor"),ul=[{value:"flex-grow",label:(0,m.__)("Grow","elementor"),renderContent:({size:e})=>t.createElement(i.ExpandIcon,{fontSize:e}),showTooltip:!0},{value:"flex-shrink",label:(0,m.__)("Shrink","elementor"),renderContent:({size:e})=>t.createElement(i.ShrinkIcon,{fontSize:e}),showTooltip:!0},{value:"custom",label:(0,m.__)("Custom","elementor"),renderContent:({size:e})=>t.createElement(i.PencilIcon,{fontSize:e}),showTooltip:!0}],pl=()=>{const{values:n,setValues:l,canEdit:r}=dt(["flex-grow","flex-shrink","flex-basis"]),o=n?.["flex-grow"]?.value||null,a=n?.["flex-shrink"]?.value||null,i=n?.["flex-basis"]?.value||null,s=(0,t.useMemo)((()=>El({grow:o,shrink:a,basis:i})),[o,a,i]),[c,m]=(0,t.useState)(s);return t.createElement(rn,null,t.createElement(At,null,t.createElement(Vt,{bind:c??"",propDisplayName:ml},t.createElement(jt,{label:ml},t.createElement(e.ControlToggleButtonGroup,{value:c,onChange:(e=null)=>{let t;m(e),t=e&&"custom"!==e?"flex-grow"===e?{"flex-basis":null,"flex-grow":d.numberPropTypeUtil.create(1),"flex-shrink":null}:{"flex-basis":null,"flex-grow":null,"flex-shrink":d.numberPropTypeUtil.create(1)}:{"flex-basis":null,"flex-grow":null,"flex-shrink":null},l(t,{history:{propDisplayName:ml}})},disabled:!r,items:ul,exclusive:!0}))),"custom"===c&&t.createElement(dl,null)))},dl=()=>{const n=(0,t.useRef)(null);return t.createElement(t.Fragment,null,t.createElement(Vt,{bind:"flex-grow",propDisplayName:ml},t.createElement(jt,{label:(0,m.__)("Grow","elementor")},t.createElement(e.NumberControl,{min:0,shouldForceInt:!0}))),t.createElement(Vt,{bind:"flex-shrink",propDisplayName:ml},t.createElement(jt,{label:(0,m.__)("Shrink","elementor")},t.createElement(e.NumberControl,{min:0,shouldForceInt:!0}))),t.createElement(Vt,{bind:"flex-basis",propDisplayName:ml},t.createElement(jt,{label:(0,m.__)("Basis","elementor"),ref:n},t.createElement(e.SizeControl,{extendedOptions:["auto"],anchorRef:n}))))},El=({grow:e,shrink:t,basis:n})=>null!==e||null!==t||n?t&&e||n?"custom":1===e?"flex-grow":1===t?"flex-shrink":"custom":null,fl=(0,m.__)("Gaps","elementor"),bl=()=>t.createElement(Vt,{bind:"gap",propDisplayName:fl},t.createElement(e.GapControl,{label:fl})),yl=(0,m.__)("Justify content","elementor"),vl=(0,c.withDirection)(i.JustifyTopIcon),gl=(0,c.withDirection)(i.JustifyBottomIcon),_l={isClockwise:!0,offset:-90},hl=[{value:"flex-start",label:(0,m.__)("Start","elementor"),renderContent:({size:e})=>t.createElement(In,{icon:vl,size:e,..._l}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:e})=>t.createElement(In,{icon:i.JustifyCenterIcon,size:e,..._l}),showTooltip:!0},{value:"flex-end",label:(0,m.__)("End","elementor"),renderContent:({size:e})=>t.createElement(In,{icon:gl,size:e,..._l}),showTooltip:!0},{value:"space-between",label:(0,m.__)("Space between","elementor"),renderContent:({size:e})=>t.createElement(In,{icon:i.JustifySpaceBetweenVerticalIcon,size:e,..._l}),showTooltip:!0},{value:"space-around",label:(0,m.__)("Space around","elementor"),renderContent:({size:e})=>t.createElement(In,{icon:i.JustifySpaceAroundVerticalIcon,size:e,..._l}),showTooltip:!0},{value:"space-evenly",label:(0,m.__)("Space evenly","elementor"),renderContent:({size:e})=>t.createElement(In,{icon:i.JustifyDistributeVerticalIcon,size:e,..._l}),showTooltip:!0}],wl=()=>t.createElement(Vt,{bind:"justify-content",propDisplayName:yl},t.createElement(rn,null,t.createElement(jt,{label:yl,direction:"column"},t.createElement(e.ToggleControl,{options:hl,fullWidth:!0})))),Sl=(0,m.__)("Wrap","elementor"),xl=[{value:"nowrap",label:(0,m.__)("No wrap","elementor"),renderContent:({size:e})=>t.createElement(i.ArrowRightIcon,{fontSize:e}),showTooltip:!0},{value:"wrap",label:(0,m.__)("Wrap","elementor"),renderContent:({size:e})=>t.createElement(i.ArrowBackIcon,{fontSize:e}),showTooltip:!0},{value:"wrap-reverse",label:(0,m.__)("Reversed wrap","elementor"),renderContent:({size:e})=>t.createElement(i.ArrowForwardIcon,{fontSize:e}),showTooltip:!0}],Cl=()=>t.createElement(Vt,{bind:"flex-wrap",propDisplayName:Sl},t.createElement(rn,null,t.createElement(jt,{label:Sl},t.createElement(e.ToggleControl,{options:xl})))),Tl=(0,m.__)("Display","elementor"),Il=(0,m.__)("Flex wrap","elementor"),kl=()=>{const{value:e}=yt("display",{history:{propDisplayName:Tl}}),n=Xn(),l=Dl(e,n),{element:o}=N(),a=(0,r.useParentElement)(o.id),i=(s=a?.id||null,(0,E.__privateUseListenTo)([(0,E.windowEvent)("elementor/device-mode/change"),(0,E.commandEndEvent)("document/elements/reset-style"),(0,E.commandEndEvent)("document/elements/settings"),(0,E.commandEndEvent)("document/elements/paste-style")],(()=>{if(!s)return null;const e=window,t=e.elementor?.getContainer?.(s);return t?.view?.el?window.getComputedStyle(t.view.el):null})));var s;const c=i?.flexDirection??"row";return t.createElement(At,null,t.createElement(qn,null),l&&t.createElement(zl,null),"flex"===i?.display&&t.createElement(Pl,{parentStyleDirection:c}))},zl=()=>{const{value:e}=yt("flex-wrap",{history:{propDisplayName:Il}});return t.createElement(t.Fragment,null,t.createElement(el,null),t.createElement(wl,null),t.createElement(Fn,null),t.createElement(Ot,null),t.createElement(bl,null),t.createElement(Cl,null),["wrap","wrap-reverse"].includes(e?.value)&&t.createElement(Vn,null))},Pl=({parentStyleDirection:n})=>t.createElement(t.Fragment,null,t.createElement(Ot,null),t.createElement(e.ControlFormLabel,null,(0,m.__)("Flex child","elementor")),t.createElement(Hn,{parentStyleDirection:n}),t.createElement(sl,null),t.createElement(pl,null)),Dl=(e,t)=>{const n=e?.value??t?.value;return!!n&&("flex"===n||"inline-flex"===n)},Rl=(0,c.withDirection)(i.SideLeftIcon),Ll=(0,c.withDirection)(i.SideRightIcon),Vl={"inset-block-start":t.createElement(i.SideTopIcon,{fontSize:"tiny"}),"inset-block-end":t.createElement(i.SideBottomIcon,{fontSize:"tiny"}),"inset-inline-start":t.createElement(In,{icon:Rl,size:"tiny"}),"inset-inline-end":t.createElement(In,{icon:Ll,size:"tiny"})},Al=e=>e?(0,m.__)("Right","elementor"):(0,m.__)("Left","elementor"),Nl=e=>e?(0,m.__)("Left","elementor"):(0,m.__)("Right","elementor"),Bl=()=>{const{isSiteRtl:e}=_t(),n=[(0,t.useRef)(null),(0,t.useRef)(null)];return t.createElement(rn,null,t.createElement(c.Stack,{direction:"row",gap:2,flexWrap:"nowrap",ref:n[0]},t.createElement(Ol,{side:"inset-block-start",label:(0,m.__)("Top","elementor"),rowRef:n[0]}),t.createElement(Ol,{side:"inset-inline-end",label:Nl(e),rowRef:n[0]})),t.createElement(c.Stack,{direction:"row",gap:2,flexWrap:"nowrap",ref:n[1]},t.createElement(Ol,{side:"inset-block-end",label:(0,m.__)("Bottom","elementor"),rowRef:n[1]}),t.createElement(Ol,{side:"inset-inline-start",label:Al(e),rowRef:n[1]})))},Ol=({side:n,label:l,rowRef:r})=>t.createElement(Vt,{bind:n,propDisplayName:l},t.createElement(c.Grid,{container:!0,gap:.75,alignItems:"center"},t.createElement(c.Grid,{item:!0,xs:12},t.createElement($t,null,l)),t.createElement(c.Grid,{item:!0,xs:12},t.createElement(e.SizeControl,{startIcon:Vl[n],extendedOptions:["auto"],anchorRef:r})))),Ml=(0,m.__)("Anchor offset","elementor"),Fl=["px","em","rem","vw","vh"],$l=()=>{const n=(0,t.useRef)(null);return t.createElement(Vt,{bind:"scroll-margin-top",propDisplayName:Ml},t.createElement(jt,{label:Ml,ref:n},t.createElement(e.SizeControl,{units:Fl,anchorRef:n})))},jl=(0,m.__)("Position","elementor"),Ul=[{label:(0,m.__)("Static","elementor"),value:"static"},{label:(0,m.__)("Relative","elementor"),value:"relative"},{label:(0,m.__)("Absolute","elementor"),value:"absolute"},{label:(0,m.__)("Fixed","elementor"),value:"fixed"},{label:(0,m.__)("Sticky","elementor"),value:"sticky"}],Wl=({onChange:n})=>t.createElement(Vt,{bind:"position",propDisplayName:jl},t.createElement(jt,{label:jl},t.createElement(e.SelectControl,{options:Ul,onChange:n}))),Gl=(0,m.__)("Z-index","elementor"),Kl=()=>t.createElement(Vt,{bind:"z-index",propDisplayName:Gl},t.createElement(jt,{label:Gl},t.createElement(e.NumberControl,null))),Hl=(0,m.__)("Position","elementor"),Jl=(0,m.__)("Dimensions","elementor"),Yl=()=>{const{value:e}=yt("position",{history:{propDisplayName:Hl}}),{values:n,setValues:l}=dt(["inset-block-start","inset-block-end","inset-inline-start","inset-inline-end"]),[r,o,a]=ql(),i=(0,E.isExperimentActive)("e_v_3_30"),s=e&&"static"!==e?.value;return t.createElement(At,null,t.createElement(Wl,{onChange:(e,t)=>{const i={history:{propDisplayName:Jl}};"static"===e?n&&(o(n),l({"inset-block-start":void 0,"inset-block-end":void 0,"inset-inline-start":void 0,"inset-inline-end":void 0},i)):"static"===t&&r&&(l(r,i),a())}}),s?t.createElement(t.Fragment,null,t.createElement(Bl,null),t.createElement(Kl,null)):null,i&&t.createElement(t.Fragment,null,t.createElement(Ot,null),t.createElement($l,null)))},ql=()=>{const{id:e,meta:t}=U(),n=`styles/${e}/${t.breakpoint||"desktop"}/${t.state||"null"}/dimensions`;return(0,b.useSessionStorage)(n)},Xl=({fields:e})=>{const{id:n,meta:l,provider:r}=U(),a=ct(),i=Object.fromEntries(Object.entries(a??{}).filter((([t])=>e.includes(t)))),{hasValues:s,hasOverrides:u}=function(e,t,n){let l=!1,r=!1;return Object.values(e).forEach((e=>{const o=function(e,t,n){return e.find((({style:{id:e},variant:{meta:{breakpoint:l,state:r}}})=>e===t&&l===n.breakpoint&&r===n.state))}(e,t,n);if(!o)return;const[a]=e;o===a?l=!0:r=!0})),{hasValues:l,hasOverrides:r}}(i,n??"",l);if(!s&&!u)return null;const p=(0,m.__)("Has effective styles","elementor"),d=(0,m.__)("Has overridden styles","elementor");return t.createElement(c.Tooltip,{title:(0,m.__)("Has styles","elementor"),placement:"top"},t.createElement(c.Stack,{direction:"row",sx:{"& > *":{marginInlineStart:-.25}},role:"list"},s&&r&&t.createElement(le,{getColor:K(r.getKey()),"data-variant":(0,o.isElementsStylesProvider)(r.getKey())?"local":"global",role:"listitem","aria-label":p}),u&&t.createElement(le,{isOverridden:!0,"data-variant":"overridden",role:"listitem","aria-label":d})))},Zl=({fields:e=[],children:n})=>t.createElement(Ke,{titleEnd:Ql(e)},n);function Ql(e){const n=(0,E.isExperimentActive)(x.V_3_30);return 0!==e.length&&n?n=>n?null:t.createElement(Xl,{fields:e}):null}var er=(0,m.__)("Object fit","elementor"),tr=[{label:(0,m.__)("Fill","elementor"),value:"fill"},{label:(0,m.__)("Cover","elementor"),value:"cover"},{label:(0,m.__)("Contain","elementor"),value:"contain"},{label:(0,m.__)("None","elementor"),value:"none"},{label:(0,m.__)("Scale down","elementor"),value:"scale-down"}],nr=()=>t.createElement(Vt,{bind:"object-fit",propDisplayName:er},t.createElement(jt,{label:er},t.createElement(e.SelectControl,{options:tr}))),lr=(0,m.__)("Object position","elementor"),rr=()=>t.createElement(Vt,{bind:"object-position",propDisplayName:lr},t.createElement(e.PositionControl,null)),or=(0,m.__)("Overflow","elementor"),ar=[{value:"visible",label:(0,m.__)("Visible","elementor"),renderContent:({size:e})=>t.createElement(i.EyeIcon,{fontSize:e}),showTooltip:!0},{value:"hidden",label:(0,m.__)("Hidden","elementor"),renderContent:({size:e})=>t.createElement(i.EyeOffIcon,{fontSize:e}),showTooltip:!0},{value:"auto",label:(0,m.__)("Auto","elementor"),renderContent:({size:e})=>t.createElement(i.LetterAIcon,{fontSize:e}),showTooltip:!0}],ir=()=>t.createElement(Vt,{bind:"overflow",propDisplayName:or},t.createElement(jt,{label:or},t.createElement(e.ToggleControl,{options:ar}))),sr=[[{bind:"width",label:(0,m.__)("Width","elementor")},{bind:"height",label:(0,m.__)("Height","elementor")}],[{bind:"min-width",label:(0,m.__)("Min width","elementor")},{bind:"min-height",label:(0,m.__)("Min height","elementor")}],[{bind:"max-width",label:(0,m.__)("Max width","elementor")},{bind:"max-height",label:(0,m.__)("Max height","elementor")}]],cr=(0,m.__)("Aspect Ratio","elementor"),mr=(0,m.__)("Object fit","elementor"),ur=()=>{const{value:n}=yt("object-fit",{history:{propDisplayName:mr}}),l=n&&"fill"!==n?.value,r=[(0,t.useRef)(null),(0,t.useRef)(null),(0,t.useRef)(null)],o=(0,E.isExperimentActive)("e_v_3_30");return t.createElement(At,null,sr.map(((e,n)=>t.createElement(c.Grid,{key:n,container:!0,gap:2,flexWrap:"nowrap",ref:r[n]},e.map((e=>t.createElement(c.Grid,{item:!0,xs:6,key:e.bind},t.createElement(pr,{...e,rowRef:r[n],extendedOptions:["auto"]}))))))),t.createElement(Ot,null),t.createElement(c.Stack,null,t.createElement(ir,null)),o&&t.createElement(Zl,{fields:["aspect-ratio","object-fit"]},t.createElement(c.Stack,{gap:2,pt:2},t.createElement(Vt,{bind:"aspect-ratio",propDisplayName:cr},t.createElement(e.AspectRatioControl,{label:cr})),t.createElement(Ot,null),t.createElement(nr,null),l&&t.createElement(c.Grid,{item:!0,xs:6},t.createElement(rr,null)))))},pr=({label:n,bind:l,rowRef:r,extendedOptions:o})=>t.createElement(Vt,{bind:l,propDisplayName:n},t.createElement(c.Grid,{container:!0,gap:.75,alignItems:"center"},t.createElement(c.Grid,{item:!0,xs:12},t.createElement($t,null,n)),t.createElement(c.Grid,{item:!0,xs:12},t.createElement(e.SizeControl,{extendedOptions:o,anchorRef:r})))),dr=(0,m.__)("Margin","elementor"),Er=(0,m.__)("Padding","elementor"),fr=()=>{const{isSiteRtl:n}=_t();return t.createElement(At,null,t.createElement(Vt,{bind:"margin",propDisplayName:dr},t.createElement(e.LinkedDimensionsControl,{label:dr,isSiteRtl:n,extendedOptions:["auto"]})),t.createElement(Ot,null),t.createElement(Vt,{bind:"padding",propDisplayName:Er},t.createElement(e.LinkedDimensionsControl,{label:Er,isSiteRtl:n})))},br=(0,m.__)("Columns","elementor"),yr=()=>t.createElement(Vt,{bind:"column-count",propDisplayName:br},t.createElement(jt,{label:br},t.createElement(e.NumberControl,{shouldForceInt:!0,min:0,step:1}))),vr=(0,m.__)("Column gap","elementor"),gr=()=>{const n=(0,t.useRef)(null);return t.createElement(Vt,{bind:"column-gap",propDisplayName:vr},t.createElement(jt,{label:vr,ref:n},t.createElement(e.SizeControl,{anchorRef:n})))},_r={system:(0,m.__)("System","elementor"),custom:(0,m.__)("Custom Fonts","elementor"),googlefonts:(0,m.__)("Google Fonts","elementor")},hr=()=>{const e=(()=>{const{controls:e}=(()=>{const e=window;return e.elementor?.config??{}})(),t=e?.font?.options;return t||null})();return(0,t.useMemo)((()=>{const t=["system","custom","googlefonts"];return Object.entries(e||{}).reduce(((e,[n,l])=>{if(!_r[l])return e;const r=t.indexOf(l);return e[r]||(e[r]={label:_r[l],fonts:[]}),e[r].fonts.push(n),e}),[]).filter(Boolean)}),[e])},wr=(0,m.__)("Font family","elementor"),Sr=()=>{const n=hr(),l=Ue();return 0===n.length?null:t.createElement(Vt,{bind:"font-family",propDisplayName:wr},t.createElement(jt,{label:wr},t.createElement(e.FontFamilyControl,{fontFamilies:n,sectionWidth:l})))},xr=(0,m.__)("Font size","elementor"),Cr=()=>{const n=(0,t.useRef)(null);return t.createElement(Vt,{bind:"font-size",propDisplayName:xr},t.createElement(jt,{label:xr,ref:n},t.createElement(e.SizeControl,{anchorRef:n})))},Tr=(0,m.__)("Font style","elementor"),Ir=[{value:"normal",label:(0,m.__)("Normal","elementor"),renderContent:({size:e})=>t.createElement(i.MinusIcon,{fontSize:e}),showTooltip:!0},{value:"italic",label:(0,m.__)("Italic","elementor"),renderContent:({size:e})=>t.createElement(i.ItalicIcon,{fontSize:e}),showTooltip:!0}],kr=()=>t.createElement(Vt,{bind:"font-style",propDisplayName:Tr},t.createElement(jt,{label:Tr},t.createElement(e.ToggleControl,{options:Ir}))),zr=(0,m.__)("Font weight","elementor"),Pr=[{value:"100",label:(0,m.__)("100 - Thin","elementor")},{value:"200",label:(0,m.__)("200 - Extra light","elementor")},{value:"300",label:(0,m.__)("300 - Light","elementor")},{value:"400",label:(0,m.__)("400 - Normal","elementor")},{value:"500",label:(0,m.__)("500 - Medium","elementor")},{value:"600",label:(0,m.__)("600 - Semi bold","elementor")},{value:"700",label:(0,m.__)("700 - Bold","elementor")},{value:"800",label:(0,m.__)("800 - Extra bold","elementor")},{value:"900",label:(0,m.__)("900 - Black","elementor")}],Dr=()=>t.createElement(Vt,{bind:"font-weight",propDisplayName:zr},t.createElement(jt,{label:zr},t.createElement(e.SelectControl,{options:Pr}))),Rr=(0,m.__)("Letter spacing","elementor"),Lr=()=>{const n=(0,t.useRef)(null);return t.createElement(Vt,{bind:"letter-spacing",propDisplayName:Rr},t.createElement(jt,{label:Rr,ref:n},t.createElement(e.SizeControl,{anchorRef:n})))},Vr=(0,m.__)("Line height","elementor"),Ar=()=>{const n=(0,t.useRef)(null);return t.createElement(Vt,{bind:"line-height",propDisplayName:Vr},t.createElement(jt,{label:Vr,ref:n},t.createElement(e.SizeControl,{anchorRef:n})))},Nr=(0,m.__)("Text align","elementor"),Br=(0,c.withDirection)(i.AlignLeftIcon),Or=(0,c.withDirection)(i.AlignRightIcon),Mr=[{value:"start",label:(0,m.__)("Start","elementor"),renderContent:({size:e})=>t.createElement(Br,{fontSize:e}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:e})=>t.createElement(i.AlignCenterIcon,{fontSize:e}),showTooltip:!0},{value:"end",label:(0,m.__)("End","elementor"),renderContent:({size:e})=>t.createElement(Or,{fontSize:e}),showTooltip:!0},{value:"justify",label:(0,m.__)("Justify","elementor"),renderContent:({size:e})=>t.createElement(i.AlignJustifiedIcon,{fontSize:e}),showTooltip:!0}],Fr=()=>t.createElement(Vt,{bind:"text-align",propDisplayName:Nr},t.createElement(rn,null,t.createElement(jt,{label:Nr},t.createElement(e.ToggleControl,{options:Mr})))),$r=(0,m.__)("Text color","elementor"),jr=()=>t.createElement(Vt,{bind:"color",propDisplayName:$r},t.createElement(jt,{label:$r},t.createElement(e.ColorControl,null))),Ur=(0,m.__)("Line decoration","elementor"),Wr=[{value:"none",label:(0,m.__)("None","elementor"),renderContent:({size:e})=>t.createElement(i.MinusIcon,{fontSize:e}),showTooltip:!0,exclusive:!0},{value:"underline",label:(0,m.__)("Underline","elementor"),renderContent:({size:e})=>t.createElement(i.UnderlineIcon,{fontSize:e}),showTooltip:!0},{value:"line-through",label:(0,m.__)("Line-through","elementor"),renderContent:({size:e})=>t.createElement(i.StrikethroughIcon,{fontSize:e}),showTooltip:!0},{value:"overline",label:(0,m.__)("Overline","elementor"),renderContent:({size:e})=>t.createElement(i.OverlineIcon,{fontSize:e}),showTooltip:!0}],Gr=()=>t.createElement(Vt,{bind:"text-decoration",propDisplayName:Ur},t.createElement(jt,{label:Ur},t.createElement(e.ToggleControl,{options:Wr,exclusive:!1}))),Kr=(0,m.__)("Direction","elementor"),Hr=[{value:"ltr",label:(0,m.__)("Left to right","elementor"),renderContent:({size:e})=>t.createElement(i.TextDirectionLtrIcon,{fontSize:e}),showTooltip:!0},{value:"rtl",label:(0,m.__)("Right to left","elementor"),renderContent:({size:e})=>t.createElement(i.TextDirectionRtlIcon,{fontSize:e}),showTooltip:!0}],Jr=()=>t.createElement(Vt,{bind:"direction",propDisplayName:Kr},t.createElement(jt,{label:Kr},t.createElement(e.ToggleControl,{options:Hr}))),Yr={$$type:"stroke",value:{color:{$$type:"color",value:"#000000"},width:{$$type:"size",value:{unit:"px",size:1}}}},qr=(0,m.__)("Text stroke","elementor"),Xr=()=>{const{value:n,setValue:l,canEdit:r}=yt("stroke",{history:{propDisplayName:qr}}),o=Boolean(n);return t.createElement(Vt,{bind:"stroke",propDisplayName:qr},t.createElement(Ft,{isAdded:o,onAdd:()=>{l(Yr)},onRemove:()=>{l(null)},disabled:!r,renderLabel:()=>t.createElement($t,null,qr)},t.createElement(e.StrokeControl,null)))},Zr=(0,m.__)("Text transform","elementor"),Qr=[{value:"none",label:(0,m.__)("None","elementor"),renderContent:({size:e})=>t.createElement(i.MinusIcon,{fontSize:e}),showTooltip:!0},{value:"capitalize",label:(0,m.__)("Capitalize","elementor"),renderContent:({size:e})=>t.createElement(i.LetterCaseIcon,{fontSize:e}),showTooltip:!0},{value:"uppercase",label:(0,m.__)("Uppercase","elementor"),renderContent:({size:e})=>t.createElement(i.LetterCaseUpperIcon,{fontSize:e}),showTooltip:!0},{value:"lowercase",label:(0,m.__)("Lowercase","elementor"),renderContent:({size:e})=>t.createElement(i.LetterCaseLowerIcon,{fontSize:e}),showTooltip:!0}],eo=()=>t.createElement(Vt,{bind:"text-transform",propDisplayName:Zr},t.createElement(jt,{label:Zr},t.createElement(e.ToggleControl,{options:Qr}))),to=(0,m.__)("Word spacing","elementor"),no=()=>{const n=(0,t.useRef)(null);return t.createElement(Vt,{bind:"word-spacing",propDisplayName:to},t.createElement(jt,{label:to,ref:n},t.createElement(e.SizeControl,{anchorRef:n})))},lo=(0,m.__)("Column count","elementor"),ro=()=>{const{value:e}=yt("column-count",{history:{propDisplayName:lo}}),n=!!(e?.value&&e?.value>1),l=(0,E.isExperimentActive)("e_v_3_30");return t.createElement(At,null,t.createElement(Sr,null),t.createElement(Dr,null),t.createElement(Cr,null),t.createElement(Ot,null),t.createElement(Fr,null),t.createElement(jr,null),t.createElement(Zl,{fields:["line-height","letter-spacing","word-spacing","column-count","text-decoration","text-transform","direction","font-style","stroke"]},t.createElement(At,{sx:{pt:2}},t.createElement(Ar,null),t.createElement(Lr,null),t.createElement(no,null),l&&t.createElement(t.Fragment,null,t.createElement(yr,null),n&&t.createElement(gr,null)),t.createElement(Ot,null),t.createElement(Gr,null),t.createElement(eo,null),t.createElement(Jr,null),t.createElement(kr,null),t.createElement(Xr,null))))},oo=({section:e,fields:n=[]})=>{const{component:l,name:r,title:o}=e,a=Re(),i=l,s=!!(0,E.isExperimentActive)(x.V_3_30)&&a.defaultSectionsExpanded.style?.includes(r);return t.createElement(Je,{title:o,defaultExpanded:s,titleEnd:Ql(n)},t.createElement(i,null))},ao={position:"sticky",zIndex:1100,opacity:1,backgroundColor:"background.default",transition:"top 300ms ease"},io=()=>{const e=function(){const{elementType:e}=N(),t=Object.entries(e.propsSchema).find((([,e])=>"plain"===e.kind&&e.key===d.CLASSES_PROP_KEY));if(!t)throw new Error("Element does not have a classes prop");return t[0]}(),[n,l]=function(e){const[t,n]=Le("active-style-id",null),l=function(e){const{element:t}=N();return(0,r.useElementSetting)(t.id,e)}(e)?.value||[],o=function(e){const{element:t}=N(),n=(0,r.getElementStyles)(t.id)??{};return Object.values(n).find((t=>e.includes(t.id)))}(l);return[function(e,t){return e&&t.includes(e)?e:null}(t,l)||o?.id||null,n]}(e),[o,a]=(0,t.useState)(null),i=(0,v.useActiveBreakpoint)();return t.createElement(R,{prop:e},t.createElement(j,{meta:{breakpoint:i,state:o},id:n,setId:e=>{l(e),a(null)},setMetaState:a},t.createElement(b.SessionStorageProvider,{prefix:n??""},t.createElement(st,null,t.createElement(so,null,t.createElement(_e,null),t.createElement(c.Divider,null)),t.createElement(Ye,null,t.createElement(oo,{section:{component:kl,name:"Layout",title:(0,m.__)("Layout","elementor")},fields:["display","flex-direction","flex-wrap","justify-content","align-items","align-content","align-self","gap"]}),t.createElement(oo,{section:{component:fr,name:"Spacing",title:(0,m.__)("Spacing","elementor")},fields:["margin","padding"]}),t.createElement(oo,{section:{component:ur,name:"Size",title:(0,m.__)("Size","elementor")},fields:["width","min-width","max-width","height","min-height","max-height","overflow","aspect-ratio","object-fit"]}),t.createElement(oo,{section:{component:Yl,name:"Position",title:(0,m.__)("Position","elementor")},fields:["position","z-index","scroll-margin-top"]}),t.createElement(oo,{section:{component:ro,name:"Typography",title:(0,m.__)("Typography","elementor")},fields:["font-family","font-weight","font-size","text-align","color","line-height","letter-spacing","word-spacing","column-count","text-decoration","text-transform","direction","font-style","stroke"]}),t.createElement(oo,{section:{component:Bt,name:"Background",title:(0,m.__)("Background","elementor")},fields:["background"]}),t.createElement(oo,{section:{component:yn,name:"Border",title:(0,m.__)("Border","elementor")},fields:["border-radius","border-width","border-color","border-style"]}),t.createElement(oo,{section:{component:Sn,name:"Effects",title:(0,m.__)("Effects","elementor")},fields:["box-shadow"]}))))))};function so({children:e}){const n=(0,t.useContext)(Ie)?.direction??"up";return t.createElement(c.Stack,{sx:{...ao,top:"up"===n?"37px":0}},e)}var co=()=>{const{element:e}=N();return t.createElement(t.Fragment,{key:e.id},t.createElement(mo,null))},mo=()=>{const e=Re(),n=(0,E.isExperimentActive)(x.V_3_30)?e.defaultTab:"settings",[l,r]=Le("tab",n),{getTabProps:o,getTabPanelProps:a,getTabsProps:i}=(0,c.useTabs)(l);return t.createElement(ze,null,t.createElement(c.Stack,{direction:"column",sx:{width:"100%"}},t.createElement(c.Stack,{sx:{...ao,top:0}},t.createElement(c.Tabs,{variant:"fullWidth",size:"small",sx:{mt:.5},...i(),onChange:(e,t)=>{i().onChange(e,t),r(t)}},t.createElement(c.Tab,{label:(0,m.__)("General","elementor"),...o("settings")}),t.createElement(c.Tab,{label:(0,m.__)("Style","elementor"),...o("style")})),t.createElement(c.Divider,null)),t.createElement(c.TabPanel,{...a("settings"),disablePadding:!0},t.createElement(qe,null)),t.createElement(c.TabPanel,{...a("style"),disablePadding:!0},t.createElement(io,null))))},{useMenuItems:uo}=Ce,{panel:po,usePanelActions:Eo,usePanelStatus:fo}=(0,f.__createPanel)({id:"editing-panel",component:()=>{const{element:n,elementType:l}=(0,r.useSelectedElement)(),o=T(),s=uo().default;if(!n||!l)return null;const u=(0,m.__)("Edit %s","elementor").replace("%s",l.title);return t.createElement(c.ErrorBoundary,{fallback:t.createElement(Te,null)},t.createElement(b.SessionStorageProvider,{prefix:"elementor"},t.createElement(a.ThemeProvider,null,t.createElement(f.Panel,null,t.createElement(f.PanelHeader,null,t.createElement(f.PanelHeaderTitle,null,u),t.createElement(i.AtomIcon,{fontSize:"small",sx:{color:"text.tertiary"}})),t.createElement(f.PanelBody,null,t.createElement(e.ControlActionsProvider,{items:s},t.createElement(e.ControlReplacementsProvider,{replacements:o},t.createElement(A,{element:n,elementType:l},t.createElement(co,null)))))))))}}),bo=e=>{const n=Ue();return t.createElement(a.PopoverScrollableContent,{...e,width:n})},yo=()=>{const e=(0,r.getSelectedElements)(),t=(0,r.getWidgetsCache)();return 1===e.length&&!!t?.[e[0].type]?.atomic_controls},vo=()=>((()=>{const{open:e}=Eo();(0,t.useEffect)((()=>(0,E.__privateListenTo)((0,E.commandStartEvent)("panel/editor/open"),(()=>{yo()&&e()}))),[])})(),null),go=()=>{const{atomicDynamicTags:e}=(()=>{const e=window;return e.elementor?.config??{}})();return e?{tags:e.tags,groups:e.groups}:null},_o="dynamic",ho=e=>{const t="union"===e.kind&&e.prop_types[_o];return t&&t.key===_o?t:null},wo=e=>(0,d.isTransformable)(e)&&e.$$type===_o,So=(0,d.createPropUtils)(_o,w.z.strictObject({name:w.z.string(),settings:w.z.any().optional()})),xo=()=>{let n=[];const{propType:l}=(0,e.useBoundProp)();if(l){const e=ho(l);n=e?.settings.categories||[]}return(0,t.useMemo)((()=>Co(n)),[n.join()])},Co=e=>{const t=go();if(!e.length||!t?.tags)return[];const n=new Set(e);return Object.values(t.tags).filter((e=>e.categories.some((e=>n.has(e)))))},To=e=>{const n=xo();return(0,t.useMemo)((()=>n.find((t=>t.name===e))??null),[n,e])},Io=()=>t.createElement(i.DatabaseIcon,{fontSize:"tiny"}),ko=({value:n})=>{const l=(0,e.useBoundProp)(d.backgroundImageOverlayPropTypeUtil);return t.createElement(e.PropProvider,{...l,value:n.value},t.createElement(e.PropKeyProvider,{bind:"image"},t.createElement(zo,{rawValue:n.value})))},zo=({rawValue:n})=>{const{propType:l}=(0,e.useBoundProp)(),r=l.prop_types["background-image-overlay"];return t.createElement(e.PropProvider,{propType:r.shape.image,value:n,setValue:()=>{}},t.createElement(e.PropKeyProvider,{bind:"src"},t.createElement(Po,{rawValue:n.image})))},Po=({rawValue:e})=>{const n=e.value.src,l=To(n.value.name||"");return t.createElement(t.Fragment,null,l?.label)},Do=e=>{const{element:t}=N(),n=`dynamic/non-dynamic-values-history/${t.id}/${e}`;return(0,b.useSessionStorage)(n)},Ro=({bind:n,children:l})=>{const{value:r,setValue:o}=(0,e.useBoundProp)(So),{name:a="",settings:i}=r??{},s=To(a);if(!s)throw new Error(`Dynamic tag ${a} not found`);const c=s.props_schema[n],m=c?.default,u=i?.[n]??m,p=Fe({schema:s.props_schema});return t.createElement(e.PropProvider,{propType:p,setValue:e=>{o({name:a,settings:{...i,...e}})},value:{[n]:u}},t.createElement(e.PropKeyProvider,{bind:n},l))},Lo=({close:n})=>{const[l,r]=(0,t.useState)(""),{groups:o}=go()||{},s=(0,c.useTheme)(),{value:u}=(0,e.useBoundProp)(),{bind:p,value:d,setValue:E}=(0,e.useBoundProp)(So),[,f]=Do(p),b=!!d,y=No(l),v=!y.length&&!l.trim(),g=y.flatMap((([e,t])=>[{type:"category",value:e,label:o?.[e]?.title||e},...t.map((e=>({type:"item",value:e.value,label:e.label})))]));return t.createElement(t.Fragment,null,t.createElement(a.PopoverHeader,{title:(0,m.__)("Dynamic tags","elementor"),onClose:n,icon:t.createElement(i.DatabaseIcon,{fontSize:"tiny"})}),t.createElement(c.Stack,null,v?t.createElement(Ao,null):t.createElement(t.Fragment,null,t.createElement(a.PopoverSearch,{value:l,onSearch:e=>{r(e)},placeholder:(0,m.__)("Search dynamic tags…","elementor")}),t.createElement(c.Divider,null),t.createElement(bo,null,t.createElement(a.PopoverMenuList,{items:g,onSelect:e=>{b||f(u);const t=y.flatMap((([,e])=>e)).find((t=>t.value===e));E({name:e,settings:{label:t?.label}}),n()},onClose:n,selectedValue:d?.name,itemStyle:e=>"item"===e.type?{paddingInlineStart:s.spacing(3.5)}:{},noResultsComponent:t.createElement(Vo,{searchValue:l,onClear:()=>r("")})})))))},Vo=({searchValue:e,onClear:n})=>t.createElement(c.Stack,{gap:1,alignItems:"center",justifyContent:"center",height:"100%",p:2.5,color:"text.secondary",sx:{pb:3.5}},t.createElement(i.DatabaseIcon,{fontSize:"large"}),t.createElement(c.Typography,{align:"center",variant:"subtitle2"},(0,m.__)("Sorry, nothing matched","elementor"),t.createElement("br",null),"“",e,"”."),t.createElement(c.Typography,{align:"center",variant:"caption",sx:{display:"flex",flexDirection:"column"}},(0,m.__)("Try something else.","elementor"),t.createElement(c.Link,{color:"text.secondary",variant:"caption",component:"button",onClick:n},(0,m.__)("Clear & try again","elementor")))),Ao=()=>t.createElement(c.Box,{sx:{overflowY:"hidden",height:297,width:220}},t.createElement(c.Divider,null),t.createElement(c.Stack,{gap:1,alignItems:"center",justifyContent:"center",height:"100%",p:2.5,color:"text.secondary",sx:{pb:3.5}},t.createElement(i.DatabaseIcon,{fontSize:"large"}),t.createElement(c.Typography,{align:"center",variant:"subtitle2"},(0,m.__)("Streamline your workflow with dynamic tags","elementor")),t.createElement(c.Typography,{align:"center",variant:"caption"},(0,m.__)("You'll need Elementor Pro to use this feature.","elementor")))),No=e=>[...xo().reduce(((t,{name:n,label:l,group:r})=>l.toLowerCase().includes(e.trim().toLowerCase())?(t.has(r)||t.set(r,[]),t.get(r)?.push({label:l,value:n}),t):t),new Map)],Bo="tiny",Oo=()=>{const{setValue:n}=(0,e.useBoundProp)(),{bind:l,value:r}=(0,e.useBoundProp)(So),[o]=Do(l),a=(0,c.usePopupState)({variant:"popover"}),{name:s=""}=r,u=To(s);if(!u)throw new Error(`Dynamic tag ${s} not found`);return t.createElement(c.Box,null,t.createElement(c.UnstableTag,{fullWidth:!0,showActionsOnHover:!0,label:u.label,startIcon:t.createElement(i.DatabaseIcon,{fontSize:Bo}),...(0,c.bindTrigger)(a),actions:t.createElement(t.Fragment,null,t.createElement(Mo,{dynamicTag:u}),t.createElement(c.IconButton,{size:Bo,onClick:()=>{n(o??null)},"aria-label":(0,m.__)("Remove dynamic value","elementor")},t.createElement(i.XIcon,{fontSize:Bo})))}),t.createElement(c.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},PaperProps:{sx:{my:1}},...(0,c.bindPopover)(a)},t.createElement(c.Stack,null,t.createElement(Lo,{close:a.close}))))},Mo=({dynamicTag:e})=>{const n=(0,c.usePopupState)({variant:"popover"});return e.atomic_controls.length?t.createElement(t.Fragment,null,t.createElement(c.IconButton,{size:Bo,...(0,c.bindTrigger)(n),"aria-label":(0,m.__)("Settings","elementor")},t.createElement(i.SettingsIcon,{fontSize:Bo})),t.createElement(c.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"center"},PaperProps:{sx:{my:.5}},...(0,c.bindPopover)(n)},t.createElement(a.PopoverHeader,{title:e.label,onClose:n.close,icon:t.createElement(i.DatabaseIcon,{fontSize:Bo})}),t.createElement(Fo,{controls:e.atomic_controls}))):null},Fo=({controls:e})=>{const n=e.filter((({type:e})=>"section"===e)),{getTabsProps:l,getTabProps:r,getTabPanelProps:o}=(0,c.useTabs)(0);return n.length?t.createElement(a.PopoverScrollableContent,null,t.createElement(c.Tabs,{size:"small",variant:"fullWidth",...l()},n.map((({value:e},n)=>t.createElement(c.Tab,{key:n,label:e.label,sx:{px:1,py:.5},...r(n)})))),t.createElement(c.Divider,null),n.map((({value:e},n)=>t.createElement(c.TabPanel,{key:n,sx:{flexGrow:1,py:0},...o(n)},t.createElement(c.Stack,{p:2,gap:2},e.items.map((e=>"control"===e.type?t.createElement($o,{key:e.value.bind,control:e.value}):null))))))):null},$o=({control:n})=>Ae(n.type)?t.createElement(Ro,{bind:n.bind},t.createElement(c.Grid,{container:!0,gap:.75},n.label?t.createElement(c.Grid,{item:!0,xs:12},t.createElement(e.ControlFormLabel,null,n.label)):null,t.createElement(c.Grid,{item:!0,xs:12},t.createElement(Ne,{type:n.type,props:n.props})))):null,jo=(0,u.createError)({code:"dynamic_tags_manager_not_found",message:"Dynamic tags manager not found"}),Uo=(0,_.createTransformer)((e=>e.name?function(e,t){const n=window,{dynamicTags:l}=n.elementor??{};if(!l)throw new jo;const r=()=>{const n=l.createTag("v4-dynamic-tag",e,t);return n?l.loadTagDataFromCache(n)??null:null},o=r();return null!==o?o:new Promise((e=>{l.refreshCacheFromServer((()=>{e(r())}))}))}(e.name,function(e){const t=Object.entries(e).map((([e,t])=>[e,(0,d.isTransformable)(t)?t.value:t]));return Object.fromEntries(t)}(e.settings??{})):null)),Wo=()=>{const{propType:n}=(0,e.useBoundProp)(),l=!!n&&(e=>!!ho(e))(n);return{visible:l,icon:i.DatabaseIcon,title:(0,m.__)("Dynamic tags","elementor"),content:({close:e})=>t.createElement(Lo,{close:e})}},{registerPopoverAction:Go}=Ce,Ko=()=>{C({component:Oo,condition:({value:e})=>wo(e)}),(0,e.injectIntoRepeaterItemLabel)({id:"dynamic-background-image",condition:({value:e})=>wo(e.value?.image?.value?.src),component:ko}),(0,e.injectIntoRepeaterItemIcon)({id:"dynamic-background-image",condition:({value:e})=>wo(e.value?.image?.value?.src),component:Io}),Go({id:"dynamic-tags",useProps:Wo}),_.styleTransformersRegistry.register("dynamic",Uo),_.settingsTransformersRegistry.register("dynamic",Uo)},{registerAction:Ho}=Ce,Jo=["order","flex-grow","flex-shrink","flex-basis"];function Yo(){const n=!!(0,t.useContext)($),{value:l,setValue:r,path:o,bind:a}=(0,e.useBoundProp)();return{visible:n&&null!=l&&o.length<=2&&!Jo.includes(a),title:(0,m.__)("Clear","elementor"),icon:i.BrushBigIcon,onClick:()=>r(null)}}var qo=(0,_.createTransformer)((e=>t.createElement(c.Stack,{direction:"row",gap:10},t.createElement(Xo,{value:e}),t.createElement(Zo,{value:e})))),Xo=({value:e})=>{const{color:n}=e;return t.createElement(Qo,{size:"inherit",component:"span",value:n})},Zo=({value:{color:e}})=>t.createElement("span",null,e),Qo=(0,c.styled)(c.UnstableColorIndicator)((({theme:e})=>({borderRadius:e.shape.borderRadius/2+"px"}))),ea=(0,_.createTransformer)((e=>t.createElement(c.Stack,{direction:"row",gap:10},t.createElement(ta,{value:e}),t.createElement(na,{value:e})))),ta=({value:e})=>{const n=la(e);return t.createElement(Qo,{size:"inherit",component:"span",value:n})},na=({value:e})=>"linear"===e.type?t.createElement("span",null,(0,m.__)("Linear Gradient","elementor")):t.createElement("span",null,(0,m.__)("Radial Gradient","elementor")),la=e=>{const t=e.stops?.map((({color:e,offset:t})=>`${e} ${t??0}%`))?.join(",");return"linear"===e.type?`linear-gradient(${e.angle}deg, ${t})`:`radial-gradient(circle at ${e.positions}, ${t})`},ra=(0,_.createTransformer)((e=>t.createElement(c.Stack,{direction:"row",gap:10},t.createElement(oa,{value:e}),t.createElement(aa,{value:e})))),oa=({value:e})=>{const{imageUrl:n}=ia(e);return t.createElement(c.CardMedia,{image:n,sx:e=>({height:"1em",width:"1em",borderRadius:e.shape.borderRadius/2+"px",outline:`1px solid ${e.palette.action.disabled}`})})},aa=({value:e})=>{const{imageTitle:n}=ia(e);return t.createElement(a.EllipsisWithTooltip,{title:n},t.createElement("span",null,n))},ia=e=>{let t,n=null;const l=e?.image.src,{data:r}=(0,S.useWpMediaAttachment)(l.id||null);if(l.id){const e=sa(r?.filename);t=`${r?.title}${e}`||null,n=r?.url||null}else l.url&&(n=l.url,t=n?.substring(n.lastIndexOf("/")+1)||null);return{imageTitle:t,imageUrl:n}},sa=e=>e?`.${e.substring(e.lastIndexOf(".")+1)}`:"",ca=(0,_.createTransformer)((e=>e&&0!==e.length?t.createElement(c.Stack,{direction:"column"},e.map(((e,n)=>t.createElement(c.Stack,{key:n},e)))):null));var ma=()=>{gt()&&function(){const e=_.styleTransformersRegistry.all();Object.entries(e).forEach((([e,t])=>{vt.has(e)||wt.register(e,t)})),wt.registerFallback((0,_.createTransformer)((e=>e))),wt.register("background-color-overlay",qo),wt.register("background-gradient-overlay",ea),wt.register("background-image-overlay",ra),wt.register("background-overlay",ca)}()};function ua(){(0,f.__registerPanel)(po),pa(),(0,h.injectIntoLogic)({id:"editing-panel-hooks",component:vo}),Ko(),ma(),(0,E.isExperimentActive)(x.V_3_30)&&Ho({id:"reset-style-value",useProps:Yo})}var pa=()=>{(0,E.blockCommand)({command:"panel/editor/open",condition:yo})}}(),(window.elementorV2=window.elementorV2||{}).editorEditingPanel=l}(),window.elementorV2.editorEditingPanel?.init?.();