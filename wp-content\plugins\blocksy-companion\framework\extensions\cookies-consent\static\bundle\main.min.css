/**
 *  - v2.1.2
 * 
 * Copyright (c) 2025
 * Licensed GPLv2+
 */

.cookie-notification{position:fixed;inset-inline-start:var(--theme-frame-size, 0px);bottom:var(--theme-frame-size, 0px);z-index:999999;color:var(--theme-text-color)}@media(max-width: 689.98px){.cookie-notification{inset-inline-end:var(--theme-frame-size, 0px)}}.cookie-notification>div{display:flex;flex-direction:column;gap:20px;padding:20px 0}@media(max-width: 689.98px){.cookie-notification>div{align-items:center}}@media(max-width: 689.98px){.cookie-notification[data-type=type-1]{background:var(--backgroundColor)}}@media(min-width: 690px){.cookie-notification[data-type=type-1]{padding:0 25px 25px 25px}}.cookie-notification[data-type=type-1] .container{position:relative}@media(max-width: 689.98px){.cookie-notification[data-type=type-1] .container{width:88%;margin:0 auto}}@media(min-width: 690px){.cookie-notification[data-type=type-1] .container{background:var(--backgroundColor);box-shadow:0px 5px 30px -5px rgba(34,56,101,.15);padding:30px;border-radius:3px;max-width:var(--maxWidth)}}.cookie-notification[data-type=type-2]{inset-inline-end:var(--theme-frame-size, 0px);padding-inline-end:var(--scrollbar-width, 0px);background:var(--backgroundColor)}@media(min-width: 690px){.cookie-notification[data-type=type-2] .ct-container{flex-direction:initial;align-items:center;justify-content:center}}.cookie-notification .ct-cookies-content{font-size:14px;line-height:1.4}.cookie-notification .ct-cookies-content>*:last-child{margin-bottom:0}.cookie-notification .ct-cookies-content a{text-decoration:underline}@media(max-width: 689.98px){.cookie-notification .ct-cookies-content{text-align:center}}.cookie-notification .ct-button-group{display:flex;gap:10px}.cookie-notification .ct-button-group .ct-button{--theme-button-font-size: 13px;--theme-button-min-height: 35px;--theme-button-padding: 0 20px;--theme-button-shadow: none;--theme-button-transform: none}.cookie-notification.ct-fade-in-start,.cookie-notification.ct-fade-in-end,.cookie-notification.ct-fade-start,.cookie-notification.ct-fade-end{transition:all .3s ease}.cookie-notification.ct-fade-in-start{opacity:0;transform:translate3d(0, 15px, 0)}.cookie-notification.ct-fade-end{opacity:0;transform:translate3d(0, 15px, 0)}