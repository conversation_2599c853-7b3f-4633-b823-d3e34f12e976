/*! elementor - v3.30.0 - 01-07-2025 */
(()=>{var t={95480:(t,r,o)=>{"use strict";var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(39805)),i=a(o(40989)),c=a(o(15118)),l=a(o(29402)),d=a(o(87861)),p=a(o(83535));function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>,[],(function(){})))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}r.default=function(t){function CommandContainerBase(){return(0,u.default)(this,CommandContainerBase),function _callSuper(t,r,o){return r=(0,l.default)(r),(0,c.default)(t,_isNativeReflectConstruct()?Reflect.construct(r,o||[],(0,l.default)(t).constructor):r.apply(t,o))}(this,CommandContainerBase,arguments)}return(0,d.default)(CommandContainerBase,t),(0,i.default)(CommandContainerBase,[{key:"requireContainer",value:function requireContainer(){var t=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.args;if(!r.container&&!r.containers)throw Error("container or containers are required.");if(r.container&&r.containers)throw Error("container and containers cannot go together please select one of them.");(r.containers||[r.container]).forEach((function(r){t.requireArgumentInstance("container",elementorModules.editor.Container,{container:r})}))}}],[{key:"getInstanceType",value:function getInstanceType(){return"CommandContainerBase"}}])}(p.default)},12508:(t,r,o)=>{"use strict";var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(39805)),i=a(o(40989)),c=a(o(15118)),l=a(o(29402)),d=a(o(87861)),p=a(o(95480));function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}r.default=function(t){function CommandContainerInternalBase(t){return(0,u.default)(this,CommandContainerInternalBase),function _callSuper(t,r,o){return r=(0,l.default)(r),(0,c.default)(t,_isNativeReflectConstruct()?Reflect.construct(r,o||[],(0,l.default)(t).constructor):r.apply(t,o))}(this,CommandContainerInternalBase,[t,$e.commandsInternal])}return(0,d.default)(CommandContainerInternalBase,t),(0,i.default)(CommandContainerInternalBase,null,[{key:"getInstanceType",value:function getInstanceType(){return"CommandContainerInternalBase"}}])}(p.default)},8813:(t,r,o)=>{"use strict";var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(39805)),i=a(o(40989)),c=a(o(15118)),l=a(o(29402)),d=a(o(41621)),p=a(o(87861)),y=a(o(95480));function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}function _superPropGet(t,r,o,a){var u=(0,d.default)((0,l.default)(1&a?t.prototype:t),r,o);return 2&a&&"function"==typeof u?function(t){return u.apply(o,t)}:u}r.default=function(t){function CommandHistoryBase(){return(0,u.default)(this,CommandHistoryBase),function _callSuper(t,r,o){return r=(0,l.default)(r),(0,c.default)(t,_isNativeReflectConstruct()?Reflect.construct(r,o||[],(0,l.default)(t).constructor):r.apply(t,o))}(this,CommandHistoryBase,arguments)}return(0,p.default)(CommandHistoryBase,t),(0,i.default)(CommandHistoryBase,[{key:"initialize",value:function initialize(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.options,o=(void 0===r?{}:r).useHistory;(void 0===o||o)&&(this.history=this.getHistory(t),this.historyId=!1)}},{key:"getHistory",value:function getHistory(){elementorModules.ForceMethodImplementation()}},{key:"isHistoryActive",value:function isHistoryActive(){return elementor.documents.getCurrent().history.getActive()}},{key:"onBeforeRun",value:function onBeforeRun(t){_superPropGet(CommandHistoryBase,"onBeforeRun",this,3)([t]),this.history&&this.isHistoryActive()&&(this.historyId=$e.internal("document/history/start-log",this.history))}},{key:"onAfterRun",value:function onAfterRun(t,r){_superPropGet(CommandHistoryBase,"onAfterRun",this,3)([t,r]),this.history&&this.isHistoryActive()&&$e.internal("document/history/end-log",{id:this.historyId})}},{key:"onAfterApply",value:function onAfterApply(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;_superPropGet(CommandHistoryBase,"onAfterApply",this,3)([t,r]),this.isDataChanged()&&$e.internal("document/save/set-is-modified",{status:!0})}},{key:"onCatchApply",value:function onCatchApply(t){t instanceof $e.modules.HookBreak&&this.historyId&&$e.internal("document/history/delete-log",{id:this.historyId}),_superPropGet(CommandHistoryBase,"onCatchApply",this,3)([t])}},{key:"isDataChanged",value:function isDataChanged(){return!0}}],[{key:"getInstanceType",value:function getInstanceType(){return"CommandHistoryBase"}}])}(y.default)},26665:(t,r,o)=>{"use strict";var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.getDefaultDebounceDelay=r.default=r.DEFAULT_DEBOUNCE_DELAY=void 0;var u=a(o(39805)),i=a(o(40989)),c=a(o(15118)),l=a(o(29402)),d=a(o(41621)),p=a(o(87861)),y=a(o(85707)),m=a(o(8813));function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}var v=r.DEFAULT_DEBOUNCE_DELAY=800,h=r.getDefaultDebounceDelay=function getDefaultDebounceDelay(){var t=v;return elementor.config.document&&void 0!==elementor.config.document.debounceDelay&&(t=elementor.config.document.debounceDelay),t},g=r.default=function(t){function CommandHistoryDebounceBase(){return(0,u.default)(this,CommandHistoryDebounceBase),function _callSuper(t,r,o){return r=(0,l.default)(r),(0,c.default)(t,_isNativeReflectConstruct()?Reflect.construct(r,o||[],(0,l.default)(t).constructor):r.apply(t,o))}(this,CommandHistoryDebounceBase,arguments)}return(0,p.default)(CommandHistoryDebounceBase,t),(0,i.default)(CommandHistoryDebounceBase,[{key:"initialize",value:function initialize(t){var r=t.options,o=void 0===r?{}:r;!function _superPropGet(t,r,o,a){var u=(0,d.default)((0,l.default)(1&a?t.prototype:t),r,o);return 2&a&&"function"==typeof u?function(t){return u.apply(o,t)}:u}(CommandHistoryDebounceBase,"initialize",this,3)([t]),this.constructor.debounce||(this.constructor.debounce=_.debounce((function(t){return t()}),h())),(1===$e.commands.currentTrace.length||o.debounce)&&(this.isDebounceRequired=!0)}},{key:"onBeforeRun",value:function onBeforeRun(t){$e.modules.CommandBase.prototype.onBeforeRun.call(this,t),this.history&&this.isHistoryActive()&&$e.internal("document/history/add-transaction",this.history)}},{key:"onAfterRun",value:function onAfterRun(t,r){$e.modules.CommandBase.prototype.onAfterRun.call(this,t,r),this.isHistoryActive()&&(this.isDebounceRequired?this.constructor.debounce((function(){return $e.internal("document/history/end-transaction")})):$e.internal("document/history/end-transaction"))}},{key:"onCatchApply",value:function onCatchApply(t){$e.modules.CommandBase.prototype.onCatchApply.call(this,t),t instanceof $e.modules.HookBreak&&this.history&&(this.isDebounceRequired?this.constructor.debounce((function(){return $e.internal("document/history/clear-transaction")})):$e.internal("document/history/clear-transaction"))}}],[{key:"getInstanceType",value:function getInstanceType(){return"CommandHistoryDebounceBase"}}])}(m.default);(0,y.default)(g,"debounce",void 0)},15213:(t,r)=>{"use strict";function _createForOfIteratorHelper(t,r){var o="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!o){if(Array.isArray(t)||(o=function _unsupportedIterableToArray(t,r){if(t){if("string"==typeof t)return _arrayLikeToArray(t,r);var o={}.toString.call(t).slice(8,-1);return"Object"===o&&t.constructor&&(o=t.constructor.name),"Map"===o||"Set"===o?Array.from(t):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?_arrayLikeToArray(t,r):void 0}}(t))||r&&t&&"number"==typeof t.length){o&&(t=o);var a=0,u=function F(){};return{s:u,n:function n(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},e:function e(t){throw t},f:u}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,c=!0,l=!1;return{s:function s(){o=o.call(t)},n:function n(){var t=o.next();return c=t.done,t},e:function e(t){l=!0,i=t},f:function f(){try{c||null==o.return||o.return()}finally{if(l)throw i}}}}function _arrayLikeToArray(t,r){(null==r||r>t.length)&&(r=t.length);for(var o=0,a=Array(r);o<r;o++)a[o]=t[o];return a}Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;r.default=function _default(t,r){var o,a=_createForOfIteratorHelper(r=Array.isArray(r)?r:[r]);try{for(a.s();!(o=a.n()).done;){var u=o.value;if(t.constructor.name===u.prototype[Symbol.toStringTag])return!0}}catch(t){a.e(t)}finally{a.f()}return!1}},82946:(t,r,o)=>{"use strict";var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(10564)),i=a(o(39805)),c=a(o(40989)),l=a(o(15118)),d=a(o(29402)),p=a(o(87861)),y=a(o(70751)),m=a(o(15213));function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}r.default=function(t){function ArgsObject(t){var r;return(0,i.default)(this,ArgsObject),(r=function _callSuper(t,r,o){return r=(0,d.default)(r),(0,l.default)(t,_isNativeReflectConstruct()?Reflect.construct(r,o||[],(0,d.default)(t).constructor):r.apply(t,o))}(this,ArgsObject)).args=t,r}return(0,p.default)(ArgsObject,t),(0,c.default)(ArgsObject,[{key:"requireArgument",value:function requireArgument(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.args;if(!Object.prototype.hasOwnProperty.call(r,t))throw Error("".concat(t," is required."))}},{key:"requireArgumentType",value:function requireArgumentType(t,r){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.args;if(this.requireArgument(t,o),(0,u.default)(o[t])!==r)throw Error("".concat(t," invalid type: ").concat(r,"."))}},{key:"requireArgumentInstance",value:function requireArgumentInstance(t,r){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.args;if(this.requireArgument(t,o),!(o[t]instanceof r||(0,m.default)(o[t],r)))throw Error("".concat(t," invalid instance."))}},{key:"requireArgumentConstructor",value:function requireArgumentConstructor(t,r){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.args;if(this.requireArgument(t,o),o[t].constructor.toString()!==r.prototype.constructor.toString())throw Error("".concat(t," invalid constructor type."))}}],[{key:"getInstanceType",value:function getInstanceType(){return"ArgsObject"}}])}(y.default)},70751:(t,r,o)=>{"use strict";var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(39805)),i=a(o(40989)),c=a(o(29402)),l=a(o(41621));r.default=function(){function InstanceType(){var t=this;(0,u.default)(this,InstanceType);for(var r=this instanceof InstanceType?this.constructor:void 0,o=[];r.__proto__&&r.__proto__.name;)o.push(r.__proto__),r=r.__proto__;o.reverse().forEach((function(r){return t instanceof r}))}return(0,i.default)(InstanceType,null,[{key:Symbol.hasInstance,value:function value(t){var r=function _superPropGet(t,r,o,a){var u=(0,l.default)((0,c.default)(1&a?t.prototype:t),r,o);return 2&a&&"function"==typeof u?function(t){return u.apply(o,t)}:u}(InstanceType,Symbol.hasInstance,this,2)([t]);if(t&&!t.constructor.getInstanceType)return r;if(t&&(t.instanceTypes||(t.instanceTypes=[]),r||this.getInstanceType()===t.constructor.getInstanceType()&&(r=!0),r)){var o=this.getInstanceType===InstanceType.getInstanceType?"BaseInstanceType":this.getInstanceType();-1===t.instanceTypes.indexOf(o)&&t.instanceTypes.push(o)}return!r&&t&&(r=t.instanceTypes&&Array.isArray(t.instanceTypes)&&-1!==t.instanceTypes.indexOf(this.getInstanceType())),r}},{key:"getInstanceType",value:function getInstanceType(){elementorModules.ForceMethodImplementation()}}])}()},83535:(t,r,o)=>{"use strict";var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(39805)),i=a(o(40989)),c=a(o(15118)),l=a(o(29402)),d=a(o(87861)),p=a(o(79958)),y=a(o(92766));function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}r.default=function(t){function CommandBase(){return(0,u.default)(this,CommandBase),function _callSuper(t,r,o){return r=(0,l.default)(r),(0,c.default)(t,_isNativeReflectConstruct()?Reflect.construct(r,o||[],(0,l.default)(t).constructor):r.apply(t,o))}(this,CommandBase,arguments)}return(0,d.default)(CommandBase,t),(0,i.default)(CommandBase,[{key:"onBeforeRun",value:function onBeforeRun(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};$e.hooks.runUIBefore(this.command,t)}},{key:"onAfterRun",value:function onAfterRun(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;$e.hooks.runUIAfter(this.command,t,r)}},{key:"onBeforeApply",value:function onBeforeApply(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};$e.hooks.runDataDependency(this.command,t)}},{key:"onAfterApply",value:function onAfterApply(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;return $e.hooks.runDataAfter(this.command,t,r)}},{key:"onCatchApply",value:function onCatchApply(t){this.runCatchHooks(t)}},{key:"runCatchHooks",value:function runCatchHooks(t){$e.hooks.runDataCatch(this.command,this.args,t),$e.hooks.runUICatch(this.command,this.args,t)}},{key:"requireContainer",value:function requireContainer(){var t=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.args;if(y.default.deprecated("requireContainer()","3.7.0","Extend `$e.modules.editor.CommandContainerBase` or `$e.modules.editor.CommandContainerInternalBase`"),!r.container&&!r.containers)throw Error("container or containers are required.");if(r.container&&r.containers)throw Error("container and containers cannot go together please select one of them.");(r.containers||[r.container]).forEach((function(r){t.requireArgumentInstance("container",elementorModules.editor.Container,{container:r})}))}}],[{key:"getInstanceType",value:function getInstanceType(){return"CommandBase"}}])}(p.default)},79958:(t,r,o)=>{"use strict";var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(39805)),i=a(o(40989)),c=a(o(15118)),l=a(o(29402)),d=a(o(87861)),p=a(o(85707)),y=a(o(82946)),m=a(o(92766));function _isNativeReflectConstruct(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!t})()}var v=r.default=function(t){function CommandInfra(){var t,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if((0,u.default)(this,CommandInfra),!(t=function _callSuper(t,r,o){return r=(0,l.default)(r),(0,c.default)(t,_isNativeReflectConstruct()?Reflect.construct(r,o||[],(0,l.default)(t).constructor):r.apply(t,o))}(this,CommandInfra,[r])).constructor.registerConfig)throw RangeError("Doing it wrong: Each command type should have `registerConfig`.");return t.command=t.constructor.getCommand(),t.component=t.constructor.getComponent(),t.initialize(r),r=t.args,t.validateArgs(r),t}return(0,d.default)(CommandInfra,t),(0,i.default)(CommandInfra,[{key:"currentCommand",get:function get(){return m.default.deprecated("this.currentCommand","3.7.0","this.command"),this.command}},{key:"initialize",value:function initialize(){}},{key:"validateArgs",value:function validateArgs(){}},{key:"apply",value:function apply(){elementorModules.ForceMethodImplementation()}},{key:"run",value:function run(){return this.apply(this.args)}},{key:"onBeforeRun",value:function onBeforeRun(){}},{key:"onAfterRun",value:function onAfterRun(){}},{key:"onBeforeApply",value:function onBeforeApply(){}},{key:"onAfterApply",value:function onAfterApply(){}},{key:"onCatchApply",value:function onCatchApply(t){}}],[{key:"getInstanceType",value:function getInstanceType(){return"CommandInfra"}},{key:"getInfo",value:function getInfo(){return{}}},{key:"getCommand",value:function getCommand(){return this.registerConfig.command}},{key:"getComponent",value:function getComponent(){return this.registerConfig.component}},{key:"setRegisterConfig",value:function setRegisterConfig(t){this.registerConfig=Object.freeze(t)}}])}(y.default);(0,p.default)(v,"registerConfig",null)},88413:(t,r,o)=>{"use strict";var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(39805)),i=a(o(40989));r.default=function(){return(0,i.default)((function Console(){(0,u.default)(this,Console)}),null,[{key:"error",value:function error(t){$e.devTools&&$e.devTools.log.error(t),t instanceof $e.modules.HookBreak||console.error(t)}},{key:"warn",value:function warn(){for(var t,r='font-size: 12px; background-image: url("'.concat(elementorWebCliConfig.urls.assets,'images/logo-icon.png"); background-repeat: no-repeat; background-size: contain;'),o=arguments.length,a=new Array(o),u=0;u<o;u++)a[u]=arguments[u];a.unshift("%c  %c",r,""),(t=console).warn.apply(t,a)}}])}()},92766:(t,r,o)=>{"use strict";var a=o(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var u=a(o(18821)),i=a(o(39805)),c=a(o(40989)),l=a(o(88413)),d=function deprecatedMessage(t,r,o,a){var u="`".concat(r,"` is ").concat(t," deprecated since ").concat(o);a&&(u+=" - Use `".concat(a,"` instead")),l.default.warn(u)};r.default=function(){return(0,c.default)((function Deprecation(){(0,i.default)(this,Deprecation)}),null,[{key:"deprecated",value:function deprecated(t,r,o){this.isHardDeprecated(r)?function hardDeprecated(t,r,o){d("hard",t,r,o)}(t,r,o):function softDeprecated(t,r,o){elementorWebCliConfig.isDebug&&d("soft",t,r,o)}(t,r,o)}},{key:"parseVersion",value:function parseVersion(t){var r=t.split(".");if(r.length<3||r.length>4)throw new RangeError("Invalid Semantic Version string provided");var o=(0,u.default)(r,4),a=o[0],i=o[1],c=o[2],l=o[3],d=void 0===l?"":l;return{major1:parseInt(a),major2:parseInt(i),minor:parseInt(c),build:d}}},{key:"getTotalMajor",value:function getTotalMajor(t){var r=parseInt("".concat(t.major1).concat(t.major2,"0"));return r=Number((r/10).toFixed(0)),t.major2>9&&(r=t.major2-9),r}},{key:"compareVersion",value:function compareVersion(t,r){var o=this;return[this.parseVersion(t),this.parseVersion(r)].map((function(t){return o.getTotalMajor(t)})).reduce((function(t,r){return t-r}))}},{key:"isSoftDeprecated",value:function isSoftDeprecated(t){return this.compareVersion(t,elementorWebCliConfig.version)<=4}},{key:"isHardDeprecated",value:function isHardDeprecated(t){var r=this.compareVersion(t,elementorWebCliConfig.version);return r<0||r>=8}}])}()},78113:t=>{t.exports=function _arrayLikeToArray(t,r){(null==r||r>t.length)&&(r=t.length);for(var o=0,a=Array(r);o<r;o++)a[o]=t[o];return a},t.exports.__esModule=!0,t.exports.default=t.exports},70569:t=>{t.exports=function _arrayWithHoles(t){if(Array.isArray(t))return t},t.exports.__esModule=!0,t.exports.default=t.exports},36417:t=>{t.exports=function _assertThisInitialized(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t},t.exports.__esModule=!0,t.exports.default=t.exports},39805:t=>{t.exports=function _classCallCheck(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")},t.exports.__esModule=!0,t.exports.default=t.exports},40989:(t,r,o)=>{var a=o(45498);function _defineProperties(t,r){for(var o=0;o<r.length;o++){var u=r[o];u.enumerable=u.enumerable||!1,u.configurable=!0,"value"in u&&(u.writable=!0),Object.defineProperty(t,a(u.key),u)}}t.exports=function _createClass(t,r,o){return r&&_defineProperties(t.prototype,r),o&&_defineProperties(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t},t.exports.__esModule=!0,t.exports.default=t.exports},85707:(t,r,o)=>{var a=o(45498);t.exports=function _defineProperty(t,r,o){return(r=a(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o,t},t.exports.__esModule=!0,t.exports.default=t.exports},41621:(t,r,o)=>{var a=o(14718);function _get(){return t.exports=_get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,r,o){var u=a(t,r);if(u){var i=Object.getOwnPropertyDescriptor(u,r);return i.get?i.get.call(arguments.length<3?t:o):i.value}},t.exports.__esModule=!0,t.exports.default=t.exports,_get.apply(null,arguments)}t.exports=_get,t.exports.__esModule=!0,t.exports.default=t.exports},29402:t=>{function _getPrototypeOf(r){return t.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},t.exports.__esModule=!0,t.exports.default=t.exports,_getPrototypeOf(r)}t.exports=_getPrototypeOf,t.exports.__esModule=!0,t.exports.default=t.exports},87861:(t,r,o)=>{var a=o(91270);t.exports=function _inherits(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(r&&r.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),r&&a(t,r)},t.exports.__esModule=!0,t.exports.default=t.exports},96784:t=>{t.exports=function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}},t.exports.__esModule=!0,t.exports.default=t.exports},65474:t=>{t.exports=function _iterableToArrayLimit(t,r){var o=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=o){var a,u,i,c,l=[],d=!0,p=!1;try{if(i=(o=o.call(t)).next,0===r){if(Object(o)!==o)return;d=!1}else for(;!(d=(a=i.call(o)).done)&&(l.push(a.value),l.length!==r);d=!0);}catch(t){p=!0,u=t}finally{try{if(!d&&null!=o.return&&(c=o.return(),Object(c)!==c))return}finally{if(p)throw u}}return l}},t.exports.__esModule=!0,t.exports.default=t.exports},11018:t=>{t.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},t.exports.__esModule=!0,t.exports.default=t.exports},15118:(t,r,o)=>{var a=o(10564).default,u=o(36417);t.exports=function _possibleConstructorReturn(t,r){if(r&&("object"==a(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return u(t)},t.exports.__esModule=!0,t.exports.default=t.exports},91270:t=>{function _setPrototypeOf(r,o){return t.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,r){return t.__proto__=r,t},t.exports.__esModule=!0,t.exports.default=t.exports,_setPrototypeOf(r,o)}t.exports=_setPrototypeOf,t.exports.__esModule=!0,t.exports.default=t.exports},18821:(t,r,o)=>{var a=o(70569),u=o(65474),i=o(37744),c=o(11018);t.exports=function _slicedToArray(t,r){return a(t)||u(t,r)||i(t,r)||c()},t.exports.__esModule=!0,t.exports.default=t.exports},14718:(t,r,o)=>{var a=o(29402);t.exports=function _superPropBase(t,r){for(;!{}.hasOwnProperty.call(t,r)&&null!==(t=a(t)););return t},t.exports.__esModule=!0,t.exports.default=t.exports},11327:(t,r,o)=>{var a=o(10564).default;t.exports=function toPrimitive(t,r){if("object"!=a(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var u=o.call(t,r||"default");if("object"!=a(u))return u;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)},t.exports.__esModule=!0,t.exports.default=t.exports},45498:(t,r,o)=>{var a=o(10564).default,u=o(11327);t.exports=function toPropertyKey(t){var r=u(t,"string");return"symbol"==a(r)?r:r+""},t.exports.__esModule=!0,t.exports.default=t.exports},10564:t=>{function _typeof(r){return t.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,_typeof(r)}t.exports=_typeof,t.exports.__esModule=!0,t.exports.default=t.exports},37744:(t,r,o)=>{var a=o(78113);t.exports=function _unsupportedIterableToArray(t,r){if(t){if("string"==typeof t)return a(t,r);var o={}.toString.call(t).slice(8,-1);return"Object"===o&&t.constructor&&(o=t.constructor.name),"Map"===o||"Set"===o?Array.from(t):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?a(t,r):void 0}},t.exports.__esModule=!0,t.exports.default=t.exports}},r={};function __webpack_require__(o){var a=r[o];if(void 0!==a)return a.exports;var u=r[o]={exports:{}};return t[o](u,u.exports,__webpack_require__),u.exports}(()=>{"use strict";var t=__webpack_require__(96784),r=t(__webpack_require__(95480)),o=t(__webpack_require__(12508)),a=t(__webpack_require__(8813)),u=t(__webpack_require__(26665));$e.modules.editor={CommandContainerBase:r.default,CommandContainerInternalBase:o.default,document:{CommandHistoryBase:a.default,CommandHistoryDebounceBase:u.default}},$e.modules.document={get CommandHistory(){return elementorDevTools.deprecation.deprecated("$e.modules.document.CommandHistory","3.7.0","$e.modules.editor.document.CommandHistoryBase"),$e.modules.editor.document.CommandHistoryBase},get CommandHistoryDebounce(){return elementorDevTools.deprecation.deprecated("$e.modules.CommandHistoryDebounce","3.7.0","$e.modules.editor.document.CommandHistoryDebounceBase"),$e.modules.editor.document.CommandHistoryDebounceBase}}})()})();