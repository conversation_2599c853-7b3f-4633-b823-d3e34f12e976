(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6bd81dbc"],{"04ed":function(e,t,a){e.exports=a.p+"img/connect-success.svg"},"07e6":function(e,t,a){e.exports=a.p+"img/info.svg"},"07f4":function(e,t,a){},"0ac9":function(e,t,a){e.exports=a.p+"img/add-language.svg"},"0d3d":function(e,t,a){e.exports=a.p+"img/info-alert.svg"},"0ea3":function(e,t,a){e.exports=a.p+"img/tcf-gcm.svg"},"136f":function(e,t,a){},1729:function(e,t,a){e.exports=a.p+"img/scan-result.svg"},1771:function(e,t,a){var i={"./accessyes-icon.svg":"e02a","./action-alert.svg":"d645","./add-language.svg":"0ac9","./arrow-back.svg":"b5fb","./arrow-bold.svg":"6ee3","./arrow-down.svg":"896a","./arrow-left.svg":"857e","./arrow-right.svg":"50c3","./arrow.svg":"b94f","./blue-tick.svg":"f499","./check-fill.svg":"dcb3","./check.svg":"f222","./circle-warning.svg":"fb71","./close.svg":"bf37","./connect-success.svg":"04ed","./cookie-policy.svg":"b8f6","./cookieyes-logo.svg":"37f1","./crown.svg":"7cd5","./custom-icon.svg":"1381","./custom-logo.svg":"f405","./dashboard/lang.svg":"c3cb","./dashboard/location.svg":"5544","./dashboard/reg.svg":"78d0","./dashboard/scan-history.svg":"6aed","./dashboard/status.svg":"4a95","./error.svg":"2ee5","./external.svg":"ef87","./gdpr-us.png":"969c","./geo-location.svg":"e622","./global-privacy.svg":"d91f","./help.svg":"6a22","./info-alert.svg":"0d3d","./info-new.svg":"34c7","./info.svg":"07e6","./layouts/banner-top.svg":"6f43","./layouts/banner.svg":"9d9c","./layouts/box-bottom-right.svg":"2f94","./layouts/box-top-left.svg":"2cc0","./layouts/box-top-right.svg":"73cc","./layouts/box.svg":"d520","./layouts/center.svg":"f7fc","./layouts/classic-bottom.svg":"89bc","./layouts/classic-top.svg":"bca3","./layouts/popup.svg":"6008","./layouts/pushdown.svg":"69e9","./layouts/sidebar.svg":"7801","./loader.svg":"6e70","./logo.png":"cf05","./logo.svg":"9b19","./popup-layout.svg":"3b6a","./privacy-policy.svg":"1dc8","./scan-now.svg":"5fa2","./scan-result.svg":"1729","./schedule-scan.svg":"fba1","./search.svg":"51f2","./star.svg":"42a8","./success-circle.svg":"eb98","./success.svg":"2764","./tcf-gcm.svg":"0ea3","./tick.svg":"4ab9","./time-schedule.svg":"6c96","./tooltip.svg":"e56e","./trends.png":"f72a","./warning.svg":"afb7","./widget-icon.svg":"6a11"};function n(e){var t=s(e);return a(t)}function s(e){if(!a.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}n.keys=function(){return Object.keys(i)},n.resolve=s,e.exports=n,n.id="1771"},"1dc8":function(e,t,a){e.exports=a.p+"img/privacy-policy.svg"},2698:function(e,t,a){},2764:function(e,t,a){e.exports=a.p+"img/success.svg"},"297a":function(e,t,a){"use strict";a("07f4")},"2cc0":function(e,t,a){e.exports=a.p+"img/box-top-left.svg"},"2ee5":function(e,t,a){e.exports=a.p+"img/error.svg"},"2f94":function(e,t,a){e.exports=a.p+"img/box-bottom-right.svg"},"34c7":function(e,t,a){e.exports=a.p+"img/info-new.svg"},3636:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e._self._c;return e.isLoaded?t("div",{staticClass:"cky-section cky-section-consent-banner cky-zero-padding cky-zero--margin"},[t("div",{staticClass:"cky-section-header"}),t("div",{staticClass:"cky-section-content"},[t("div",{staticClass:"cky-nav-tab cky-vertical-tab cky-nav-tab-banner-settings",attrs:{role:"navigation"}},[t("div",{staticClass:"cky-nav-tabs-container"},[t("ul",{staticClass:"cky-vertical-tabs"},e._l(e.tabs,(function(a){return t("li",{key:a.id,class:["cky-nav-tab-item",{active:e.currentTab===a.id}]},[t("button",{staticClass:"cky-nav-tab-button",on:{click:function(t){e.currentTab=a.id}}},[e.showIcon(a)?t("cky-icon",{staticClass:"cky-nav-tab-button-icon",attrs:{icon:a.icon}}):e._e(),t("span",{staticClass:"cky-nav-tab-item-title"},[e._v(" "+e._s(a.title)+" ")])],1)])})),0)]),t("div",{staticClass:"cky-nav-tab-content-container"},[t("div",{staticClass:"cky-nav-tab-content"},[t("div",{staticClass:"cky-nav-tab-section"},[t("div",{staticClass:"cky-row"},[t("div",{staticClass:"cky-col-8"},[t("div",{staticClass:"cky-align-center"},[t("cky-banner-selector"),t("div",{staticClass:"cky-banner-preview-toggler cky-center"},[t("label",{staticClass:"cky-toggle",attrs:{for:"cky-banner-preview"}},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.preview,expression:"preview"}],attrs:{type:"checkbox",id:"cky-banner-preview",disabled:e.noticeDisabled},domProps:{checked:Array.isArray(e.preview)?e._i(e.preview,null)>-1:e.preview},on:{change:function(t){var a=e.preview,i=t.target,n=!!i.checked;if(Array.isArray(a)){var s=null,o=e._i(a,s);i.checked?o<0&&(e.preview=a.concat([s])):o>-1&&(e.preview=a.slice(0,o).concat(a.slice(o+1)))}else e.preview=n}}}),t("span",{staticClass:"cky-toggle-text"},[e._v(e._s(e.$i18n.__("Banner Preview","cookie-law-info")))]),e.noticeDisabled?t("cky-popper",{attrs:{content:e.$i18n.__("The banner has been disabled. You can enable it from ‘General’, if needed.","cookie-law-info")}},[t("span",{staticClass:"cky-toggle-slider cky-disabled",attrs:{"aria-hidden":"true"}})]):t("span",{staticClass:"cky-toggle-slider",attrs:{"aria-hidden":"true"}})],1)])],1)]),t("div",{staticClass:"cky-col-4 cky-align-center cky-justify-end"},[t("cky-button",{ref:"ckyButtonSaveBanner",staticClass:"cky-button cky-button-green",staticStyle:{"margin-left":"15px"},attrs:{disabled:e.publishDisabled},nativeOn:{click:function(t){return e.saveConfig.apply(null,arguments)}}},[e._v(" "+e._s(e.$i18n.__("Publish Changes","cookie-law-info"))+" ")])],1)]),e.banner.id?t("div",{staticClass:"cky-nav-tab-section-content"},[t(e.currentTabComponent,{tag:"component"})],1):e._e()])])])])])]):t("div",{staticClass:"cky-loader-view",staticStyle:{"min-height":"400px"}},[t("p",{staticClass:"cky-loading-text"},[t("cky-loader"),e._v(" "+e._s(e.$i18n.__("Loading...","cookie-law-info"))+" ")],1)])},n=[],s=a("a2b6"),o=a("c4aa"),r=a("8259"),c=a("bde3"),l=a("9e47"),p=a("1f3d"),d=function(){var e=this,t=e._self._c;return e.show?t("div",{staticClass:"cky-banner-preview",attrs:{id:"cky-banner-preview-container"}}):e._e()},u=[],g={name:"CkyBannerPreview",props:{},components:{},data(){return{show:!0}}},y=g,f=a("2877"),h=Object(f["a"])(y,d,u,!1,null,null,null),b=h.exports,k=a("c2c9"),v=function(){var e=this,t=e._self._c;return t("div",{staticClass:"cky-banner-selector"},[t("label",{staticClass:"cky-label"},[e._v(e._s(e.$i18n.__("Consent Template","cookie-law-info")))]),t("cky-dropdown",{ref:"languageSelector",staticClass:"cky-dropdown-banners",attrs:{text:e.getBannerName(e.banner)}},[t("ul",[e._l(e.banners,(function(a){return t("li",{key:a.name,staticClass:"cky-drop-down-item"},[t("cky-popper",{attrs:{content:"GDPR"===a.name?e.$i18n.__("The selected template (opt-in banner) supports GDPR (EU & UK), LGPD (Brazil), PIPEDA (Canada), Law 25 (Quebec), POPIA (South Africa), nFADP (Switzerland), Privacy Act (Australia), PDPL (Saudi Arabia), PDPL (Argentina), PDPL (Andorra), DPA (Faroe Island).","cookie-law-info"):e.$i18n.__("The selected template (opt-out banner) supports CCPA/CPRA (California), VCDPA (Virginia), CPA (Colorado), CTDPA (Connecticut), & UCPA (Utah).","cookie-law-info"),position:"right"}},[t("a",{attrs:{value:a.id,href:"#",target:"_self"},on:{click:function(t){return t.preventDefault(),e.changeBanner(a.id)}}},[e._v(" "+e._s(e.getBannerName(a))+" ")])])],1)})),t("li",{staticClass:"cky-drop-down-item cky-action-link"},[t("a",{on:{click:function(t){return t.preventDefault(),e.showConnectModal.apply(null,arguments)}}},[e._v(" "+e._s(e.$i18n.__("GDPR & US State Laws","cookie-law-info"))+" "),t("img",{staticClass:"cky-crown-img",attrs:{src:a("7cd5"),alt:"crown-icon"}})])])],2)]),t("cky-connect-modal",{ref:"CkyConnectModal",attrs:{availablePlan:"pro"},scopedSlots:e._u([{key:"title",fn:function(){return[t("img",{attrs:{src:a("969c"),alt:"Gdpr-US"}})]},proxy:!0},{key:"message",fn:function(){return[t("div",{staticClass:"cky-feature-text"},[e._v(" "+e._s(e.$i18n.__("Display GDPR (opt-in) and US State Laws (opt-out) banners at the same time","cookie-law-info"))+" ")]),t("div",{staticClass:"cky-available-wrapper"},[t("span",{staticClass:"cky-available-text",domProps:{innerHTML:e._s(e.$i18n.__("Available in: <b>Pro and Ultimate plans</b>","cookie-law-info"))}})])]},proxy:!0}])})],1)},m=[],C=a("9947"),w={name:"CkyBannerSelector",components:{CkyDropdown:k["a"],CkyConnectModal:C["a"]},data(){return{laws:["gdpr","ccpa","gdpr&ccpa"]}},methods:{async changeBanner(e){o["a"].toggleBanner(e)},getBannerName(e){return"CCPA"===e.name?"US State Laws":e.name},showConnectModal(){this.$refs.CkyConnectModal.show()}},computed:{banner(){return this.$store.state.banners.current},banners(){return this.$store.state.banners.items||{}}}},_=w,P=(a("694f"),Object(f["a"])(_,v,m,!1,null,null,null)),x=P.exports,L=function(){var e=this,t=e._self._c;return e.loading?t("div",{staticClass:"cky-loader-view"},[t("p",{staticClass:"cky-loading-text"},[t("cky-loader"),e._v(" "+e._s(e.$i18n.__("Fetching data","cookie-law-info"))+" ")],1)]):t("div",[t("div",{staticClass:"cky-banner-message"},[t("div",{staticClass:"cky-row cky-align-center"},["gdpr"===e.appliedLaw?t("p",{staticClass:"cky-col-label"},[e._v(" "+e._s(e.$i18n.__("The selected template (opt-in banner) supports GDPR (EU & UK), LGPD (Brazil), PIPEDA (Canada), Law 25 (Quebec), POPIA (South Africa), nFADP (Switzerland), Privacy Act (Australia), PDPL (Saudi Arabia), PDPL (Argentina), PDPL (Andorra), DPA (Faroe Island)","cookie-law-info"))+" ")]):"ccpa"===e.appliedLaw?t("p",{staticClass:"cky-col-label"},[e._v(" "+e._s(e.$i18n.__("The selected template (opt-out banner) supports CCPA/CPRA (California), VCDPA (Virginia), CPA (Colorado), CTDPA (Connecticut), & UCPA (Utah)","cookie-law-info"))+" ")]):e._e()])]),t("div",{staticClass:"cky-app-box"},[t("cky-banner-region-selector",{attrs:{law:e.appliedLaw}})],1),"ccpa"===e.appliedLaw?t("div",{staticClass:"cky-row"},[t("div",{staticClass:"cky-col-12"},[t("div",{staticClass:"cky-app-box"},[t("div",{staticClass:"cky-row cky-align-center"},[t("label",{staticClass:"cky-col-3 cky-col-label"},[e._v(" "+e._s(e.$i18n.__("Show Banner","cookie-law-info"))+" ")]),t("div",{staticClass:"cky-col-9"},[t("label",{staticClass:"cky-toggle",attrs:{for:"cky-toggle-notice"}},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.banner.properties.config.notice.status,expression:"banner.properties.config.notice.status"}],attrs:{type:"checkbox",id:"cky-toggle-notice"},domProps:{checked:Array.isArray(e.banner.properties.config.notice.status)?e._i(e.banner.properties.config.notice.status,null)>-1:e.banner.properties.config.notice.status},on:{change:function(t){var a=e.banner.properties.config.notice.status,i=t.target,n=!!i.checked;if(Array.isArray(a)){var s=null,o=e._i(a,s);i.checked?o<0&&e.$set(e.banner.properties.config.notice,"status",a.concat([s])):o>-1&&e.$set(e.banner.properties.config.notice,"status",a.slice(0,o).concat(a.slice(o+1)))}else e.$set(e.banner.properties.config.notice,"status",n)}}}),t("span",{staticClass:"cky-toggle-slider",attrs:{"aria-hidden":"true"}})])])])])])]):t("div",{staticClass:"cky-row"},[t("div",{staticClass:"cky-col-12"},[t("div",{staticClass:"cky-app-box"},[t("div",{staticClass:"cky-row cky-align-center cky-app-row"},[t("div",{staticClass:"cky-col-5"},[t("label",{staticClass:"cky-zero-padding cky-col-label cky-action-link",attrs:{for:"cky-toggle-iab"}},[e._v(" "+e._s(e.$i18n.__("Support IAB TCF v2.2","cookie-law-info"))+" "),t("cky-popper",{attrs:{content:e.$i18n.__("Enable the support for IAB Transparency and Consent Framework if you run ads on your site. When enabled, a GDPR-compliant consent banner will appear on your site, allowing your visitors to set granular advertising tracking preferences.","cookie-law-info")}}),t("img",{staticClass:"cky-crown-img",attrs:{src:a("7cd5"),alt:"crown-icon"}})],1)]),t("div",{staticClass:"cky-col-7"},[t("label",{staticClass:"cky-toggle",attrs:{for:"cky-toggle-iab"}},[t("input",{attrs:{type:"checkbox",id:"cky-toggle-iab"},domProps:{value:!1},on:{click:function(t){t.preventDefault(),e.showConnectModal(e.$i18n.__("Comply with Google’s requirements for serving ads in EEA, UK & Switzerland","cookie-law-info"))}}}),t("span",{staticClass:"cky-toggle-slider",attrs:{id:"cky-toggle-iab-toggle","aria-hidden":"true"}})])])]),t("div",{staticClass:"cky-row cky-align-center"},[t("div",{staticClass:"cky-col-5"},[t("label",{staticClass:"cky-zero-padding cky-col-label cky-action-link",attrs:{for:"cky-toggle-gacm"}},[e._v(" "+e._s(e.$i18n.__("Support Google's Additional Consent Mode","cookie-law-info"))+" "),t("cky-popper",{attrs:{content:e.$i18n.__("Google's Additional Consent (AC) Mode allows the collection of consents for Google's Ad Technology Providers (ATPs) that are not yet registered on the IAB Europe Global Vendor List (GVL). AC Mode is intended only for use alongside IAB TCF.","cookie-law-info")}}),e._m(0)],1)]),t("div",{staticClass:"cky-col-7"},[t("label",{staticClass:"cky-toggle",attrs:{for:"cky-toggle-gacm"}},[t("input",{attrs:{type:"checkbox",id:"cky-toggle-gacm"},domProps:{value:!1},on:{click:function(t){t.preventDefault(),e.showConnectModal(e.$i18n.__("Comply with Google’s requirements for serving ads in EEA, UK & Switzerland","cookie-law-info"))}}}),t("span",{staticClass:"cky-toggle-slider",attrs:{id:"cky-toggle-gacm-toggle","aria-hidden":"true"}})])])])])])]),t("cky-connect-modal",{ref:"ckyConnectModal",attrs:{availablePlan:"pro"},scopedSlots:e._u([{key:"title",fn:function(){return[t("img",{attrs:{src:a("0ea3"),alt:"languages"}})]},proxy:!0},{key:"message",fn:function(){return[t("div",{staticClass:"cky-feature-text"},[e._v(" "+e._s(e.upgradeNoticeTitle)+" ")]),t("div",{staticClass:"cky-available-wrapper"},[t("span",{staticClass:"cky-available-text",domProps:{innerHTML:e._s(e.$i18n.__("Available in: <b> Pro and Ultimate plans </b>","cookie-law-info"))}})])]},proxy:!0}])}),t("div",{staticClass:"cky-row"},[t("div",{staticClass:"cky-col-12"},[t("cky-accordion",{staticClass:"cky-app-accordion-no-border cky-app-accordion-advanced"},[t("cky-accordion-item",{staticClass:"cky-app-accordion-item"},[t("template",{slot:"cky-accordion-trigger"},[t("label",{staticClass:"cky-label cky-app-accordion-title",on:{click:e.toggleAdvanced}},[e.toggleSettings?[e._v(e._s(e.$i18n.__("Hide advanced settings","cookie-law-info")))]:[e._v(e._s(e.$i18n.__("Show advanced settings","cookie-law-info")))]],2)]),t("template",{slot:"cky-accordion-content"},[t("div",{staticClass:"cky-app-box"},[t("div",{staticClass:"cky-form-group cky-row cky-align-center"},[t("label",{staticClass:"cky-col-4 cky-form-heading",staticStyle:{"margin-bottom":"0px"},attrs:{for:"cky-consent-expiration"}},[e._v(e._s(e.$i18n.__("Consent expiration (days)","cookie-law-info"))+" "),t("cky-popper",{attrs:{content:e.expiryMessage,ishtml:!0}})],1),t("div",{staticClass:"cky-col-4"},[t("cky-banner-expiry")],1)])]),t("div",{staticClass:"cky-app-box"},[t("div",{staticClass:"cky-form-group cky-row"},[t("label",{staticClass:"cky-col-4 cky-form-heading",staticStyle:{"margin-bottom":"0px"}},[e._v(e._s(e.$i18n.__("Reload page on consent action","cookie-law-info")))]),t("div",{staticClass:"cky-col-2"},[t("label",{staticClass:"cky-toggle",attrs:{for:"cky-reload-on-accept"}},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.banner.properties.behaviours.reloadBannerOnAccept.status,expression:"\n\t\t\t\t\t\t\t\t\t\t\t\tbanner.properties.behaviours.reloadBannerOnAccept\n\t\t\t\t\t\t\t\t\t\t\t\t\t.status\n\t\t\t\t\t\t\t\t\t\t\t"}],attrs:{type:"checkbox",id:"cky-reload-on-accept"},domProps:{checked:Array.isArray(e.banner.properties.behaviours.reloadBannerOnAccept.status)?e._i(e.banner.properties.behaviours.reloadBannerOnAccept.status,null)>-1:e.banner.properties.behaviours.reloadBannerOnAccept.status},on:{change:function(t){var a=e.banner.properties.behaviours.reloadBannerOnAccept.status,i=t.target,n=!!i.checked;if(Array.isArray(a)){var s=null,o=e._i(a,s);i.checked?o<0&&e.$set(e.banner.properties.behaviours.reloadBannerOnAccept,"status",a.concat([s])):o>-1&&e.$set(e.banner.properties.behaviours.reloadBannerOnAccept,"status",a.slice(0,o).concat(a.slice(o+1)))}else e.$set(e.banner.properties.behaviours.reloadBannerOnAccept,"status",n)}}}),t("span",{staticClass:"cky-toggle-slider",attrs:{"aria-hidden":"true"}})])])])]),"gdpr"===e.appliedLaw?t("cky-accordion",{staticClass:"cky-app-box",attrs:{name:"prior-consent",type:"boxed"}},[t("cky-accordion-item",[t("template",{slot:"cky-accordion-trigger"},[t("label",{staticClass:"cky-app-accordion-title"},[e._v(e._s(e.$i18n.__("Load cookies prior to consent","cookie-law-info")))])]),t("template",{slot:"cky-accordion-content"},[t("cky-notice",{attrs:{type:"warning"}},[t("p",[e._v(" "+e._s(e.$i18n.__("Choosing any of these categories(cookies) to load prior to receiving user consent will make your website non-compliant with GDPR.","cookie-law-info"))+" ")])]),t("div",{staticClass:"cky-banner-tab-cookies"},[e._l(e.cookieGroups,(function(a){return["necessary"!==a.slug?t("div",{key:a.id,staticClass:"cky-form-check cky-align-center"},[t("input",{directives:[{name:"model",rawName:"v-model",value:a.prior_consent,expression:"item.prior_consent"}],staticClass:"cky-cky-form-check-input",attrs:{type:"checkbox",id:"cky-checkbox-prior-consent-"+a.slug},domProps:{checked:Array.isArray(a.prior_consent)?e._i(a.prior_consent,null)>-1:a.prior_consent},on:{change:function(t){var i=a.prior_consent,n=t.target,s=!!n.checked;if(Array.isArray(i)){var o=null,r=e._i(i,o);n.checked?r<0&&e.$set(a,"prior_consent",i.concat([o])):r>-1&&e.$set(a,"prior_consent",i.slice(0,r).concat(i.slice(r+1)))}else e.$set(a,"prior_consent",s)}}}),t("label",{staticClass:"cky-form-check-label",class:["cky-form-check-label"],attrs:{for:"cky-checkbox-prior-consent-"+a.slug}},[e._v(" "+e._s(a.name[e.language])+" ")])]):e._e()]}))],2)],1)],2)],1):e._e(),"gdpr"===e.appliedLaw?t("cky-accordion",{staticClass:"cky-app-box",attrs:{name:"hide-cookies",type:"boxed"}},[t("cky-accordion-item",[t("template",{slot:"cky-accordion-trigger"},[t("label",{staticClass:"cky-app-accordion-title"},[e._v(e._s(e.$i18n.__("Hide categories from banner","cookie-law-info")))])]),t("template",{slot:"cky-accordion-content"},[t("cky-notice",{attrs:{type:"warning"}},[t("p",[e._v(" "+e._s(e.$i18n.__("Hiding any of the categories (with cookies) will make your website non-compliant with GDPR.","cookie-law-info"))+" ")])]),t("div",{staticClass:"cky-banner-tab-cookies"},[e._l(e.cookieGroups,(function(a){return["necessary"!==a.slug?t("div",{key:a.id,staticClass:"cky-form-check cky-align-center"},[t("input",{directives:[{name:"model",rawName:"v-model",value:a.visibility,expression:"item.visibility"}],staticClass:"cky-cky-form-check-input",attrs:{type:"checkbox",id:"cky-checkbox-visibility-"+a.slug,"true-value":!1,"false-value":!0},domProps:{checked:Array.isArray(a.visibility)?e._i(a.visibility,null)>-1:e._q(a.visibility,!1)},on:{change:function(t){var i=a.visibility,n=t.target,s=!n.checked;if(Array.isArray(i)){var o=null,r=e._i(i,o);n.checked?r<0&&e.$set(a,"visibility",i.concat([o])):r>-1&&e.$set(a,"visibility",i.slice(0,r).concat(i.slice(r+1)))}else e.$set(a,"visibility",s)}}}),t("label",{staticClass:"cky-form-check-label",class:["cky-form-check-label"],attrs:{for:"cky-checkbox-visibility-"+a.slug}},[e._v(" "+e._s(a.name[e.language])+" ")])]):e._e()]}))],2)],1)],2)],1):e._e()],1)],2)],1)],1)])],1)},$=[function(){var e=this,t=e._self._c;return t("span",[t("img",{staticClass:"cky-crown-img",attrs:{src:a("7cd5"),alt:"crown-icon"}})])}],A=a("a9f4"),S=a("b02b"),D=a("c068"),B=a("462b"),T=function(){var e=this,t=e._self._c;return t("fragment",[t("div",{staticClass:"cky-banner-region-selector"},[t("div",{staticClass:"cky-row"},[t("label",{staticClass:"cky-col-12 cky-section-heading"},[e._v(" "+e._s(e.geoTargetHeading)+" ")]),t("div",{staticClass:"cky-col-12"},e._l(e.regions[e.law],(function(i){return t("div",{key:"region-"+i.code,staticClass:"cky-form-check cky-align-center"},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.selectedRegion,expression:"selectedRegion"}],staticClass:"cky-cky-form-check-input",attrs:{type:"radio",id:"cky-radio-region-"+i.code},domProps:{value:i.code,checked:e._q(e.selectedRegion,i.code)},on:{click:function(t){return t.preventDefault(),e.handleRegionChange(i.code)},change:function(t){e.selectedRegion=i.code}}}),t("label",{staticClass:"cky-form-check-label cky-action-link",attrs:{for:"cky-radio-region-"+i.code}},[e._v(" "+e._s(i.label)+" "),"ALL"!==i.code?t("span",[t("img",{staticClass:"cky-crown-img",attrs:{src:a("7cd5"),alt:"crown-icon"}})]):e._e()])])})),0)])]),t("cky-connect-modal",{ref:"ckyLocationModal",attrs:{availablePlan:"pro",feature:"config_geo_rules"},scopedSlots:e._u([{key:"title",fn:function(){return[t("img",{attrs:{src:a("e622"),alt:"location"}})]},proxy:!0},{key:"message",fn:function(){return["EU"===e.currentRegionCode?t("div",{staticClass:"cky-feature-text"},[e._v(" "+e._s(e.$i18n.__("Display your banner only in the EU and the UK","cookie-law-info"))+" ")]):"US"===e.currentRegionCode?t("div",{staticClass:"cky-feature-text"},[e._v(" "+e._s(e.$i18n.__("Display your banner only in the US","cookie-law-info"))+" ")]):"OTHER"===e.currentRegionCode?t("div",{staticClass:"cky-feature-text"},[e._v(" "+e._s(e.$i18n.__("Select the countries where you want to display your banner","cookie-law-info"))+" ")]):e._e(),t("div",{staticClass:"cky-available-wrapper"},[t("span",{staticClass:"cky-available-text",domProps:{innerHTML:e._s(e.$i18n.__("Available in: <b>Pro</b> and <b>Ultimate</b> plans","cookie-law-info"))}})])]},proxy:!0}])})],1)},O=[],E=a("76e6"),U={name:"CkyBannerRegionSelector",components:{Fragment:E["a"],CkyConnectModal:C["a"]},props:{law:{type:String,default:"gdpr"}},data(){return{regions:{gdpr:[{label:this.$i18n.__("Worldwide","cookie-law-info"),code:"ALL"},{label:this.$i18n.__("EU Countries & UK","cookie-law-info"),code:"EU"},{label:this.$i18n.__("Select countries","cookie-law-info"),code:"OTHER"}],ccpa:[{label:this.$i18n.__("Worldwide","cookie-law-info"),code:"ALL"},{label:this.$i18n.__("United States","cookie-law-info"),code:"US"},{label:this.$i18n.__("Select countries","cookie-law-info"),code:"OTHER"}]},selectedRegion:"ALL",currentRegionCode:null}},methods:{handleRegionChange(e){this.selectedRegion="ALL","ALL"!==e&&this.showLocationModal(e)},showLocationModal(e){this.$refs.ckyLocationModal.show(),this.currentRegionCode=e}},computed:{banner(){return this.$store.state.banners.current},appliedLaw(){return this.banner.properties.settings.applicableLaw},geoTargetHeading(){return this.$i18n.__("Geo-target Banner","cookie-law-info")}}},N=U,M=Object(f["a"])(N,T,O,!1,null,null,null),G=M.exports,R=function(){var e=this,t=e._self._c;return t("cky-input",{class:{"cky-input-error":e.hasErrors},attrs:{type:"number",inputId:e.inputId,min:e.min,max:e.limit},on:{input:t=>e.checkForErrors(t)},model:{value:e.banner.properties.settings.consentExpiry.value,callback:function(t){e.$set(e.banner.properties.settings.consentExpiry,"value",t)},expression:"banner.properties.settings.consentExpiry.value"}},[e.hasErrors?t("template",{slot:"cky-input-error"},[t("div",{staticClass:"cky-input-error-container"},e._l(e.errors,(function(a,i){return t("div",{key:i,staticClass:"cky-input-inline-error"},[!0===a?[e._v(e._s(e.errorMessages[i]))]:e._e()],2)})),0)]):e._e()],2)},j=[],F={name:"CkyBannerExpiry",components:{},props:{limit:{type:Number,default:9999},min:{type:Number,default:1}},data(){return{errors:{},errorMessages:{maxLimit:this.$i18n.__(`Please enter a value between ${this.min} and ${this.limit}`,"cookie-law-info")},inputId:"cky-consent-expiration"}},methods:{checkForErrors(e){this.errors={},(""===e||e>this.limit||e<this.min)&&(this.errors["maxLimit"]=!0),o["a"].setErrors(this.errors)}},computed:{banner(){return this.$store.state.banners.current},hasErrors(){return Object.keys(this.errors).length},getValue(){return this.banner.properties.settings.consentExpiry.value}},watch:{getValue(e){this.checkForErrors(e)}}},I=F,H=Object(f["a"])(I,R,j,!1,null,null,null),V=H.exports,z=a("2f62"),J={name:"TabGeneral",mixins:[D["a"]],components:{CkyLoader:l["a"],CkyAccordion:A["a"],CkyAccordionItem:S["a"],CkyNotice:B["a"],CkyBannerRegionSelector:G,CkyBannerExpiry:V,CkyConnectModal:C["a"]},data(){return{upgradeNoticeTitle:"",selectedLocation:"all",toggleSettings:!1,contents:{expiryGdpr:this.$i18n.sprintf(this.$i18n.__("Customise the validity period of your users' consent preferences here. While the <b>GDPR</b> does not specify a specific time limit for consent durations, the French Data Protection Authority, <b>CNIL</b>, recommends retaining user choices for a period of six months (<b>180</b> days) as a best practice.","cookie-law-info")),expiryCcpa:this.$i18n.sprintf(this.$i18n.__("Customise the validity period of your users' consent preferences here. The <b>US State Laws</b> do not specify a specific time limit for retaining user choices.","cookie-law-info"))}}},methods:{toggleAdvanced(){this.toggleSettings=!this.toggleSettings},showConnectModal(e){this.upgradeNoticeTitle=e,this.$refs.ckyConnectModal.show()}},computed:{cookieGroups(){return this.$store.state.cookies.items},banner(){return this.$store.state.banners.current},loading(){return this.$store.state.banners.current.id&&!this.$store.state.banners.current.id>0||!Object.prototype.hasOwnProperty.call(this.info,"plan")},appliedLaw(){return this.banner.properties.settings.applicableLaw},expiryMessage(){return"gdpr"===this.appliedLaw?this.contents.expiryGdpr:this.contents.expiryCcpa},language(){return this.$store.state.languages.default&&this.$store.state.languages.default||"en"},...Object(z["d"])("settings",["info"])},watch:{}},K=J,q=(a("90c0"),Object(f["a"])(K,L,$,!1,null,null,null)),Q=q.exports,W=function(){var e=this,t=e._self._c;return e.loading?t("div",{staticClass:"cky-loader-view"},[t("p",{staticClass:"cky-loading-text"},[t("cky-loader"),e._v(" "+e._s(e.$i18n.__("Fetching data","cookie-law-info"))+" ")],1)]):t("div",[t("div",{staticClass:"cky-banner-message"},[t("div",{staticClass:"cky-row cky-align-center"},["gdpr"===e.appliedLaw?t("p",{staticClass:"cky-col-label"},[e._v(" "+e._s(e.$i18n.__("The selected template (opt-in banner) supports GDPR (EU & UK), LGPD (Brazil), PIPEDA (Canada), Law 25 (Quebec), POPIA (South Africa), nFADP (Switzerland), Privacy Act (Australia), PDPL (Saudi Arabia), PDPL (Argentina), PDPL (Andorra), DPA (Faroe Island)","cookie-law-info"))+" ")]):"ccpa"===e.appliedLaw?t("p",{staticClass:"cky-col-label"},[e._v(" "+e._s(e.$i18n.__("The selected template (opt-out banner) supports CCPA/CPRA (California), VCDPA (Virginia), CPA (Colorado), CTDPA (Connecticut), & UCPA (Utah)","cookie-law-info"))+" ")]):e._e()])]),t("div",{staticClass:"cky-section-layout"},[t("div",{staticClass:"cky-form-section cky-form-section-group cky-row cky-align-top"},[t("div",{staticClass:"cky-col-12"},[t("h6",[e._v(e._s(e.$i18n.__("Cookie Notice","cookie-law-info")))]),t("cky-banner-type",{attrs:{banner:e.banner}})],1)]),t("div",{staticClass:"cky-form-section cky-row"},[t("div",{staticClass:"cky-col-12"},[t("h6",[e._v(e._s(e.$i18n.__("Preference Centre","cookie-law-info")))]),t("cky-preference-type")],1)])])])},Y=[],X=function(){var e=this,t=e._self._c;return t("fragment",[t("div",{staticClass:"cky-banner-layout-container"},e._l(e.layouts,(function(a){return t("cky-banner-layout-item",{key:""+a.type,attrs:{properties:a,disabled:"popup"===a.type},on:{upgrade:e.showConnectModal}})})),1),t("div",{staticClass:"cky-banner-position-group cky-col-4"},e._l(e.getSelectedLayout.positions,(function(a){return t("cky-radio",{key:`${e.getSelectedLayout.type}-${a}`,attrs:{id:`cky-consent-type-${e.getSelectedLayout.type}-${a}`,value:""+a,label:e.transformText(""+a)},model:{value:e.selectedPosition,callback:function(t){e.selectedPosition=t},expression:"selectedPosition"}})})),1),t("cky-connect-modal",{ref:"ckyConnectModal",attrs:{availablePlan:"pro",feature:"popup_layout"},scopedSlots:e._u([{key:"title",fn:function(){return[t("img",{attrs:{src:a("3b6a"),alt:"popuplayout"}})]},proxy:!0},{key:"message",fn:function(){return[t("div",{staticClass:"cky-feature-text"},[e._v(" "+e._s(e.$i18n.__("Use a popup layout to boost your consent opt-in rates","cookie-law-info"))+" ")]),t("div",{staticClass:"cky-available-wrapper"},[t("span",{staticClass:"cky-available-text",domProps:{innerHTML:e._s(e.$i18n.__("Available in: <b>Pro and Ultimate plans</b>","cookie-law-info"))}})])]},proxy:!0}])})],1)},Z=[],ee=function(){var e=this,t=e._self._c;return t("div",{staticClass:"cky-banner-layout-group"},[t("p",{staticClass:"cky-banner-layout-title cky-action-link"},[e._v(e._s(e.properties.title)+" "),e.disabled?t("span",[t("img",{staticClass:"cky-crown-img",attrs:{src:a("7cd5"),alt:"crown-icon"}})]):e._e()]),t("div",{staticClass:"cky-banner-layout-items"},[t("div",{key:""+e.properties.type,staticClass:"cky-radio-group",on:{click:function(t){return e.showConnectModal()}}},[t("cky-radio",{attrs:{id:"cky-consent-type-"+e.properties.type,value:""+e.properties.type,icon:`layouts/${e.properties.type}.svg`,disabled:e.disabled},model:{value:e.selectedLayout,callback:function(t){e.selectedLayout=t},expression:"selectedLayout"}})],1)])])},te=[],ae=function(){var e=this,t=e._self._c;return t("label",{class:e.radioClass,attrs:{for:e.id,id:e.id+"-radio-label"}},[t("input",{attrs:{id:e.id,type:"radio",name:e.name,disabled:e.disabled},domProps:{checked:e.isChecked,value:e.value},on:{change:function(t){return e.$emit("change",t.target.value)}}}),e.icon?t("span",{staticClass:"cky-radio-icon",attrs:{id:e.id+"-radio-icon"}},[t("img",{attrs:{src:a("1771")("./"+e.icon)}})]):e._e(),e.label?t("span",{staticClass:"cky-radio-label",attrs:{id:e.id+"-radio-label"}},[e._v(" "+e._s(e.label)+" ")]):e._e()])},ie=[],ne={name:"CkyRadio",components:{},model:{prop:"modelValue",event:"change"},props:{id:{type:String,required:!0},name:{type:String,default:""},value:{type:[String,Boolean],required:!0},label:{type:String,default:""},icon:{type:String,default:""},modelValue:{default:""},disabled:{type:Boolean,default:!1}},data(){return{selected:!1}},computed:{radioClass(){return{"cky-radio":!0,"cky-image-radio":""!==this.icon,checked:this.isChecked}},isChecked(){return this.modelValue==this.value}}},se=ne,oe=Object(f["a"])(se,ae,ie,!1,null,null,null),re=oe.exports,ce={name:"CkyBannerLayoutItem",mixins:[D["a"]],components:{CkyRadio:re},props:{properties:{type:Object,default(){return{}}},disabled:{type:Boolean,default:!0}},data(){return{upgradeModalVisible:!1,publicPath:"/wp-content/plugins/cookie-law-info/admin/dist/"}},methods:{showConnectModal(){this.disabled&&this.$emit("upgrade")},getSelectedLayout(e){const t=this.layouts.filter(t=>t.type===e);return t[0]}},computed:{banner(){return this.$store.state.banners.current},layouts:function(){const e=this.banner.properties.settings.applicableLaw;return o["a"].getLayouts(e)},selectedLayout:{get(){let e=this.banner.properties.settings.type;return"classic"===e&&(e="banner"),""+e},set(e){this.banner.properties.settings.type=e,this.banner.properties.settings.preferenceCenterType="classic"===e?"pushdown":"popup";const t=this.getSelectedLayout(e);this.banner.properties.settings.position=t.default}}},mounted(){}},le=ce,pe=(a("5ae5"),Object(f["a"])(le,ee,te,!1,null,null,null)),de=pe.exports,ue={name:"CkyBannerType",components:{CkyConnectModal:C["a"],Fragment:E["a"],CkyBannerLayoutItem:de,CkyRadio:re},props:{title:{type:String,default:""},type:{type:String,default:"normal"}},methods:{showConnectModal(){this.$refs.ckyConnectModal.show()},transformText(e){const t=e.replace("-"," ");return t.charAt(0).toUpperCase()+t.slice(1)}},computed:{banner(){return this.$store.state.banners.current},layouts:function(){const e=this.banner.properties.settings.applicableLaw;return o["a"].getLayouts(e)},getSelectedLayout(){let e=this.banner.properties.settings.type;"classic"===e&&(e="banner");const t=this.layouts.filter(t=>t.type===e);return t[0]},selectedPosition:{get(){const e=this.banner.properties.settings.position;return""+e},set(e){this.banner.properties.settings.position=e}},...Object(z["d"])("settings",["info"]),loading(){return!Object.prototype.hasOwnProperty.call(this.info,"plan")}}},ge=ue,ye=(a("cd0a"),Object(f["a"])(ge,X,Z,!1,null,null,null)),fe=ye.exports,he=function(){var e=this,t=e._self._c;return t("fragment",[t("div",{staticClass:"cky-preference-layout-container"},[t("div",{staticClass:"cky-preference-layout"},e._l(e.playouts,(function(e){return t("cky-preference-layout-item",{key:""+e.type,attrs:{properties:e}})})),1),"sidebar"===e.getSelectedPlayout.type?t("div",{staticClass:"cky-sidebar-position-group cky-col-4"},e._l(e.getSelectedPlayout.positions,(function(a){return t("cky-radio",{key:`${e.getSelectedPlayout.type}-${a}`,attrs:{id:`cky-preference-type-${e.getSelectedPlayout.type}-${a}`,value:""+a,label:e.transformText(""+a)},model:{value:e.selectedPosition,callback:function(t){e.selectedPosition=t},expression:"selectedPosition"}})})),1):e._e()]),"gdpr"===e.appliedLaw?t("div",{staticClass:"cky-row",class:{"cky-text-muted":e.isDisabled}},[t("div",{staticClass:"cky-col-3"},[t("label",{staticClass:"cky-zero-padding cky-col-label",attrs:{for:"cky-toggle-categories-on-firstlayer"}},[e._v(e._s(e.$i18n.__("Categories on first layer","cookie-law-info"))),t("cky-popper",{attrs:{content:e.$i18n.__("Enabling this option will display cookie categories on the first layer of your banner. The categories will be displayed on the second layer (preference centre) even if this option is disabled.","cookie-law-info"),position:"right"}})],1)]),t("div",{staticClass:"cky-col-5",class:{"cky-text-muted":e.isDisabled}},[t("label",{staticClass:"cky-toggle",attrs:{for:"cky-toggle-categories-on-firstlayer"}},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.currentBanner.properties.config.categoryPreview.status,expression:"currentBanner.properties.config.categoryPreview.status"}],attrs:{type:"checkbox",id:"cky-toggle-categories-on-firstlayer",disabled:e.isDisabled},domProps:{checked:Array.isArray(e.currentBanner.properties.config.categoryPreview.status)?e._i(e.currentBanner.properties.config.categoryPreview.status,null)>-1:e.currentBanner.properties.config.categoryPreview.status},on:{change:function(t){var a=e.currentBanner.properties.config.categoryPreview.status,i=t.target,n=!!i.checked;if(Array.isArray(a)){var s=null,o=e._i(a,s);i.checked?o<0&&e.$set(e.currentBanner.properties.config.categoryPreview,"status",a.concat([s])):o>-1&&e.$set(e.currentBanner.properties.config.categoryPreview,"status",a.slice(0,o).concat(a.slice(o+1)))}else e.$set(e.currentBanner.properties.config.categoryPreview,"status",n)}}}),e.isDisabled?t("cky-popper",{attrs:{content:e.$i18n.__("This feature is available with <b>Banner + Push down</b> layout only.","cookie-law-info"),ishtml:!0,position:"right"}},[t("span",{staticClass:"cky-toggle-slider",attrs:{id:"cky-toggle-categories-on-firstlayer-text","aria-hidden":"true"}})]):t("span",{staticClass:"cky-toggle-slider",attrs:{id:"cky-toggle-categories-on-firstlayer-text","aria-hidden":"true"}})],1)])]):e._e()])},be=[],ke=function(){var e=this,t=e._self._c;return t("div",{staticClass:"cky-preference-layout-group"},[t("p",{staticClass:"cky-preference-layout-title"},[e._v(e._s(e.properties.title))]),t("div",{staticClass:"cky-preference-layout-items"},[t("div",{key:""+e.properties.type,staticClass:"cky-radio-group"},["pushdown"===e.properties.type&&e.isDisabled?t("cky-popper",{attrs:{content:this.contents,ishtml:!0,position:"right"}},[t("cky-radio",{class:{"cky-icon-disabled":e.isDisabled},attrs:{id:"cky-preference-type-"+e.properties.type,value:""+e.properties.type,icon:`layouts/${e.properties.type}.svg`,disabled:!0},model:{value:e.pselectedLayout,callback:function(t){e.pselectedLayout=t},expression:"pselectedLayout"}})],1):t("cky-radio",{attrs:{id:"cky-preference-type-"+e.properties.type,value:""+e.properties.type,icon:`layouts/${e.properties.type}.svg`},model:{value:e.pselectedLayout,callback:function(t){e.pselectedLayout=t},expression:"pselectedLayout"}})],1)])])},ve=[],me={name:"CkyBannerLayoutItem",mixins:[D["a"]],components:{CkyRadio:re},data(){return{isHovered:!1,contents:this.$i18n.sprintf(this.$i18n.__("The <b>Push down</b> style is supported for the <b>Banner</b> layout only.","cookie-law-info"))}},props:{properties:{type:Object,default(){return{}}}},computed:{...Object(z["c"])("banners",{currentBanner:"getCurrentBanner"}),isDisabled(){let e=this.currentBanner.properties.settings.type;return e.includes("-sidebar")&&(e=e.replace("-sidebar","")),"popup"!==this.properties.type&&"sidebar"!==this.properties.type&&("banner"!==e&&"classic"!==e)},pselectedLayout:{get(){let e=this.currentBanner.properties.settings.preferenceCenterType,t=this.currentBanner.properties.settings.type;return"classic"===t&&(e="pushdown"),e.includes("sidebar")&&(e=e.replace("-left",""),e=e.replace("-right","")),""+e},set(e){const t=this.currentBanner.properties.settings.type;this.currentBanner.properties.settings.preferenceCenterType="sidebar"===e?"sidebar-left":e,"pushdown"===e&&(this.currentBanner.properties.settings.type="classic"),"pushdown"!==e&&"classic"===t&&(this.currentBanner.properties.settings.type="banner")}}}},Ce=me,we=(a("9f63"),Object(f["a"])(Ce,ke,ve,!1,null,null,null)),_e=we.exports,Pe={name:"CkyPreferenceType",components:{Fragment:E["a"],CkyPreferenceLayoutItem:_e,CkyRadio:re},methods:{transformText(e){return e.charAt(0).toUpperCase()+e.slice(1)},checkCategoryOnSave(e){this.currentBanner.properties.config.preferenceCenter.elements.categories.elements.toggle.status=this.currentBanner.properties.config.preferenceCenter.elements.buttons.elements.save.status="pushdown"!==e||!this.currentBanner.properties.config.categoryPreview.status}},computed:{...Object(z["c"])("banners",{currentBanner:"getCurrentBanner"}),appliedLaw(){return this.currentBanner.properties.settings.applicableLaw},isDisabled(){let e=this.currentBanner.properties.settings.type;return e.includes("-sidebar")&&(e=e.replace("-sidebar","")),"classic"!==e},playouts:function(){return o["a"].getPreferenceLayouts(this.appliedLaw)},getSelectedPlayout(){let e=this.currentBanner.properties.settings.preferenceCenterType;e.includes("sidebar")&&(e=e.replace("-left",""),e=e.replace("-right",""));const t=this.playouts.filter(t=>t.type===e);return this.checkCategoryOnSave(e),t[0]},selectedPosition:{get(){let e=this.currentBanner.properties.settings.preferenceCenterType;return e=e.replace("sidebar-",""),""+e},set(e){this.currentBanner.properties.settings.preferenceCenterType="sidebar-"+e}}}},xe=Pe,Le=(a("77ac"),Object(f["a"])(xe,he,be,!1,null,null,null)),$e=Le.exports,Ae={name:"TabLayout",components:{CkyLoader:l["a"],CkyBannerType:fe,CkyPreferenceType:$e},data(){return{bannerPreview:!1,showPositions:!0,customLogoType:"image"}},methods:{toggleDetailSaveButton(){const e=this.banner.properties.config;let t=!0;this.showCategoriesOnFirstLayer&&e.categoryPreview.status&&(t=!1),e.preferenceCenter.elements.buttons.elements.save.status=t}},computed:{banner(){return this.$store.state.banners.current},loading(){return this.$store.state.banners.current.id&&!this.$store.state.banners.current.id>0||!Object.prototype.hasOwnProperty.call(this.info,"plan")},appliedLaw(){return this.banner.properties.settings.applicableLaw},showCategoriesOnFirstLayer(){return"classic"===this.banner.properties.settings.type},...Object(z["d"])("settings",["info"])},watch:{"banner.properties.settings":{handler(){this.toggleDetailSaveButton()},deep:!0}}},Se=Ae,De=(a("297a"),Object(f["a"])(Se,W,Y,!1,null,null,null)),Be=De.exports,Te=function(){var e=this,t=e._self._c;return e.loading?t("div",{staticClass:"cky-loader-view"},[t("p",{staticClass:"cky-loading-text"},[t("cky-loader"),e._v(" "+e._s(e.$i18n.__("Fetching data","cookie-law-info"))+" ")],1)]):t("div",[t("div",{staticClass:"cky-banner-message"},[t("div",{staticClass:"cky-row cky-align-center"},["gdpr"===e.appliedLaw?t("p",{staticClass:"cky-col-label"},[e._v(" "+e._s(e.$i18n.__("The selected template (opt-in banner) supports GDPR (EU & UK), LGPD (Brazil), PIPEDA (Canada), Law 25 (Quebec), POPIA (South Africa), nFADP (Switzerland), Privacy Act (Australia), PDPL (Saudi Arabia), PDPL (Argentina), PDPL (Andorra), DPA (Faroe Island)","cookie-law-info"))+" ")]):"ccpa"===e.appliedLaw?t("p",{staticClass:"cky-col-label"},[e._v(" "+e._s(e.$i18n.__("The selected template (opt-out banner) supports CCPA/CPRA (California), VCDPA (Virginia), CPA (Colorado), CTDPA (Connecticut), & UCPA (Utah)","cookie-law-info"))+" ")]):e._e()])]),e.customHtmlMode?t("cky-notice",{attrs:{type:"warning"}},[t("p",[t("span",[e._v(e._s(e.$i18n.__("You're in Custom HTML mode. ")))]),e._v(" "+e._s(e.$i18n.__("The edits that you have made in the HTML Editor will override any changes made to the settings below","cookie-law-info"))+" ")])]):e._e(),t("div",{staticClass:"cky-banner-theme-radio"},[t("label",{staticClass:"cky-col-label",staticStyle:{"font-weight":"500"}},[e._v(e._s(e.$i18n.__("Colour scheme","cookie-law-info")))]),e._l(e.colourSchemes,(function(a){return t("div",{key:a.id,staticClass:"cky-form-check cky-form-check-inline"},[t("cky-radio",{attrs:{id:a.id,value:a.id,label:a.label},model:{value:e.banner.properties.settings.theme,callback:function(t){e.$set(e.banner.properties.settings,"theme",t)},expression:"banner.properties.settings.theme"}})],1)}))],2),t("tab-content-accordion",{attrs:{language:e.language}})],1)},Oe=[],Ee=a("c702"),Ue={name:"TabContent",components:{CkyRadio:re,CkyLoader:l["a"],TabContentAccordion:Ee["a"],CkyNotice:B["a"]},data(){return{customHtmlMode:!1}},methods:{saveConfig:async function(){await o["a"].save(),this.$root.$emit("triggerNotification",{type:"success",message:"Successfully saved"})}},computed:{banner(){return this.$store.state.banners.current},loading(){return this.$store.state.banners.current.id&&!this.$store.state.banners.current.id>0},appliedLaw(){return this.banner.properties.settings.applicableLaw},presets(){return this.$store.state.banners.presets},contentSections:function(){let e={};if(this.loading)return e;const t=this.banner.properties.settings.applicableLaw;return e=o["a"].getContentSections(t),e},colourSchemes:function(){let e=[];return this.loading||(e=[{id:"light",label:"Light"},{id:"dark",label:"Dark"},{id:"custom",label:"Custom"}]),e},language(){return this.$store.state.languages.default&&this.$store.state.languages.default||"en"}},watch:{"banner.properties.settings":{async handler(){o["a"].resetPreset(),await o["a"].showPreview()},deep:!0}}},Ne=Ue,Me=(a("464b"),Object(f["a"])(Ne,Te,Oe,!1,null,null,null)),Ge=Me.exports,Re=function(){var e=this,t=e._self._c;return e.loading?t("div",{staticClass:"cky-loader-view"},[t("p",{staticClass:"cky-loading-text"},[t("cky-loader"),e._v(" "+e._s(e.$i18n.__("Fetching data","cookie-law-info"))+" ")],1)]):t("div",[t("div",{staticClass:"cky-banner-message"},[t("div",{staticClass:"cky-row cky-align-center"},["gdpr"===e.appliedLaw?t("p",{staticClass:"cky-col-label"},[e._v(" "+e._s(e.$i18n.__("The selected template (opt-in banner) supports GDPR (EU & UK), LGPD (Brazil), PIPEDA (Canada), Law 25 (Quebec), POPIA (South Africa), nFADP (Switzerland), Privacy Act (Australia), PDPL (Saudi Arabia), PDPL (Argentina), PDPL (Andorra), DPA (Faroe Island)","cookie-law-info"))+" ")]):"ccpa"===e.appliedLaw?t("p",{staticClass:"cky-col-label"},[e._v(" "+e._s(e.$i18n.__("The selected template (opt-out banner) supports CCPA/CPRA (California), VCDPA (Virginia), CPA (Colorado), CTDPA (Connecticut), & UCPA (Utah)","cookie-law-info"))+" ")]):e._e()])]),t("div",{staticClass:"cky-section-css"},[t("div",{staticClass:"cky-form-group cky-row"},[t("label",{staticClass:"cky-col-12 cky-action-link"},[e._v(" "+e._s(e.$i18n.__("Custom CSS for additional styling","cookie-law-info"))+" "),t("img",{staticClass:"cky-crown-img",attrs:{src:a("7cd5"),alt:"crown-icon"}})]),t("div",{staticClass:"cky-col-12"},[t("textarea",{directives:[{name:"model",rawName:"v-model",value:e.banner.properties.meta.customCSS,expression:"banner.properties.meta.customCSS"}],staticClass:"cky-form-control",attrs:{rows:"12",disabled:!e.info.plan.features.popup_layout,id:"cky-textarea-custom-css"},domProps:{value:e.banner.properties.meta.customCSS},on:{input:function(t){t.target.composing||e.$set(e.banner.properties.meta,"customCSS",t.target.value)}}}),t("cky-connect-modal",{staticClass:"cky-connect-modal-css",attrs:{visible:!0,availablePlan:"premium",feature:"custom_css"},scopedSlots:e._u([{key:"message",fn:function(){return[t("div",{staticClass:"cky-feature-text"},[e._v(" "+e._s(e.$i18n.__("Put your cookie banner in the spotlight with custom CSS","cookie-law-info"))+" ")]),t("div",{staticClass:"cky-available-wrapper"},[t("span",{staticClass:"cky-available-text",domProps:{innerHTML:e._s(e.$i18n.__("Available in: <b>All premium plans</b>","cookie-law-info"))}})])]},proxy:!0}])})],1)])])])},je=[],Fe={name:"TabCss",components:{CkyLoader:l["a"],CkyConnectModal:C["a"]},data(){return{}},methods:{},computed:{banner(){return this.$store.state.banners.current},loading(){return this.$store.state.banners.current.id&&!this.$store.state.banners.current.id>0},appliedLaw(){return this.banner.properties.settings.applicableLaw},...Object(z["d"])("settings",["info"])}},Ie=Fe,He=(a("3a5b"),Object(f["a"])(Ie,Re,je,!1,null,null,null)),Ve=He.exports,ze=a("63ea"),Je=a.n(ze),Ke={name:"Customize",mixins:[c["a"]],components:{CkyLoader:l["a"],CkyIcon:p["a"],CkyBannerPreview:b,CkyDropdown:k["a"],CkyBannerSelector:x,TabGeneral:Q,TabLayout:Be,TabContent:Ge,TabCss:Ve},data(){return{tabs:[{id:"general",icon:"general",title:"General"},{id:"layout",icon:"layout",title:"Layout"},{id:"content",icon:"note",title:"Content & Colours"},{id:"css",icon:"css",title:"Custom CSS"}],currentTab:"general",previewLoader:!1,loading:!0,initialStoreState:null}},methods:{hasChanges(){return!!(this.initialStoreState&&this.banner&&this.cookieGroups)&&!Je()(this.initialStoreState,{banner:this.banner,cookies:this.cookieGroups})},handleBeforeUnload(e){this.hasChanges()&&(e.preventDefault(),e.returnValue="")},async loadCookies(){try{await this.$store.dispatch("cookies/reInit")}catch(e){this.showErrors(this.$i18n.__("Failed to load cookies please try again later!","cookie-law-info"))}},showIcon(e){return!!e.icon},loadBanner:async function(){try{await o["a"].getActiveBanner(),await o["a"].loadPresets()}catch(e){console.log(e),this.showErrors(this.$i18n.__("An unexpected error occurred please try reloading the page or logging in again.","cookie-law-info"))}},saveConfig:async function(){this.$refs.ckyButtonSaveBanner.startLoading();try{await o["a"].bulkUpdate({clear:!1}),await this.$store.dispatch("cookies/bulkUpdate"),this.$root.$emit("triggerNotification",{type:"success",message:this.$i18n.__("Update was successful!","cookie-law-info")})}catch(e){console.log(e),this.showErrors(this.$i18n.__("Problem occurred while saving your settings. Please try again later!","cookie-law-info"))}this.$refs.ckyButtonSaveBanner.stopLoading(),await this.loadCookies(),this.initialStoreState={banner:JSON.parse(JSON.stringify(this.banner)),cookies:JSON.parse(JSON.stringify(this.cookieGroups))}},updateContentPreview:Object(s["b"])((async function(){r["a"].startLoading(),await o["a"].updateContentPreview(),r["a"].stopLoading()}),500),updateSettingsPreview:Object(s["c"])((async function(){await o["a"].showPreview()}),500),showErrors(e){this.$root.$emit("triggerNotification",{type:"error",message:e})}},computed:{currentTabComponent:function(){return"tab-"+this.currentTab.toLowerCase()},tab(){return this.tabs.find(e=>e.id==this.currentTab)},banner(){return this.$store.state.banners.current},cookieGroups(){return this.$store.state.cookies.items},preview:{get(){return this.$store.state.banners.preview},set(e){this.$store.state.banners.preview=e}},connected(){let e=this.getOption("account");return e.connected},banners(){return this.$store.state.banners.items||{}},appliedLaw(){return this.banner.properties.settings.applicableLaw},noticeDisabled(){return!1===this.banner.properties.config.notice.status},publishDisabled(){return o["a"].hasErrors()},isLoaded(){return!this.loading&&!!this.banner.id}},watch:{"banner.properties.config":{async handler(){this.loading||(await o["a"].resetTheme(),r["a"].startLoading(),await this.updateSettingsPreview(),r["a"].stopLoading())},deep:!0},"banner.properties.settings":{async handler(){this.loading||(r["a"].startLoading(),await this.updateSettingsPreview(),r["a"].stopLoading())},deep:!0},"banner.contents":{async handler(){this.loading||this.updateContentPreview()},deep:!0},"banner.properties.config.categoryPreview.status":{async handler(){r["a"].startLoading(),await this.updateSettingsPreview(),r["a"].stopLoading()},deep:!0},"banner.properties.config.notice.status":{handler(e){!1===e&&(this.preview=!1)},deep:!0},cookieGroups:{async handler(){r["a"].startLoading(),await this.updateSettingsPreview(),r["a"].stopLoading()},deep:!0},preview:{async handler(){this.preview?(r["a"].startLoading(),await this.updateSettingsPreview(),r["a"].stopLoading()):o["a"].hidePreview()}}},async mounted(){this.loading=!0,await this.loadBanner(),await this.loadCookies(),this.banner&&this.cookieGroups&&(this.initialStoreState={banner:JSON.parse(JSON.stringify(this.banner)),cookies:JSON.parse(JSON.stringify(this.cookieGroups))}),this.loading=!1,await o["a"].loadTemplate(),window.addEventListener("beforeunload",this.handleBeforeUnload)},beforeDestroy(){o["a"].closePreview(!0),o["a"].reset(),window.removeEventListener("beforeunload",this.handleBeforeUnload)}},qe=Ke,Qe=(a("4e5b"),Object(f["a"])(qe,i,n,!1,null,null,null));t["default"]=Qe.exports},"37f1":function(e,t,a){e.exports=a.p+"img/cookieyes-logo.svg"},"3a5b":function(e,t,a){"use strict";a("5b48")},"3b6a":function(e,t,a){e.exports=a.p+"img/popup-layout.svg"},"3d1a":function(e,t,a){},"464b":function(e,t,a){"use strict";a("acd1")},"4a95":function(e,t,a){e.exports=a.p+"img/status.svg"},"4ab9":function(e,t,a){e.exports=a.p+"img/tick.svg"},"4e5b":function(e,t,a){"use strict";a("5e04")},"505c":function(e,t,a){"use strict";a("136f")},"50c3":function(e,t,a){e.exports=a.p+"img/arrow-right.svg"},"51f2":function(e,t,a){e.exports=a.p+"img/search.svg"},5544:function(e,t,a){e.exports=a.p+"img/location.svg"},"5ae5":function(e,t,a){"use strict";a("3d1a")},"5b48":function(e,t,a){},"5e04":function(e,t,a){},"5fa2":function(e,t,a){e.exports=a.p+"img/scan-now.svg"},6008:function(e,t,a){e.exports=a.p+"img/popup.svg"},"694f":function(e,t,a){"use strict";a("aba1")},"69e9":function(e,t,a){e.exports=a.p+"img/pushdown.svg"},"6a22":function(e,t,a){e.exports=a.p+"img/help.svg"},"6aed":function(e,t,a){e.exports=a.p+"img/scan-history.svg"},"6c96":function(e,t,a){e.exports=a.p+"img/time-schedule.svg"},"6e70":function(e,t,a){e.exports=a.p+"img/loader.svg"},"6ee3":function(e,t,a){e.exports=a.p+"img/arrow-bold.svg"},"6f43":function(e,t,a){e.exports=a.p+"img/banner-top.svg"},"73cc":function(e,t,a){e.exports=a.p+"img/box-top-right.svg"},"76e6":function(e,t,a){"use strict";a.d(t,"a",(function(){return _}));var i,n=Symbol(),s=Symbol(),o=Symbol(),r=Symbol(),c=function(e){return"frag"in e};function l(e,t){s in e||(e[s]=t,Object.defineProperty(e,"parentNode",{get:function(){return this[s]||this.parentElement}}))}function p(e){o in e||(e[o]=!0,Object.defineProperty(e,"nextSibling",{get:function(){var e=this.parentNode.childNodes,t=e.indexOf(this);return t>-1&&e[t+1]||null}}))}function d(e,t){while(e.parentNode!==t){var a=e,i=a.parentNode;i&&(e=i)}return e}function u(e){if(!i){var t=Object.getOwnPropertyDescriptor(Node.prototype,"childNodes");i=t.get}var a=i.apply(e),n=Array.from(a).map((function(t){return d(t,e)}));return n.filter((function(e,t){return e!==n[t-1]}))}function g(e){r in e||(e[r]=!0,Object.defineProperties(e,{childNodes:{get:function(){return this.frag||u(this)}},firstChild:{get:function(){return this.childNodes[0]||null}}}),e.hasChildNodes=function(){return this.childNodes.length>0})}function y(){var e;(e=this.frag[0]).before.apply(e,arguments)}function f(){var e=this.frag,t=e.splice(0,e.length);t.forEach((function(e){e.remove()}))}var h=function e(t){var a;return(a=Array.prototype).concat.apply(a,t.map((function(t){return c(t)?e(t.frag):t})))};function b(e,t){var a=e[n];t.before(a),l(a,e),e.frag.unshift(a)}function k(e){if(c(this)){var t=this.frag.indexOf(e);if(t>-1){var a=this.frag.splice(t,1),i=a[0];0===this.frag.length&&b(this,i),e.remove()}}else{var n=u(this),s=n.indexOf(e);s>-1&&e.remove()}return e}function v(e,t){var a=this,i=e.frag||[e];if(c(this)){var n=this.frag;if(t){var s=n.indexOf(t);s>-1&&(n.splice.apply(n,[s,0].concat(i)),t.before.apply(t,i))}else{var o=n[n.length-1];n.push.apply(n,i),o.after.apply(o,i)}C(this)}else t?this.childNodes.includes(t)&&t.before.apply(t,i):this.append.apply(this,i);i.forEach((function(e){l(e,a)}));var r=i[i.length-1];return p(r),e}function m(e){var t=this.frag,a=t[t.length-1];return a.after(e),l(e,this),C(this),t.push(e),e}function C(e){var t=e[n];e.frag[0]===t&&(e.frag.shift(),t.remove())}var w={inserted:function(e){var t=e.parentNode,a=e.nextSibling,i=e.previousSibling,s=Array.from(e.childNodes),o=document.createComment("");0===s.length&&s.push(o),e.frag=s,e[n]=o;var r=document.createDocumentFragment();r.append.apply(r,h(s)),e.replaceWith(r),s.forEach((function(t){l(t,e),p(t)})),g(e),Object.assign(e,{remove:f,appendChild:m,insertBefore:v,removeChild:k,before:y}),Object.defineProperty(e,"innerHTML",{set:function(e){var t=this,a=document.createElement("div");a.innerHTML=e;var i=this.frag.length;Array.from(a.childNodes).forEach((function(e){t.appendChild(e)})),a.append.apply(a,this.frag.splice(0,i))},get:function(){return""}}),t&&(Object.assign(t,{removeChild:k,insertBefore:v}),l(e,t),g(t)),a&&p(e),i&&p(i)},unbind:function(e){e.remove()}},_={name:"Fragment",directives:{frag:w},render:function(e){return e("div",{directives:[{name:"frag"}]},this.$slots["default"])}}},"77ac":function(e,t,a){"use strict";a("927b")},7801:function(e,t,a){e.exports=a.p+"img/sidebar.svg"},"78d0":function(e,t,a){e.exports=a.p+"img/reg.svg"},"857e":function(e,t,a){e.exports=a.p+"img/arrow-left.svg"},"896a":function(e,t,a){e.exports=a.p+"img/arrow-down.svg"},"89bc":function(e,t,a){e.exports=a.p+"img/classic-bottom.svg"},"90c0":function(e,t,a){"use strict";a("2698")},"927b":function(e,t,a){},"969c":function(e,t,a){e.exports=a.p+"img/gdpr-us.png"},"9b19":function(e,t,a){e.exports=a.p+"img/logo.svg"},"9d9c":function(e,t,a){e.exports=a.p+"img/banner.svg"},"9deb":function(e,t,a){},"9f63":function(e,t,a){"use strict";a("cf11")},aba1:function(e,t,a){},acd1:function(e,t,a){},afb7:function(e,t,a){e.exports=a.p+"img/warning.svg"},b5fb:function(e,t,a){e.exports=a.p+"img/arrow-back.svg"},b8f6:function(e,t,a){e.exports=a.p+"img/cookie-policy.svg"},b94f:function(e,t,a){e.exports=a.p+"img/arrow.svg"},bca3:function(e,t,a){e.exports=a.p+"img/classic-top.svg"},bde3:function(e,t,a){"use strict";a.d(t,"a",(function(){return n}));var i=a("87ea");const n={data(){return{checkFocusTimer:0,hidden:"hidden",visibilityChange:"visibilitychange",hasFocus:!1}},components:{},computed:{account(){return Object(i["e"])("account")}},mounted(){this.initialize()},beforeDestroy(){document.removeEventListener(this.visibilityChange,this.handleVisibilityChange)},methods:{initialize(){!0!==this.account.connected&&("undefined"!==typeof document.hidden?(this.hidden="hidden",this.visibilityChange="visibilitychange"):"undefined"!==typeof document.msHidden?(this.hidden="msHidden",this.visibilityChange="msvisibilitychange"):"undefined"!==typeof document.webkitHidden&&(this.hidden="webkitHidden",this.visibilityChange="webkitvisibilitychange"),document.addEventListener(this.visibilityChange,this.handleVisibilityChange,!1))},handleVisibilityChange(){document[this.hidden]||this.checkForScreenChange()},async checkForScreenChange(){await Object(i["j"])(),this.account.connected&&(document.removeEventListener(this.visibilityChange,this.handleVisibilityChange),this.$router.redirectToDashboard(this.$route.name))}}}},bf37:function(e,t,a){e.exports=a.p+"img/close.svg"},c2c9:function(e,t,a){"use strict";var i=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"click-outside",rawName:"v-click-outside",value:e.hide,expression:"hide"}],staticClass:"cky-dropdown",class:{open:e.visible}},[t("button",{staticClass:"cky-button-icon cky-dropdown-anchor",attrs:{disabled:e.disabled},domProps:{innerHTML:e._s(e.text)},on:{click:function(t){return e.toggle()}}}),t("div",{staticClass:"cky-dropdown-content",on:{click:function(t){return e.hide()}}},[e._t("default")],2)])},n=[],s={name:"CkyDropdown",props:{text:{type:String,default:""},disabled:{type:Boolean,default:!1},sticky:{type:Boolean,default:!1},active:{type:Boolean,default:!1}},data(){return{visible:!1}},methods:{toggle(){this.visible=!this.visible,this.$emit("ckyToggleDropDown")},hide(){this.sticky||(this.visible=!1)}},mounted(){this.visible=this.active}},o=s,r=(a("505c"),a("2877")),c=Object(r["a"])(o,i,n,!1,null,null,null);t["a"]=c.exports},c3cb:function(e,t,a){e.exports=a.p+"img/lang.svg"},cd0a:function(e,t,a){"use strict";a("9deb")},cf05:function(e,t,a){e.exports=a.p+"img/logo.png"},cf11:function(e,t,a){},d520:function(e,t,a){e.exports=a.p+"img/box.svg"},d645:function(e,t,a){e.exports=a.p+"img/action-alert.svg"},e02a:function(e,t,a){e.exports=a.p+"img/accessyes-icon.svg"},e56e:function(e,t,a){e.exports=a.p+"img/tooltip.svg"},e622:function(e,t,a){e.exports=a.p+"img/geo-location.svg"},eb98:function(e,t,a){e.exports=a.p+"img/success-circle.svg"},ef87:function(e,t,a){e.exports=a.p+"img/external.svg"},f222:function(e,t,a){e.exports=a.p+"img/check.svg"},f499:function(e,t,a){e.exports=a.p+"img/blue-tick.svg"},f72a:function(e,t,a){e.exports=a.p+"img/trends.png"},f7fc:function(e,t,a){e.exports=a.p+"img/center.svg"},fb71:function(e,t,a){e.exports=a.p+"img/circle-warning.svg"},fba1:function(e,t,a){e.exports=a.p+"img/schedule-scan.svg"}}]);