!function(){var e={649:function(e){e.exports=function(e,t){var o,n,r=0;function l(){var l,a,i=o,c=arguments.length;e:for(;i;){if(i.args.length===arguments.length){for(a=0;a<c;a++)if(i.args[a]!==arguments[a]){i=i.next;continue e}return i!==o&&(i===n&&(n=i.prev),i.prev.next=i.next,i.next&&(i.next.prev=i.prev),i.next=o,i.prev=null,o.prev=i,o=i),i.val}i=i.next}for(l=new Array(c),a=0;a<c;a++)l[a]=arguments[a];return i={args:l,val:e.apply(null,l)},o?(o.prev=i,i.next=o):n=i,r===t.maxSize?(n=n.prev).next=null:r++,o=i,i.val}return t=t||{},l.clear=function(){o=null,n=null,r=0},l}},487:function(e){var t={utf8:{stringToBytes:function(e){return t.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(t.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],o=0;o<e.length;o++)t.push(255&e.charCodeAt(o));return t},bytesToString:function(e){for(var t=[],o=0;o<e.length;o++)t.push(String.fromCharCode(e[o]));return t.join("")}}};e.exports=t},184:function(e,t){var o;
/*!
  Copyright (c) 2017 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/!function(){"use strict";var n={}.hasOwnProperty;function r(){for(var e=[],t=0;t<arguments.length;t++){var o=arguments[t];if(o){var l=typeof o;if("string"===l||"number"===l)e.push(o);else if(Array.isArray(o)&&o.length){var a=r.apply(null,o);a&&e.push(a)}else if("object"===l)for(var i in o)n.call(o,i)&&o[i]&&e.push(i)}}return e.join(" ")}e.exports?(r.default=r,e.exports=r):void 0===(o=function(){return r}.apply(t,[]))||(e.exports=o)}()},12:function(e){var t,o;t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return 16711935&o.rotl(e,8)|**********&o.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=o.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],o=0,n=0;o<e.length;o++,n+=8)t[n>>>5]|=e[o]<<24-n%32;return t},wordsToBytes:function(e){for(var t=[],o=0;o<32*e.length;o+=8)t.push(e[o>>>5]>>>24-o%32&255);return t},bytesToHex:function(e){for(var t=[],o=0;o<e.length;o++)t.push((e[o]>>>4).toString(16)),t.push((15&e[o]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],o=0;o<e.length;o+=2)t.push(parseInt(e.substr(o,2),16));return t},bytesToBase64:function(e){for(var o=[],n=0;n<e.length;n+=3)for(var r=e[n]<<16|e[n+1]<<8|e[n+2],l=0;l<4;l++)8*n+6*l<=8*e.length?o.push(t.charAt(r>>>6*(3-l)&63)):o.push("=");return o.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/gi,"");for(var o=[],n=0,r=0;n<e.length;r=++n%4)0!=r&&o.push((t.indexOf(e.charAt(n-1))&Math.pow(2,-2*r+8)-1)<<2*r|t.indexOf(e.charAt(n))>>>6-2*r);return o}},e.exports=o},960:function(e,t){"use strict";var o;Object.defineProperty(t,"__esModule",{value:!0}),t.Doctype=t.CDATA=t.Tag=t.Style=t.Script=t.Comment=t.Directive=t.Text=t.Root=t.isTag=t.ElementType=void 0,function(e){e.Root="root",e.Text="text",e.Directive="directive",e.Comment="comment",e.Script="script",e.Style="style",e.Tag="tag",e.CDATA="cdata",e.Doctype="doctype"}(o=t.ElementType||(t.ElementType={})),t.isTag=function(e){return e.type===o.Tag||e.type===o.Script||e.type===o.Style},t.Root=o.Root,t.Text=o.Text,t.Directive=o.Directive,t.Comment=o.Comment,t.Script=o.Script,t.Style=o.Style,t.Tag=o.Tag,t.CDATA=o.CDATA,t.Doctype=o.Doctype},915:function(e,t,o){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,o,n){void 0===n&&(n=o);var r=Object.getOwnPropertyDescriptor(t,o);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[o]}}),Object.defineProperty(e,n,r)}:function(e,t,o,n){void 0===n&&(n=o),e[n]=t[o]}),r=this&&this.__exportStar||function(e,t){for(var o in e)"default"===o||Object.prototype.hasOwnProperty.call(t,o)||n(t,e,o)};Object.defineProperty(t,"__esModule",{value:!0}),t.DomHandler=void 0;var l=o(960),a=o(790);r(o(790),t);var i={withStartIndices:!1,withEndIndices:!1,xmlMode:!1},c=function(){function e(e,t,o){this.dom=[],this.root=new a.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,"function"==typeof t&&(o=t,t=i),"object"==typeof e&&(t=e,e=void 0),this.callback=null!=e?e:null,this.options=null!=t?t:i,this.elementCB=null!=o?o:null}return e.prototype.onparserinit=function(e){this.parser=e},e.prototype.onreset=function(){this.dom=[],this.root=new a.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null},e.prototype.onend=function(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))},e.prototype.onerror=function(e){this.handleCallback(e)},e.prototype.onclosetag=function(){this.lastNode=null;var e=this.tagStack.pop();this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(e)},e.prototype.onopentag=function(e,t){var o=this.options.xmlMode?l.ElementType.Tag:void 0,n=new a.Element(e,t,void 0,o);this.addNode(n),this.tagStack.push(n)},e.prototype.ontext=function(e){var t=this.lastNode;if(t&&t.type===l.ElementType.Text)t.data+=e,this.options.withEndIndices&&(t.endIndex=this.parser.endIndex);else{var o=new a.Text(e);this.addNode(o),this.lastNode=o}},e.prototype.oncomment=function(e){if(this.lastNode&&this.lastNode.type===l.ElementType.Comment)this.lastNode.data+=e;else{var t=new a.Comment(e);this.addNode(t),this.lastNode=t}},e.prototype.oncommentend=function(){this.lastNode=null},e.prototype.oncdatastart=function(){var e=new a.Text(""),t=new a.CDATA([e]);this.addNode(t),e.parent=t,this.lastNode=e},e.prototype.oncdataend=function(){this.lastNode=null},e.prototype.onprocessinginstruction=function(e,t){var o=new a.ProcessingInstruction(e,t);this.addNode(o)},e.prototype.handleCallback=function(e){if("function"==typeof this.callback)this.callback(e,this.dom);else if(e)throw e},e.prototype.addNode=function(e){var t=this.tagStack[this.tagStack.length-1],o=t.children[t.children.length-1];this.options.withStartIndices&&(e.startIndex=this.parser.startIndex),this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),t.children.push(e),o&&(e.prev=o,o.next=e),e.parent=t,this.lastNode=null},e}();t.DomHandler=c,t.default=c},790:function(e,t,o){"use strict";var n,r=this&&this.__extends||(n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])},n(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function o(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(o.prototype=t.prototype,new o)}),l=this&&this.__assign||function(){return l=Object.assign||function(e){for(var t,o=1,n=arguments.length;o<n;o++)for(var r in t=arguments[o])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},l.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.cloneNode=t.hasChildren=t.isDocument=t.isDirective=t.isComment=t.isText=t.isCDATA=t.isTag=t.Element=t.Document=t.CDATA=t.NodeWithChildren=t.ProcessingInstruction=t.Comment=t.Text=t.DataNode=t.Node=void 0;var a=o(960),i=function(){function e(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}return Object.defineProperty(e.prototype,"parentNode",{get:function(){return this.parent},set:function(e){this.parent=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"previousSibling",{get:function(){return this.prev},set:function(e){this.prev=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"nextSibling",{get:function(){return this.next},set:function(e){this.next=e},enumerable:!1,configurable:!0}),e.prototype.cloneNode=function(e){return void 0===e&&(e=!1),w(this,e)},e}();t.Node=i;var c=function(e){function t(t){var o=e.call(this)||this;return o.data=t,o}return r(t,e),Object.defineProperty(t.prototype,"nodeValue",{get:function(){return this.data},set:function(e){this.data=e},enumerable:!1,configurable:!0}),t}(i);t.DataNode=c;var s=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=a.ElementType.Text,t}return r(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 3},enumerable:!1,configurable:!0}),t}(c);t.Text=s;var u=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=a.ElementType.Comment,t}return r(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 8},enumerable:!1,configurable:!0}),t}(c);t.Comment=u;var p=function(e){function t(t,o){var n=e.call(this,o)||this;return n.name=t,n.type=a.ElementType.Directive,n}return r(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 1},enumerable:!1,configurable:!0}),t}(c);t.ProcessingInstruction=p;var d=function(e){function t(t){var o=e.call(this)||this;return o.children=t,o}return r(t,e),Object.defineProperty(t.prototype,"firstChild",{get:function(){var e;return null!==(e=this.children[0])&&void 0!==e?e:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lastChild",{get:function(){return this.children.length>0?this.children[this.children.length-1]:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"childNodes",{get:function(){return this.children},set:function(e){this.children=e},enumerable:!1,configurable:!0}),t}(i);t.NodeWithChildren=d;var m=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=a.ElementType.CDATA,t}return r(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 4},enumerable:!1,configurable:!0}),t}(d);t.CDATA=m;var f=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=a.ElementType.Root,t}return r(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 9},enumerable:!1,configurable:!0}),t}(d);t.Document=f;var b=function(e){function t(t,o,n,r){void 0===n&&(n=[]),void 0===r&&(r="script"===t?a.ElementType.Script:"style"===t?a.ElementType.Style:a.ElementType.Tag);var l=e.call(this,n)||this;return l.name=t,l.attribs=o,l.type=r,l}return r(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 1},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"tagName",{get:function(){return this.name},set:function(e){this.name=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"attributes",{get:function(){var e=this;return Object.keys(this.attribs).map((function(t){var o,n;return{name:t,value:e.attribs[t],namespace:null===(o=e["x-attribsNamespace"])||void 0===o?void 0:o[t],prefix:null===(n=e["x-attribsPrefix"])||void 0===n?void 0:n[t]}}))},enumerable:!1,configurable:!0}),t}(d);function y(e){return(0,a.isTag)(e)}function h(e){return e.type===a.ElementType.CDATA}function v(e){return e.type===a.ElementType.Text}function g(e){return e.type===a.ElementType.Comment}function _(e){return e.type===a.ElementType.Directive}function k(e){return e.type===a.ElementType.Root}function w(e,t){var o;if(void 0===t&&(t=!1),v(e))o=new s(e.data);else if(g(e))o=new u(e.data);else if(y(e)){var n=t?O(e.children):[],r=new b(e.name,l({},e.attribs),n);n.forEach((function(e){return e.parent=r})),null!=e.namespace&&(r.namespace=e.namespace),e["x-attribsNamespace"]&&(r["x-attribsNamespace"]=l({},e["x-attribsNamespace"])),e["x-attribsPrefix"]&&(r["x-attribsPrefix"]=l({},e["x-attribsPrefix"])),o=r}else if(h(e)){n=t?O(e.children):[];var a=new m(n);n.forEach((function(e){return e.parent=a})),o=a}else if(k(e)){n=t?O(e.children):[];var i=new f(n);n.forEach((function(e){return e.parent=i})),e["x-mode"]&&(i["x-mode"]=e["x-mode"]),o=i}else{if(!_(e))throw new Error("Not implemented yet: ".concat(e.type));var c=new p(e.name,e.data);null!=e["x-name"]&&(c["x-name"]=e["x-name"],c["x-publicId"]=e["x-publicId"],c["x-systemId"]=e["x-systemId"]),o=c}return o.startIndex=e.startIndex,o.endIndex=e.endIndex,null!=e.sourceCodeLocation&&(o.sourceCodeLocation=e.sourceCodeLocation),o}function O(e){for(var t=e.map((function(e){return w(e,!0)})),o=1;o<t.length;o++)t[o].prev=t[o-1],t[o-1].next=t[o];return t}t.Element=b,t.isTag=y,t.isCDATA=h,t.isText=v,t.isComment=g,t.isDirective=_,t.isDocument=k,t.hasChildren=function(e){return Object.prototype.hasOwnProperty.call(e,"children")},t.cloneNode=w},991:function(e){"use strict";e.exports=function e(t,o){if(t===o)return!0;if(t&&o&&"object"==typeof t&&"object"==typeof o){if(t.constructor!==o.constructor)return!1;var n,r,l;if(Array.isArray(t)){if((n=t.length)!=o.length)return!1;for(r=n;0!=r--;)if(!e(t[r],o[r]))return!1;return!0}if(t instanceof Map&&o instanceof Map){if(t.size!==o.size)return!1;for(r of t.entries())if(!o.has(r[0]))return!1;for(r of t.entries())if(!e(r[1],o.get(r[0])))return!1;return!0}if(t instanceof Set&&o instanceof Set){if(t.size!==o.size)return!1;for(r of t.entries())if(!o.has(r[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(o)){if((n=t.length)!=o.length)return!1;for(r=n;0!=r--;)if(t[r]!==o[r])return!1;return!0}if(t.constructor===RegExp)return t.source===o.source&&t.flags===o.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===o.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===o.toString();if((n=(l=Object.keys(t)).length)!==Object.keys(o).length)return!1;for(r=n;0!=r--;)if(!Object.prototype.hasOwnProperty.call(o,l[r]))return!1;for(r=n;0!=r--;){var a=l[r];if(!e(t[a],o[a]))return!1}return!0}return t!=t&&o!=o}},885:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CASE_SENSITIVE_TAG_NAMES_MAP=t.CASE_SENSITIVE_TAG_NAMES=void 0,t.CASE_SENSITIVE_TAG_NAMES=["animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","linearGradient","radialGradient","textPath"],t.CASE_SENSITIVE_TAG_NAMES_MAP=t.CASE_SENSITIVE_TAG_NAMES.reduce((function(e,t){return e[t.toLowerCase()]=t,e}),{})},276:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t,u,p=e.match(l),d=p&&p[1]?p[1].toLowerCase():"";switch(d){case o:var f=s(e);if(!a.test(e))null===(t=null==(y=f.querySelector(n))?void 0:y.parentNode)||void 0===t||t.removeChild(y);if(!i.test(e))null===(u=null==(y=f.querySelector(r))?void 0:y.parentNode)||void 0===u||u.removeChild(y);return f.querySelectorAll(o);case n:case r:var b=c(e).querySelectorAll(d);return i.test(e)&&a.test(e)?b[0].parentNode.childNodes:b;default:return m?m(e):(y=c(e,r).querySelector(r)).childNodes;var y}};var o="html",n="head",r="body",l=/<([a-zA-Z]+[0-9]?)/,a=/<head[^]*>/i,i=/<body[^]*>/i,c=function(e,t){throw new Error("This browser does not support `document.implementation.createHTMLDocument`")},s=function(e,t){throw new Error("This browser does not support `DOMParser.prototype.parseFromString`")},u="object"==typeof window&&window.DOMParser;if("function"==typeof u){var p=new u;c=s=function(e,t){return t&&(e="<".concat(t,">").concat(e,"</").concat(t,">")),p.parseFromString(e,"text/html")}}if("object"==typeof document&&document.implementation){var d=document.implementation.createHTMLDocument();c=function(e,t){if(t){var o=d.documentElement.querySelector(t);return o&&(o.innerHTML=e),d}return d.documentElement.innerHTML=e,d}}var m,f="object"==typeof document&&document.createElement("template");f&&f.content&&(m=function(e){return f.innerHTML=e,f.content.childNodes})},152:function(e,t,o){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if("string"!=typeof e)throw new TypeError("First argument must be a string");if(!e)return[];var t=e.match(a),o=t?t[1]:void 0;return(0,l.formatDOM)((0,r.default)(e),null,o)};var r=n(o(276)),l=o(507),a=/<(![a-zA-Z\s]+)>/},507:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatAttributes=l,t.formatDOM=function e(t,o,r){void 0===o&&(o=null);for(var i,c=[],s=0,u=t.length;s<u;s++){var p=t[s];switch(p.nodeType){case 1:var d=a(p.nodeName);(i=new n.Element(d,l(p.attributes))).children=e("template"===d?p.content.childNodes:p.childNodes,i);break;case 3:i=new n.Text(p.nodeValue);break;case 8:i=new n.Comment(p.nodeValue);break;default:continue}var m=c[s-1]||null;m&&(m.next=i),i.parent=o,i.prev=m,i.next=null,c.push(i)}r&&((i=new n.ProcessingInstruction(r.substring(0,r.indexOf(" ")).toLowerCase(),r)).next=c[0]||null,i.parent=o,c.unshift(i),c[1]&&(c[1].prev=c[0]));return c};var n=o(915),r=o(885);function l(e){for(var t={},o=0,n=e.length;o<n;o++){var r=e[o];t[r.name]=r.value}return t}function a(e){var t=function(e){return r.CASE_SENSITIVE_TAG_NAMES_MAP[e]}(e=e.toLowerCase());return t||e}},484:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){void 0===e&&(e={});var o={},s=Boolean(e.type&&i[e.type]);for(var u in e){var p=e[u];if((0,n.isCustomAttribute)(u))o[u]=p;else{var d=u.toLowerCase(),m=c(d);if(m){var f=(0,n.getPropertyInfo)(m);switch(l.includes(m)&&a.includes(t)&&!s&&(m=c("default"+d)),o[m]=p,f&&f.type){case n.BOOLEAN:o[m]=!0;break;case n.OVERLOADED_BOOLEAN:""===p&&(o[m]=!0)}}else r.PRESERVE_CUSTOM_ATTRIBUTES&&(o[u]=p)}}return(0,r.setStyleProp)(e.style,o),o};var n=o(726),r=o(606),l=["checked","value"],a=["input","select","textarea"],i={reset:!0,submit:!0};function c(e){return n.possibleStandardNames[e]}},670:function(e,t,o){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function e(t,o){void 0===o&&(o={});for(var n=[],r="function"==typeof o.replace,s=o.transform||a.returnFirstArg,u=o.library||i,p=u.cloneElement,d=u.createElement,m=u.isValidElement,f=t.length,b=0;b<f;b++){var y=t[b];if(r){var h=o.replace(y,b);if(m(h)){f>1&&(h=p(h,{key:h.key||b})),n.push(s(h,y,b));continue}}if("text"!==y.type){var v=y,g={};c(v)?((0,a.setStyleProp)(v.attribs.style,v.attribs),g=v.attribs):v.attribs&&(g=(0,l.default)(v.attribs,v.name));var _=void 0;switch(y.type){case"script":case"style":y.children[0]&&(g.dangerouslySetInnerHTML={__html:y.children[0].data});break;case"tag":"textarea"===y.name&&y.children[0]?g.defaultValue=y.children[0].data:y.children&&y.children.length&&(_=e(y.children,o));break;default:continue}f>1&&(g.key=b),n.push(s(d(y.name,g,_),y,b))}else{var k=!y.data.trim().length;if(k&&y.parent&&!(0,a.canTextBeChildOfNode)(y.parent))continue;if(o.trim&&k)continue;n.push(s(y.data,y,b))}}return 1===n.length?n[0]:n};var r=o(313),l=n(o(484)),a=o(606),i={cloneElement:r.cloneElement,createElement:r.createElement,isValidElement:r.isValidElement};function c(e){return a.PRESERVE_CUSTOM_ATTRIBUTES&&"tag"===e.type&&(0,a.isCustomComponent)(e.name,e.attribs)}},426:function(e,t,o){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.htmlToDOM=t.domToReact=t.attributesToProps=t.Text=t.ProcessingInstruction=t.Element=t.Comment=void 0,t.default=function(e,t){if("string"!=typeof e)throw new TypeError("First argument must be a string");if(!e)return[];return(0,a.default)((0,r.default)(e,(null==t?void 0:t.htmlparser2)||c),t)};var r=n(o(152));t.htmlToDOM=r.default;var l=n(o(484));t.attributesToProps=l.default;var a=n(o(670));t.domToReact=a.default;var i=o(915);Object.defineProperty(t,"Comment",{enumerable:!0,get:function(){return i.Comment}}),Object.defineProperty(t,"Element",{enumerable:!0,get:function(){return i.Element}}),Object.defineProperty(t,"ProcessingInstruction",{enumerable:!0,get:function(){return i.ProcessingInstruction}}),Object.defineProperty(t,"Text",{enumerable:!0,get:function(){return i.Text}});var c={lowerCaseAttributeNames:!1}},606:function(e,t,o){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.returnFirstArg=t.canTextBeChildOfNode=t.ELEMENTS_WITH_NO_TEXT_CHILDREN=t.PRESERVE_CUSTOM_ATTRIBUTES=void 0,t.isCustomComponent=function(e,t){if(!e.includes("-"))return Boolean(t&&"string"==typeof t.is);if(a.has(e))return!1;return!0},t.setStyleProp=function(e,t){if("string"!=typeof e)return;if(!e.trim())return void(t.style={});try{t.style=(0,l.default)(e,i)}catch(e){t.style={}}};var r=o(313),l=n(o(476)),a=new Set(["annotation-xml","color-profile","font-face","font-face-src","font-face-uri","font-face-format","font-face-name","missing-glyph"]);var i={reactCompat:!0};t.PRESERVE_CUSTOM_ATTRIBUTES=Number(r.version.split(".")[0])>=16,t.ELEMENTS_WITH_NO_TEXT_CHILDREN=new Set(["tr","tbody","thead","tfoot","colgroup","table","head","html","frameset"]);t.canTextBeChildOfNode=function(e){return!t.ELEMENTS_WITH_NO_TEXT_CHILDREN.has(e.name)};t.returnFirstArg=function(e){return e}},139:function(e){var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,o=/\n/g,n=/^\s*/,r=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,l=/^:\s*/,a=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,i=/^[;\s]*/,c=/^\s+|\s+$/g,s="";function u(e){return e?e.replace(c,s):s}e.exports=function(e,c){if("string"!=typeof e)throw new TypeError("First argument must be a string");if(!e)return[];c=c||{};var p=1,d=1;function m(e){var t=e.match(o);t&&(p+=t.length);var n=e.lastIndexOf("\n");d=~n?e.length-n:d+e.length}function f(){var e={line:p,column:d};return function(t){return t.position=new b(e),g(),t}}function b(e){this.start=e,this.end={line:p,column:d},this.source=c.source}b.prototype.content=e;var y=[];function h(t){var o=new Error(c.source+":"+p+":"+d+": "+t);if(o.reason=t,o.filename=c.source,o.line=p,o.column=d,o.source=e,!c.silent)throw o;y.push(o)}function v(t){var o=t.exec(e);if(o){var n=o[0];return m(n),e=e.slice(n.length),o}}function g(){v(n)}function _(e){var t;for(e=e||[];t=k();)!1!==t&&e.push(t);return e}function k(){var t=f();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var o=2;s!=e.charAt(o)&&("*"!=e.charAt(o)||"/"!=e.charAt(o+1));)++o;if(o+=2,s===e.charAt(o-1))return h("End of comment missing");var n=e.slice(2,o-2);return d+=2,m(n),e=e.slice(o),d+=2,t({type:"comment",comment:n})}}function w(){var e=f(),o=v(r);if(o){if(k(),!v(l))return h("property missing ':'");var n=v(a),c=e({type:"declaration",property:u(o[0].replace(t,s)),value:n?u(n[0].replace(t,s)):s});return v(i),c}}return g(),function(){var e,t=[];for(_(t);e=w();)!1!==e&&(t.push(e),_(t));return t}()}},738:function(e){function t(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
e.exports=function(e){return null!=e&&(t(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&t(e.slice(0,0))}(e)||!!e._isBuffer)}},568:function(e,t,o){var n,r,l,a,i;n=o(12),r=o(487).utf8,l=o(738),a=o(487).bin,(i=function(e,t){e.constructor==String?e=t&&"binary"===t.encoding?a.stringToBytes(e):r.stringToBytes(e):l(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var o=n.bytesToWords(e),c=8*e.length,s=**********,u=-271733879,p=-**********,d=271733878,m=0;m<o.length;m++)o[m]=16711935&(o[m]<<8|o[m]>>>24)|**********&(o[m]<<24|o[m]>>>8);o[c>>>5]|=128<<c%32,o[14+(c+64>>>9<<4)]=c;var f=i._ff,b=i._gg,y=i._hh,h=i._ii;for(m=0;m<o.length;m+=16){var v=s,g=u,_=p,k=d;s=f(s,u,p,d,o[m+0],7,-680876936),d=f(d,s,u,p,o[m+1],12,-389564586),p=f(p,d,s,u,o[m+2],17,606105819),u=f(u,p,d,s,o[m+3],22,-**********),s=f(s,u,p,d,o[m+4],7,-176418897),d=f(d,s,u,p,o[m+5],12,**********),p=f(p,d,s,u,o[m+6],17,-**********),u=f(u,p,d,s,o[m+7],22,-45705983),s=f(s,u,p,d,o[m+8],7,1770035416),d=f(d,s,u,p,o[m+9],12,-1958414417),p=f(p,d,s,u,o[m+10],17,-42063),u=f(u,p,d,s,o[m+11],22,-1990404162),s=f(s,u,p,d,o[m+12],7,1804603682),d=f(d,s,u,p,o[m+13],12,-40341101),p=f(p,d,s,u,o[m+14],17,-1502002290),s=b(s,u=f(u,p,d,s,o[m+15],22,1236535329),p,d,o[m+1],5,-165796510),d=b(d,s,u,p,o[m+6],9,-1069501632),p=b(p,d,s,u,o[m+11],14,643717713),u=b(u,p,d,s,o[m+0],20,-373897302),s=b(s,u,p,d,o[m+5],5,-701558691),d=b(d,s,u,p,o[m+10],9,38016083),p=b(p,d,s,u,o[m+15],14,-660478335),u=b(u,p,d,s,o[m+4],20,-405537848),s=b(s,u,p,d,o[m+9],5,568446438),d=b(d,s,u,p,o[m+14],9,-1019803690),p=b(p,d,s,u,o[m+3],14,-187363961),u=b(u,p,d,s,o[m+8],20,1163531501),s=b(s,u,p,d,o[m+13],5,-1444681467),d=b(d,s,u,p,o[m+2],9,-51403784),p=b(p,d,s,u,o[m+7],14,1735328473),s=y(s,u=b(u,p,d,s,o[m+12],20,-1926607734),p,d,o[m+5],4,-378558),d=y(d,s,u,p,o[m+8],11,-2022574463),p=y(p,d,s,u,o[m+11],16,1839030562),u=y(u,p,d,s,o[m+14],23,-35309556),s=y(s,u,p,d,o[m+1],4,-1530992060),d=y(d,s,u,p,o[m+4],11,1272893353),p=y(p,d,s,u,o[m+7],16,-155497632),u=y(u,p,d,s,o[m+10],23,-1094730640),s=y(s,u,p,d,o[m+13],4,681279174),d=y(d,s,u,p,o[m+0],11,-358537222),p=y(p,d,s,u,o[m+3],16,-722521979),u=y(u,p,d,s,o[m+6],23,76029189),s=y(s,u,p,d,o[m+9],4,-640364487),d=y(d,s,u,p,o[m+12],11,-421815835),p=y(p,d,s,u,o[m+15],16,530742520),s=h(s,u=y(u,p,d,s,o[m+2],23,-995338651),p,d,o[m+0],6,-198630844),d=h(d,s,u,p,o[m+7],10,1126891415),p=h(p,d,s,u,o[m+14],15,-1416354905),u=h(u,p,d,s,o[m+5],21,-57434055),s=h(s,u,p,d,o[m+12],6,1700485571),d=h(d,s,u,p,o[m+3],10,-1894986606),p=h(p,d,s,u,o[m+10],15,-1051523),u=h(u,p,d,s,o[m+1],21,-2054922799),s=h(s,u,p,d,o[m+8],6,1873313359),d=h(d,s,u,p,o[m+15],10,-30611744),p=h(p,d,s,u,o[m+6],15,-1560198380),u=h(u,p,d,s,o[m+13],21,1309151649),s=h(s,u,p,d,o[m+4],6,-145523070),d=h(d,s,u,p,o[m+11],10,-1120210379),p=h(p,d,s,u,o[m+2],15,718787259),u=h(u,p,d,s,o[m+9],21,-343485551),s=s+v>>>0,u=u+g>>>0,p=p+_>>>0,d=d+k>>>0}return n.endian([s,u,p,d])})._ff=function(e,t,o,n,r,l,a){var i=e+(t&o|~t&n)+(r>>>0)+a;return(i<<l|i>>>32-l)+t},i._gg=function(e,t,o,n,r,l,a){var i=e+(t&n|o&~n)+(r>>>0)+a;return(i<<l|i>>>32-l)+t},i._hh=function(e,t,o,n,r,l,a){var i=e+(t^o^n)+(r>>>0)+a;return(i<<l|i>>>32-l)+t},i._ii=function(e,t,o,n,r,l,a){var i=e+(o^(t|~n))+(r>>>0)+a;return(i<<l|i>>>32-l)+t},i._blocksize=16,i._digestsize=16,e.exports=function(e,t){if(null==e)throw new Error("Illegal argument "+e);var o=n.wordsToBytes(i(e,t));return t&&t.asBytes?o:t&&t.asString?a.bytesToString(o):n.bytesToHex(o)}},496:function(e){for(var t=self.crypto||self.msCrypto,o="-_",n=36;n--;)o+=n.toString(36);for(n=36;n---10;)o+=n.toString(36).toUpperCase();e.exports=function(e){var r="",l=t.getRandomValues(new Uint8Array(e||21));for(n=e||21;n--;)r+=o[63&l[n]];return r}},726:function(e,t,o){"use strict";function n(e,t,o,n,r,l,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=n,this.attributeNamespace=r,this.mustUseProperty=o,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=a}const r={};["children","dangerouslySetInnerHTML","defaultValue","defaultChecked","innerHTML","suppressContentEditableWarning","suppressHydrationWarning","style"].forEach((e=>{r[e]=new n(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((([e,t])=>{r[e]=new n(e,1,!1,t,null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((e=>{r[e]=new n(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((e=>{r[e]=new n(e,2,!1,e,null,!1,!1)})),["allowFullScreen","async","autoFocus","autoPlay","controls","default","defer","disabled","disablePictureInPicture","disableRemotePlayback","formNoValidate","hidden","loop","noModule","noValidate","open","playsInline","readOnly","required","reversed","scoped","seamless","itemScope"].forEach((e=>{r[e]=new n(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((e=>{r[e]=new n(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((e=>{r[e]=new n(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((e=>{r[e]=new n(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((e=>{r[e]=new n(e,5,!1,e.toLowerCase(),null,!1,!1)}));const l=/[\-\:]([a-z])/g,a=e=>e[1].toUpperCase();["accent-height","alignment-baseline","arabic-form","baseline-shift","cap-height","clip-path","clip-rule","color-interpolation","color-interpolation-filters","color-profile","color-rendering","dominant-baseline","enable-background","fill-opacity","fill-rule","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","glyph-name","glyph-orientation-horizontal","glyph-orientation-vertical","horiz-adv-x","horiz-origin-x","image-rendering","letter-spacing","lighting-color","marker-end","marker-mid","marker-start","overline-position","overline-thickness","paint-order","panose-1","pointer-events","rendering-intent","shape-rendering","stop-color","stop-opacity","strikethrough-position","strikethrough-thickness","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-anchor","text-decoration","text-rendering","underline-position","underline-thickness","unicode-bidi","unicode-range","units-per-em","v-alphabetic","v-hanging","v-ideographic","v-mathematical","vector-effect","vert-adv-y","vert-origin-x","vert-origin-y","word-spacing","writing-mode","xmlns:xlink","x-height"].forEach((e=>{const t=e.replace(l,a);r[t]=new n(t,1,!1,e,null,!1,!1)})),["xlink:actuate","xlink:arcrole","xlink:role","xlink:show","xlink:title","xlink:type"].forEach((e=>{const t=e.replace(l,a);r[t]=new n(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((e=>{const t=e.replace(l,a);r[t]=new n(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((e=>{r[e]=new n(e,1,!1,e.toLowerCase(),null,!1,!1)}));r.xlinkHref=new n("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((e=>{r[e]=new n(e,1,!1,e.toLowerCase(),null,!0,!0)}));const{CAMELCASE:i,SAME:c,possibleStandardNames:s}=o(229),u=RegExp.prototype.test.bind(new RegExp("^(data|aria)-[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$")),p=Object.keys(s).reduce(((e,t)=>{const o=s[t];return o===c?e[t]=t:o===i?e[t.toLowerCase()]=t:e[t]=o,e}),{});t.BOOLEAN=3,t.BOOLEANISH_STRING=2,t.NUMERIC=5,t.OVERLOADED_BOOLEAN=4,t.POSITIVE_NUMERIC=6,t.RESERVED=0,t.STRING=1,t.getPropertyInfo=function(e){return r.hasOwnProperty(e)?r[e]:null},t.isCustomAttribute=u,t.possibleStandardNames=p},229:function(e,t){t.SAME=0;t.CAMELCASE=1,t.possibleStandardNames={accept:0,acceptCharset:1,"accept-charset":"acceptCharset",accessKey:1,action:0,allowFullScreen:1,alt:0,as:0,async:0,autoCapitalize:1,autoComplete:1,autoCorrect:1,autoFocus:1,autoPlay:1,autoSave:1,capture:0,cellPadding:1,cellSpacing:1,challenge:0,charSet:1,checked:0,children:0,cite:0,class:"className",classID:1,className:1,cols:0,colSpan:1,content:0,contentEditable:1,contextMenu:1,controls:0,controlsList:1,coords:0,crossOrigin:1,dangerouslySetInnerHTML:1,data:0,dateTime:1,default:0,defaultChecked:1,defaultValue:1,defer:0,dir:0,disabled:0,disablePictureInPicture:1,disableRemotePlayback:1,download:0,draggable:0,encType:1,enterKeyHint:1,for:"htmlFor",form:0,formMethod:1,formAction:1,formEncType:1,formNoValidate:1,formTarget:1,frameBorder:1,headers:0,height:0,hidden:0,high:0,href:0,hrefLang:1,htmlFor:1,httpEquiv:1,"http-equiv":"httpEquiv",icon:0,id:0,innerHTML:1,inputMode:1,integrity:0,is:0,itemID:1,itemProp:1,itemRef:1,itemScope:1,itemType:1,keyParams:1,keyType:1,kind:0,label:0,lang:0,list:0,loop:0,low:0,manifest:0,marginWidth:1,marginHeight:1,max:0,maxLength:1,media:0,mediaGroup:1,method:0,min:0,minLength:1,multiple:0,muted:0,name:0,noModule:1,nonce:0,noValidate:1,open:0,optimum:0,pattern:0,placeholder:0,playsInline:1,poster:0,preload:0,profile:0,radioGroup:1,readOnly:1,referrerPolicy:1,rel:0,required:0,reversed:0,role:0,rows:0,rowSpan:1,sandbox:0,scope:0,scoped:0,scrolling:0,seamless:0,selected:0,shape:0,size:0,sizes:0,span:0,spellCheck:1,src:0,srcDoc:1,srcLang:1,srcSet:1,start:0,step:0,style:0,summary:0,tabIndex:1,target:0,title:0,type:0,useMap:1,value:0,width:0,wmode:0,wrap:0,about:0,accentHeight:1,"accent-height":"accentHeight",accumulate:0,additive:0,alignmentBaseline:1,"alignment-baseline":"alignmentBaseline",allowReorder:1,alphabetic:0,amplitude:0,arabicForm:1,"arabic-form":"arabicForm",ascent:0,attributeName:1,attributeType:1,autoReverse:1,azimuth:0,baseFrequency:1,baselineShift:1,"baseline-shift":"baselineShift",baseProfile:1,bbox:0,begin:0,bias:0,by:0,calcMode:1,capHeight:1,"cap-height":"capHeight",clip:0,clipPath:1,"clip-path":"clipPath",clipPathUnits:1,clipRule:1,"clip-rule":"clipRule",color:0,colorInterpolation:1,"color-interpolation":"colorInterpolation",colorInterpolationFilters:1,"color-interpolation-filters":"colorInterpolationFilters",colorProfile:1,"color-profile":"colorProfile",colorRendering:1,"color-rendering":"colorRendering",contentScriptType:1,contentStyleType:1,cursor:0,cx:0,cy:0,d:0,datatype:0,decelerate:0,descent:0,diffuseConstant:1,direction:0,display:0,divisor:0,dominantBaseline:1,"dominant-baseline":"dominantBaseline",dur:0,dx:0,dy:0,edgeMode:1,elevation:0,enableBackground:1,"enable-background":"enableBackground",end:0,exponent:0,externalResourcesRequired:1,fill:0,fillOpacity:1,"fill-opacity":"fillOpacity",fillRule:1,"fill-rule":"fillRule",filter:0,filterRes:1,filterUnits:1,floodOpacity:1,"flood-opacity":"floodOpacity",floodColor:1,"flood-color":"floodColor",focusable:0,fontFamily:1,"font-family":"fontFamily",fontSize:1,"font-size":"fontSize",fontSizeAdjust:1,"font-size-adjust":"fontSizeAdjust",fontStretch:1,"font-stretch":"fontStretch",fontStyle:1,"font-style":"fontStyle",fontVariant:1,"font-variant":"fontVariant",fontWeight:1,"font-weight":"fontWeight",format:0,from:0,fx:0,fy:0,g1:0,g2:0,glyphName:1,"glyph-name":"glyphName",glyphOrientationHorizontal:1,"glyph-orientation-horizontal":"glyphOrientationHorizontal",glyphOrientationVertical:1,"glyph-orientation-vertical":"glyphOrientationVertical",glyphRef:1,gradientTransform:1,gradientUnits:1,hanging:0,horizAdvX:1,"horiz-adv-x":"horizAdvX",horizOriginX:1,"horiz-origin-x":"horizOriginX",ideographic:0,imageRendering:1,"image-rendering":"imageRendering",in2:0,in:0,inlist:0,intercept:0,k1:0,k2:0,k3:0,k4:0,k:0,kernelMatrix:1,kernelUnitLength:1,kerning:0,keyPoints:1,keySplines:1,keyTimes:1,lengthAdjust:1,letterSpacing:1,"letter-spacing":"letterSpacing",lightingColor:1,"lighting-color":"lightingColor",limitingConeAngle:1,local:0,markerEnd:1,"marker-end":"markerEnd",markerHeight:1,markerMid:1,"marker-mid":"markerMid",markerStart:1,"marker-start":"markerStart",markerUnits:1,markerWidth:1,mask:0,maskContentUnits:1,maskUnits:1,mathematical:0,mode:0,numOctaves:1,offset:0,opacity:0,operator:0,order:0,orient:0,orientation:0,origin:0,overflow:0,overlinePosition:1,"overline-position":"overlinePosition",overlineThickness:1,"overline-thickness":"overlineThickness",paintOrder:1,"paint-order":"paintOrder",panose1:0,"panose-1":"panose1",pathLength:1,patternContentUnits:1,patternTransform:1,patternUnits:1,pointerEvents:1,"pointer-events":"pointerEvents",points:0,pointsAtX:1,pointsAtY:1,pointsAtZ:1,prefix:0,preserveAlpha:1,preserveAspectRatio:1,primitiveUnits:1,property:0,r:0,radius:0,refX:1,refY:1,renderingIntent:1,"rendering-intent":"renderingIntent",repeatCount:1,repeatDur:1,requiredExtensions:1,requiredFeatures:1,resource:0,restart:0,result:0,results:0,rotate:0,rx:0,ry:0,scale:0,security:0,seed:0,shapeRendering:1,"shape-rendering":"shapeRendering",slope:0,spacing:0,specularConstant:1,specularExponent:1,speed:0,spreadMethod:1,startOffset:1,stdDeviation:1,stemh:0,stemv:0,stitchTiles:1,stopColor:1,"stop-color":"stopColor",stopOpacity:1,"stop-opacity":"stopOpacity",strikethroughPosition:1,"strikethrough-position":"strikethroughPosition",strikethroughThickness:1,"strikethrough-thickness":"strikethroughThickness",string:0,stroke:0,strokeDasharray:1,"stroke-dasharray":"strokeDasharray",strokeDashoffset:1,"stroke-dashoffset":"strokeDashoffset",strokeLinecap:1,"stroke-linecap":"strokeLinecap",strokeLinejoin:1,"stroke-linejoin":"strokeLinejoin",strokeMiterlimit:1,"stroke-miterlimit":"strokeMiterlimit",strokeWidth:1,"stroke-width":"strokeWidth",strokeOpacity:1,"stroke-opacity":"strokeOpacity",suppressContentEditableWarning:1,suppressHydrationWarning:1,surfaceScale:1,systemLanguage:1,tableValues:1,targetX:1,targetY:1,textAnchor:1,"text-anchor":"textAnchor",textDecoration:1,"text-decoration":"textDecoration",textLength:1,textRendering:1,"text-rendering":"textRendering",to:0,transform:0,typeof:0,u1:0,u2:0,underlinePosition:1,"underline-position":"underlinePosition",underlineThickness:1,"underline-thickness":"underlineThickness",unicode:0,unicodeBidi:1,"unicode-bidi":"unicodeBidi",unicodeRange:1,"unicode-range":"unicodeRange",unitsPerEm:1,"units-per-em":"unitsPerEm",unselectable:0,vAlphabetic:1,"v-alphabetic":"vAlphabetic",values:0,vectorEffect:1,"vector-effect":"vectorEffect",version:0,vertAdvY:1,"vert-adv-y":"vertAdvY",vertOriginX:1,"vert-origin-x":"vertOriginX",vertOriginY:1,"vert-origin-y":"vertOriginY",vHanging:1,"v-hanging":"vHanging",vIdeographic:1,"v-ideographic":"vIdeographic",viewBox:1,viewTarget:1,visibility:0,vMathematical:1,"v-mathematical":"vMathematical",vocab:0,widths:0,wordSpacing:1,"word-spacing":"wordSpacing",writingMode:1,"writing-mode":"writingMode",x1:0,x2:0,x:0,xChannelSelector:1,xHeight:1,"x-height":"xHeight",xlinkActuate:1,"xlink:actuate":"xlinkActuate",xlinkArcrole:1,"xlink:arcrole":"xlinkArcrole",xlinkHref:1,"xlink:href":"xlinkHref",xlinkRole:1,"xlink:role":"xlinkRole",xlinkShow:1,"xlink:show":"xlinkShow",xlinkTitle:1,"xlink:title":"xlinkTitle",xlinkType:1,"xlink:type":"xlinkType",xmlBase:1,"xml:base":"xmlBase",xmlLang:1,"xml:lang":"xmlLang",xmlns:0,"xml:space":"xmlSpace",xmlnsXlink:1,"xmlns:xlink":"xmlnsXlink",xmlSpace:1,y1:0,y2:0,y:0,yChannelSelector:1,z:0,zoomAndPan:1}},826:function(e){var t={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",Ấ:"A",Ắ:"A",Ẳ:"A",Ẵ:"A",Ặ:"A",Æ:"AE",Ầ:"A",Ằ:"A",Ȃ:"A",Ả:"A",Ạ:"A",Ẩ:"A",Ẫ:"A",Ậ:"A",Ç:"C",Ḉ:"C",È:"E",É:"E",Ê:"E",Ë:"E",Ế:"E",Ḗ:"E",Ề:"E",Ḕ:"E",Ḝ:"E",Ȇ:"E",Ẻ:"E",Ẽ:"E",Ẹ:"E",Ể:"E",Ễ:"E",Ệ:"E",Ì:"I",Í:"I",Î:"I",Ï:"I",Ḯ:"I",Ȋ:"I",Ỉ:"I",Ị:"I",Ð:"D",Ñ:"N",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",Ố:"O",Ṍ:"O",Ṓ:"O",Ȏ:"O",Ỏ:"O",Ọ:"O",Ổ:"O",Ỗ:"O",Ộ:"O",Ờ:"O",Ở:"O",Ỡ:"O",Ớ:"O",Ợ:"O",Ù:"U",Ú:"U",Û:"U",Ü:"U",Ủ:"U",Ụ:"U",Ử:"U",Ữ:"U",Ự:"U",Ý:"Y",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",ấ:"a",ắ:"a",ẳ:"a",ẵ:"a",ặ:"a",æ:"ae",ầ:"a",ằ:"a",ȃ:"a",ả:"a",ạ:"a",ẩ:"a",ẫ:"a",ậ:"a",ç:"c",ḉ:"c",è:"e",é:"e",ê:"e",ë:"e",ế:"e",ḗ:"e",ề:"e",ḕ:"e",ḝ:"e",ȇ:"e",ẻ:"e",ẽ:"e",ẹ:"e",ể:"e",ễ:"e",ệ:"e",ì:"i",í:"i",î:"i",ï:"i",ḯ:"i",ȋ:"i",ỉ:"i",ị:"i",ð:"d",ñ:"n",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",ố:"o",ṍ:"o",ṓ:"o",ȏ:"o",ỏ:"o",ọ:"o",ổ:"o",ỗ:"o",ộ:"o",ờ:"o",ở:"o",ỡ:"o",ớ:"o",ợ:"o",ù:"u",ú:"u",û:"u",ü:"u",ủ:"u",ụ:"u",ử:"u",ữ:"u",ự:"u",ý:"y",ÿ:"y",Ā:"A",ā:"a",Ă:"A",ă:"a",Ą:"A",ą:"a",Ć:"C",ć:"c",Ĉ:"C",ĉ:"c",Ċ:"C",ċ:"c",Č:"C",č:"c",C̆:"C",c̆:"c",Ď:"D",ď:"d",Đ:"D",đ:"d",Ē:"E",ē:"e",Ĕ:"E",ĕ:"e",Ė:"E",ė:"e",Ę:"E",ę:"e",Ě:"E",ě:"e",Ĝ:"G",Ǵ:"G",ĝ:"g",ǵ:"g",Ğ:"G",ğ:"g",Ġ:"G",ġ:"g",Ģ:"G",ģ:"g",Ĥ:"H",ĥ:"h",Ħ:"H",ħ:"h",Ḫ:"H",ḫ:"h",Ĩ:"I",ĩ:"i",Ī:"I",ī:"i",Ĭ:"I",ĭ:"i",Į:"I",į:"i",İ:"I",ı:"i",Ĳ:"IJ",ĳ:"ij",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",Ḱ:"K",ḱ:"k",K̆:"K",k̆:"k",Ĺ:"L",ĺ:"l",Ļ:"L",ļ:"l",Ľ:"L",ľ:"l",Ŀ:"L",ŀ:"l",Ł:"l",ł:"l",Ḿ:"M",ḿ:"m",M̆:"M",m̆:"m",Ń:"N",ń:"n",Ņ:"N",ņ:"n",Ň:"N",ň:"n",ŉ:"n",N̆:"N",n̆:"n",Ō:"O",ō:"o",Ŏ:"O",ŏ:"o",Ő:"O",ő:"o",Œ:"OE",œ:"oe",P̆:"P",p̆:"p",Ŕ:"R",ŕ:"r",Ŗ:"R",ŗ:"r",Ř:"R",ř:"r",R̆:"R",r̆:"r",Ȓ:"R",ȓ:"r",Ś:"S",ś:"s",Ŝ:"S",ŝ:"s",Ş:"S",Ș:"S",ș:"s",ş:"s",Š:"S",š:"s",Ţ:"T",ţ:"t",ț:"t",Ț:"T",Ť:"T",ť:"t",Ŧ:"T",ŧ:"t",T̆:"T",t̆:"t",Ũ:"U",ũ:"u",Ū:"U",ū:"u",Ŭ:"U",ŭ:"u",Ů:"U",ů:"u",Ű:"U",ű:"u",Ų:"U",ų:"u",Ȗ:"U",ȗ:"u",V̆:"V",v̆:"v",Ŵ:"W",ŵ:"w",Ẃ:"W",ẃ:"w",X̆:"X",x̆:"x",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Y̆:"Y",y̆:"y",Ź:"Z",ź:"z",Ż:"Z",ż:"z",Ž:"Z",ž:"z",ſ:"s",ƒ:"f",Ơ:"O",ơ:"o",Ư:"U",ư:"u",Ǎ:"A",ǎ:"a",Ǐ:"I",ǐ:"i",Ǒ:"O",ǒ:"o",Ǔ:"U",ǔ:"u",Ǖ:"U",ǖ:"u",Ǘ:"U",ǘ:"u",Ǚ:"U",ǚ:"u",Ǜ:"U",ǜ:"u",Ứ:"U",ứ:"u",Ṹ:"U",ṹ:"u",Ǻ:"A",ǻ:"a",Ǽ:"AE",ǽ:"ae",Ǿ:"O",ǿ:"o",Þ:"TH",þ:"th",Ṕ:"P",ṕ:"p",Ṥ:"S",ṥ:"s",X́:"X",x́:"x",Ѓ:"Г",ѓ:"г",Ќ:"К",ќ:"к",A̋:"A",a̋:"a",E̋:"E",e̋:"e",I̋:"I",i̋:"i",Ǹ:"N",ǹ:"n",Ồ:"O",ồ:"o",Ṑ:"O",ṑ:"o",Ừ:"U",ừ:"u",Ẁ:"W",ẁ:"w",Ỳ:"Y",ỳ:"y",Ȁ:"A",ȁ:"a",Ȅ:"E",ȅ:"e",Ȉ:"I",ȉ:"i",Ȍ:"O",ȍ:"o",Ȑ:"R",ȑ:"r",Ȕ:"U",ȕ:"u",B̌:"B",b̌:"b",Č̣:"C",č̣:"c",Ê̌:"E",ê̌:"e",F̌:"F",f̌:"f",Ǧ:"G",ǧ:"g",Ȟ:"H",ȟ:"h",J̌:"J",ǰ:"j",Ǩ:"K",ǩ:"k",M̌:"M",m̌:"m",P̌:"P",p̌:"p",Q̌:"Q",q̌:"q",Ř̩:"R",ř̩:"r",Ṧ:"S",ṧ:"s",V̌:"V",v̌:"v",W̌:"W",w̌:"w",X̌:"X",x̌:"x",Y̌:"Y",y̌:"y",A̧:"A",a̧:"a",B̧:"B",b̧:"b",Ḑ:"D",ḑ:"d",Ȩ:"E",ȩ:"e",Ɛ̧:"E",ɛ̧:"e",Ḩ:"H",ḩ:"h",I̧:"I",i̧:"i",Ɨ̧:"I",ɨ̧:"i",M̧:"M",m̧:"m",O̧:"O",o̧:"o",Q̧:"Q",q̧:"q",U̧:"U",u̧:"u",X̧:"X",x̧:"x",Z̧:"Z",z̧:"z",й:"и",Й:"И",ё:"е",Ё:"Е"},o=Object.keys(t).join("|"),n=new RegExp(o,"g"),r=new RegExp(o,"");function l(e){return t[e]}var a=function(e){return e.replace(n,l)};e.exports=a,e.exports.has=function(e){return!!e.match(r)},e.exports.remove=a},975:function(e,t,o){var n;!function(){"use strict";var r={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function l(e){return function(e,t){var o,n,a,i,c,s,u,p,d,m=1,f=e.length,b="";for(n=0;n<f;n++)if("string"==typeof e[n])b+=e[n];else if("object"==typeof e[n]){if((i=e[n]).keys)for(o=t[m],a=0;a<i.keys.length;a++){if(null==o)throw new Error(l('[sprintf] Cannot access property "%s" of undefined value "%s"',i.keys[a],i.keys[a-1]));o=o[i.keys[a]]}else o=i.param_no?t[i.param_no]:t[m++];if(r.not_type.test(i.type)&&r.not_primitive.test(i.type)&&o instanceof Function&&(o=o()),r.numeric_arg.test(i.type)&&"number"!=typeof o&&isNaN(o))throw new TypeError(l("[sprintf] expecting number but found %T",o));switch(r.number.test(i.type)&&(p=o>=0),i.type){case"b":o=parseInt(o,10).toString(2);break;case"c":o=String.fromCharCode(parseInt(o,10));break;case"d":case"i":o=parseInt(o,10);break;case"j":o=JSON.stringify(o,null,i.width?parseInt(i.width):0);break;case"e":o=i.precision?parseFloat(o).toExponential(i.precision):parseFloat(o).toExponential();break;case"f":o=i.precision?parseFloat(o).toFixed(i.precision):parseFloat(o);break;case"g":o=i.precision?String(Number(o.toPrecision(i.precision))):parseFloat(o);break;case"o":o=(parseInt(o,10)>>>0).toString(8);break;case"s":o=String(o),o=i.precision?o.substring(0,i.precision):o;break;case"t":o=String(!!o),o=i.precision?o.substring(0,i.precision):o;break;case"T":o=Object.prototype.toString.call(o).slice(8,-1).toLowerCase(),o=i.precision?o.substring(0,i.precision):o;break;case"u":o=parseInt(o,10)>>>0;break;case"v":o=o.valueOf(),o=i.precision?o.substring(0,i.precision):o;break;case"x":o=(parseInt(o,10)>>>0).toString(16);break;case"X":o=(parseInt(o,10)>>>0).toString(16).toUpperCase()}r.json.test(i.type)?b+=o:(!r.number.test(i.type)||p&&!i.sign?d="":(d=p?"+":"-",o=o.toString().replace(r.sign,"")),s=i.pad_char?"0"===i.pad_char?"0":i.pad_char.charAt(1):" ",u=i.width-(d+o).length,c=i.width&&u>0?s.repeat(u):"",b+=i.align?d+o+c:"0"===s?d+c+o:c+d+o)}return b}(function(e){if(i[e])return i[e];var t,o=e,n=[],l=0;for(;o;){if(null!==(t=r.text.exec(o)))n.push(t[0]);else if(null!==(t=r.modulo.exec(o)))n.push("%");else{if(null===(t=r.placeholder.exec(o)))throw new SyntaxError("[sprintf] unexpected placeholder");if(t[2]){l|=1;var a=[],c=t[2],s=[];if(null===(s=r.key.exec(c)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(a.push(s[1]);""!==(c=c.substring(s[0].length));)if(null!==(s=r.key_access.exec(c)))a.push(s[1]);else{if(null===(s=r.index_access.exec(c)))throw new SyntaxError("[sprintf] failed to parse named argument key");a.push(s[1])}t[2]=a}else l|=2;if(3===l)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");n.push({placeholder:t[0],param_no:t[1],keys:t[2],sign:t[3],pad_char:t[4],align:t[5],width:t[6],precision:t[7],type:t[8]})}o=o.substring(t[0].length)}return i[e]=n}(e),arguments)}function a(e,t){return l.apply(null,[e].concat(t||[]))}var i=Object.create(null);l,a,"undefined"!=typeof window&&(window.sprintf=l,window.vsprintf=a,void 0===(n=function(){return{sprintf:l,vsprintf:a}}.call(t,o,t,e))||(e.exports=n))}()},476:function(e,t,o){"use strict";var n=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(o(174)),r=o(678);function l(e,t){var o={};return e&&"string"==typeof e?((0,n.default)(e,(function(e,n){e&&n&&(o[(0,r.camelCase)(e,t)]=n)})),o):o}l.default=l,e.exports=l},678:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var o=/^--[a-zA-Z0-9_-]+$/,n=/-([a-z])/g,r=/^[^-]+$/,l=/^-(webkit|moz|ms|o|khtml)-/,a=/^-(ms)-/,i=function(e,t){return t.toUpperCase()},c=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){return void 0===t&&(t={}),function(e){return!e||r.test(e)||o.test(e)}(e)?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(a,c):e.replace(l,c)).replace(n,i))}},174:function(e,t,o){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var o=null;if(!e||"string"!=typeof e)return o;var n=(0,r.default)(e),l="function"==typeof t;return n.forEach((function(e){if("declaration"===e.type){var n=e.property,r=e.value;l?t(n,r,e):r&&((o=o||{})[n]=r)}})),o};var r=n(o(139))},313:function(e){"use strict";e.exports=window.React},61:function(e,t,o){var n=o(698).default;function r(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e.exports=r=function(){return o},e.exports.__esModule=!0,e.exports.default=e.exports;var t,o={},l=Object.prototype,a=l.hasOwnProperty,i=Object.defineProperty||function(e,t,o){e[t]=o.value},c="function"==typeof Symbol?Symbol:{},s=c.iterator||"@@iterator",u=c.asyncIterator||"@@asyncIterator",p=c.toStringTag||"@@toStringTag";function d(e,t,o){return Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(t){d=function(e,t,o){return e[t]=o}}function m(e,t,o,n){var r=t&&t.prototype instanceof _?t:_,l=Object.create(r.prototype),a=new A(n||[]);return i(l,"_invoke",{value:j(e,o,a)}),l}function f(e,t,o){try{return{type:"normal",arg:e.call(t,o)}}catch(e){return{type:"throw",arg:e}}}o.wrap=m;var b="suspendedStart",y="suspendedYield",h="executing",v="completed",g={};function _(){}function k(){}function w(){}var O={};d(O,s,(function(){return this}));var x=Object.getPrototypeOf,C=x&&x(x(B([])));C&&C!==l&&a.call(C,s)&&(O=C);var E=w.prototype=_.prototype=Object.create(O);function P(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function S(e,t){function o(r,l,i,c){var s=f(e[r],e,l);if("throw"!==s.type){var u=s.arg,p=u.value;return p&&"object"==n(p)&&a.call(p,"__await")?t.resolve(p.__await).then((function(e){o("next",e,i,c)}),(function(e){o("throw",e,i,c)})):t.resolve(p).then((function(e){u.value=e,i(u)}),(function(e){return o("throw",e,i,c)}))}c(s.arg)}var r;i(this,"_invoke",{value:function(e,n){function l(){return new t((function(t,r){o(e,n,t,r)}))}return r=r?r.then(l,l):l()}})}function j(e,o,n){var r=b;return function(l,a){if(r===h)throw Error("Generator is already running");if(r===v){if("throw"===l)throw a;return{value:t,done:!0}}for(n.method=l,n.arg=a;;){var i=n.delegate;if(i){var c=I(i,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===b)throw r=v,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=h;var s=f(e,o,n);if("normal"===s.type){if(r=n.done?v:y,s.arg===g)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r=v,n.method="throw",n.arg=s.arg)}}}function I(e,o){var n=o.method,r=e.iterator[n];if(r===t)return o.delegate=null,"throw"===n&&e.iterator.return&&(o.method="return",o.arg=t,I(e,o),"throw"===o.method)||"return"!==n&&(o.method="throw",o.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var l=f(r,e.iterator,o.arg);if("throw"===l.type)return o.method="throw",o.arg=l.arg,o.delegate=null,g;var a=l.arg;return a?a.done?(o[e.resultName]=a.value,o.next=e.nextLoc,"return"!==o.method&&(o.method="next",o.arg=t),o.delegate=null,g):a:(o.method="throw",o.arg=new TypeError("iterator result is not an object"),o.delegate=null,g)}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function D(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function A(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function B(e){if(e||""===e){var o=e[s];if(o)return o.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,l=function o(){for(;++r<e.length;)if(a.call(e,r))return o.value=e[r],o.done=!1,o;return o.value=t,o.done=!0,o};return l.next=l}}throw new TypeError(n(e)+" is not iterable")}return k.prototype=w,i(E,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:k,configurable:!0}),k.displayName=d(w,p,"GeneratorFunction"),o.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===k||"GeneratorFunction"===(t.displayName||t.name))},o.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,d(e,p,"GeneratorFunction")),e.prototype=Object.create(E),e},o.awrap=function(e){return{__await:e}},P(S.prototype),d(S.prototype,u,(function(){return this})),o.AsyncIterator=S,o.async=function(e,t,n,r,l){void 0===l&&(l=Promise);var a=new S(m(e,t,n,r),l);return o.isGeneratorFunction(t)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},P(E),d(E,p,"Generator"),d(E,s,(function(){return this})),d(E,"toString",(function(){return"[object Generator]"})),o.keys=function(e){var t=Object(e),o=[];for(var n in t)o.push(n);return o.reverse(),function e(){for(;o.length;){var n=o.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},o.values=B,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(D),!e)for(var o in this)"t"===o.charAt(0)&&a.call(this,o)&&!isNaN(+o.slice(1))&&(this[o]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var o=this;function n(n,r){return i.type="throw",i.arg=e,o.next=n,r&&(o.method="next",o.arg=t),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var l=this.tryEntries[r],i=l.completion;if("root"===l.tryLoc)return n("end");if(l.tryLoc<=this.prev){var c=a.call(l,"catchLoc"),s=a.call(l,"finallyLoc");if(c&&s){if(this.prev<l.catchLoc)return n(l.catchLoc,!0);if(this.prev<l.finallyLoc)return n(l.finallyLoc)}else if(c){if(this.prev<l.catchLoc)return n(l.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<l.finallyLoc)return n(l.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var n=this.tryEntries[o];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var r=n;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=t&&t<=r.finallyLoc&&(r=null);var l=r?r.completion:{};return l.type=e,l.arg=t,r?(this.method="next",this.next=r.finallyLoc,g):this.complete(l)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var o=this.tryEntries[t];if(o.finallyLoc===e)return this.complete(o.completion,o.afterLoc),D(o),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var o=this.tryEntries[t];if(o.tryLoc===e){var n=o.completion;if("throw"===n.type){var r=n.arg;D(o)}return r}}throw Error("illegal catch attempt")},delegateYield:function(e,o,n){return this.delegate={iterator:B(e),resultName:o,nextLoc:n},"next"===this.method&&(this.arg=t),g}},o}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports},698:function(e){function t(o){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(o)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},687:function(e,t,o){var n=o(61)();e.exports=n;try{regeneratorRuntime=n}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}}},t={};function o(n){var r=t[n];if(void 0!==r)return r.exports;var l=t[n]={exports:{}};return e[n].call(l.exports,l,l.exports,o),l.exports}o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,{a:t}),t},o.d=function(e,t){for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(t){var o=function(t,o){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,o||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===o?String:Number)(t)}(t,"string");return"symbol"==e(o)?o:o+""}function n(e,o,n){return(o=t(o))in e?Object.defineProperty(e,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[o]=n,e}var r=window.wp.element,l=window.wp.i18n,a=window.wp.blocks,i=window.wp.blockEditor;function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,n=Array(t);o<t;o++)n[o]=e[o];return n}function s(e,t){if(e){if("string"==typeof e)return c(e,t);var o={}.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?c(e,t):void 0}}function u(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var o=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=o){var n,r,l,a,i=[],c=!0,s=!1;try{if(l=(o=o.call(e)).next,0===t){if(Object(o)!==o)return;c=!1}else for(;!(c=(n=l.call(o)).done)&&(i.push(n.value),i.length!==t);c=!0);}catch(e){s=!0,r=e}finally{try{if(!c&&null!=o.return&&(a=o.return(),Object(a)!==a))return}finally{if(s)throw r}}return i}}(e,t)||s(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var p=window.wp.components,d=window.wp.coreData;function m(e){e=function(e){const{body:t}=document.implementation.createHTMLDocument("");t.innerHTML=e;const o=t.getElementsByTagName("*");let n=o.length;for(;n--;){const e=o[n];if("SCRIPT"===e.tagName)(r=e).parentNode,r.parentNode.removeChild(r);else{let t=e.attributes.length;for(;t--;){const{name:o}=e.attributes[t];o.startsWith("on")&&e.removeAttribute(o)}}}var r;return t.innerHTML}(e);const t=document.implementation.createHTMLDocument("");return t.body.innerHTML=e,t.body.textContent||""}var f=function(e){return!!e&&("wp"===e.provider?"featured_image"===e.id||"author_avatar"===e.id||"archive_image"===e.id||"term_image"===e.id:"image"===e.type)},b={"top left":"is-position-top-left","top center":"is-position-top-center","top right":"is-position-top-right","center left":"is-position-center-left","center center":"is-position-center-center",center:"is-position-center-center","center right":"is-position-center-right","bottom left":"is-position-bottom-left","bottom center":"is-position-bottom-center","bottom right":"is-position-bottom-right"};function y(e){return function(e){return!e||"center center"===e||"center"===e}(e)?"":b[e]}function h(e){return m(e=e.replace(/<br>/g,"\n")).trim().replace(/\n\n+/g,"\n\n")}var v=function(e){var t=e.breadcrumbs,o=e.postId,n=e.postType;if(!t||!t.render)return(0,r.createElement)(p.Spinner,null);var l=u((0,d.useEntityProp)("postType",n,"title",o),1)[0],a=void 0===l?"":l;return(0,r.createElement)("span",{dangerouslySetInnerHTML:{__html:t.render.replace("__BLOCKSY_BREADCRUMBS_POST_TITLE__",h(a))}})};function g(){return g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var n in o)({}).hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e},g.apply(null,arguments)}var _=function(e){var t=e.label,o=e.resetAll,n=e.panelId,l=e.settings,a=e.skipToolsPanel,c=void 0!==a&&a,s=e.containerProps,u=void 0===s?{}:s,d=(0,i.__experimentalUseMultipleOriginColorsAndGradients)();return c?(0,r.createElement)(i.__experimentalColorGradientSettingsDropdown,g({__experimentalIsRenderedInSidebar:!0,__experimentalHasMultipleOrigins:!0,__experimentalGroup:"bg",settings:l,panelId:n,disableCustomGradients:!0},u,d)):(0,r.createElement)(p.__experimentalToolsPanel,{label:t,resetAll:o,panelId:n,hasInnerWrapper:!0,className:"color-block-support-panel",__experimentalFirstVisibleItemClass:"first",__experimentalLastVisibleItemClass:"last"},(0,r.createElement)("div",{className:"color-block-support-panel__inner-wrapper"},(0,r.createElement)(i.__experimentalColorGradientSettingsDropdown,g({__experimentalIsRenderedInSidebar:!0,__experimentalHasMultipleOrigins:!0,__experimentalGroup:"bg",settings:l,panelId:n,disableCustomGradients:!0},u,d))))},k=window.wp.data,w=window.wp.editor;function O(e){return function(e){if(Array.isArray(e))return c(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||s(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function x(e,t){if(null==e)return{};var o,n,r=function(e,t){if(null==e)return{};var o={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.includes(n))continue;o[n]=e[n]}return o}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(n=0;n<l.length;n++)o=l[n],t.includes(o)||{}.propertyIsEnumerable.call(e,o)&&(r[o]=e[o])}return r}function C(e,o){for(var n=0;n<o.length;n++){var r=o[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,t(r.key),r)}}var E=function(){var e={};return e.promise=new Promise((function(t,o){e.resolve=t,e.reject=o})),e},P=o(568),S=o.n(P);function j(t){if(Array.isArray(t))return"[".concat(t.map((function(e){return j(e)})).join(","),"]");if("object"===e(t)&&null!==t){var o="",n=Object.keys(t).sort();o+="{".concat(JSON.stringify(n));for(var r=0;r<n.length;r++)o+="".concat(j(t[n[r]]),",");return"".concat(o,"}")}return"".concat(JSON.stringify(t))}var I=function(e){return S()(j(e))};function T(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function D(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?T(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):T(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var A=0,B=1,H=2,N={cacheKey:{state:A,response:null,payoad:{}}},z={},M=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,o,n;return t=e,o=[{key:"parseOptions",value:function(e){var t=e.fetcherName;return{args:{fetcherName:void 0===t?null:t},inputFetchOptions:x(e,["fetcherName"])}}},{key:"fetch",value:function(e,t,o){var n=this.parseOptions(o).args,r=I(D(D({},t),{},{url:e}));if(N[r]||(N[r]={state:A,response:null,payload:{waitingForResponse:[]}},n.fetcherName&&z[n.fetcherName]&&(z[n.fetcherName].forEach((function(e){e.abort()})),z[n.fetcherName]=[])),N[r].state===H){var l=E();return l.resolve(N[r].response.clone()),l.promise}if(N[r].state===B)return this.fetchLoadingState(r,e,t,o);if(N[r].state===A)return this.fetchEmptyState(r,e,t,o);throw new Error("Invalid state",{cacheEntry:N[r]})}},{key:"fetchLoadingState",value:function(e,t,o,n){var r=E();return N[e].payload.waitingForResponse.push(r),r.promise}},{key:"fetchEmptyState",value:function(e,t,o,n){var r=this.parseOptions(n),l=r.args,a=r.inputFetchOptions;N[e].state=B;var i=new AbortController;l.fetcherName&&(z[l.fetcherName]||(z[l.fetcherName]=[]),z[l.fetcherName].push(i));var c=E(),s=D({method:"POST",headers:{"Content-Type":"application/json"},signal:i.signal},a);return"POST"===s.method&&(s.body=JSON.stringify(o)),fetch(t,s).then((function(t){N[e].response=t.clone(),[c].concat(O(N[e].payload.waitingForResponse)).forEach((function(t){t.resolve(N[e].response.clone())})),N[e].payload={waitingForResponse:[]},N[e].state=H,l.fetcherName&&(z[l.fetcherName]=[])})).catch((function(t){[c].concat(O(N[e].payload.waitingForResponse)).forEach((function(t){t.reject(N[e].response)})),N[e].payload={waitingForResponse:[]},N[e].state=A})),c.promise}}],o&&C(t.prototype,o),n&&C(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),F=new M,R=function(e,t){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return F.fetch(e,t,o)},V=function(e){var t=e.postId,o=e.postType,n=u((0,r.useState)(null),2),l=n[0],a=n[1],i=(0,k.useSelect)((function(e){return{taxonomies:e(d.store).getEntityRecords("root","taxonomy",{per_page:-1})}}),[]).taxonomies,c=(null!=i?i:[]).filter((function(e){var t;return e.types.includes(o)&&(null===(t=e.visibility)||void 0===t?void 0:t.show_ui)})),s=(0,k.useSelect)((function(e){var t=e(w.store).getEditedPostAttribute;return c.reduce((function(e,o){var n=t(o.rest_base);return e[o.slug]=n,e}),{})}),[c]);return(0,r.useEffect)((function(){t&&R("".concat(wp.ajax.settings.url,"?action=blocksy_blocks_retrieve_breadcrumbs_data_descriptor"),{post_id:t,taxonomyTerms:s}).then((function(e){return e.json()})).then((function(e){e.success;var t=e.data;a(t)}))}),[t,s]),{breadcrumbs:l}},L=(0,i.withColors)({textColor:"color"},{linkColor:"color"},{linkHoverColor:"color"})((function(e){var t=e.clientId,o=e.textColor,n=e.setTextColor,a=e.linkColor,c=e.setLinkColor,s=e.linkHoverColor,u=e.setLinkHoverColor,p=e.className,d=e.context,m=d.postId,f=d.postType,b=V({postId:m,postType:f}).breadcrumbs,y=(0,r.useRef)(),h=(0,i.useBlockProps)({ref:y,className:{"ct-breadcrumbs":!0,className:p},style:{color:null==o?void 0:o.color,"--theme-link-initial-color":null==a?void 0:a.color,"--theme-link-hover-color":null==s?void 0:s.color}});return(0,r.createElement)(React.Fragment,null,(0,r.createElement)("div",h,(0,r.createElement)(v,{breadcrumbs:b,postId:m,postType:f}),(0,r.createElement)(i.InspectorControls,{group:"styles"},(0,r.createElement)(_,{label:(0,l.__)("Text Color","blocksy-companion"),resetAll:function(){n(""),c(""),u("")},panelId:t,settings:[{colorValue:o.color,enableAlpha:!0,label:(0,l.__)("Text","blocksy-companion"),onColorChange:n},{colorValue:a.color,enableAlpha:!0,label:(0,l.__)("Link Initial","blocksy-companion"),onColorChange:c},{colorValue:s.color,enableAlpha:!0,label:(0,l.__)("Link Hover","blocksy-companion"),onColorChange:u}]}))))})),U=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"blocksy/breadcrumbs","category":"blocksy-blocks","attributes":{"textColor":{"type":"string"},"customTextColor":{"type":"string"},"linkColor":{"type":"string"},"customLinkColor":{"type":"string"},"linkHoverColor":{"type":"string"},"customLinkHoverColor":{"type":"string"}},"supports":{"className":false,"spacing":{"margin":true,"__experimentalDefaultControls":{"margin":true}}},"providesContext":{"textColor":"textColor","linkColor":"linkColor","linkHoverColor":"linkHoverColor"}}');function q(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function G(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?q(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):q(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}(0,a.registerBlockType)("blocksy/breadcrumbs",G(G({},U),{},{title:(0,l.__)("Breadcrumbs","blocksy-companion"),description:(0,l.__)("Display navigational links, showing users their path within the site.","blocksy-companion"),icon:{src:(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)("path",{d:"M4,10.5h6v3H4V10.5z M12,13.5h3v-3h-3V13.5z M17,10.5v3h3v-3H17z"}))},edit:function(e){return(0,r.createElement)(L,e)},save:function(){return null},usesContext:["postId","postType"]}));var W=function(e){var t=e.attributes,o=e.previewedPostId,n=u((0,r.useState)(null),2),l=n[0],a=n[1];return(0,r.useEffect)((function(){var e=new URLSearchParams(window.location.search);R("".concat(wp.ajax.settings.url,"?action=blocksy_get_posts_block_data").concat(e.get("lang")?"&lang="+e.get("lang"):""),{attributes:t,previewedPostId:o},{fetcherName:"posts-block-data-".concat(t.uniqueId),headers:{Accept:"application/json","Content-Type":"application/json"},method:"POST"}).then((function(e){return e.json()})).then((function(e){var t=e.success,o=e.data;t&&a(o)})).catch((function(e){}))}),[t,o]),{blockData:l}},Z=window.blocksyOptions,$=function(e){var t=e.isSlideshow,o=e.attributes,n=e.context,l=e.toWatch,a=u((0,r.useState)(null),2),i=a[0],c=a[1],s=(0,r.useRef)(),p=u((0,r.useState)(null),2),d=p[0],m=p[1],f={},b=[];return(0,r.useEffect)((function(){i&&i.scheduleSliderRecalculation()}),[o,n]),(0,r.useEffect)((function(){i&&!t&&i.destroy(),i&&t&&setTimeout((function(){i.refreshActivation()}),50)}),[t,i,l]),(0,r.useEffect)((function(){return function(){i&&i.destroy()}}),[]),t&&(f.ref=s,f.onMouseOver=function(){i||(0,Z.mountFlexy)().then((function(e){var t=e.mount;e.Flexy;c(t((function(){return s.current}),{flexyOptions:{dragAndDropOptions:{mountDragAndDropEventListener:!1},arrowsOptions:{mountListeners:!1},onRender:function(e,t){m(t)}}}))}))}),d&&(Object.keys(d.flexyAttributeElAttr).map((function(e){f[e]=d.flexyAttributeElAttr[e]})),b=d.elementsDescriptor),{flexyInstance:i,flexyContainerAttr:f,elementsDescriptor:b,elementDescriptorForIndex:function(e){return b&&b.length>0&&b[e]?b[e]:b&&b.length>0&&!b[e]?b[0]:null},arrowsDescritor:{left:{onClick:function(e){e.preventDefault(),i&&i.sliderArrows.navigate("left")}},right:{onClick:function(e){e.preventDefault(),i&&i.sliderArrows.navigate("right")}}}}},K=o(496),X=o.n(K);function Y(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}var J=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o="",l=X()(),a={cacheId:l,initialStyleTagsDescriptor:[{readStyles:function(){return""},persistStyles:function(e){o=e}}]};Z.syncHelpers.getStyleTagsWithAst(a);e.map((function(e){var t=Z.syncHelpers.getUpdateAstsForStyleDescriptor(function(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Y(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}({variableDescriptor:e.variables,value:e.value,fullValue:{}},a));Z.syncHelpers.persistNewAsts(l,t)}));var i=o?(0,r.createElement)("style",null,o):null;return t=[(window.ct_localizations||window.ct_customizer_localizations).backend_dynamic_styles_urls.flexy],(0,r.createElement)(r.Fragment,null,i,t.map((function(e){return(0,r.createElement)("link",{key:e,rel:"stylesheet",href:e})})))},Q=o(184),ee=o.n(Q),te=o(426),oe=te.default||te;function ne(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function re(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?ne(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):ne(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var le=function(e){var t=e.attributes,o=e.postId,n=(e.uniqueId,W({attributes:t,previewedPostId:o}).blockData),a="yes"===t.has_slideshow,i=$({isSlideshow:a,attributes:t,toWatch:n?n.customizer_preview:{}}),c=J([]);return n?n.customizer_preview?(0,r.createElement)(React.Fragment,null,function(e){var t=e.isSlideshow,o=e.sliderDescriptor,n=e.blockData;if(!t)return(0,r.createElement)(r.RawHTML,null,n.customizer_preview);var l={replace:function(e,t){var n=e.attribs,a=e.children,i=e.name;if(n&&n.class&&n.class.includes("flexy-container"))return(0,r.createElement)("div",g({},(0,te.attributesToProps)(n),o.flexyContainerAttr),(0,te.domToReact)(a,l));if(n&&n.class&&n.class.includes("flexy-arrow-")){var c=i,s=n.class.includes("flexy-arrow-next")?o.arrowsDescritor.right:o.arrowsDescritor.left;return(0,r.createElement)(c,g({},(0,te.attributesToProps)(n),s),(0,te.domToReact)(a,l))}if((n&&n.class?n.class.split(" "):[]).includes("flexy-item")){var u=o.elementDescriptorForIndex(t);u&&(u=u.attr);var p=(0,te.attributesToProps)(n),d=p.className,m=x(p,["className"]);if(u){var f=u,b=f.className,y=x(f,["className"]);d=ee()(d,b),m=re(re({},m),y)}return(0,r.createElement)("div",g({className:ee()(d)},m),(0,te.domToReact)(a,l))}}};return oe(n.customizer_preview,l)}({isSlideshow:a,sliderDescriptor:i,blockData:n}),c):(0,r.createElement)("p",null,(0,l.__)("No results found.","blocksy-companion")):(0,r.createElement)(p.Spinner,null)},ae=function(e){return e.split("-")[0]};var ie=function(e){var t=e.attributes,o=e.clientId,n=e.setAttributes,l=e.blockType;(0,r.useEffect)((function(){t.uniqueId&&!function(e){var t=e.attributes,o=e.blockType,n=document.querySelectorAll("iframe");return[document].concat(O(Array.from(n).filter((function(e){return!e.closest(".block-editor-block-preview__container")})).map((function(e){return e.contentDocument})).filter((function(e){return e})))).flatMap((function(e){return O(e.querySelectorAll('[data-id="'.concat(t.uniqueId,'"][data-type="').concat(o,'"]')))})).length>1}({attributes:t,clientId:o,blockType:l})||n({uniqueId:ae(o)})}),[o]);var a=t.uniqueId||ae(o);return{uniqueId:a,props:{"data-id":a}}},ce=function(e){var t=e.clientId,o=e.setAttributes,n=e.setIsPatternSelectionModalOpen,c=(0,k.useDispatch)(i.store).replaceInnerBlocks;return(0,r.createElement)("div",{className:"components-placeholder is-large"},(0,r.createElement)("div",{className:"components-placeholder__label"},(0,r.createElement)("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,r.createElement)("path",{d:"M5.5 18v-1c0-.3.2-.5.5-.5h3c.3 0 .5.2.5.5v1c0 .3-.2.5-.5.5H6c-.3 0-.5-.2-.5-.5zm.5 2c-1.1 0-2-.9-2-2v-1c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2v1c0 1.1-.9 2-2 2H6zm-.5-9V6c0-.3.2-.5.5-.5h3c.3 0 .5.2.5.5v5c0 .3-.2.5-.5.5H6c-.3 0-.5-.2-.5-.5zm.5 2c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2v5c0 1.1-.9 2-2 2H6zm8.5 0v5c0 .*******.5h3c.3 0 .5-.2.5-.5v-5c0-.3-.2-.5-.5-.5h-3c-.3 0-.5.2-.5.5zM13 18c0 1.1.9 2 2 2h3c1.1 0 2-.9 2-2v-5c0-1.1-.9-2-2-2h-3c-1.1 0-2 .9-2 2v5zm1.5-11V6c0-.3.2-.5.5-.5h3c.3 0 .5.2.5.5v1c0 .3-.2.5-.5.5h-3c-.3 0-.5-.2-.5-.5zm.5 2c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2v1c0 1.1-.9 2-2 2h-3z",fillRule:"evenodd"})),(0,l.__)("Advanced Posts","blocksy-companion")),(0,r.createElement)("fieldset",{className:"components-placeholder__fieldset"},(0,r.createElement)("legend",{className:"components-placeholder__instructions"},(0,l.__)("Inherit the Customizer layout, start with a pattern or create a custom layout","blocksy-companion")),(0,r.createElement)("button",{className:"components-button is-primary",onClick:function(e){e.preventDefault(),o({design:"default"})}},(0,l.__)("Inherit From Customizer","blocksy-companion")),(0,r.createElement)("button",{className:"components-button is-primary",onClick:function(e){e.preventDefault(),n(!0)}},(0,l.__)("Choose Pattern","blocksy-companion")),(0,r.createElement)("button",{className:"components-button is-primary",onClick:function(e){e.preventDefault(),c(t,(0,a.createBlocksFromInnerBlocksTemplate)([["blocksy/post-template",{},[["blocksy/dynamic-data",{tagName:"h2",field:"wp:title",has_field_link:"yes"}],["blocksy/dynamic-data",{field:"wp:date"}],["blocksy/dynamic-data",{field:"wp:excerpt"}]]]]),!1)}},(0,l.__)("Create Custom Layout","blocksy-companion"))))},se=o(313),ue=window.wp.primitives;var pe=(0,se.createElement)(ue.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,se.createElement)(ue.Path,{d:"M11 12.5V17.5H12.5V12.5H17.5V11H12.5V6H11V11H6V12.5H11Z"}));var de=(0,se.createElement)(ue.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,se.createElement)(ue.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"}));var me=(0,se.createElement)(ue.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,se.createElement)(ue.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})),fe=window.wp.a11y,be=function(e){var t=e.items,o=e.selectedItems,n=e.onItemClick,a=e.label,i=e.resetAll,c=o.length>0;return(0,r.createElement)(p.__experimentalHStack,null,(0,r.createElement)(p.__experimentalHeading,{style:{margin:0},level:2},a),(0,r.createElement)(p.DropdownMenu,{icon:0===o.length?pe:de,label:(0,l.__)("Parameters options","blocksy-companion"),toggleProps:{isSmall:!0,describedBy:0===o.length?(0,l.__)("All options are currently hidden","blocksy-companion"):void 0}},(function(){return(0,r.createElement)(React.Fragment,null,t.map((function(e,t){return(0,r.createElement)(p.MenuGroup,{key:t,label:e.label},e.items.map((function(e,t){var l=o.includes(e.label);return(0,r.createElement)(p.MenuItem,{key:t,icon:l?me:null,isSelected:l,onClick:function(){return n(e.label)}},e.label)})))})),(0,r.createElement)(p.MenuGroup,null,(0,r.createElement)(p.MenuItem,{"aria-disabled":!c,variant:"tertiary",onClick:function(){c&&(i(),(0,fe.speak)((0,l.__)("All options reset","blocksy-companion"),"assertive"))}},(0,l.__)("Reset all","blocksy-companion"))))})))},ye=function(e){var t=e.className,o=e.attributes,n=e.setAttributes,l=e.resetAll,a=e.items,i=e.label,c=a.reduce((function(e,t){return[].concat(O(e),O(t.items.filter((function(e){return e.hasValue()})).map((function(e){return e.label}))))}),[]),s=u((0,r.useState)([]),2),p=s[0],d=s[1],m=O(new Set([].concat(O(p),O(c))));return(0,r.createElement)("div",{className:ee()("ct-tools-panel",t)},(0,r.createElement)(be,{label:i,resetAll:function(){d([]),l()},items:a,selectedItems:m,attributes:o,setAttributes:n,onItemClick:function(e){if(m.includes(e)){var t=a.reduce((function(e,t){return[].concat(O(e),O(t.items))}),[]).find((function(t){return t.label===e}));d(p.filter((function(t){return t!==e}))),t.reset()}else d([].concat(O(p),[e]))}}),(0,r.createElement)("div",{className:"ct-tools-panel-items"},a.reduce((function(e,t){return[].concat(O(e),O(t.items))}),[]).filter((function(e){return m.includes(e.label)})).map((function(e){return e.render()}))))};function he(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function ve(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?he(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):he(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var ge=["wp_pattern_category","nav_menu"],_e=function(e,t){for(var o=e.map((function(e){return(0,a.cloneBlock)(e)})),n=[],r=O(o);r.length>0;){var l,i=r.shift();"blocksy/query"===i.name&&(i.attributes.uniqueId="",n.push(i.clientId)),null===(l=i.innerBlocks)||void 0===l||l.forEach((function(e){r.push(e)}))}return{newBlocks:o,queryClientIds:n}};var ke=function(e){return(0,k.useSelect)((function(t){return((0,t(d.store).getTaxonomies)({type:e,per_page:-1,context:"view"})||[]).filter((function(e){var t=e.slug;return!ge.includes(t)}))}),[e])},we=window.wp.compose;function Oe(e){if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(e=function(e,t){if(!e)return;if("string"==typeof e)return xe(e,t);var o=Object.prototype.toString.call(e).slice(8,-1);"Object"===o&&e.constructor&&(o=e.constructor.name);if("Map"===o||"Set"===o)return Array.from(e);if("Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return xe(e,t)}(e))){var t=0,o=function(){};return{s:o,n:function(){return t>=e.length?{done:!0}:{done:!1,value:e[t++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,r,l=!0,a=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return l=e.done,e},e:function(e){a=!0,r=e},f:function(){try{l||null==n.return||n.return()}finally{if(a)throw r}}}}function xe(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,n=new Array(t);o<t;o++)n[o]=e[o];return n}function Ce(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Ee(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Ce(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Ce(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var Pe=[],Se={order:"asc",_fields:"id,name,slug",context:"view"},je=function(e,t){var o,n,r=(null==t?void 0:t.slug)||(null==e||null===(o=e.find((function(e){return e.name===t})))||void 0===o?void 0:o.slug);if(r)return r;var l=t.toLocaleLowerCase();return null==e||null===(n=e.find((function(e){return e.name.toLocaleLowerCase()===l})))||void 0===n?void 0:n.slug};function Ie(e){var t=e.taxonomy,o=e.termIds,n=e.onChange,a=e.label,i=void 0!==a&&a,c=u((0,r.useState)(""),2),s=c[0],m=c[1],f=u((0,r.useState)(Pe),2),b=f[0],y=f[1],h=u((0,r.useState)(Pe),2),v=h[0],g=h[1],_=(0,we.useDebounce)(m,250),w=(0,k.useSelect)((function(e){if(!s)return{searchResults:Pe,searchHasResolved:!0};var o=e(d.store),n=o.getEntityRecords,r=o.hasFinishedResolution,l=["taxonomy",t.slug,Ee(Ee({},Se),{},{search:s,orderby:"name",per_page:20})];return{searchResults:n.apply(void 0,l),searchHasResolved:r("getEntityRecords",l)}}),[s,o]),O=w.searchResults,x=w.searchHasResolved,C=(0,k.useSelect)((function(e){return null!=o&&o.length?(0,e(d.store).getEntityRecords)("taxonomy",t.slug,Ee(Ee({},Se),{},{slug:o,per_page:o.length})):Pe}),[o]);(0,r.useEffect)((function(){if(null!=o&&o.length||y(Pe),null!=C&&C.length){var e=o.reduce((function(e,t){var o=C.find((function(e){return e.slug===t}));return o&&e.push({id:t,slug:t,value:o.name}),e}),[]);y(e)}}),[o,C]),(0,r.useEffect)((function(){x&&g(O.map((function(e){return e.name})))}),[O,x]);return(0,r.createElement)(p.FormTokenField,{label:i,value:b,onInputChange:_,suggestions:v,onChange:function(e){var t,o=new Set,r=Oe(e);try{for(r.s();!(t=r.n()).done;){var l=t.value,a=je(O,l);a&&o.add(a)}}catch(e){r.e(e)}finally{r.f()}g(Pe),n(Array.from(o))},__experimentalShowHowTo:!1,__nextHasNoMarginBottom:!0,placeholder:(0,l.__)("Search for a term","blocksy-companion")})}function Te(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function De(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Te(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Te(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function Ae(t){var o=function(t,o){if("object"!==e(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,o||"default");if("object"!==e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===o?String:Number)(t)}(t,"string");return"symbol"===e(o)?o:String(o)}var Be=function(e){var t=e.taxonomy,o=e.attributes,a=e.setAttributes,i=e.previewedPostMatchesType;return{label:(0,l.sprintf)((0,l.__)("Include %s","blocksy-companion"),t.name),hasValue:function(){return o.include_term_ids[t.slug]},reset:function(){var e=o.include_term_ids||{},n=t.slug,r=(e[n],x(e,[n].map(Ae)));a({include_term_ids:r})},render:function(){var e=o.include_term_ids[t.slug]||{strategy:"all",terms:[]};return(0,r.createElement)("div",null,(0,r.createElement)(p.__experimentalToggleGroupControl,{label:(0,l.sprintf)((0,l.__)("Include %s","blocksy-companion"),t.name),value:e.strategy,isBlock:!0,onChange:function(r){a({include_term_ids:De(De({},o.include_term_ids),{},n({},t.slug,De(De({},e),{},{strategy:r})))})}},(0,r.createElement)(p.__experimentalToggleGroupControlOption,{value:"all",label:(0,l.__)("All","blocksy-companion")}),(0,r.createElement)(p.__experimentalToggleGroupControlOption,{value:"specific",label:(0,l.__)("Specific","blocksy-companion")}),i&&(0,r.createElement)(p.__experimentalToggleGroupControlOption,{value:"related",label:(0,l.__)("Related","blocksy-companion")})),"specific"===e.strategy&&(0,r.createElement)(Ie,{taxonomy:t,termIds:e.terms,onChange:function(r){a({include_term_ids:De(De({},o.include_term_ids),{},n({},t.slug,De(De({},e),{},{terms:r})))})}}))}}},He=function(e){var t=e.taxonomy,o=e.attributes,a=e.setAttributes;return{label:(0,l.sprintf)((0,l.__)("Exclude %s","blocksy-companion"),t.name),hasValue:function(){return o.exclude_term_ids[t.slug]},reset:function(){var e=o.exclude_term_ids||{},n=t.slug,r=(e[n],x(e,[n].map(Ae)));a({exclude_term_ids:r})},render:function(){var e=o.exclude_term_ids[t.slug]||{strategy:"specific",terms:[]};return(0,r.createElement)(Ie,{label:(0,l.sprintf)((0,l.__)("Exclude %s","blocksy-companion"),t.name),taxonomy:t,termIds:e.terms,onChange:function(r){a({exclude_term_ids:De(De({},o.exclude_term_ids),{},n({},t.slug,De(De({},e),{},{terms:r})))})}})}}},Ne=function(e){var t=e.attributes,o=e.attributes.taxonomy,n=e.setAttributes,r=function(e){return(0,k.useSelect)((function(t){return(0,t(d.store).getTaxonomy)(e)}),[e])}(o);if(!r)return{taxonomiesGroup:null};var a=[Be({taxonomy:r,attributes:t,setAttributes:n}),He({taxonomy:r,attributes:t,setAttributes:n})].concat();return{taxonomiesGroup:a.length>0?{label:(0,l.__)("Taxonomies","blocksy-companion"),items:a}:null}},ze=function(e){var t=e.context,o=e.attributes,a=(e.attributes.post_type,e.setAttributes),c=function(e){var t=e.attributes,o=e.attributes.post_type,n=e.previewedPostMatchesType,r=e.setAttributes,a=ke(o),i=[];return a&&a.length>0&&(i=[].concat(O(a.map((function(e){return Be({taxonomy:e,attributes:t,setAttributes:r,previewedPostMatchesType:n})}))),O(a.map((function(e){return He({taxonomy:e,attributes:t,setAttributes:r})}))))),{taxonomiesGroup:i.length>0?{label:(0,l.__)("Taxonomies","blocksy-companion"),items:i}:null}}({attributes:o,setAttributes:a,previewedPostMatchesType:o.post_type===t.postType}),s=c.taxonomiesGroup;return(0,r.createElement)(i.InspectorControls,null,(0,r.createElement)(ye,{className:"ct-query-parameters-component",attributes:o,setAttributes:a,resetAll:function(){a({offset:0,sticky_posts:"include",orderby:"post_date",order:"desc",include_term_ids:{},exclude_term_ids:{}})},items:[{label:(0,l.__)("General","blocksy-companion"),items:[{label:(0,l.__)("Offset","blocksy-companion"),hasValue:function(){return 0!==o.offset},reset:function(){a({offset:0})},render:function(){return(0,r.createElement)(Z.OptionsPanel,{purpose:"gutenberg",onChange:function(e,t){a(n({},e,t))},options:{offset:{type:"ct-number",label:(0,l.__)("Offset","blocksy-companion"),value:"",min:0,max:500}},value:o,hasRevertButton:!1})}},{label:(0,l.__)("Order by","blocksy-companion"),hasValue:function(){return"post_date"!==o.orderby},reset:function(){a({orderby:"post_date"})},render:function(){return(0,r.createElement)(Z.OptionsPanel,{purpose:"gutenberg",onChange:function(e,t){a(n({},e,t))},options:{orderby:{type:"ct-select",label:(0,l.__)("Order by","blocksy-companion"),value:"",choices:[{key:"title",value:(0,l.__)("Title","blocksy-companion")},{key:"post_date",value:(0,l.__)("Publish Date","blocksy-companion")},{key:"modified",value:(0,l.__)("Modified Date","blocksy-companion")},{key:"comment_count",value:(0,l.__)("Most commented","blocksy-companion")},{key:"author",value:(0,l.__)("Author","blocksy-companion")},{key:"rand",value:(0,l.__)("Random","blocksy-companion")},{key:"menu_order",value:(0,l.__)("Menu Order","blocksy-companion")}]}},value:o,hasRevertButton:!1})}},{label:(0,l.__)("Order","blocksy-companion"),hasValue:function(){return"desc"!==o.order},reset:function(){a({order:"desc"})},render:function(){return(0,r.createElement)(Z.OptionsPanel,{purpose:"gutenberg",onChange:function(e,t){a(n({},e,t))},options:{order:{type:"ct-select",label:(0,l.__)("Order","blocksy-companion"),value:"",choices:[{key:"DESC",value:(0,l.__)("Descending","blocksy-companion")},{key:"ASC",value:(0,l.__)("Ascending","blocksy-companion")}]}},value:o,hasRevertButton:!1})}},{label:(0,l.__)("Sticky Posts","blocksy-companion"),hasValue:function(){return"include"!==o.sticky_posts},reset:function(){a({sticky_posts:"include"})},render:function(){return(0,r.createElement)(Z.OptionsPanel,{purpose:"gutenberg",onChange:function(e,t){a(n({},e,t))},options:{sticky_posts:{type:"ct-select",label:(0,l.__)("Sticky Posts","blocksy-companion"),value:"include",choices:[{key:"include",value:(0,l.__)("Include","blocksy-companion")},{key:"exclude",value:(0,l.__)("Exclude","blocksy-companion")},{key:"only",value:(0,l.__)("Only","blocksy-companion")}]}},value:o,hasRevertButton:!1})}}]}].concat(O(s?[s]:[])),label:(0,l.__)("Parameters","blocksy-companion")}))};function Me(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Fe(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Me(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Me(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var Re=null,Ve=new AbortController,Le=o(826),Ue=o.n(Le);function qe(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e=(e=Ue()(e)).trim().toLowerCase()}function Ge(e,t){var o=qe(t),n=qe(e.title),r=0;if(o===n)r+=30;else if(n.startsWith(o))r+=20;else{o.split(" ").every((function(e){return n.includes(e)}))&&(r+=10)}return r}function We(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(!t)return e;var o=e.map((function(e){return[e,Ge(e,t)]})).filter((function(e){return u(e,2)[1]>0}));return o.sort((function(e,t){var o=u(e,2)[1];return u(t,2)[1]-o})),o.map((function(e){return u(e,1)[0]}))}var Ze=function(e){var t=e.clientId,o=(e.attributes,e.setIsPatternSelectionModalOpen),n=e.postType,c=u((0,r.useState)(""),2),s=c[0],d=c[1],m=(0,k.useDispatch)(i.store),f=m.replaceBlock,b=m.selectBlock,y=u((0,r.useState)([]),2),h=y[0],v=y[1];(0,r.useEffect)((function(){new Promise((function(e){Re?e(Re):fetch("".concat(wp.ajax.settings.url,"?action=blocksy_get_posts_block_patterns"),{headers:{Accept:"application/json","Content-Type":"application/json"},method:"POST",body:JSON.stringify({}),signal:Ve.signal}).then((function(e){return e.json()})).then((function(t){var o=t.success,n=t.data;o&&(Re=n.patterns.map((function(e){return Fe(Fe({},e),{},{blocks:(0,a.parse)(e.content,{__unstableSkipMigrationLogs:!0})})})),e(Re))}))})).then((function(e){v(e)}))}),[]);var g=(0,r.useMemo)((function(){return{previewPostType:n}}),[n]),_=(0,r.useMemo)((function(){return We(h,s)}),[h,s]),w=(0,we.useAsyncList)(_);return(0,r.createElement)(p.Modal,{overlayClassName:"block-library-query-pattern__selection-modal",title:(0,l.__)("Choose a pattern","blocksy-companion"),onRequestClose:function(){return o(!1)},isFullScreen:!0},(0,r.createElement)("div",{className:"block-library-query-pattern__selection-content"},(0,r.createElement)("div",{className:"block-library-query-pattern__selection-search"},(0,r.createElement)(p.SearchControl,{__nextHasNoMarginBottom:!0,onChange:d,value:s,label:(0,l.__)("Search for patterns","blocksy-companion"),placeholder:(0,l.__)("Search","blocksy-companion")})),(0,r.createElement)(i.BlockContextProvider,{value:g},(0,r.createElement)(i.__experimentalBlockPatternsList,{blockPatterns:_,shownPatterns:w,onClickPattern:function(e,o){var n=_e(o),r=n.newBlocks,l=n.queryClientIds;f(t,r),l[0]&&b(l[0])}}))))};function $e(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Ke(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?$e(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):$e(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var Xe=function(e){var t=e.clientId,o=e.className,a=e.attributes,c=e.setAttributes,s=e.context,m=(0,i.useInnerBlocksProps)({},{}),f=(0,k.useSelect)((function(e){return!!e(i.store).getBlocks(t).length}),[t]),b=!f&&"default"!==a.design,y=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).hasPages,t=void 0!==e&&e,o=(0,k.useSelect)((function(e){var o,n=e(d.store).getPostTypes,r=["attachment","product"].concat(O(t?[]:["page"]));return null===(o=n({per_page:-1}))||void 0===o?void 0:o.filter((function(e){var t=e.viewable,o=e.slug;return t&&!r.includes(o)&&!o.includes("ct_")&&!o.includes("blc-")}))}),[t]);return{postTypesTaxonomiesMap:(0,r.useMemo)((function(){if(null!=o&&o.length)return o.reduce((function(e,t){return e[t.slug]=t.taxonomies,e}),{})}),[o]),postTypesSelectOptions:(0,r.useMemo)((function(){return(o||[]).reduce((function(e,t){return ve(ve({},e),{},n({},t.slug,t.labels.singular_name))}),{})}),[o])}}({hasPages:f}),h=y.postTypesSelectOptions,v=ie({attributes:a,setAttributes:c,clientId:t,blockType:"blocksy/query"}),_=v.uniqueId,w=v.props,x=s.postId,C=(s.postType,(0,r.useRef)()),E=u((0,r.useState)(null),2),P=(E[0],E[1],(0,i.__experimentalUseBorderProps)(a)),S=(0,i.useBlockProps)({ref:C,className:o,style:Ke({},P.style)}),j=u((0,r.useState)(!1),2),I=j[0],T=j[1];return(0,r.createElement)(React.Fragment,null,I&&(0,r.createElement)(Ze,{clientId:t,attributes:a,setIsPatternSelectionModalOpen:T,postType:a.post_type}),b?(0,r.createElement)("div",g({},S,w),(0,r.createElement)(ce,{setIsPatternSelectionModalOpen:T,attributes:a,setAttributes:c,clientId:t})):(0,r.createElement)("div",g({},S,w),"default"===a.design&&(0,r.createElement)(le,{uniqueId:_,attributes:a,postId:x}),f&&(0,r.createElement)("div",m)),(0,r.createElement)(i.BlockControls,null,(0,r.createElement)(p.ToolbarGroup,null,(0,r.createElement)(p.ToolbarButton,{className:"components-toolbar__control",icon:"layout",label:(0,l.__)("Reset layout","blocksy-companion"),disabled:b,onClick:function(){f?(0,k.dispatch)("core/block-editor").replaceInnerBlocks(t,[],!1):(0,k.dispatch)("core/block-editor").updateBlockAttributes(t,{design:""})}}))),!b&&(0,r.createElement)(React.Fragment,null,(0,r.createElement)(i.InspectorControls,null,(0,r.createElement)(p.PanelBody,null,(0,r.createElement)(Z.OptionsPanel,{purpose:"gutenberg",onChange:function(e,t){c(Ke(n({},e,t),"post_type"===e?{include_term_ids:{},exclude_term_ids:{},include_post_ids:{ids:[],current_post:!1},exclude_post_ids:{ids:[],current_post:!1}}:{}))},options:{post_type:{type:"ct-select",label:(0,l.__)("Post Type","blocksy-companion"),value:"",defaultToFirstItem:!1,choices:h,purpose:"default"},limit:{type:"ct-number",label:(0,l.__)("Limit","blocksy-companion"),value:"",min:1,max:100},has_slideshow:{type:"ct-switch",label:(0,l.__)("Slideshow","blocksy-companion"),value:""},has_slideshow_condition:{type:"ct-condition",condition:{has_slideshow:"yes"},options:{has_slideshow_arrows:{type:"ct-switch",label:(0,l.__)("Arrows","blocksy-companion"),value:""},has_slideshow_autoplay:{type:"ct-switch",label:(0,l.__)("Autoplay","blocksy-companion"),value:""},has_slideshow_autoplay_condition:{type:"ct-condition",condition:{has_slideshow_autoplay:"yes"},options:{has_slideshow_autoplay_speed:{type:"ct-number",label:(0,l.__)("Autoplay Speed","blocksy-companion"),value:"",min:1,max:100}}}}},has_pagination_condition:{type:"ct-condition",condition:{has_slideshow:"no"},options:{has_pagination:{type:"ct-switch",label:(0,l.__)("Pagination","blocksy-companion"),value:""}}}},value:a,hasRevertButton:!1}))),(0,r.createElement)(ze,{attributes:a,setAttributes:c,context:s})),(0,r.createElement)(i.InspectorControls,{group:"advanced"},(0,r.createElement)(p.TextControl,{__nextHasNoMarginBottom:!0,autoComplete:"off",label:(0,l.__)("Block ID","blocksy-companion"),value:_,onChange:function(e){},onFocus:function(e){e.target.select()},help:(0,l.__)("Please look at the documentation for more information on why this is useful.","blocksy-companion")})))},Ye=function(e){var t=i.useBlockProps.save(),o=i.useInnerBlocksProps.save(t);return(0,r.createElement)("div",o)},Je=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"blocksy/query","category":"blocksy-blocks","attributes":{"uniqueId":{"type":"string","default":""},"type":{"type":"string","default":"post"},"post_type":{"type":"string","default":"post"},"limit":{"type":"number","default":5},"include_term_ids":{"type":"object","default":{}},"exclude_term_ids":{"type":"object","default":{}},"include_post_ids":{"type":"object","default":{"ids":[],"current_post":false}},"exclude_post_ids":{"type":"object","default":{"ids":[],"current_post":false}},"order":{"type":"string","default":"desc"},"orderby":{"type":"string","default":"post_date"},"orderby_custom":{"type":"string","default":""},"offset":{"type":"number","default":0},"design":{"type":"string","default":""},"has_pagination":{"type":"string","default":"no"},"textColor":{"type":"string"},"customTextColor":{"type":"string"},"linkColor":{"type":"string"},"customLinkColor":{"type":"string"},"linkHoverColor":{"type":"string"},"customLinkHoverColor":{"type":"string"},"sticky_posts":{"type":"string","default":"include"},"has_slideshow":{"type":"string","default":"no"},"has_slideshow_arrows":{"type":"string","default":"yes"},"has_slideshow_autoplay":{"type":"string","default":"no"},"has_slideshow_autoplay_speed":{"type":"number","default":3}},"supports":{"align":["wide","full"],"html":false,"spacing":{"margin":true,"padding":true,"__experimentalDefaultControls":{"margin":false,"padding":false}},"__experimentalBorder":{"color":true,"radius":true,"width":true,"__experimentalDefaultControls":{"color":true,"radius":true,"width":true},"__experimentalSelector":"img, .block-editor-media-placeholder, .wp-block-post-featured-image__overlay","__experimentalSkipSerialization":true},"color":{"gradients":true,"link":false,"__experimentalDefaultControls":{"text":false,"background":true,"link":false}}},"usesContext":["postId","postType"],"providesContext":{"uniqueId":"uniqueId","post_type":"post_type","limit":"limit","order":"order","orderby":"orderby","sticky_posts":"sticky_posts","orderby_custom":"orderby_custom","offset":"offset","has_slideshow":"has_slideshow","has_slideshow_arrows":"has_slideshow_arrows","has_slideshow_autoplay":"has_slideshow_autoplay","has_slideshow_autoplay_speed":"has_slideshow_autoplay_speed","has_pagination":"has_pagination","include_term_ids":"include_term_ids","exclude_term_ids":"exclude_term_ids"}}');function Qe(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function et(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Qe(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Qe(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}(0,a.registerBlockType)("blocksy/query",et(et({},Je),{},{title:(0,l.__)("Advanced Posts","blocksy-companion"),description:(0,l.__)("Create advanced queries based on your specified criterias.","blocksy-companion"),icon:{src:(0,r.createElement)("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",context:"list-view","aria-hidden":"true",focusable:"false"},(0,r.createElement)("path",{d:"M5.5 18v-1c0-.3.2-.5.5-.5h3c.3 0 .5.2.5.5v1c0 .3-.2.5-.5.5H6c-.3 0-.5-.2-.5-.5zm.5 2c-1.1 0-2-.9-2-2v-1c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2v1c0 1.1-.9 2-2 2H6zm-.5-9V6c0-.3.2-.5.5-.5h3c.3 0 .5.2.5.5v5c0 .3-.2.5-.5.5H6c-.3 0-.5-.2-.5-.5zm.5 2c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2v5c0 1.1-.9 2-2 2H6zm8.5 0v5c0 .*******.5h3c.3 0 .5-.2.5-.5v-5c0-.3-.2-.5-.5-.5h-3c-.3 0-.5.2-.5.5zM13 18c0 1.1.9 2 2 2h3c1.1 0 2-.9 2-2v-5c0-1.1-.9-2-2-2h-3c-1.1 0-2 .9-2 2v5zm1.5-11V6c0-.3.2-.5.5-.5h3c.3 0 .5.2.5.5v1c0 .3-.2.5-.5.5h-3c-.3 0-.5-.2-.5-.5zm.5 2c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2v1c0 1.1-.9 2-2 2h-3z",fillRule:"evenodd"}))},edit:function(e){return(0,r.createElement)(Xe,e)},save:function(){return(0,r.createElement)(Ye,null)}}));var tt=function(e){var t=e.attributes,o=u((0,r.useState)(null),2),n=o[0],l=o[1];return(0,r.useEffect)((function(){var e=new URLSearchParams(window.location.search);R("".concat(wp.ajax.settings.url,"?action=blocksy_get_tax_block_data").concat(e.get("lang")?"&lang="+e.get("lang"):""),{attributes:t},{fetcherName:"tax-block-data-".concat(t.uniqueId),headers:{Accept:"application/json","Content-Type":"application/json"},method:"POST"}).then((function(e){return e.json()})).then((function(e){var t=e.success,o=e.data;t&&l(o)})).catch((function(e){}))}),[t]),{blockData:n}},ot=function(e){var t=e.attributes,o=tt({attributes:t}).blockData;return o&&o.block?(0,r.createElement)(React.Fragment,null,(0,r.createElement)(r.RawHTML,null,o.block),o&&o.dynamic_styles&&(0,r.createElement)("style",null,o.dynamic_styles)):(0,r.createElement)(p.Spinner,null)},nt=function(e){var t=e.clientId,o=(e.setAttributes,e.setIsPatternSelectionModalOpen),n=(0,k.useDispatch)(i.store).replaceInnerBlocks;return(0,r.createElement)("div",{className:"components-placeholder is-large"},(0,r.createElement)("div",{className:"components-placeholder__label"},(0,r.createElement)("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,r.createElement)("path",{d:"M5.5 18v-1c0-.3.2-.5.5-.5h3c.3 0 .5.2.5.5v1c0 .3-.2.5-.5.5H6c-.3 0-.5-.2-.5-.5zm.5 2c-1.1 0-2-.9-2-2v-1c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2v1c0 1.1-.9 2-2 2H6zm-.5-9V6c0-.3.2-.5.5-.5h3c.3 0 .5.2.5.5v5c0 .3-.2.5-.5.5H6c-.3 0-.5-.2-.5-.5zm.5 2c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2v5c0 1.1-.9 2-2 2H6zm8.5 0v5c0 .*******.5h3c.3 0 .5-.2.5-.5v-5c0-.3-.2-.5-.5-.5h-3c-.3 0-.5.2-.5.5zM13 18c0 1.1.9 2 2 2h3c1.1 0 2-.9 2-2v-5c0-1.1-.9-2-2-2h-3c-1.1 0-2 .9-2 2v5zm1.5-11V6c0-.3.2-.5.5-.5h3c.3 0 .5.2.5.5v1c0 .3-.2.5-.5.5h-3c-.3 0-.5-.2-.5-.5zm.5 2c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2v1c0 1.1-.9 2-2 2h-3z",fillRule:"evenodd"})),(0,l.__)("Advanced Taxonomies","blocksy-companion")),(0,r.createElement)("fieldset",{className:"components-placeholder__fieldset"},(0,r.createElement)("legend",{className:"components-placeholder__instructions"},(0,l.__)("Start with a pattern or create a custom layout","blocksy-companion")),(0,r.createElement)("button",{className:"components-button is-primary",onClick:function(e){e.preventDefault(),o(!0)}},(0,l.__)("Choose Pattern","blocksy-companion")),(0,r.createElement)("button",{className:"components-button is-primary",onClick:function(e){e.preventDefault(),n(t,(0,a.createBlocksFromInnerBlocksTemplate)([["blocksy/tax-template",{},[["blocksy/dynamic-data",{tagName:"h2",field:"wp:term_title",has_field_link:"yes"}],["blocksy/dynamic-data",{field:"wp:term_description"}]]]]),!1)}},(0,l.__)("Create Custom Layout","blocksy-companion"))))};function rt(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function lt(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?rt(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):rt(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var at=null,it=new AbortController,ct=function(e){var t=e.clientId,o=(e.attributes,e.setIsPatternSelectionModalOpen),n=e.postType,c=u((0,r.useState)(""),2),s=c[0],d=c[1],m=(0,k.useDispatch)(i.store),f=m.replaceBlock,b=m.selectBlock,y=u((0,r.useState)([]),2),h=y[0],v=y[1];(0,r.useEffect)((function(){new Promise((function(e){at?e(at):fetch("".concat(wp.ajax.settings.url,"?action=blocksy_get_terms_block_patterns"),{headers:{Accept:"application/json","Content-Type":"application/json"},method:"POST",body:JSON.stringify({}),signal:it.signal}).then((function(e){return e.json()})).then((function(t){var o=t.success,n=t.data;o&&(at=n.patterns.map((function(e){return lt(lt({},e),{},{blocks:(0,a.parse)(e.content,{__unstableSkipMigrationLogs:!0})})})),e(at))}))})).then((function(e){v(e)}))}),[]);var g=(0,r.useMemo)((function(){return{previewPostType:n}}),[n]),_=(0,r.useMemo)((function(){return We(h,s)}),[h,s]),w=(0,we.useAsyncList)(_);return(0,r.createElement)(p.Modal,{overlayClassName:"block-library-query-pattern__selection-modal",title:(0,l.__)("Choose a pattern","blocksy-companion"),onRequestClose:function(){return o(!1)},isFullScreen:!0},(0,r.createElement)("div",{className:"block-library-query-pattern__selection-content"},(0,r.createElement)("div",{className:"block-library-query-pattern__selection-search"},(0,r.createElement)(p.SearchControl,{__nextHasNoMarginBottom:!0,onChange:d,value:s,label:(0,l.__)("Search for patterns","blocksy-companion"),placeholder:(0,l.__)("Search","blocksy-companion")})),(0,r.createElement)(i.BlockContextProvider,{value:g},(0,r.createElement)(i.__experimentalBlockPatternsList,{blockPatterns:_,shownPatterns:w,onClickPattern:function(e,o){var n=_e(o),r=n.newBlocks,l=n.queryClientIds;f(t,r),l[0]&&b(l[0])}}))))},st=function(e){e.context;var t=e.attributes,o=(e.attributes.post_type,e.setAttributes),a=Ne({attributes:t,setAttributes:o}).taxonomiesGroup;return(0,r.createElement)(i.InspectorControls,null,(0,r.createElement)(ye,{className:"ct-query-parameters-component",attributes:t,setAttributes:o,resetAll:function(){o({level:"all",hide_empty:"yes",offset:0,orderby:"none",order:"desc",include_term_ids:{},exclude_term_ids:{}})},items:[{label:(0,l.__)("General","blocksy-companion"),items:[{label:(0,l.__)("Offset","blocksy-companion"),hasValue:function(){return 0!==t.offset},reset:function(){o({offset:0})},render:function(){return(0,r.createElement)(Z.OptionsPanel,{purpose:"gutenberg",onChange:function(e,t){o(n({},e,t))},options:{offset:{type:"ct-number",label:(0,l.__)("Offset","blocksy-companion"),value:"",min:0,max:500}},value:t,hasRevertButton:!1})}},{label:(0,l.__)("Order by","blocksy-companion"),hasValue:function(){return"none"!==t.orderby},reset:function(){o({orderby:"none"})},render:function(){return(0,r.createElement)(Z.OptionsPanel,{purpose:"gutenberg",onChange:function(e,t){o(n({},e,t))},options:{orderby:{type:"ct-select",label:(0,l.__)("Order by","blocksy-companion"),value:"",choices:[{key:"id",value:(0,l.__)("ID","blocksy-companion")},{key:"name",value:(0,l.__)("Name","blocksy-companion")},{key:"count",value:(0,l.__)("Count","blocksy-companion")},{key:"rand",value:(0,l.__)("Random","blocksy-companion")}]}},value:t,hasRevertButton:!1})}},{label:(0,l.__)("Order","blocksy-companion"),hasValue:function(){return"desc"!==t.order},reset:function(){o({order:"desc"})},render:function(){return(0,r.createElement)(Z.OptionsPanel,{purpose:"gutenberg",onChange:function(e,t){o(n({},e,t))},options:{order:{type:"ct-select",label:(0,l.__)("Order","blocksy-companion"),value:"",choices:[{key:"DESC",value:(0,l.__)("Descending","blocksy-companion")},{key:"ASC",value:(0,l.__)("Ascending","blocksy-companion")}]}},value:t,hasRevertButton:!1})}},{label:(0,l.__)("Level","blocksy-companion"),hasValue:function(){return"all"!==t.level},reset:function(){o({level:"all"})},render:function(){return(0,r.createElement)(Z.OptionsPanel,{purpose:"gutenberg",onChange:function(e,t){o(n({},e,t))},options:{level:{type:"ct-select",label:(0,l.__)("Level","blocksy-companion"),value:"",choices:[{key:"all",value:(0,l.__)("All Taxonomies","blocksy-companion")},{key:"parent",value:(0,l.__)("Only Parent Taxonomies","blocksy-companion")},{key:"relevant",value:(0,l.__)("Relevant Taxonomies","blocksy-companion")}]}},value:t,hasRevertButton:!1})}},{label:(0,l.__)("Taxonomies Visibility","blocksy-companion"),hasValue:function(){return"no"===t.hide_empty},reset:function(){o({hide_empty:"yes"})},render:function(){return(0,r.createElement)(p.__experimentalToggleGroupControl,{label:(0,l.__)("Hide Empty Taxonomies","blocksy-companion"),value:t.hide_empty,isBlock:!0,onChange:function(e){o({hide_empty:e})}},(0,r.createElement)(p.__experimentalToggleGroupControlOption,{value:"no",label:(0,l.__)("No","blocksy-companion")}),(0,r.createElement)(p.__experimentalToggleGroupControlOption,{value:"yes",label:(0,l.__)("Yes","blocksy-companion")}))}}]}].concat(O(a?[a]:[])),label:(0,l.__)("Parameters","blocksy-companion")}))};function ut(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function pt(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?ut(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):ut(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var dt=function(e){var t=e.clientId,o=e.className,a=e.attributes,c=e.setAttributes,s=e.context,d=(0,i.useInnerBlocksProps)({},{}),m=(0,k.useSelect)((function(e){return!!e(i.store).getBlocks(t).length}),[t]),f=!m&&"default"!==a.design,b=(ke()||[]).reduce((function(e,t){return e[t.slug]=t.name,e}),{}),y=ie({attributes:a,setAttributes:c,clientId:t,blockType:"blocksy/tax-query"}),h=y.uniqueId,v=y.props,_=s.postId,w=(0,r.useRef)(),O=(0,i.__experimentalUseBorderProps)(a),x=(0,i.useBlockProps)({ref:w,className:o,style:pt({},O.style)}),C=u((0,r.useState)(!1),2),E=C[0],P=C[1];return(0,r.createElement)(React.Fragment,null,E&&(0,r.createElement)(ct,{clientId:t,attributes:a,setIsPatternSelectionModalOpen:P,postType:a.taxonomy}),f?(0,r.createElement)("div",g({},x,v),(0,r.createElement)(nt,{setIsPatternSelectionModalOpen:P,attributes:a,setAttributes:c,clientId:t})):(0,r.createElement)("div",g({},x,v),"default"===a.design&&(0,r.createElement)(ot,{uniqueId:h,attributes:a,postId:_}),m&&(0,r.createElement)("div",d)),(0,r.createElement)(i.BlockControls,null,(0,r.createElement)(p.ToolbarGroup,null,(0,r.createElement)(p.ToolbarButton,{className:"components-toolbar__control",icon:"layout",label:(0,l.__)("Reset layout","blocksy-companion"),disabled:f,onClick:function(){m?(0,k.dispatch)("core/block-editor").replaceInnerBlocks(t,[],!1):(0,k.dispatch)("core/block-editor").updateBlockAttributes(t,{design:""})}}))),!f&&(0,r.createElement)(React.Fragment,null,(0,r.createElement)(i.InspectorControls,null,(0,r.createElement)(p.PanelBody,null,(0,r.createElement)(Z.OptionsPanel,{purpose:"gutenberg",onChange:function(e,t){c(n({},e,t))},options:{taxonomy:{type:"ct-select",label:(0,l.__)("Taxonomy","blocksy-companion"),value:"",defaultToFirstItem:!1,choices:b,purpose:"default"},limit:{type:"ct-number",label:(0,l.__)("Limit","blocksy-companion"),value:"",min:1,max:200},has_slideshow:{type:"ct-switch",label:(0,l.__)("Slideshow","blocksy-companion"),value:""},has_slideshow_condition:{type:"ct-condition",condition:{has_slideshow:"yes"},options:{has_slideshow_arrows:{type:"ct-switch",label:(0,l.__)("Arrows","blocksy-companion"),value:""},has_slideshow_autoplay:{type:"ct-switch",label:(0,l.__)("Autoplay","blocksy-companion"),value:""},has_slideshow_autoplay_condition:{type:"ct-condition",condition:{has_slideshow_autoplay:"yes"},options:{has_slideshow_autoplay_speed:{type:"ct-number",label:(0,l.__)("Autoplay Speed","blocksy-companion"),value:"",min:1,max:100}}}}}},value:a,hasRevertButton:!1}))),(0,r.createElement)(st,{attributes:a,setAttributes:c,context:s})),(0,r.createElement)(i.InspectorControls,{group:"advanced"},(0,r.createElement)(p.TextControl,{__nextHasNoMarginBottom:!0,autoComplete:"off",label:(0,l.__)("Block ID","blocksy-companion"),value:h,onChange:function(e){},onFocus:function(e){e.target.select()},help:(0,l.__)("Please look at the documentation for more information on why this is useful.","blocksy-companion")})))},mt=function(e){var t=i.useBlockProps.save(),o=i.useInnerBlocksProps.save(t);return(0,r.createElement)("div",o)},ft=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"blocksy/tax-query","category":"blocksy-blocks","attributes":{"uniqueId":{"type":"string","default":""},"taxonomy":{"type":"string","default":"category"},"level":{"type":"string","default":"all"},"limit":{"type":"number","default":5},"hide_empty":{"type":"string","default":"yes"},"include_term_ids":{"type":"object","default":{}},"exclude_term_ids":{"type":"object","default":{}},"order":{"type":"string","default":"desc"},"orderby":{"type":"string","default":"none"},"offset":{"type":"number","default":0},"design":{"type":"string","default":""},"textColor":{"type":"string"},"customTextColor":{"type":"string"},"linkColor":{"type":"string"},"customLinkColor":{"type":"string"},"linkHoverColor":{"type":"string"},"customLinkHoverColor":{"type":"string"},"has_slideshow":{"type":"string","default":"no"},"has_slideshow_arrows":{"type":"string","default":"yes"},"has_slideshow_autoplay":{"type":"string","default":"no"},"has_slideshow_autoplay_speed":{"type":"number","default":3}},"supports":{"align":["wide","full"],"html":false,"spacing":{"margin":true,"padding":true,"__experimentalDefaultControls":{"margin":false,"padding":false}},"__experimentalBorder":{"color":true,"radius":true,"width":true,"__experimentalDefaultControls":{"color":true,"radius":true,"width":true},"__experimentalSelector":"img, .block-editor-media-placeholder, .wp-block-post-featured-image__overlay","__experimentalSkipSerialization":true},"color":{"gradients":true,"link":false,"__experimentalDefaultControls":{"text":false,"background":true,"link":false}}},"usesContext":["postId","postType"],"providesContext":{"uniqueId":"uniqueId","taxonomy":"taxonomy","level":"level","limit":"limit","hide_empty":"hide_empty","order":"order","orderby":"orderby","offset":"offset","include_term_ids":"include_term_ids","exclude_term_ids":"exclude_term_ids","has_slideshow":"has_slideshow","has_slideshow_arrows":"has_slideshow_arrows","has_slideshow_autoplay":"has_slideshow_autoplay","has_slideshow_autoplay_speed":"has_slideshow_autoplay_speed"}}');function bt(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function yt(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?bt(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):bt(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}(0,a.registerBlockType)("blocksy/tax-query",yt(yt({},ft),{},{title:(0,l.__)("Advanced Taxonomies","blocksy-companion"),description:(0,l.__)("Create advanced queries based on your specified criterias.","blocksy-companion"),icon:{src:(0,r.createElement)("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",context:"list-view","aria-hidden":"true",focusable:"false"},(0,r.createElement)("path",{d:"M5.5 18v-1c0-.3.2-.5.5-.5h3c.3 0 .5.2.5.5v1c0 .3-.2.5-.5.5H6c-.3 0-.5-.2-.5-.5zm.5 2c-1.1 0-2-.9-2-2v-1c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2v1c0 1.1-.9 2-2 2H6zm-.5-9V6c0-.3.2-.5.5-.5h3c.3 0 .5.2.5.5v5c0 .3-.2.5-.5.5H6c-.3 0-.5-.2-.5-.5zm.5 2c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2v5c0 1.1-.9 2-2 2H6zm8.5 0v5c0 .*******.5h3c.3 0 .5-.2.5-.5v-5c0-.3-.2-.5-.5-.5h-3c-.3 0-.5.2-.5.5zM13 18c0 1.1.9 2 2 2h3c1.1 0 2-.9 2-2v-5c0-1.1-.9-2-2-2h-3c-1.1 0-2 .9-2 2v5zm1.5-11V6c0-.3.2-.5.5-.5h3c.3 0 .5.2.5.5v1c0 .3-.2.5-.5.5h-3c-.3 0-.5-.2-.5-.5zm.5 2c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2h3c1.1 0 2 .9 2 2v1c0 1.1-.9 2-2 2h-3z",fillRule:"evenodd"}))},edit:function(e){return(0,r.createElement)(dt,e)},save:function(){return(0,r.createElement)(mt,null)},deprecated:[{isEligible:function(e){return"product_brands"===e.taxonomy},migrate:function(){return{taxonomy:"product_brand"}}}]}));var ht=(0,se.createElement)(ue.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,se.createElement)(ue.Path,{d:"M4 4v1.5h16V4H4zm8 8.5h8V11h-8v1.5zM4 20h16v-1.5H4V20zm4-8c0-1.1-.9-2-2-2s-2 .9-2 2 .9 2 2 2 2-.9 2-2z"}));var vt=(0,se.createElement)(ue.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,se.createElement)(ue.Path,{d:"m3 5c0-1.10457.89543-2 2-2h13.5c1.1046 0 2 .89543 2 2v13.5c0 1.1046-.8954 2-2 2h-13.5c-1.10457 0-2-.8954-2-2zm2-.5h6v6.5h-6.5v-6c0-.27614.22386-.5.5-.5zm-.5 8v6c0 .2761.22386.5.5.5h6v-6.5zm8 0v6.5h6c.2761 0 .5-.2239.5-.5v-6zm0-8v6.5h6.5v-6c0-.27614-.2239-.5-.5-.5z",fillRule:"evenodd",clipRule:"evenodd"})),gt=function(e){var t=e.label,o=e.onChange,n=e.value;return(0,r.createElement)(p.BaseControl,{label:t},(0,r.createElement)(p.Flex,{gap:4},(0,r.createElement)(p.FlexItem,{isBlock:!0},(0,r.createElement)(p.__experimentalNumberControl,{size:"__unstable-large",onChange:function(e){return o(+e)},value:n,min:1,label:t,hideLabelFromVision:!0})),(0,r.createElement)(p.FlexItem,{isBlock:!0},(0,r.createElement)(p.RangeControl,{value:parseInt(n,10),onChange:function(e){return o(+e)},min:1,max:6,withInputField:!1,label:t,hideLabelFromVision:!0}))))};function _t(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function kt(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?_t(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):_t(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var wt=[];function Ot(e){var t=e.isSlideshow,o=e.elementDescriptor,n=(0,i.useInnerBlocksProps)({className:"wp-block-post is-layout-flow"},{template:wt,__unstableDisableLayoutClassNames:!0});if(t){var l=(null==o?void 0:o.attr)||{},a=l.className,c=x(l,["className"]);return(0,r.createElement)("div",g({className:ee()("flexy-item",a)},c),(0,r.createElement)("article",n))}return(0,r.createElement)("article",n)}var xt=(0,r.memo)((function(e){var t=e.blocks,o=e.blockContextId,n=e.isHidden,l=e.isSlideshow,a=e.elementDescriptor,c=e.setActiveBlockContextId,s=(0,i.__experimentalUseBlockPreview)({blocks:t,props:{className:"wp-block-post is-layout-flow"}}),u=function(){c(o)};if(n)return null;if(l){var p=(null==a?void 0:a.attr)||{},d=p.className,m=x(p,["className"]);return(0,r.createElement)("div",g({className:ee()("flexy-item",d)},m),(0,r.createElement)("article",g({tabIndex:0},s,{role:"button",onClick:u,onKeyPress:u})))}return(0,r.createElement)("article",g({},s,{tabIndex:0,role:"button",onClick:u,onKeyPress:u}))})),Ct=function(e){var t=e.has_slideshow_arrows,o=e.sliderDescriptor;return t?(0,r.createElement)(React.Fragment,null,(0,r.createElement)("span",g({class:"flexy-arrow-prev"},o.arrowsDescritor.left),(0,r.createElement)("svg",{width:"16",height:"10",fill:"currentColor",viewBox:"0 0 16 10"},(0,r.createElement)("path",{d:"M15.3 4.3h-13l2.8-3c.3-.3.3-.7 0-1-.3-.3-.6-.3-.9 0l-4 4.2-.2.2v.6c0 .*******.2l4 4.2c.*******.9 0 .3-.3.3-.7 0-1l-2.8-3h13c.2 0 .4-.1.5-.2s.2-.3.2-.5-.1-.4-.2-.5c-.1-.1-.3-.2-.5-.2z"}))),(0,r.createElement)("span",g({class:"flexy-arrow-next"},o.arrowsDescritor.right),(0,r.createElement)("svg",{width:"16",height:"10",fill:"currentColor",viewBox:"0 0 16 10"},(0,r.createElement)("path",{d:"M.2 4.5c-.1.1-.2.3-.2.5s.1.4.2.5c.*******.5.2h13l-2.8 3c-.3.3-.3.7 0 1 .*******.9 0l4-4.2.2-.2V5v-.3c0-.1-.1-.2-.2-.2l-4-4.2c-.3-.4-.6-.4-.9 0-.3.3-.3.7 0 1l2.8 3H.7c-.2 0-.4.1-.5.2z"})))):null},Et=function(e){var t=e.clientId,o=(e.className,e.attributes),n=e.attributes,a=n.layout,c=n.verticalAlignment,s=e.setAttributes,d=e.context,m=e.__unstableLayoutClassNames,f=d.postId,b=d.has_slideshow,y=d.has_slideshow_arrows,h=d.uniqueId,v=u((0,r.useState)(),2),_=v[0],w=v[1],O=a||{},x=O.type,C=O.columnCount,E=void 0===C?3:C,P="grid"===x,S=null!==E,j="yes"===b,I=J(function(e){var t=e.attributes,o=e.context,n=t.style,r=t.layout,l=t.verticalAlignment,a=o.has_slideshow,c=o.uniqueId,s=r||{},u=s.type,p=s.columnCount,d=void 0===p?3:p,m="grid"===u,f=null!==d,b="yes"===a,y="";n&&n.spacing&&n.spacing.blockGap&&(y=(0,i.getSpacingPresetCssVar)(n.spacing.blockGap)),y||(y="CT_CSS_SKIP_RULE");var h=[],v={desktop:1,tablet:1,mobile:1};if(m&&l&&h.push({variables:{selector:"[data-id='".concat(c,"']"),variable:"align-items"},value:"top"===l?"flex-start":"bottom"===l?"flex-end":"center"}),m&&l&&h.push({variables:{selector:"[data-id='".concat(c,"']"),variable:"align-items"},value:"top"===l?"flex-start":"bottom"===l?"flex-end":"center"}),m&&f){v={desktop:d,tablet:t.tabletColumns,mobile:t.mobileColumns},h.push({variables:{selector:"[data-id='".concat(c,"']"),variable:"grid-columns-gap",unit:""},value:y});var g={desktop:d,tablet:t.tabletColumns,mobile:t.mobileColumns};b&&(g={desktop:"".concat((100/d).toFixed(2),"%"),tablet:"".concat((100/t.tabletColumns).toFixed(2),"%"),mobile:"".concat((100/t.mobileColumns).toFixed(2),"%")}),h.push({variables:{selector:"[data-id='".concat(c,"']"),variable:"grid-columns-width",unit:"",responsive:!0},value:g})}else b||h.push({variables:{selector:".editor-styles-wrapper [data-id='".concat(c,"'] :where(.is-layout-flow > *)"),variable:"margin-block-end",unit:"",responsive:!0},value:y});if(b){var _={desktop:"",tablet:"",mobile:""};Object.keys(_).forEach((function(e){_[e]=["[data-id='".concat(c,"']"),'[data-flexy="no"]',".flexy-item:nth-child(n + ".concat(parseFloat(v[e])+1,")")].join(" ")})),h.push({variables:{selector:_,variable:"height",responsive:!0},value:"1px"})}return h}({attributes:o,context:d})),T=(0,i.useBlockProps)({className:ee()(m,{"ct-query-template-grid":P&&!j,"ct-query-template-default":!P&&!j,"is-layout-flow":!P&&!j,"ct-query-template":j,"is-layout-slider":j}),"data-id":h}),D=W({attributes:d,previewedPostId:f}).blockData,A=$({isSlideshow:j,context:d,attributes:o,toWatch:D?D.all_posts:{}}),B=(0,k.useSelect)((function(e){return{blocks:(0,e(i.store).getBlocks)(t)}}),[t]).blocks;if(!D)return(0,r.createElement)("p",T,(0,r.createElement)(p.Spinner,null));var H=D.all_posts.map((function(e){return{postId:e.ID,postType:e.post_type}})),N=[{icon:ht,title:(0,l.__)("List view"),onClick:function(){s({layout:{type:"default"},tabletColumns:2,mobileColumns:1})},isActive:!P},{icon:vt,title:(0,l.__)("Grid view"),onClick:function(){s({layout:{type:"grid",columnCount:3},tabletColumns:2,mobileColumns:1})},isActive:P}],z=function(e){var t=e.isSlideshow,o=e.elementDescriptorForIndex;return(0,r.createElement)(React.Fragment,null,0===H.length&&(0,r.createElement)("p",null,(0,l.__)("No results found.","blocksy-companion")),H.length>0&&H.map((function(e,n){var l,a;return(0,r.createElement)(i.BlockContextProvider,{key:e.postId,value:e},e.postId===(_||(null===(l=H[0])||void 0===l?void 0:l.postId))?(0,r.createElement)(Ot,{isSlideshow:t,elementDescriptor:o?o(n):null}):null,(0,r.createElement)(xt,{blocks:B,blockContextId:e.postId,setActiveBlockContextId:w,isSlideshow:t,elementDescriptor:o?o(n):null,isHidden:e.postId===(_||(null===(a=H[0])||void 0===a?void 0:a.postId))}))})))};return(0,r.createElement)(React.Fragment,null,(0,r.createElement)(i.BlockControls,null,(0,r.createElement)(p.ToolbarGroup,{controls:N}),P?(0,r.createElement)(i.BlockVerticalAlignmentToolbar,{onChange:function(e){s({verticalAlignment:e})},value:c}):null),(0,r.createElement)(i.InspectorControls,null,P&&S?(0,r.createElement)(p.PanelBody,{title:"Layout",initialOpen:!0},(0,r.createElement)(gt,{label:(0,l.__)("Desktop Columns","blocksy-companion"),onChange:function(e){return s({layout:kt(kt({},a),{},{columnCount:e})})},value:E}),(0,r.createElement)(gt,{label:(0,l.__)("Tablet Columns","blocksy-companion"),onChange:function(e){return s({tabletColumns:e})},value:null==o?void 0:o.tabletColumns}),(0,r.createElement)(gt,{label:(0,l.__)("Mobile Columns","blocksy-companion"),onChange:function(e){return s({mobileColumns:e})},value:null==o?void 0:o.mobileColumns})):null),(0,r.createElement)("div",T,j?(0,r.createElement)("div",g({class:"flexy-container","data-flexy":"no"},A.flexyContainerAttr),(0,r.createElement)("div",{class:"flexy"},(0,r.createElement)("div",{class:"flexy-view","data-flexy-view":"boxed"},(0,r.createElement)("div",{class:"flexy-items"},z({isSlideshow:j,elementDescriptorForIndex:A.elementDescriptorForIndex}))),(0,r.createElement)(Ct,{has_slideshow_arrows:"yes"===y,sliderDescriptor:A}))):z({isSlideshow:j})),D&&"yes"===d.has_pagination&&!j&&(0,r.createElement)(r.RawHTML,null,D.pagination_output),I||null)},Pt=function(){return(0,r.createElement)(i.InnerBlocks.Content,null)},St=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"blocksy/post-template","parent":["blocksy/query"],"title":"Post Template","category":"theme","description":"Contains the block elements used to render a post, like the title, date, featured image, content or excerpt, and more.","textdomain":"default","supports":{"reusable":false,"html":false,"layout":false,"spacing":{"blockGap":{"__experimentalDefault":"1.25em"},"__experimentalDefaultControls":{"blockGap":true}}},"attributes":{"tabletColumns":{"type":"number","default":2},"mobileColumns":{"type":"number","default":1},"verticalAlignment":{"type":"string"},"layout":{"type":"object"}},"usesContext":["uniqueId","post_type","limit","order","orderby","orderby_custom","offset","postId","postType","has_slideshow","has_slideshow_arrows","has_slideshow_autoplay","has_slideshow_autoplay_speed","has_pagination","include_term_ids","exclude_term_ids","sticky_posts"]}');function jt(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function It(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?jt(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):jt(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}(0,a.registerBlockType)("blocksy/post-template",It(It({},St),{},{title:(0,l.__)("Post Template","blocksy-companion"),description:(0,l.__)("Post Template","blocksy-companion"),icon:{src:(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",context:"list-view","aria-hidden":"true",focusable:"false"},(0,r.createElement)("path",{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"}))},edit:function(e){return(0,r.createElement)(Et,e)},save:Pt}));function Tt(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Dt(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Tt(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Tt(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var At=[];function Bt(e){var t=e.isSlideshow,o=(e.termId,e.elementDescriptor),n=(0,i.useInnerBlocksProps)({className:ee()("wp-block-term is-layout-flow",[])},{template:At,__unstableDisableLayoutClassNames:!0});if(t){var l=(null==o?void 0:o.attr)||{},a=l.className,c=x(l,["className"]);return(0,r.createElement)("div",g({className:ee()("flexy-item",a)},c),(0,r.createElement)("article",n))}return(0,r.createElement)("div",n)}var Ht=(0,r.memo)((function(e){var t=e.blocks,o=e.blockContextId,n=e.isHidden,l=e.isSlideshow,a=e.elementDescriptor,c=e.setActiveBlockContextId,s=(e.termId,(0,i.__experimentalUseBlockPreview)({blocks:t,props:{className:ee()("wp-block-term is-layout-flow",[])}})),u=function(){c(o)};if(n)return null;if(l){var p=(null==a?void 0:a.attr)||{},d=p.className,m=x(p,["className"]);return(0,r.createElement)("div",g({className:ee()("flexy-item",d)},m),(0,r.createElement)("div",g({tabIndex:0},s,{role:"button",onClick:u,onKeyPress:u})))}return(0,r.createElement)("div",g({},s,{tabIndex:0,role:"button",onClick:u,onKeyPress:u}))})),Nt=function(e){var t=e.clientId,o=e.attributes,n=o.layout,a=o.verticalAlignment,c=(o.style,e.attributes),s=e.setAttributes,d=e.context,m=e.__unstableLayoutClassNames,f=d.postId,b=d.has_slideshow,y=d.has_slideshow_arrows,h=d.uniqueId,v=u((0,r.useState)(),2),_=v[0],w=v[1],O=n||{},x=O.type,C=O.columnCount,E=void 0===C?3:C,P="grid"===x,S="yes"===b,j=J(function(e){var t=e.attributes,o=e.context,n=t.style,r=t.layout,l=o.has_slideshow,a=o.uniqueId,c=r||{},s=c.type,u=c.columnCount,p=void 0===u?3:u,d="grid"===s,m=null!==p,f="yes"===l,b="";n&&n.spacing&&n.spacing.blockGap&&(b=(0,i.getSpacingPresetCssVar)(n.spacing.blockGap)),b||(b="CT_CSS_SKIP_RULE");var y=[],h={desktop:1,tablet:1,mobile:1};if(d&&m){h={desktop:p,tablet:t.tabletColumns,mobile:t.mobileColumns},y.push({variables:{selector:"[data-id='".concat(a,"']"),variable:"grid-columns-gap",unit:""},value:b});var v={desktop:p,tablet:t.tabletColumns,mobile:t.mobileColumns};f&&(v={desktop:"".concat((100/p).toFixed(2),"%"),tablet:"".concat((100/t.tabletColumns).toFixed(2),"%"),mobile:"".concat((100/t.mobileColumns).toFixed(2),"%")}),y.push({variables:{selector:"[data-id='".concat(a,"']"),variable:"grid-columns-width",unit:"",responsive:!0},value:v})}else f||y.push({variables:{selector:".editor-styles-wrapper [data-id='".concat(a,"'] :where(.is-layout-flow > *)"),variable:"margin-block-end",unit:""},value:b});if(f){var g={desktop:"",tablet:"",mobile:""};Object.keys(g).forEach((function(e){g[e]=["[data-id='".concat(a,"']"),'[data-flexy="no"]',".flexy-item:nth-child(n + ".concat(parseFloat(h[e])+1,")")].join(" ")})),y.push({variables:{selector:g,variable:"height",responsive:!0},value:"1px"})}return y}({attributes:c,context:d})),I=(0,i.useBlockProps)({className:ee()(m,{"ct-query-template-grid":P&&!S,"ct-query-template-default":!P&&!S,"is-layout-flow":!P&&!S,"ct-query-template":S,"is-layout-slider":S}),"data-id":h}),T=tt({attributes:d,previewedPostId:f}).blockData,D=$({isSlideshow:S,attributes:c,context:d,toWatch:T?T.all_terms:{}}),A=(0,k.useSelect)((function(e){return{blocks:(0,e(i.store).getBlocks)(t)}}),[t]).blocks;if(!T)return(0,r.createElement)("p",I,(0,r.createElement)(p.Spinner,null));var B=T.all_terms.map((function(e){return{termId:e.term_id,termIcon:null==e?void 0:e.icon,termImage:null==e?void 0:e.image}})),H=[{icon:ht,title:(0,l.__)("List view"),onClick:function(){s({layout:{type:"default"},tabletColumns:2,mobileColumns:1})},isActive:!P},{icon:vt,title:(0,l.__)("Grid view"),onClick:function(){s({layout:{type:"grid",columnCount:3},tabletColumns:2,mobileColumns:1})},isActive:P}],N=function(e){var t=e.isSlideshow,o=e.elementDescriptorForIndex;return(0,r.createElement)(React.Fragment,null,0===B.length&&(0,r.createElement)("p",null,(0,l.__)("No results found.","blocksy-companion")),B.length>0&&B.map((function(e,n){var l,a;return(0,r.createElement)(i.BlockContextProvider,{key:e.termId,value:e},e.termId===(_||(null===(l=B[0])||void 0===l?void 0:l.termId))?(0,r.createElement)(Bt,{isSlideshow:t,termId:e.termId,elementDescriptor:o?o(n):null}):null,(0,r.createElement)(Ht,{blocks:A,blockContextId:e.termId,setActiveBlockContextId:w,isSlideshow:t,termId:e.termId,elementDescriptor:o?o(n):null,isHidden:e.termId===(_||(null===(a=B[0])||void 0===a?void 0:a.termId))}))})))};return(0,r.createElement)(React.Fragment,null,(0,r.createElement)(i.BlockControls,null,(0,r.createElement)(p.ToolbarGroup,{controls:H}),P?(0,r.createElement)(i.BlockVerticalAlignmentToolbar,{onChange:function(e){s({verticalAlignment:e})},value:a}):null),(0,r.createElement)(i.InspectorControls,null,P?(0,r.createElement)(p.PanelBody,{title:"Layout",initialOpen:!0},(0,r.createElement)(gt,{label:(0,l.__)("Desktop Columns","blocksy-companion"),onChange:function(e){return s({layout:Dt(Dt({},n),{},{columnCount:e})})},value:E}),(0,r.createElement)(gt,{label:(0,l.__)("Tablet Columns","blocksy-companion"),onChange:function(e){return s({tabletColumns:e})},value:null==c?void 0:c.tabletColumns}),(0,r.createElement)(gt,{label:(0,l.__)("Mobile Columns","blocksy-companion"),onChange:function(e){return s({mobileColumns:e})},value:null==c?void 0:c.mobileColumns})):null),(0,r.createElement)("div",I,S?(0,r.createElement)("div",g({class:"flexy-container","data-flexy":"no"},D.flexyContainerAttr),(0,r.createElement)("div",{class:"flexy"},(0,r.createElement)("div",{class:"flexy-view","data-flexy-view":"boxed"},(0,r.createElement)("div",{class:"flexy-items"},N({isSlideshow:S,elementDescriptorForIndex:D.elementDescriptorForIndex}))),(0,r.createElement)(Ct,{has_slideshow_arrows:"yes"===y,sliderDescriptor:D}))):N({isSlideshow:S})),j||null)},zt=function(){return(0,r.createElement)(i.InnerBlocks.Content,null)},Mt=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"blocksy/tax-template","parent":["blocksy/tax-query"],"title":"Tax Template","category":"theme","description":"Contains the block elements used to render a taxonomy, like the title, date, featured image, content or excerpt, and more.","textdomain":"default","supports":{"reusable":false,"html":false,"layout":false,"spacing":{"blockGap":{"__experimentalDefault":"1.25em"},"__experimentalDefaultControls":{"blockGap":true}}},"attributes":{"tabletColumns":{"type":"number","default":2},"mobileColumns":{"type":"number","default":1},"verticalAlignment":{"type":"string"},"layout":{"type":"object"}},"usesContext":["uniqueId","taxonomy","level","limit","hide_empty","order","orderby","offset","include_term_ids","exclude_term_ids","has_slideshow","has_slideshow_arrows","has_slideshow_autoplay","has_slideshow_autoplay_speed"]}');function Ft(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Rt(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Ft(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Ft(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function Vt(e,t,o,n,r,l,a){try{var i=e[l](a),c=i.value}catch(e){return void o(e)}i.done?t(c):Promise.resolve(c).then(n,r)}function Lt(e){return function(){var t=this,o=arguments;return new Promise((function(n,r){var l=e.apply(t,o);function a(e){Vt(l,n,r,a,i,"next",e)}function i(e){Vt(l,n,r,a,i,"throw",e)}a(void 0)}))}}(0,a.registerBlockType)("blocksy/tax-template",Rt(Rt({},Mt),{},{title:(0,l.__)("Taxonomy Template","blocksy-companion"),description:(0,l.__)("Taxonomy Template","blocksy-companion"),icon:{src:(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",context:"list-view","aria-hidden":"true",focusable:"false"},(0,r.createElement)("path",{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"}))},edit:function(e){return(0,r.createElement)(Nt,e)},save:zt}));var Ut=o(687),qt=o.n(Ut),Gt={},Wt=function(e,t,o){var n=u((0,r.useState)([]),2),l=n[0],a=n[1],i=u((0,r.useState)([]),2),c=i[0],s=i[1],p=(0,r.useCallback)(Lt(qt().mark((function n(){var r,i,c;return qt().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:r=new FormData,i=JSON.stringify(t),r.append("action","blocksy_get_dynamic_block_view"),r.append("block",e),r.append("attributes",i),Gt[i]?(a(o(Gt[i])),s(!1)):(l||s(!0),fetch((null===(c=window.ct_localizations||ct_customizer_localizations)||void 0===c?void 0:c.ajax_url)||wp.ajax.settings.url,{method:"POST",body:r}).then((function(e){return e.json()})).then((function(e){var t=e.data.content;a(o(t)),s(!1),Gt[i]=t})));case 6:case"end":return n.stop()}}),n)}))),[t,l]);return(0,r.useEffect)((function(){p()}),[t]),{isLoading:c,preview:l}},Zt=function(e){return Object.keys(e).reduce((function(t,o){return t[o]={type:"string",default:e[o]},t}),{})},$t={textColor:"",customTextColor:"",textHoverColor:"",customTextHoverColor:"",iconsColor:"",customIconsColor:"",iconsHoverColor:"",customIconsHoverColor:"",borderColor:"rgba(218, 222, 228, 0.5)",customBorderColor:"rgba(218, 222, 228, 0.5)",borderHoverColor:"rgba(218, 222, 228, 0.7)",customBorderHoverColor:"rgba(218, 222, 228, 0.7)",backgroundColor:"rgba(218, 222, 228, 0.5)",customBackgroundColor:"rgba(218, 222, 228, 0.5)",backgroundHoverColor:"rgba(218, 222, 228, 0.7)",customBackgroundHoverColor:"rgba(218, 222, 228, 0.7)"},Kt=Zt($t);function Xt(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Yt(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Xt(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Xt(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var Jt=Yt({about_alignment:"center",avatar_shape:"round",about_avatar_size:"small",post_widget_thumb_size:"default",about_social_type:"rounded",about_social_icons_fill:"outline",about_social_icons_color:"official",about_text:"Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua tincidunt tortor aliquam.",about_name:"John Doe",about_items_spacing:"",about_social_icons_size:""},$t),Qt=function(e){var t=e.attributes,o=e.setAttributes,n=(0,r.useRef)({image:"",socials:""}),l=t.about_name,a=t.about_text,c=t.about_source,s=t.about_alignment,u=t.avatar_shape,d=t.about_avatar_size,m=t.about_social_type,f=t.about_social_icons_fill,b=t.about_items_spacing,y=t.about_social_icons_size,h=t.about_social_icons_color,v=(0,r.useCallback)((function(e){var t=document.createElement("div");t.innerHTML=e;var o=t.querySelector(".ct-about-me-block");if(o){o.querySelector("div").dataset.alignment=s;var r=o.querySelector(".ct-media-container");r&&(r.dataset.shape=u,r.dataset.size=d);var l,a=o.querySelector(".ct-social-box");if(a&&(a.dataset.iconsType="simple"===m?m:"".concat(m,":").concat(f),a.dataset.color=h),"custom"===c)n.current={image:(null===(l=o.querySelector(".ct-media-container"))||void 0===l?void 0:l.outerHTML)||"",socials:(null==a?void 0:a.outerHTML)||""}}return o.innerHTML}),[s,u,c,d,m,f,y,b,h]),g=Wt("about-me",Yt(Yt({},t),Jt),v),_=g.isLoading,k=g.preview;return _?(0,r.createElement)(p.Spinner,null):"from_wp"===c?(0,r.createElement)(r.RawHTML,null,k):(0,r.createElement)("div",{"data-alignment":s},(0,r.createElement)(r.RawHTML,null,n.current.image),(0,r.createElement)("div",{className:"ct-about-me-name"},(0,r.createElement)(i.RichText,{tagName:"span",value:l,placeholder:"User Name",onChange:function(e){return o({about_name:e})}})),(0,r.createElement)(i.RichText,{tagName:"div",className:"ct-about-me-text",value:a,placeholder:"User Description",onChange:function(e){return o({about_text:e})}}),(0,r.createElement)(r.RawHTML,null,n.current.socials))},eo=function(e){var t=e.attributes,o=e.setAttributes,l=e.options;return(0,r.createElement)(i.InspectorControls,null,(0,r.createElement)(p.PanelBody,null,(0,r.createElement)(Z.OptionsPanel,{purpose:"gutenberg",onChange:function(e,t){o(n({},e,t))},options:l,value:t,hasRevertButton:!1})))};function to(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function oo(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?to(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):to(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var no=(0,i.withColors)({textColor:"color"},{textHoverColor:"color"},{iconsColor:"color"},{iconsHoverColor:"color"},{backgroundColor:"color"},{backgroundHoverColor:"color"},{borderColor:"color"},{borderHoverColor:"color"})((function(e){var t=e.attributes,o=e.setAttributes,n=e.clientId,a=e.className,c=e.textColor,s=e.setTextColor,u=e.textHoverColor,p=e.setTextHoverColor,d=e.iconsColor,m=e.setIconsColor,f=e.iconsHoverColor,b=e.setIconsHoverColor,y=e.backgroundColor,h=e.setBackgroundColor,v=e.backgroundHoverColor,g=e.setBackgroundHoverColor,k=e.borderColor,w=e.setBorderColor,O=e.borderHoverColor,x=e.setBorderHoverColor,C=t.about_alignment,E=void 0===C?"center":C,P=t.about_items_spacing,S=void 0===P?"":P,j=t.about_social_icons_size,I=void 0===j?"":j,T=t.about_social_type,D=void 0===T?"simple":T,A=t.about_social_icons_fill,B=void 0===A?"outline":A,H=t.about_social_icons_color,N=void 0===H?"default":H,z=(0,r.useRef)(),M=(0,i.useBlockProps)({ref:z,className:{"ct-about-me-block":!0,className:a},"data-alignment":E,style:oo(oo({"--theme-block-text-color":null==c?void 0:c.color,"--theme-link-hover-color":null==c?void 0:c.color,"--theme-icon-color":null==d?void 0:d.color,"--theme-icon-hover-color":null==f?void 0:f.color,"--background-color":"solid"===B?null==y?void 0:y.color:null==k?void 0:k.color,"--background-hover-color":"solid"===B?null==v?void 0:v.color:null==O?void 0:O.color},I?{"--theme-icon-size":"".concat(I,"px")}:{}),S?{"--items-spacing":"".concat(S,"px")}:{})});return(0,r.createElement)("div",M,(0,r.createElement)(Qt,{attributes:t,setAttributes:o}),(0,r.createElement)(eo,{attributes:t,setAttributes:o,options:ao}),(0,r.createElement)(i.InspectorControls,{group:"styles"},(0,r.createElement)(_,{label:(0,l.__)("Text Color","blocksy-companion"),resetAll:function(){s($t.textColor),p($t.textHoverColor)},panelId:n,settings:[{colorValue:c.color,enableAlpha:!0,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return s(e||$t.textColor)}},{colorValue:u.color,enableAlpha:!0,label:(0,l.__)("Hover","blocksy-companion"),onColorChange:function(e){return p(e||$t.textHoverColor)}}]}),"default"===N?(0,r.createElement)(_,{label:(0,l.__)("Icons Color","blocksy-companion"),resetAll:function(){m($t.iconsColor),b($t.iconsHoverColor)},panelId:n,settings:[{colorValue:d.color,enableAlpha:!0,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return m(e||$t.iconsColor)}},{colorValue:f.color,enableAlpha:!0,label:(0,l.__)("Hover","blocksy-companion"),onColorChange:function(e){return b(e||$t.iconsHoverColor)}}]}):null,"simple"!==D&&"default"===N&&("solid"===B?(0,r.createElement)(_,{label:(0,l.__)("Icons Background Color","blocksy-companion"),resetAll:function(){h($t.backgroundColor),g($t.backgroundHoverColor)},panelId:n,settings:[{colorValue:y.color,enableAlpha:!0,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return h(e||$t.backgroundColor)}},{colorValue:v.color,enableAlpha:!0,label:(0,l.__)("Hover","blocksy-companion"),onColorChange:function(e){return g(e||$t.backgroundHoverColor)}}]}):(0,r.createElement)(_,{label:(0,l.__)("Icons Border Color","blocksy-companion"),resetAll:function(){w($t.borderColor),x($t.borderHoverColor)},panelId:n,settings:[{colorValue:k.color,enableAlpha:!0,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return w(e||$t.borderColor)}},{colorValue:O.color,enableAlpha:!0,label:(0,l.__)("Hover","blocksy-companion"),onColorChange:function(e){return x(e||$t.borderHoverColor)}}]}))))}));function ro(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function lo(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?ro(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):ro(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var ao=(0,Z.getOptionsForBlock)("about_me"),io=(0,Z.getAttributesFromOptions)(ao);(0,a.registerBlockType)("blocksy/about-me",{apiVersion:3,title:(0,l.__)("About Me Controls","blocksy-companion"),icon:{src:(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"wc-block-editor-components-block-icon"},(0,r.createElement)("path",{d:"M5.8 13H4.2v-1c0-1.5 1.2-2.8 2.8-2.8h4c1.5 0 2.8 1.2 2.8 2.8v1h-1.5v-1c0-.7-.6-1.2-1.2-1.2H7c-.7 0-1.2.6-1.2 1.2v1zM4 21h9v-1.5H4V21zm0-5.5V17h16v-1.5H4zm2.5-10C6.5 4.1 7.6 3 9 3s2.5 1.1 2.5 2.5S10.4 8 9 8 6.5 6.9 6.5 5.5zm1.5 0c0 .6.4 1 1 1s1-.4 1-1-.4-1-1-1-1 .4-1 1z"}))},supports:{html:!1,inserter:!1,lock:!1},parent:["blocksy/widgets-wrapper"],attributes:lo(lo({},io),Kt),edit:function(e){return(0,r.createElement)(no,e)},save:function(){return(0,r.createElement)("div",null,"Blocksy: About Me")}}),wp.blocks.registerBlockVariation("blocksy/widgets-wrapper",{name:"blocksy-about-me",title:(0,l.__)("About Me","blocksy-companion"),description:(0,l.__)("Showcase your personal information across your website.","blocksy-companion"),attributes:{heading:(0,l.__)("About Me","blocksy-companion"),block:"blocksy/about-me"},isDefault:!0,isActive:function(e){return"blocksy/about-me"===e.block},icon:{src:(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"wc-block-editor-components-block-icon"},(0,r.createElement)("path",{d:"M5.8 13H4.2v-1c0-1.5 1.2-2.8 2.8-2.8h4c1.5 0 2.8 1.2 2.8 2.8v1h-1.5v-1c0-.7-.6-1.2-1.2-1.2H7c-.7 0-1.2.6-1.2 1.2v1zM4 21h9v-1.5H4V21zm0-5.5V17h16v-1.5H4zm2.5-10C6.5 4.1 7.6 3 9 3s2.5 1.1 2.5 2.5S10.4 8 9 8 6.5 6.9 6.5 5.5zm1.5 0c0 .6.4 1 1 1s1-.4 1-1-.4-1-1-1-1 .4-1 1z"}))}});var co={textColor:"",customTextColor:"",textInitialColor:"",customTextInitialColor:"",textHoverColor:"",customTextHoverColor:"",iconsColor:"",customIconsColor:"",iconsHoverColor:"",customIconsHoverColor:"",borderColor:"rgba(218, 222, 228, 0.5)",customBorderColor:"rgba(218, 222, 228, 0.5)",borderHoverColor:"rgba(218, 222, 228, 0.7)",customBorderHoverColor:"rgba(218, 222, 228, 0.7)",backgroundColor:"rgba(218, 222, 228, 0.5)",customBackgroundColor:"rgba(218, 222, 228, 0.5)",backgroundHoverColor:"rgba(218, 222, 228, 0.7)",customBackgroundHoverColor:"rgba(218, 222, 228, 0.7)"},so=Zt(co);function uo(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function po(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?uo(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):uo(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var mo=po({contacts_icons_size:20,contacts_items_spacing:"",contacts_icon_shape:"rounded",contacts_icon_fill_type:"outline",contact_link_target:"no",contact_text:"",contacts_items_direction:"column",link_icons:"no"},co),fo=function(e){var t=e.attributes,o=t.contacts_icons_size,n=void 0===o?20:o,l=t.contacts_items_spacing,a=void 0===l?"":l,i=t.contacts_icon_shape,c=void 0===i?"rounded":i,s=t.contacts_icon_fill_type,u=void 0===s?"outline":s,d=t.contact_link_target,m=void 0===d?"no":d,f=t.contacts_items_direction,b=void 0===f?"column":f,y=(0,r.useCallback)((function(e){var t=document.createElement("div");t.innerHTML=e;var o=t.querySelector(".ct-contact-info-block ul"),n=t.querySelectorAll("a");return n&&n.forEach((function(e){e.target="yes"===m?"_blank":"_self"})),o.innerHTML}),[n,c,u,m,b,a]),h=Wt("contact-info",po(po({},t),mo),y),v=h.isLoading,g=h.preview;return v?(0,r.createElement)(p.Spinner,null):(0,r.createElement)("ul",{"data-icons-type":"simple"===c?c:"".concat(c,":").concat(u),dangerouslySetInnerHTML:{__html:g}})};function bo(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function yo(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?bo(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):bo(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var ho,vo=(0,i.withColors)({textColor:"color"},{textInitialColor:"color"},{textHoverColor:"color"},{iconsColor:"color"},{iconsHoverColor:"color"},{backgroundColor:"color"},{backgroundHoverColor:"color"},{borderColor:"color"},{borderHoverColor:"color"})((function(e){var t=e.attributes,o=e.setAttributes,n=e.clientId,a=e.className,c=e.textColor,s=e.setTextColor,u=e.textInitialColor,p=e.setTextInitialColor,d=e.textHoverColor,m=e.setTextHoverColor,f=e.iconsColor,b=e.setIconsColor,y=e.iconsHoverColor,h=e.setIconsHoverColor,v=e.backgroundColor,g=e.setBackgroundColor,k=e.backgroundHoverColor,w=e.setBackgroundHoverColor,O=e.borderColor,x=e.setBorderColor,C=e.borderHoverColor,E=e.setBorderHoverColor,P=t.contacts_items_direction,S=void 0===P?"column":P,j=t.contacts_icons_size,I=void 0===j?20:j,T=t.contacts_items_spacing,D=void 0===T?"":T,A=t.contacts_icon_shape,B=void 0===A?"rounded":A,H=t.contacts_icon_fill_type,N=void 0===H?"outline":H,z=(0,r.useRef)(),M=(0,i.useBlockProps)({ref:z,className:{"ct-contact-info-block":!0,className:a},style:yo(yo(yo({"--theme-block-text-color":null==c?void 0:c.color,"--theme-link-initial-color":null==u?void 0:u.color,"--theme-link-hover-color":null==d?void 0:d.color,"--theme-icon-color":null==f?void 0:f.color,"--theme-icon-hover-color":null==y?void 0:y.color,"--background-color":"solid"===N?null==v?void 0:v.color:null==O?void 0:O.color,"--background-hover-color":"solid"===N?null==k?void 0:k.color:null==C?void 0:C.color},I?{"--theme-icon-size":"".concat(I,"px")}:{}),D?{"--items-spacing":"".concat(D,"px")}:{}),"column"===S?{"--items-direction":S}:{})});return(0,r.createElement)("div",M,(0,r.createElement)(fo,{attributes:t,setAttributes:o}),(0,r.createElement)(eo,{attributes:t,setAttributes:o,options:ko}),(0,r.createElement)(i.InspectorControls,{group:"styles"},(0,r.createElement)(_,{label:(0,l.__)("Text Color","blocksy-companion"),resetAll:function(){s(co.textColor),p(co.textInitialColor),m(co.textHoverColor)},panelId:n,settings:[{colorValue:c.color,enableAlpha:!0,label:(0,l.__)("Text","blocksy-companion"),onColorChange:function(e){return s(e||co.textColor)}},{colorValue:u.color,enableAlpha:!0,label:(0,l.__)("Link Initial","blocksy-companion"),onColorChange:function(e){return p(e||co.textInitialColor)}},{colorValue:d.color,enableAlpha:!0,label:(0,l.__)("Link Hover","blocksy-companion"),onColorChange:function(e){return m(e||co.textHoverColor)}}]}),(0,r.createElement)(_,{label:(0,l.__)("Icons Color","blocksy-companion"),resetAll:function(){b(co.iconsColor),h(co.iconsHoverColor)},panelId:n,settings:[{colorValue:f.color,enableAlpha:!0,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return b(e||co.iconsColor)}},{colorValue:y.color,enableAlpha:!0,label:(0,l.__)("Hover","blocksy-companion"),onColorChange:function(e){return h(e||co.iconsHoverColor)}}]}),"simple"!==B&&("solid"===N?(0,r.createElement)(_,{label:(0,l.__)("Icons Background Color","blocksy-companion"),resetAll:function(){g(co.backgroundColor),w(co.backgroundHoverColor)},panelId:n,settings:[{colorValue:v.color,enableAlpha:!0,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return g(e||co.backgroundColor)}},{colorValue:k.color,enableAlpha:!0,label:(0,l.__)("Hover","blocksy-companion"),onColorChange:function(e){return w(e||co.backgroundHoverColor)}}]}):(0,r.createElement)(_,{label:(0,l.__)("Icons Border Color","blocksy-companion"),resetAll:function(){x(co.borderColor),E(co.borderHoverColor)},panelId:n,settings:[{colorValue:O.color,enableAlpha:!0,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return x(e||co.borderColor)}},{colorValue:C.color,enableAlpha:!0,label:(0,l.__)("Hover","blocksy-companion"),onColorChange:function(e){return E(e||co.borderHoverColor)}}]}))))}));function go(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function _o(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?go(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):go(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var ko=(0,Z.getOptionsForBlock)("contact_info"),wo=(0,Z.getAttributesFromOptions)(ko);(0,a.registerBlockType)("blocksy/contact-info",{apiVersion:3,title:(0,l.__)("Contact Info Controls","blocksy-companion"),icon:{src:(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"wc-block-editor-components-block-icon"},(0,r.createElement)("path",{fillRule:"evenodd",d:"M7.25 16.437a6.5 6.5 0 1 1 9.5 0V16A2.75 2.75 0 0 0 14 13.25h-4A2.75 2.75 0 0 0 7.25 16v.437Zm1.5 1.193a6.47 6.47 0 0 0 3.25.87 6.47 6.47 0 0 0 3.25-.87V16c0-.69-.56-1.25-1.25-1.25h-4c-.69 0-1.25.56-1.25 1.25v1.63ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm10-2a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z",clipRule:"evenodd"}))},supports:{html:!1,inserter:!1,lock:!1,typography:{fontSize:!0}},parent:["blocksy/widgets-wrapper"],attributes:_o(_o({},wo),so),edit:function(e){return(0,r.createElement)(vo,e)},save:function(){return(0,r.createElement)("div",null,"Blocksy: Contact Info")}}),wp.blocks.registerBlockVariation("blocksy/widgets-wrapper",{name:"blocksy-contact-info",title:(0,l.__)("Contact Info","blocksy-companion"),description:(0,l.__)("Display essential contact details to your visitors.","blocksy-companion"),attributes:{heading:(0,l.__)("Contact Info","blocksy-companion"),block:"blocksy/contact-info",hasDescription:!0,description:(null==wo||null===(ho=wo.contact_text)||void 0===ho?void 0:ho.default)||""},isActive:function(e){return"blocksy/contact-info"===e.block},icon:{src:(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"wc-block-editor-components-block-icon"},(0,r.createElement)("path",{fillRule:"evenodd",d:"M7.25 16.437a6.5 6.5 0 1 1 9.5 0V16A2.75 2.75 0 0 0 14 13.25h-4A2.75 2.75 0 0 0 7.25 16v.437Zm1.5 1.193a6.47 6.47 0 0 0 3.25.87 6.47 6.47 0 0 0 3.25-.87V16c0-.69-.56-1.25-1.25-1.25h-4c-.69 0-1.25.56-1.25 1.25v1.63ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm10-2a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z",clipRule:"evenodd"}))}});var Oo={initialColor:"",customInitialColor:"",hoverColor:"",customHoverColor:"",borderColor:"rgba(218, 222, 228, 0.5)",customBorderColor:"rgba(218, 222, 228, 0.5)",borderHoverColor:"rgba(218, 222, 228, 0.7)",customBorderHoverColor:"rgba(218, 222, 228, 0.7)",backgroundColor:"rgba(218, 222, 228, 0.5)",customBackgroundColor:"rgba(218, 222, 228, 0.5)",backgroundHoverColor:"rgba(218, 222, 228, 0.7)",customBackgroundHoverColor:"rgba(218, 222, 228, 0.7)"},xo=Zt(Oo);function Co(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Eo(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Co(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Co(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var Po=Eo({link_nofollow:"no",link_target:"no",social_icons_color:"official",social_icons_fill:"outline",social_icons_size:"",items_spacing:"",social_type:"simple"},Oo),So=function(e){var t=e.attributes,o=t.social_icons_color,n=t.social_icons_size,l=t.social_type,a=t.social_icons_fill,i=t.items_spacing,c=(0,r.useCallback)((function(e){var t=document.createElement("div");return t.innerHTML=e,t.querySelector(".ct-social-box").innerHTML}),[o,n,l,a,i]),s=Wt("socials",Eo(Eo({},t),Po),c),u=s.isLoading,d=s.preview;return u?(0,r.createElement)(p.Spinner,null):(0,r.createElement)(r.RawHTML,{className:"ct-social-box","data-icons-type":"simple"===l?l:"".concat(l,":").concat(a),"data-color":o},d)};function jo(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Io(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?jo(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):jo(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var To=(0,Z.getOptionsForBlock)("socials"),Do=(0,i.withColors)({initialColor:"color"},{hoverColor:"color"},{backgroundColor:"color"},{backgroundHoverColor:"color"},{borderColor:"color"},{borderHoverColor:"color"})((function(e){var t=e.attributes,o=e.setAttributes,n=e.clientId,a=e.className,c=e.initialColor,s=e.setInitialColor,u=e.hoverColor,p=e.setHoverColor,d=e.backgroundColor,m=e.setBackgroundColor,f=e.backgroundHoverColor,b=e.setBackgroundHoverColor,y=e.borderColor,h=e.setBorderColor,v=e.borderHoverColor,g=e.setBorderHoverColor,k=t.items_spacing,w=void 0===k?"":k,O=t.social_icons_size,x=void 0===O?"":O,C=t.social_type,E=void 0===C?"simple":C,P=t.social_icons_fill,S=void 0===P?"outline":P,j=t.social_icons_color,I=void 0===j?"default":j,T=(0,r.useRef)(),D=(0,i.useBlockProps)({ref:T,className:{"ct-socials-block":!0,className:a},style:Io(Io({"--theme-icon-color":null==c?void 0:c.color,"--theme-icon-hover-color":null==u?void 0:u.color,"--background-color":"solid"===S?null==d?void 0:d.color:null==y?void 0:y.color,"--background-hover-color":"solid"===S?null==f?void 0:f.color:null==v?void 0:v.color},x?{"--theme-icon-size":"".concat(x,"px")}:{}),w?{"--items-spacing":"".concat(w,"px")}:{})});return(0,r.createElement)("div",D,(0,r.createElement)(So,{attributes:t,setAttributes:o}),(0,r.createElement)(eo,{attributes:t,setAttributes:o,options:To}),"default"===I?(0,r.createElement)(i.InspectorControls,{group:"styles"},(0,r.createElement)(_,{label:(0,l.__)("Icon Color","blocksy-companion"),resetAll:function(){s(Oo.initialColor),p(Oo.hoverColor)},panelId:n,settings:[{colorValue:c.color,enableAlpha:!0,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return s(e||Oo.initialColor)}},{colorValue:u.color,enableAlpha:!0,label:(0,l.__)("Hover","blocksy-companion"),onColorChange:function(e){return p(e||Oo.hoverColor)}}]}),"simple"!==E&&("solid"===S?(0,r.createElement)(_,{label:(0,l.__)("Icons Background Colors","blocksy-companion"),resetAll:function(){m(Oo.backgroundColor),b(Oo.backgroundHoverColor)},panelId:n,settings:[{colorValue:d.color,enableAlpha:!0,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return m(e||Oo.backgroundColor)}},{colorValue:f.color,enableAlpha:!0,label:(0,l.__)("Hover","blocksy-companion"),onColorChange:function(e){return b(e||Oo.backgroundHoverColor)}}]}):(0,r.createElement)(_,{label:(0,l.__)("Icons Border Colors","blocksy-companion"),resetAll:function(){h(Oo.borderColor),g(Oo.borderHoverColor)},panelId:n,settings:[{colorValue:y.color,enableAlpha:!0,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return h(e||Oo.borderColor)}},{colorValue:v.color,enableAlpha:!0,label:(0,l.__)("Hover","blocksy-companion"),onColorChange:function(e){return g(e||Oo.borderHoverColor)}}]}))):null)}));function Ao(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Bo(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Ao(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Ao(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var Ho=(0,Z.getOptionsForBlock)("socials"),No=(0,Z.getAttributesFromOptions)(Ho);(0,a.registerBlockType)("blocksy/socials",{apiVersion:3,title:(0,l.__)("Socials Controls","blocksy-companion"),icon:{src:(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"wc-block-editor-components-block-icon"},(0,r.createElement)("path",{d:"M16.4 14.2c-.8 0-1.5.3-2.1.9l-3.9-2.3c.1-.3.1-.5.1-.8 0-.3-.1-.5-.1-.8L14.3 9c.5.5 1.3.9 2.1.9 1.6 0 2.9-1.3 2.9-2.9S18 4 16.4 4s-2.9 1.3-2.9 2.9c0 .*******.8L9.7 10c-.5-.6-1.3-.9-2.1-.9-1.6 0-2.9 1.3-2.9 2.9 0 1.6 1.3 2.9 2.9 2.9.8 0 1.5-.3 2.1-.9l3.9 2.3c-.1.3-.1.5-.1.8 0 1.6 1.3 2.9 2.9 2.9s2.9-1.3 2.9-2.9c0-1.6-1.3-2.9-2.9-2.9zm0-8.7c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5-1.5-.7-1.5-1.5.7-1.5 1.5-1.5zm-8.8 8c-.8 0-1.5-.7-1.5-1.5s.7-1.5 1.5-1.5 1.5.7 1.5 1.5-.7 1.5-1.5 1.5zm8.8 5c-.8 0-1.5-.7-1.5-1.5 0-.3.1-.5.2-.7.3-.4.7-.7 1.2-.7.8 0 1.5.7 1.5 1.5s-.6 1.4-1.4 1.4z"}))},supports:{html:!1,inserter:!1,lock:!1},parent:["blocksy/widgets-wrapper"],attributes:Bo(Bo({},No),xo),edit:function(e){return(0,r.createElement)(Do,e)},save:function(){return(0,r.createElement)("div",null,"Blocksy: Socials")}}),wp.blocks.registerBlockVariation("blocksy/widgets-wrapper",{name:"blocksy-socials",title:(0,l.__)("Socials","blocksy-companion"),description:(0,l.__)("Display your social media profiles and boost the site engagement.","blocksy-companion"),attributes:{heading:(0,l.__)("Socials","blocksy-companion"),block:"blocksy/socials"},isActive:function(e){return"blocksy/socials"===e.block},icon:{src:(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"wc-block-editor-components-block-icon"},(0,r.createElement)("path",{d:"M16.4 14.2c-.8 0-1.5.3-2.1.9l-3.9-2.3c.1-.3.1-.5.1-.8 0-.3-.1-.5-.1-.8L14.3 9c.5.5 1.3.9 2.1.9 1.6 0 2.9-1.3 2.9-2.9S18 4 16.4 4s-2.9 1.3-2.9 2.9c0 .*******.8L9.7 10c-.5-.6-1.3-.9-2.1-.9-1.6 0-2.9 1.3-2.9 2.9 0 1.6 1.3 2.9 2.9 2.9.8 0 1.5-.3 2.1-.9l3.9 2.3c-.1.3-.1.5-.1.8 0 1.6 1.3 2.9 2.9 2.9s2.9-1.3 2.9-2.9c0-1.6-1.3-2.9-2.9-2.9zm0-8.7c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5-1.5-.7-1.5-1.5.7-1.5 1.5-1.5zm-8.8 8c-.8 0-1.5-.7-1.5-1.5s.7-1.5 1.5-1.5 1.5.7 1.5 1.5-.7 1.5-1.5 1.5zm8.8 5c-.8 0-1.5-.7-1.5-1.5 0-.3.1-.5.2-.7.3-.4.7-.7 1.2-.7.8 0 1.5.7 1.5 1.5s-.6 1.4-1.4 1.4z"}))}});var zo={initialColor:"",customInitialColor:"",hoverColor:"",customHoverColor:"",borderColor:"rgba(218, 222, 228, 0.5)",customBorderColor:"rgba(218, 222, 228, 0.5)",borderHoverColor:"rgba(218, 222, 228, 0.7)",customBorderHoverColor:"rgba(218, 222, 228, 0.7)",backgroundColor:"rgba(218, 222, 228, 0.5)",customBackgroundColor:"rgba(218, 222, 228, 0.5)",backgroundHoverColor:"rgba(218, 222, 228, 0.7)",customBackgroundHoverColor:"rgba(218, 222, 228, 0.7)"},Mo=Zt(zo);function Fo(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Ro(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Fo(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Fo(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var Vo=Ro({link_nofollow:"no",share_icons_size:"",items_spacing:""},zo),Lo=function(e){var t=e.attributes,o=t.share_icons_color,n=t.share_icons_size,l=t.share_type,a=t.share_icons_fill,i=t.items_spacing,c=(0,r.useCallback)((function(e){var t=document.createElement("div");return t.innerHTML=e,t.querySelector(".ct-share-box").innerHTML}),[o,n,l,a,i]),s=Wt("share-box",Ro(Ro({},t),Vo),c),u=s.isLoading,d=s.preview;return u?(0,r.createElement)(p.Spinner,null):(0,r.createElement)(r.RawHTML,{className:"ct-share-box","data-type":"type-3"},d)};function Uo(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function qo(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Uo(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Uo(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var Go=(0,Z.getOptionsForBlock)("share_box"),Wo=(0,i.withColors)({initialColor:"color"},{hoverColor:"color"},{backgroundColor:"color"},{backgroundHoverColor:"color"},{borderColor:"color"},{borderHoverColor:"color"})((function(e){var t=e.attributes,o=e.setAttributes,n=e.clientId,a=e.className,c=e.initialColor,s=e.setInitialColor,u=e.hoverColor,p=e.setHoverColor,d=e.backgroundColor,m=e.setBackgroundColor,f=e.backgroundHoverColor,b=e.setBackgroundHoverColor,y=e.borderColor,h=e.setBorderColor,v=e.borderHoverColor,g=e.setBorderHoverColor,k=t.items_spacing,w=void 0===k?"":k,O=t.share_icons_size,x=void 0===O?"":O,C=t.share_type,E=void 0===C?"simple":C,P=t.share_icons_fill,S=void 0===P?"outline":P,j=t.share_icons_color,I=void 0===j?"default":j,T=(0,r.useRef)(),D=(0,i.useBlockProps)({ref:T,className:{"ct-shares-block":!0,className:a},style:qo(qo({"--theme-icon-color":null==c?void 0:c.color,"--theme-icon-hover-color":null==u?void 0:u.color,"--background-color":"solid"===S?null==d?void 0:d.color:null==y?void 0:y.color,"--background-hover-color":"solid"===S?null==f?void 0:f.color:null==v?void 0:v.color},x?{"--theme-icon-size":"".concat(x,"px")}:{}),w?{"--items-spacing":"".concat(w,"px")}:{})});return(0,r.createElement)("div",D,(0,r.createElement)(Lo,{attributes:t,setAttributes:o}),(0,r.createElement)(eo,{attributes:t,setAttributes:o,options:Go}),"default"===I?(0,r.createElement)(i.InspectorControls,{group:"styles"},(0,r.createElement)(_,{label:(0,l.__)("Icon Color","blocksy-companion"),resetAll:function(){s(zo.initialColor),p(zo.hoverColor)},panelId:n,settings:[{colorValue:c.color,enableAlpha:!0,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return s(e||zo.initialColor)}},{colorValue:u.color,enableAlpha:!0,label:(0,l.__)("Hover","blocksy-companion"),onColorChange:function(e){return p(e||zo.hoverColor)}}]}),"simple"!==E&&("solid"===S?(0,r.createElement)(_,{label:(0,l.__)("Icons Background Colors","blocksy-companion"),resetAll:function(){m(zo.backgroundColor),b(zo.backgroundHoverColor)},panelId:n,settings:[{colorValue:d.color,enableAlpha:!0,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return m(e||zo.backgroundColor)}},{colorValue:f.color,enableAlpha:!0,label:(0,l.__)("Hover","blocksy-companion"),onColorChange:function(e){return b(e||zo.backgroundHoverColor)}}]}):(0,r.createElement)(_,{label:(0,l.__)("Icons Border Colors","blocksy-companion"),resetAll:function(){h(zo.borderColor),g(zo.borderHoverColor)},panelId:n,settings:[{colorValue:y.color,enableAlpha:!0,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return h(e||zo.borderColor)}},{colorValue:v.color,enableAlpha:!0,label:(0,l.__)("Hover","blocksy-companion"),onColorChange:function(e){return g(e||zo.borderHoverColor)}}]}))):null)}));function Zo(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function $o(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Zo(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Zo(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var Ko=["facebook","twitter","pinterest","linkedin"],Xo=["facebook","twitter","pinterest","linkedin","reddit","hacker_news","vk","ok","telegram","viber","whatsapp","flipboard","line","email","clipboard"],Yo=[{apiVersion:3,isEligible:function(e){return!e.share_networks},migrate:function(e){var t=Xo.reduce((function(t,o){return e["share_".concat(o)]?("yes"===e["share_".concat(o)]&&t.push({id:o,enabled:!0}),t):(Ko.includes(o)&&t.push({id:o,enabled:!0}),t)}),[]);return $o($o({},Object.keys(e).reduce((function(t,o){return Xo.includes(o.replace("share_",""))||(t[o]=e[o]),t}),{})),{},{share_networks:t})},attributes:{title:{type:"string",default:"Share Icons"},share_facebook:{type:"string",default:"yes"},share_twitter:{type:"string",default:"yes"},share_pinterest:{type:"string",default:"yes"},share_linkedin:{type:"string",default:"yes"},share_reddit:{type:"string",default:"no"},share_hacker_news:{type:"string",default:"no"},share_vk:{type:"string",default:"no"},share_ok:{type:"string",default:"no"},share_telegram:{type:"string",default:"no"},share_viber:{type:"string",default:"no"},share_whatsapp:{type:"string",default:"no"},share_flipboard:{type:"string",default:"no"},share_line:{type:"string",default:"no"},share_email:{type:"string",default:"no"},share_clipboard:{type:"string",default:"no"},share_item_tooltip:{type:"string",default:"no"},link_nofollow:{type:"string",default:"no"},share_icons_size:{type:"string",default:""},items_spacing:{type:"string",default:""},share_icons_color:{type:"string",default:"default"},share_type:{type:"string",default:"simple"},share_icons_fill:{type:"string",default:"outline"},initialColor:{type:"string",default:""},customInitialColor:{type:"string",default:""},hoverColor:{type:"string",default:""},customHoverColor:{type:"string",default:""},borderColor:{type:"string",default:"rgba(218, 222, 228, 0.5)"},customBorderColor:{type:"string",default:"rgba(218, 222, 228, 0.5)"},borderHoverColor:{type:"string",default:"rgba(218, 222, 228, 0.7)"},customBorderHoverColor:{type:"string",default:"rgba(218, 222, 228, 0.7)"},backgroundColor:{type:"string",default:"rgba(218, 222, 228, 0.5)"},customBackgroundColor:{type:"string",default:"rgba(218, 222, 228, 0.5)"},backgroundHoverColor:{type:"string",default:"rgba(218, 222, 228, 0.7)"},customBackgroundHoverColor:{type:"string",default:"rgba(218, 222, 228, 0.7)"}},save:function(e){return(0,r.createElement)("div",null,"Blocksy: Share Box")}}];function Jo(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Qo(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Jo(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Jo(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var en=(0,Z.getOptionsForBlock)("share_box"),tn=(0,Z.getAttributesFromOptions)(en);(0,a.registerBlockType)("blocksy/share-box",{apiVersion:3,title:(0,l.__)("Share Box Controls","blocksy-companion"),icon:{src:(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"wc-block-editor-components-block-icon"},(0,r.createElement)("path",{d:"M20 14.6c-.2-.3-.5-.6-.9-.8.1-1.2-.1-2.4-.6-3.5s-1.3-2.1-2.3-2.9c-.4-.3-.8-.5-1.3-.8 0-.6-.2-1.2-.5-1.6-.3-.5-.8-.9-1.4-1.1-1-.4-2.4-.1-3.2.8-.5.5-.8 1.2-.8 2-1 .4-1.8 1-2.5 1.8-.8 1-1.4 2.1-1.6 3.3-.1.6-.1 1.3-.1 2-.7.4-1.2 1-1.4 1.7-.3 1.2.1 2.4 1 3.2s2.3.8 3.3.2c.9.6 1.9 1.1 3 *******.8.1 ******* 0 1.9-.2 2.7-.5.5-.2.9-.5 1.4-.*******.2.7.3 1.1.3 2.4-.1 3.1-1 .1-.1.2-.3.3-.4v-.1c.7-.9.6-2.2-.1-3.1zm-9.6-8.1c0-.1 0-.2.1-.3 0 0 0-.1.1-.2 0-.1.1-.2.2-.3l.1-.1s.1-.1.1-.2h.1s.1 0 .1-.1c0 0 .1 0 .1-.1h.1c.1 0 .2-.1.3-.1h.7c.1 0 .1 0 .2.1.1 0 .******* 0 0 .1 0 .1.1 0 0 .1 0 .1.1l.*******v.1l.1.1s0 .1.1.1V7c0 .1 0 .2-.1.3 0 0 0 .1-.1.1 0 0 0 .1-.1.1 0 0-.1.1-.1.2l-.1.1-.1.1h-.1c-.1 0-.1.1-.2.1h-.2c-.1 0-.2.1-.3.1h-1l.1-.1s-.1 0-.1-.1l-.1-.1-.1-.1-.1-.1v-.1l-.1-.1s0-.1-.1-.1v-.1c0-.1-.1-.2-.1-.3V6.6c-.1 0-.1 0-.1-.1zM8 16.6c0 .1-.1.3-.1.4 0 .1-.1.1-.1.2s-.1.1-.1.1l-.1.1-.3.3-.1.1H7v.1c-.1 0-.2.1-.3.1h-.8c-.1 0-.2 0-.3-.1 0 0-.1 0-.1-.1 0 0-.1 0-.1-.1l-.1-.1h-.1l-.1-.1-.1-.1v-.1c.1-.1 0-.2-.1-.3v-.2c0-.1 0-.2-.1-.2V16.3c0-.1 0-.2.1-.2 0 0 0-.1.1-.2 0-.1.1-.2.1-.2l.1-.1.2-.2.1-.1c.1 0 .1-.1.2-.1h.2c.1 0 .2-.1.3-.1h.6c.1 0 .1 0 .2.1l.3-.2s.1 0 .1.1l.2.2c0 .1.1.1.1.1 0 .1.*******v.1c.1.2.2.3.2.5v.4c0-.1 0 0 0 0zm7.1 1.4c-.3.2-.5.3-.8.4h-.1l-.2.1c-.1 0-.3.1-.4.1-.3.1-.5.1-.8.2H11.1l-.2-.1c-.1 0-.3-.1-.4-.1-.3-.1-.5-.2-.8-.3-.1-.1-.2-.1-.3-.2-.1-.1-.3-.1-.4-.2 0 .2 0 .1-.1.1.5-.8.6-1.9.3-2.7-.3-.6-.7-1.1-1.2-1.4-.5-.3-1.1-.5-1.7-.4v-1c0-.3.1-.5.2-.8 0-.1.1-.3.1-.4l.1-.3c.1-.2.2-.5.4-.7.1-.1.1-.2.2-.3l.1-.1.1-.1c.1-.4.3-.6.5-.8l.3-.3.1-.1.1-.1c.2-.1.5-.3.7-.4 0 0 .1 0 .1-.1 0 0 0 .1.1.1.3.5.7.9 1.3 1.2.6.3 1.2.4 1.9.3.9-.1 1.7-.8 2.2-1.6.2.1.4.3.7.5l.2.1c.*******.3.2.2.2.4.4.5.6l.1.1s0 .1.1.1c.*******.2.3.1.2.3.4.4.7 0 .1.*******v.1c0 .*******.4.1.3.1.5.2.8v1.3c-.4 0-.8 0-1.1.1-1.2.4-2.1 1.6-2 2.9 0 .6.2 1.1.5 1.6zm1.7-3.3zm2.4 1.8v.2c0 .1 0 .2-.1.3v.1c0 .1 0 .1-.1.1 0 0-.1.1-.1.2l-.2.2-.1.1c-.1.1-.2.1-.3.2h-.1c-.1 0-.2.1-.3.1h-.7c-.1 0-.2-.1-.3-.2h-.1l-.1-.1-.1-.1c-.1-.1-.1-.2-.2-.3v-.2c0-.1-.1-.2-.1-.4v-.4c0-.1 0-.2.1-.3l.1-.2v-.1c0-.1.1-.2.2-.3l.1-.1.1-.1c.1-.1.2-.1.3-.2h.2c.1 0 .2-.1.3-.1H18.7c.1 0 .2.1.3.2l.*******c.*******.2.3v.2c0 .*******.3v.1c-.4 0-.4.1-.4.2z"}))},supports:{html:!1,inserter:!1,lock:!1},parent:["blocksy/widgets-wrapper"],attributes:Qo(Qo({},tn),Mo),edit:function(e){return(0,r.createElement)(Wo,e)},save:function(){return(0,r.createElement)("div",null,"Blocksy: Share Box")},deprecated:Yo}),wp.blocks.registerBlockVariation("blocksy/widgets-wrapper",{name:"blocksy-share-box",title:(0,l.__)("Share Box","blocksy-companion"),description:(0,l.__)("Share content on social media, boosting visibility & engagement.","blocksy-companion"),attributes:{heading:(0,l.__)("Share Box","blocksy-companion"),block:"blocksy/share-box"},isActive:function(e){return"blocksy/share-box"===e.block},icon:{src:(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"wc-block-editor-components-block-icon"},(0,r.createElement)("path",{d:"M20 14.6c-.2-.3-.5-.6-.9-.8.1-1.2-.1-2.4-.6-3.5s-1.3-2.1-2.3-2.9c-.4-.3-.8-.5-1.3-.8 0-.6-.2-1.2-.5-1.6-.3-.5-.8-.9-1.4-1.1-1-.4-2.4-.1-3.2.8-.5.5-.8 1.2-.8 2-1 .4-1.8 1-2.5 1.8-.8 1-1.4 2.1-1.6 3.3-.1.6-.1 1.3-.1 2-.7.4-1.2 1-1.4 1.7-.3 1.2.1 2.4 1 3.2s2.3.8 3.3.2c.9.6 1.9 1.1 3 *******.8.1 ******* 0 1.9-.2 2.7-.5.5-.2.9-.5 1.4-.*******.2.7.3 1.1.3 2.4-.1 3.1-1 .1-.1.2-.3.3-.4v-.1c.7-.9.6-2.2-.1-3.1zm-9.6-8.1c0-.1 0-.2.1-.3 0 0 0-.1.1-.2 0-.1.1-.2.2-.3l.1-.1s.1-.1.1-.2h.1s.1 0 .1-.1c0 0 .1 0 .1-.1h.1c.1 0 .2-.1.3-.1h.7c.1 0 .1 0 .2.1.1 0 .******* 0 0 .1 0 .1.1 0 0 .1 0 .1.1l.*******v.1l.1.1s0 .1.1.1V7c0 .1 0 .2-.1.3 0 0 0 .1-.1.1 0 0 0 .1-.1.1 0 0-.1.1-.1.2l-.1.1-.1.1h-.1c-.1 0-.1.1-.2.1h-.2c-.1 0-.2.1-.3.1h-1l.1-.1s-.1 0-.1-.1l-.1-.1-.1-.1-.1-.1v-.1l-.1-.1s0-.1-.1-.1v-.1c0-.1-.1-.2-.1-.3V6.6c-.1 0-.1 0-.1-.1zM8 16.6c0 .1-.1.3-.1.4 0 .1-.1.1-.1.2s-.1.1-.1.1l-.1.1-.3.3-.1.1H7v.1c-.1 0-.2.1-.3.1h-.8c-.1 0-.2 0-.3-.1 0 0-.1 0-.1-.1 0 0-.1 0-.1-.1l-.1-.1h-.1l-.1-.1-.1-.1v-.1c.1-.1 0-.2-.1-.3v-.2c0-.1 0-.2-.1-.2V16.3c0-.1 0-.2.1-.2 0 0 0-.1.1-.2 0-.1.1-.2.1-.2l.1-.1.2-.2.1-.1c.1 0 .1-.1.2-.1h.2c.1 0 .2-.1.3-.1h.6c.1 0 .1 0 .2.1l.3-.2s.1 0 .1.1l.2.2c0 .1.1.1.1.1 0 .1.*******v.1c.1.2.2.3.2.5v.4c0-.1 0 0 0 0zm7.1 1.4c-.3.2-.5.3-.8.4h-.1l-.2.1c-.1 0-.3.1-.4.1-.3.1-.5.1-.8.2H11.1l-.2-.1c-.1 0-.3-.1-.4-.1-.3-.1-.5-.2-.8-.3-.1-.1-.2-.1-.3-.2-.1-.1-.3-.1-.4-.2 0 .2 0 .1-.1.1.5-.8.6-1.9.3-2.7-.3-.6-.7-1.1-1.2-1.4-.5-.3-1.1-.5-1.7-.4v-1c0-.3.1-.5.2-.8 0-.1.1-.3.1-.4l.1-.3c.1-.2.2-.5.4-.7.1-.1.1-.2.2-.3l.1-.1.1-.1c.1-.4.3-.6.5-.8l.3-.3.1-.1.1-.1c.2-.1.5-.3.7-.4 0 0 .1 0 .1-.1 0 0 0 .1.1.1.3.5.7.9 1.3 1.2.6.3 1.2.4 1.9.3.9-.1 1.7-.8 2.2-1.6.2.1.4.3.7.5l.2.1c.*******.3.2.2.2.4.4.5.6l.1.1s0 .1.1.1c.*******.2.3.1.2.3.4.4.7 0 .1.*******v.1c0 .*******.4.1.3.1.5.2.8v1.3c-.4 0-.8 0-1.1.1-1.2.4-2.1 1.6-2 2.9 0 .6.2 1.1.5 1.6zm1.7-3.3zm2.4 1.8v.2c0 .1 0 .2-.1.3v.1c0 .1 0 .1-.1.1 0 0-.1.1-.1.2l-.2.2-.1.1c-.1.1-.2.1-.3.2h-.1c-.1 0-.2.1-.3.1h-.7c-.1 0-.2-.1-.3-.2h-.1l-.1-.1-.1-.1c-.1-.1-.1-.2-.2-.3v-.2c0-.1-.1-.2-.1-.4v-.4c0-.1 0-.2.1-.3l.1-.2v-.1c0-.1.1-.2.2-.3l.1-.1.1-.1c.1-.1.2-.1.3-.2h.2c.1 0 .2-.1.3-.1H18.7c.1 0 .2.1.3.2l.*******c.*******.2.3v.2c0 .*******.3v.1c-.4 0-.4.1-.4.2z"}))}});var on=function(e){var t=e.attributes.has_field_link,o=e.postId,n=e.postType,l=u((0,d.useEntityProp)("postType",n,"title",o),3),a=l[0],i=void 0===a?"":a;l[1],l[2];return o?i?"yes"===t?(0,r.createElement)("a",{href:"#",rel:"noopener noreferrer"},h(i)):h(i):null:"Title"},nn=function(e){if(!e)return"";var t=(new window.DOMParser).parseFromString(e,"text/html");return t.body.textContent||t.body.innerText||""},rn=function(e){var t=e.attributes.excerpt_length,o=e.postId,n=e.postType,l=e.fallback,a=u((0,d.useEntityProp)("postType",n,"excerpt",o),3),i=a[0],c=(a[1],a[2]),s=(c=void 0===c?{}:c).rendered,p=(c.protected,u((0,d.useEntityProp)("postType",n,"content",o),3)),m=(p[0],p[1],p[2]),f=(m=void 0===m?{}:m).rendered,b=(i||nn(s)||nn(f)).trim(),y=b.split(" ",t).join(" "),h=y!==b?"...":"";return o?y?(0,r.createElement)(r.RawHTML,null,y,h):l:"Excerpt"},ln=window.wp.date,an=function(e){var t=e.postId,o=e.postType,n=e.attributes,l=n.date_type,a=n.default_format,i=n.date_format,c=n.custom_date_format,s=n.has_field_link,p=u((0,d.useEntityProp)("postType",o,"published"===l?"date":"modified",t),1)[0],m="yes"===a?(0,ln.getSettings)().formats.date:"custom"!==i?i:c,f=(0,r.createElement)("span",null,(0,ln.format)(m,p));return t?(s&&(f=(0,r.createElement)("a",{href:"#"},f)),f):"Date"},cn=window.wp.apiFetch,sn=o.n(cn),un=window.wp.url,pn={},dn=function(e){var t=e.postId,o=u((0,r.useState)(null),2),n=o[0],l=o[1];return(0,r.useEffect)((function(){pn[t]&&l(pn[t]),pn[t]||sn()({path:(0,un.addQueryArgs)("/wp/v2/comments",{post:t,_fields:"id"}),method:"HEAD",parse:!1}).then((function(e){var o={total:parseInt(e.headers.get("X-WP-Total"))};l(o),pn[t]=o}))}),[t]),{commentsData:n}},mn=function(e){var t=e.postId,o=(e.postType,e.attributes),n=o.has_field_link,l=o.zero_text,a=o.single_text,i=o.multiple_text,c=dn({postId:t}).commentsData,s=c&&c.total?c.total:0,u=0===s?l:1===s?a:i;return"yes"===n?(0,r.createElement)("a",{href:"#",rel:"noopener noreferrer"},u.replace("%",s)):u.replace("%",s)},fn=function(e,t){switch(t){case"email":return(null==e?void 0:e.email)||"";case"nicename":return(null==e?void 0:e.nickname)||"";case"display_name":return(null==e?void 0:e.name)||"";case"first_name":return(null==e?void 0:e.first_name)||"";case"last_name":return(null==e?void 0:e.last_name)||"";case"description":return(null==e?void 0:e.description)||""}},bn=function(e){var t=e.postId,o=e.postType,n=e.attributes,l=n.has_field_link,a=n.author_field,i=e.fallback,c=(e.fieldsDescriptor,(0,k.useSelect)((function(e){var n,r=e(d.store),l=r.getEditedEntityRecord,a=r.getUser,i=(r.getUsers,null===(n=l("postType",o,t))||void 0===n?void 0:n.author);return{authorId:i,authorDetails:i?a(i):null}}),[o,t])),s=(c.authorId,c.authorDetails);return t?s?"yes"===l?(0,r.createElement)("a",{href:"#",rel:"noopener noreferrer"},fn(s,a)||i):fn(s,a)||i:null:"Author: ".concat(a)},yn=function(e){var t,o=e.postId,l=e.postType,a=e.fallback,i=e.attributes,c=e.attributes,s=c.has_field_link,u=c.taxonomy,p=c.separator,d=(e.fieldsDescriptor,ke(l)),m=(0,k.useSelect)((function(e){return{terms:e("core").getEntityRecords("taxonomy",u||(d&&d.length>0?d[0].slug:""),{per_page:-1,post:o})||[]}})).terms,f=(null===(t=d.find((function(e){return e.slug===u})))||void 0===t?void 0:t.name)||"Category";if(!o)return"".concat(f);if(0===m.length)return a||"".concat(f);var b="span",y={};return"yes"===s&&(b="a",y.href="#",y.rel="noopener noreferrer"),m.map((function(e,t){return(0,r.createElement)(React.Fragment,null,(0,r.createElement)(b,g({},y,{className:ee()(n({},"ct-term-".concat(e.id),"yes"===i.termAccentColor),i.termClass),dangerouslySetInnerHTML:{__html:e.name}})),t!==m.length-1?p.replace(/ /g," "):"")}))},hn={x:.5,y:.5};function vn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:hn,t=e.x,o=e.y;return"".concat(Math.round(100*t),"% ").concat(Math.round(100*o),"%")}var gn=function(e){var t=e.attributes,o=e.url,n=t.hasParallax,l=t.focalPoint,a=t.alt,i=o?"url(".concat(o,")"):void 0,c=vn(l);return o?n?(0,r.createElement)("div",{role:a?"img":void 0,"aria-label":a||void 0,className:ee()("wp-block-cover__image-background",{"has-parallax":n}),style:{backgroundImage:i,backgroundPosition:c}}):(0,r.createElement)("img",{alt:a,className:ee()("wp-block-cover__image-background",{"has-parallax":n}),src:o,style:{objectPosition:l?vn(l):void 0}}):(0,r.createElement)("div",{className:ee()("ct-dynamic-data-placeholder",{})},(0,r.createElement)("svg",{fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 60 60",preserveAspectRatio:"none",className:"ct-dynamic-data-placeholder-illustration","aria-hidden":"true",focusable:"false"},(0,r.createElement)("path",{vectorEffect:"non-scaling-stroke",d:"M60 60 0 0"})))};var _n=function(){return _n=Object.assign||function(e){for(var t,o=1,n=arguments.length;o<n;o++)for(var r in t=arguments[o])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},_n.apply(this,arguments)};Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;function kn(e){return e.toLowerCase()}var wn=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],On=/[^A-Z0-9]+/gi;function xn(e,t,o){return t instanceof RegExp?e.replace(t,o):t.reduce((function(e,t){return e.replace(t,o)}),e)}function Cn(e,t){return void 0===t&&(t={}),function(e,t){void 0===t&&(t={});for(var o=t.splitRegexp,n=void 0===o?wn:o,r=t.stripRegexp,l=void 0===r?On:r,a=t.transform,i=void 0===a?kn:a,c=t.delimiter,s=void 0===c?" ":c,u=xn(xn(e,n,"$1\0$2"),l,"\0"),p=0,d=u.length;"\0"===u.charAt(p);)p++;for(;"\0"===u.charAt(d-1);)d--;return u.slice(p,d).split("\0").map(i).join(s)}(e,_n({delimiter:"."},t))}var En=window.wp.styleEngine;function Pn(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Sn(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Pn(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Pn(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function jn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=t.additionalRules,n=x(t,["additionalRules"]),r=[].concat(O((0,En.getCSSRules)(e,n)),O(o||[]));if(null==n||!n.selector){var l=[];return r.forEach((function(e){l.push("".concat(Tn(e.key),": ").concat(e.value,";"))})),l.join(" ")}var a=r.reduce((function(e,t){var o=t.selector;return o?(e[o]||(e[o]=[]),e[o].push(t),e):e}),{});return Object.keys(a).reduce((function(e,t){return e.push("".concat(t," { ").concat(a[t].map((function(e){return"".concat(Tn(e.key),": ").concat(e.value,";")})).join(" ")," }")),e}),[]).join("\n")}function In(e,t){var o=u((0,i.useSettings)("background.backgroundImage","background.backgroundSize","typography.fontFamilies.custom","typography.fontFamilies.default","typography.fontFamilies.theme","typography.defaultFontSizes","typography.fontSizes.custom","typography.fontSizes.default","typography.fontSizes.theme","typography.customFontSize","typography.fontStyle","typography.fontWeight","typography.lineHeight","typography.textAlign","typography.textColumns","typography.textDecoration","typography.writingMode","typography.textTransform","typography.letterSpacing","spacing.padding","spacing.margin","spacing.blockGap","spacing.defaultSpacingSizes","spacing.customSpacingSize","spacing.spacingSizes.custom","spacing.spacingSizes.default","spacing.spacingSizes.theme","spacing.units","dimensions.aspectRatio","dimensions.minHeight","layout","border.color","border.radius","border.style","border.width","color.custom","color.palette.custom","color.customDuotone","color.palette.theme","color.palette.default","color.defaultPalette","color.defaultDuotone","color.duotone.custom","color.duotone.theme","color.duotone.default","color.gradients.custom","color.gradients.theme","color.gradients.default","color.defaultGradients","color.customGradient","color.background","color.link","color.text","color.heading","color.button","shadow"),56),n=o[0],l=o[1],a=o[2],c=o[3],s=o[4],p=o[5],d=o[6],m=o[7],f=o[8],b=o[9],y=o[10],h=o[11],v=o[12],g=o[13],_=o[14],k=o[15],w=o[16],O=o[17],x=o[18],C=o[19],E=o[20],P=o[21],S=o[22],j=o[23],I=o[24],T=o[25],D=o[26],A=o[27],B=o[28],H=o[29],N=o[30],z=o[31],M=o[32],F=o[33],R=o[34],V=o[35],L=o[36],U=o[37],q=o[38],G=o[39],W=o[40],Z=o[41],$=o[42],K=o[43],X=o[44],Y=o[45],J=o[46],Q=o[47],ee=o[48],te=o[49],oe=o[50],ne=o[51],re=o[52],le=o[53],ae=o[54],ie=o[55];return(0,r.useMemo)((function(){return{background:{backgroundImage:n,backgroundSize:l},color:{palette:{custom:L,theme:q,default:G},gradients:{custom:Y,theme:J,default:Q},duotone:{custom:$,theme:K,default:X},defaultGradients:ee,defaultPalette:W,defaultDuotone:Z,custom:V,customGradient:te,customDuotone:U,background:oe,link:ne,heading:le,button:ae,text:re},typography:{fontFamilies:{custom:a,default:c,theme:s},fontSizes:{custom:d,default:m,theme:f},customFontSize:b,defaultFontSizes:p,fontStyle:y,fontWeight:h,lineHeight:v,textAlign:g,textColumns:_,textDecoration:k,textTransform:O,letterSpacing:x,writingMode:w},spacing:{spacingSizes:{custom:I,default:T,theme:D},customSpacingSize:j,defaultSpacingSizes:S,padding:C,margin:E,blockGap:P,units:A},border:{color:z,radius:M,style:F,width:R},dimensions:{aspectRatio:B,minHeight:H},layout:N,parentLayout:t,shadow:ie}}),[n,l,a,c,s,p,d,m,f,b,y,h,v,g,_,k,O,x,w,C,E,P,S,j,I,T,D,A,B,H,N,t,z,M,F,R,V,L,U,q,G,W,Z,$,K,X,Y,J,Q,ee,te,oe,ne,re,le,ae,ie])}function Tn(e){var t,o,n=null!==(t=null==e||null===(o=e.toString)||void 0===o?void 0:o.call(e))&&void 0!==t?t:"";return function(e,t){return void 0===t&&(t={}),Cn(e,_n({delimiter:"-"},t))}(n=n.replace(/['\u2019]/,""),{splitRegexp:[/(?!(?:1ST|2ND|3RD|[4-9]TH)(?![a-z]))([a-z0-9])([A-Z])/g,/(?!(?:1st|2nd|3rd|[4-9]th)(?![a-z]))([0-9])([a-z])/g,/([A-Za-z])([0-9])/g,/([A-Z])([A-Z][a-z])/g]})}function Dn(e,t){if(e&&t)return"has-".concat(Tn(t),"-").concat(e)}var An=function t(o){if(null===o||"object"!==e(o)||Array.isArray(o))return o;var n=Object.entries(o).map((function(e){var o=u(e,2),n=o[0],r=o[1];return[n,t(r)]})).filter((function(e){return void 0!==u(e,2)[1]}));return n.length?Object.fromEntries(n):void 0};function Bn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t={};return(0,En.getCSSRules)(e).forEach((function(e){t[e.key]=e.value})),t}var Hn={link:"a",overlay:".wp-block-cover__background"};function Nn(e,t){if(!e||!t)return t;var o=e.split(","),n=t.split(","),r=[];return o.forEach((function(e){n.forEach((function(t){r.push("".concat(e.trim()," ").concat(t.trim()))}))})),r.join(", ")}var zn=function(e){var t=e.fieldType,o=e.attributes,l=e.uniqueClass,a=u((0,i.useSettings)("color.palette.custom","color.palette.theme","color.palette.default"),3),c=a[0],s=a[1],p=a[2],d=((0,r.useMemo)((function(){return[].concat(O(c||[]),O(s||[]),O(p||[]))}),[c,s,p]),o.style),m=function(e,t,o,n){var l=[{elementType:"link",pseudo:[":hover"]}];"image"===o&&l.push({elementType:"overlay",additionalRules:function(e,t){return[{selector:e,key:"opacity",value:parseFloat(t.dimRatio)/100}]}});var a=".".concat(n),i=null==e?void 0:e.elements;return{className:n,styles:(0,r.useMemo)((function(){if(i){var e=[];return l.forEach((function(o){var n=o.elementType,r=o.pseudo,l=(o.elements,o.additionalRules),c=null==i?void 0:i[n];if(c){var s=Nn(a,Hn[n]),u=jn(c,{selector:s,additionalRules:l?l(s,t):[]});e.push(u),r&&r.forEach((function(t){c[t]&&e.push(jn(c[t],{selector:Nn(a,"".concat(Hn[n]).concat(t))}))}))}})),e.length>0?e.join(""):void 0}}),[a,i,t])}}(d,o,t,l);if("text"===t){var f,b,y,h,v,g,_=o.backgroundColor,k=o.textColor,w=Dn("background-color",_),x=Dn("color",k),C=_||(null==d||null===(f=d.color)||void 0===f?void 0:f.background);_||(null==d||null===(b=d.color)||void 0===b||b.background);return{className:ee()(x,m.className,(g={},n(g,w,!!w),n(g,"has-background",C),n(g,"has-text-color",k||(null==d||null===(y=d.color)||void 0===y?void 0:y.text)),n(g,"has-link-color",null==d||null===(h=d.elements)||void 0===h||null===(v=h.link)||void 0===v?void 0:v.color),g)),style:Bn(d),css:m.styles}}return{className:ee()(m.className,{}),style:"",css:m.styles}};function Mn(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Fn(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Mn(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Mn(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var Rn=function(){return(0,r.createElement)("span",{className:"ct-video-indicator"},(0,r.createElement)("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"#fff"},(0,r.createElement)("path",{className:"ct-play-path",d:"M20,0C8.9,0,0,8.9,0,20s8.9,20,20,20s20-9,20-20S31,0,20,0z M16,29V11l12,9L16,29z"})))},Vn=function(e){var t,o=e.media,l=e.url,a=e.postId,c=e.attributes,s=e.attributes,p=s.aspectRatio,d=s.imageFit,m=s.width,f=s.height,b=s.imageAlign,h=s.image_hover_effect,v=s.videoThumbnail,_=s.minimumHeight,k=s.contentPosition,w=s.viewType,O=s.hasParallax,x=s.gradient,C=s.customGradient,E=(0,i.__experimentalUseBorderProps)(c),P=u((0,i.useSettings)("color.gradients","color.customGradient"),1)[0],S=C||(0,i.getGradientValueBySlug)(P,x),j=(0,i.__experimentalGetGradientClass)(x),I=(0,i.useBlockProps)({className:ee()("ct-dynamic-media",(t={},n(t,"align".concat(b),b),n(t,"wp-block-cover","cover"===w),n(t,"has-parallax","cover"===w&&O),n(t,y(k),"cover"===w&&k),n(t,"has-custom-content-position","cover"===w&&k),t)),style:{width:m,height:f}}),T=I.className.split(" ").find((function(e){return e.startsWith("wp-elements-")})),D=zn({fieldType:"image",attributes:c,uniqueClass:T}),A={height:p?"100%":f,width:!!p&&"100%",objectFit:d,aspectRatio:p},B=c.allowCustomContentAndWideSize,H=c.contentSize,N=c.wideSize,z=(0,i.useInnerBlocksProps)({className:ee()("wp-block-cover__inner-container",{"is-layout-constrained":B,"wp-block-cover-is-layout-constrained":B,"is-layout-flow":!B,"wp-block-cover-is-layout-flow":!B})},{template:[["core/paragraph",{align:"center",placeholder:"Add text…"}]],templateInsertUpdatesSelection:!1}),M=(null==o?void 0:o.has_video)&&"yes"===v||"none"!==h;if("cover"===w)return(0,r.createElement)("div",g({},I,{style:Fn(Fn(Fn(Fn({},I.style||{}),E.style||{}),"auto"!==p?{aspectRatio:p}:{}),{},{minHeight:_||("auto"!==p?"unset":void 0)}),className:ee()(I.className,E.className,D.className)}),(0,r.createElement)("style",null,"\n\t\t\t\t\t\t\t".concat(H?"#".concat(I.id," > .wp-block-cover__inner-container > :where(:not(.alignleft):not(.alignright):not(.alignfull)) {\n\t\t\t\t\t\t\t\t\t\tmax-width: ").concat(H,";\n\t\t\t\t\t\t\t\t\t}"):"","\n\n\t\t\t\t\t\t\t").concat(N?"#".concat(I.id," > .wp-block-cover__inner-container > .alignwide {\n\t\t\t\t\t\t\t\t\t\t\tmax-width: ").concat(N,";\n\t\t\t\t\t\t\t\t\t\t}"):"","\n\t\t\t\t\t\t"),D.css),(0,r.createElement)(gn,{attributes:c,url:l}),(0,r.createElement)("span",{"aria-hidden":"true",className:ee()("wp-block-cover__background",n({"wp-block-cover__gradient-background":!!S,"has-background-gradient":!!S},j,!!j)),style:{background:S}}),(0,r.createElement)("div",z));var F=(0,r.createElement)("img",{className:M?"":E.className,style:Fn(Fn({},M?{}:E.style),A),src:l,loading:"lazy"});return l&&a||(F=(0,r.createElement)("div",{className:ee()("ct-dynamic-data-placeholder",n({},E.className,!M)),style:Fn(Fn({},M?{}:E.style),A)},(0,r.createElement)("svg",{fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 60 60",preserveAspectRatio:"none",className:"ct-dynamic-data-placeholder-illustration","aria-hidden":"true",focusable:"false"},(0,r.createElement)("path",{vectorEffect:"non-scaling-stroke",d:"M60 60 0 0"})))),M&&(F=(0,r.createElement)("span",{"data-hover":h,className:"ct-dynamic-media-inner ".concat(E.className),style:Fn({},E.style)},F,null!=o&&o.has_video&&"yes"===v?(0,r.createElement)(Rn,null):null)),(0,r.createElement)("figure",I,F,null!=o&&o.has_video&&"yes"===v?(0,r.createElement)(Rn,null):null)},Ln=o(649),Un=o.n(Ln);o(975);Un()(console.error);var qn,Gn,Wn,Zn;qn={"(":9,"!":8,"*":7,"/":7,"%":7,"+":6,"-":6,"<":5,"<=":5,">":5,">=":5,"==":4,"!=":4,"&&":3,"||":2,"?":1,"?:":1},Gn=["(","?"],Wn={")":["("],":":["?","?:"]},Zn=/<=|>=|==|!=|&&|\|\||\?:|\(|!|\*|\/|%|\+|-|<|>|\?|\)|:/;var $n={"!":function(e){return!e},"*":function(e,t){return e*t},"/":function(e,t){return e/t},"%":function(e,t){return e%t},"+":function(e,t){return e+t},"-":function(e,t){return e-t},"<":function(e,t){return e<t},"<=":function(e,t){return e<=t},">":function(e,t){return e>t},">=":function(e,t){return e>=t},"==":function(e,t){return e===t},"!=":function(e,t){return e!==t},"&&":function(e,t){return e&&t},"||":function(e,t){return e||t},"?:":function(e,t,o){if(e)throw t;return o}};function Kn(e){var t=function(e){for(var t,o,n,r,l=[],a=[];t=e.match(Zn);){for(o=t[0],(n=e.substr(0,t.index).trim())&&l.push(n);r=a.pop();){if(Wn[o]){if(Wn[o][0]===r){o=Wn[o][1]||o;break}}else if(Gn.indexOf(r)>=0||qn[r]<qn[o]){a.push(r);break}l.push(r)}Wn[o]||a.push(o),e=e.substr(t.index+o.length)}return(e=e.trim())&&l.push(e),l.concat(a.reverse())}(e);return function(e){return function(e,t){var o,n,r,l,a,i,c=[];for(o=0;o<e.length;o++){if(a=e[o],l=$n[a]){for(n=l.length,r=Array(n);n--;)r[n]=c.pop();try{i=l.apply(null,r)}catch(e){return e}}else i=t.hasOwnProperty(a)?t[a]:+a;c.push(i)}return c[0]}(t,e)}}var Xn={contextDelimiter:"",onMissingKey:null};function Yn(e,t){var o;for(o in this.data=e,this.pluralForms={},this.options={},Xn)this.options[o]=void 0!==t&&o in t?t[o]:Xn[o]}Yn.prototype.getPluralForm=function(e,t){var o,n,r,l=this.pluralForms[e];return l||("function"!=typeof(r=(o=this.data[e][""])["Plural-Forms"]||o["plural-forms"]||o.plural_forms)&&(n=function(e){var t,o,n;for(t=e.split(";"),o=0;o<t.length;o++)if(0===(n=t[o].trim()).indexOf("plural="))return n.substr(7)}(o["Plural-Forms"]||o["plural-forms"]||o.plural_forms),r=function(e){var t=Kn(e);return function(e){return+t({n:e})}}(n)),l=this.pluralForms[e]=r),l(t)},Yn.prototype.dcnpgettext=function(e,t,o,n,r){var l,a,i;return l=void 0===r?0:this.getPluralForm(e,r),a=o,t&&(a=t+this.options.contextDelimiter+o),(i=this.data[e][a])&&i[l]?i[l]:(this.options.onMissingKey&&this.options.onMissingKey(o,e),0===l?o:n)};const Jn={plural_forms:e=>1===e?0:1},Qn=/^i18n\.(n?gettext|has_translation)(_|$)/;const er=((e,t,o)=>{const n=new Yn({}),r=new Set,l=()=>{r.forEach((e=>e()))},a=function(e){var t;let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default";n.data[o]={...n.data[o],...e},n.data[o][""]={...Jn,...null===(t=n.data[o])||void 0===t?void 0:t[""]},delete n.pluralForms[o]},i=(e,t)=>{a(e,t),l()},c=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default",t=arguments.length>1?arguments[1]:void 0,o=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,l=arguments.length>4?arguments[4]:void 0;return n.data[e]||a(void 0,e),n.dcnpgettext(e,t,o,r,l)},s=function(){return arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default"},u=(e,t,n)=>{let r=c(n,t,e);return o?(r=o.applyFilters("i18n.gettext_with_context",r,e,t,n),o.applyFilters("i18n.gettext_with_context_"+s(n),r,e,t,n)):r};if(e&&i(e,t),o){const e=e=>{Qn.test(e)&&l()};o.addAction("hookAdded","core/i18n",e),o.addAction("hookRemoved","core/i18n",e)}return{getLocaleData:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default";return n.data[e]},setLocaleData:i,addLocaleData:function(e){var t;let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default";n.data[o]={...n.data[o],...e,"":{...Jn,...null===(t=n.data[o])||void 0===t?void 0:t[""],...null==e?void 0:e[""]}},delete n.pluralForms[o],l()},resetLocaleData:(e,t)=>{n.data={},n.pluralForms={},i(e,t)},subscribe:e=>(r.add(e),()=>r.delete(e)),__:(e,t)=>{let n=c(t,void 0,e);return o?(n=o.applyFilters("i18n.gettext",n,e,t),o.applyFilters("i18n.gettext_"+s(t),n,e,t)):n},_x:u,_n:(e,t,n,r)=>{let l=c(r,void 0,e,t,n);return o?(l=o.applyFilters("i18n.ngettext",l,e,t,n,r),o.applyFilters("i18n.ngettext_"+s(r),l,e,t,n,r)):l},_nx:(e,t,n,r,l)=>{let a=c(l,r,e,t,n);return o?(a=o.applyFilters("i18n.ngettext_with_context",a,e,t,n,r,l),o.applyFilters("i18n.ngettext_with_context_"+s(l),a,e,t,n,r,l)):a},isRTL:()=>"rtl"===u("ltr","text direction"),hasTranslation:(e,t,r)=>{var l,a;const i=t?t+""+e:e;let c=!(null===(l=n.data)||void 0===l||null===(a=l[null!=r?r:"default"])||void 0===a||!a[i]);return o&&(c=o.applyFilters("i18n.has_translation",c,e,t,r),c=o.applyFilters("i18n.has_translation_"+s(r),c,e,t,r)),c}}})(void 0,void 0,window.wp.hooks.defaultHooks);er.getLocaleData.bind(er),er.setLocaleData.bind(er),er.resetLocaleData.bind(er),er.subscribe.bind(er);const tr=er.__.bind(er);er._x.bind(er),er._n.bind(er),er._nx.bind(er),er.isRTL.bind(er),er.hasTranslation.bind(er);function or(e){var t=e.postId,o=e.postType,n=(0,k.useSelect)((function(e){var n,r=e(d.store),l=r.getEditedEntityRecord,a=r.getUser,i=null===(n=l("postType",o,t))||void 0===n?void 0:n.author;return{authorDetails:i?a(i):null}}),[o,t]).authorDetails,r=null!=n&&n.avatar_urls?Object.values(n.avatar_urls):null,l=(null!=n&&n.avatar_urls&&Object.keys(n.avatar_urls),(0,k.useSelect)((function(e){return(0,e(i.store).getSettings)().__experimentalDiscussionSettings})).avatarURL);return{src:r?r[r.length-1]:l}}function nr(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function rr(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?nr(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):nr(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var lr=function(e){var t=e.postId,o=e.postType,l=e.attributes,a=e.attributes,c=a.avatar_size,s=a.imageAlign,u=a.has_field_link,p=(0,i.useBlockProps)({className:ee()("ct-dynamic-media",n({},"align".concat(s),s)),style:{}}),d=(0,i.__experimentalUseBorderProps)(l),m=or({postId:t,postType:o}),f=(0,un.addQueryArgs)((0,un.removeQueryArgs)(m.src,["s"]),{s:2*c}),b=rr({},d.style),y=(0,r.createElement)("img",{style:rr(rr({},b),{},{width:"".concat(c,"px"),height:"".concat(c,"px")}),src:f,className:ee()("avatar","avatar-"+c,"photo",d.className)});return u&&(y=(0,r.createElement)("a",{href:"#"},y)),(0,r.createElement)("figure",p,y)},ar={"wp:term_title":(0,l.__)("Term Title","blocksy-companion"),"wp:term_description":(0,l.__)("Term Description","blocksy-companion"),"wp:term_count":(0,l.__)("Term Count","blocksy-companion")},ir={"wp:term_title":"name","wp:term_description":"description","wp:term_count":"count"},cr=function(e){var t=e.termId,o=e.taxonomy,l=e.attributes,a=e.attributes.has_field_link,i=(e.fieldsDescriptor,(0,k.useSelect)((function(e){return{terms:e("core").getEntityRecords("taxonomy",o,{per_page:1,include:[t]})||[]}})).terms);if(!i.length)return ar[l.field]||ar["wp:term_title"];var c="span",s={};"yes"===a&&(c="a",s.href="#",s.rel="noopener noreferrer");var u=i[0][ir[l.field]];return(0,r.createElement)(c,g({},s,{className:ee()(n({},"ct-term-".concat(i[0].id),"yes"===l.termAccentColor),l.termClass),dangerouslySetInnerHTML:{__html:u}}))};var sr=function(e){var t=e.termImage,o=e.termIcon,n=e.attributes,l=e.attributes,a=l.sizeSlug,i="icon"===l.imageSource?null==o?void 0:o.attachment_id:null==t?void 0:t.attachment_id,c=function(e,t){var o,n,r;return(null==e||null===(o=e.media_details)||void 0===o||null===(n=o.sizes)||void 0===n||null===(r=n[t])||void 0===r?void 0:r.source_url)||(null==e?void 0:e.source_url)}((0,k.useSelect)((function(e){var t=e(d.store).getMedia;return{media:i&&t(i,{context:"view"})}}),[i]).media,a);return(0,r.createElement)(Vn,{attributes:n,url:c,postId:!0})};var ur=function(e){var t=e.postType,o=e.postId,n=e.attributes,l=e.attributes.sizeSlug,a=u((0,d.useEntityProp)("postType",t,"featured_media",o),2),i=a[0],c=(a[1],(0,k.useSelect)((function(e){var t=e(d.store).getMedia;return{media:i&&t(i,{context:"view"})}}),[i]).media),s=function(e,t){var o,n,r;return(null==e||null===(o=e.media_details)||void 0===o||null===(n=o.sizes)||void 0===n||null===(r=n[t])||void 0===r?void 0:r.source_url)||(null==e?void 0:e.source_url)}(c,l);return(0,r.createElement)(Vn,{postId:o,attributes:n,url:s,media:c})},pr=function(e){e.tagName;var t=e.children,o=e.before,n=e.after,l=x(e,["tagName","children","before","after"]),a=u((0,r.useState)(null),2),i=(a[0],a[1]),c=o||"",s=n||"",p=(0,r.useRef)(null),d=-1!==c.indexOf("<")||-1!==s.indexOf("<");return(0,r.useEffect)((function(){d&&i(Math.random())}),[o,n]),d?(0,r.createElement)(r.Fragment,null,(0,r.createElement)("span",g({ref:p,dangerouslySetInnerHTML:{__html:"".concat(o,"<span></span>").concat(n)}},l)),p.current&&p.current.querySelector("span")&&(0,r.createPortal)(t,p.current.querySelector("span"))):(0,r.createElement)(r.Fragment,null,c,t,s)};function dr(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function mr(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?dr(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):dr(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var fr=function(e){var t=e.fieldDescriptor,o=e.fieldsDescriptor,a=e.attributes,c=e.attributes,s=c.align,p=c.tagName,d=c.before,m=c.after,f=c.fallback,b=e.postId,y=e.postType,h=e.termId,v=e.taxonomy,_=u((0,r.useState)(!1),2),k=(_[0],_[1],(0,r.useRef)(null)),w=((0,r.useRef)(null),(0,i.useBlockProps)({className:ee()("ct-dynamic-data",n({},"has-text-align-".concat(s),s)),ref:k})),O=w.className.split(" ").find((function(e){return e.startsWith("wp-elements-")})),x=zn({fieldType:"text",attributes:a,uniqueClass:O}),C=(0,i.__experimentalUseBorderProps)(a),E=null;"archive_title"===t.id&&(E=function(){return(0,l.__)("Archive Title","blocksy-companion")}),"archive_description"===t.id&&(E=function(){return(0,l.__)("Archive Description","blocksy-companion")}),"term_title"!==t.id&&"term_description"!==t.id&&"term_count"!==t.id||(E=cr),"title"===t.id&&(E=on),"excerpt"===t.id&&(E=rn),"date"===t.id&&(E=an),"comments"===t.id&&(E=mn),"terms"===t.id&&(E=yn),"author"===t.id&&(E=bn);var P="";return o&&o.dynamic_styles&&(P=o.dynamic_styles),x.css&&(P+=x.css),E?(0,r.createElement)(r.Fragment,null,(0,r.createElement)(p,g({},w,C,{style:mr(mr(mr({},w.style||{}),C.style||{}),x.style||{}),className:ee()(w.className,C.className,x.className)}),P&&(0,r.createElement)("style",null,P),(0,r.createElement)(pr,{before:d,after:m},(0,r.createElement)(E,{attributes:a,postId:b,postType:y,termId:h,taxonomy:v,fallback:f,fieldsDescriptor:o})))):null},br=function(e){var t=e.fieldDescriptor;return"featured_image"===t.id?(0,r.createElement)(ur,e):"author_avatar"===t.id?(0,r.createElement)(lr,e):"term_image"===t.id?(0,r.createElement)(sr,e):"archive_image"===t.id?(0,r.createElement)(Vn,e):(0,r.createElement)(fr,e)};function yr(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function hr(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?yr(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):yr(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var vr=function(e){var t=e.postId,o=e.fieldDescriptor,l=u((0,r.useState)({}),2),a=l[0],i=l[1],c=o.provider,s=o.id,p=x(o,["provider","id"]),d=(0,r.useMemo)((function(){var e="".concat(wp.ajax.settings.url,"?action=blocksy_dynamic_data_block_custom_field_data"),o=hr({post_id:t,field_provider:c,field_id:s},p);return{url:e,body:o,cacheKey:I(hr(hr({},o),{},{url:e}))}}),[t,c,s,p]);return(0,r.useEffect)((function(){R(d.url,d.body).then((function(e){return e.json()})).then((function(e){var t=e.success,o=e.data;t&&i((function(e){return hr(hr({},e),{},n({},d.cacheKey,o.field_data))}))}))}),[d]),{fieldData:a[d.cacheKey]?a[d.cacheKey]:null}};function gr(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function _r(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?gr(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):gr(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var kr=function(e){var t=e.fieldDescriptor,o=e.attributes,l=e.attributes,a=l.align,c=l.tagName,s=l.before,u=l.after,p=l.fallback,d=e.fieldData,m=(e.colors,(0,i.useBlockProps)({className:ee()("ct-dynamic-data",n({},"has-text-align-".concat(a),a))})),f=m.className.split(" ").find((function(e){return e.startsWith("wp-elements-")})),b=zn({fieldType:"text",attributes:o,uniqueClass:f}),y=(0,i.__experimentalUseBorderProps)(o),h=!1,v=d||"";v||(h=!0,v=p||"Dynamic data: ".concat(null==t?void 0:t.label)),!h&&v&&"string"==typeof v&&(v=s+v+u);var _="";return b.css&&(_+=b.css),(0,r.createElement)(r.Fragment,null,(0,r.createElement)(c,g({},m,y,{style:_r(_r(_r({},m.style||{}),y.style||{}),b.style||{}),className:ee()(m.className,y.className,b.className),dangerouslySetInnerHTML:{__html:v}})),_&&(0,r.createElement)("style",null,_))},wr=function(e){var t,o,n,l=e.fieldData,a=e.attributes,i=e.attributes.sizeSlug,c=e.postId,s=null==l||null===(t=l.value)||void 0===t?void 0:t.url;return null!=l&&null!==(o=l.value)&&void 0!==o&&null!==(n=o.sizes)&&void 0!==n&&n[i]&&(s="string"==typeof l.value.sizes[i]?l.value.sizes[i]:l.value.sizes[i].url),(0,r.createElement)(Vn,{postId:c,attributes:a,url:s})},Or=function(e){var t=e.fieldDescriptor,o=e.postId,n=(e.postType,e.attributes),l=vr({postId:o,fieldDescriptor:t}).fieldData;return"image"===t.type?(0,r.createElement)(wr,{fieldData:l,fieldDescriptor:t,attributes:n,postId:o}):(0,r.createElement)(kr,{fieldData:l,fieldDescriptor:t,attributes:n,postId:o})},xr=function(e){var t=e.product;return(0,r.createElement)(r.RawHTML,null,null==t?void 0:t.price_html)},Cr=function(e){var t=e.product;return null!=t&&t.is_in_stock?(0,l.__)("In Stock","blocksy-companion"):(0,l.__)("Out of Stock","blocksy-companion")},Er=function(e){var t=e.product,o=parseFloat(null==t?void 0:t.average_rating)/5*100+"%";return(0,r.createElement)("div",{className:"star-rating",role:"img","aria-label":"Rated 2.15 out of 5"},(0,r.createElement)("span",{style:{width:o}},(0,l.sprintf)((0,l.__)("Rated %s out of 5","blocksy-companion"),null==t?void 0:t.average_rating)))},Pr=function(e){var t=e.product;return(null==t?void 0:t.sku)||""},Sr=function(e){var t=e.product,o=e.attributes,n=vr({postId:t.id,fieldDescriptor:{provider:"woo",id:"brands"}}).fieldData;return(0,r.createElement)("div",{className:"ct-product-brands",style:{"--product-brand-logo-size":"".concat(o.brands_size,"px"),"--product-brands-gap":"".concat(o.brands_gap,"px")}},n&&n.map((function(e){return e.image&&e.image.url?(0,r.createElement)("span",{key:e.slug,className:"ct-media-container"},(0,r.createElement)("img",{src:e.image.url,alt:e.name})):(0,r.createElement)("span",{key:e.slug,dangerouslySetInnerHTML:{__html:e.name}})})))},jr=function(e){var t=e.postId,o=(e.postType,e.fallback),l=e.attributes,a=e.attributes,i=a.attribute,c=a.separator,s=(e.fieldsDescriptor,vr({postId:t,fieldDescriptor:{provider:"woo",id:"attributes",attribute:i}}).fieldData),u="Attributes: ".concat(i);if(!t||!s)return"".concat(u);if(0===s.length)return o||"".concat(u);return s.map((function(e,t){return(0,r.createElement)(React.Fragment,null,(0,r.createElement)("span",{className:ee()(n({},"ct-term-".concat(e.term_id),"yes"===l.termAccentColor),l.termClass),dangerouslySetInnerHTML:{__html:e.name}}),t!==s.length-1?c.replace(/ /g," "):"")}))};function Ir(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Tr(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Ir(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Ir(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var Dr=function(e){var t=e.fieldDescriptor,o=e.fieldsDescriptor,l=e.attributes,a=e.attributes,c=a.align,s=a.tagName,u=a.before,d=a.after,m=a.fallback,f=e.postId,b=e.postType,y=e.termId,h=e.taxonomy,v=(e.colors,(0,r.useRef)(null)),_=((0,r.useRef)(null),wc.wcBlocksSharedContext.useProductDataContext()),k=(_.product,_.isLoading),w=(0,i.useBlockProps)({className:ee()("ct-dynamic-data",n({},"has-text-align-".concat(c),c)),ref:v}),O=w.className.split(" ").find((function(e){return e.startsWith("wp-elements-")})),x=zn({fieldType:"text",attributes:l,uniqueClass:O}),C=(0,i.__experimentalUseBorderProps)(l);if(k)return(0,r.createElement)(p.Spinner,null);var E=null;"price"===t.id&&(E=xr),"stock_status"===t.id&&(E=Cr),"brands"===t.id&&(E=Sr),"rating"===t.id&&(E=Er),"sku"===t.id&&(E=Pr),"attributes"===t.id&&(E=jr);var P="";return x.css&&(P+=x.css),E?(0,r.createElement)(s,g({},w,C,{style:Tr(Tr(Tr({},w.style||{}),C.style||{}),x.style||{}),className:ee()(w.className,C.className,x.className)}),P&&(0,r.createElement)("style",null,P),(0,r.createElement)(pr,{before:u,after:d},(0,r.createElement)(E,{attributes:l,postId:f,postType:b,termId:y,taxonomy:h,fallback:m,fieldsDescriptor:o}))):null},Ar=function(e){e.fieldDescriptor;var t=e.postId;return(0,wc.wcBlocksSharedHocs.withProductDataContext)(Dr)(Tr(Tr({},e),{},{productId:t}))},Br=function(e){var t=e.fieldDescriptor,o=e.fieldsDescriptor,n=e.postId,a=e.postType,c=e.termId,s=e.taxonomy,u=e.termIcon,p=e.termImage,d=e.attributes,m=e.attributes,f=(m.tagName,m.align,m.field,m.before,m.after,m.fallback,e.colors);if(!t){var b=(0,i.useBlockProps)({className:"ct-block-notice components-notice is-warning"});return(0,r.createElement)("div",b,(0,l.__)("Field not found","ct-blocks"))}return"woo"===t.provider?(0,r.createElement)(Ar,{fieldDescriptor:t,attributes:d,postId:n,postType:a,colors:f}):"wp"===t.provider?(0,r.createElement)(br,{fieldsDescriptor:o,fieldDescriptor:t,attributes:d,postId:n,postType:a,termId:c,taxonomy:s,termImage:p,termIcon:u,colors:f}):(0,r.createElement)(Or,{fieldDescriptor:t,attributes:d,postId:n,postType:a,colors:f})};function Hr(e){var t=e.level,o=e.isPressed,n=void 0!==o&&o,l={h1:"M9 5h2v10H9v-4H5v4H3V5h2v4h4V5zm6.6 0c-.6.9-1.5 1.7-2.6 2v1h2v7h2V5h-1.4z",h2:"M7 5h2v10H7v-4H3v4H1V5h2v4h4V5zm8 8c.5-.4.6-.6 1.1-1.1.4-.4.8-.8 1.2-1.3.3-.4.6-.8.9-1.3.2-.4.3-.8.3-1.3 0-.4-.1-.9-.3-1.3-.2-.4-.4-.7-.8-1-.3-.3-.7-.5-1.2-.6-.5-.2-1-.2-1.5-.2-.4 0-.7 0-1.1.1-.3.1-.7.2-1 .3-.3.1-.6.3-.9.5-.3.2-.6.4-.8.7l1.2 1.2c.3-.3.6-.5 1-.7.4-.2.7-.3 1.2-.3s.9.1 1.3.4c.*******.5 1.1 0 .4-.1.8-.4 1.1-.3.5-.6.9-1 1.2-.4.4-1 .9-1.6 1.4-.6.5-1.4 1.1-2.2 1.6V15h8v-2H15z",h3:"M12.1 12.2c.******* *******.2.9.3 ******* 0 1-.1 1.4-.3.3-.1.5-.5.5-.8 0-.2 0-.4-.1-.6-.1-.2-.3-.3-.5-.4-.3-.1-.7-.2-1-.3-.5-.1-1-.1-1.5-.1V9.1c.7.1 1.5-.1 2.2-.4.4-.2.6-.5.6-.9 0-.3-.1-.6-.4-.8-.3-.2-.7-.3-1.1-.3-.4 0-.8.1-1.1.3-.4.2-.7.4-1.1.6l-1.2-1.4c.5-.4 1.1-.7 1.6-.9.5-.2 1.2-.3 1.8-.3.5 0 1 .1 *******.1.8.3 *******.*******.*******.7.3 1.1 0 .5-.2.9-.5 1.3-.4.4-.9.7-1.5.9v.1c.6.1 1.2.4 *******.4.7.9.7 1.5 0 .4-.1.8-.3 1.2-.2.4-.5.7-.9.9-.4.3-.9.4-1.3.5-.5.1-1 .2-1.6.2-.8 0-1.6-.1-2.3-.4-.6-.2-1.1-.6-1.6-1l1.1-1.4zM7 9H3V5H1v10h2v-4h4v4h2V5H7v4z",h4:"M9 15H7v-4H3v4H1V5h2v4h4V5h2v10zm10-2h-1v2h-2v-2h-5v-2l4-6h3v6h1v2zm-3-2V7l-2.8 4H16z",h5:"M12.1 12.2c.4.3.7.5 1.1.7.4.2.9.3 1.3.3.5 0 1-.1 1.4-.4.4-.3.6-.7.6-1.1 0-.4-.2-.9-.6-1.1-.4-.3-.9-.4-1.4-.4H14c-.1 0-.3 0-.4.1l-.4.1-.5.2-1-.6.3-5h6.4v1.9h-4.3L14 8.8c.2-.1.5-.1.7-.2.2 0 .5-.1.7-.1.5 0 .9.1 1.4.2.4.1.8.3 1.1.6.3.2.6.6.8.9.2.4.3.9.3 1.4 0 .5-.1 1-.3 1.4-.2.4-.5.8-.9 1.1-.4.3-.8.5-1.3.7-.5.2-1 .3-1.5.3-.8 0-1.6-.1-2.3-.4-.6-.2-1.1-.6-1.6-1-.1-.1 1-1.5 1-1.5zM9 15H7v-4H3v4H1V5h2v4h4V5h2v10z",h6:"M9 15H7v-4H3v4H1V5h2v4h4V5h2v10zm8.6-7.5c-.2-.2-.5-.4-.8-.5-.6-.2-1.3-.2-1.9 0-.3.1-.6.3-.8.5l-.6.9c-.2.5-.2.9-.2 1.4.4-.3.8-.6 1.2-.8.4-.2.8-.3 1.3-.3.4 0 .8 0 1.2.2.4.1.7.3 1 .6.3.3.5.6.7.9.2.4.3.8.3 1.3s-.1.9-.3 1.4c-.2.4-.5.7-.8 1-.4.3-.8.5-1.2.6-1 .3-2 .3-3 0-.5-.2-1-.5-1.4-.9-.4-.4-.8-.9-1-1.5-.2-.6-.3-1.3-.3-2.1s.1-1.6.4-2.3c.2-.6.6-1.2 1-1.6.4-.4.9-.7 1.4-.9.6-.3 1.1-.4 1.7-.4.7 0 1.4.1 2 .3.5.2 1 .5 1.4.8 0 .1-1.3 1.4-1.3 1.4zm-2.4 5.8c.2 0 .4 0 .6-.1.2 0 .4-.1.5-.2.1-.1.3-.3.4-.5.1-.2.1-.5.1-.7 0-.4-.1-.8-.4-1.1-.3-.2-.7-.3-1.1-.3-.3 0-.7.1-1 .2-.4.2-.7.4-1 .7 0 .3.1.7.3 1 .1.2.3.4.4.6.2.1.3.3.5.3.2.1.5.2.7.1z",p:"M6.2 15V5h3.2c1.2 0 2 .1 2.4.2.6.2 1 .5 1.4 1 .4.5.6 1.1.6 1.9 0 .6-.1 1.1-.3 1.6-.2.4-.5.8-.9 1-.3.2-.7.4-1 .5-.5.1-1.2.1-2.1.1H8.2V15h-2zm2-8.3v2.8h1.1c.8 0 1.3 0 1.6-.2.3-.1.5-.3.6-.5.2-.2.2-.5.2-.8 0-.4-.1-.7-.3-.9-.2-.2-.5-.4-.8-.4-.2 0-.7-.1-1.5-.1.1.1-.9.1-.9.1z",span:"M8.2 12c.2 1 .9 1.4 2 1.4s1.6-.4 1.6-1.2c0-.8-.5-1.1-2.1-1.5-2.7-.6-3.3-1.5-3.3-2.9C6.4 6.2 7.6 5 9.9 5c2.6 0 3.6 1.4 3.7 2.8h-2.1c-.1-.6-.4-1.2-1.7-1.2-.9 0-1.4.4-1.4 1 0 .7.4.9 2 1.3 2.9.7 3.6 1.7 3.6 3.1 0 1.8-1.3 3-3.9 3-2.5 0-3.8-1.2-4-3h2.1z",div:"M5.8 5h3.7c.8 0 1.5.1 1.9.2.6.2 1.1.5 1.5.9.4.4.7 1 1 1.6.2.6.3 1.4.3 2.4 0 .8-.1 1.5-.3 2.1-.3.7-.6 1.3-1.1 1.8-.3.3-.8.6-1.4.8-.4.1-1 .2-1.8.2H5.8V5zm2 1.7v6.6h1.5c.6 0 1 0 1.2-.1.3-.1.6-.2.8-.4.2-.2.4-.5.5-1 .1-.4.2-1.1.2-1.8 0-.8-.1-1.4-.2-1.8-.1-.4-.3-.7-.6-1-.2-.2-.6-.4-.9-.5-.3-.1-.8-.1-1.6-.1l-.9.1z"};return l.hasOwnProperty(t)?(0,r.createElement)(p.SVG,{width:"24",height:"24",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",isPressed:n},(0,r.createElement)(p.Path,{d:l[t]})):null}function Nr(e){var t=e.tagName,o=e.onChange;return(0,r.createElement)(p.ToolbarDropdownMenu,{popoverProps:{className:"block-library-heading-level-dropdown"},icon:(0,r.createElement)(Hr,{level:t}),label:(0,l.__)("Change heading level","blocksy-companion"),controls:["h1","h2","h3","h4","h5","h6","p","span","div"].map((function(e){var n=e===t;return{icon:(0,r.createElement)(Hr,{level:e,isPressed:n}),label:e,title:{h1:(0,l.__)("Heading 1","blocksy-companion"),h2:(0,l.__)("Heading 2","blocksy-companion"),h3:(0,l.__)("Heading 3","blocksy-companion"),h4:(0,l.__)("Heading 4","blocksy-companion"),h5:(0,l.__)("Heading 5","blocksy-companion"),h6:(0,l.__)("Heading 6","blocksy-companion"),p:(0,l.__)("Paragraph","blocksy-companion"),span:(0,l.__)("Span","blocksy-companion"),div:(0,l.__)("Div","blocksy-companion")}[e],isActive:n,onClick:function(){o(e)},role:"menuitemradio"}}))})}var zr=function(e){var t,o,n,l,a,c,s,p,d,m,b,y,h,v=e.fieldDescriptor,_=e.attributes,k=e.attributes,w=(k.name,k.style),x=k.align,C=k.imageAlign,E=k.contentPosition,P=k.minimumHeight,S=k.viewType,j=e.setAttributes,I=u((0,r.useState)(""),2),T=I[0],D=I[1],A="100vh"===P,B=In(),H=[].concat(O(null!==(t=null===(o=B.color.duotone)||void 0===o?void 0:o.custom)&&void 0!==t?t:[]),O(null!==(n=null===(l=B.color.duotone)||void 0===l?void 0:l.theme)&&void 0!==n?n:[]),O(null!==(a=null===(c=B.color.duotone)||void 0===c?void 0:c.default)&&void 0!==a?a:[])),N=([].concat(O(null!==(s=null===(p=B.color.palette)||void 0===p?void 0:p.custom)&&void 0!==s?s:[]),O(null!==(d=null===(m=B.color.palette)||void 0===m?void 0:m.theme)&&void 0!==d?d:[]),O(null!==(b=null===(y=B.color.palette)||void 0===y?void 0:y.default)&&void 0!==b?b:[])),null==w||null===(h=w.color)||void 0===h?void 0:h.duotone);Array.isArray(N)||function(e,t){if(e){var o=null==t?void 0:t.find((function(t){var o=t.slug;return e==="var:preset|duotone|".concat(o)}));o&&o.colors}}(N,H);return(0,r.createElement)(i.BlockControls,{group:"block"},f(v)?(0,r.createElement)(React.Fragment,null,(0,r.createElement)(i.BlockAlignmentControl,g({},"wp"===v.provider&&"author_avatar"===v.id?{controls:["none","left","center","right"]}:{},{value:C,onChange:function(e){return j({imageAlign:e})}})),null,"cover"===S?(0,r.createElement)(React.Fragment,null,(0,r.createElement)(i.__experimentalBlockAlignmentMatrixControl,{label:tr("Change content position"),value:E,onChange:function(e){return j({contentPosition:e})}}),(0,r.createElement)(i.__experimentalBlockFullHeightAligmentControl,{isActive:A,onToggle:function(){A?j({minimumHeight:T}):(D(P),j({minimumHeight:"100vh",aspectRatio:"auto"}))}})):null):(0,r.createElement)(React.Fragment,null,(0,r.createElement)(i.AlignmentControl,{value:x,onChange:function(e){return j({align:e})}}),(0,r.createElement)(Nr,{tagName:_.tagName,onChange:function(e){return j({tagName:e})}})))},Mr=(0,Z.getOptionsForBlock)("dynamic-data"),Fr=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).termId,t=document.body.classList.contains("post-type-ct_content_block"),o=[];return e&&(o=[{id:"term_title",label:(0,l.__)("Term Title","blocksy-companion")},{id:"term_description",label:(0,l.__)("Term Description","blocksy-companion")},{id:"term_image",label:(0,l.__)("Term Image","blocksy-companion")},{id:"term_count",label:(0,l.__)("Term Count","blocksy-companion")}]),e||(o=[{id:"title",label:(0,l.__)("Title","blocksy-companion")},{id:"excerpt",label:(0,l.__)("Excerpt","blocksy-companion")},{id:"date",label:(0,l.__)("Post Date","blocksy-companion")},{id:"comments",label:(0,l.__)("Comments","blocksy-companion")},{id:"terms",label:(0,l.__)("Terms","blocksy-companion")},{id:"author",label:(0,l.__)("Author","blocksy-companion")},{id:"featured_image",label:(0,l.__)("Featured Image","blocksy-companion")},{id:"author_avatar",label:(0,l.__)("Author Avatar","blocksy-companion")}]),!e&&t&&(o=[].concat(O(o),[{id:"archive_title",label:(0,l.__)("Archive Title","blocksy-companion")},{id:"archive_description",label:(0,l.__)("Archive Description","blocksy-companion")},{id:"archive_image",label:(0,l.__)("Archive Image","blocksy-companion")}])),{provider:"wp",fields:o}},Rr=function(e){var t=e.postId,o=e.postType,n=e.termId,a=(e.taxonomy,ke(o)),i=u((0,r.useState)([]),2),c=i[0],s=i[1];(0,r.useEffect)((function(){t&&!n&&R("".concat(wp.ajax.settings.url,"?action=blocksy_blocks_retrieve_dynamic_data_descriptor"),{post_id:t}).then((function(e){return e.json()})).then((function(e){e.success;var t=e.data;s(t.fields)}))}),[t,n]);var p={fields:[Fr({termId:n})]},d=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(void 0===window.wc||"product"!==e)return null;var o=(t||[]).find((function(e){return"product_brand"===e.slug}));return{provider:"woo",fields:[{id:"price",label:(0,l.__)("Price","blocksy-companion")},{id:"rating",label:(0,l.__)("Rating","blocksy-companion")},{id:"stock_status",label:(0,l.__)("Stock Status","blocksy-companion")},{id:"sku",label:(0,l.__)("SKU","blocksy-companion")},{id:"attributes",label:(0,l.__)("Attributes","blocksy-companion")}].concat(O(o?[{id:"brands",label:(0,l.__)("Brands","blocksy-companion")}]:[]))}}(o,a);return d&&p.fields.push(d),c.length>0&&(p.fields=[].concat(O(p.fields),O(c))),{fieldsDescriptor:p,options:Mr,fieldsChoices:p.fields.reduce((function(e,t){return[].concat(O(e),O(t.fields.filter((function(e){return"wp"!==t.provider||"terms"!==e.id||a&&a.length>0})).map((function(e){return{group:(o=t.provider,{wp:"WordPress",woo:"WooCommerce",acf:"ACF",metabox:"MetaBox",custom:(0,l.__)("Custom","blocksy-companion"),toolset:"Toolset",jetengine:"Jet Engine",pods:"Pods",acpt:"ACPT"}[o]||(0,l.__)("Unknown","blocksy-companion")),key:"".concat(t.provider,":").concat(e.id),value:e.label};var o}))))}),[])}},Vr=function(e){var t=e.sizeSlug,o=e.onChange,n=e.clientId,a=(0,k.useSelect)((function(e){return e(i.store).getSettings().imageSizes}),[]).map((function(e){var t=e.name;return{value:e.slug,label:t}}));return a.length?(0,r.createElement)(p.__experimentalToolsPanelItem,{hasValue:function(){return!!t},label:(0,l.__)("Resolution","blocksy-companion"),onDeselect:function(){return o(void 0)},resetAllFilter:function(){return{sizeSlug:void 0}},isShownByDefault:!1,key:n},(0,r.createElement)(p.SelectControl,{__nextHasNoMarginBottom:!0,label:(0,l.__)("Resolution","blocksy-companion"),value:t||"full",options:a,onChange:function(e){return o(e)},help:(0,l.__)("Select the size of the source image.","blocksy-companion")})):null},Lr=function(e){var t=e.clientId,o=e.attributes,a=o.aspectRatio,c=o.imageFit,s=o.width,u=o.height,d=o.sizeSlug,m=o.viewType,f=o.minimumHeight,b=e.setAttributes,y=(0,p.__experimentalUseCustomUnits)({availableUnits:(0,i.useSetting)("spacing.units")||["px","%","vw","em","rem"]}),h=function(e,t){var o=parseFloat(t);isNaN(o)&&t||b(n({},e,o<0?"0":t))};return(0,r.createElement)(p.__experimentalToolsPanel,{label:"cover"===m?(0,l.__)("Block Settings","blocksy-companion"):(0,l.__)("Image Settings","blocksy-companion"),resetAll:function(){b({aspectRatio:"auto",width:void 0,height:void 0,sizeSlug:void 0,minimumHeight:void 0})}},(0,r.createElement)(p.__experimentalToolsPanelItem,{hasValue:function(){return!!a},label:(0,l.__)("Aspect Ratio","blocksy-companion"),onDeselect:function(){return b({aspectRatio:void 0})},resetAllFilter:function(){return{aspectRatio:"auto"}},isShownByDefault:!0,key:t},(0,r.createElement)(p.SelectControl,{__nextHasNoMarginBottom:!0,label:(0,l.__)("Aspect Ratio","blocksy-companion"),value:a,options:[{label:(0,l.__)("Original","blocksy-companion"),value:"auto"},{label:(0,l.__)("Square","blocksy-companion"),value:"1"},{label:(0,l.__)("16:9","blocksy-companion"),value:"16/9"},{label:(0,l.__)("4:3","blocksy-companion"),value:"4/3"},{label:(0,l.__)("3:2","blocksy-companion"),value:"3/2"},{label:(0,l.__)("9:16","blocksy-companion"),value:"9/16"},{label:(0,l.__)("3:4","blocksy-companion"),value:"3/4"},{label:(0,l.__)("2:3","blocksy-companion"),value:"2/3"}],onChange:function(e){return b({aspectRatio:e,minimumHeight:void 0})}})),"cover"!==m?(0,r.createElement)(React.Fragment,null,(0,r.createElement)(p.__experimentalToolsPanelItem,{style:{"grid-column":"span 1 / auto"},hasValue:function(){return!!s},label:(0,l.__)("Width","blocksy-companion"),onDeselect:function(){return b({width:void 0})},resetAllFilter:function(){return{width:void 0}},isShownByDefault:!0,key:t},(0,r.createElement)(p.__experimentalUnitControl,{label:(0,l.__)("Width","blocksy-companion"),labelPosition:"top",value:s||"",min:0,onChange:function(e){return h("width",e)},units:y})),(0,r.createElement)(p.__experimentalToolsPanelItem,{style:{"grid-column":"span 1 / auto"},hasValue:function(){return!!u},label:(0,l.__)("Height","blocksy-companion"),onDeselect:function(){return b({height:void 0})},resetAllFilter:function(){return{height:void 0}},isShownByDefault:!0,key:t},(0,r.createElement)(p.__experimentalUnitControl,{label:(0,l.__)("Height","blocksy-companion"),labelPosition:"top",value:u||"",min:0,onChange:function(e){return h("height",e)},units:y})),(0,r.createElement)(p.__experimentalToolsPanelItem,{hasValue:function(){return!!c},label:(0,l.__)("Scale","blocksy-companion"),onDeselect:function(){return b({imageFit:void 0})},resetAllFilter:function(){return{imageFit:"cover"}},isShownByDefault:!0,key:t},(0,r.createElement)(p.__experimentalToggleGroupControl,{label:(0,l.__)("Scale","blocksy-companion"),value:c,isBlock:!0,onChange:function(e){return b({imageFit:e})}},(0,r.createElement)(p.__experimentalToggleGroupControlOption,{key:"cover",value:"cover",label:(0,l.__)("Cover","blocksy-companion")}),(0,r.createElement)(p.__experimentalToggleGroupControlOption,{key:"contain",value:"contain",label:(0,l.__)("Contain","blocksy-companion")})))):null,"cover"===m?(0,r.createElement)(p.__experimentalToolsPanelItem,{hasValue:function(){return!!f},label:(0,l.__)("Minimum height"),onDeselect:function(){return b({minimumHeight:void 0})},isShownByDefault:!0},(0,r.createElement)(p.__experimentalUnitControl,{__next40pxDefaultSize:!0,label:(0,l.__)("Minimum height"),labelPosition:"top",value:f||"",onChange:function(e){e=0>parseFloat(e)?"0":e,b({minimumHeight:e,aspectRatio:"auto"})},units:y})):(0,r.createElement)(Vr,{sizeSlug:d,onChange:function(e){return b({sizeSlug:e})},clientId:t}))};function Ur(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}var qr=function(){return(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,r.createElement)("path",{d:"M19 5.5H5V4h14v1.5ZM19 20H5v-1.5h14V20ZM5 9h14v6H5V9Z"}))},Gr=function(){return(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false"},(0,r.createElement)("path",{d:"M16 5.5H8V4h8v1.5ZM16 20H8v-1.5h8V20ZM5 9h14v6H5V9Z"}))},Wr=function(e){var t=e.clientId,o=e.postType,l=e.postId,a=e.attributes,c=e.setAttributes,s=a.focalPoint,m=a.hasParallax,f=a.isRepeated,b=a.sizeSlug,y=a.allowCustomContentAndWideSize,h=a.contentSize,v=a.wideSize,g=u((0,d.useEntityProp)("postType",o,"featured_media",l),2),_=g[0],w=(g[1],function(e,t){var o,n,r;return(null==e||null===(o=e.media_details)||void 0===o||null===(n=o.sizes)||void 0===n||null===(r=n[t])||void 0===r?void 0:r.source_url)||(null==e?void 0:e.source_url)}((0,k.useSelect)((function(e){var t=e(d.store).getMedia;return{media:_&&t(_,{context:"view"})}}),[_]).media,b)),O=!m,x=(0,p.__experimentalUseCustomUnits)({availableUnits:(0,i.useSetting)("spacing.units")||["px","%","vw","em","rem"]});return"cover"!==a.viewType?null:(0,r.createElement)(React.Fragment,null,(0,r.createElement)(p.__experimentalToolsPanel,{label:tr("Layout"),resetAll:function(){c({hasParallax:!1,focalPoint:void 0,isRepeated:!1})}},(0,r.createElement)(p.__experimentalToolsPanelItem,{label:tr("Layout"),isShownByDefault:!0,hasValue:function(){return!y},onDeselect:function(){return c({allowCustomContentAndWideSize:!0,contentSize:void 0,wideSize:void 0})}},(0,r.createElement)(p.__experimentalVStack,{spacing:4,className:"block-editor-hooks__layout-constrained"},(0,r.createElement)(p.ToggleControl,{label:tr("Inner blocks use content width"),checked:y,onChange:function(){c({allowCustomContentAndWideSize:!y})},help:tr(y?"Nested blocks use content width with options for full and wide widths.":"Nested blocks will fill the width of this container. Toggle to constrain.")}),y&&(0,r.createElement)(React.Fragment,null,(0,r.createElement)(p.__experimentalUnitControl,{__next40pxDefaultSize:!0,label:tr("Content width"),labelPosition:"top",value:h||v||"",onChange:function(e){e=0>parseFloat(e)?"0":e,c({contentSize:e})},units:x,prefix:(0,r.createElement)(p.__experimentalInputControlPrefixWrapper,{variant:"icon"},(0,r.createElement)(qr,null))}),(0,r.createElement)(p.__experimentalUnitControl,{__next40pxDefaultSize:!0,label:tr("Wide width"),labelPosition:"top",value:v||h||"",onChange:function(e){e=0>parseFloat(e)?"0":e,c({wideSize:e})},units:x,prefix:(0,r.createElement)(p.__experimentalInputControlPrefixWrapper,{variant:"icon"},(0,r.createElement)(Gr,null))}),(0,r.createElement)("p",{className:"block-editor-hooks__layout-constrained-helptext"},tr("Customize the width for all elements that are assigned to the center or wide columns.")))))),(0,r.createElement)(p.__experimentalToolsPanel,{label:tr("Image Settings","blocksy-companion"),resetAll:function(){c({hasParallax:!1,focalPoint:void 0,isRepeated:!1})}},O&&(0,r.createElement)(p.__experimentalToolsPanelItem,{label:tr("Focal point"),isShownByDefault:!0,hasValue:function(){return!!s},onDeselect:function(){return c({focalPoint:void 0})}},(0,r.createElement)(p.FocalPointPicker,{__nextHasNoMarginBottom:!0,label:tr("Focal point"),url:w,value:s,onDragStart:function(e){return c({focalPoint:e})},onDrag:function(e){return c({focalPoint:e})},onChange:function(e){return c({focalPoint:e})}})),(0,r.createElement)(p.__experimentalToolsPanelItem,{label:tr("Fixed background"),isShownByDefault:!0,hasValue:function(){return m},onDeselect:function(){return c({hasParallax:!1,focalPoint:void 0})}},(0,r.createElement)(p.ToggleControl,{__nextHasNoMarginBottom:!0,label:tr("Fixed background"),checked:m,onChange:function(){c(function(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Ur(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Ur(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}({hasParallax:!m},m?{}:{focalPoint:void 0}))}})),(0,r.createElement)(p.__experimentalToolsPanelItem,{label:tr("Repeated background"),isShownByDefault:!0,hasValue:function(){return f},onDeselect:function(){return c({isRepeated:!1})}},(0,r.createElement)(p.ToggleControl,{__nextHasNoMarginBottom:!0,label:tr("Repeated background"),checked:f,onChange:function(){c({isRepeated:!f})}})),(0,r.createElement)(Vr,{sizeSlug:b,onChange:function(e){return c({sizeSlug:e})},clientId:t})))};o(991);const Zr="1600px",$r="320px",Kr=1,Xr=.25,Yr=.75,Jr="14px";function Qr(e,t={}){if("string"!=typeof e&&"number"!=typeof e)return null;isFinite(e)&&(e=`${e}px`);const{coerceTo:o,rootSizeValue:n,acceptableUnits:r}={coerceTo:"",rootSizeValue:16,acceptableUnits:["rem","px","em"],...t},l=r?.join("|"),a=new RegExp(`^(\\d*\\.?\\d+)(${l}){1,1}$`),i=e.match(a);if(!i||i.length<3)return null;let[,c,s]=i,u=parseFloat(c);return"px"!==o||"em"!==s&&"rem"!==s||(u*=n,s=o),"px"!==s||"em"!==o&&"rem"!==o||(u/=n,s=o),"em"!==o&&"rem"!==o||"em"!==s&&"rem"!==s||(s=o),{value:el(u,3),unit:s}}function el(e,t=3){const o=Math.pow(10,t);return Number.isFinite(e)?parseFloat(Math.round(e*o)/o):void 0}function tl(e,t){const{size:o}=e;if(!o||"0"===o||!1===e?.fluid)return o;if(!ol(t?.typography)&&!ol(e))return o;let n=function(e){const t=e?.typography,o=e?.layout,n=Qr(o?.wideSize)?o?.wideSize:null;return ol(t)&&n?{fluid:{maxViewportWidth:n,...t.fluid}}:{fluid:t?.fluid}}(t);n="object"==typeof n?.fluid?n?.fluid:{};const r=function({minimumFontSize:e,maximumFontSize:t,fontSize:o,minimumViewportWidth:n=$r,maximumViewportWidth:r=Zr,scaleFactor:l=Kr,minimumFontSizeLimit:a}){if(a=Qr(a)?a:Jr,o){const n=Qr(o);if(!n?.unit)return null;const r=Qr(a,{coerceTo:n.unit});if(r?.value&&!e&&!t&&n?.value<=r?.value)return null;if(t||(t=`${n.value}${n.unit}`),!e){const t="px"===n.unit?n.value:16*n.value,o=Math.min(Math.max(1-.075*Math.log2(t),Xr),Yr),l=el(n.value*o,3);e=r?.value&&l<r?.value?`${r.value}${r.unit}`:`${l}${n.unit}`}}const i=Qr(e),c=i?.unit||"rem",s=Qr(t,{coerceTo:c});if(!i||!s)return null;const u=Qr(e,{coerceTo:"rem"}),p=Qr(r,{coerceTo:c}),d=Qr(n,{coerceTo:c});if(!p||!d||!u)return null;const m=p.value-d.value;if(!m)return null;const f=el(d.value/100,3),b=el(f,3)+c,y=el(((s.value-i.value)/m*100||1)*l,3);return`clamp(${e}, ${u.value}${u.unit} + ((1vw - ${b}) * ${y}), ${t})`}({minimumFontSize:e?.fluid?.min,maximumFontSize:e?.fluid?.max,fontSize:o,minimumFontSizeLimit:n?.minFontSize,maximumViewportWidth:n?.maxViewportWidth,minimumViewportWidth:n?.minViewportWidth});return r||o}function ol(e){const t=e?.fluid;return!0===t||t&&"object"==typeof t&&Object.keys(t).length>0}const nl=(e,t,o)=>{const n=Array.isArray(t)?t:t.split(".");let r=e;return n.forEach((e=>{r=r?.[e]})),r??o};const rl=[{path:["color","palette"],valueKey:"color",cssVarInfix:"color",classes:[{classSuffix:"color",propertyName:"color"},{classSuffix:"background-color",propertyName:"background-color"},{classSuffix:"border-color",propertyName:"border-color"}]},{path:["color","gradients"],valueKey:"gradient",cssVarInfix:"gradient",classes:[{classSuffix:"gradient-background",propertyName:"background"}]},{path:["color","duotone"],valueKey:"colors",cssVarInfix:"duotone",valueFunc:({slug:e})=>`url( '#wp-duotone-${e}' )`,classes:[]},{path:["shadow","presets"],valueKey:"shadow",cssVarInfix:"shadow",classes:[]},{path:["typography","fontSizes"],valueFunc:(e,t)=>tl(e,t),valueKey:"size",cssVarInfix:"font-size",classes:[{classSuffix:"font-size",propertyName:"font-size"}]},{path:["typography","fontFamilies"],valueKey:"fontFamily",cssVarInfix:"font-family",classes:[{classSuffix:"font-family",propertyName:"font-family"}]},{path:["spacing","spacingSizes"],valueKey:"size",cssVarInfix:"spacing",valueFunc:({size:e})=>e,classes:[]}];function ll(e,t,o,n,r){const l=[nl(e,["blocks",t,...o]),nl(e,o)];for(const a of l)if(a){const l=["custom","theme","default"];for(const i of l){const l=a[i];if(l){const a=l.find((e=>e[n]===r));if(a){if("slug"===n)return a;return ll(e,t,o,"slug",a.slug)[n]===a[n]?a:void 0}}}}}function al(e,t,o){if(!o||"string"!=typeof o){if("string"!=typeof o?.ref)return o;if(!(o=nl(e,o.ref))||o?.ref)return o}const n="var:",r="var(--wp--";let l;if(o.startsWith(n))l=o.slice(4).split("|");else{if(!o.startsWith(r)||!o.endsWith(")"))return o;l=o.slice(10,-1).split("--")}const[a,...i]=l;return"preset"===a?function(e,t,o,[n,r]){const l=rl.find((e=>e.cssVarInfix===n));if(!l)return o;const a=ll(e.settings,t,l.path,"slug",r);if(a){const{valueKey:o}=l;return al(e,t,a[o])}return o}(e,t,o,i):"custom"===a?function(e,t,o,n){const r=nl(e.settings,["blocks",t,"custom",...n])??nl(e.settings,["custom",...n]);return r?al(e,t,r):o}(e,t,o,i):o}function il(e){if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(e=function(e,t){if(!e)return;if("string"==typeof e)return cl(e,t);var o=Object.prototype.toString.call(e).slice(8,-1);"Object"===o&&e.constructor&&(o=e.constructor.name);if("Map"===o||"Set"===o)return Array.from(e);if("Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return cl(e,t)}(e))){var t=0,o=function(){};return{s:o,n:function(){return t>=e.length?{done:!0}:{done:!1,value:e[t++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var n,r,l=!0,a=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return l=e.done,e},e:function(e){a=!0,r=e},f:function(){try{l||null==n.return||n.return()}finally{if(a)throw r}}}}function cl(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,n=new Array(t);o<t;o++)n[o]=e[o];return n}function sl(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function ul(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?sl(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):sl(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function pl(e,t,o){t=Array.isArray(t)?O(t):[t],e=Array.isArray(e)?O(e):ul({},e);var n,r=t.pop(),l=e,a=il(t);try{for(a.s();!(n=a.n()).done;){var i=n.value,c=l[i];l=l[i]=Array.isArray(c)?O(c):ul({},c)}}catch(e){a.e(e)}finally{a.f()}return l[r]=o,e}var dl=function(e){return function(e){var t,o,n,r,l,a,i;return Sn(Sn({},e.style),{},{color:Sn(Sn({},null===(t=e.style)||void 0===t?void 0:t.color),{},{text:e.textColor?"var:preset|color|"+e.textColor:null===(o=e.style)||void 0===o||null===(n=o.color)||void 0===n?void 0:n.text,background:e.backgroundColor?"var:preset|color|"+e.backgroundColor:null===(r=e.style)||void 0===r||null===(l=r.color)||void 0===l?void 0:l.background,gradient:e.gradient?"var:preset|gradient|"+e.gradient:null===(a=e.style)||void 0===a||null===(i=a.color)||void 0===i?void 0:i.gradient})})}({style:e.style,textColor:e.textColor})},ml=function(e){var t,o,a,c,s,u,d,m,b,y,h,v,g,w,C,E,P=e.fieldDescriptor,S=e.fieldsDescriptor,j=e.attributes,I=e.setAttributes,T=e.options,D=e.fieldsChoices,A=e.clientId,B=(e.name,e.__unstableParentLayout),H=e.taxonomies,N=e.postId,z=e.postType,M=(0,k.useDispatch)("core/block-editor").replaceInnerBlocks,F=dl(j),R=In(0,B),V=function(e){return al({settings:R},"",e)},L=function(e){var t,o,n,a,i,c,s,u=null==e||null===(t=e.color)||void 0===t||null===(o=t.palette)||void 0===o?void 0:o.custom,p=null==e||null===(n=e.color)||void 0===n||null===(a=n.palette)||void 0===a?void 0:a.theme,d=null==e||null===(i=e.color)||void 0===i||null===(c=i.palette)||void 0===c?void 0:c.default,m=null==e||null===(s=e.color)||void 0===s?void 0:s.defaultPalette;return(0,r.useMemo)((function(){var e=[];return p&&p.length&&e.push({name:(0,l._x)("Theme","Indicates this palette comes from the theme."),colors:p}),m&&d&&d.length&&e.push({name:(0,l._x)("Default","Indicates this palette comes from WordPress."),colors:d}),u&&u.length&&e.push({name:(0,l._x)("Custom","Indicates this palette is created by the user."),colors:u}),e}),[u,p,d,m])}(R),U=function(e){var t,o,n,a,i,c,s,u=null==e||null===(t=e.color)||void 0===t||null===(o=t.gradients)||void 0===o?void 0:o.custom,p=null==e||null===(n=e.color)||void 0===n||null===(a=n.gradients)||void 0===a?void 0:a.theme,d=null==e||null===(i=e.color)||void 0===i||null===(c=i.gradients)||void 0===c?void 0:c.default,m=null==e||null===(s=e.color)||void 0===s?void 0:s.defaultGradients;return(0,r.useMemo)((function(){var e=[];return p&&p.length&&e.push({name:(0,l._x)("Theme","Indicates this palette comes from the theme."),gradients:p}),m&&d&&d.length&&e.push({name:(0,l._x)("Default","Indicates this palette comes from WordPress."),gradients:d}),u&&u.length&&e.push({name:(0,l._x)("Custom","Indicates this palette is created by the user."),gradients:u}),e}),[u,p,d,m])}(R),q=function(e){var t=L.flatMap((function(e){return e.colors})).find((function(t){return t.color===e}));return t?"var:preset|color|"+t.slug:e},G=function(e){var t=U.flatMap((function(e){return e.gradients})).find((function(t){return t.gradient===e}));return t?"var:preset|gradient|"+t.slug:e},W=function(e){var t,o,n,r,l,a,i,c,s,u,p;I((l=null==(t=e)||null===(o=t.color)||void 0===o?void 0:o.text,a=null!=l&&l.startsWith("var:preset|color|")?l.substring(17):void 0,i=null==t||null===(n=t.color)||void 0===n?void 0:n.background,c=null!=i&&i.startsWith("var:preset|color|")?i.substring(17):void 0,s=null==t||null===(r=t.color)||void 0===r?void 0:r.gradient,u=null!=s&&s.startsWith("var:preset|gradient|")?s.substring(20):void 0,(p=Sn({},t)).color=Sn(Sn({},p.color),{},{text:a?void 0:l,background:c?void 0:i,gradient:u?void 0:s}),{style:An(p),textColor:a,backgroundColor:c,gradient:u}))},$=V(null==F||null===(t=F.elements)||void 0===t||null===(o=t.link)||void 0===o||null===(a=o.color)||void 0===a?void 0:a.text),K=V(null==F||null===(c=F.elements)||void 0===c||null===(s=c.link)||void 0===s||null===(u=s[":hover"])||void 0===u||null===(d=u.color)||void 0===d?void 0:d.text),X=V(null==j||null===(m=j.style)||void 0===m||null===(b=m.elements)||void 0===b||null===(y=b.overlay)||void 0===y||null===(h=y.color)||void 0===h?void 0:h.background),Y=V(null==j||null===(v=j.style)||void 0===v||null===(g=v.elements)||void 0===g||null===(w=g.overlay)||void 0===w||null===(C=w.color)||void 0===C?void 0:C.gradient),J=null,Q=function(e){if(J||(J="color"),"gradient"!==J){var t=pl(F,["elements","overlay","color","background"],q(e));e&&(t=pl(t,["elements","overlay","color","gradient"],G(void 0))),W(t)}else J=null},ee=V(null==F||null===(E=F.color)||void 0===E?void 0:E.text),te=function(e){var t=pl(F,["color","text"],q(e));ee===$&&(t=pl(t,["elements","link","color","text"],q(e))),W(t)},oe=function(e){W(pl(F,["elements","link","color","text"],q(e)))},ne=function(e){W(pl(F,["elements","link",":hover","color","text"],q(e)))},re="default"!==j.viewType&&f(P)?[{colorValue:Y?void 0:X,gradientValue:Y,label:(0,l.__)("Overlay","blocksy-companion"),enableAlpha:!0,onColorChange:Q,onGradientChange:function(e){!function(e){if(J||(J="gradient"),"color"!==J){var t=pl(F,["elements","overlay","color","gradient"],G(e));e&&(t=pl(t,["elements","overlay","color","background"],q(void 0))),W(t)}}(e)},isShownByDefault:!0,clearable:!0}]:O("yes"===j.has_field_link?[{colorValue:$,label:(0,l.__)("Link","blocksy-companion"),enableAlpha:!0,onColorChange:oe},{colorValue:K,label:(0,l.__)("Link Hover","blocksy-companion"),enableAlpha:!0,onColorChange:ne}]:[{colorValue:ee,label:(0,l.__)("Text","blocksy-companion"),enableAlpha:!0,onColorChange:te}]);return P?(0,r.createElement)(React.Fragment,null,(0,r.createElement)(i.InspectorControls,null,(0,r.createElement)(p.PanelBody,null,(0,r.createElement)(Z.OptionsPanel,{purpose:"gutenberg",onChange:function(e,t){I(n({},e,t)),"viewType"!==e||X||setTimeout((function(){Q("#000000")}),50),"viewType"!==e&&("field"!==e||f(t)&&"wp:author_avatar"!==j.field)||M(A,[],!1)},options:ul(ul(ul(ul({field:{type:"ct-select",label:(0,l.__)("Content Source","blocksy-companion"),value:"",search:!0,searchPlaceholder:(0,l.__)("Search for field","blocksy-companion"),defaultToFirstItem:!1,choices:D,purpose:"default"}},"wp:author_avatar"!==j.field&&f(P)?{viewType:{type:"ct-radio",label:(0,l.__)("View Type","blocksy-companion"),value:j.viewType,design:"inline",purpose:"gutenberg",divider:"bottom:full",choices:{default:(0,l.__)("Image","blocksy-companion"),cover:(0,l.__)("Cover","blocksy-companion")}}}:{}),"wp:terms"===j.field&&H&&H.length>0?{taxonomy:{type:"ct-select",label:(0,l.__)("Taxonomy","blocksy-companion"),value:"",design:"inline",purpose:"default",choices:H.map((function(e){var t=e.name;return{key:e.slug,value:t}}))}}:{}),"wp:term_image"===j.field?{imageSource:{type:"ct-radio",label:(0,l.__)("Image Source","blocksy-companion"),value:j.imageSource,design:"inline",purpose:"gutenberg",divider:"bottom",choices:{featured:(0,l.__)("Image","blocksy-companion"),icon:(0,l.__)("Icon/Logo","blocksy-companion")}}}:{}),T),value:ul(ul({},j),S&&S.has_taxonomies_customization?{has_taxonomies_customization:"yes"}:{}),hasRevertButton:!1}),f(P)&&"wp:author_avatar"!==j.field&&"wp:archive_image"!==j.field&&"default"===j.viewType&&(0,r.createElement)(Z.OptionsPanel,{purpose:"gutenberg",onChange:function(e,t){I(n({},e,t))},options:ul(ul({lightbox_condition:{type:"ct-condition",condition:{has_field_link:"no"},options:{lightbox:{type:"ct-switch",label:(0,l.__)("Expand on click","blocksy-companion"),value:"no"}}}},"wp:featured_image"===j.field?{videoThumbnail:{type:"ct-switch",label:(0,l.__)("Video thumbnail","blocksy-companion"),value:"no"}}:{}),{},{image_hover_effect:{label:(0,l.__)("Image Hover Effect","blocksy-companion"),type:"ct-select",value:"none",view:"text",design:"inline",divider:"top:full",choices:{none:(0,l.__)("None","blocksy-companion"),"zoom-in":(0,l.__)("Zoom In","blocksy-companion"),"zoom-out":(0,l.__)("Zoom Out","blocksy-companion")}}}),value:j,hasRevertButton:!1})),f(P)&&"wp:author_avatar"!==j.field&&(0,r.createElement)(React.Fragment,null,(0,r.createElement)(Wr,{attributes:j,setAttributes:I,postId:N,postType:z}),(0,r.createElement)(Lr,{clientId:A,attributes:j,setAttributes:I})),"wp:author_avatar"===j.field&&(0,r.createElement)(p.PanelBody,null,(0,r.createElement)(p.RangeControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,l.__)("Image size","blocksy-companion"),onChange:function(e){return I({avatar_size:e})},min:5,max:500,initialPosition:null==j?void 0:j.avatar_size,value:null==j?void 0:j.avatar_size})),"woo:brands"===j.field&&(0,r.createElement)(p.PanelBody,null,(0,r.createElement)(p.RangeControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,l.__)("Logo Size","blocksy-companion"),onChange:function(e){return I({brands_size:e})},min:5,max:500,initialPosition:null==j?void 0:j.brands_size,value:null==j?void 0:j.brands_size}),(0,r.createElement)(p.RangeControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,l.__)("Logo Gap","blocksy-companion"),onChange:function(e){return I({brands_gap:e})},min:5,max:500,initialPosition:null==j?void 0:j.brands_gap,value:null==j?void 0:j.brands_gap})),!f(P)&&"woo:brands"!==j.field&&(0,r.createElement)(p.PanelBody,null,(0,r.createElement)(Z.OptionsPanel,{purpose:"gutenberg",onChange:function(e,t){I(n({},e,t))},options:ul({before:{type:"text",label:(0,l.__)("Before","blocksy-companion"),value:""},after:{type:"text",label:(0,l.__)("After","blocksy-companion"),value:""}},"wp"!==P.provider||"wp"===P.provider&&("excerpt"===P.id||"terms"===P.id||"author"===P.id)?{fallback:{type:"text",label:(0,l.__)("Fallback","blocksy-companion"),value:(0,l.__)("Custom field fallback","blocksy-companion")}}:{}),value:j,hasRevertButton:!1}))),(0,r.createElement)(i.InspectorControls,{group:"color",resetAllFilter:function(){var e=f(P)?"image":"text";"text"===e&&(te(),oe(),ne(),setTimeout((function(){var e,t=null==j||null===(e=j.style)||void 0===e?void 0:e.elements,o=(t.link,x(t,["link"])),n=ul(ul({},j.style),{},{elements:o});I({textColor:void 0,style:n})}))),"image"===e&&setTimeout((function(){Q("#000000")}),50)}},(0,r.createElement)(_,{label:(0,l.__)("Colors","blocksy-companion"),panelId:A,settings:re,skipToolsPanel:!0,containerProps:{"data-field-type":f(P)?"image:".concat(j.viewType):"text"}}),f(P)&&"default"!==j.viewType?(0,r.createElement)(p.__experimentalToolsPanelItem,{hasValue:function(){return void 0!==j.dimRatio&&50!==j.dimRatio},label:(0,l.__)("Overlay opacity"),onDeselect:function(){return I({dimRatio:50})},resetAllFilter:function(){return{dimRatio:50}},isShownByDefault:!0,panelId:A},(0,r.createElement)(p.RangeControl,{__nextHasNoMarginBottom:!0,label:(0,l.__)("Overlay opacity"),value:j.dimRatio,onChange:function(e){return I({dimRatio:e})},min:0,max:100,step:10,required:!0,__next40pxDefaultSize:!0})):null),"wp:terms"===j.field&&(0,r.createElement)(i.InspectorControls,{group:"advanced"},(0,r.createElement)(p.TextControl,{__nextHasNoMarginBottom:!0,autoComplete:"off",label:(0,l.__)("Term additional class","blocksy-companion"),value:j.termClass,onChange:function(e){I({termClass:e})},help:(0,l.__)("Additional class for term items. Useful for styling.","blocksy-companion")}))):(0,r.createElement)(i.InspectorControls,null,(0,r.createElement)(p.PanelBody,null,(0,r.createElement)(Z.OptionsPanel,{purpose:"gutenberg",onChange:function(e,t){I(n({},e,t)),"viewType"!==e||X||setTimeout((function(){Q("#000000")}),50),"viewType"!==e&&("field"!==e||f(t)&&"wp:author_avatar"!==j.field)||M(A,[],!1)},options:{field:{type:"ct-select",label:(0,l.__)("Content Source","blocksy-companion"),value:"",search:!0,searchPlaceholder:(0,l.__)("Search for field","blocksy-companion"),defaultToFirstItem:!1,choices:D,purpose:"default"}},value:ul({},j),hasRevertButton:!1})))};function fl(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function bl(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?fl(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):fl(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var yl=function(e){var t=e.clientId,o=e.attributes,n=e.setAttributes,l=e.context,a=e.name,i=e.__unstableParentLayout,c=l.postType,s=l.taxonomy,p=Rr(l),d=p.fieldsDescriptor,m=p.options,f=p.fieldsChoices,b=ke(c),y=(0,r.useMemo)((function(){if(!o.field||!d)return null;var e=u(o.field.split(":"),2),t=e[0],n=e[1],r=d.fields.find((function(e){return e.provider===t}));if(!r)return null;var l=r.fields.find((function(e){return e.id===n}));return l?bl(bl({},l),{},{provider:r.provider}):null}),[o.field,d]);return(0,r.useEffect)((function(){"wp:title"===o.field&&s&&n({field:"wp:term_title"})}),[s,o.field]),(0,r.createElement)(React.Fragment,null,(0,r.createElement)(zr,{fieldDescriptor:y,attributes:o,setAttributes:n}),(0,r.createElement)(Br,g({attributes:o,fieldsDescriptor:d,fieldDescriptor:y},l)),(0,r.createElement)(ml,g({options:m,fieldDescriptor:y,attributes:o,setAttributes:n,fieldsChoices:f,clientId:t,fieldsDescriptor:d,taxonomies:b},l,{name:a,__unstableParentLayout:i})))},hl=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"blocksy/dynamic-data","category":"blocksy-blocks","attributes":{"tagName":{"type":"string","default":"div"},"field":{"type":"string","default":"wp:title"},"before":{"type":"string","default":""},"after":{"type":"string","default":""},"fallback":{"type":"string","default":""},"align":{"type":"string"},"imageAlign":{"type":"string"},"aspectRatio":{"type":"string","default":"auto"},"imageFit":{"type":"string","default":"cover"},"width":{"type":"string"},"height":{"type":"string"},"sizeSlug":{"type":"string"},"alt_text":{"type":"string","default":""},"avatar_size":{"type":"number","default":96},"avatarIsLink":{"type":"boolean","default":false},"avatarLinkTarget":{"type":"string","default":"_self"},"featuredIsLink":{"type":"boolean","default":false},"featuredLinkTarget":{"type":"string","default":"_self"},"taxonomy":{"type":"string","default":""},"attribute":{"type":"string","default":""},"lightbox":{"type":"string","default":"no"},"videoThumbnail":{"type":"string","default":"no"},"image_hover_effect":{"type":"string","default":"none"},"termClass":{"type":"string","default":""},"termAccentColor":{"type":"string","default":"yes"},"brands_size":{"type":"number","default":100},"brands_gap":{"type":"number","default":10},"imageSource":{"type":"string","default":"featured"},"viewType":{"type":"string","default":"default"},"backgroundColor":{"type":"string"},"textColor":{"type":"string"},"dimRatio":{"type":"number","default":50},"style":{"type":"object","default":{"elements":{"overlay":{"color":{"background":"#000000"}}}}}},"supports":{"color":{"text":false,"background":false,"__experimentalSkipSerialization":["link","heading","button","gradients"]},"className":false,"spacing":{"margin":true,"padding":true,"__experimentalDefaultControls":{"margin":false,"padding":false}},"__experimentalBorder":{"color":true,"radius":true,"width":true,"__experimentalDefaultControls":{"color":true,"radius":true,"width":true},"__experimentalSelector":"img, .block-editor-media-placeholder, .wp-block-post-featured-image__overlay","__experimentalSkipSerialization":true},"typography":{"fontSize":true,"lineHeight":true,"__experimentalFontFamily":true,"__experimentalTextDecoration":true,"__experimentalFontStyle":true,"__experimentalFontWeight":true,"__experimentalLetterSpacing":true,"__experimentalTextTransform":true,"__experimentalDefaultControls":{"fontSize":true}}},"usesContext":["postId","postType","queryId","taxonomy","termId","termIcon","termImage"]}'),vl=JSON.parse('{"Y":{"hasParallax":{"type":"boolean","default":false},"isRepeated":{"type":"boolean","default":false},"overlayColor":{"type":"string"},"customOverlayColor":{"type":"string"},"isUserOverlayColor":{"type":"boolean"},"focalPoint":{"type":"object"},"minHeight":{"type":"number"},"minHeightUnit":{"type":"string"},"contentPosition":{"type":"string"},"isDark":{"type":"boolean","default":true},"allowedBlocks":{"type":"array"},"templateLock":{"type":["string","boolean"],"enum":["all","insert","contentOnly",false]},"tagName":{"type":"string","default":"div"},"allowCustomContentAndWideSize":{"type":"boolean","default":true},"contentSize":{"type":"string","default":""},"wideSize":{"type":"string","default":""},"minimumHeight":{"type":"string","default":""}}}'),gl=(0,Z.getColorsDefaults)({textColor:"",customTextColor:"",backgroundColor:"",customBackgroundColor:"",linkColor:"",customLinkColor:"",linkHoverColor:"",customLinkHoverColor:"",overlayColor:"",customOverlayColor:"#000000"});function _l(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function kl(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?_l(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):_l(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var wl=(0,Z.getOptionsForBlock)("dynamic-data"),Ol=(0,Z.getAttributesFromOptions)(wl);(0,a.registerBlockType)("blocksy/dynamic-data",kl(kl({},hl),{},{title:(0,l.__)("Dynamic Data","blocksy-companion"),description:(0,l.__)("Fetch and display content from various sources.","blocksy-companion"),attributes:kl(kl(kl(kl({},hl.attributes),vl.Y),Ol),gl),icon:{src:(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)("path",{d:"M17.9 10.5c-.1-.3-.4-.4-.7-.4h-3.7V4.6c0-.4-.2-.7-.6-.8h-.2c-.3 0-.5.1-.7.4l-5.7 8.6c-.2.3-.2.6 0 .8 0 .*******.4h3.7v5.5c0 .*******.8h.2c.3 0 .5-.1.7-.4l5.7-8.6c.2-.2.2-.6.1-.8zm-5.9 7v-4.4c0-.3-.3-.6-.6-.6H7.9l4.1-6v4.4c0 .*******.6h3.5l-4.1 6z"}))},edit:function(e){return(0,r.createElement)(yl,e)},save:function(){return(0,r.createElement)(i.InnerBlocks.Content,null)},__experimentalLabel:function(e){return"wp:title"===e.field?(0,l.__)("Dynamic Title","blocksy-companion"):"wp:excerpt"===e.field?(0,l.__)("Dynamic Excerpt","blocksy-companion"):"wp:date"===e.field?(0,l.__)("Dynamic Post Date","blocksy-companion"):"wp:comments"===e.field?(0,l.__)("Dynamic Comments","blocksy-companion"):"wp:terms"===e.field?(0,l.__)("Dynamic Terms","blocksy-companion"):"wp:author"===e.field?(0,l.__)("Dynamic Author","blocksy-companion"):"wp:featured_image"===e.field?(0,l.__)("Dynamic Featured Image","blocksy-companion"):"wp:author_avatar"===e.field?(0,l.__)("Dynamic Author Avatar","blocksy-companion"):"woo:price"===e.field?(0,l.__)("Dynamic Price","blocksy-companion"):"woo:stock_status"===e.field?(0,l.__)("Dynamic Stock Status","blocksy-companion"):"woo:attributes"===e.field?(0,l.__)("Dynamic Attributes","blocksy-companion"):"woo:brands"===e.field?(0,l.__)("Dynamic Brands","blocksy-companion"):"woo:sku"===e.field?(0,l.__)("Dynamic SKU","blocksy-companion"):"woo:rating"===e.field?(0,l.__)("Dynamic Rating","blocksy-companion"):"wp:term_title"===e.field?(0,l.__)("Dynamic Term Title","blocksy-companion"):"wp:term_description"===e.field?(0,l.__)("Dynamic Term Description","blocksy-companion"):"wp:term_count"===e.field?(0,l.__)("Dynamic Term Count","blocksy-companion"):"wp:term_image"===e.field?(0,l.__)("Dynamic Term Image","blocksy-companion"):"wp:archive_image"===e.field?(0,l.__)("Dynamic Archive Image","blocksy-companion"):"wp:archive_title"===e.field?(0,l.__)("Dynamic Archive Title","blocksy-companion"):"wp:archive_description"===e.field?(0,l.__)("Dynamic Archive Description","blocksy-companion"):(0,l.__)("Dynamic Data","blocksy-companion")}}));var xl={inputFontColor:"",customInputFontColor:"",inputFontColorFocus:"",customInputFontColorFocus:"",inputIconColor:"",customInputIconColor:"",inputIconColorFocus:"",customInputIconColorFocus:"",inputBorderColor:"",customInputBorderColor:"",inputBorderColorFocus:"",customInputBorderColorFocus:"",inputBackgroundColor:"",customInputBackgroundColor:"",inputBackgroundColorFocus:"",customInputBackgroundColorFocus:"",buttonBackgroundColor:"",customButtonBackgroundColor:"",buttonBackgroundColorHover:"",customButtonBackgroundColorHover:"",dropdownTextInitialColor:"",customDropdownTextInitialColor:"",dropdownTextHoverColor:"",customDropdownTextHoverColor:"",dropdownBackgroundColor:"",customDropdownBackgroundColor:"",shadowColor:"",customShadowColor:""},Cl=Zt(xl);function El(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Pl(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?El(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):El(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var Sl=Pl({enable_live_results:"no",live_results_images:"yes",searchBoxHeight:"",searchProductPrice:"no",searchProductStatus:"no",search_box_placeholder:(0,l.__)("Search","blocksy-companion"),taxonomy_filter_label:(0,l.__)("Select category","blocksy-companion"),search_through:{post:!0,page:!0,product:!0,custom:!0},taxonomy_filter_visibility:{desktop:!0,tablet:!0,mobile:!1}},xl),jl=function(e){var t=e.attributes,o=e.setAttributes,n=e.buttonStyles,l=u((0,r.useState)(!1),2),a=l[0],c=l[1],s=t.search_box_button_text,d=t.search_box_placeholder,m=t.taxonomy_filter_label,f=t.buttonPosition,b=t.has_taxonomy_filter,y=t.buttonUseText,h=t.taxonomy_filter_visibility,v=(0,r.useRef)({taxonomy:"",icon:""}),g=(0,r.useCallback)((function(e){var t=document.createElement("div");t.innerHTML=e;var o=t.querySelector('[type="search"]');return o&&o.setAttribute("placeholder",d),t.querySelector(".ct-search-box").style="",t.querySelector(".ct-select-taxonomy")&&(v.current=Pl(Pl({},v.current),{},{taxonomy:t.querySelector(".ct-select-taxonomy").outerHTML})),t.querySelector(".ct-icon")&&(v.current=Pl(Pl({},v.current),{},{icon:t.querySelector(".ct-icon").outerHTML})),t.innerHTML}),[d,f,n]),_=Wt("search",Pl(Pl({},t),Sl),g).isLoading;return(0,r.useEffect)((function(){c(!0),setTimeout((function(){c(!1)}),100)}),[t]),_?(0,r.createElement)(p.Spinner,null):(0,r.createElement)("form",{role:"search",method:"get",className:"ct-search-form","data-form-controls":f,"data-taxonomy-filter":"yes"===b?"true":"false","data-submit-button":"yes"===y?"text":"icon","data-updating":a?"yes":"no"},(0,r.createElement)("input",{type:"search",value:d,onChange:function(e){o({search_box_placeholder:e.target.value})},placeholder:"Search",name:"s",autoComplete:"off",title:"Search for...","aria-label":"Search for..."}),(0,r.createElement)("div",{className:"ct-search-form-controls"},"yes"===b?(0,r.createElement)("span",{className:ee()("ct-fake-select-container",{"ct-hidden-lg":!h.desktop,"ct-hidden-md":!h.tablet,"ct-hidden-sm":!h.mobile})},(0,r.createElement)("select",{className:"ct-select-taxonomy"}),(0,r.createElement)(i.RichText,{tagName:"span",className:"ct-fake-select",value:m,placeholder:"Select Category",allowedFormats:[],onChange:function(e){return o({taxonomy_filter_label:e})}})):null,(0,r.createElement)("div",{className:"wp-element-button","data-button":"".concat(f,":").concat("yes"===y?"text":"icon"),"aria-label":"Search button",style:n},"yes"===y?(0,r.createElement)(i.RichText,{tagName:"span",value:s,placeholder:"Search",allowedFormats:[],onChange:function(e){return o({search_box_button_text:e})}}):(0,r.createElement)(r.RawHTML,null,v.current.icon),(0,r.createElement)("span",{className:"ct-ajax-loader"},(0,r.createElement)("svg",{viewBox:"0 0 24 24"},(0,r.createElement)("circle",{cx:"12",cy:"12",r:"10",opacity:"0.2",fill:"none",stroke:"currentColor",strokeMiterlimit:"10",strokeWidth:"2"}),(0,r.createElement)("path",{d:"m12,2c5.52,0,10,4.48,10,10",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeMiterlimit:"10",strokeWidth:"2"},(0,r.createElement)("animateTransform",{attributeName:"transform",attributeType:"XML",type:"rotate",dur:"0.6s",from:"0 12 12",to:"360 12 12",repeatCount:"indefinite"})))))))},Il=(0,r.createElement)(p.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(p.Rect,{x:"4.75",y:"15.25",width:"6.5",height:"9.5",transform:"rotate(-90 4.75 15.25)",stroke:"currentColor",strokeWidth:"1.5",fill:"none"}),(0,r.createElement)(p.Rect,{x:"16",y:"10",width:"4",height:"4",rx:"1",fill:"currentColor"})),Tl=(0,r.createElement)(p.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(p.Rect,{x:"4.75",y:"7.75",width:"14.5",height:"8.5",rx:"1.25",stroke:"currentColor",fill:"none",strokeWidth:"1.5"}),(0,r.createElement)(p.Rect,{x:"8",y:"11",width:"8",height:"2",fill:"currentColor"}));function Dl(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Al(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Dl(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Dl(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var Bl=(0,i.withColors)({textColor:"color"},{inputFontColor:"color"},{inputFontColorFocus:"color"},{inputIconColor:"color"},{inputIconColorFocus:"color"},{inputBorderColor:"color"},{inputBorderColorFocus:"color"},{inputBackgroundColor:"color"},{inputBackgroundColorFocus:"color"},{buttonBackgroundColor:"color"},{buttonBackgroundColorHover:"color"},{dropdownTextInitialColor:"color"},{dropdownTextHoverColor:"color"},{dropdownBackgroundColor:"color"},{shadowColor:"color"})((function(e){var t,o,n=e.attributes,a=e.setAttributes,c=e.clientId,s=e.className,u=e.inputFontColor,d=e.setInputFontColor,m=e.inputFontColorFocus,f=e.setInputFontColorFocus,b=e.inputIconColor,y=e.setInputIconColor,h=e.inputIconColorFocus,v=e.setInputIconColorFocus,g=e.inputBorderColor,k=e.setInputBorderColor,w=e.inputBorderColorFocus,O=e.setInputBorderColorFocus,x=e.inputBackgroundColor,C=e.setInputBackgroundColor,E=e.inputBackgroundColorFocus,P=e.setInputBackgroundColorFocus,S=e.buttonBackgroundColor,j=e.setButtonBackgroundColor,I=e.buttonBackgroundColorHover,T=e.setButtonBackgroundColorHover,D=e.dropdownTextInitialColor,A=e.setDropdownTextInitialColor,B=e.dropdownTextHoverColor,H=e.setDropdownTextHoverColor,N=e.dropdownBackgroundColor,z=e.setDropdownBackgroundColor,M=e.shadowColor,F=e.setShadowColor,R=n.buttonUseText,V=void 0===R?"no":R,L=n.buttonPosition,U=n.enable_live_results,q=null==n||null===(t=n.style)||void 0===t||null===(o=t.border)||void 0===o?void 0:o.radius,G=(0,r.useRef)(),W=(0,i.useBlockProps)({ref:G,className:{"ct-search-box":!0,className:s},style:Al(Al(Al({"--theme-form-text-initial-color":null==u?void 0:u.color,"--theme-form-text-focus-color":null==m?void 0:m.color,"--theme-form-field-border-initial-color":null==g?void 0:g.color,"--theme-form-field-border-focus-color":null==w?void 0:w.color,"--theme-form-field-background-initial-color":null==x?void 0:x.color,"--theme-form-field-background-focus-color":null==E?void 0:E.color},q?{"--theme-form-field-border-radius":"".concat("string"==typeof q?q:"".concat(q.topLeft," ").concat(q.topRight," ").concat(q.bottomLeft," ").concat(q.bottomRight))}:{}),null!=n&&n.searchBoxHeight?{"--theme-form-field-height":"".concat(n.searchBoxHeight,"px")}:{}),"yes"===U?{"--theme-link-initial-color":null==D?void 0:D.color,"--theme-link-hover-color":null==B?void 0:B.color,"--search-dropdown-background":null==N?void 0:N.color,"--search-dropdown-box-shadow-color":null==M?void 0:M.color}:{})});return(0,r.createElement)("div",W,(0,r.createElement)(jl,{blockProps:W,attributes:n,setAttributes:a,buttonStyles:Al(Al(Al({},null!=b&&b.color?{"--theme-button-text-initial-color":b.color}:{}),null!=h&&h.color?{"--theme-button-text-hover-color":h.color}:{}),"outside"===L?Al(Al({},null!=S&&S.color?{"--theme-button-background-initial-color":S.color}:{}),null!=I&&I.color?{"--theme-button-background-hover-color":I.color}:{}):{})}),(0,r.createElement)(eo,{attributes:n,setAttributes:a,options:zl}),(0,r.createElement)(i.BlockControls,null,(0,r.createElement)(p.ToolbarGroup,null,(0,r.createElement)(p.ToolbarButton,{title:(0,l.__)("Button Outside"),icon:Il,onClick:function(){a({buttonPosition:"outside"===L?"inside":"outside"})},className:"outside"===L?"is-pressed":void 0}),(0,r.createElement)(p.ToolbarButton,{title:(0,l.__)("Use button with text"),icon:Tl,onClick:function(){a({buttonUseText:"no"===V?"yes":"no"})},className:"yes"===V?"is-pressed":void 0}))),(0,r.createElement)(i.InspectorControls,{group:"styles"},(0,r.createElement)(_,{label:(0,l.__)("Input Font Color","blocksy-companion"),resetAll:function(){d(xl.inputFontColor),f(xl.inputFontColorFocus)},panelId:c,settings:[{colorValue:u.color,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return d(e||xl.inputFontColor)}},{colorValue:m.color,label:(0,l.__)("Focus","blocksy-companion"),onColorChange:function(e){return f(e||xl.inputFontColorFocus)}}]}),(0,r.createElement)(_,{label:(0,l.__)("Input Border Color","blocksy-companion"),resetAll:function(){k(xl.inputBorderColor),O(xl.inputBorderColorFocus)},panelId:c,settings:[{colorValue:g.color,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return k(e||xl.inputBorderColor)}},{colorValue:w.color,label:(0,l.__)("Focus","blocksy-companion"),onColorChange:function(e){return O(e||xl.inputBorderColorFocus)}}]}),(0,r.createElement)(_,{label:(0,l.__)("Input Background Color","blocksy-companion"),resetAll:function(){C(xl.inputBackgroundColor),P(xl.inputBackgroundColorFocus)},panelId:c,settings:[{colorValue:x.color,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return C(e||xl.inputBackgroundColor)}},{colorValue:E.color,label:(0,l.__)("Focus","blocksy-companion"),onColorChange:function(e){return P(e||xl.inputBackgroundColorFocus)}}]}),(0,r.createElement)(_,{label:"yes"===V?(0,l.__)("Button Text Color","blocksy-companion"):(0,l.__)("Button Icon Color","blocksy-companion"),resetAll:function(){y(xl.inputIconColor),v(xl.inputIconColorFocus)},panelId:c,settings:[{colorValue:b.color,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return y(e||xl.inputIconColor)}},{colorValue:h.color,label:(0,l.__)("Hover","blocksy-companion"),onColorChange:function(e){return v(e||xl.inputIconColorFocus)}}]}),"outside"===L?(0,r.createElement)(_,{label:(0,l.__)("Button Background Color","blocksy-companion"),resetAll:function(){j(xl.buttonBackgroundColor),T(xl.buttonBackgroundColorHover)},panelId:c,settings:[{colorValue:S.color,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return j(e||xl.buttonBackgroundColor)}},{colorValue:I.color,label:(0,l.__)("Hover","blocksy-companion"),onColorChange:function(e){return T(e||xl.buttonBackgroundColorHover)}}]}):null,"yes"===U?(0,r.createElement)(React.Fragment,null,(0,r.createElement)(_,{label:(0,l.__)("Dropdown Text Color","blocksy-companion"),resetAll:function(){A(xl.dropdownTextInitialColor),H(xl.dropdownTextHoverColor)},panelId:c,settings:[{colorValue:D.color,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return A(e||xl.dropdownTextInitialColor)}},{colorValue:B.color,label:(0,l.__)("Hover","blocksy-companion"),onColorChange:function(e){return H(e||xl.dropdownTextHoverColor)}}]}),(0,r.createElement)(_,{label:(0,l.__)("Dropdown Background Color","blocksy-companion"),resetAll:function(){z(xl.dropdownBackgroundColor)},panelId:c,settings:[{colorValue:N.color,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return z(e||xl.dropdownBackgroundColor)}}]}),(0,r.createElement)(_,{label:(0,l.__)("Dropdown Shadow Color","blocksy-companion"),resetAll:function(){F(xl.shadowColor)},panelId:c,settings:[{colorValue:M.color,enableAlpha:!0,label:(0,l.__)("Initial","blocksy-companion"),onColorChange:function(e){return F(e||xl.shadowColor)}}]})):null))}));function Hl(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Nl(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Hl(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Hl(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}var zl=(0,Z.getOptionsForBlock)("search"),Ml=(0,Z.getAttributesFromOptions)(zl);(0,a.registerBlockType)("blocksy/search",{apiVersion:3,title:(0,l.__)("Advanced Search","blocksy-companion"),description:(0,l.__)("Quickly find specific content on your site.","blocksy-companion"),icon:{src:(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"wc-block-editor-components-block-icon"},(0,r.createElement)("path",{d:"m19.7 18.5-3-2.9c1-1.3 1.6-2.8 1.6-4.5 0-3.9-3.2-7.2-7.2-7.2S4 7.2 4 11.2s3.2 7.2 7.2 7.2c1.7 0 3.3-.6 4.5-1.6l3 3c.*******.5.2s.4-.1.5-.2c.3-.4.3-1 0-1.3zM5.6 11.2c0-3.1 2.5-5.5 5.5-5.5s5.6 2.4 5.6 5.5-2.5 5.5-5.5 5.5-5.6-2.5-5.6-5.5z"}))},category:"blocksy-blocks",attributes:Nl(Nl({},Ml),Cl),supports:{spacing:{margin:!0,__experimentalDefaultControls:{margin:!0}},__experimentalBorder:{color:!1,radius:!0,width:!1,__experimentalSkipSerialization:!0,__experimentalDefaultControls:{color:!1,radius:!0,width:!1}}},edit:function(e){return(0,r.createElement)(Bl,e)},save:function(){return(0,r.createElement)("div",null,"Blocksy: Search Block")}})}()}();