/*! For license information please see editor-canvas.js.LICENSE.txt */
!function(){"use strict";var e,t,n={"./node_modules/react/cjs/react-jsx-runtime.development.js":function(e,t,n){!function(){var e,r=n("react"),o=Symbol.for("react.element"),i=Symbol.for("react.portal"),l=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),a=Symbol.for("react.context"),f=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),m=Symbol.for("react.suspense_list"),p=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),v=Symbol.for("react.offscreen"),h=Symbol.iterator,y=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function b(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];!function(e,t,n){var r=y.ReactDebugCurrentFrame.getStackAddendum();""!==r&&(t+="%s",n=n.concat([r]));var o=n.map((function(e){return String(e)}));o.unshift("Warning: "+t),Function.prototype.apply.call(console.error,console,o)}(0,e,n)}function w(e){return e.displayName||"Context"}function E(e){if(null==e)return null;if("number"==typeof e.tag&&b("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),"function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case l:return"Fragment";case i:return"Portal";case u:return"Profiler";case s:return"StrictMode";case d:return"Suspense";case m:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case a:return w(e)+".Consumer";case c:return w(e._context)+".Provider";case f:return function(e,t,n){var r=e.displayName;if(r)return r;var o=t.displayName||t.name||"";return""!==o?n+"("+o+")":n}(e,e.render,"ForwardRef");case p:var t=e.displayName||null;return null!==t?t:E(e.type)||"Memo";case g:var n=e,r=n._payload,o=n._init;try{return E(o(r))}catch(e){return null}}return null}e=Symbol.for("react.module.reference");var x,R,k,S,T,O,C,P=Object.assign,A=0;function L(){}L.__reactDisabledLog=!0;var I,M=y.ReactCurrentDispatcher;function _(e,t,n){if(void 0===I)try{throw Error()}catch(e){var r=e.stack.trim().match(/\n( *(at )?)/);I=r&&r[1]||""}return"\n"+I+e}var j,D=!1,N="function"==typeof WeakMap?WeakMap:Map;function F(e,t){if(!e||D)return"";var n,r=j.get(e);if(void 0!==r)return r;D=!0;var o,i=Error.prepareStackTrace;Error.prepareStackTrace=void 0,o=M.current,M.current=null,function(){if(0===A){x=console.log,R=console.info,k=console.warn,S=console.error,T=console.group,O=console.groupCollapsed,C=console.groupEnd;var e={configurable:!0,enumerable:!0,value:L,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}A++}();try{if(t){var l=function(){throw Error()};if(Object.defineProperty(l.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(l,[])}catch(e){n=e}Reflect.construct(e,[],l)}else{try{l.call()}catch(e){n=e}e.call(l.prototype)}}else{try{throw Error()}catch(e){n=e}e()}}catch(t){if(t&&n&&"string"==typeof t.stack){for(var s=t.stack.split("\n"),u=n.stack.split("\n"),c=s.length-1,a=u.length-1;c>=1&&a>=0&&s[c]!==u[a];)a--;for(;c>=1&&a>=0;c--,a--)if(s[c]!==u[a]){if(1!==c||1!==a)do{if(c--,--a<0||s[c]!==u[a]){var f="\n"+s[c].replace(" at new "," at ");return e.displayName&&f.includes("<anonymous>")&&(f=f.replace("<anonymous>",e.displayName)),"function"==typeof e&&j.set(e,f),f}}while(c>=1&&a>=0);break}}}finally{D=!1,M.current=o,function(){if(0==--A){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:P({},e,{value:x}),info:P({},e,{value:R}),warn:P({},e,{value:k}),error:P({},e,{value:S}),group:P({},e,{value:T}),groupCollapsed:P({},e,{value:O}),groupEnd:P({},e,{value:C})})}A<0&&b("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}(),Error.prepareStackTrace=i}var d=e?e.displayName||e.name:"",m=d?_(d):"";return"function"==typeof e&&j.set(e,m),m}function $(e,t,n){if(null==e)return"";if("function"==typeof e)return F(e,!(!(r=e.prototype)||!r.isReactComponent));var r;if("string"==typeof e)return _(e);switch(e){case d:return _("Suspense");case m:return _("SuspenseList")}if("object"==typeof e)switch(e.$$typeof){case f:return F(e.render,!1);case p:return $(e.type,t,n);case g:var o=e,i=o._payload,l=o._init;try{return $(l(i),t,n)}catch(e){}}return""}j=new N;var H=Object.prototype.hasOwnProperty,W={},V=y.ReactDebugCurrentFrame;function B(e){if(e){var t=e._owner,n=$(e.type,e._source,t?t.type:null);V.setExtraStackFrame(n)}else V.setExtraStackFrame(null)}var z=Array.isArray;function K(e){return z(e)}function U(e){return""+e}function q(e){if(function(e){try{return U(e),!1}catch(e){return!0}}(e))return b("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",function(e){return"function"==typeof Symbol&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||"Object"}(e)),U(e)}var Y,X,G,J=y.ReactCurrentOwner,Z={key:!0,ref:!0,__self:!0,__source:!0};G={};var Q,ee=y.ReactCurrentOwner,te=y.ReactDebugCurrentFrame;function ne(e){if(e){var t=e._owner,n=$(e.type,e._source,t?t.type:null);te.setExtraStackFrame(n)}else te.setExtraStackFrame(null)}function re(e){return"object"==typeof e&&null!==e&&e.$$typeof===o}function oe(){if(ee.current){var e=E(ee.current.type);if(e)return"\n\nCheck the render method of `"+e+"`."}return""}Q=!1;var ie={};function le(e,t){if(e._store&&!e._store.validated&&null==e.key){e._store.validated=!0;var n=function(e){var t=oe();if(!t){var n="string"==typeof e?e:e.displayName||e.name;n&&(t="\n\nCheck the top-level render call using <"+n+">.")}return t}(t);if(!ie[n]){ie[n]=!0;var r="";e&&e._owner&&e._owner!==ee.current&&(r=" It was passed a child from "+E(e._owner.type)+"."),ne(e),b('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',n,r),ne(null)}}}function se(e,t){if("object"==typeof e)if(K(e))for(var n=0;n<e.length;n++){var r=e[n];re(r)&&le(r,t)}else if(re(e))e._store&&(e._store.validated=!0);else if(e){var o=function(e){if(null===e||"object"!=typeof e)return null;var t=h&&e[h]||e["@@iterator"];return"function"==typeof t?t:null}(e);if("function"==typeof o&&o!==e.entries)for(var i,l=o.call(e);!(i=l.next()).done;)re(i.value)&&le(i.value,t)}}var ue={};function ce(t,n,r,i,h,y){var w=function(t){return"string"==typeof t||"function"==typeof t||t===l||t===u||t===s||t===d||t===m||t===v||"object"==typeof t&&null!==t&&(t.$$typeof===g||t.$$typeof===p||t.$$typeof===c||t.$$typeof===a||t.$$typeof===f||t.$$typeof===e||void 0!==t.getModuleId)}(t);if(!w){var x="";(void 0===t||"object"==typeof t&&null!==t&&0===Object.keys(t).length)&&(x+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var R,k=function(e){return void 0!==e?"\n\nCheck your code at "+e.fileName.replace(/^.*[\\\/]/,"")+":"+e.lineNumber+".":""}(h);x+=k||oe(),null===t?R="null":K(t)?R="array":void 0!==t&&t.$$typeof===o?(R="<"+(E(t.type)||"Unknown")+" />",x=" Did you accidentally export a JSX literal instead of a component?"):R=typeof t,b("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",R,x)}var S=function(e,t,n,r,i){var l,s={},u=null,c=null;for(l in void 0!==n&&(q(n),u=""+n),function(e){if(H.call(e,"key")){var t=Object.getOwnPropertyDescriptor(e,"key").get;if(t&&t.isReactWarning)return!1}return void 0!==e.key}(t)&&(q(t.key),u=""+t.key),function(e){if(H.call(e,"ref")){var t=Object.getOwnPropertyDescriptor(e,"ref").get;if(t&&t.isReactWarning)return!1}return void 0!==e.ref}(t)&&(c=t.ref,function(e,t){if("string"==typeof e.ref&&J.current&&t&&J.current.stateNode!==t){var n=E(J.current.type);G[n]||(b('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',E(J.current.type),e.ref),G[n]=!0)}}(t,i)),t)H.call(t,l)&&!Z.hasOwnProperty(l)&&(s[l]=t[l]);if(e&&e.defaultProps){var a=e.defaultProps;for(l in a)void 0===s[l]&&(s[l]=a[l])}if(u||c){var f="function"==typeof e?e.displayName||e.name||"Unknown":e;u&&function(e,t){var n=function(){Y||(Y=!0,b("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",t))};n.isReactWarning=!0,Object.defineProperty(e,"key",{get:n,configurable:!0})}(s,f),c&&function(e,t){var n=function(){X||(X=!0,b("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",t))};n.isReactWarning=!0,Object.defineProperty(e,"ref",{get:n,configurable:!0})}(s,f)}return function(e,t,n,r,i,l,s){var u={$$typeof:o,type:e,key:t,ref:n,props:s,_owner:l,_store:{}};return Object.defineProperty(u._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(u,"_self",{configurable:!1,enumerable:!1,writable:!1,value:r}),Object.defineProperty(u,"_source",{configurable:!1,enumerable:!1,writable:!1,value:i}),Object.freeze&&(Object.freeze(u.props),Object.freeze(u)),u}(e,u,c,i,r,J.current,s)}(t,n,r,h,y);if(null==S)return S;if(w){var T=n.children;if(void 0!==T)if(i)if(K(T)){for(var O=0;O<T.length;O++)se(T[O],t);Object.freeze&&Object.freeze(T)}else b("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else se(T,t)}if(H.call(n,"key")){var C=E(t),P=Object.keys(n).filter((function(e){return"key"!==e})),A=P.length>0?"{key: someKey, "+P.join(": ..., ")+": ...}":"{key: someKey}";ue[C+A]||(b('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />',A,C,P.length>0?"{"+P.join(": ..., ")+": ...}":"{}",C),ue[C+A]=!0)}return t===l?function(e){for(var t=Object.keys(e.props),n=0;n<t.length;n++){var r=t[n];if("children"!==r&&"key"!==r){ne(e),b("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",r),ne(null);break}}null!==e.ref&&(ne(e),b("Invalid attribute `ref` supplied to `React.Fragment`."),ne(null))}(S):function(e){var t,n=e.type;if(null!=n&&"string"!=typeof n){if("function"==typeof n)t=n.propTypes;else{if("object"!=typeof n||n.$$typeof!==f&&n.$$typeof!==p)return;t=n.propTypes}if(t){var r=E(n);!function(e,t,n,r,o){var i=Function.call.bind(H);for(var l in e)if(i(e,l)){var s=void 0;try{if("function"!=typeof e[l]){var u=Error((r||"React class")+": "+n+" type `"+l+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[l]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw u.name="Invariant Violation",u}s=e[l](t,l,r,n,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(e){s=e}!s||s instanceof Error||(B(o),b("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",r||"React class",n,l,typeof s),B(null)),s instanceof Error&&!(s.message in W)&&(W[s.message]=!0,B(o),b("Failed %s type: %s",n,s.message),B(null))}}(t,e.props,"prop",r,e)}else void 0===n.PropTypes||Q||(Q=!0,b("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",E(n)||"Unknown"));"function"!=typeof n.getDefaultProps||n.getDefaultProps.isReactClassApproved||b("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}(S),S}t.Fragment=l,t.jsx=function(e,t,n){return ce(e,t,n,!1)},t.jsxs=function(e,t,n){return ce(e,t,n,!0)}}()},"./node_modules/react/jsx-runtime.js":function(e,t,n){e.exports=n("./node_modules/react/cjs/react-jsx-runtime.development.js")},"./node_modules/tabbable/dist/index.esm.js":function(e,t,n){n.r(t),n.d(t,{focusable:function(){return E},getTabIndex:function(){return d},isFocusable:function(){return k},isTabbable:function(){return x},tabbable:function(){return w}});var r=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],o=r.join(","),i="undefined"==typeof Element,l=i?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,s=!i&&Element.prototype.getRootNode?function(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},u=function e(t,n){var r;void 0===n&&(n=!0);var o=null==t||null===(r=t.getAttribute)||void 0===r?void 0:r.call(t,"inert");return""===o||"true"===o||n&&t&&e(t.parentNode)},c=function(e,t,n){if(u(e))return[];var r=Array.prototype.slice.apply(e.querySelectorAll(o));return t&&l.call(e,o)&&r.unshift(e),r.filter(n)},a=function e(t,n,r){for(var i=[],s=Array.from(t);s.length;){var c=s.shift();if(!u(c,!1))if("SLOT"===c.tagName){var a=c.assignedElements(),f=e(a.length?a:c.children,!0,r);r.flatten?i.push.apply(i,f):i.push({scopeParent:c,candidates:f})}else{l.call(c,o)&&r.filter(c)&&(n||!t.includes(c))&&i.push(c);var d=c.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(c),m=!u(d,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(c));if(d&&m){var p=e(!0===d?c.children:d.children,!0,r);r.flatten?i.push.apply(i,p):i.push({scopeParent:c,candidates:p})}else s.unshift.apply(s,c.children)}}return i},f=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},d=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||function(e){var t,n=null==e||null===(t=e.getAttribute)||void 0===t?void 0:t.call(e,"contenteditable");return""===n||"true"===n}(e))&&!f(e)?0:e.tabIndex},m=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},p=function(e){return"INPUT"===e.tagName},g=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},v=function(e,t){return!(t.disabled||u(t)||function(e){return p(e)&&"hidden"===e.type}(t)||function(e,t){var n=t.displayCheck,r=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var o=l.call(e,"details>summary:first-of-type")?e.parentElement:e;if(l.call(o,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return g(e)}else{if("function"==typeof r){for(var i=e;e;){var u=e.parentElement,c=s(e);if(u&&!u.shadowRoot&&!0===r(u))return g(e);e=e.assignedSlot?e.assignedSlot:u||c===e.ownerDocument?u:c.host}e=i}if(function(e){var t,n,r,o,i=e&&s(e),l=null===(t=i)||void 0===t?void 0:t.host,u=!1;if(i&&i!==e)for(u=!!(null!==(n=l)&&void 0!==n&&null!==(r=n.ownerDocument)&&void 0!==r&&r.contains(l)||null!=e&&null!==(o=e.ownerDocument)&&void 0!==o&&o.contains(e));!u&&l;){var c,a,f;u=!(null===(a=l=null===(c=i=s(l))||void 0===c?void 0:c.host)||void 0===a||null===(f=a.ownerDocument)||void 0===f||!f.contains(l))}return u}(e))return!e.getClientRects().length;if("legacy-full"!==n)return!0}return!1}(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some((function(e){return"SUMMARY"===e.tagName}))}(t)||function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var r=t.children.item(n);if("LEGEND"===r.tagName)return!!l.call(t,"fieldset[disabled] *")||!r.contains(e)}return!0}t=t.parentElement}return!1}(t))},h=function(e,t){return!(function(e){return function(e){return p(e)&&"radio"===e.type}(e)&&!function(e){if(!e.name)return!0;var t,n=e.form||s(e),r=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=r(window.CSS.escape(e.name));else try{t=r(e.name)}catch(e){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",e.message),!1}var o=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form);return!o||o===e}(e)}(t)||d(t)<0||!v(e,t))},y=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},b=function e(t){var n=[],r=[];return t.forEach((function(t,o){var i=!!t.scopeParent,l=i?t.scopeParent:t,s=function(e,t){var n=d(e);return n<0&&t&&!f(e)?0:n}(l,i),u=i?e(t.candidates):l;0===s?i?n.push.apply(n,u):n.push(l):r.push({documentOrder:o,tabIndex:s,item:t,isScope:i,content:u})})),r.sort(m).reduce((function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e}),[]).concat(n)},w=function(e,t){var n;return n=(t=t||{}).getShadowRoot?a([e],t.includeContainer,{filter:h.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:y}):c(e,t.includeContainer,h.bind(null,t)),b(n)},E=function(e,t){return(t=t||{}).getShadowRoot?a([e],t.includeContainer,{filter:v.bind(null,t),flatten:!0,getShadowRoot:t.getShadowRoot}):c(e,t.includeContainer,v.bind(null,t))},x=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==l.call(e,o)&&h(t,e)},R=r.concat("iframe").join(","),k=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==l.call(e,R)&&v(t,e)}},react:function(e){e.exports=window.React},"react-dom":function(e){e.exports=window.ReactDOM},"@elementor/editor":function(e){e.exports=window.elementorV2.editor},"@elementor/editor-elements":function(e){e.exports=window.elementorV2.editorElements},"@elementor/editor-notifications":function(e){e.exports=window.elementorV2.editorNotifications},"@elementor/editor-props":function(e){e.exports=window.elementorV2.editorProps},"@elementor/editor-responsive":function(e){e.exports=window.elementorV2.editorResponsive},"@elementor/editor-styles":function(e){e.exports=window.elementorV2.editorStyles},"@elementor/editor-styles-repository":function(e){e.exports=window.elementorV2.editorStylesRepository},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/twing":function(e){e.exports=window.elementorV2.twing},"@elementor/ui":function(e){e.exports=window.elementorV2.ui},"@elementor/utils":function(e){e.exports=window.elementorV2.utils},"@elementor/wp-media":function(e){e.exports=window.elementorV2.wpMedia},"@wordpress/i18n":function(e){e.exports=window.wp.i18n},"./node_modules/@floating-ui/core/dist/floating-ui.core.mjs":function(e,t,n){n.r(t),n.d(t,{arrow:function(){return s},autoPlacement:function(){return u},computePosition:function(){return i},detectOverflow:function(){return l},flip:function(){return c},hide:function(){return d},inline:function(){return p},limitShift:function(){return h},offset:function(){return g},rectToClientRect:function(){return r.rectToClientRect},shift:function(){return v},size:function(){return y}});var r=n("./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs");function o(e,t,n){let{reference:o,floating:i}=e;const l=(0,r.getSideAxis)(t),s=(0,r.getAlignmentAxis)(t),u=(0,r.getAxisLength)(s),c=(0,r.getSide)(t),a="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,m=o[u]/2-i[u]/2;let p;switch(c){case"top":p={x:f,y:o.y-i.height};break;case"bottom":p={x:f,y:o.y+o.height};break;case"right":p={x:o.x+o.width,y:d};break;case"left":p={x:o.x-i.width,y:d};break;default:p={x:o.x,y:o.y}}switch((0,r.getAlignment)(t)){case"start":p[s]-=m*(n&&a?-1:1);break;case"end":p[s]+=m*(n&&a?-1:1)}return p}const i=async(e,t,n)=>{const{placement:r="bottom",strategy:i="absolute",middleware:l=[],platform:s}=n,u=l.filter(Boolean),c=await(null==s.isRTL?void 0:s.isRTL(t));let a=await s.getElementRects({reference:e,floating:t,strategy:i}),{x:f,y:d}=o(a,r,c),m=r,p={},g=0;for(let n=0;n<u.length;n++){const{name:l,fn:v}=u[n],{x:h,y:y,data:b,reset:w}=await v({x:f,y:d,initialPlacement:r,placement:m,strategy:i,middlewareData:p,rects:a,platform:s,elements:{reference:e,floating:t}});f=null!=h?h:f,d=null!=y?y:d,p={...p,[l]:{...p[l],...b}},w&&g<=50&&(g++,"object"==typeof w&&(w.placement&&(m=w.placement),w.rects&&(a=!0===w.rects?await s.getElementRects({reference:e,floating:t,strategy:i}):w.rects),({x:f,y:d}=o(a,m,c))),n=-1)}return{x:f,y:d,placement:m,strategy:i,middlewareData:p}};async function l(e,t){var n;void 0===t&&(t={});const{x:o,y:i,platform:l,rects:s,elements:u,strategy:c}=e,{boundary:a="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:m=!1,padding:p=0}=(0,r.evaluate)(t,e),g=(0,r.getPaddingObject)(p),v=u[m?"floating"===d?"reference":"floating":d],h=(0,r.rectToClientRect)(await l.getClippingRect({element:null==(n=await(null==l.isElement?void 0:l.isElement(v)))||n?v:v.contextElement||await(null==l.getDocumentElement?void 0:l.getDocumentElement(u.floating)),boundary:a,rootBoundary:f,strategy:c})),y="floating"===d?{x:o,y:i,width:s.floating.width,height:s.floating.height}:s.reference,b=await(null==l.getOffsetParent?void 0:l.getOffsetParent(u.floating)),w=await(null==l.isElement?void 0:l.isElement(b))&&await(null==l.getScale?void 0:l.getScale(b))||{x:1,y:1},E=(0,r.rectToClientRect)(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:u,rect:y,offsetParent:b,strategy:c}):y);return{top:(h.top-E.top+g.top)/w.y,bottom:(E.bottom-h.bottom+g.bottom)/w.y,left:(h.left-E.left+g.left)/w.x,right:(E.right-h.right+g.right)/w.x}}const s=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:i,rects:l,platform:s,elements:u,middlewareData:c}=t,{element:a,padding:f=0}=(0,r.evaluate)(e,t)||{};if(null==a)return{};const d=(0,r.getPaddingObject)(f),m={x:n,y:o},p=(0,r.getAlignmentAxis)(i),g=(0,r.getAxisLength)(p),v=await s.getDimensions(a),h="y"===p,y=h?"top":"left",b=h?"bottom":"right",w=h?"clientHeight":"clientWidth",E=l.reference[g]+l.reference[p]-m[p]-l.floating[g],x=m[p]-l.reference[p],R=await(null==s.getOffsetParent?void 0:s.getOffsetParent(a));let k=R?R[w]:0;k&&await(null==s.isElement?void 0:s.isElement(R))||(k=u.floating[w]||l.floating[g]);const S=E/2-x/2,T=k/2-v[g]/2-1,O=(0,r.min)(d[y],T),C=(0,r.min)(d[b],T),P=O,A=k-v[g]-C,L=k/2-v[g]/2+S,I=(0,r.clamp)(P,L,A),M=!c.arrow&&null!=(0,r.getAlignment)(i)&&L!==I&&l.reference[g]/2-(L<P?O:C)-v[g]/2<0,_=M?L<P?L-P:L-A:0;return{[p]:m[p]+_,data:{[p]:I,centerOffset:L-I-_,...M&&{alignmentOffset:_}},reset:M}}}),u=function(e){return void 0===e&&(e={}),{name:"autoPlacement",options:e,async fn(t){var n,o,i;const{rects:s,middlewareData:u,placement:c,platform:a,elements:f}=t,{crossAxis:d=!1,alignment:m,allowedPlacements:p=r.placements,autoAlignment:g=!0,...v}=(0,r.evaluate)(e,t),h=void 0!==m||p===r.placements?function(e,t,n){return(e?[...n.filter((t=>(0,r.getAlignment)(t)===e)),...n.filter((t=>(0,r.getAlignment)(t)!==e))]:n.filter((e=>(0,r.getSide)(e)===e))).filter((n=>!e||(0,r.getAlignment)(n)===e||!!t&&(0,r.getOppositeAlignmentPlacement)(n)!==n))}(m||null,g,p):p,y=await l(t,v),b=(null==(n=u.autoPlacement)?void 0:n.index)||0,w=h[b];if(null==w)return{};const E=(0,r.getAlignmentSides)(w,s,await(null==a.isRTL?void 0:a.isRTL(f.floating)));if(c!==w)return{reset:{placement:h[0]}};const x=[y[(0,r.getSide)(w)],y[E[0]],y[E[1]]],R=[...(null==(o=u.autoPlacement)?void 0:o.overflows)||[],{placement:w,overflows:x}],k=h[b+1];if(k)return{data:{index:b+1,overflows:R},reset:{placement:k}};const S=R.map((e=>{const t=(0,r.getAlignment)(e.placement);return[e.placement,t&&d?e.overflows.slice(0,2).reduce(((e,t)=>e+t),0):e.overflows[0],e.overflows]})).sort(((e,t)=>e[1]-t[1])),T=(null==(i=S.filter((e=>e[2].slice(0,(0,r.getAlignment)(e[0])?2:3).every((e=>e<=0))))[0])?void 0:i[0])||S[0][0];return T!==c?{data:{index:b+1,overflows:R},reset:{placement:T}}:{}}}},c=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:i,middlewareData:s,rects:u,initialPlacement:c,platform:a,elements:f}=t,{mainAxis:d=!0,crossAxis:m=!0,fallbackPlacements:p,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:h=!0,...y}=(0,r.evaluate)(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};const b=(0,r.getSide)(i),w=(0,r.getSideAxis)(c),E=(0,r.getSide)(c)===c,x=await(null==a.isRTL?void 0:a.isRTL(f.floating)),R=p||(E||!h?[(0,r.getOppositePlacement)(c)]:(0,r.getExpandedPlacements)(c)),k="none"!==v;!p&&k&&R.push(...(0,r.getOppositeAxisPlacements)(c,h,v,x));const S=[c,...R],T=await l(t,y),O=[];let C=(null==(o=s.flip)?void 0:o.overflows)||[];if(d&&O.push(T[b]),m){const e=(0,r.getAlignmentSides)(i,u,x);O.push(T[e[0]],T[e[1]])}if(C=[...C,{placement:i,overflows:O}],!O.every((e=>e<=0))){var P,A;const e=((null==(P=s.flip)?void 0:P.index)||0)+1,t=S[e];if(t)return{data:{index:e,overflows:C},reset:{placement:t}};let n=null==(A=C.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:A.placement;if(!n)switch(g){case"bestFit":{var L;const e=null==(L=C.filter((e=>{if(k){const t=(0,r.getSideAxis)(e.placement);return t===w||"y"===t}return!0})).map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:L[0];e&&(n=e);break}case"initialPlacement":n=c}if(i!==n)return{reset:{placement:n}}}return{}}}};function a(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function f(e){return r.sides.some((t=>e[t]>=0))}const d=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:o="referenceHidden",...i}=(0,r.evaluate)(e,t);switch(o){case"referenceHidden":{const e=a(await l(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:f(e)}}}case"escaped":{const e=a(await l(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:f(e)}}}default:return{}}}}};function m(e){const t=(0,r.min)(...e.map((e=>e.left))),n=(0,r.min)(...e.map((e=>e.top)));return{x:t,y:n,width:(0,r.max)(...e.map((e=>e.right)))-t,height:(0,r.max)(...e.map((e=>e.bottom)))-n}}const p=function(e){return void 0===e&&(e={}),{name:"inline",options:e,async fn(t){const{placement:n,elements:o,rects:i,platform:l,strategy:s}=t,{padding:u=2,x:c,y:a}=(0,r.evaluate)(e,t),f=Array.from(await(null==l.getClientRects?void 0:l.getClientRects(o.reference))||[]),d=function(e){const t=e.slice().sort(((e,t)=>e.y-t.y)),n=[];let o=null;for(let e=0;e<t.length;e++){const r=t[e];!o||r.y-o.y>o.height/2?n.push([r]):n[n.length-1].push(r),o=r}return n.map((e=>(0,r.rectToClientRect)(m(e))))}(f),p=(0,r.rectToClientRect)(m(f)),g=(0,r.getPaddingObject)(u),v=await l.getElementRects({reference:{getBoundingClientRect:function(){if(2===d.length&&d[0].left>d[1].right&&null!=c&&null!=a)return d.find((e=>c>e.left-g.left&&c<e.right+g.right&&a>e.top-g.top&&a<e.bottom+g.bottom))||p;if(d.length>=2){if("y"===(0,r.getSideAxis)(n)){const e=d[0],t=d[d.length-1],o="top"===(0,r.getSide)(n),i=e.top,l=t.bottom,s=o?e.left:t.left,u=o?e.right:t.right;return{top:i,bottom:l,left:s,right:u,width:u-s,height:l-i,x:s,y:i}}const e="left"===(0,r.getSide)(n),t=(0,r.max)(...d.map((e=>e.right))),o=(0,r.min)(...d.map((e=>e.left))),i=d.filter((n=>e?n.left===o:n.right===t)),l=i[0].top,s=i[i.length-1].bottom;return{top:l,bottom:s,left:o,right:t,width:t-o,height:s-l,x:o,y:l}}return p}},floating:o.floating,strategy:s});return i.reference.x!==v.reference.x||i.reference.y!==v.reference.y||i.reference.width!==v.reference.width||i.reference.height!==v.reference.height?{reset:{rects:v}}:{}}}},g=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:i,y:l,placement:s,middlewareData:u}=t,c=await async function(e,t){const{placement:n,platform:o,elements:i}=e,l=await(null==o.isRTL?void 0:o.isRTL(i.floating)),s=(0,r.getSide)(n),u=(0,r.getAlignment)(n),c="y"===(0,r.getSideAxis)(n),a=["left","top"].includes(s)?-1:1,f=l&&c?-1:1,d=(0,r.evaluate)(t,e);let{mainAxis:m,crossAxis:p,alignmentAxis:g}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return u&&"number"==typeof g&&(p="end"===u?-1*g:g),c?{x:p*f,y:m*a}:{x:m*a,y:p*f}}(t,e);return s===(null==(n=u.offset)?void 0:n.placement)&&null!=(o=u.arrow)&&o.alignmentOffset?{}:{x:i+c.x,y:l+c.y,data:{...c,placement:s}}}}},v=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:i}=t,{mainAxis:s=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...a}=(0,r.evaluate)(e,t),f={x:n,y:o},d=await l(t,a),m=(0,r.getSideAxis)((0,r.getSide)(i)),p=(0,r.getOppositeAxis)(m);let g=f[p],v=f[m];if(s){const e="y"===p?"bottom":"right",t=g+d["y"===p?"top":"left"],n=g-d[e];g=(0,r.clamp)(t,g,n)}if(u){const e="y"===m?"bottom":"right",t=v+d["y"===m?"top":"left"],n=v-d[e];v=(0,r.clamp)(t,v,n)}const h=c.fn({...t,[p]:g,[m]:v});return{...h,data:{x:h.x-n,y:h.y-o,enabled:{[p]:s,[m]:u}}}}}},h=function(e){return void 0===e&&(e={}),{options:e,fn(t){const{x:n,y:o,placement:i,rects:l,middlewareData:s}=t,{offset:u=0,mainAxis:c=!0,crossAxis:a=!0}=(0,r.evaluate)(e,t),f={x:n,y:o},d=(0,r.getSideAxis)(i),m=(0,r.getOppositeAxis)(d);let p=f[m],g=f[d];const v=(0,r.evaluate)(u,t),h="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(c){const e="y"===m?"height":"width",t=l.reference[m]-l.floating[e]+h.mainAxis,n=l.reference[m]+l.reference[e]-h.mainAxis;p<t?p=t:p>n&&(p=n)}if(a){var y,b;const e="y"===m?"width":"height",t=["top","left"].includes((0,r.getSide)(i)),n=l.reference[d]-l.floating[e]+(t&&(null==(y=s.offset)?void 0:y[d])||0)+(t?0:h.crossAxis),o=l.reference[d]+l.reference[e]+(t?0:(null==(b=s.offset)?void 0:b[d])||0)-(t?h.crossAxis:0);g<n?g=n:g>o&&(g=o)}return{[m]:p,[d]:g}}}},y=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:i,rects:s,platform:u,elements:c}=t,{apply:a=()=>{},...f}=(0,r.evaluate)(e,t),d=await l(t,f),m=(0,r.getSide)(i),p=(0,r.getAlignment)(i),g="y"===(0,r.getSideAxis)(i),{width:v,height:h}=s.floating;let y,b;"top"===m||"bottom"===m?(y=m,b=p===(await(null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(b=m,y="end"===p?"top":"bottom");const w=h-d.top-d.bottom,E=v-d.left-d.right,x=(0,r.min)(h-d[y],w),R=(0,r.min)(v-d[b],E),k=!t.middlewareData.shift;let S=x,T=R;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(T=E),null!=(o=t.middlewareData.shift)&&o.enabled.y&&(S=w),k&&!p){const e=(0,r.max)(d.left,0),t=(0,r.max)(d.right,0),n=(0,r.max)(d.top,0),o=(0,r.max)(d.bottom,0);g?T=v-2*(0!==e||0!==t?e+t:(0,r.max)(d.left,d.right)):S=h-2*(0!==n||0!==o?n+o:(0,r.max)(d.top,d.bottom))}await a({...t,availableWidth:T,availableHeight:S});const O=await u.getDimensions(c.floating);return v!==O.width||h!==O.height?{reset:{rects:!0}}:{}}}}},"./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs":function(e,t,n){n.r(t),n.d(t,{arrow:function(){return P},autoPlacement:function(){return k},autoUpdate:function(){return E},computePosition:function(){return I},detectOverflow:function(){return x},flip:function(){return T},getOverflowAncestors:function(){return i.getOverflowAncestors},hide:function(){return C},inline:function(){return A},limitShift:function(){return L},offset:function(){return R},platform:function(){return w},shift:function(){return S},size:function(){return O}});var r=n("./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs"),o=n("./node_modules/@floating-ui/core/dist/floating-ui.core.mjs"),i=n("./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs");function l(e){const t=(0,i.getComputedStyle)(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const l=(0,i.isHTMLElement)(e),s=l?e.offsetWidth:n,u=l?e.offsetHeight:o,c=(0,r.round)(n)!==s||(0,r.round)(o)!==u;return c&&(n=s,o=u),{width:n,height:o,$:c}}function s(e){return(0,i.isElement)(e)?e:e.contextElement}function u(e){const t=s(e);if(!(0,i.isHTMLElement)(t))return(0,r.createCoords)(1);const n=t.getBoundingClientRect(),{width:o,height:u,$:c}=l(t);let a=(c?(0,r.round)(n.width):n.width)/o,f=(c?(0,r.round)(n.height):n.height)/u;return a&&Number.isFinite(a)||(a=1),f&&Number.isFinite(f)||(f=1),{x:a,y:f}}const c=(0,r.createCoords)(0);function a(e){const t=(0,i.getWindow)(e);return(0,i.isWebKit)()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:c}function f(e,t,n,o){void 0===t&&(t=!1),void 0===n&&(n=!1);const l=e.getBoundingClientRect(),c=s(e);let f=(0,r.createCoords)(1);t&&(o?(0,i.isElement)(o)&&(f=u(o)):f=u(e));const d=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==(0,i.getWindow)(e))&&t}(c,n,o)?a(c):(0,r.createCoords)(0);let m=(l.left+d.x)/f.x,p=(l.top+d.y)/f.y,g=l.width/f.x,v=l.height/f.y;if(c){const e=(0,i.getWindow)(c),t=o&&(0,i.isElement)(o)?(0,i.getWindow)(o):o;let n=e,r=(0,i.getFrameElement)(n);for(;r&&o&&t!==n;){const e=u(r),t=r.getBoundingClientRect(),o=(0,i.getComputedStyle)(r),l=t.left+(r.clientLeft+parseFloat(o.paddingLeft))*e.x,s=t.top+(r.clientTop+parseFloat(o.paddingTop))*e.y;m*=e.x,p*=e.y,g*=e.x,v*=e.y,m+=l,p+=s,n=(0,i.getWindow)(r),r=(0,i.getFrameElement)(n)}}return(0,r.rectToClientRect)({width:g,height:v,x:m,y:p})}function d(e,t){const n=(0,i.getNodeScroll)(e).scrollLeft;return t?t.left+n:f((0,i.getDocumentElement)(e)).left+n}function m(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:d(e,r)),y:r.top+t.scrollTop}}function p(e,t,n){let o;if("viewport"===t)o=function(e,t){const n=(0,i.getWindow)(e),r=(0,i.getDocumentElement)(e),o=n.visualViewport;let l=r.clientWidth,s=r.clientHeight,u=0,c=0;if(o){l=o.width,s=o.height;const e=(0,i.isWebKit)();(!e||e&&"fixed"===t)&&(u=o.offsetLeft,c=o.offsetTop)}return{width:l,height:s,x:u,y:c}}(e,n);else if("document"===t)o=function(e){const t=(0,i.getDocumentElement)(e),n=(0,i.getNodeScroll)(e),o=e.ownerDocument.body,l=(0,r.max)(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),s=(0,r.max)(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let u=-n.scrollLeft+d(e);const c=-n.scrollTop;return"rtl"===(0,i.getComputedStyle)(o).direction&&(u+=(0,r.max)(t.clientWidth,o.clientWidth)-l),{width:l,height:s,x:u,y:c}}((0,i.getDocumentElement)(e));else if((0,i.isElement)(t))o=function(e,t){const n=f(e,!0,"fixed"===t),o=n.top+e.clientTop,l=n.left+e.clientLeft,s=(0,i.isHTMLElement)(e)?u(e):(0,r.createCoords)(1);return{width:e.clientWidth*s.x,height:e.clientHeight*s.y,x:l*s.x,y:o*s.y}}(t,n);else{const n=a(e);o={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return(0,r.rectToClientRect)(o)}function g(e,t){const n=(0,i.getParentNode)(e);return!(n===t||!(0,i.isElement)(n)||(0,i.isLastTraversableNode)(n))&&("fixed"===(0,i.getComputedStyle)(n).position||g(n,t))}function v(e,t,n){const o=(0,i.isHTMLElement)(t),l=(0,i.getDocumentElement)(t),s="fixed"===n,u=f(e,!0,s,t);let c={scrollLeft:0,scrollTop:0};const a=(0,r.createCoords)(0);if(o||!o&&!s)if(("body"!==(0,i.getNodeName)(t)||(0,i.isOverflowElement)(l))&&(c=(0,i.getNodeScroll)(t)),o){const e=f(t,!0,s,t);a.x=e.x+t.clientLeft,a.y=e.y+t.clientTop}else l&&(a.x=d(l));const p=!l||o||s?(0,r.createCoords)(0):m(l,c);return{x:u.left+c.scrollLeft-a.x-p.x,y:u.top+c.scrollTop-a.y-p.y,width:u.width,height:u.height}}function h(e){return"static"===(0,i.getComputedStyle)(e).position}function y(e,t){if(!(0,i.isHTMLElement)(e)||"fixed"===(0,i.getComputedStyle)(e).position)return null;if(t)return t(e);let n=e.offsetParent;return(0,i.getDocumentElement)(e)===n&&(n=n.ownerDocument.body),n}function b(e,t){const n=(0,i.getWindow)(e);if((0,i.isTopLayer)(e))return n;if(!(0,i.isHTMLElement)(e)){let t=(0,i.getParentNode)(e);for(;t&&!(0,i.isLastTraversableNode)(t);){if((0,i.isElement)(t)&&!h(t))return t;t=(0,i.getParentNode)(t)}return n}let r=y(e,t);for(;r&&(0,i.isTableElement)(r)&&h(r);)r=y(r,t);return r&&(0,i.isLastTraversableNode)(r)&&h(r)&&!(0,i.isContainingBlock)(r)?n:r||(0,i.getContainingBlock)(e)||n}const w={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:o,strategy:l}=e;const s="fixed"===l,c=(0,i.getDocumentElement)(o),a=!!t&&(0,i.isTopLayer)(t.floating);if(o===c||a&&s)return n;let d={scrollLeft:0,scrollTop:0},p=(0,r.createCoords)(1);const g=(0,r.createCoords)(0),v=(0,i.isHTMLElement)(o);if((v||!v&&!s)&&(("body"!==(0,i.getNodeName)(o)||(0,i.isOverflowElement)(c))&&(d=(0,i.getNodeScroll)(o)),(0,i.isHTMLElement)(o))){const e=f(o);p=u(o),g.x=e.x+o.clientLeft,g.y=e.y+o.clientTop}const h=!c||v||s?(0,r.createCoords)(0):m(c,d,!0);return{width:n.width*p.x,height:n.height*p.y,x:n.x*p.x-d.scrollLeft*p.x+g.x+h.x,y:n.y*p.y-d.scrollTop*p.y+g.y+h.y}},getDocumentElement:i.getDocumentElement,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:o,strategy:l}=e;const s=[..."clippingAncestors"===n?(0,i.isTopLayer)(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=(0,i.getOverflowAncestors)(e,[],!1).filter((e=>(0,i.isElement)(e)&&"body"!==(0,i.getNodeName)(e))),o=null;const l="fixed"===(0,i.getComputedStyle)(e).position;let s=l?(0,i.getParentNode)(e):e;for(;(0,i.isElement)(s)&&!(0,i.isLastTraversableNode)(s);){const t=(0,i.getComputedStyle)(s),n=(0,i.isContainingBlock)(s);n||"fixed"!==t.position||(o=null),(l?!n&&!o:!n&&"static"===t.position&&o&&["absolute","fixed"].includes(o.position)||(0,i.isOverflowElement)(s)&&!n&&g(e,s))?r=r.filter((e=>e!==s)):o=t,s=(0,i.getParentNode)(s)}return t.set(e,r),r}(t,this._c):[].concat(n),o],u=s[0],c=s.reduce(((e,n)=>{const o=p(t,n,l);return e.top=(0,r.max)(o.top,e.top),e.right=(0,r.min)(o.right,e.right),e.bottom=(0,r.min)(o.bottom,e.bottom),e.left=(0,r.max)(o.left,e.left),e}),p(t,u,l));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:b,getElementRects:async function(e){const t=this.getOffsetParent||b,n=this.getDimensions,r=await n(e.floating);return{reference:v(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=l(e);return{width:t,height:n}},getScale:u,isElement:i.isElement,isRTL:function(e){return"rtl"===(0,i.getComputedStyle)(e).direction}};function E(e,t,n,o){void 0===o&&(o={});const{ancestorScroll:l=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:a="function"==typeof IntersectionObserver,animationFrame:d=!1}=o,m=s(e),p=l||u?[...m?(0,i.getOverflowAncestors)(m):[],...(0,i.getOverflowAncestors)(t)]:[];p.forEach((e=>{l&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)}));const g=m&&a?function(e,t){let n,o=null;const l=(0,i.getDocumentElement)(e);function s(){var e;clearTimeout(n),null==(e=o)||e.disconnect(),o=null}return function i(u,c){void 0===u&&(u=!1),void 0===c&&(c=1),s();const{left:a,top:f,width:d,height:m}=e.getBoundingClientRect();if(u||t(),!d||!m)return;const p={rootMargin:-(0,r.floor)(f)+"px "+-(0,r.floor)(l.clientWidth-(a+d))+"px "+-(0,r.floor)(l.clientHeight-(f+m))+"px "+-(0,r.floor)(a)+"px",threshold:(0,r.max)(0,(0,r.min)(1,c))||1};let g=!0;function v(e){const t=e[0].intersectionRatio;if(t!==c){if(!g)return i();t?i(!1,t):n=setTimeout((()=>{i(!1,1e-7)}),1e3)}g=!1}try{o=new IntersectionObserver(v,{...p,root:l.ownerDocument})}catch(e){o=new IntersectionObserver(v,p)}o.observe(e)}(!0),s}(m,n):null;let v,h=-1,y=null;c&&(y=new ResizeObserver((e=>{let[r]=e;r&&r.target===m&&y&&(y.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame((()=>{var e;null==(e=y)||e.observe(t)}))),n()})),m&&!d&&y.observe(m),y.observe(t));let b=d?f(e):null;return d&&function t(){const r=f(e);!b||r.x===b.x&&r.y===b.y&&r.width===b.width&&r.height===b.height||n(),b=r,v=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach((e=>{l&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)})),null==g||g(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(v)}}const x=o.detectOverflow,R=o.offset,k=o.autoPlacement,S=o.shift,T=o.flip,O=o.size,C=o.hide,P=o.arrow,A=o.inline,L=o.limitShift,I=(e,t,n)=>{const r=new Map,i={platform:w,...n},l={...i.platform,_c:r};return(0,o.computePosition)(e,t,{...i,platform:l})}},"./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs":function(e,t,n){n.r(t),n.d(t,{arrow:function(){return x},autoPlacement:function(){return b},autoUpdate:function(){return r.autoUpdate},computePosition:function(){return r.computePosition},detectOverflow:function(){return r.detectOverflow},flip:function(){return h},getOverflowAncestors:function(){return o.getOverflowAncestors},hide:function(){return w},inline:function(){return E},limitShift:function(){return v},offset:function(){return p},platform:function(){return r.platform},shift:function(){return g},size:function(){return y},useFloating:function(){return d}});var r=n("./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs"),o=n("./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs"),i=n("react"),l=n("react-dom"),s="undefined"!=typeof document?i.useLayoutEffect:i.useEffect;function u(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!=r--;)if(!u(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){const n=o[r];if(!("_owner"===n&&e.$$typeof||u(e[n],t[n])))return!1}return!0}return e!=e&&t!=t}function c(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function a(e,t){const n=c(e);return Math.round(t*n)/n}function f(e){const t=i.useRef(e);return s((()=>{t.current=e})),t}function d(e){void 0===e&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:d,elements:{reference:m,floating:p}={},transform:g=!0,whileElementsMounted:v,open:h}=e,[y,b]=i.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[w,E]=i.useState(o);u(w,o)||E(o);const[x,R]=i.useState(null),[k,S]=i.useState(null),T=i.useCallback((e=>{e!==A.current&&(A.current=e,R(e))}),[]),O=i.useCallback((e=>{e!==L.current&&(L.current=e,S(e))}),[]),C=m||x,P=p||k,A=i.useRef(null),L=i.useRef(null),I=i.useRef(y),M=null!=v,_=f(v),j=f(d),D=f(h),N=i.useCallback((()=>{if(!A.current||!L.current)return;const e={placement:t,strategy:n,middleware:w};j.current&&(e.platform=j.current),(0,r.computePosition)(A.current,L.current,e).then((e=>{const t={...e,isPositioned:!1!==D.current};F.current&&!u(I.current,t)&&(I.current=t,l.flushSync((()=>{b(t)})))}))}),[w,t,n,j,D]);s((()=>{!1===h&&I.current.isPositioned&&(I.current.isPositioned=!1,b((e=>({...e,isPositioned:!1}))))}),[h]);const F=i.useRef(!1);s((()=>(F.current=!0,()=>{F.current=!1})),[]),s((()=>{if(C&&(A.current=C),P&&(L.current=P),C&&P){if(_.current)return _.current(C,P,N);N()}}),[C,P,N,_,M]);const $=i.useMemo((()=>({reference:A,floating:L,setReference:T,setFloating:O})),[T,O]),H=i.useMemo((()=>({reference:C,floating:P})),[C,P]),W=i.useMemo((()=>{const e={position:n,left:0,top:0};if(!H.floating)return e;const t=a(H.floating,y.x),r=a(H.floating,y.y);return g?{...e,transform:"translate("+t+"px, "+r+"px)",...c(H.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}}),[n,g,H.floating,y.x,y.y]);return i.useMemo((()=>({...y,update:N,refs:$,elements:H,floatingStyles:W})),[y,N,$,H,W])}const m=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:o}="function"==typeof e?e(t):e;return n&&(i=n,{}.hasOwnProperty.call(i,"current"))?null!=n.current?(0,r.arrow)({element:n.current,padding:o}).fn(t):{}:n?(0,r.arrow)({element:n,padding:o}).fn(t):{};var i}}),p=(e,t)=>({...(0,r.offset)(e),options:[e,t]}),g=(e,t)=>({...(0,r.shift)(e),options:[e,t]}),v=(e,t)=>({...(0,r.limitShift)(e),options:[e,t]}),h=(e,t)=>({...(0,r.flip)(e),options:[e,t]}),y=(e,t)=>({...(0,r.size)(e),options:[e,t]}),b=(e,t)=>({...(0,r.autoPlacement)(e),options:[e,t]}),w=(e,t)=>({...(0,r.hide)(e),options:[e,t]}),E=(e,t)=>({...(0,r.inline)(e),options:[e,t]}),x=(e,t)=>({...m(e),options:[e,t]})},"./node_modules/@floating-ui/react/dist/floating-ui.react.mjs":function(e,t,n){var r;n.r(t),n.d(t,{Composite:function(){return W},CompositeItem:function(){return V},FloatingArrow:function(){return X},FloatingDelayGroup:function(){return me},FloatingFocusManager:function(){return Ye},FloatingList:function(){return _},FloatingNode:function(){return ne},FloatingOverlay:function(){return Je},FloatingPortal:function(){return $e},FloatingTree:function(){return re},arrow:function(){return f.arrow},autoPlacement:function(){return f.autoPlacement},autoUpdate:function(){return d.autoUpdate},computePosition:function(){return d.computePosition},detectOverflow:function(){return d.detectOverflow},flip:function(){return f.flip},getOverflowAncestors:function(){return u.getOverflowAncestors},hide:function(){return f.hide},inline:function(){return f.inline},inner:function(){return At},limitShift:function(){return f.limitShift},offset:function(){return f.offset},platform:function(){return d.platform},safePolygon:function(){return Mt},shift:function(){return f.shift},size:function(){return f.size},useClick:function(){return et},useClientPoint:function(){return nt},useDelayGroup:function(){return pe},useDelayGroupContext:function(){return de},useDismiss:function(){return lt},useFloating:function(){return ut},useFloatingNodeId:function(){return te},useFloatingParentNodeId:function(){return Q},useFloatingPortalNode:function(){return Fe},useFloatingRootContext:function(){return st},useFloatingTree:function(){return ee},useFocus:function(){return at},useHover:function(){return ce},useId:function(){return U},useInnerOffset:function(){return Lt},useInteractions:function(){return pt},useListItem:function(){return j},useListNavigation:function(){return Et},useMergeRefs:function(){return m},useRole:function(){return Rt},useTransitionStatus:function(){return Tt},useTransitionStyles:function(){return Ot},useTypeahead:function(){return Ct}});var o=n("react"),i=n("./node_modules/@floating-ui/react/dist/floating-ui.react.utils.mjs"),l=n("./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs"),s=n("./node_modules/react/jsx-runtime.js"),u=n("./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs"),c=n("./node_modules/tabbable/dist/index.esm.js"),a=n("react-dom"),f=n("./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs"),d=n("./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs");function m(e){const t=o.useRef(void 0),n=o.useCallback((t=>{const n=e.map((e=>{if(null!=e){if("function"==typeof e){const n=e,r=n(t);return"function"==typeof r?r:()=>{n(null)}}return e.current=t,()=>{e.current=null}}}));return()=>{n.forEach((e=>null==e?void 0:e()))}}),e);return o.useMemo((()=>e.every((e=>null==e))?null:e=>{t.current&&(t.current(),t.current=void 0),null!=e&&(t.current=n(e))}),e)}const p={...r||(r=n.t(o,2))},g=p.useInsertionEffect||(e=>e());function v(e){const t=o.useRef((()=>{throw new Error("Cannot call an event handler while rendering.")}));return g((()=>{t.current=e})),o.useCallback((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])}const h="ArrowUp",y="ArrowDown",b="ArrowLeft",w="ArrowRight";function E(e,t,n){return Math.floor(e/t)!==n}function x(e,t){return t<0||t>=e.current.length}function R(e,t){return S(e,{disabledIndices:t})}function k(e,t){return S(e,{decrement:!0,startingIndex:e.current.length,disabledIndices:t})}function S(e,t){let{startingIndex:n=-1,decrement:r=!1,disabledIndices:o,amount:i=1}=void 0===t?{}:t;const l=e.current;let s=n;do{s+=r?-i:i}while(s>=0&&s<=l.length-1&&A(l,s,o));return s}function T(e,t){let{event:n,orientation:r,loop:o,rtl:s,cols:u,disabledIndices:c,minIndex:a,maxIndex:f,prevIndex:d,stopEvent:m=!1}=t,p=d;if(n.key===h){if(m&&(0,i.stopEvent)(n),-1===d)p=f;else if(p=S(e,{startingIndex:p,amount:u,decrement:!0,disabledIndices:c}),o&&(d-u<a||p<0)){const e=d%u,t=f%u,n=f-(t-e);p=t===e?f:t>e?n:n-u}x(e,p)&&(p=d)}if(n.key===y&&(m&&(0,i.stopEvent)(n),-1===d?p=a:(p=S(e,{startingIndex:d,amount:u,disabledIndices:c}),o&&d+u>f&&(p=S(e,{startingIndex:d%u-u,amount:u,disabledIndices:c}))),x(e,p)&&(p=d)),"both"===r){const t=(0,l.floor)(d/u);n.key===(s?b:w)&&(m&&(0,i.stopEvent)(n),d%u!=u-1?(p=S(e,{startingIndex:d,disabledIndices:c}),o&&E(p,u,t)&&(p=S(e,{startingIndex:d-d%u-1,disabledIndices:c}))):o&&(p=S(e,{startingIndex:d-d%u-1,disabledIndices:c})),E(p,u,t)&&(p=d)),n.key===(s?w:b)&&(m&&(0,i.stopEvent)(n),d%u!=0?(p=S(e,{startingIndex:d,decrement:!0,disabledIndices:c}),o&&E(p,u,t)&&(p=S(e,{startingIndex:d+(u-d%u),decrement:!0,disabledIndices:c}))):o&&(p=S(e,{startingIndex:d+(u-d%u),decrement:!0,disabledIndices:c})),E(p,u,t)&&(p=d));const r=(0,l.floor)(f/u)===t;x(e,p)&&(p=o&&r?n.key===(s?w:b)?f:S(e,{startingIndex:d-d%u-1,disabledIndices:c}):d)}return p}function O(e,t,n){const r=[];let o=0;return e.forEach(((e,i)=>{let{width:l,height:s}=e;if(l>t)throw new Error("[Floating UI]: Invalid grid - item width at index "+i+" is greater than grid columns");let u=!1;for(n&&(o=0);!u;){const e=[];for(let n=0;n<l;n++)for(let r=0;r<s;r++)e.push(o+n+r*t);o%t+l<=t&&e.every((e=>null==r[e]))?(e.forEach((e=>{r[e]=i})),u=!0):o++}})),[...r]}function C(e,t,n,r,o){if(-1===e)return-1;const i=n.indexOf(e),l=t[e];switch(o){case"tl":return i;case"tr":return l?i+l.width-1:i;case"bl":return l?i+(l.height-1)*r:i;case"br":return n.lastIndexOf(e)}}function P(e,t){return t.flatMap(((t,n)=>e.includes(t)?[n]:[]))}function A(e,t,n){if(n)return n.includes(t);const r=e[t];return null==r||r.hasAttribute("disabled")||"true"===r.getAttribute("aria-disabled")}var L="undefined"!=typeof document?o.useLayoutEffect:o.useEffect;function I(e,t){const n=e.compareDocumentPosition(t);return n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY?-1:n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS?1:0}const M=o.createContext({register:()=>{},unregister:()=>{},map:new Map,elementsRef:{current:[]}});function _(e){const{children:t,elementsRef:n,labelsRef:r}=e,[i,l]=o.useState((()=>new Set)),u=o.useCallback((e=>{l((t=>new Set(t).add(e)))}),[]),c=o.useCallback((e=>{l((t=>{const n=new Set(t);return n.delete(e),n}))}),[]),a=o.useMemo((()=>{const e=new Map;return Array.from(i.keys()).sort(I).forEach(((t,n)=>{e.set(t,n)})),e}),[i]);return(0,s.jsx)(M.Provider,{value:o.useMemo((()=>({register:u,unregister:c,map:a,elementsRef:n,labelsRef:r})),[u,c,a,n,r]),children:t})}function j(e){void 0===e&&(e={});const{label:t}=e,{register:n,unregister:r,map:i,elementsRef:l,labelsRef:s}=o.useContext(M),[u,c]=o.useState(null),a=o.useRef(null),f=o.useCallback((e=>{if(a.current=e,null!==u&&(l.current[u]=e,s)){var n;const r=void 0!==t;s.current[u]=r?t:null!=(n=null==e?void 0:e.textContent)?n:null}}),[u,l,s,t]);return L((()=>{const e=a.current;if(e)return n(e),()=>{r(e)}}),[n,r]),L((()=>{const e=a.current?i.get(a.current):null;null!=e&&c(e)}),[i]),o.useMemo((()=>({ref:f,index:null==u?-1:u})),[u,f])}function D(e,t){return"function"==typeof e?e(t):e?o.cloneElement(e,t):(0,s.jsx)("div",{...t})}const N=o.createContext({activeIndex:0,onNavigate:()=>{}}),F=[b,w],$=[h,y],H=[...F,...$],W=o.forwardRef((function(e,t){const{render:n,orientation:r="both",loop:i=!0,rtl:l=!1,cols:u=1,disabledIndices:c,activeIndex:a,onNavigate:f,itemSizes:d,dense:m=!1,...p}=e,[g,E]=o.useState(0),L=null!=a?a:g,I=v(null!=f?f:E),M=o.useRef([]),j=n&&"function"!=typeof n?n.props:{},W=o.useMemo((()=>({activeIndex:L,onNavigate:I})),[L,I]),V=u>1,B={...p,...j,ref:t,"aria-orientation":"both"===r?void 0:r,onKeyDown(e){null==p.onKeyDown||p.onKeyDown(e),null==j.onKeyDown||j.onKeyDown(e),function(e){if(!H.includes(e.key))return;let t=L;const n=R(M,c),o=k(M,c),s=l?b:w,a=l?w:b;if(V){const a=d||Array.from({length:M.current.length},(()=>({width:1,height:1}))),f=O(a,u,m),p=f.findIndex((e=>null!=e&&!A(M.current,e,c))),g=f.reduce(((e,t,n)=>null==t||A(M.current,t,c)?e:n),-1),v=f[T({current:f.map((e=>e?M.current[e]:null))},{event:e,orientation:r,loop:i,rtl:l,cols:u,disabledIndices:P([...c||M.current.map(((e,t)=>A(M.current,t)?t:void 0)),void 0],f),minIndex:p,maxIndex:g,prevIndex:C(L>o?n:L,a,f,u,e.key===y?"bl":e.key===s?"tr":"tl")})];null!=v&&(t=v)}const f={horizontal:[s],vertical:[y],both:[s,y]}[r],p={horizontal:[a],vertical:[h],both:[a,h]}[r],g=V?H:{horizontal:F,vertical:$,both:H}[r];var v;t===L&&[...f,...p].includes(e.key)&&(t=i&&t===o&&f.includes(e.key)?n:i&&t===n&&p.includes(e.key)?o:S(M,{startingIndex:t,decrement:p.includes(e.key),disabledIndices:c})),t===L||x(M,t)||(e.stopPropagation(),g.includes(e.key)&&e.preventDefault(),I(t),null==(v=M.current[t])||v.focus())}(e)}};return(0,s.jsx)(N.Provider,{value:W,children:(0,s.jsx)(_,{elementsRef:M,children:D(n,B)})})})),V=o.forwardRef((function(e,t){const{render:n,...r}=e,i=n&&"function"!=typeof n?n.props:{},{activeIndex:l,onNavigate:s}=o.useContext(N),{ref:u,index:c}=j(),a=m([u,t,i.ref]),f=l===c;return D(n,{...r,...i,ref:a,tabIndex:f?0:-1,"data-active":f?"":void 0,onFocus(e){null==r.onFocus||r.onFocus(e),null==i.onFocus||i.onFocus(e),s(c)}})}));let B=!1,z=0;const K=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+z++,U=p.useId||function(){const[e,t]=o.useState((()=>B?K():void 0));return L((()=>{null==e&&t(K())}),[]),o.useEffect((()=>{B=!0}),[]),e};let q;function Y(){for(var e,t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];const o="Floating UI: "+n.join(" ");var i;null!=(e=q)&&e.has(o)||(null==(i=q)||i.add(o),console.warn(o))}q=new Set;const X=o.forwardRef((function(e,t){const{context:{placement:n,elements:{floating:r},middlewareData:{arrow:i,shift:l}},width:c=14,height:a=7,tipRadius:f=0,strokeWidth:d=0,staticOffset:m,stroke:p,d:g,style:{transform:v,...h}={},...y}=e;t||Y("The `ref` prop is required for `FloatingArrow`.");const b=U(),[w,E]=o.useState(!1);if(L((()=>{r&&"rtl"===(0,u.getComputedStyle)(r).direction&&E(!0)}),[r]),!r)return null;const[x,R]=n.split("-"),k="top"===x||"bottom"===x;let S=m;(k&&null!=l&&l.x||!k&&null!=l&&l.y)&&(S=null);const T=2*d,O=T/2,C=c/2*(f/-8+1),P=a/2*f/4,A=!!g,I=S&&"end"===R?"bottom":"top";let M=S&&"end"===R?"right":"left";S&&w&&(M="end"===R?"left":"right");const _=null!=(null==i?void 0:i.x)?S||i.x:"",j=null!=(null==i?void 0:i.y)?S||i.y:"",D=g||"M0,0 H"+c+" L"+(c-C)+","+(a-P)+" Q"+c/2+","+a+" "+C+","+(a-P)+" Z",N={top:A?"rotate(180deg)":"",left:A?"rotate(90deg)":"rotate(-90deg)",bottom:A?"":"rotate(180deg)",right:A?"rotate(-90deg)":"rotate(90deg)"}[x];return(0,s.jsxs)("svg",{...y,"aria-hidden":!0,ref:t,width:A?c:c+T,height:c,viewBox:"0 0 "+c+" "+(a>c?a:c),style:{position:"absolute",pointerEvents:"none",[M]:_,[I]:j,[x]:k||A?"100%":"calc(100% - "+T/2+"px)",transform:[N,v].filter((e=>!!e)).join(" "),...h},children:[T>0&&(0,s.jsx)("path",{clipPath:"url(#"+b+")",fill:"none",stroke:p,strokeWidth:T+(g?0:1),d:D}),(0,s.jsx)("path",{stroke:T&&!g?y.fill:"none",d:D}),(0,s.jsx)("clipPath",{id:b,children:(0,s.jsx)("rect",{x:-O,y:O*(A?-1:1),width:c+T,height:c})})]})}));function G(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach((e=>e(n)))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter((e=>e!==n)))||[])}}}const J=o.createContext(null),Z=o.createContext(null),Q=()=>{var e;return(null==(e=o.useContext(J))?void 0:e.id)||null},ee=()=>o.useContext(Z);function te(e){const t=U(),n=ee(),r=Q(),o=e||r;return L((()=>{if(!t)return;const e={id:t,parentId:o};return null==n||n.addNode(e),()=>{null==n||n.removeNode(e)}}),[n,t,o]),t}function ne(e){const{children:t,id:n}=e,r=Q();return(0,s.jsx)(J.Provider,{value:o.useMemo((()=>({id:n,parentId:r})),[n,r]),children:t})}function re(e){const{children:t}=e,n=o.useRef([]),r=o.useCallback((e=>{n.current=[...n.current,e]}),[]),i=o.useCallback((e=>{n.current=n.current.filter((t=>t!==e))}),[]),l=o.useState((()=>G()))[0];return(0,s.jsx)(Z.Provider,{value:o.useMemo((()=>({nodesRef:n,addNode:r,removeNode:i,events:l})),[r,i,l]),children:t})}function oe(e){return"data-floating-ui-"+e}function ie(e){-1!==e.current&&(clearTimeout(e.current),e.current=-1)}function le(e){const t=(0,o.useRef)(e);return L((()=>{t.current=e})),t}const se=oe("safe-polygon");function ue(e,t,n){return n&&!(0,i.isMouseLikePointerType)(n)?0:"number"==typeof e?e:null==e?void 0:e[t]}function ce(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:l,events:s,elements:c}=e,{enabled:a=!0,delay:f=0,handleClose:d=null,mouseOnly:m=!1,restMs:p=0,move:g=!0}=t,h=ee(),y=Q(),b=le(d),w=le(f),E=le(n),x=o.useRef(),R=o.useRef(-1),k=o.useRef(),S=o.useRef(-1),T=o.useRef(!0),O=o.useRef(!1),C=o.useRef((()=>{})),P=o.useRef(!1),A=o.useCallback((()=>{var e;const t=null==(e=l.current.openEvent)?void 0:e.type;return(null==t?void 0:t.includes("mouse"))&&"mousedown"!==t}),[l]);o.useEffect((()=>{if(a)return s.on("openchange",e),()=>{s.off("openchange",e)};function e(e){let{open:t}=e;t||(ie(R),ie(S),T.current=!0,P.current=!1)}}),[a,s]),o.useEffect((()=>{if(!a)return;if(!b.current)return;if(!n)return;function e(e){A()&&r(!1,e,"hover")}const t=(0,i.getDocument)(c.floating).documentElement;return t.addEventListener("mouseleave",e),()=>{t.removeEventListener("mouseleave",e)}}),[c.floating,n,r,a,b,A]);const I=o.useCallback((function(e,t,n){void 0===t&&(t=!0),void 0===n&&(n="hover");const o=ue(w.current,"close",x.current);o&&!k.current?(ie(R),R.current=window.setTimeout((()=>r(!1,e,n)),o)):t&&(ie(R),r(!1,e,n))}),[w,r]),M=v((()=>{C.current(),k.current=void 0})),_=v((()=>{if(O.current){const e=(0,i.getDocument)(c.floating).body;e.style.pointerEvents="",e.removeAttribute(se),O.current=!1}})),j=v((()=>!!l.current.openEvent&&["click","mousedown"].includes(l.current.openEvent.type)));o.useEffect((()=>{if(a&&(0,u.isElement)(c.domReference)){var e;const r=c.domReference;return n&&r.addEventListener("mouseleave",s),null==(e=c.floating)||e.addEventListener("mouseleave",s),g&&r.addEventListener("mousemove",t,{once:!0}),r.addEventListener("mouseenter",t),r.addEventListener("mouseleave",o),()=>{var e;n&&r.removeEventListener("mouseleave",s),null==(e=c.floating)||e.removeEventListener("mouseleave",s),g&&r.removeEventListener("mousemove",t),r.removeEventListener("mouseenter",t),r.removeEventListener("mouseleave",o)}}function t(e){if(ie(R),T.current=!1,m&&!(0,i.isMouseLikePointerType)(x.current)||p>0&&!ue(w.current,"open"))return;const t=ue(w.current,"open",x.current);t?R.current=window.setTimeout((()=>{E.current||r(!0,e,"hover")}),t):n||r(!0,e,"hover")}function o(e){if(j())return;C.current();const t=(0,i.getDocument)(c.floating);if(ie(S),P.current=!1,b.current&&l.current.floatingContext){n||ie(R),k.current=b.current({...l.current.floatingContext,tree:h,x:e.clientX,y:e.clientY,onClose(){_(),M(),j()||I(e,!0,"safe-polygon")}});const r=k.current;return t.addEventListener("mousemove",r),void(C.current=()=>{t.removeEventListener("mousemove",r)})}("touch"!==x.current||!(0,i.contains)(c.floating,e.relatedTarget))&&I(e)}function s(e){j()||l.current.floatingContext&&(null==b.current||b.current({...l.current.floatingContext,tree:h,x:e.clientX,y:e.clientY,onClose(){_(),M(),j()||I(e)}})(e))}}),[c,a,e,m,p,g,I,M,_,r,n,E,h,w,b,l,j]),L((()=>{var e;if(a&&n&&null!=(e=b.current)&&e.__options.blockPointerEvents&&A()){O.current=!0;const e=c.floating;if((0,u.isElement)(c.domReference)&&e){var t;const n=(0,i.getDocument)(c.floating).body;n.setAttribute(se,"");const r=c.domReference,o=null==h||null==(t=h.nodesRef.current.find((e=>e.id===y)))||null==(t=t.context)?void 0:t.elements.floating;return o&&(o.style.pointerEvents=""),n.style.pointerEvents="none",r.style.pointerEvents="auto",e.style.pointerEvents="auto",()=>{n.style.pointerEvents="",r.style.pointerEvents="",e.style.pointerEvents=""}}}}),[a,n,y,c,h,b,A]),L((()=>{n||(x.current=void 0,P.current=!1,M(),_())}),[n,M,_]),o.useEffect((()=>()=>{M(),ie(R),ie(S),_()}),[a,c.domReference,M,_]);const D=o.useMemo((()=>{function e(e){x.current=e.pointerType}return{onPointerDown:e,onPointerEnter:e,onMouseMove(e){const{nativeEvent:t}=e;function o(){T.current||E.current||r(!0,t,"hover")}m&&!(0,i.isMouseLikePointerType)(x.current)||n||0===p||P.current&&e.movementX**2+e.movementY**2<2||(ie(S),"touch"===x.current?o():(P.current=!0,S.current=window.setTimeout(o,p)))}}}),[m,r,n,E,p]),N=o.useMemo((()=>({onMouseEnter(){ie(R)},onMouseLeave(e){j()||I(e.nativeEvent,!1)}})),[I,j]);return o.useMemo((()=>a?{reference:D,floating:N}:{}),[a,D,N])}const ae=()=>{},fe=o.createContext({delay:0,initialDelay:0,timeoutMs:0,currentId:null,setCurrentId:ae,setState:ae,isInstantPhase:!1}),de=()=>o.useContext(fe);function me(e){const{children:t,delay:n,timeoutMs:r=0}=e,[i,l]=o.useReducer(((e,t)=>({...e,...t})),{delay:n,timeoutMs:r,initialDelay:n,currentId:null,isInstantPhase:!1}),u=o.useRef(null),c=o.useCallback((e=>{l({currentId:e})}),[]);return L((()=>{i.currentId?null===u.current?u.current=i.currentId:i.isInstantPhase||l({isInstantPhase:!0}):(i.isInstantPhase&&l({isInstantPhase:!1}),u.current=null)}),[i.currentId,i.isInstantPhase]),(0,s.jsx)(fe.Provider,{value:o.useMemo((()=>({...i,setState:l,setCurrentId:c})),[i,c]),children:t})}function pe(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,floatingId:o}=e,{id:i,enabled:l=!0}=t,s=null!=i?i:o,u=de(),{currentId:c,setCurrentId:a,initialDelay:f,setState:d,timeoutMs:m}=u;return L((()=>{l&&c&&(d({delay:{open:1,close:ue(f,"close")}}),c!==s&&r(!1))}),[l,s,r,d,c,f]),L((()=>{function e(){r(!1),d({delay:f,currentId:null})}if(l&&c&&!n&&c===s){if(m){const t=window.setTimeout(e,m);return()=>{clearTimeout(t)}}e()}}),[l,n,d,c,s,r,f,m]),L((()=>{l&&a!==ae&&n&&a(s)}),[l,n,a,s]),u}let ge=0;function ve(e,t){void 0===t&&(t={});const{preventScroll:n=!1,cancelPrevious:r=!0,sync:o=!1}=t;r&&cancelAnimationFrame(ge);const i=()=>null==e?void 0:e.focus({preventScroll:n});o?i():ge=requestAnimationFrame(i)}function he(e,t){var n;let r=[],o=null==(n=e.find((e=>e.id===t)))?void 0:n.parentId;for(;o;){const t=e.find((e=>e.id===o));o=null==t?void 0:t.parentId,t&&(r=r.concat(t))}return r}function ye(e,t){let n=e.filter((e=>{var n;return e.parentId===t&&(null==(n=e.context)?void 0:n.open)})),r=n;for(;r.length;)r=e.filter((e=>{var t;return null==(t=r)?void 0:t.some((t=>{var n;return e.parentId===t.id&&(null==(n=e.context)?void 0:n.open)}))})),n=n.concat(r);return n}let be=new WeakMap,we=new WeakSet,Ee={},xe=0;const Re=()=>"undefined"!=typeof HTMLElement&&"inert"in HTMLElement.prototype,ke=e=>e&&(e.host||ke(e.parentNode)),Se=(e,t)=>t.map((t=>{if(e.contains(t))return t;const n=ke(t);return e.contains(n)?n:null})).filter((e=>null!=e));function Te(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);const r=(0,i.getDocument)(e[0]).body;return function(e,t,n,r){const o="data-floating-ui-inert",i=r?"inert":n?"aria-hidden":null,l=Se(t,e),s=new Set,c=new Set(l),a=[];Ee[o]||(Ee[o]=new WeakMap);const f=Ee[o];return l.forEach((function e(t){t&&!s.has(t)&&(s.add(t),t.parentNode&&e(t.parentNode))})),function e(t){t&&!c.has(t)&&[].forEach.call(t.children,(t=>{if("script"!==(0,u.getNodeName)(t))if(s.has(t))e(t);else{const e=i?t.getAttribute(i):null,n=null!==e&&"false"!==e,r=be.get(t)||0,l=i?r+1:r,s=(f.get(t)||0)+1;be.set(t,l),f.set(t,s),a.push(t),1===l&&n&&we.add(t),1===s&&t.setAttribute(o,""),!n&&i&&t.setAttribute(i,"inert"===i?"":"true")}}))}(t),s.clear(),xe++,()=>{a.forEach((e=>{const t=be.get(e)||0,n=i?t-1:t,r=(f.get(e)||0)-1;be.set(e,n),f.set(e,r),n||(!we.has(e)&&i&&e.removeAttribute(i),we.delete(e)),r||e.removeAttribute(o)})),xe--,xe||(be=new WeakMap,be=new WeakMap,we=new WeakSet,Ee={})}}(e.concat(Array.from(r.querySelectorAll("[aria-live]"))),r,t,n)}const Oe=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function Ce(e,t){const n=(0,c.tabbable)(e,Oe());"prev"===t&&n.reverse();const r=n.indexOf((0,i.activeElement)((0,i.getDocument)(e)));return n.slice(r+1)[0]}function Pe(e){return Ce((0,i.getDocument)(e).body,"next")||e}function Ae(e){return Ce((0,i.getDocument)(e).body,"prev")||e}function Le(e,t){const n=t||e.currentTarget,r=e.relatedTarget;return!r||!(0,i.contains)(n,r)}function Ie(e){(0,c.tabbable)(e,Oe()).forEach((e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}))}function Me(e){e.querySelectorAll("[data-tabindex]").forEach((e=>{const t=e.dataset.tabindex;delete e.dataset.tabindex,t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex")}))}const _e={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0},je=o.forwardRef((function(e,t){const[n,r]=o.useState();L((()=>{(0,i.isSafari)()&&r("button")}),[]);const l={ref:t,tabIndex:0,role:n,"aria-hidden":!n||void 0,[oe("focus-guard")]:"",style:_e};return(0,s.jsx)("span",{...e,...l})})),De=o.createContext(null),Ne=oe("portal");function Fe(e){void 0===e&&(e={});const{id:t,root:n}=e,r=U(),i=He(),[l,s]=o.useState(null),c=o.useRef(null);return L((()=>()=>{null==l||l.remove(),queueMicrotask((()=>{c.current=null}))}),[l]),L((()=>{if(!r)return;if(c.current)return;const e=t?document.getElementById(t):null;if(!e)return;const n=document.createElement("div");n.id=r,n.setAttribute(Ne,""),e.appendChild(n),c.current=n,s(n)}),[t,r]),L((()=>{if(null===n)return;if(!r)return;if(c.current)return;let e=n||(null==i?void 0:i.portalNode);e&&!(0,u.isElement)(e)&&(e=e.current),e=e||document.body;let o=null;t&&(o=document.createElement("div"),o.id=t,e.appendChild(o));const l=document.createElement("div");l.id=r,l.setAttribute(Ne,""),e=o||e,e.appendChild(l),c.current=l,s(l)}),[t,n,r,i]),l}function $e(e){const{children:t,id:n,root:r,preserveTabOrder:i=!0}=e,l=Fe({id:n,root:r}),[u,c]=o.useState(null),f=o.useRef(null),d=o.useRef(null),m=o.useRef(null),p=o.useRef(null),g=null==u?void 0:u.modal,v=null==u?void 0:u.open,h=!!u&&!u.modal&&u.open&&i&&!(!r&&!l);return o.useEffect((()=>{if(l&&i&&!g)return l.addEventListener("focusin",e,!0),l.addEventListener("focusout",e,!0),()=>{l.removeEventListener("focusin",e,!0),l.removeEventListener("focusout",e,!0)};function e(e){l&&Le(e)&&("focusin"===e.type?Me:Ie)(l)}}),[l,i,g]),o.useEffect((()=>{l&&(v||Me(l))}),[v,l]),(0,s.jsxs)(De.Provider,{value:o.useMemo((()=>({preserveTabOrder:i,beforeOutsideRef:f,afterOutsideRef:d,beforeInsideRef:m,afterInsideRef:p,portalNode:l,setFocusManagerState:c})),[i,l]),children:[h&&l&&(0,s.jsx)(je,{"data-type":"outside",ref:f,onFocus:e=>{if(Le(e,l)){var t;null==(t=m.current)||t.focus()}else{const e=Ae(u?u.domReference:null);null==e||e.focus()}}}),h&&l&&(0,s.jsx)("span",{"aria-owns":l.id,style:_e}),l&&a.createPortal(t,l),h&&l&&(0,s.jsx)(je,{"data-type":"outside",ref:d,onFocus:e=>{if(Le(e,l)){var t;null==(t=p.current)||t.focus()}else{const t=Pe(u?u.domReference:null);null==t||t.focus(),(null==u?void 0:u.closeOnFocusOut)&&(null==u||u.onOpenChange(!1,e.nativeEvent,"focus-out"))}}})]})}const He=()=>o.useContext(De),We="data-floating-ui-focusable";function Ve(e){return e?e.hasAttribute(We)?e:e.querySelector("["+We+"]")||e:null}function Be(e){return o.useMemo((()=>t=>{e.forEach((e=>{e&&(e.current=t)}))}),e)}const ze=20;let Ke=[];function Ue(){return Ke.slice().reverse().find((e=>e.isConnected))}const qe=o.forwardRef((function(e,t){return(0,s.jsx)("button",{...e,type:"button",ref:t,tabIndex:-1,style:_e})}));function Ye(e){const{context:t,children:n,disabled:r=!1,order:l=["content"],guards:a=!0,initialFocus:f=0,returnFocus:d=!0,restoreFocus:m=!1,modal:p=!0,visuallyHiddenDismiss:g=!1,closeOnFocusOut:h=!0,outsideElementsInert:y=!1,getInsideElements:b=()=>[]}=e,{open:w,onOpenChange:E,events:x,dataRef:R,elements:{domReference:k,floating:S}}=t,T=v((()=>{var e;return null==(e=R.current.floatingContext)?void 0:e.nodeId})),O=v(b),C="number"==typeof f&&f<0,P=(0,i.isTypeableCombobox)(k)&&C,A=Re(),I=!A||a,M=!I||A&&y,_=le(l),j=le(f),D=le(d),N=ee(),F=He(),$=o.useRef(null),H=o.useRef(null),W=o.useRef(!1),V=o.useRef(!1),B=o.useRef(-1),z=null!=F,K=Ve(S),U=v((function(e){return void 0===e&&(e=K),e?(0,c.tabbable)(e,Oe()):[]})),q=v((e=>{const t=U(e);return _.current.map((e=>k&&"reference"===e?k:K&&"floating"===e?K:t)).filter(Boolean).flat()}));o.useEffect((()=>{if(r)return;if(!p)return;function e(e){if("Tab"===e.key){(0,i.contains)(K,(0,i.activeElement)((0,i.getDocument)(K)))&&0===U().length&&!P&&(0,i.stopEvent)(e);const t=q(),n=(0,i.getTarget)(e);"reference"===_.current[0]&&n===k&&((0,i.stopEvent)(e),e.shiftKey?ve(t[t.length-1]):ve(t[1])),"floating"===_.current[1]&&n===K&&e.shiftKey&&((0,i.stopEvent)(e),ve(t[0]))}}const t=(0,i.getDocument)(K);return t.addEventListener("keydown",e),()=>{t.removeEventListener("keydown",e)}}),[r,k,K,p,_,P,U,q]),o.useEffect((()=>{if(!r&&S)return S.addEventListener("focusin",e),()=>{S.removeEventListener("focusin",e)};function e(e){const t=(0,i.getTarget)(e),n=U().indexOf(t);-1!==n&&(B.current=n)}}),[r,S,U]),o.useEffect((()=>{if(!r&&h)return S&&(0,u.isHTMLElement)(k)?(k.addEventListener("focusout",t),k.addEventListener("pointerdown",e),S.addEventListener("focusout",t),()=>{k.removeEventListener("focusout",t),k.removeEventListener("pointerdown",e),S.removeEventListener("focusout",t)}):void 0;function e(){V.current=!0,setTimeout((()=>{V.current=!1}))}function t(e){const t=e.relatedTarget;queueMicrotask((()=>{const n=T(),r=!((0,i.contains)(k,t)||(0,i.contains)(S,t)||(0,i.contains)(t,S)||(0,i.contains)(null==F?void 0:F.portalNode,t)||null!=t&&t.hasAttribute(oe("focus-guard"))||N&&(ye(N.nodesRef.current,n).find((e=>{var n,r;return(0,i.contains)(null==(n=e.context)?void 0:n.elements.floating,t)||(0,i.contains)(null==(r=e.context)?void 0:r.elements.domReference,t)}))||he(N.nodesRef.current,n).find((e=>{var n,r,o;return[null==(n=e.context)?void 0:n.elements.floating,Ve(null==(r=e.context)?void 0:r.elements.floating)].includes(t)||(null==(o=e.context)?void 0:o.elements.domReference)===t}))));if(m&&r&&(0,i.activeElement)((0,i.getDocument)(K))===(0,i.getDocument)(K).body){(0,u.isHTMLElement)(K)&&K.focus();const e=B.current,t=U(),n=t[e]||t[t.length-1]||K;(0,u.isHTMLElement)(n)&&n.focus()}!P&&p||!t||!r||V.current||t===Ue()||(W.current=!0,E(!1,e,"focus-out"))}))}}),[r,k,S,K,p,N,F,E,h,m,U,P,T]);const Y=o.useRef(null),X=o.useRef(null),G=Be([Y,null==F?void 0:F.beforeInsideRef]),J=Be([X,null==F?void 0:F.afterInsideRef]);function Z(e){return!r&&g&&p?(0,s.jsx)(qe,{ref:"start"===e?$:H,onClick:e=>E(!1,e.nativeEvent),children:"string"==typeof g?g:"Dismiss"}):null}o.useEffect((()=>{var e,t;if(r)return;if(!S)return;const n=Array.from((null==F||null==(e=F.portalNode)?void 0:e.querySelectorAll("["+oe("portal")+"]"))||[]),o=N?he(N.nodesRef.current,T()):[],l=N&&!p?o.map((e=>{var t;return null==(t=e.context)?void 0:t.elements.floating})):[],s=null==(t=o.find((e=>{var t;return(0,i.isTypeableCombobox)((null==(t=e.context)?void 0:t.elements.domReference)||null)})))||null==(t=t.context)?void 0:t.elements.domReference,u=[S,s,...n,...l,...O(),$.current,H.current,Y.current,X.current,null==F?void 0:F.beforeOutsideRef.current,null==F?void 0:F.afterOutsideRef.current,_.current.includes("reference")||P?k:null].filter((e=>null!=e)),c=p||P?Te(u,!M,M):Te(u);return()=>{c()}}),[r,k,S,p,_,F,P,I,M,N,T,O]),L((()=>{if(r||!(0,u.isHTMLElement)(K))return;const e=(0,i.getDocument)(K),t=(0,i.activeElement)(e);queueMicrotask((()=>{const e=q(K),n=j.current,r=("number"==typeof n?e[n]:n.current)||K,o=(0,i.contains)(K,t);C||o||!w||ve(r,{preventScroll:r===K})}))}),[r,w,K,C,q,j]),L((()=>{if(r||!K)return;let e=!1,t=!1;const n=(0,i.getDocument)(K),o=(0,i.activeElement)(n);let l=R.current.openEvent;var s;function a(n){let{open:r,reason:o,event:s,nested:u}=n;if(r&&(l=s),"escape-key"===o&&(t=!0),["hover","safe-polygon"].includes(o)&&"mouseleave"===s.type&&(W.current=!0),"outside-press"===o)if(u)W.current=!1,e=!0;else if((0,i.isVirtualClick)(s)||(0,i.isVirtualPointerEvent)(s))W.current=!1;else{let t=!1;document.createElement("div").focus({get preventScroll(){return t=!0,!1}}),t?(W.current=!1,e=!0):W.current=!0}}s=o,Ke=Ke.filter((e=>e.isConnected)),s&&"body"!==(0,u.getNodeName)(s)&&(Ke.push(s),Ke.length>ze&&(Ke=Ke.slice(-20))),x.on("openchange",a);const f=n.createElement("span");return f.setAttribute("tabindex","-1"),f.setAttribute("aria-hidden","true"),Object.assign(f.style,_e),z&&k&&k.insertAdjacentElement("afterend",f),()=>{x.off("openchange",a);const r=(0,i.activeElement)(n),o=(0,i.contains)(S,r)||N&&ye(N.nodesRef.current,T()).some((e=>{var t;return(0,i.contains)(null==(t=e.context)?void 0:t.elements.floating,r)}));(o||l&&["click","mousedown"].includes(l.type))&&(t=!0);const s="boolean"==typeof D.current?t&&k?k:Ue()||f:D.current.current||f;queueMicrotask((()=>{const t=function(e){const t=Oe();return(0,c.isTabbable)(e,t)?e:(0,c.tabbable)(e,t)[0]||e}(s);D.current&&!W.current&&(0,u.isHTMLElement)(t)&&(t===r||r===n.body||o)&&t.focus({preventScroll:e}),f.remove()}))}}),[r,S,K,D,R,x,N,z,k,T]),o.useEffect((()=>{queueMicrotask((()=>{W.current=!1}))}),[r]),L((()=>{if(!r&&F)return F.setFocusManagerState({modal:p,closeOnFocusOut:h,open:w,onOpenChange:E,domReference:k}),()=>{F.setFocusManagerState(null)}}),[r,F,p,w,E,h,k]),L((()=>{if(r)return;if(!K)return;if("function"!=typeof MutationObserver)return;if(C)return;const e=()=>{const e=K.getAttribute("tabindex"),t=U(),n=(0,i.activeElement)((0,i.getDocument)(S)),r=t.indexOf(n);-1!==r&&(B.current=r),_.current.includes("floating")||n!==k&&0===t.length?"0"!==e&&K.setAttribute("tabindex","0"):"-1"!==e&&K.setAttribute("tabindex","-1")};e();const t=new MutationObserver(e);return t.observe(K,{childList:!0,subtree:!0,attributes:!0}),()=>{t.disconnect()}}),[r,S,K,k,_,U,C]);const Q=!r&&I&&(!p||!P)&&(z||p);return(0,s.jsxs)(s.Fragment,{children:[Q&&(0,s.jsx)(je,{"data-type":"inside",ref:G,onFocus:e=>{if(p){const e=q();ve("reference"===l[0]?e[0]:e[e.length-1])}else if(null!=F&&F.preserveTabOrder&&F.portalNode)if(W.current=!1,Le(e,F.portalNode)){const e=Pe(k);null==e||e.focus()}else{var t;null==(t=F.beforeOutsideRef.current)||t.focus()}}}),!P&&Z("start"),n,Z("end"),Q&&(0,s.jsx)(je,{"data-type":"inside",ref:J,onFocus:e=>{if(p)ve(q()[0]);else if(null!=F&&F.preserveTabOrder&&F.portalNode)if(h&&(W.current=!0),Le(e,F.portalNode)){const e=Ae(k);null==e||e.focus()}else{var t;null==(t=F.afterOutsideRef.current)||t.focus()}}})]})}let Xe=0,Ge=()=>{};const Je=o.forwardRef((function(e,t){const{lockScroll:n=!1,...r}=e;return L((()=>{if(n)return Xe++,1===Xe&&(Ge=function(){const e=/iP(hone|ad|od)|iOS/.test((0,i.getPlatform)()),t=document.body.style,n=Math.round(document.documentElement.getBoundingClientRect().left)+document.documentElement.scrollLeft?"paddingLeft":"paddingRight",r=window.innerWidth-document.documentElement.clientWidth,o=t.left?parseFloat(t.left):window.scrollX,l=t.top?parseFloat(t.top):window.scrollY;if(t.overflow="hidden",r&&(t[n]=r+"px"),e){var s,u;const e=(null==(s=window.visualViewport)?void 0:s.offsetLeft)||0,n=(null==(u=window.visualViewport)?void 0:u.offsetTop)||0;Object.assign(t,{position:"fixed",top:-(l-Math.floor(n))+"px",left:-(o-Math.floor(e))+"px",right:"0"})}return()=>{Object.assign(t,{overflow:"",[n]:""}),e&&(Object.assign(t,{position:"",top:"",left:"",right:""}),window.scrollTo(o,l))}}()),()=>{Xe--,0===Xe&&Ge()}}),[n]),(0,s.jsx)("div",{ref:t,...r,style:{position:"fixed",overflow:"auto",top:0,right:0,bottom:0,left:0,...r.style}})}));function Ze(e){return(0,u.isHTMLElement)(e.target)&&"BUTTON"===e.target.tagName}function Qe(e){return(0,i.isTypeableElement)(e)}function et(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:l,elements:{domReference:s}}=e,{enabled:c=!0,event:a="click",toggle:f=!0,ignoreMouse:d=!1,keyboardHandlers:m=!0,stickIfOpen:p=!0}=t,g=o.useRef(),v=o.useRef(!1),h=o.useMemo((()=>({onPointerDown(e){g.current=e.pointerType},onMouseDown(e){const t=g.current;0===e.button&&"click"!==a&&((0,i.isMouseLikePointerType)(t,!0)&&d||(!n||!f||l.current.openEvent&&p&&"mousedown"!==l.current.openEvent.type?(e.preventDefault(),r(!0,e.nativeEvent,"click")):r(!1,e.nativeEvent,"click")))},onClick(e){const t=g.current;"mousedown"===a&&g.current?g.current=void 0:(0,i.isMouseLikePointerType)(t,!0)&&d||(!n||!f||l.current.openEvent&&p&&"click"!==l.current.openEvent.type?r(!0,e.nativeEvent,"click"):r(!1,e.nativeEvent,"click"))},onKeyDown(e){g.current=void 0,e.defaultPrevented||!m||Ze(e)||(" "!==e.key||Qe(s)||(e.preventDefault(),v.current=!0),function(e){return(0,u.isHTMLElement)(e.target)&&"A"===e.target.tagName}(e)||"Enter"===e.key&&r(!n||!f,e.nativeEvent,"click"))},onKeyUp(e){e.defaultPrevented||!m||Ze(e)||Qe(s)||" "===e.key&&v.current&&(v.current=!1,r(!n||!f,e.nativeEvent,"click"))}})),[l,s,a,d,m,r,n,p,f]);return o.useMemo((()=>c?{reference:h}:{}),[c,h])}function tt(e){return null!=e&&null!=e.clientX}function nt(e,t){void 0===t&&(t={});const{open:n,dataRef:r,elements:{floating:l,domReference:s},refs:c}=e,{enabled:a=!0,axis:f="both",x:d=null,y:m=null}=t,p=o.useRef(!1),g=o.useRef(null),[h,y]=o.useState(),[b,w]=o.useState([]),E=v(((e,t)=>{p.current||r.current.openEvent&&!tt(r.current.openEvent)||c.setPositionReference(function(e,t){let n=null,r=null,o=!1;return{contextElement:e||void 0,getBoundingClientRect(){var i;const l=(null==e?void 0:e.getBoundingClientRect())||{width:0,height:0,x:0,y:0},s="x"===t.axis||"both"===t.axis,u="y"===t.axis||"both"===t.axis,c=["mouseenter","mousemove"].includes((null==(i=t.dataRef.current.openEvent)?void 0:i.type)||"")&&"touch"!==t.pointerType;let a=l.width,f=l.height,d=l.x,m=l.y;return null==n&&t.x&&s&&(n=l.x-t.x),null==r&&t.y&&u&&(r=l.y-t.y),d-=n||0,m-=r||0,a=0,f=0,!o||c?(a="y"===t.axis?l.width:0,f="x"===t.axis?l.height:0,d=s&&null!=t.x?t.x:d,m=u&&null!=t.y?t.y:m):o&&!c&&(f="x"===t.axis?l.height:f,a="y"===t.axis?l.width:a),o=!0,{width:a,height:f,x:d,y:m,top:m,right:d+a,bottom:m+f,left:d}}}}(s,{x:e,y:t,axis:f,dataRef:r,pointerType:h}))})),x=v((e=>{null==d&&null==m&&(n?g.current||w([]):E(e.clientX,e.clientY))})),R=(0,i.isMouseLikePointerType)(h)?l:n,k=o.useCallback((()=>{if(!R||!a||null!=d||null!=m)return;const e=(0,u.getWindow)(l);function t(n){const r=(0,i.getTarget)(n);(0,i.contains)(l,r)?(e.removeEventListener("mousemove",t),g.current=null):E(n.clientX,n.clientY)}if(!r.current.openEvent||tt(r.current.openEvent)){e.addEventListener("mousemove",t);const n=()=>{e.removeEventListener("mousemove",t),g.current=null};return g.current=n,n}c.setPositionReference(s)}),[R,a,d,m,l,r,c,s,E]);o.useEffect((()=>k()),[k,b]),o.useEffect((()=>{a&&!l&&(p.current=!1)}),[a,l]),o.useEffect((()=>{!a&&n&&(p.current=!0)}),[a,n]),L((()=>{!a||null==d&&null==m||(p.current=!1,E(d,m))}),[a,d,m,E]);const S=o.useMemo((()=>{function e(e){let{pointerType:t}=e;y(t)}return{onPointerDown:e,onPointerEnter:e,onMouseMove:x,onMouseEnter:x}}),[x]);return o.useMemo((()=>a?{reference:S}:{}),[a,S])}const rt={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},ot={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},it=e=>{var t,n;return{escapeKey:"boolean"==typeof e?e:null!=(t=null==e?void 0:e.escapeKey)&&t,outsidePress:"boolean"==typeof e?e:null==(n=null==e?void 0:e.outsidePress)||n}};function lt(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,elements:l,dataRef:s}=e,{enabled:c=!0,escapeKey:a=!0,outsidePress:f=!0,outsidePressEvent:d="pointerdown",referencePress:m=!1,referencePressEvent:p="pointerdown",ancestorScroll:g=!1,bubbles:h,capture:y}=t,b=ee(),w=v("function"==typeof f?f:()=>!1),E="function"==typeof f?w:f,x=o.useRef(!1),R=o.useRef(!1),{escapeKey:k,outsidePress:S}=it(h),{escapeKey:T,outsidePress:O}=it(y),C=o.useRef(!1),P=v((e=>{var t;if(!n||!c||!a||"Escape"!==e.key)return;if(C.current)return;const o=null==(t=s.current.floatingContext)?void 0:t.nodeId,l=b?ye(b.nodesRef.current,o):[];if(!k&&(e.stopPropagation(),l.length>0)){let e=!0;if(l.forEach((t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__escapeKeyBubbles||(e=!1)})),!e)return}r(!1,(0,i.isReactEvent)(e)?e.nativeEvent:e,"escape-key")})),A=v((e=>{var t;const n=()=>{var t;P(e),null==(t=(0,i.getTarget)(e))||t.removeEventListener("keydown",n)};null==(t=(0,i.getTarget)(e))||t.addEventListener("keydown",n)})),L=v((e=>{var t;const n=x.current;x.current=!1;const o=R.current;if(R.current=!1,"click"===d&&o)return;if(n)return;if("function"==typeof E&&!E(e))return;const c=(0,i.getTarget)(e),a="["+oe("inert")+"]",f=(0,i.getDocument)(l.floating).querySelectorAll(a);let m=(0,u.isElement)(c)?c:null;for(;m&&!(0,u.isLastTraversableNode)(m);){const e=(0,u.getParentNode)(m);if((0,u.isLastTraversableNode)(e)||!(0,u.isElement)(e))break;m=e}if(f.length&&(0,u.isElement)(c)&&!(0,i.isRootElement)(c)&&!(0,i.contains)(c,l.floating)&&Array.from(f).every((e=>!(0,i.contains)(m,e))))return;if((0,u.isHTMLElement)(c)&&_){const t=(0,u.isLastTraversableNode)(c),n=(0,u.getComputedStyle)(c),r=/auto|scroll/,o=t||r.test(n.overflowX),i=t||r.test(n.overflowY),l=o&&c.clientWidth>0&&c.scrollWidth>c.clientWidth,s=i&&c.clientHeight>0&&c.scrollHeight>c.clientHeight,a="rtl"===n.direction,f=s&&(a?e.offsetX<=c.offsetWidth-c.clientWidth:e.offsetX>c.clientWidth),d=l&&e.offsetY>c.clientHeight;if(f||d)return}const p=null==(t=s.current.floatingContext)?void 0:t.nodeId,g=b&&ye(b.nodesRef.current,p).some((t=>{var n;return(0,i.isEventTargetWithin)(e,null==(n=t.context)?void 0:n.elements.floating)}));if((0,i.isEventTargetWithin)(e,l.floating)||(0,i.isEventTargetWithin)(e,l.domReference)||g)return;const v=b?ye(b.nodesRef.current,p):[];if(v.length>0){let e=!0;if(v.forEach((t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__outsidePressBubbles||(e=!1)})),!e)return}r(!1,e,"outside-press")})),I=v((e=>{var t;const n=()=>{var t;L(e),null==(t=(0,i.getTarget)(e))||t.removeEventListener(d,n)};null==(t=(0,i.getTarget)(e))||t.addEventListener(d,n)}));o.useEffect((()=>{if(!n||!c)return;s.current.__escapeKeyBubbles=k,s.current.__outsidePressBubbles=S;let e=-1;function t(e){r(!1,e,"ancestor-scroll")}function o(){window.clearTimeout(e),C.current=!0}function f(){e=window.setTimeout((()=>{C.current=!1}),(0,u.isWebKit)()?5:0)}const m=(0,i.getDocument)(l.floating);a&&(m.addEventListener("keydown",T?A:P,T),m.addEventListener("compositionstart",o),m.addEventListener("compositionend",f)),E&&m.addEventListener(d,O?I:L,O);let p=[];return g&&((0,u.isElement)(l.domReference)&&(p=(0,u.getOverflowAncestors)(l.domReference)),(0,u.isElement)(l.floating)&&(p=p.concat((0,u.getOverflowAncestors)(l.floating))),!(0,u.isElement)(l.reference)&&l.reference&&l.reference.contextElement&&(p=p.concat((0,u.getOverflowAncestors)(l.reference.contextElement)))),p=p.filter((e=>{var t;return e!==(null==(t=m.defaultView)?void 0:t.visualViewport)})),p.forEach((e=>{e.addEventListener("scroll",t,{passive:!0})})),()=>{a&&(m.removeEventListener("keydown",T?A:P,T),m.removeEventListener("compositionstart",o),m.removeEventListener("compositionend",f)),E&&m.removeEventListener(d,O?I:L,O),p.forEach((e=>{e.removeEventListener("scroll",t)})),window.clearTimeout(e)}}),[s,l,a,E,d,n,r,g,c,k,S,P,T,A,L,O,I]),o.useEffect((()=>{x.current=!1}),[E,d]);const M=o.useMemo((()=>({onKeyDown:P,...m&&{[rt[p]]:e=>{r(!1,e.nativeEvent,"reference-press")},..."click"!==p&&{onClick(e){r(!1,e.nativeEvent,"reference-press")}}}})),[P,r,m,p]),_=o.useMemo((()=>({onKeyDown:P,onMouseDown(){R.current=!0},onMouseUp(){R.current=!0},[ot[d]]:()=>{x.current=!0}})),[P,d]);return o.useMemo((()=>c?{reference:M,floating:_}:{}),[c,M,_])}function st(e){const{open:t=!1,onOpenChange:n,elements:r}=e,i=U(),l=o.useRef({}),[s]=o.useState((()=>G())),c=null!=Q();{const e=r.reference;e&&!(0,u.isElement)(e)&&function(){for(var e,t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];const o="Floating UI: "+n.join(" ");var i;null!=(e=q)&&e.has(o)||(null==(i=q)||i.add(o),console.error(o))}("Cannot pass a virtual element to the `elements.reference` option,","as it must be a real DOM element. Use `refs.setPositionReference()`","instead.")}const[a,f]=o.useState(r.reference),d=v(((e,t,r)=>{l.current.openEvent=e?t:void 0,s.emit("openchange",{open:e,event:t,reason:r,nested:c}),null==n||n(e,t,r)})),m=o.useMemo((()=>({setPositionReference:f})),[]),p=o.useMemo((()=>({reference:a||r.reference||null,floating:r.floating||null,domReference:r.reference})),[a,r.reference,r.floating]);return o.useMemo((()=>({dataRef:l,open:t,onOpenChange:d,elements:p,events:s,floatingId:i,refs:m})),[t,d,p,s,i,m])}function ut(e){void 0===e&&(e={});const{nodeId:t}=e,n=st({...e,elements:{reference:null,floating:null,...e.elements}}),r=e.rootContext||n,i=r.elements,[l,s]=o.useState(null),[c,a]=o.useState(null),d=(null==i?void 0:i.domReference)||l,m=o.useRef(null),p=ee();L((()=>{d&&(m.current=d)}),[d]);const g=(0,f.useFloating)({...e,elements:{...i,...c&&{reference:c}}}),v=o.useCallback((e=>{const t=(0,u.isElement)(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),getClientRects:()=>e.getClientRects(),contextElement:e}:e;a(t),g.refs.setReference(t)}),[g.refs]),h=o.useCallback((e=>{((0,u.isElement)(e)||null===e)&&(m.current=e,s(e)),((0,u.isElement)(g.refs.reference.current)||null===g.refs.reference.current||null!==e&&!(0,u.isElement)(e))&&g.refs.setReference(e)}),[g.refs]),y=o.useMemo((()=>({...g.refs,setReference:h,setPositionReference:v,domReference:m})),[g.refs,h,v]),b=o.useMemo((()=>({...g.elements,domReference:d})),[g.elements,d]),w=o.useMemo((()=>({...g,...r,refs:y,elements:b,nodeId:t})),[g,y,b,t,r]);return L((()=>{r.dataRef.current.floatingContext=w;const e=null==p?void 0:p.nodesRef.current.find((e=>e.id===t));e&&(e.context=w)})),o.useMemo((()=>({...g,context:w,refs:y,elements:b})),[g,y,b,w])}function ct(){return(0,i.isMac)()&&(0,i.isSafari)()}function at(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,events:l,dataRef:s,elements:c}=e,{enabled:a=!0,visibleOnly:f=!0}=t,d=o.useRef(!1),m=o.useRef(-1),p=o.useRef(!0);o.useEffect((()=>{if(!a)return;const e=(0,u.getWindow)(c.domReference);function t(){!n&&(0,u.isHTMLElement)(c.domReference)&&c.domReference===(0,i.activeElement)((0,i.getDocument)(c.domReference))&&(d.current=!0)}function r(){p.current=!0}function o(){p.current=!1}return e.addEventListener("blur",t),ct()&&(e.addEventListener("keydown",r,!0),e.addEventListener("pointerdown",o,!0)),()=>{e.removeEventListener("blur",t),ct()&&(e.removeEventListener("keydown",r,!0),e.removeEventListener("pointerdown",o,!0))}}),[c.domReference,n,a]),o.useEffect((()=>{if(a)return l.on("openchange",e),()=>{l.off("openchange",e)};function e(e){let{reason:t}=e;"reference-press"!==t&&"escape-key"!==t||(d.current=!0)}}),[l,a]),o.useEffect((()=>()=>{ie(m)}),[]);const g=o.useMemo((()=>({onMouseLeave(){d.current=!1},onFocus(e){if(d.current)return;const t=(0,i.getTarget)(e.nativeEvent);if(f&&(0,u.isElement)(t))if(ct()&&!e.relatedTarget){if(!p.current&&!(0,i.isTypeableElement)(t))return}else if(!function(e){if(!e||function(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map((e=>{let{brand:t,version:n}=e;return t+"/"+n})).join(" "):navigator.userAgent}().includes("jsdom/"))return!0;try{return e.matches(":focus-visible")}catch(e){return!0}}(t))return;r(!0,e.nativeEvent,"focus")},onBlur(e){d.current=!1;const t=e.relatedTarget,n=e.nativeEvent,o=(0,u.isElement)(t)&&t.hasAttribute(oe("focus-guard"))&&"outside"===t.getAttribute("data-type");m.current=window.setTimeout((()=>{var e;const l=(0,i.activeElement)(c.domReference?c.domReference.ownerDocument:document);(t||l!==c.domReference)&&((0,i.contains)(null==(e=s.current.floatingContext)?void 0:e.refs.floating.current,l)||(0,i.contains)(c.domReference,l)||o||r(!1,n,"focus"))}))}})),[s,c.domReference,r,f]);return o.useMemo((()=>a?{reference:g}:{}),[a,g])}const ft="active",dt="selected";function mt(e,t,n){const r=new Map,o="item"===n;let i=e;if(o&&e){const{[ft]:t,[dt]:n,...r}=e;i=r}return{..."floating"===n&&{tabIndex:-1,[We]:""},...i,...t.map((t=>{const r=t?t[n]:null;return"function"==typeof r?e?r(e):null:r})).concat(e).reduce(((e,t)=>t?(Object.entries(t).forEach((t=>{let[n,i]=t;var l;o&&[ft,dt].includes(n)||(0===n.indexOf("on")?(r.has(n)||r.set(n,[]),"function"==typeof i&&(null==(l=r.get(n))||l.push(i),e[n]=function(){for(var e,t=arguments.length,o=new Array(t),i=0;i<t;i++)o[i]=arguments[i];return null==(e=r.get(n))?void 0:e.map((e=>e(...o))).find((e=>void 0!==e))})):e[n]=i)})),e):e),{})}}function pt(e){void 0===e&&(e=[]);const t=e.map((e=>null==e?void 0:e.reference)),n=e.map((e=>null==e?void 0:e.floating)),r=e.map((e=>null==e?void 0:e.item)),i=o.useCallback((t=>mt(t,e,"reference")),t),l=o.useCallback((t=>mt(t,e,"floating")),n),s=o.useCallback((t=>mt(t,e,"item")),r);return o.useMemo((()=>({getReferenceProps:i,getFloatingProps:l,getItemProps:s})),[i,l,s])}const gt="Escape";function vt(e,t,n){switch(e){case"vertical":return t;case"horizontal":return n;default:return t||n}}function ht(e,t){return vt(t,e===h||e===y,e===b||e===w)}function yt(e,t,n){return vt(t,e===y,n?e===b:e===w)||"Enter"===e||" "===e||""===e}function bt(e,t,n){return vt(t,n?e===b:e===w,e===y)}function wt(e,t,n,r){return"both"===t||"horizontal"===t&&r&&r>1?e===gt:vt(t,n?e===w:e===b,e===h)}function Et(e,t){const{open:n,onOpenChange:r,elements:l}=e,{listRef:s,activeIndex:c,onNavigate:a=()=>{},enabled:f=!0,selectedIndex:d=null,allowEscape:m=!1,loop:p=!1,nested:g=!1,rtl:h=!1,virtual:E=!1,focusItemOnOpen:I="auto",focusItemOnHover:M=!0,openOnArrowKeyDown:_=!0,disabledIndices:j,orientation:D="vertical",cols:N=1,scrollItemIntoView:F=!0,virtualItemRef:$,itemSizes:H,dense:W=!1}=t;m&&(p||Y("`useListNavigation` looping must be enabled to allow escaping."),E||Y("`useListNavigation` must be virtual to allow escaping.")),"vertical"===D&&N>1&&Y("In grid list navigation mode (`cols` > 1), the `orientation` should",'be either "horizontal" or "both".');const V=le(Ve(l.floating)),B=Q(),z=ee();L((()=>{e.dataRef.current.orientation=D}),[e,D]);const K=v((()=>{a(-1===X.current?null:X.current)})),U=(0,i.isTypeableCombobox)(l.domReference),q=o.useRef(I),X=o.useRef(null!=d?d:-1),G=o.useRef(null),J=o.useRef(!0),Z=o.useRef(K),te=o.useRef(!!l.floating),ne=o.useRef(n),re=o.useRef(!1),oe=o.useRef(!1),ie=le(j),se=le(n),ue=le(F),ce=le(d),[ae,fe]=o.useState(),[de,me]=o.useState(),pe=v((()=>{function e(e){E?(fe(e.id),null==z||z.events.emit("virtualfocus",e),$&&($.current=e)):ve(e,{sync:re.current,preventScroll:!0})}const t=s.current[X.current];t&&e(t),(re.current?e=>e():requestAnimationFrame)((()=>{const n=s.current[X.current]||t;if(!n)return;t||e(n);const r=ue.current;r&&he&&(oe.current||!J.current)&&(null==n.scrollIntoView||n.scrollIntoView("boolean"==typeof r?{block:"nearest",inline:"nearest"}:r))}))}));L((()=>{f&&(n&&l.floating?q.current&&null!=d&&(oe.current=!0,X.current=d,K()):te.current&&(X.current=-1,Z.current()))}),[f,n,l.floating,d,K]),L((()=>{if(f&&n&&l.floating)if(null==c){if(re.current=!1,null!=ce.current)return;if(te.current&&(X.current=-1,pe()),(!ne.current||!te.current)&&q.current&&(null!=G.current||!0===q.current&&null==G.current)){let e=0;const t=()=>{null==s.current[0]?(e<2&&(e?requestAnimationFrame:queueMicrotask)(t),e++):(X.current=null==G.current||yt(G.current,D,h)||g?R(s,ie.current):k(s,ie.current),G.current=null,K())};t()}}else x(s,c)||(X.current=c,pe(),oe.current=!1)}),[f,n,l.floating,c,ce,g,s,D,h,K,pe,ie]),L((()=>{var e;if(!f||l.floating||!z||E||!te.current)return;const t=z.nodesRef.current,n=null==(e=t.find((e=>e.id===B)))||null==(e=e.context)?void 0:e.elements.floating,r=(0,i.activeElement)((0,i.getDocument)(l.floating)),o=t.some((e=>e.context&&(0,i.contains)(e.context.elements.floating,r)));n&&!o&&J.current&&n.focus({preventScroll:!0})}),[f,l.floating,z,B,E]),L((()=>{if(f&&z&&E&&!B)return z.events.on("virtualfocus",e),()=>{z.events.off("virtualfocus",e)};function e(e){me(e.id),$&&($.current=e)}}),[f,z,E,B,$]),L((()=>{Z.current=K,ne.current=n,te.current=!!l.floating})),L((()=>{n||(G.current=null)}),[n]);const ge=null!=c,he=o.useMemo((()=>{function e(e){if(!n)return;const t=s.current.indexOf(e);-1!==t&&X.current!==t&&(X.current=t,K())}return{onFocus(t){let{currentTarget:n}=t;re.current=!0,e(n)},onClick:e=>{let{currentTarget:t}=e;return t.focus({preventScroll:!0})},...M&&{onMouseMove(t){let{currentTarget:n}=t;re.current=!0,oe.current=!1,e(n)},onPointerLeave(e){let{pointerType:t}=e;var n;J.current&&"touch"!==t&&(re.current=!0,X.current=-1,K(),E||null==(n=V.current)||n.focus({preventScroll:!0}))}}}}),[n,V,M,s,K,E]),be=v((e=>{if(J.current=!1,re.current=!0,229===e.which)return;if(!se.current&&e.currentTarget===V.current)return;if(g&&wt(e.key,D,h,N))return(0,i.stopEvent)(e),r(!1,e.nativeEvent,"list-navigation"),void((0,u.isHTMLElement)(l.domReference)&&(E?null==z||z.events.emit("virtualfocus",l.domReference):l.domReference.focus()));const t=X.current,o=R(s,j),c=k(s,j);if(U||("Home"===e.key&&((0,i.stopEvent)(e),X.current=o,K()),"End"===e.key&&((0,i.stopEvent)(e),X.current=c,K())),N>1){const t=H||Array.from({length:s.current.length},(()=>({width:1,height:1}))),n=O(t,N,W),r=n.findIndex((e=>null!=e&&!A(s.current,e,j))),i=n.reduce(((e,t,n)=>null==t||A(s.current,t,j)?e:n),-1),l=n[T({current:n.map((e=>null!=e?s.current[e]:null))},{event:e,orientation:D,loop:p,rtl:h,cols:N,disabledIndices:P([...j||s.current.map(((e,t)=>A(s.current,t)?t:void 0)),void 0],n),minIndex:r,maxIndex:i,prevIndex:C(X.current>c?o:X.current,t,n,N,e.key===y?"bl":e.key===(h?b:w)?"tr":"tl"),stopEvent:!0})];if(null!=l&&(X.current=l,K()),"both"===D)return}if(ht(e.key,D)){if((0,i.stopEvent)(e),n&&!E&&(0,i.activeElement)(e.currentTarget.ownerDocument)===e.currentTarget)return X.current=yt(e.key,D,h)?o:c,void K();yt(e.key,D,h)?X.current=p?t>=c?m&&t!==s.current.length?-1:o:S(s,{startingIndex:t,disabledIndices:j}):Math.min(c,S(s,{startingIndex:t,disabledIndices:j})):X.current=p?t<=o?m&&-1!==t?s.current.length:c:S(s,{startingIndex:t,decrement:!0,disabledIndices:j}):Math.max(o,S(s,{startingIndex:t,decrement:!0,disabledIndices:j})),x(s,X.current)&&(X.current=-1),K()}})),we=o.useMemo((()=>E&&n&&ge&&{"aria-activedescendant":de||ae}),[E,n,ge,de,ae]),Ee=o.useMemo((()=>({"aria-orientation":"both"===D?void 0:D,...U?{}:we,onKeyDown:be,onPointerMove(){J.current=!0}})),[we,be,D,U]),xe=o.useMemo((()=>{function e(e){"auto"===I&&(0,i.isVirtualClick)(e.nativeEvent)&&(q.current=!0)}function t(e){q.current=I,"auto"===I&&(0,i.isVirtualPointerEvent)(e.nativeEvent)&&(q.current=!0)}return{...we,onKeyDown(e){var t;J.current=!1;const o=e.key.startsWith("Arrow"),l=["Home","End"].includes(e.key),u=o||l,c=null==z||null==(t=z.nodesRef.current.find((e=>e.id===B)))||null==(t=t.context)||null==(t=t.dataRef)?void 0:t.current.orientation,a=bt(e.key,D,h),f=wt(e.key,D,h,N),m=bt(e.key,c,h),p=ht(e.key,D),v=(g?m:p)||"Enter"===e.key||""===e.key.trim();if(E&&n){const t=null==z?void 0:z.nodesRef.current.find((e=>null==e.parentId)),n=z&&t?function(e,t){let n,r=-1;return function t(o,i){i>r&&(n=o,r=i),ye(e,o).forEach((e=>{t(e.id,i+1)}))}(t,0),e.find((e=>e.id===n))}(z.nodesRef.current,t.id):null;if(u&&n&&$){const t=new KeyboardEvent("keydown",{key:e.key,bubbles:!0});if(a||f){var y,b;const r=(null==(y=n.context)?void 0:y.elements.domReference)===e.currentTarget,o=f&&!r?null==(b=n.context)?void 0:b.elements.domReference:a?s.current.find((e=>(null==e?void 0:e.id)===ae)):null;o&&((0,i.stopEvent)(e),o.dispatchEvent(t),me(void 0))}var w;if((p||l)&&n.context&&n.context.open&&n.parentId&&e.currentTarget!==n.context.elements.domReference)return(0,i.stopEvent)(e),void(null==(w=n.context.elements.domReference)||w.dispatchEvent(t))}return be(e)}if(n||_||!o){if(v){const t=ht(e.key,c);G.current=g&&t?null:e.key}g?m&&((0,i.stopEvent)(e),n?(X.current=R(s,ie.current),K()):r(!0,e.nativeEvent,"list-navigation")):p&&(null!=d&&(X.current=d),(0,i.stopEvent)(e),!n&&_?r(!0,e.nativeEvent,"list-navigation"):be(e),n&&K())}},onFocus(){n&&!E&&(X.current=-1,K())},onPointerDown:t,onPointerEnter:t,onMouseDown:e,onClick:e}}),[ae,we,N,be,ie,I,s,g,K,r,n,_,D,B,h,d,z,E,$]);return o.useMemo((()=>f?{reference:xe,floating:Ee,item:he}:{}),[f,xe,Ee,he])}const xt=new Map([["select","listbox"],["combobox","listbox"],["label",!1]]);function Rt(e,t){var n;void 0===t&&(t={});const{open:r,floatingId:i}=e,{enabled:l=!0,role:s="dialog"}=t,u=null!=(n=xt.get(s))?n:s,c=U(),a=null!=Q(),f=o.useMemo((()=>"tooltip"===u||"label"===s?{["aria-"+("label"===s?"labelledby":"describedby")]:r?i:void 0}:{"aria-expanded":r?"true":"false","aria-haspopup":"alertdialog"===u?"dialog":u,"aria-controls":r?i:void 0,..."listbox"===u&&{role:"combobox"},..."menu"===u&&{id:c},..."menu"===u&&a&&{role:"menuitem"},..."select"===s&&{"aria-autocomplete":"none"},..."combobox"===s&&{"aria-autocomplete":"list"}}),[u,i,a,r,c,s]),d=o.useMemo((()=>{const e={id:i,...u&&{role:u}};return"tooltip"===u||"label"===s?e:{...e,..."menu"===u&&{"aria-labelledby":c}}}),[u,i,c,s]),m=o.useCallback((e=>{let{active:t,selected:n}=e;const r={role:"option",...t&&{id:i+"-option"}};switch(s){case"select":return{...r,"aria-selected":t&&n};case"combobox":return{...r,...t&&{"aria-selected":!0}}}return{}}),[i,s]);return o.useMemo((()=>l?{reference:f,floating:d,item:m}:{}),[l,f,d,m])}const kt=e=>e.replace(/[A-Z]+(?![a-z])|[A-Z]/g,((e,t)=>(t?"-":"")+e.toLowerCase()));function St(e,t){return"function"==typeof e?e(t):e}function Tt(e,t){void 0===t&&(t={});const{open:n,elements:{floating:r}}=e,{duration:i=250}=t,l=("number"==typeof i?i:i.close)||0,[s,u]=o.useState("unmounted"),c=function(e,t){const[n,r]=o.useState(e);return e&&!n&&r(!0),o.useEffect((()=>{if(!e&&n){const e=setTimeout((()=>r(!1)),t);return()=>clearTimeout(e)}}),[e,n,t]),n}(n,l);return c||"close"!==s||u("unmounted"),L((()=>{if(r){if(n){u("initial");const e=requestAnimationFrame((()=>{u("open")}));return()=>{cancelAnimationFrame(e)}}u("close")}}),[n,r]),{isMounted:c,status:s}}function Ot(e,t){void 0===t&&(t={});const{initial:n={opacity:0},open:r,close:i,common:l,duration:s=250}=t,u=e.placement,c=u.split("-")[0],a=o.useMemo((()=>({side:c,placement:u})),[c,u]),f="number"==typeof s,d=(f?s:s.open)||0,m=(f?s:s.close)||0,[p,g]=o.useState((()=>({...St(l,a),...St(n,a)}))),{isMounted:v,status:h}=Tt(e,{duration:s}),y=le(n),b=le(r),w=le(i),E=le(l);return L((()=>{const e=St(y.current,a),t=St(w.current,a),n=St(E.current,a),r=St(b.current,a)||Object.keys(e).reduce(((e,t)=>(e[t]="",e)),{});if("initial"===h&&g((t=>({transitionProperty:t.transitionProperty,...n,...e}))),"open"===h&&g({transitionProperty:Object.keys(r).map(kt).join(","),transitionDuration:d+"ms",...n,...r}),"close"===h){const r=t||e;g({transitionProperty:Object.keys(r).map(kt).join(","),transitionDuration:m+"ms",...n,...r})}}),[m,w,y,b,E,d,h,a]),{isMounted:v,styles:p}}function Ct(e,t){var n;const{open:r,dataRef:l}=e,{listRef:s,activeIndex:u,onMatch:c,onTypingChange:a,enabled:f=!0,findMatch:d=null,resetMs:m=750,ignoreKeys:p=[],selectedIndex:g=null}=t,h=o.useRef(-1),y=o.useRef(""),b=o.useRef(null!=(n=null!=g?g:u)?n:-1),w=o.useRef(null),E=v(c),x=v(a),R=le(d),k=le(p);L((()=>{r&&(ie(h),w.current=null,y.current="")}),[r]),L((()=>{var e;r&&""===y.current&&(b.current=null!=(e=null!=g?g:u)?e:-1)}),[r,g,u]);const S=v((e=>{e?l.current.typing||(l.current.typing=e,x(e)):l.current.typing&&(l.current.typing=e,x(e))})),T=v((e=>{function t(e,t,n){const r=R.current?R.current(t,n):t.find((e=>0===(null==e?void 0:e.toLocaleLowerCase().indexOf(n.toLocaleLowerCase()))));return r?e.indexOf(r):-1}const n=s.current;if(y.current.length>0&&" "!==y.current[0]&&(-1===t(n,n,y.current)?S(!1):" "===e.key&&(0,i.stopEvent)(e)),null==n||k.current.includes(e.key)||1!==e.key.length||e.ctrlKey||e.metaKey||e.altKey)return;r&&" "!==e.key&&((0,i.stopEvent)(e),S(!0)),n.every((e=>{var t,n;return!e||(null==(t=e[0])?void 0:t.toLocaleLowerCase())!==(null==(n=e[1])?void 0:n.toLocaleLowerCase())}))&&y.current===e.key&&(y.current="",b.current=w.current),y.current+=e.key,ie(h),h.current=window.setTimeout((()=>{y.current="",b.current=w.current,S(!1)}),m);const o=b.current,l=t(n,[...n.slice((o||0)+1),...n.slice(0,(o||0)+1)],y.current);-1!==l?(E(l),w.current=l):" "!==e.key&&(y.current="",S(!1))})),O=o.useMemo((()=>({onKeyDown:T})),[T]),C=o.useMemo((()=>({onKeyDown:T,onKeyUp(e){" "===e.key&&S(!1)}})),[T,S]);return o.useMemo((()=>f?{reference:O,floating:C}:{}),[f,O,C])}function Pt(e,t){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:t}}}}const At=e=>({name:"inner",options:e,async fn(t){const{listRef:n,overflowRef:r,onFallbackChange:o,offset:i=0,index:s=0,minItemsVisible:u=4,referenceOverflowThreshold:c=0,scrollRef:m,...p}=(0,l.evaluate)(e,t),{rects:g,elements:{floating:v}}=t,h=n.current[s],y=(null==m?void 0:m.current)||v,b=v.clientTop||y.clientTop,w=0!==v.clientTop,E=0!==y.clientTop,x=v===y;if(t.placement.startsWith("bottom")||Y('`placement` side must be "bottom" when using the `inner`',"middleware."),!h)return{};const R={...t,...await(0,f.offset)(-h.offsetTop-v.clientTop-g.reference.height/2-h.offsetHeight/2-i).fn(t)},k=await(0,d.detectOverflow)(Pt(R,y.scrollHeight+b+v.clientTop),p),S=await(0,d.detectOverflow)(R,{...p,elementContext:"reference"}),T=(0,l.max)(0,k.top),O=R.y+T,C=(y.scrollHeight>y.clientHeight?e=>e:l.round)((0,l.max)(0,y.scrollHeight+(w&&x||E?2*b:0)-T-(0,l.max)(0,k.bottom)));if(y.style.maxHeight=C+"px",y.scrollTop=T,o){const e=y.offsetHeight<h.offsetHeight*(0,l.min)(u,n.current.length)-1||S.top>=-c||S.bottom>=-c;a.flushSync((()=>o(e)))}return r&&(r.current=await(0,d.detectOverflow)(Pt({...R,y:O},y.offsetHeight+b+v.clientTop),p)),{y:O}}});function Lt(e,t){const{open:n,elements:r}=e,{enabled:l=!0,overflowRef:s,scrollRef:u,onChange:c}=t,f=v(c),d=o.useRef(!1),m=o.useRef(null),p=o.useRef(null);o.useEffect((()=>{if(!l)return;function e(e){if(e.ctrlKey||!t||null==s.current)return;const n=e.deltaY,r=s.current.top>=-.5,o=s.current.bottom>=-.5,l=t.scrollHeight-t.clientHeight,u=n<0?-1:1,c=n<0?"max":"min";t.scrollHeight<=t.clientHeight||(!r&&n>0||!o&&n<0?(e.preventDefault(),a.flushSync((()=>{f((e=>e+Math[c](n,l*u)))}))):/firefox/i.test((0,i.getUserAgent)())&&(t.scrollTop+=n))}const t=(null==u?void 0:u.current)||r.floating;return n&&t?(t.addEventListener("wheel",e),requestAnimationFrame((()=>{m.current=t.scrollTop,null!=s.current&&(p.current={...s.current})})),()=>{m.current=null,p.current=null,t.removeEventListener("wheel",e)}):void 0}),[l,n,r.floating,s,u,f]);const g=o.useMemo((()=>({onKeyDown(){d.current=!0},onWheel(){d.current=!1},onPointerMove(){d.current=!1},onScroll(){const e=(null==u?void 0:u.current)||r.floating;if(s.current&&e&&d.current){if(null!==m.current){const t=e.scrollTop-m.current;(s.current.bottom<-.5&&t<-1||s.current.top<-.5&&t>1)&&a.flushSync((()=>f((e=>e+t))))}requestAnimationFrame((()=>{m.current=e.scrollTop}))}}})),[r.floating,f,s,u]);return o.useMemo((()=>l?{floating:g}:{}),[l,g])}function It(e,t){const[n,r]=e;let o=!1;const i=t.length;for(let e=0,l=i-1;e<i;l=e++){const[i,s]=t[e]||[0,0],[u,c]=t[l]||[0,0];s>=r!=c>=r&&n<=(u-i)*(r-s)/(c-s)+i&&(o=!o)}return o}function Mt(e){void 0===e&&(e={});const{buffer:t=.5,blockPointerEvents:n=!1,requireIntent:r=!0}=e;let o,l=!1,s=null,c=null,a=performance.now();const f=e=>{let{x:n,y:f,placement:d,elements:m,onClose:p,nodeId:g,tree:v}=e;return function(e){function h(){clearTimeout(o),p()}if(clearTimeout(o),!m.domReference||!m.floating||null==d||null==n||null==f)return;const{clientX:y,clientY:b}=e,w=[y,b],E=(0,i.getTarget)(e),x="mouseleave"===e.type,R=(0,i.contains)(m.floating,E),k=(0,i.contains)(m.domReference,E),S=m.domReference.getBoundingClientRect(),T=m.floating.getBoundingClientRect(),O=d.split("-")[0],C=n>T.right-T.width/2,P=f>T.bottom-T.height/2,A=function(e,t){return e[0]>=t.x&&e[0]<=t.x+t.width&&e[1]>=t.y&&e[1]<=t.y+t.height}(w,S),L=T.width>S.width,I=T.height>S.height,M=(L?S:T).left,_=(L?S:T).right,j=(I?S:T).top,D=(I?S:T).bottom;if(R&&(l=!0,!x))return;if(k&&(l=!1),k&&!x)return void(l=!0);if(x&&(0,u.isElement)(e.relatedTarget)&&(0,i.contains)(m.floating,e.relatedTarget))return;if(v&&ye(v.nodesRef.current,g).some((e=>{let{context:t}=e;return null==t?void 0:t.open})))return;if("top"===O&&f>=S.bottom-1||"bottom"===O&&f<=S.top+1||"left"===O&&n>=S.right-1||"right"===O&&n<=S.left+1)return h();let N=[];switch(O){case"top":N=[[M,S.top+1],[M,T.bottom-1],[_,T.bottom-1],[_,S.top+1]];break;case"bottom":N=[[M,T.top+1],[M,S.bottom-1],[_,S.bottom-1],[_,T.top+1]];break;case"left":N=[[T.right-1,D],[T.right-1,j],[S.left+1,j],[S.left+1,D]];break;case"right":N=[[S.right-1,D],[S.right-1,j],[T.left+1,j],[T.left+1,D]]}if(!It([y,b],N)){if(l&&!A)return h();if(!x&&r){const t=function(e,t){const n=performance.now(),r=n-a;if(null===s||null===c||0===r)return s=e,c=t,a=n,null;const o=e-s,i=t-c,l=Math.sqrt(o*o+i*i);return s=e,c=t,a=n,l/r}(e.clientX,e.clientY);if(null!==t&&t<.1)return h()}It([y,b],function(e){let[n,r]=e;switch(O){case"top":return[[L?n+t/2:C?n+4*t:n-4*t,r+t+1],[L?n-t/2:C?n+4*t:n-4*t,r+t+1],[T.left,C||L?T.bottom-t:T.top],[T.right,C?L?T.bottom-t:T.top:T.bottom-t]];case"bottom":return[[L?n+t/2:C?n+4*t:n-4*t,r-t],[L?n-t/2:C?n+4*t:n-4*t,r-t],[T.left,C||L?T.top+t:T.bottom],[T.right,C?L?T.top+t:T.bottom:T.top+t]];case"left":{const e=[n+t+1,I?r+t/2:P?r+4*t:r-4*t],o=[n+t+1,I?r-t/2:P?r+4*t:r-4*t];return[[P||I?T.right-t:T.left,T.top],[P?I?T.right-t:T.left:T.right-t,T.bottom],e,o]}case"right":return[[n-t,I?r+t/2:P?r+4*t:r-4*t],[n-t,I?r-t/2:P?r+4*t:r-4*t],[P||I?T.left+t:T.right,T.top],[P?I?T.left+t:T.right:T.left+t,T.bottom]]}}([n,f]))?!l&&r&&(o=window.setTimeout(h,40)):h()}}};return f.__options={blockPointerEvents:n},f}},"./node_modules/@floating-ui/react/dist/floating-ui.react.utils.mjs":function(e,t,n){n.r(t),n.d(t,{TYPEABLE_SELECTOR:function(){return w},activeElement:function(){return o},contains:function(){return i},getDocument:function(){return h},getPlatform:function(){return l},getTarget:function(){return b},getUserAgent:function(){return s},isAndroid:function(){return f},isEventTargetWithin:function(){return y},isJSDOM:function(){return m},isMac:function(){return d},isMouseLikePointerType:function(){return p},isReactEvent:function(){return g},isRootElement:function(){return v},isSafari:function(){return a},isTypeableCombobox:function(){return R},isTypeableElement:function(){return E},isVirtualClick:function(){return u},isVirtualPointerEvent:function(){return c},stopEvent:function(){return x}});var r=n("./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs");function o(e){let t=e.activeElement;for(;null!=(null==(n=t)||null==(n=n.shadowRoot)?void 0:n.activeElement);){var n;t=t.shadowRoot.activeElement}return t}function i(e,t){if(!e||!t)return!1;const n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&(0,r.isShadowRoot)(n)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}function l(){const e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}function s(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map((e=>{let{brand:t,version:n}=e;return t+"/"+n})).join(" "):navigator.userAgent}function u(e){return!(0!==e.mozInputSource||!e.isTrusted)||(f()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}function c(e){return!m()&&(!f()&&0===e.width&&0===e.height||f()&&1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType||e.width<1&&e.height<1&&0===e.pressure&&0===e.detail&&"touch"===e.pointerType)}function a(){return/apple/i.test(navigator.vendor)}function f(){const e=/android/i;return e.test(l())||e.test(s())}function d(){return l().toLowerCase().startsWith("mac")&&!navigator.maxTouchPoints}function m(){return s().includes("jsdom/")}function p(e,t){const n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}function g(e){return"nativeEvent"in e}function v(e){return e.matches("html,body")}function h(e){return(null==e?void 0:e.ownerDocument)||document}function y(e,t){if(null==t)return!1;if("composedPath"in e)return e.composedPath().includes(t);const n=e;return null!=n.target&&t.contains(n.target)}function b(e){return"composedPath"in e?e.composedPath()[0]:e.target}const w="input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])";function E(e){return(0,r.isHTMLElement)(e)&&e.matches(w)}function x(e){e.preventDefault(),e.stopPropagation()}function R(e){return!!e&&"combobox"===e.getAttribute("role")&&E(e)}},"./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs":function(e,t,n){function r(){return"undefined"!=typeof window}function o(e){return s(e)?(e.nodeName||"").toLowerCase():"#document"}function i(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function l(e){var t;return null==(t=(s(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function s(e){return!!r()&&(e instanceof Node||e instanceof i(e).Node)}function u(e){return!!r()&&(e instanceof Element||e instanceof i(e).Element)}function c(e){return!!r()&&(e instanceof HTMLElement||e instanceof i(e).HTMLElement)}function a(e){return!(!r()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof i(e).ShadowRoot)}function f(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=y(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function d(e){return["table","td","th"].includes(o(e))}function m(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(e){return!1}}))}function p(e){const t=v(),n=u(e)?y(e):e;return["transform","translate","scale","rotate","perspective"].some((e=>!!n[e]&&"none"!==n[e]))||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function g(e){let t=w(e);for(;c(t)&&!h(t);){if(p(t))return t;if(m(t))return null;t=w(t)}return null}function v(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function h(e){return["html","body","#document"].includes(o(e))}function y(e){return i(e).getComputedStyle(e)}function b(e){return u(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function w(e){if("html"===o(e))return e;const t=e.assignedSlot||e.parentNode||a(e)&&e.host||l(e);return a(t)?t.host:t}function E(e){const t=w(e);return h(t)?e.ownerDocument?e.ownerDocument.body:e.body:c(t)&&f(t)?t:E(t)}function x(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=E(e),l=o===(null==(r=e.ownerDocument)?void 0:r.body),s=i(o);if(l){const e=R(s);return t.concat(s,s.visualViewport||[],f(o)?o:[],e&&n?x(e):[])}return t.concat(o,x(o,[],n))}function R(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}n.r(t),n.d(t,{getComputedStyle:function(){return y},getContainingBlock:function(){return g},getDocumentElement:function(){return l},getFrameElement:function(){return R},getNearestOverflowAncestor:function(){return E},getNodeName:function(){return o},getNodeScroll:function(){return b},getOverflowAncestors:function(){return x},getParentNode:function(){return w},getWindow:function(){return i},isContainingBlock:function(){return p},isElement:function(){return u},isHTMLElement:function(){return c},isLastTraversableNode:function(){return h},isNode:function(){return s},isOverflowElement:function(){return f},isShadowRoot:function(){return a},isTableElement:function(){return d},isTopLayer:function(){return m},isWebKit:function(){return v}})},"./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs":function(e,t,n){n.r(t),n.d(t,{alignments:function(){return o},clamp:function(){return m},createCoords:function(){return a},evaluate:function(){return p},expandPaddingObject:function(){return T},floor:function(){return c},getAlignment:function(){return v},getAlignmentAxis:function(){return w},getAlignmentSides:function(){return E},getAxisLength:function(){return y},getExpandedPlacements:function(){return x},getOppositeAlignmentPlacement:function(){return R},getOppositeAxis:function(){return h},getOppositeAxisPlacements:function(){return k},getOppositePlacement:function(){return S},getPaddingObject:function(){return O},getSide:function(){return g},getSideAxis:function(){return b},max:function(){return s},min:function(){return l},placements:function(){return i},rectToClientRect:function(){return C},round:function(){return u},sides:function(){return r}});const r=["top","right","bottom","left"],o=["start","end"],i=r.reduce(((e,t)=>e.concat(t,t+"-"+o[0],t+"-"+o[1])),[]),l=Math.min,s=Math.max,u=Math.round,c=Math.floor,a=e=>({x:e,y:e}),f={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function m(e,t,n){return s(e,l(t,n))}function p(e,t){return"function"==typeof e?e(t):e}function g(e){return e.split("-")[0]}function v(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function y(e){return"y"===e?"height":"width"}function b(e){return["top","bottom"].includes(g(e))?"y":"x"}function w(e){return h(b(e))}function E(e,t,n){void 0===n&&(n=!1);const r=v(e),o=w(e),i=y(o);let l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=S(l)),[l,S(l)]}function x(e){const t=S(e);return[R(e),t,R(t)]}function R(e){return e.replace(/start|end/g,(e=>d[e]))}function k(e,t,n,r){const o=v(e);let i=function(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],l=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:l;default:return[]}}(g(e),"start"===n,r);return o&&(i=i.map((e=>e+"-"+o)),t&&(i=i.concat(i.map(R)))),i}function S(e){return e.replace(/left|right|bottom|top/g,(e=>f[e]))}function T(e){return{top:0,right:0,bottom:0,left:0,...e}}function O(e){return"number"!=typeof e?T(e):{top:e,right:e,bottom:e,left:e}}function C(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}}},r={};function o(e){var t=r[e];if(void 0!==t)return t.exports;var i=r[e]={exports:{}};return n[e](i,i.exports,o),i.exports}t=Object.getPrototypeOf?function(e){return Object.getPrototypeOf(e)}:function(e){return e.__proto__},o.t=function(n,r){if(1&r&&(n=this(n)),8&r)return n;if("object"==typeof n&&n){if(4&r&&n.__esModule)return n;if(16&r&&"function"==typeof n.then)return n}var i=Object.create(null);o.r(i);var l={};e=e||[null,t({}),t([]),t(t)];for(var s=2&r&&n;"object"==typeof s&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach((function(e){l[e]=function(){return n[e]}}));return l.default=function(){return n},o.d(i,l),i},o.d=function(e,t){for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};!function(){o.r(i),o.d(i,{createPropsResolver:function(){return j},createTransformer:function(){return Y},createTransformersRegistry:function(){return D},init:function(){return Ie},settingsTransformersRegistry:function(){return q},styleTransformersRegistry:function(){return N}});var e=o("@elementor/editor"),t=o("react"),n=o("@elementor/editor-elements"),r=o("@elementor/editor-v1-adapters"),l=o("@elementor/ui"),s=o("./node_modules/@floating-ui/react/dist/floating-ui.react.mjs"),u=o("./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs"),c=o("./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs"),a=o("@elementor/editor-styles-repository"),f=o("@elementor/editor-styles"),d=o("@elementor/editor-props"),m=o("@elementor/editor-responsive"),p=o("@elementor/utils"),g=o("@elementor/wp-media"),v=o("@elementor/twing"),h=o("@elementor/editor-notifications"),y=o("@wordpress/i18n");var b="elementor-preview-responsive-wrapper",w=(0,l.styled)(l.Box,{shouldForwardProp:e=>"isSelected"!==e&&"isSmallerOffset"!==e})((({theme:e,isSelected:t,isSmallerOffset:n})=>({outline:`${t?"2px":"1px"} solid ${e.palette.primary.light}`,outlineOffset:t&&!n?"-2px":"-1px",pointerEvents:"none"})));function E({element:e,isSelected:n,id:r}){const{context:o,floating:i,isVisible:l}=function({element:e,isSelected:n}){const[r,o]=(0,t.useState)(!1),{refs:i,floatingStyles:l,context:a}=(0,s.useFloating)({open:r||n,onOpenChange:o,whileElementsMounted:u.autoUpdate,middleware:[(0,c.size)({apply({elements:e,rects:t}){Object.assign(e.floating.style,{width:`${t.reference.width+2}px`,height:`${t.reference.height+2}px`})}}),(0,c.offset)((({rects:e})=>-e.reference.height/2-e.floating.height/2))]});return(0,t.useEffect)((()=>{i.setReference(e)}),[e,i]),{isVisible:r||n,context:a,floating:{setRef:i.setFloating,ref:i.floating,styles:l}}}({element:e,isSelected:n}),{getFloatingProps:a,getReferenceProps:f}=(0,s.useInteractions)([(0,s.useHover)(o)]);!function(e,n){(0,t.useEffect)((()=>{const t=e,{events:r,attrs:o}=function(e){const t=/^on(?=[A-Z])/;return Object.entries(e).reduce(((e,[n,r])=>{if(!t.test(n))return e.attrs.push([n,r]),e;const o=n.replace(t,"").toLowerCase(),i=r;return e.events.push([o,i]),e}),{events:[],attrs:[]})}(n());return r.forEach((([e,n])=>t.addEventListener(e,n))),o.forEach((([e,n])=>t.setAttribute(e,n))),()=>{r.forEach((([e,n])=>t.removeEventListener(e,n))),o.forEach((([e])=>t.removeAttribute(e)))}}),[n,e])}(e,f);const d=e.offsetHeight<=1;return l&&t.createElement(s.FloatingPortal,{id:b},t.createElement(w,{ref:i.setRef,isSelected:n,style:i.styles,"data-element-overlay":r,role:"presentation",isSmallerOffset:d,...a()}))}function x(){const e=(0,n.useSelectedElement)(),o=(0,r.__privateUseListenTo)([(0,r.windowEvent)("elementor/editor/element-rendered"),(0,r.windowEvent)("elementor/editor/element-destroyed")],(()=>(0,n.getElements)().filter((e=>R in(e.view?.el?.dataset??{}))).map((e=>[e.id,e.view?.getDomElement?.()?.get?.(0)])).filter((e=>!!e[1])))),i="edit"===(0,r.useEditMode)(),l=(0,r.__privateUseIsRouteActive)("panel/global");return i&&!l&&o.map((([n,r])=>t.createElement(E,{key:n,id:n,element:r,isSelected:e.element?.id===n})))}var R="atomic";function k(){const e=window;return e.elementor?.$preview?.[0]?.contentDocument}var S="data-e-removed",T="data-elementor-id",O="elementor-post-",C="-css";function P(e){const t=[...e.attributes].map((e=>[e.name,e.value]));return Object.fromEntries(t)}function A(e,t=[]){return{then:n=>(t.push(n),A(e,t)),execute:async()=>{let n;for(const r of t){if(e.aborted)break;n=await r(n,e)}}}}var L=e=>!!e&&"object"==typeof e&&"$$multi-props"in e&&!0===e["$$multi-props"],I=e=>({"$$multi-props":!0,value:e}),M=e=>e.value,_=3;function j({transformers:e,schema:t,onPropResolve:n}){async function r({props:e,schema:r,signal:i}){r=r??t;const l=Promise.all(Object.entries(r).map((async([t,r])=>{const l=e[t]??r.default,s=await o({value:l,key:t,type:r,signal:i});return n?.({key:t,value:s}),L(s)?M(s):{[t]:s}})));return Object.assign({},...(await l).filter(Boolean))}async function o({value:t,key:n,type:i,signal:l,depth:s=0}){if(null==t)return null;if(!(0,d.isTransformable)(t))return t;if(s>_)return null;if(!0===t.disabled)return null;if("union"===i.kind&&!(i=i.prop_types[t.$$type]))return null;if(t.$$type!==i.key)return null;let u=t.value;"object"===i.kind&&(u=await r({props:u,schema:i.shape,signal:l})),"array"===i.kind&&(u=await Promise.all(u.map((e=>o({value:e,key:n,type:i.item_prop_type,depth:s,signal:l})))));const c=e.get(t.$$type);if(!c)return null;try{return o({value:await c(u,{key:n,signal:l}),key:n,type:i,signal:l,depth:s+1})}catch{return null}}return r}function D(){const e={};let t=null;return{register(t,n){return e[t]=n,this},registerFallback(e){return t=e,this},get(n){return e[n]??t},all(){return{...e}}}}var N=D(),F=(e,t="preview")=>{const n=window;return n.elementor?.helpers?.enqueueFont?.(e,t)??null},$=(0,p.createError)({code:"unknown_style_type",message:"Unknown style type"}),H={class:"."};function W({resolve:e,breakpoints:t,selectorPrefix:n=""}){return async({styles:r,signal:o})=>{const i=r.map((async r=>{const i=Object.values(r.variants).map((async i=>{const l=await async function({props:e,resolve:t,signal:n}){const r=await t({props:e,signal:n});return Object.entries(r).reduce(((e,[t,n])=>(null===n||e.push(t+":"+n+";"),e)),[]).join("")}({props:i.props,resolve:e,signal:o});return V().for(r.cssName,r.type).withPrefix(n).withState(i.meta.state).withMediaQuery(i.meta.breakpoint?t[i.meta.breakpoint]:null).wrap(l)})),l=await Promise.all(i);return{id:r.id,value:l.join("")}}));return await Promise.all(i)}}function V(e="",t){return{for:(n,r)=>{const o=H[r];if(!o)throw new $({context:{type:r}});return V(`${e}${o}${n}`,t)},withPrefix:n=>V([n,e].filter(Boolean).join(" "),t),withState:n=>V(n?`${e}:${n}`:e,t),withMediaQuery:n=>{if(!n?.type)return V(e,t);const r=`${n.type}:${n.width}px`;return V(e,(e=>`@media(${r}){${e}}`))},wrap:n=>{const r=`${e}{${n}}`;return t?t(r):r}}}var B=".elementor";function z(){const e=function(e){const n=(0,m.useBreakpointsMap)();return(0,t.useMemo)((()=>W({selectorPrefix:B,breakpoints:n,resolve:e})),[e,n])}((0,t.useMemo)((()=>j({transformers:N,schema:(0,f.getStylesSchema)(),onPropResolve:({key:e,value:t})=>{"font-family"===e&&"string"==typeof t&&F(t)}})),[])),[n,o]=(0,t.useState)({}),i=(0,t.useMemo)((()=>a.stylesRepository.getProviders().map((t=>({provider:t,subscriber:K({provider:t,renderStyles:e,setStyleItems:o})})))),[e]);return(0,t.useEffect)((()=>{const e=i.map((({provider:e,subscriber:t})=>e.subscribe(t)));return()=>{e.forEach((e=>e()))}}),[i]),function(){const e=(0,t.useRef)(!1);(0,t.useEffect)((()=>{e.current||(e.current=!0,(0,r.registerDataHook)("after","editor/documents/attach-preview",(async()=>{const e=i.map((async({subscriber:e})=>e()));await Promise.all(e)})))}),[])}(),Object.values(n).sort((({provider:e},{provider:t})=>e.priority-t.priority)).flatMap((({items:e})=>e))}function K({provider:e,renderStyles:t,setStyleItems:n}){return function(){let r=null;return(...o)=>(r&&r.abort(),r=new AbortController,(r=>A(r.signal).then(((n,r)=>{const o=e.actions.all().map(((t,n,r)=>{const o=r[r.length-1-n];return{...o,cssName:e.actions.resolveCssName(o.id)}}));return t({styles:o,signal:r})})).then((t=>{n((n=>({...n,[e.getKey()]:{provider:e,items:t}})))})).execute())(r,...o))}()}function U(){const e=(0,r.__privateUseListenTo)((0,r.commandEndEvent)("editor/documents/attach-preview"),(()=>k()?.head)),n=z(),o=(0,r.__privateUseListenTo)((0,r.commandEndEvent)("editor/documents/attach-preview"),(()=>{const e=k();if(!e)return[];const t=function(e){return[...e.body.querySelectorAll(`[${T}]`)??[]].map((e=>e.getAttribute(T)||""))}(e).map((e=>`${O}${e}${C}`)),n=function(e){return[...e.head.querySelectorAll(`link[rel="stylesheet"][id^=${O}][id$=${C}]`)??[]]}(e).filter((e=>t.includes(e.getAttribute("id")??"")));return n.forEach((e=>{e.hasAttribute(S)||e.remove()})),n.map((e=>({...P(e),id:e.getAttribute("id")??"",[S]:!0})))}));return e?t.createElement(l.Portal,{container:e},n.map((e=>t.createElement("style",{"data-e-style-id":e.id,key:e.id},e.value))),o.map((e=>t.createElement("link",{...e,key:e.id})))):null}var q=D();function Y(e){return e}var X=({destination:e,isTargetBlank:t})=>({href:"number"==typeof e?"#post-id-"+e:e,target:t?"_blank":"_self"}),G=e=>({id:e.id??null,url:e.url??null}),J=async e=>{const{src:t,size:n}=e;if(!t?.id)return t?.url?{src:t.url}:null;const r=await(0,g.getMediaAttachment)({id:t.id}),o=r?.sizes?.[n??""];return o?{src:o.url,height:o.height,width:o.width}:r?{src:r.url,height:r.height,width:r.width}:null},Z=e=>e,Q=e=>{const{color:t=null}=e;return t?`linear-gradient(${t}, ${t})`:null},ee=e=>"radial"===e.type?`radial-gradient(circle at ${e.positions}, ${e.stops})`:`linear-gradient(${e.angle}deg, ${e.stops})`,te=e=>{const{image:t,size:n=null,position:r=null,repeat:o=null,attachment:i=null}=e;return t?{src:t.src?`url(${t.src})`:null,repeat:o,attachment:i,size:n,position:r}:null},ne=({width:e,height:t})=>`${e??"auto"} ${t??"auto"}`,re=e=>{if(!e||0===e.length)return null;const t=function(e){const t=(0,r.isExperimentActive)("e_v_3_30"),n=e.map((e=>"string"==typeof e?{src:e,repeat:null,attachment:null,size:null,position:null}:e));return t?n.filter((e=>e&&!!e.src)):n}(e);return 0===t.length?null:{"background-image":oe(t,"src","none",!0),"background-repeat":oe(t,"repeat","repeat"),"background-attachment":oe(t,"attachment","scroll"),"background-size":oe(t,"size","auto auto"),"background-position":oe(t,"position","0% 0%")}};function oe(e,t,n,o=!1){const i=(0,r.isExperimentActive)("e_v_3_30");if(0===e.filter((e=>e?.[t])).length)return i?n:null;const l=e.map((e=>e[t]??n));return!o&&l.every((e=>e===l[0]))?l[0]:l.join(",")}var ie=e=>{const{color:t=null,"background-overlay":n=null}=e;return I({...n,"background-color":t})},le=e=>`${e?.color} ${e?.offset??0}%`,se=e=>t=>t.filter(Boolean).join(e),ue=(e,t)=>(n,{key:r})=>{const o=e.filter((e=>n[e])).map((e=>[t({propKey:r,key:e}),n[e]]));return I(Object.fromEntries(o))},ce=e=>e?.length<1?null:e.map(ae).join(" "),ae=e=>"radius"in e?`blur(${e.radius})`:"amount"in e?`brightness(${e.amount})`:"",fe=({x:e,y:t})=>`${e??"0px"} ${t??"0px"}`,de=e=>[e.hOffset,e.vOffset,e.blur,e.spread,e.color,e.position].filter(Boolean).join(" "),me=e=>"custom"===e.unit?e.size:`${e.size}${e.unit}`,pe=e=>{const t={"-webkit-text-stroke":`${e.width} ${e.color}`,stroke:`${e.color}`,"stroke-width":`${e.width}`};return I(t)},ge=e=>`translate3d(${e.x}, ${e.y}, ${e.z})`,ve=e=>e?.length<1?null:e.join(" ");function he(e){return["a","article","aside","button","div","footer","h1","h2","h3","h4","h5","h6","header","main","nav","p","section","span"].includes(e)?e:"div"}function ye(e){const t=["http:","https:","mailto:","tel:"];try{const n=new URL(e);return t.includes(n.protocol)?e:""}catch{return""}}function be(){const e=window;return class extends e.elementor.modules.elements.views.Widget{onRender(...e){super.onRender(...e),this.#e("elementor/preview/atomic-widget/render"),this.#t("elementor/element/render")}onDestroy(...e){super.onDestroy(...e),this.#e("elementor/preview/atomic-widget/destroy"),this.#t("elementor/element/destroy")}attributes(){return{...super.attributes(),"data-atomic":"",style:"display: contents !important;"}}behaviors(){const e=["InlineEditing","Draggable","Resizable"],t=Object.entries(super.behaviors()).filter((([t])=>!e.includes(t)));return Object.fromEntries(t)}getDomElement(){return this.$el.find(":first-child")}getHandlesOverlay(){return null}#e(e){window.top?.dispatchEvent(new CustomEvent(e,{detail:{id:this.model.get("id")}}))}#t(t){e.elementor?.$preview?.[0]?.contentWindow.dispatchEvent(new CustomEvent(t,{detail:{id:this.model.get("id"),type:this.model.get("widgetType"),element:this.getDomElement().get(0)}}))}getContextMenuGroups(){return super.getContextMenuGroups().filter((e=>"save"!==e.name))}}}function we(){(0,r.__privateListenTo)((0,r.v1ReadyEvent)(),(()=>{const e=(0,n.getWidgetsCache)()??{},t=window,r=function(){const e=(0,v.createArrayLoader)({}),t=(0,v.createEnvironment)(e);return t.registerEscapingStrategy(he,"html_tag"),t.registerEscapingStrategy(ye,"full_url"),{register:e.setTemplate,render:t.render}}();Object.entries(e).forEach((([e,n])=>{if(!n.atomic)return;const o=function(e){return!!(e.atomic_props_schema&&e.twig_templates&&e.twig_main_template&&e.base_styles_dictionary)}(n)?function({type:e,renderer:t,element:n}){const r=window;Object.entries(n.twig_templates).forEach((([e,n])=>{t.register(e,n)}));const o=j({transformers:q,schema:n.atomic_props_schema});return class extends r.elementor.modules.elements.types.Widget{getType(){return e}getView(){return function({type:e,renderer:t,propsResolver:n,templateKey:r,baseStylesDictionary:o}){const i=be();return class extends i{#n=null;getTemplateType(){return"twig"}renderOnChange(){this.render()}async _renderTemplate(){this.#r(),this.#n?.abort(),this.#n=new AbortController;const i=A(this.#n.signal).then(((e,t)=>{const r=this.model.get("settings").toJSON();return n({props:r,signal:t})})).then((n=>{const i={id:this.model.get("id"),type:e,settings:n,base_styles:o};return t.render(r,i)})).then((e=>this.$el.html(e)));await i.execute(),this.#o()}#r(){this.triggerMethod("before:render:template")}#o(){this.bindUIElements(),this.triggerMethod("render:template")}}}({type:e,renderer:t,propsResolver:o,baseStylesDictionary:n.base_styles_dictionary,templateKey:n.twig_main_template})}}}({type:e,renderer:r,element:n}):function(e){const t=window;return class extends t.elementor.modules.elements.types.Widget{getType(){return e}getView(){return be()}}}(e);t.elementor.elementsManager.registerElementType(new o)}))}))}var Ee={href:"https://go.elementor.com/element-link-inside-link-infotip",target:"_blank",color:"inherit",variant:"text",sx:{marginInlineStart:"20px"},children:"Learn more"};function xe(e){const{containers:t=[e.container],storageType:n}=e,r=t;if("localstorage"!==n)return!1;const o=window?.elementorCommon?.storage?.get();if(!o?.clipboard?.elements)return!1;const i=o.clipboard.elements,l={type:"default",message:(0,y.__)("To paste a link to this element, first remove the link from it's parent container.","elementor"),id:"paste-in-link-blocked",additionalActionProps:[Ee]},s=ke(i,r);return s&&(0,h.notify)(l),s}function Re(e){const{containers:t=[e.container],target:n}=e,r=t,o=n,i={type:"default",message:(0,y.__)("To drag a link to this element, first remove the link from it's parent container.","elementor"),id:"move-in-link-blocked",additionalActionProps:[Ee]},l=ke(r,[o]);return l&&(0,h.notify)(i),l}function ke(e,t){return!(!e?.length||!t?.length)&&(!!e.some((e=>!!e?.id&&((0,n.isElementAnchored)(e.id)||!!(0,n.getAnchoredDescendantId)(e.id))))&&t.some((e=>!!e?.id&&((0,n.isElementAnchored)(e.id)||!!(0,n.getAnchoredAncestorId)(e.id)))))}function Se(e){const{containers:t=[e.container]}=e;return t.some(Te)}function Te(e){return!!e&&Boolean(Ce(e))}function Oe(e){const t=Ce(e);if(!t)return null;const[n]=Object.entries(t).find((([,e])=>"plain"===e.kind&&e.key===d.CLASSES_PROP_KEY))??[];return n??null}function Ce(e){const t=e?.model.get("widgetType")||e?.model.get("elType"),r=(0,n.getWidgetsCache)(),o=r?.[t];return o?.atomic_props_schema??null}function Pe(e){return e.length>1?(0,y.__)("Elements","elementor"):(0,n.getElementLabel)(e[0].id)}var Ae=()=>(0,r.undoable)({do:({containers:e,newStyle:t})=>e.map((e=>{const r=e.id,o=Oe(e);if(!o)return null;const i=(0,n.getElementStyles)(e.id),[l,s]=Object.entries(i??{})[0]??[],u=Object.keys(s??{}).length?s:null,c={styleId:l,originalStyle:u};if(l)t.variants.forEach((({meta:e,props:t})=>{(0,n.updateElementStyle)({elementId:r,styleId:l,meta:e,props:t})}));else{const[e]=t.variants,i=t.variants.slice(1);c.styleId=(0,n.createElementStyle)({elementId:r,classesProp:o,label:a.ELEMENTS_STYLES_RESERVED_LABEL,...e,additionalVariants:i})}return c})),undo:({containers:e},t)=>{e.forEach(((e,r)=>{const o=t[r];if(!o)return;if(!o.originalStyle)return void(0,n.deleteElementStyle)(e.id,o.styleId);const i=Oe(e);if(!i)return;const[l]=o.originalStyle.variants,s=o.originalStyle.variants.slice(1);(0,n.createElementStyle)({elementId:e.id,classesProp:i,label:a.ELEMENTS_STYLES_RESERVED_LABEL,styleId:o.styleId,...l,additionalVariants:s})}))}},{title:({containers:e})=>Pe(e),subtitle:(0,y.__)("Style Pasted","elementor")});var Le=()=>(0,r.undoable)({do:({containers:e})=>e.map((e=>{const t=e.model.get("id"),r=(0,n.getElementStyles)(t);return Object.keys(r??{}).forEach((e=>(0,n.deleteElementStyle)(t,e))),r})),undo:({containers:e},t)=>{e.forEach(((e,r)=>{const o=Oe(e);if(!o)return;const i=e.model.get("id"),l=t[r];Object.entries(l??{}).forEach((([e,t])=>{const[r]=t.variants,l=t.variants.slice(1);(0,n.createElementStyle)({elementId:i,classesProp:o,styleId:e,label:a.ELEMENTS_STYLES_RESERVED_LABEL,...r,additionalVariants:l})}))}))}},{title:({containers:e})=>Pe(e),subtitle:(0,y.__)("Style Reset","elementor")});function Ie(){N.register("size",me).register("shadow",de).register("stroke",pe).register("dimensions",ue(["block-start","block-end","inline-start","inline-end"],(({propKey:e,key:t})=>`${e}-${t}`))).register("filter",ce).register("box-shadow",se(",")).register("background",ie).register("background-overlay",re).register("background-color-overlay",Q).register("background-image-overlay",te).register("background-gradient-overlay",ee).register("gradient-color-stop",se(",")).register("color-stop",le).register("background-image-position-offset",fe).register("background-image-size-scale",ne).register("image-src",G).register("image",J).register("object-position",fe).register("transform-move",ge).register("transform",ve).register("layout-direction",ue(["row","column"],(({propKey:e,key:t})=>`${t}-${e}`))).register("border-width",ue(["block-start","block-end","inline-start","inline-end"],(({key:e})=>`border-${e}-width`))).register("border-radius",ue(["start-start","start-end","end-start","end-end"],(({key:e})=>`border-${e}-radius`))).registerFallback(Z),function(){const e=Ae();(0,r.blockCommand)({command:"document/elements/paste-style",condition:Se}),(0,r.__privateListenTo)((0,r.commandStartEvent)("document/elements/paste-style"),(t=>function(e,t){const{containers:n=[e.container],storageKey:r}=e,o=function(e="clipboard"){try{const t=JSON.parse(localStorage.getItem("elementor")??"{}");return t[e]?.elements}catch{return}}(r),[i]=o??[];if(!i)return;const l=i.styles,s=Object.values(l??{})[0];if(!s)return;const u=n.filter(Te);u.length&&t({containers:u,newStyle:s})}(t.args,e)))}(),function(){const e=Le();(0,r.blockCommand)({command:"document/elements/reset-style",condition:Se}),(0,r.__privateListenTo)((0,r.commandStartEvent)("document/elements/reset-style"),(t=>function(e,t){const{containers:n=[e.container]}=e,r=n.filter(Te);r.length&&t({containers:r})}(t.args,e)))}(),(0,r.blockCommand)({command:"document/elements/paste",condition:xe}),(0,r.blockCommand)({command:"document/elements/move",condition:Re}),we(),q.register("classes",function(){const e=new Map;return t=>t.map((t=>(e.has(t)||e.set(t,function(e){const t=a.stylesRepository.getProviders().find((t=>t.actions.all().find((t=>t.id===e))));return t?t.actions.resolveCssName(e):e}(t)),e.get(t)))).filter(Boolean)}()).register("link",X).register("image",J).register("image-src",G).registerFallback(Z),(0,e.injectIntoTop)({id:"elements-overlays",component:x}),(0,e.injectIntoTop)({id:"canvas-style-render",component:U})}}(),(window.elementorV2=window.elementorV2||{}).editorCanvas=i}(),window.elementorV2.editorCanvas?.init?.();