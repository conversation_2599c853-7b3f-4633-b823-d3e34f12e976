/**
 * Document Analizer Widget Styles
 * Clone completo del Document Viewer con stile dedicato e coerente
 * Basato sullo stile del Chat Model Widget per coerenza
 */

/* Container principale - stile simile a chat-widget */
.document-analizer-widget {
    background-color: #ffffff;
    border: 1px solid #e5e5e5;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin: 15px 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    overflow: hidden;
    display: flex;
    min-height: 500px;
}

.document-analizer-widget h3 {
    background-color: #f7f7f8;
    padding: 15px;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
}

.document-analizer-widget h3::before {
    content: "📊";
    margin-right: 8px;
    font-size: 18px;
}

/* Layout a 3 colonne */
.analizer-layout {
    display: flex;
    flex-direction: row;
    height: 100%;
    flex: 1;
}

/* Colonna statistiche (sinistra) - simile al mega menu del chat widget */
.analizer-stats-column {
    flex: 0 0 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    overflow-y: auto;
    border-right: 1px solid #e5e5e5;
}

.analizer-stats-column h3 {
    color: white;
    background: transparent;
    border: none;
    padding: 0 0 15px 0;
    font-size: 18px;
    font-weight: 700;
}

.analizer-stats-column h3::before {
    content: "📈";
    margin-right: 10px;
}

/* Colonna form (centro) */
.analizer-form-column {
    flex: 1;
    background: #fff;
    padding: 20px;
    overflow-y: auto;
    min-width: 350px;
}

/* Colonna display (destra) */
.analizer-display-column {
    flex: 2;
    background: #f9fafb;
    padding: 20px;
    overflow-y: auto;
    border-left: 1px solid #e5e5e5;
}

/* Colonna visualizzazione (destra) */
.analizer-display-column {
    flex: 1;
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid #e9ecef;
}

/* Stili per le statistiche (riutilizza i CSS del document-stats.css) */
.document-analizer-stats-container .stats-grid {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 10px;
    width: 100%;
}

.document-analizer-stats-container .stats-row {
    display: flex;
    gap: 8px;
    width: 100%;
    margin-bottom: 0;
}

.document-analizer-stats-container .stats-item {
    position: relative;
    flex: 1;
    background: #fff;
    border-radius: 6px;
    padding: 12px 10px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
    text-align: center;
    box-shadow: 0 1px 2px rgba(0,0,0,0.03);
}

.document-analizer-stats-container .stats-item:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0,0,0,0.05);
}

.document-analizer-stats-container .stats-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 8px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    letter-spacing: 0.3px;
    text-transform: uppercase;
}

.document-analizer-stats-container .stats-value {
    font-size: 1.4rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 8px 0 6px;
    line-height: 1.2;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.document-analizer-stats-container .cost-highlight {
    color: #e67e22;
}

.document-analizer-stats-container .credit-highlight {
    color: #27ae60;
    font-size: 20px;
}

/* Info icon e tooltip */
.document-analizer-stats-container .stats-info-icon {
    position: relative;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 600;
    margin-left: 6px;
    cursor: help;
    border: none;
    transition: all 0.2s ease;
    vertical-align: middle;
}

.document-analizer-stats-container .stats-info-icon:hover {
    background: #0073aa;
    color: #fff;
}

.document-analizer-stats-container .stats-tooltip {
    position: absolute;
    background: rgba(51, 51, 51, 0.95);
    color: #fff;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: normal;
    width: 180px;
    z-index: 9999;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    transform: translateX(-50%);
    bottom: 130%;
    left: 50%;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    pointer-events: none;
    text-align: center;
    max-width: calc(100vw - 40px);
    word-break: normal;
    word-wrap: break-word;
}

.document-analizer-stats-container .stats-tooltip:after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -4px;
    border: 4px solid transparent;
    border-top-color: rgba(51, 51, 51, 0.95);
}

.document-analizer-stats-container .stats-info-icon:hover .stats-tooltip {
    opacity: 1;
    visibility: visible;
}

/* Stili per il form */
.analizer-form-column h3 {
    color: #1a2533;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 700;
}

.analizer-form-column .form-row {
    margin-bottom: 15px;
}

.analizer-form-column label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #2c3e50;
}

.analizer-form-column input[type="text"],
.analizer-form-column input[type="file"],
.analizer-form-column textarea,
.analizer-form-column select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.analizer-form-column input[type="text"]:focus,
.analizer-form-column textarea:focus,
.analizer-form-column select:focus {
    border-color: #0073aa;
    outline: none;
    box-shadow: 0 0 5px rgba(0, 115, 170, 0.3);
}

.analizer-form-column button {
    background: #0073aa;
    color: #fff;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
    margin-right: 10px;
}

.analizer-form-column button:hover {
    background: #005f8d;
}

/* Stili per la visualizzazione */
.analizer-display-column h3 {
    color: #1a2533;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 700;
}

/* File upload styling */
.file-upload-container {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: border-color 0.3s;
}

.file-upload-container:hover {
    border-color: #0073aa;
}

.file-upload-input {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.file-upload-input input[type="file"] {
    display: none;
}

.file-upload-input label {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
}

.file-upload-input label:hover {
    background: #e9ecef;
}

/* Document info inline */
.document-info-inline {
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
    font-size: 0.9rem;
}

.document-info-inline span {
    display: inline-block;
    margin-right: 15px;
    color: #6c757d;
}

/* Preset queries */
.preset-queries-container {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.preset-queries-select {
    flex: 1;
}

.preset-info-tip {
    width: 20px;
    height: 20px;
    background: #e9ecef;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: help;
    font-size: 12px;
    color: #6c757d;
}

/* Export actions */
.export-actions {
    border-top: 1px solid #eee;
    padding-top: 15px;
    margin-top: 20px;
}

/* Notification area */
.document-notification-area {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 15px;
}

.document-notification-area.error {
    background: #f8d7da;
    border-color: #f5c6cb;
}

.document-notification-area.processing {
    background: #d1ecf1;
    border-color: #bee5eb;
}

/* Spinner animation */
.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
    vertical-align: middle;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Extraction success message */
.extraction-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
    color: #155724;
    text-align: center;
}

.extraction-success p {
    margin: 8px 0;
}

.extraction-success p:first-child {
    font-size: 1.1em;
    margin-bottom: 12px;
}

/* Analysis wait container */
.analysis-wait-container {
    text-align: center;
    padding: 40px 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    margin: 20px 0;
}

.analysis-wait-spinner {
    margin-bottom: 20px;
}

.analysis-wait-container h4 {
    color: #495057;
    margin: 15px 0 10px 0;
    font-size: 1.2em;
}

.analysis-wait-message {
    color: #6c757d;
    font-style: italic;
    margin: 10px 0;
}

/* Cost update animation */
.cost-updated {
    background-color: #d4edda !important;
    transition: background-color 0.3s ease;
    border-radius: 4px;
    padding: 2px 4px;
}

.cost-highlight {
    font-weight: bold;
    color: #28a745;
}

/* Zoom controls */
.zoom-controls {
    margin-bottom: 10px;
    text-align: center;
}

.zoom-btn {
    width: 40px;
    height: 40px;
    border: 1px solid #ddd;
    background: #fff;
    border-radius: 5px;
    cursor: pointer;
    margin: 0 5px;
    font-size: 18px;
    font-weight: bold;
}

.zoom-btn:hover {
    background: #f8f9fa;
}

/* Document display */
#analizer-document-display {
    border: 1px solid #ddd;
    border-radius: 5px;
    min-height: 400px;
}

#analizer-document-frame {
    width: 100%;
    height: 400px;
    border: none;
    border-radius: 5px;
}

/* Login required */
.document-analizer-login-required {
    text-align: center;
    padding: 40px 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.login-button {
    background: #0073aa;
    color: #fff;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    display: inline-block;
    margin-top: 10px;
}

.login-button:hover {
    background: #005f8d;
    color: #fff;
    text-decoration: none;
}

/* Responsive */
@media (max-width: 1200px) {
    .analizer-layout {
        flex-direction: column;
    }
    
    .analizer-stats-column {
        flex: none;
        order: 1;
    }
    
    .analizer-form-column {
        order: 2;
    }
    
    .analizer-display-column {
        order: 3;
    }
}

@media (max-width: 768px) {
    .document-analizer-widget {
        padding: 15px;
    }
    
    .analizer-layout {
        gap: 15px;
    }
    
    .analizer-stats-column,
    .analizer-form-column,
    .analizer-display-column {
        padding: 15px;
    }
    
    .file-upload-input {
        flex-direction: column;
    }
    
    .document-analizer-stats-container .stats-tooltip {
        width: 160px;
        max-width: calc(100vw - 20px);
    }
}
