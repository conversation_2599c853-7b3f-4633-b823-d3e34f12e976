# Translation of Blocksy Companion Pro in Croatian
# This file is distributed under the same license as the Blocksy Companion Pro package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-04-07 10:15:45+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: hr\n"
"Project-Id-Version: Blocksy Companion Pro\n"

#: static/js/dashboard/helpers/useUpsellModal.js:88
msgid "Business or Agency"
msgstr "Firma ili Agencija"

#: static/js/options/ConditionsManager/SingleCondition.js:482
msgid "Select value"
msgstr "Odaberi vrijednost"

#: framework/helpers/exts-configs.php:385
msgid "Product Waitlist"
msgstr "Lista čekanja proizvoda"

#: framework/helpers/exts-configs.php:386
msgid "Allow your customers to sign up for a waitlist for products that are out of stock and get notified when they are back in stock."
msgstr "Omogućite kupcima da se prijave na listu čekanja za proizvode koji su trenutno rasprodani i dobiju obavijest kada budu ponovno dostupni."

#: framework/features/blocks/share-box/options.php:103
msgid "Clipboard"
msgstr "Međuspremnik"

#: framework/premium/extensions/shortcuts/customizer.php:609,
#: framework/premium/extensions/shortcuts/customizer.php:635,
#: framework/premium/extensions/shortcuts/views/bar.php:53,
#: framework/features/header/items/account/views/login.php:554,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:4,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/feature.php:189,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/feature.php:193,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-account.php:67
msgid "Waitlist"
msgstr "Lista čekanja"

#: framework/premium/extensions/shortcuts/customizer.php:895
msgid "Tooltip Visibility"
msgstr "Vidljivost opisa"

#: framework/premium/extensions/shortcuts/customizer.php:1351
msgid "Container Backdrop Blur"
msgstr "Zamućenje pozadine kontejnera"

#: framework/premium/features/content-blocks/hooks-manager.php:957
msgid "Added to Cart: Before product"
msgstr "Dodano u košaricu: Prije proizvoda"

#: framework/premium/features/content-blocks/hooks-manager.php:961
msgid "Added to Cart: Before actions"
msgstr "Dodano u košaricu: Prije radnji"

#: framework/premium/features/content-blocks/hooks-manager.php:965
msgid "Added to Cart: Before suggested products"
msgstr "Dodano u košaricu: Prije predloženih proizvoda"

#: framework/premium/features/content-blocks/hooks-manager.php:969
msgid "Added to Cart: After suggested products"
msgstr "Dodano u košaricu: Nakon predloženih proizvoda"

#: framework/premium/features/content-blocks/hooks-manager.php:971
msgid "WooCommerce: Added to Cart"
msgstr "WooCommerce: Dodano u košaricu"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:294
msgid "Upsell Products"
msgstr "Proizvodi za upsell"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:295
msgid "Cross-sell Products"
msgstr "Proizvodi za cross-sell"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:506
msgid "Auto Close Panel"
msgstr "Automatsko zatvaranje panela"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:507
msgid "Automatically close the panel when a filter option is selected."
msgstr "Automatski zatvori panel kada se odabere opcija filtera."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:15
msgid "Form Type"
msgstr "Vrsta obrasca"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:29
msgid "Form Max Width"
msgstr "Maksimalna širina obrasca"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:41
msgid "Enable For Backorders"
msgstr "Omogući za proizvode na prednarudžbi"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:42
msgid "Allow users to join the waitlist even if the product is on backorder."
msgstr "Dozvoli korisnicima da se pridruže listi čekanja čak i ako je proizvod dostupan putem prednarudžbe."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:55
msgid "Show Users Count"
msgstr "Prikaži broj korisnika"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:56
msgid "Display a counter that reflects the current number of users on the waitlist."
msgstr "Prikaz broja korisnika koji su trenutno na listi čekanja."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:69
msgid "Logged In Users Only"
msgstr "Samo prijavljeni korisnici"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:70
msgid "Display the waitlist feature exclusively to users who are logged in."
msgstr "Omogućite funkciju liste čekanja samo prijavljenim korisnicima."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:83
msgid "Subscription Confirmation"
msgstr "Potvrda pretplate"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:84
msgid "Specify which users should verify their waitlist subscription through email confirmation."
msgstr "Odredite koji korisnici trebaju potvrditi svoju pretplatu na listu čekanja putem e-maila."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:107
msgid "Waitlist Form Display Conditions"
msgstr "Uvjeti prikaza obrasca liste čekanja"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:108
msgid "Choose where you want this Waitlist Form to be displayed."
msgstr "Odaberite gdje želite prikazati ovaj obrazac za listu čekanja."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:160
msgid "Message Font"
msgstr "Font poruke"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:168
msgid "Message Color"
msgstr "Boja poruke"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:225
msgid "Container Padding"
msgstr "Unutarnji razmak kontejnera"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/table.php:40,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:46
msgid "Actions"
msgstr "Radnje"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:96,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:200,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:221,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:282
msgid "Invalid request"
msgstr "Nevažeći zahtjev"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:106
msgid "No waitlist found"
msgstr "Lista čekanja nije pronađena"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:127,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:128,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:162
msgid "Waitlists"
msgstr "Liste čekanja"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:153
msgid "Number of items per page"
msgstr "Broj stavki po stranici"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:171
msgid "Waitlist for %s"
msgstr "Lista čekanja za %s"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:213
msgid "Invalid email"
msgstr "Nevažeća e-mail adresa"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:229
msgid "You are already on the waitlist"
msgstr "Već ste na listi čekanja"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:267
msgid "You have been added to the waitlist"
msgstr "Dodani ste na listu čekanja"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:291
msgid "You have been removed from the waitlist"
msgstr "Uklonjeni ste s liste čekanja"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-layer.php:33
msgid "%s %s joined the waitlist for this item."
msgstr "%s %s se pridružio listi čekanja za ovaj artikl."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-layer.php:243
msgid "Waitlist Form"
msgstr "Obrazac liste čekanja"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-mailer.php:78
msgid "Your waitlist subscription has been successfully canceled."
msgstr "Vaša pretplata na listu čekanja je uspješno otkazana."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-mailer.php:117
msgid "Your waitlist subscription has been successfully confirmed."
msgstr "Vaša pretplata na listu čekanja je uspješno potvrđena."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-table.php:11
msgid "Search Products"
msgstr "Pretraži proizvode"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:14
msgid "Export Subscribers"
msgstr "Izvoz pretplatnika"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:63
msgid "Guest"
msgstr "Gost"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:75
msgid "Edit this customer"
msgstr "Uredi ovog kupca"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:76
msgid "Edit"
msgstr "Uredi"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:115,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:182
msgid "Delete"
msgstr "Obriši"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:123
msgid "View"
msgstr "Pregledaj"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:153
msgid "%s ago"
msgstr "Prije %s"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:169
msgid "Is registered"
msgstr "Registriran"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:170
msgid "Date created"
msgstr "Datum kreiranja"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:8
msgid "Waitlist - Back in Stock Notification"
msgstr "Lista čekanja - Obavijest o ponovnom dolasku proizvoda na zalihu"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:9
msgid "This email is sent when a product is back in stock"
msgstr "Ovaj e-mail se šalje kada proizvod ponovno postane dostupan"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:10
msgid "A product you are waiting for is back in stock"
msgstr "Proizvod koji ste čekali je ponovno dostupan na zalihi"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:11
msgid "Good news! The product you have been waiting for is now back in stock!"
msgstr "Dobre vijesti! Proizvod koji ste čekali sada je ponovno dostupan!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:8
msgid "Waitlist - Confirm Subscription"
msgstr "Lista čekanja - Potvrda pretplate"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:9
msgid "This email is sent when a user subscribes to a product stock alert and should confirm their subscription"
msgstr "Ovaj e-mail se šalje kada se korisnik prijavi na obavijest o zalihama i treba potvrditi pretplatu"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:10
msgid "Confirm waitlist subscription"
msgstr "Potvrdi pretplatu na listu čekanja"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:11
msgid "Get notified when {product_title} is back in stock"
msgstr "Primajte obavijesti kada {product_title} ponovno bude dostupan na zalihi"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:8
msgid "Waitlist - Subscription Confirmed"
msgstr "Lista čekanja - Pretplata potvrđena"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:9
msgid "This email is sent after a user confirmed the subscription to a product stock alert"
msgstr "Ovaj e-mail se šalje nakon što korisnik potvrdi pretplatu na obavijest o zalihama proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:10
msgid "Waitlist subscription confirmed"
msgstr "Pretplata na listu čekanja potvrđena"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:11
msgid "You will be notified when {product_title} is back in stock"
msgstr "Obavijestit ćemo vas kada {product_title} ponovno bude dostupan na zalihi"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/waitlist-email.php:35,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/waitlist-email.php:139
msgid "Customer"
msgstr "Kupac"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:27
msgid "Enter your email"
msgstr "Unesite svoju e-mail adresu"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:40
msgid "Join Waitlist"
msgstr "Pridruži se listi čekanja"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:53
msgid "This product is currently sold out!"
msgstr "Ovaj proizvod je trenutno rasprodan!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:61
msgid "No worries! Please enter your e-mail address and we will promptly notify you as soon as the item is back in stock."
msgstr "Bez brige! Unesite svoju e-mail adresu i odmah ćemo vas obavijestiti čim artikl ponovno bude dostupan."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:69
msgid "Great! You have been added to the waitlist for this product. Please check your inbox and confirm the subscription to this waitlist."
msgstr "Odlično! Dodani ste na listu čekanja za ovaj proizvod. Provjerite svoj inbox i potvrdite pretplatu na ovu listu čekanja."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:97
msgid "Great! You have been added to the waitlist for this product. You will receive an email as soon as the item is back in stock."
msgstr "Odlično! Dodani ste na listu čekanja za ovaj proizvod. Dobit ćete e-mail čim artikl ponovno bude dostupan."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:151,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:86
msgid "Unsubscribe"
msgstr "Odjava"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:65
msgid "Yes"
msgstr "Da"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:65
msgid "No"
msgstr "Ne"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:21
msgid "You don't have any products in your waitlist yet."
msgstr "Još nemate proizvoda na svojoj listi čekanja."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:42
msgid "Confirmed"
msgstr "Potvrđeno"

#. translators: %s User name.
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:19,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:17,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:17,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:12,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:10,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-subscription-confirmed.php:10
msgid "Hi, %s!"
msgstr "Bok, %s!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:31,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:19
msgid "Great news! The %s from your waitlist is now back in stock!"
msgstr "Sjajne vijesti! %s s vaše liste čekanja sada je ponovno dostupan na zalihi!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:42,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:24
msgid "Click the link below to secure your purchase before it is gone!"
msgstr "Kliknite na poveznicu ispod kako biste osigurali kupnju prije nego što nestane!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:26,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:15
msgid "You have requested to join the waitlist for this item:"
msgstr "Zatražili ste pridruživanje listi čekanja za ovaj artikl:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:62
msgid "Click the button below to confirm your subscription. Once confirmed, we will notify you when the item is back in stock."
msgstr "Kliknite na gumb ispod kako biste potvrdili svoju pretplatu. Nakon potvrde, obavijestit ćemo vas kada proizvod bude ponovno dostupan."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:66
msgid "Confirm Subscription"
msgstr "Potvrdi pretplatu"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:70,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:26
msgid "Please note, the confirmation period is 2 days."
msgstr "Napomena: razdoblje potvrde traje 2 dana."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:77,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:65
msgid "If you don't want to receive any further notifications, please %s"
msgstr "Ako ne želite primati daljnje obavijesti, molimo vas da se %s"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:78,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:66
msgid "unsubscribe"
msgstr "odjavite"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:26,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-subscription-confirmed.php:15
msgid "You have been successfully added to the waitlist for the following item:"
msgstr "Uspješno ste dodani na listu čekanja za sljedeći artikl:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:28,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:17
msgid "Product:"
msgstr "Proizvod:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:29,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:18
msgid "Price:"
msgstr "Cijena:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:30
msgid "Add to cart:"
msgstr "Dodaj u košaricu:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:19
msgid "Product link:"
msgstr "Poveznica na proizvod:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:23
msgid "Click the link below to confirm your subscription. Once confirmed, we will notify you when the item is back in stock."
msgstr "Kliknite na poveznicu ispod kako biste potvrdili svoju pretplatu. Nakon potvrde, obavijestit ćemo vas kada proizvod bude ponovno dostupan."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:32,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-subscription-confirmed.php:25
msgid "If you don't want to receive any further notifications, please unsubscribe by clicking on this link - %s"
msgstr "Ako ne želite primati daljnje obavijesti, odjavite se klikom na ovu poveznicu - %s"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:127
msgid "API URL"
msgstr "API URL"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:304
msgid "More information on how to generate an API key for ActiveCampaign can be found %shere%s."
msgstr "Više informacija o tome kako generirati API ključ za ActiveCampaign možete pronaći %shere%s"

#: static/js/dashboard/helpers/useUpsellModal.js:18
#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:111
msgid "Business"
msgstr "Posao"

#: framework/helpers/exts-configs.php:377,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:4
msgid "Added to Cart Popup"
msgstr "Skočni prozor dodanog u košaricu"

#: framework/helpers/exts-configs.php:378
msgid "Show a dynamic confirmation popup with product recommendations whenever items are added to the cart."
msgstr "Prikaži skočni prozor dinamičke potvrde s preporukama proizvoda svaki put kada se stavke dodaju u košaricu."

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:15
msgid "Trigger Popup On"
msgstr "Okini skočni prozor"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:26
msgid "Product Page"
msgstr "Stranica proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:91
msgid "Description Length"
msgstr "Dužina opisa"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:102
msgid "Cart Button"
msgstr "Gumb košarice"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:112
msgid "Checkout Button"
msgstr "Gumb za naplatu"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:121
msgid "Continue Shopping Button"
msgstr "Gumb nastavka kupovine"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:140
msgid "Shipping Info"
msgstr "Informacije o dostavi"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:149
msgid "Tax Info"
msgstr "Informacije o porezu"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:158
msgid "Total Info"
msgstr "Informacije o ukupnom iznosu"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:264
msgid "Suggested Products"
msgstr "Predloženi proizvodi"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:292,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/suggested-products.php:51
msgid "Related Products"
msgstr "Povezani proizvodi"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:293
msgid "Recently Viewed Products"
msgstr "Nedavno pregledani proizvodi"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:368
msgid "Products Card Type"
msgstr "Vrsta kartica proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:382
msgid "Products Visibility"
msgstr ""

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:516
msgid "Popup Options"
msgstr ""

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:736,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:752,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:315,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:96
msgid "Popup Backdrop"
msgstr ""

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:768
msgid "Close Icon Size"
msgstr ""

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/view.php:95
msgid "Product succesfully added to your cart!"
msgstr "Proizvod uspješno dodan u košaricu!"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/view.php:102
msgid "Close Modal"
msgstr ""

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:331,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:124
msgid "Popup Shadow"
msgstr ""

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:350,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:143
msgid "Popup Border Radius"
msgstr ""

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/added-product.php:227
msgid "Shipping Cost"
msgstr "Troškovi dostave"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/added-product.php:236
msgid "Tax Amount"
msgstr ""

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/added-product.php:245
msgid "Cart Total"
msgstr "Košarica ukupno"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/product-actions.php:24
msgid "View Cart"
msgstr "Pogledaj košaricu"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/product-actions.php:34
msgid "Checkout"
msgstr "Naplata"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/product-actions.php:44
msgid "Continue Shopping"
msgstr "Nastavi s kupovinom"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/suggested-products.php:53
msgid "Recently Viewed"
msgstr "Nedavno pregledano"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:340
msgid "More information on how to create a list in MailPoet can be found %shere%s."
msgstr "Više informacija o tome kako kreirati listu u MailPoet možete pronaći %shere%s."

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:9
msgid "Color Mode"
msgstr "Način boja"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:17
msgid "One Color"
msgstr "Jedna boja"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:18
msgid "Dual Color"
msgstr "Dvostruka boja"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:28
msgid "Colors"
msgstr "Boje"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:50
msgid "Color 1"
msgstr "Boja 1"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:58
msgid "Color 2"
msgstr "Boja 2"

#: framework/features/blocks/share-box/options.php:110,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:88
msgid "Tooltip"
msgstr "Opis alata"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:108
msgid "Tooltip Text"
msgstr "Tekst opisa alata"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:111
msgid "{term_name}"
msgstr "{term_name}"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:122
msgid "Tooltip Image"
msgstr "Slika opisa alata"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:135
msgid "Subtype"
msgstr "Podvrsta"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:174
msgid "here"
msgstr "ovdje"

#: framework/premium/features/premium-header/items/contacts/options.php:21
msgid "Item Visibility"
msgstr "Vidljivost stavke"

#: framework/premium/features/premium-header/items/language-switcher/options.php:36
msgid "Hide Missing Language"
msgstr "Sakrij nedostajući jezik"

#: framework/premium/extensions/woocommerce-extra/features/filters/filter-types/taxonomies.php:52
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Preview.js:94
msgid "Please wait until the lookup table is generated."
msgstr "Molimo pričekajte dok se tabela pretrage generira."

#: framework/premium/extensions/woocommerce-extra/features/filters/filter-types/taxonomies.php:472
msgid "Collapse"
msgstr "Sažmi"

#: framework/premium/extensions/woocommerce-extra/features/filters/filter-types/taxonomies.php:473
msgid "Expand"
msgstr "Proširi"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/filter-presenter.php:64
msgid "category"
msgstr "kategorija"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/filter-presenter.php:108
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Preview.js:32
msgid "Find by %s"
msgstr "Pronađi po %s"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:434
msgid "Regenerate the product taxonomies lookup table"
msgstr "Ponovno generiraj tabelu pretrage taksonomija proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:435
msgid "This tool will regenerate the product taxonomies lookup table data from existing product(s) data. This process may take a while."
msgstr "Ovaj alat će regenerirati podatke tabele pretrage taksonomija proizvoda na temelju postojećih podataka o proizvodima. Ovaj proces može potrajati."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:446
msgid "Product taxonomies lookup table data is regenerating"
msgstr "Podaci tabele pretrage taksonomija proizvoda se regeneriraju"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:459
msgid "Regenerate"
msgstr "Regeneriraj"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:453
msgid "Filling in progress (%d)"
msgstr "Popunjavanje u tijeku (%d)"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:494
msgid "Resume the product taxonomies lookup table regeneration"
msgstr "Nastavi regeneraciju tabele pretrage taksonomija proizvoda"

#. translators: %1$s = count of products already processed.
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:497
msgid "This tool will resume the product taxonomies lookup table regeneration at the point in which it was aborted (%1$s products were already processed)."
msgstr "Ovaj alat će nastaviti regeneraciju tabele pretrage taksonomija proizvoda od točke na kojoj je prekinuta (%1$s proizvoda je već obrađeno)."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:516
msgid "Product taxonomies lookup table regeneration process has been resumed."
msgstr "Proces regeneracije tabele pretrage taksonomija proizvoda je nastavljen."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:518
msgid "Resume"
msgstr "Nastavi"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:466
msgid "Abort the product taxonomies lookup table regeneration"
msgstr "Prekini regeneraciju tabele pretrage taksonomija proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:486
msgid "Product taxonomies lookup table regeneration process has been aborted."
msgstr "Proces regeneracije tabele pretrage taksonomija proizvoda je prekinut."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:488
msgid "Abort"
msgstr "Prekini"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:158
msgid "Container Border Color"
msgstr "Boja obruba kontejnera"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:189
msgid "Container Background Color"
msgstr "Boja pozadine kontejnera"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:143
msgid "API Version"
msgstr "API verzija"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:268
msgid "More information on how to generate an API key for Brevo can be found %shere%s."
msgstr "Više informacija o tome kako generirati API ključ za Brevo možete pronaći %shere%s."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:834
msgid "Hide Documentation Links"
msgstr "Sakrij poveznice na dokumentaciju"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:842
msgid "Hide Video Links"
msgstr "Sakrij poveznice na videozapise"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:104
msgid "Display the currently active filters."
msgstr "Prikaži trenutno aktivne filtere."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:47
msgid "Category 1"
msgstr "Kategorija 1"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:53
msgid "Category 2"
msgstr "Kategorija 2"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:66
msgid "Attribute 1"
msgstr "Atribut 1"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:72
msgid "Attribute 2"
msgstr "Atribut 2"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:12
msgid "Date"
msgstr "Datum"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:10
msgid "Filter by Price Controls"
msgstr "Filter po kontroli cijena"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:106
msgid "Filter by Price"
msgstr "Filtriraj po cijeni"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:11
msgid "Widget for filtering the WooCommerce products by price."
msgstr "Widget za filtriranje WooCommerce proizvoda po cijeni."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:58
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:473
msgid "Show Tooltip"
msgstr "Prikaži opis alata"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:70
msgid "Show Prices"
msgstr "Prikaži cijene"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Disabled.js:6
msgid "Please select a valid taxonomy."
msgstr "Molimo odaberite važeću taksonomiju."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:174
msgid "Filter Settings"
msgstr "Postavke filtera"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:253
msgid "Show Search Box"
msgstr "Prikaži pretraživačko polje"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:496
msgid "Container Maximum Height"
msgstr "Maksimalna visina kontejnera"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:55
msgid "Exclude Speciffic Items"
msgstr "Isključi određene stavke"

#: static/js/dashboard/VersionMismatch.js:62
#: static/js/notifications/VersionMismatchNotice.js:74
msgid "Update Blocksy Theme Now"
msgstr "Ažuriraj Blocksy temu sada"

#: static/js/dashboard/screens/DemoInstall.js:183
msgid "Your site is misconfigured and AJAX requests are not reaching your backend. Please click %shere%s to find the common causes and possible solutions to this.<br> Error code - %s"
msgstr "Vaša stranica je pogrešno konfigurirana i AJAX zahtjevi ne stižu do backend-a. Kliknite %shere%s kako biste pronašli najčešće uzroke i moguća rješenja.<br> Kod greške - %s"

#: static/js/dashboard/screens/DemoInstall.js:201
msgid "Failed to retrieve starter sites list.<br> Error code - %s"
msgstr "Neuspjelo dohvaćanje liste početnih stranica.<br> Kod greške - %s"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:129
msgid "Installing %s"
msgstr "Instaliranje %s"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:345
msgid "Preparing data..."
msgstr "Priprema podataka..."

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:89
msgid "Required plan"
msgstr "Potreban plan"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:13
msgid "All Plans"
msgstr "Svi planovi"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:15
msgid "Pro"
msgstr "Pro"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:182
msgid "All Builders"
msgstr "Svi graditelji"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:206
msgid "Search for a starter site..."
msgstr "Pretraži početnu stranicu..."

#: static/js/dashboard/screens/SiteExport.js:176
msgid "Export Site"
msgstr "Izvoz stranice"

#: static/js/dashboard/screens/SiteExport.js:191
msgid "Starter site"
msgstr "Početna stranica"

#: static/js/dashboard/screens/SiteExport.js:203
msgid "Select a starter site"
msgstr "Odaberi početnu stranicu"

#: static/js/editor/blocks/about-me/index.js:15
msgid "About Me Controls"
msgstr "Postavke O meni"

#: static/js/editor/blocks/about-me/index.js:43
msgid "Showcase your personal information across your website."
msgstr "Prikažite svoje osobne informacije na cijeloj web stranici."

#: static/js/editor/blocks/breadcrumbs/Preview.js:63
msgid "Subpage"
msgstr "Podstranica"

#: static/js/editor/blocks/breadcrumbs/index.js:11
msgid "Breadcrumbs"
msgstr "Navigacijske staze"

#: static/js/editor/blocks/breadcrumbs/index.js:12
msgid "Display navigational links, showing users their path within the site."
msgstr "Prikaz navigacijskih poveznica, pokazujući korisnicima njihov put unutar stranice."

#: static/js/editor/blocks/contact-info/index.js:15
msgid "Contact Info Controls"
msgstr "Postavke kontakt informacija"

#: static/js/editor/blocks/contact-info/index.js:52
msgid "Display essential contact details to your visitors."
msgstr "Prikažite osnovne kontakt informacije svojim posjetiteljima."

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:102
msgid "9:16"
msgstr "09:16:00"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:106
msgid "3:4"
msgstr "3:4"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:110
msgid "2:3"
msgstr "2:3"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:133
msgid "Width"
msgstr "Širina"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:157
msgid "Height"
msgstr "Visina"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:178
msgid "Scale"
msgstr "Razmjer"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:209
msgid "Resolution"
msgstr "Rezolucija"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:215
msgid "Select the size of the source image."
msgstr "Odaberite veličinu izvorne slike."

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:57
msgid "Image Settings"
msgstr "Postavke slike"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:77
msgid "Aspect Ratio"
msgstr "Omjer slike"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:82
msgid "Original"
msgstr "Izvorno"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:90
msgid "16:9"
msgstr "16:9"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:94
msgid "4:3"
msgstr "4:3"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:98
msgid "3:2"
msgstr "3:2"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:100
msgid "Icon/Logo"
msgstr "Ikona/Logo"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:138
msgid "Expand on click"
msgstr "Proširi na klik"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:151
msgid "Video thumbnail"
msgstr "Sličica videa"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:161
msgid "Image Hover Effect"
msgstr "Efekt lebdenja slike"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:174
msgid "Zoom In"
msgstr "Zumiranje unutra"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:178
msgid "Zoom Out"
msgstr "Zumiranje van"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:202
msgid "Alternative Text"
msgstr "Alternativni tekst"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:215
msgid "Describe the purpose of the image."
msgstr "Opišite svrhu slike."

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:221
msgid "Leave empty if decorative."
msgstr "Ostavite prazno ako je dekorativno."

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:238
msgid "Image size"
msgstr "Veličina slike"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:271
msgid "Logo Gap"
msgstr "Razmak logotipa"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:323
msgid "Custom field fallback"
msgstr "Zamjensko polje po prilagodbi"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:343
msgid "Term additional class"
msgstr "Dodatna klasa termina"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:350
msgid "Additional class for term items. Useful for styling."
msgstr "Dodatna klasa za stavke termina. Korisno za stiliziranje."

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:45
msgid "Content Source"
msgstr "Izvor sadržaja"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:51
msgid "Search for field"
msgstr "Pretraži polje"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:87
msgid "Image Source"
msgstr "Izvor slike"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:14
msgid "Change heading level"
msgstr "Promijeni razinu naslova"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:38
msgid "Heading 1"
msgstr "Naslov 1"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:39
msgid "Heading 2"
msgstr "Naslov 2"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:40
msgid "Heading 3"
msgstr "Naslov 3"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:41
msgid "Heading 4"
msgstr "Naslov 4"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:42
msgid "Heading 5"
msgstr "Naslov 5"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:43
msgid "Heading 6"
msgstr "Naslov 6"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:44
msgid "Paragraph"
msgstr "Paragraf"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:45
msgid "Span"
msgstr "Span"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:46
msgid "Div"
msgstr "Div"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:101
msgid "Archive Image"
msgstr "Slika arhive"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:38
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:136
msgid "Stock Status"
msgstr "Status zaliha"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:24
#: static/js/editor/blocks/dynamic-data/preview-parts/wp/TermTextPreview.js:8
msgid "Term Title"
msgstr "Naslov termina"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:28
#: static/js/editor/blocks/dynamic-data/preview-parts/wp/TermTextPreview.js:9
msgid "Term Description"
msgstr "Opis termina"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:32
msgid "Term Image"
msgstr "Slika termina"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:36
#: static/js/editor/blocks/dynamic-data/preview-parts/wp/TermTextPreview.js:10
msgid "Term Count"
msgstr "Broj termina"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:50
msgid "Excerpt"
msgstr "Isječak"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:55
msgid "Post Date"
msgstr "Datum objave"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:65
msgid "Terms"
msgstr "Termini"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:91
#: static/js/editor/blocks/dynamic-data/preview-parts/WpFieldPreview.js:46
msgid "Archive Title"
msgstr "Naslov arhive"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:96
#: static/js/editor/blocks/dynamic-data/preview-parts/WpFieldPreview.js:50
msgid "Archive Description"
msgstr "Opis arhive"

#: static/js/editor/blocks/dynamic-data/index.js:17
msgid "Fetch and display content from various sources."
msgstr "Dohvati i prikaži sadržaj iz različitih izvora."

#: static/js/editor/blocks/dynamic-data/index.js:33
msgid "Dynamic Title"
msgstr "Dinamički naslov"

#: static/js/editor/blocks/dynamic-data/index.js:37
msgid "Dynamic Excerpt"
msgstr "Dinamički isječak"

#: static/js/editor/blocks/dynamic-data/index.js:41
msgid "Dynamic Post Date"
msgstr "Dinamički datum objave"

#: static/js/editor/blocks/dynamic-data/index.js:45
msgid "Dynamic Comments"
msgstr "Dinamički komentari"

#: static/js/editor/blocks/dynamic-data/index.js:49
msgid "Dynamic Terms"
msgstr "Dinamički termini"

#: static/js/editor/blocks/dynamic-data/index.js:53
msgid "Dynamic Author"
msgstr "Dinamički autor"

#: static/js/editor/blocks/dynamic-data/index.js:57
msgid "Dynamic Featured Image"
msgstr "Dinamička istaknuta slika"

#: static/js/editor/blocks/dynamic-data/index.js:61
msgid "Dynamic Author Avatar"
msgstr "Dinamički avatar autora"

#: static/js/editor/blocks/dynamic-data/index.js:65
msgid "Dynamic Price"
msgstr "Dinamička cijena"

#: static/js/editor/blocks/dynamic-data/index.js:69
msgid "Dynamic Stock Status"
msgstr "Dinamički status zaliha"

#: static/js/editor/blocks/dynamic-data/index.js:73
msgid "Dynamic Brands"
msgstr "Dinamički brendovi"

#: static/js/editor/blocks/dynamic-data/index.js:77
msgid "Dynamic SKU"
msgstr "Dinamički SKU"

#: static/js/editor/blocks/dynamic-data/index.js:81
msgid "Dynamic Rating"
msgstr "Dinamička ocjena"

#: static/js/editor/blocks/dynamic-data/index.js:85
msgid "Dynamic Term Title"
msgstr "Dinamički naslov termina"

#: static/js/editor/blocks/dynamic-data/index.js:89
msgid "Dynamic Term Description"
msgstr "Dinamički opis termina"

#: static/js/editor/blocks/dynamic-data/index.js:93
msgid "Dynamic Term Count"
msgstr "Dinamički broj termina"

#: static/js/editor/blocks/dynamic-data/index.js:97
msgid "Dynamic Term Image"
msgstr "Dinamička slika termina"

#: static/js/editor/blocks/dynamic-data/preview-parts/woo/RatingPreview.js:13
msgid "Rated %s out of 5"
msgstr "Ocijenjeno %s od 5"

#: static/js/editor/blocks/dynamic-data/utils.js:15
msgid "Unknown"
msgstr "Nepoznato"

#: static/js/editor/blocks/post-template/Edit.js:170
#: static/js/editor/blocks/tax-template/Edit.js:152
msgid "List view"
msgstr "Prikaz liste"

#: static/js/editor/blocks/post-template/Edit.js:176
#: static/js/editor/blocks/tax-template/Edit.js:158
msgid "Grid view"
msgstr "Prikaz mreže"

#: static/js/editor/blocks/post-template/Edit.js:209
#: static/js/editor/blocks/tax-template/Edit.js:190
msgid "Tablet Columns"
msgstr "Stupci na tabletu"

#: static/js/editor/blocks/post-template/Edit.js:225
#: static/js/editor/blocks/tax-template/Edit.js:206
msgid "Mobile Columns"
msgstr "Stupci na mobilnom uređaju"

#: static/js/editor/blocks/post-template/index.js:13
msgid "Post Template"
msgstr "Predložak objave"

#: static/js/editor/blocks/query/Edit.js:120
#: static/js/editor/blocks/tax-query/Edit.js:123
msgid "Reset layout"
msgstr "Resetiraj izgled"

#: static/js/editor/blocks/query/Edit.js:186
msgid "Pagination"
msgstr "Paginacija"

#: static/js/editor/blocks/query/Edit.js:208
#: static/js/editor/blocks/tax-query/Edit.js:189
msgid "Block ID"
msgstr "ID bloka"

#: static/js/editor/blocks/query/Edit.js:214
#: static/js/editor/blocks/tax-query/Edit.js:195
msgid "Please look at the documentation for more information on why this is useful."
msgstr "Molimo pogledajte dokumentaciju za više informacija o tome zašto je ovo korisno."

#: static/js/editor/blocks/query/edit/PatternSelectionModal.js:82
#: static/js/editor/blocks/tax-query/edit/PatternSelectionModal.js:68
msgid "Choose a pattern"
msgstr "Odaberi uzorak"

#: static/js/editor/blocks/query/edit/PatternSelectionModal.js:91
#: static/js/editor/blocks/tax-query/edit/PatternSelectionModal.js:77
msgid "Search for patterns"
msgstr "Pretraži uzorke"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:133
msgid "Publish Date"
msgstr "Datum objave"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:173
msgid "Menu Order"
msgstr "Redoslijed izbornika"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:216
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:187
msgid "Order"
msgstr "Redoslijed"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:224
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:195
msgid "Descending"
msgstr "Silazno"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:232
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:203
msgid "Ascending"
msgstr "Uzlazno"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:275
msgid "Sticky Posts"
msgstr "Prikačene objave"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:299
msgid "Only"
msgstr "Samo"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:318
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:222
msgid "Parameters"
msgstr "Parametri"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:105
#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:91
msgid "Create Custom Layout"
msgstr "Kreiraj prilagođeni izgled"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:37
#: static/js/editor/blocks/query/index.js:12
msgid "Advanced Posts"
msgstr "Napredne objave"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:41
msgid "Inherit the Customizer layout, start with a pattern or create a custom layout"
msgstr "Naslijedi izgled iz Customizera, započni s uzorkom ili kreiraj prilagođeni izgled"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:56
msgid "Inherit From Customizer"
msgstr "Naslijedi iz Customizera"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:66
#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:57
msgid "Choose Pattern"
msgstr "Odaberi uzorak"

#: static/js/editor/blocks/query/edit/TaxonomyControls.js:167
msgid "Search for a term"
msgstr "Pretraži termin"

#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:47
msgid "Include %s"
msgstr "Uključi %s"

#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:75
msgid "Related"
msgstr "Povezano"

#: static/js/editor/blocks/query/index.js:13
#: static/js/editor/blocks/tax-query/index.js:13
msgid "Create advanced queries based on your specified criterias."
msgstr "Kreiraj napredne upite na temelju zadanih kriterija."

#: static/js/editor/blocks/search/Edit.js:163
msgid "Button Outside"
msgstr "Gumb izvan"

#: static/js/editor/blocks/search/Edit.js:180
msgid "Use button with text"
msgstr "Koristi gumb s tekstom"

#: static/js/editor/blocks/search/Edit.js:282
msgid "Button Icon Color"
msgstr "Boja ikone gumba"

#: static/js/editor/blocks/search/Edit.js:379
msgid "Dropdown Background Color"
msgstr "Boja pozadine padajućeg izbornika"

#: static/js/editor/blocks/search/Edit.js:400
msgid "Dropdown Shadow Color"
msgstr "Boja sjene padajućeg izbornika"

#: static/js/editor/blocks/search/Preview.js:24
msgid "Select category"
msgstr "Odaberi kategoriju"

#: static/js/editor/blocks/search/index.js:16
msgid "Advanced Search"
msgstr "Napredno pretraživanje"

#: static/js/editor/blocks/search/index.js:17
msgid "Quickly find specific content on your site."
msgstr "Brzo pronađite određeni sadržaj na svojoj web stranici."

#: static/js/editor/blocks/share-box/Edit.js:110
#: static/js/editor/blocks/socials/Edit.js:110
msgid "Icons Background Colors"
msgstr "Boje pozadine ikona"

#: static/js/editor/blocks/share-box/Edit.js:142
#: static/js/editor/blocks/socials/Edit.js:142
msgid "Icons Border Colors"
msgstr "Boje obruba ikona"

#: static/js/editor/blocks/share-box/index.js:15
msgid "Share Box Controls"
msgstr "Kontrole okvira za dijeljenje"

#: static/js/editor/blocks/share-box/index.js:45
msgid "Share content on social media, boosting visibility & engagement."
msgstr "Podijelite sadržaj na društvenim mrežama, povećavajući vidljivost i angažman."

#: static/js/editor/blocks/socials/index.js:15
msgid "Socials Controls"
msgstr "Kontrole društvenih mreža"

#: static/js/editor/blocks/socials/index.js:45
msgid "Display your social media profiles and boost the site engagement."
msgstr "Prikažite svoje profile na društvenim mrežama i povećajte angažman na web stranici."

#: static/js/editor/blocks/socials/index.js:47
#: static/js/editor/blocks/widgets-wrapper/index.js:58
msgid "Socials"
msgstr "Društvene mreže"

#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:120
msgid "ID"
msgstr "ID"

#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:136
msgid "Count"
msgstr "Broj"

#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:40
#: static/js/editor/blocks/tax-query/index.js:12
msgid "Advanced Taxonomies"
msgstr "Napredne taksonomije"

#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:44
msgid "Start with a pattern or create a custom layout"
msgstr "Započnite s uzorkom ili kreirajte prilagođeni izgled"

#: static/js/editor/blocks/tax-template/index.js:13
msgid "Taxonomy Template"
msgstr "Predložak taksonomije"

#: static/js/editor/blocks/widgets-wrapper/Edit.js:81
msgid "Expandable Container"
msgstr "Proširivi kontejner"

#: static/js/editor/blocks/widgets-wrapper/index.js:40
msgid "Widgets Wrapper"
msgstr "Omotač widgeta"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:41
msgid "Parameters options"
msgstr "Opcije parametara"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:46
msgid "All options are currently hidden"
msgstr "Sve opcije su trenutno skrivene"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:82
msgid "All options reset"
msgstr "Sve opcije resetirane"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:87
msgid "Reset all"
msgstr "Resetiraj sve"

#: static/js/options/ConditionsManager/ExpireCondition.js:119
msgid "The expiration date cannot be set earlier than the start date."
msgstr "Datum isteka ne može biti postavljen prije početnog datuma."

#: framework/features/demo-install.php:155,
#: framework/features/demo-install/content-installer.php:164,
#: framework/features/demo-install/content-installer.php:159,
#: framework/features/demo-install/demo-register.php:9
msgid "No demo name provided."
msgstr "Nije naveden naziv demonstracije."

#: framework/helpers/exts-configs.php:369,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:248,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:271,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:4
msgid "Stock Scarcity"
msgstr "Nedostatak zaliha"

#: framework/helpers/exts-configs.php:370
msgid "Show the remaining stock of a product to create a sense of urgency and encourage your visitors to make a purchase."
msgstr "Prikažite preostalu količinu proizvoda kako biste stvorili osjećaj hitnosti i potaknuli posjetitelje na kupnju."

#: framework/views/theme-mismatch.php:36
#: static/js/dashboard/VersionMismatch.js:19
#: static/js/notifications/VersionMismatchNotice.js:27
msgid "Action required - please update Blocksy theme to the latest version!"
msgstr "Potrebna akcija - ažurirajte Blocksy temu na najnoviju verziju!"

#: framework/views/theme-mismatch.php:41
#: static/js/dashboard/VersionMismatch.js:25
#: static/js/notifications/VersionMismatchNotice.js:35
msgid "We detected that you are using an outdated version of Blocksy theme."
msgstr "Otkrili smo da koristite zastarjelu verziju Blocksy teme."

#: framework/views/theme-mismatch.php:45
#: static/js/dashboard/VersionMismatch.js:32
#: static/js/notifications/VersionMismatchNotice.js:44
msgid "In order to take full advantage of all features the core has to offer - please install and activate the latest version of Blocksy theme."
msgstr "Da biste u potpunosti iskoristili sve značajke koje jezgra nudi, instalirajte i aktivirajte najnoviju verziju Blocksy teme."

#: framework/extensions/newsletter-subscribe/customizer.php:94,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:127
msgid "Make Name Field Required"
msgstr "Učini polje imena obaveznim"

#: framework/extensions/trending/customizer.php:121
msgid "Tag"
msgstr "Oznaka"

#: framework/extensions/trending/customizer.php:142
msgid "Taxonomy Source"
msgstr "Izvor taksonomije"

#: framework/extensions/trending/customizer.php:216
msgid "Module Title Icon Source"
msgstr "Izvor ikone naslova modula"

#: framework/extensions/trending/customizer.php:303
msgid "Products Status"
msgstr "Status proizvoda"

#: framework/extensions/trending/customizer.php:307
msgid "On Sale"
msgstr "Na sniženju"

#: framework/extensions/trending/customizer.php:308
msgid "Top Rated"
msgstr "Najbolje ocijenjeno"

#: framework/extensions/trending/customizer.php:309
msgid "Best Sellers"
msgstr "Najprodavaniji"

#: framework/extensions/trending/customizer.php:409
msgid "Show Product Price"
msgstr "Prikaži cijenu proizvoda"

#: framework/extensions/trending/customizer.php:423
msgid "Show Taxonomy"
msgstr "Prikaži taksonomiju"

#: framework/extensions/trending/customizer.php:441
msgid "Taxonomy Style"
msgstr "Stil taksonomije"

#: framework/extensions/trending/customizer.php:450
msgid "Underline"
msgstr "Podcrtano"

#: framework/extensions/trending/customizer.php:465,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:55
msgid "Image Width"
msgstr "Širina slike"

#: framework/extensions/trending/customizer.php:693,
#: framework/extensions/trending/customizer.php:825
msgid "Taxonomy Font"
msgstr "Font taksonomije"

#: framework/extensions/trending/customizer.php:708,
#: framework/extensions/trending/customizer.php:748
msgid "Taxonomies Font Color"
msgstr "Boja fonta taksonomija"

#: framework/extensions/trending/customizer.php:780
msgid "Taxonomies Button Color"
msgstr "Boja gumba taksonomija"

#: framework/extensions/trending/customizer.php:835
msgid "Taxonomy Font Color"
msgstr "Boja fonta taksonomije"

#: framework/extensions/trending/customizer.php:860,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:248,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:476,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:494
msgid "Image Border Radius"
msgstr "Polumjer obruba slike"

#: framework/features/blocks/blocks.php:19
msgctxt "Block pattern category"
msgid "Blocksy"
msgstr ""

#: framework/features/blocks/blocks.php:24
msgid "Patterns that contain buttons and call to actions."
msgstr "Uzorci koji sadrže gumbe i pozive na akciju."

#: framework/features/blocks/blocks.php:71
msgid "Home Page Text"
msgstr "Tekst početne stranice"

#: framework/features/demo-install/child-theme.php:9
msgid "Sorry, you don't have permission to install child themes."
msgstr "Žao nam je, nemate dopuštenje za instalaciju child tema."

#: framework/features/demo-install/content-eraser.php:23
msgid "Sorry, you don't have permission to erase content."
msgstr "Žao nam je, nemate dopuštenje za brisanje sadržaja."

#: framework/features/demo-install/content-installer.php:69
msgid "Sorry, you don't have permission to install content."
msgstr "Žao nam je, nemate dopuštenje za instalaciju sadržaja."

#: framework/features/demo-install/content-installer.php:195,
#: framework/features/demo-install/content-installer.php:189
msgid "No demo data found."
msgstr "Nisu pronađeni demo podaci."

#: framework/features/demo-install/content-installer.php:351,
#: framework/features/demo-install/content-installer.php:346
msgid "No pages to assign."
msgstr "Nema stranica za dodjelu."

#: framework/features/demo-install/install-finish.php:23
msgid "Sorry, you don't have permission to finish the installation."
msgstr "Žao nam je, nemate dopuštenje za dovršetak instalacije."

#: framework/features/demo-install/options-import.php:38
msgid "Sorry, you don't have permission to install options."
msgstr "Žao nam je, nemate dopuštenje za instalaciju opcija."

#: framework/features/demo-install/options-import.php:50,
#: framework/features/demo-install/options-import.php:45,
#: framework/features/demo-install/options-import.php:80,
#: framework/features/demo-install/options-import.php:75,
#: framework/features/demo-install/widgets-import.php:48,
#: framework/features/demo-install/widgets-import.php:43
msgid "No demo to install"
msgstr "Nema dostupnog demo sadržaja za instalaciju"

#: framework/features/demo-install/plugins-uninstaller.php:9
msgid "Sorry, you don't have permission to uninstall plugins."
msgstr "Žao nam je, nemate dopuštenje za deinstalaciju dodataka."

#: framework/features/demo-install/required-plugins.php:37
msgid "Sorry, you don't have permission to install plugins."
msgstr "Žao nam je, nemate dopuštenje za instalaciju dodataka."

#: framework/features/demo-install/required-plugins.php:49,
#: framework/features/demo-install/required-plugins.php:44
msgid "No plugins to install."
msgstr "Nema dostupnih dodataka za instalaciju."

#: framework/features/demo-install/widgets-import.php:36
msgid "Sorry, you don't have permission to install widgets."
msgstr "Žao nam je, nemate dopuštenje za instalaciju widgeta."

#: framework/features/demo-install/widgets-import.php:79,
#: framework/features/demo-install/widgets-import.php:73
msgid "No widgets to install."
msgstr "Nema dostupnih widgeta za instalaciju."

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:98
msgid "Fields Gap"
msgstr "Razmak polja"

#: framework/features/blocks/about-me/options.php:21
msgid "User Source"
msgstr "Izvor korisnika"

#: framework/features/blocks/about-me/options.php:26
msgid "Dynamic"
msgstr "Dinamički"

#: framework/features/blocks/about-me/options.php:37
msgid "User"
msgstr "Korisnik"

#: framework/features/blocks/about-me/options.php:92
msgid "Image Shape"
msgstr "Oblik slike"

#: framework/features/blocks/about-me/options.php:104
msgid "Alignment"
msgstr "Poravnanje"

#: framework/features/blocks/about-me/options.php:119,
#: framework/features/blocks/socials/options.php:19
msgid "Social Channels"
msgstr "Društveni kanali"

#: framework/features/blocks/about-me/options.php:195,
#: framework/features/blocks/share-box/options.php:149,
#: framework/features/blocks/socials/options.php:101
msgid "Official"
msgstr "Službeni"

#: framework/features/blocks/about-me/view.php:197
msgid "View Profile"
msgstr "Pogledaj profil"

#: framework/features/blocks/contact-info/options.php:44
msgid "Contact Information"
msgstr "Kontakt informacije"

#: framework/features/blocks/dynamic-data/options.php:24,
#: framework/features/blocks/dynamic-data/options.php:49
msgid "Date type"
msgstr "Vrsta datuma"

#: framework/features/blocks/dynamic-data/options.php:31
msgid "Published Date"
msgstr "Datum objave"

#: framework/features/blocks/dynamic-data/options.php:32
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:141
msgid "Modified Date"
msgstr "Datum izmjene"

#: framework/features/blocks/dynamic-data/options.php:39
msgid "Default format"
msgstr "Zadani format"

#: framework/features/blocks/dynamic-data/options.php:41
msgid "Example: January 24, 2022"
msgstr "Primjer: 24. siječnja 2022"

#: framework/features/blocks/dynamic-data/options.php:75
msgid "Custom date format"
msgstr "Prilagođeni format datuma"

#: framework/features/blocks/dynamic-data/options.php:79
msgid "Enter a date or time"
msgstr "Unesite datum ili vrijeme"

#: framework/features/blocks/dynamic-data/options.php:96,
#: framework/features/blocks/dynamic-data/options.php:97,
#: framework/features/blocks/dynamic-data/views/wp-field.php:194
msgid "No comments"
msgstr "Nema komentara"

#: framework/features/blocks/dynamic-data/options.php:102,
#: framework/features/blocks/dynamic-data/options.php:103,
#: framework/features/blocks/dynamic-data/views/wp-field.php:195
msgid "One comment"
msgstr "Jedan komentar"

#: framework/features/blocks/dynamic-data/options.php:108
msgid "Multiple comments"
msgstr "Više komentara"

#: framework/features/blocks/dynamic-data/options.php:120
msgid "Separator"
msgstr "Razdjelnik"

#: framework/features/blocks/dynamic-data/options.php:132
msgid "Author Field"
msgstr "Polje autora"

#: framework/features/blocks/dynamic-data/options.php:139
msgid "Nickname"
msgstr "Nadimak"

#: framework/features/blocks/dynamic-data/options.php:138
msgid "Display Name"
msgstr "Prikazano ime"

#: framework/features/blocks/dynamic-data/options.php:140
msgid "First Name"
msgstr "Ime"

#: framework/features/blocks/dynamic-data/options.php:141
msgid "Last Name"
msgstr "Prezime"

#: framework/features/blocks/dynamic-data/options.php:172
msgid "Link to post"
msgstr "Poveznica na objavu"

#: framework/features/blocks/dynamic-data/options.php:176
msgid "Link to author page"
msgstr "Poveznica na stranicu autora"

#: framework/features/blocks/dynamic-data/options.php:180
msgid "Link to term page"
msgstr "Poveznica na stranicu termina"

#: framework/features/blocks/dynamic-data/options.php:184
msgid "Link to archive page"
msgstr "Poveznica na stranicu arhive"

#: framework/features/blocks/dynamic-data/options.php:197
msgid "Open in new tab"
msgstr "Otvori u novoj kartici"

#: framework/features/blocks/dynamic-data/options.php:203
msgid "Link Rel"
msgstr "Rel atribut poveznice"

#: framework/features/blocks/dynamic-data/options.php:220
msgid "Terms accent color"
msgstr "Naglašena boja termina"

#: framework/features/blocks/search/options.php:147,
#: framework/features/blocks/search/view.php:265
msgid "Select Category"
msgstr "Odaberi kategoriju"

#: framework/features/blocks/search/options.php:175,
#: framework/premium/features/premium-header/items/search-input/options.php:164
msgid "Taxonomy Children"
msgstr "Podređene taksonomije"

#: framework/features/blocks/search/options.php:205,
#: framework/premium/features/premium-header/items/search-input/options.php:192
msgid "Search Through Taxonomies"
msgstr "Pretraživanje kroz taksonomije"

#: framework/features/blocks/search/options.php:209,
#: framework/premium/features/premium-header/items/search-input/options.php:196
msgid "Search through taxonomies from selected custom post types."
msgstr "Pretražujte taksonomije odabranih prilagođenih vrsta objava."

#: framework/features/blocks/share-box/options.php:15
msgid "Share Icons"
msgstr "Ikone za dijeljenje"

#: framework/features/blocks/share-box/options.php:43
msgid "Reddit"
msgstr "Reddit"

#: framework/features/blocks/share-box/options.php:49
msgid "Hacker News"
msgstr "Hacker News"

#: framework/features/blocks/share-box/options.php:67
msgid "Telegram"
msgstr "Telegram"

#: framework/features/blocks/share-box/options.php:73
msgid "Viber"
msgstr "Viber"

#: framework/features/blocks/share-box/options.php:79
msgid "WhatsApp"
msgstr "WhatsApp"

#: framework/features/blocks/share-box/options.php:85
msgid "Flipboard"
msgstr "Flipboard"

#: framework/features/blocks/share-box/options.php:91
msgid "Line"
msgstr "Line"

#: framework/features/conditions/rules/archive-loop.php:6
msgid "Archive Item with Taxonomy ID"
msgstr "Arhivska stavka s ID-jem taksonomije"

#: framework/features/conditions/rules/archive-loop.php:13
msgid "WooCommerce Archive Item with Taxonomy ID"
msgstr "WooCommerce arhivska stavka s ID-jem taksonomije"

#: framework/features/conditions/rules/archive-loop.php:19
msgid "Archive Loop Speciffic"
msgstr "Specifični arhivski prikaz"

#: framework/features/conditions/rules/posts.php:10
msgid "Post Archives"
msgstr "Arhive objava"

#: framework/features/conditions/rules/woo.php:54
msgid "Single Product ID"
msgstr "ID pojedinačnog proizvoda"

#: framework/features/conditions/rules/woo.php:59
msgid "Single Product with Taxonomy ID"
msgstr "Pojedinačni proizvod s ID-jem taksonomije"

#: framework/premium/extensions/woocommerce-extra/utils.php:141
msgid "Private: %s"
msgstr "Privatno: %s"

#: framework/premium/extensions/woocommerce-extra/utils.php:131
msgid "Protected: %s"
msgstr "Zaštićeno: %s"

#: framework/premium/features/content-blocks/hooks-manager.php:733
msgid "WooCommerce Single Product"
msgstr "WooCommerce pojedinačni proizvod"

#: framework/premium/features/media-video/options.php:119
msgid "Video Size"
msgstr "Veličina videa"

#: framework/premium/features/media-video/options.php:127
#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:192
msgid "Contain"
msgstr "Prilagodi"

#: framework/premium/features/media-video/options.php:128
#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:187
msgid "Cover"
msgstr "Prekrij"

#. translators: placeholder here means the actual URL.
#: framework/premium/features/media-video/options.php:132
msgid "Choose how the video will fill its container. More info about this can be found %shere%s."
msgstr "Odaberite kako će video popuniti svoj kontejner. Više informacija možete pronaći %shere%s."

#: framework/features/blocks/dynamic-data/views/avatar-field.php:30
msgid "%s Avatar"
msgstr "%s Avatar"

#: framework/features/blocks/query/block-patterns/posts-layout-1.php:4
msgid "Posts - Layout 1"
msgstr "Objave - Izgled 1"

#: framework/features/blocks/query/block-patterns/posts-layout-2.php:4
msgid "Posts - Layout 2"
msgstr "Objave - Izgled 2"

#: framework/features/blocks/query/block-patterns/posts-layout-3.php:4
msgid "Posts - Layout 3"
msgstr "Objave - Izgled 3"

#: framework/features/blocks/query/block-patterns/posts-layout-4.php:4
msgid "Posts - Layout 4"
msgstr "Objave - Izgled 4"

#: framework/features/blocks/query/block-patterns/posts-layout-5.php:4
msgid "Posts - Layout 5"
msgstr "Objave - Izgled 5"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-1.php:4
msgid "Taxonomies - Layout 1"
msgstr "Taksonomije - Izgled 1"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-2.php:4
msgid "Taxonomies - Layout 2"
msgstr "Taksonomije - Izgled 2"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-3.php:4
msgid "Taxonomies - Layout 3"
msgstr "Taksonomije - Izgled 3"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-4.php:4
msgid "Taxonomies - Layout 4"
msgstr "Taksonomije - Izgled 4"

#: framework/features/header/items/account/options.php:15
msgid "Action Link"
msgstr "Akcijska poveznica"

#: framework/features/header/items/account/options.php:49
msgid "Additional User Info"
msgstr "Dodatne korisničke informacije"

#: framework/features/header/items/account/options.php:53
msgid "Available fields: {user_email}, {user_name}, {user_role}"
msgstr "Dostupna polja: {user_email}, {user_name}, {user_role}"

#: framework/features/header/items/account/options.php:142
msgid "Menu"
msgstr "Izbornik"

#: framework/features/header/items/account/options.php:717
msgid "Items Hover Effect"
msgstr "Efekt lebdenja stavki"

#: framework/features/header/items/account/options.php:727
msgid "Boxed Color"
msgstr "Ograničena boja"

#: framework/features/header/items/account/options.php:1945
msgid "Link Active"
msgstr "Aktivna poveznica"

#: framework/premium/features/content-blocks/options/popup.php:262
msgid "Close Trigger Delay"
msgstr "Kašnjenje zatvaranja okidača"

#: framework/premium/features/content-blocks/options/popup.php:269
msgid "Set the close delay time (in seconds) after the form submit action is detected."
msgstr "Postavite kašnjenje zatvaranja (u sekundama) nakon što se detektira akcija slanja obrasca."

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:100,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:813
msgid "Featured Icon/Logo"
msgstr "Istaknuta ikona/logo"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:570
msgid "Upvote"
msgstr "Glasaj za"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:584
msgid "Downvote"
msgstr "Glasaj protiv"

#: framework/premium/extensions/woocommerce-extra/features/brands/brands-import-export.php:17,
#: framework/premium/extensions/woocommerce-extra/features/brands/brands-import-export.php:69
msgid "Blocksy Brands"
msgstr "Blocksy Brendovi"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/select.php:18
msgid "Choose page"
msgstr "Odaberi stranicu"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/select.php:33
msgid "Choose a custom thank you page for this product."
msgstr "Odaberite prilagođenu zahvalnu stranicu za ovaj proizvod."

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:49
msgid "Product Image Visibility"
msgstr "Vidljivost slike proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:87
msgid "Product Price & Stock Visibility"
msgstr "Vidljivost cijene i zalihe proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:42
msgid "Trigger Icon Type"
msgstr "Vrsta ikone okidača"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:77
msgid "Trigger Visibility"
msgstr "Vidljivost okidača"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:107
msgid "Trigger Label"
msgstr "Oznaka okidača"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:233
msgid "Panel Default State"
msgstr "Zadano stanje panela"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:240
msgid "Closed"
msgstr "Zatvoren"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:241
msgid "Opened"
msgstr "Otvoren"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:488
msgid "Panel AJAX Reveal"
msgstr "AJAX otkrivanje panela"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:140
msgid "Autoplay Gallery"
msgstr "Automatska reprodukcija galerije"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:152
msgid "Delay (in seconds)"
msgstr "Kašnjenje (u sekundama)"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:219
msgid "Columns Spacing"
msgstr "Razmak stupaca"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:247,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:275
msgid "Arrows Visibility"
msgstr "Vidljivost strelica"

#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:11
msgid "Prev/Next Arrow"
msgstr "Prethodna/sljedeća strelica"

#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:41
msgid "Prev/Next Background"
msgstr "Pozadina prethodna/sljedeća"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:372,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:160
msgid "Add {items} more items to get free shipping!"
msgstr "Dodajte još {items} stavki i ostvarite besplatnu dostavu!"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:52
msgid "Count Criteria"
msgstr "Kriterij broja"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:69,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:59,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:246,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/price-filter.php:299,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:53,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:34,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:34
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:128
msgid "Price"
msgstr "Cijena"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:60
msgid "Items"
msgstr "Stavke"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:86
msgid "Goal Items"
msgstr "Ciljane stavke"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:90
msgid "Amount of items the client has to buy in order to get free shipping."
msgstr "Broj stavki koje kupac mora kupiti kako bi dobio besplatnu dostavu."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:168
msgid "You can use dynamic code tags such as {items} inside this option."
msgstr "Možete koristiti dinamičke kodne oznake poput {items} unutar ove opcije."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:195,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:62
msgid "Bar Color"
msgstr "Boja trake"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:187,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:29
msgid "🚨 Hurry up! Only {items} units left in stock!"
msgstr "🚨 Požurite! Ostalo je samo {items} jedinica na zalihi!"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:15
msgid "Stock Threshold"
msgstr "Prag zaliha"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:23
msgid "Show the stock scarcity module when product stock is below this number."
msgstr "Prikažite modul nestašice zaliha kada količina proizvoda padne ispod ovog broja."

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:27
msgid "Message"
msgstr "Poruka"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:43
msgid "Bar Height"
msgstr "Visina trake"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:686
msgid "Swatches removed"
msgstr "Prilagođeni uzorci uklonjeni"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:693
msgid "Swatches saved"
msgstr "Prilagođeni uzorci spremljeni"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:755
msgid "Custom Attributes"
msgstr "Prilagođeni atributi"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:761,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:800,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:873
msgid "Terms Limit"
msgstr "Ograničenje termina"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:764,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:803
msgid "Set how many terms you want to display in this attribute."
msgstr "Postavite koliko termina želite prikazati u ovom atributu."

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:774,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:813,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:887
#: static/js/editor/blocks/query/Edit.js:178
#: static/js/editor/blocks/tax-query/Edit.js:165
msgid "Limit"
msgstr "Ograničenje"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:876
msgid "Set how many terms you want to display in each attribute."
msgstr "Postavite koliko termina želite prikazati u svakom atributu."

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:179,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:462
msgid "Mixed Swatches"
msgstr "Mješoviti uzorci"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:233
msgid "Generate Variation URL"
msgstr "Generiraj URL varijacije"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:239
msgid "Generate a shareable single product page URL with pre-selected variation attributes."
msgstr "Generirajte dijeljivu stranicu pojedinačnog proizvoda s unaprijed odabranim atributima varijacije."

#: static/js/options/ConditionsManager/SingleCondition.js:446
msgid "Display if query string is present in URL"
msgstr "Prikaži ako je niz upita prisutan u URL-u"

#: static/js/options/DisplayCondition.js:62
msgid "Add Conditions"
msgstr "Dodaj uvjete"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:68
msgid "Upgrade to the agency plan and get instant access to this starter site and many other features."
msgstr "Nadogradite na agency plan i odmah pristupite ovoj početnoj stranici i mnogim drugim značajkama."

#: static/js/dashboard/screens/Extensions/CurrentExtension.js:316
msgid "Documentation"
msgstr "Dokumentacija"

#: static/js/dashboard/screens/Extensions/CurrentExtension.js:346
msgid "Manage"
msgstr "Upravljanje"

#: static/js/dashboard/screens/Extensions/CurrentExtension.js:86
msgid "Video Tutorial"
msgstr "Video vodič"

#: static/js/options/ConditionsManager.js:207
msgid "Advanced Mode"
msgstr "Napredni način"

#: static/js/options/ConditionsManager/PostIdPicker.js:78
msgid "Select product"
msgstr "Odaberi proizvod"

#: static/js/options/ConditionsManager/ScheduleDate.js:149
msgid "Monday"
msgstr "Ponedjeljak"

#: static/js/options/ConditionsManager/ScheduleDate.js:154
msgid "Tuesday"
msgstr "Utorak"

#: static/js/options/ConditionsManager/ScheduleDate.js:159
msgid "Wednesday"
msgstr "Srijeda"

#: static/js/options/ConditionsManager/ScheduleDate.js:164
msgid "Thursday"
msgstr "Četvrtak"

#: static/js/options/ConditionsManager/ScheduleDate.js:169
msgid "Friday"
msgstr "Petak"

#: static/js/options/ConditionsManager/ScheduleDate.js:174
msgid "Saturday"
msgstr "Subota"

#: static/js/options/ConditionsManager/ScheduleDate.js:179
msgid "Sunday"
msgstr "Nedjelja"

#: static/js/options/ConditionsManager/ScheduleDate.js:21
msgid "Mon"
msgstr "Pon"

#: static/js/options/ConditionsManager/ScheduleDate.js:211
msgid "Start Time"
msgstr "Početno vrijeme"

#: static/js/options/ConditionsManager/ScheduleDate.js:22
msgid "Tue"
msgstr "Uto"

#: static/js/options/ConditionsManager/ScheduleDate.js:23
msgid "Wed"
msgstr "Sri"

#: static/js/options/ConditionsManager/ScheduleDate.js:234
msgid "Stop Time"
msgstr "Završno vrijeme"

#: static/js/options/ConditionsManager/ScheduleDate.js:24
msgid "Thu"
msgstr "Čet"

#: static/js/options/ConditionsManager/ScheduleDate.js:25
msgid "Fri"
msgstr "Pet"

#: static/js/options/ConditionsManager/ScheduleDate.js:26
msgid "Sat"
msgstr "Sub"

#: static/js/options/ConditionsManager/ScheduleDate.js:27
msgid "Sun"
msgstr "Ned"

#: static/js/options/ConditionsManager/ScheduleDate.js:58
msgid "Every day"
msgstr "Svaki dan"

#: static/js/options/ConditionsManager/ScheduleDate.js:66
msgid "Only weekends"
msgstr "Samo vikendom"

#: static/js/options/ConditionsManager/ScheduleDate.js:74
msgid "Only weekdays"
msgstr "Samo radnim danima"

#: static/js/options/ConditionsManager/SingleCondition.js:325
msgid "Select sub field"
msgstr "Odaberi podpolje"

#: static/js/options/ConditionsManager/SingleCondition.js:378
msgid "Display based on referer domain"
msgstr "Prikaži na temelju domene referenta"

#: static/js/options/ConditionsManager/SingleCondition.js:412
msgid "Display if cookie is present"
msgstr "Prikaži ako je kolačić prisutan"

#: static/js/dashboard/helpers/useUpsellModal.js:42
msgid "Upgrade to the agency plan and get instant access to this and many other features."
msgstr "Nadogradite na agency plan i odmah pristupite ovoj i mnogim drugim značajkama."

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:53
msgid "This is a Pro starter site"
msgstr "Ovo je Pro početna stranica"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:54
msgid "Upgrade to any pro plan and get instant access to this starter site and many other features."
msgstr "Nadogradite na bilo koji Pro plan i odmah pristupite ovoj početnoj stranici i mnogim drugim značajkama."

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:61
msgid "Upgrade to the business or agency plan and get instant access to this starter site and many other features."
msgstr "Nadogradite na business ili agency plan i odmah pristupite ovoj početnoj stranici i mnogim drugim značajkama."

#: static/js/dashboard/helpers/useProExtensionInFree.js:14
msgid "This is a Pro extension"
msgstr "Ovo je Pro ekstenzija"

#: static/js/dashboard/helpers/useUpsellModal.js:103
#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:115
msgid "Agency"
msgstr "Agencija"

#: static/js/dashboard/helpers/useUpsellModal.js:11
#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:14
msgid "Free"
msgstr "Besplatno"

#: static/js/dashboard/helpers/useUpsellModal.js:122
msgid "Compare Plans"
msgstr "Usporedite planove"

#: static/js/dashboard/helpers/useUpsellModal.js:17
#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:107
msgid "Personal"
msgstr "Osobno"

#: static/js/dashboard/helpers/useUpsellModal.js:27
msgid "This is a Pro feature"
msgstr "Ovo je Pro značajka"

#: static/js/dashboard/helpers/useUpsellModal.js:28
msgid "Upgrade to any pro plan and get instant access to this and many other feature."
msgstr "Nadogradite na bilo koji Pro plan i odmah pristupite ovoj i mnogim drugim značajkama."

#: static/js/dashboard/helpers/useUpsellModal.js:35
msgid "Upgrade to the business or agency plan and get instant access to this and many other features."
msgstr "Nadogradite na business ili agency plan i odmah pristupite ovoj i mnogim drugim značajkama."

#: static/js/dashboard/NoTheme.js:31
msgid "In order to take full advantage of all features it has to offer - please install and activate the Blocksy theme also."
msgstr "Da biste u potpunosti iskoristili sve značajke koje nudi, instalirajte i aktivirajte Blocksy temu."

#: static/js/dashboard/NoTheme.js:65
msgid "Install and activate the Blocksy theme"
msgstr "Instaliraj i aktiviraj Blocksy temu"

#: static/js/dashboard/NoTheme.js:18
msgid "Action Required - Install Blocksy Theme"
msgstr "Potrebna radnja - Instalirajte Blocksy temu"

#: static/js/dashboard/NoTheme.js:24
msgid "Blocksy Companion is the complementary plugin to Blocksy theme. It adds a bunch of great features to the theme and acts as an unlocker for the Blocksy Pro package."
msgstr "Blocksy Companion je dodatni dodatak za Blocksy temu. Dodaje niz sjajnih značajki i djeluje kao otključavanje za Blocksy Pro paket."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:423
msgid "Show Image Frame"
msgstr "Prikaži okvir slike"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:444
msgid "Show Label"
msgstr "Prikaži oznaku"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:457
msgid "Show Counter"
msgstr "Prikaži brojač"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:164
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:82
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:526
msgid "Show Reset Button"
msgstr "Prikaži gumb za resetiranje"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:168
msgid "Show or hide reset filter button."
msgstr "Prikaži ili sakrij gumb za resetiranje filtera."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:9
msgid "Shop Filters Controls"
msgstr "Kontrole filtera trgovine"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:135
msgid "Shop Filters"
msgstr "Filteri trgovine"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:10
msgid "Widget for filtering the WooCommerce products loop by category, attribute or brand."
msgstr "Widget za filtriranje WooCommerce proizvoda prema kategoriji, atributu ili brendu."

#: framework/premium/static/js/blocks/ContentBlock.js:26
msgid "Insert a specific Content Block anywhere on the site."
msgstr "Umetnite određeni blok sadržaja bilo gdje na web-lokaciji."

#: framework/premium/static/js/hooks/CreateHook.js:156
msgid "Nothing Found Template"
msgstr "Predložak nije pronađen"

#: framework/premium/static/js/hooks/CreateHook.js:164
msgid "Maintenance Template"
msgstr "Predložak za održavanje"

#: framework/premium/static/js/media-video/components/EditVideoMeta.js:44
msgid "Video Options"
msgstr "Opcije videa"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:75
msgid "Display order overhiew section."
msgstr "Prikaz pregleda narudžbe."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:90
msgid "Order Details"
msgstr "Detalji narudžbe"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:94
msgid "Display order details section."
msgstr "Prikaz odjeljka s detaljima narudžbe."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:177
msgid "Filter By"
msgstr "Filtriraj po"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:92
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:221
msgid "Attribute"
msgstr "Atribut"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:11
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:145
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:192
msgid "Display Type"
msgstr "Vrsta prikaza"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:154
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:200
msgid "List"
msgstr "Popis"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:387
msgid "Image Aspect Ratio"
msgstr "Omjer slike"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:408
msgid "Image Max width"
msgstr "Maksimalna širina slike"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:233
msgid "Multiple Selections"
msgstr "Višestruki odabiri"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:237
msgid "Allow selecting multiple items in a filter."
msgstr "Omogući odabir više stavki u filteru."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:118
msgid "Select attribute"
msgstr "Odaberi atribut"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:305
msgid "Show Hierarchy"
msgstr "Prikaži hijerarhiju"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:321
msgid "Expandable"
msgstr "Proširivo"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:336
#: static/js/editor/blocks/widgets-wrapper/Edit.js:95
msgid "Expanded by Default"
msgstr "Prošireno prema zadanim postavkama"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:283
msgid "Show Checkboxes"
msgstr "Prikaži potvrdne okvire"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:364
msgid "Show Brands Images"
msgstr "Prikaži slike brendova"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:368
msgid "Show Swatches"
msgstr "Prikaži uzorke"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/VariableTab.js:30
msgid "Upload only the %s.woff2%s or %s.ttf%s font file formats. Don't convert variable fonts by yourself. Ask the font provider to hand a correct file or the %svariable%s font won't work."
msgstr "Prenesite samo formate datoteka fontova %s.woff2%s ili %s.ttf%s. Nemojte sami pretvarati varijabilne fontove. Zamolite dobavljača fonta da dostavi ispravnu datoteku jer u suprotnom %svariable%s font neće raditi."

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/AdvancedTab.js:54
msgid "Preload Subsets"
msgstr "Predučitaj podskupove"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/GeneralTab.js:10
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:65
msgid "Select Variations"
msgstr "Odaberi varijacije"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:255
msgid "Local Google Fonts Settings"
msgstr "Postavke lokalnih Google fontova"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:267
msgid "Select font"
msgstr "Odaberi font"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:335
msgid "Download Font"
msgstr "Preuzmi font"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:217
msgid "Save Font"
msgstr "Spremi font"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:608
msgid "Companion Plugin Details"
msgstr "Detalji dodatka Companion"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Disabled.js:7
msgid "Please select a valid attribute."
msgstr "Molimo odaberite važeći atribut."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:41
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:130
msgid "Exclude %s"
msgstr "Isključi %s"

#: framework/features/conditions/rules/woo.php:35
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:31
msgid "Product Attributes"
msgstr "Atributi proizvoda"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/CustomerDetails.js:10
msgid "Billing address"
msgstr "Adresa za naplatu"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/CustomerDetails.js:34
msgid "Shipping address"
msgstr "Adresa za dostavu"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:64
msgid "Subtotal"
msgstr "Međuzbroj"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:77
msgid "Shipping"
msgstr "Dostava"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:79
msgid "Free shipping"
msgstr "Besplatna dostava"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:83
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:42
msgid "Payment method"
msgstr "Način plaćanja"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:85
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:43
msgid "Cash on delivery"
msgstr "Plaćanje pouzećem"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:88
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:28
msgid "Total"
msgstr "Ukupno"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:8
msgid "Order number"
msgstr "Broj narudžbe"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:108
msgid "Customer Details"
msgstr "Podaci o kupcu"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:112
msgid "Display customer details section."
msgstr "Prikaz odjeljka s podacima o kupcu."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:71
msgid "Order Overview"
msgstr "Pregled narudžbe"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:89
msgid "Once you insert your %sProject ID%s and click the \"Fetch Fonts\" button, your fonts will become available in all theme’s typography options."
msgstr "Nakon što unesete svoj %sProject ID%s i kliknete gumb \"Dohvati fontove\", vaši fontovi će biti dostupni u svim opcijama tipografije teme."

#: framework/premium/extensions/custom-fonts/dashboard-static/js/CustomFonts.js:135
#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:320
msgid "Upload Font"
msgstr "Prenesi font"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/PreloadTab.js:14
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/AdvancedTab.js:19
msgid "Preload Variations"
msgstr "Predučitaj varijacije"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:356
msgid "Simple Font"
msgstr "Jednostavan font"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:360
msgid "Variable Font"
msgstr "Varijabilni font"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:364
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:116
msgid "Preload"
msgstr "Predučitaj"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:174
#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:20
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:342
msgid "Available Fonts"
msgstr "Dostupni fontovi"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:322
msgid "More information on how to generate an API key for Campaign Monitor can be found %shere%s."
msgstr "Više informacija o tome kako generirati API ključ za Campaign Monitor možete pronaći %shere%s."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:83
msgid "Connect Newsletter Provider"
msgstr "Poveži pružatelja newslettera"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:96
msgid "Provider"
msgstr "Pružatelj"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/ListPicker.js:91
msgid "Fetching..."
msgstr "Dohvaćanje..."

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:164
msgid "Please enter a valid Project ID to get all fonts."
msgstr "Unesite važeći Project ID kako biste preuzeli sve fontove."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:254
msgid "This provider is used only for testing purposes. It doesnt register any real subscribers."
msgstr "Ovaj pružatelj se koristi samo u testne svrhe. Ne registrira stvarne pretplatnike."

#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:486,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:520,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:555,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:589,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:428,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:463,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:494,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:524
msgid "Badge Color"
msgstr "Boja oznake"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:221
#: static/js/editor/blocks/search/Edit.js:281
msgid "Button Text Color"
msgstr "Boja teksta gumba"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/index.js:17
msgid "Newsletter Controls"
msgstr "Kontrole newslettera"

#: framework/features/blocks/search/options.php:126,
#: framework/premium/features/premium-header/items/search-input/options.php:119
msgid "Live Results Product Status"
msgstr "Status proizvoda u rezultatima uživo"

#: framework/features/blocks/search/options.php:138,
#: framework/premium/features/premium-header/items/search-input/options.php:132
msgid "Taxonomy Filter"
msgstr "Filter taksonomije"

#: framework/features/blocks/search/options.php:156,
#: framework/premium/features/premium-header/items/search-input/options.php:144
msgid "Filter Visibility"
msgstr "Vidljivost filtera"

#: framework/premium/features/premium-header/items/search-input/options.php:795
msgid "Input Border Radius"
msgstr "Polumjer obruba unosa"

#: framework/premium/features/premium-header/items/search-input/options.php:823
msgid "Dropdown Font"
msgstr "Font padajućeg izbornika"

#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:604
msgid "You don't have any products in your compare list yet."
msgstr "Još nemate proizvoda na popisu za usporedbu."

#: framework/features/blocks/dynamic-data/views/woo-field.php:25,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:481
#: static/js/editor/blocks/dynamic-data/preview-parts/woo/StockPreview.js:6
msgid "In Stock"
msgstr "Na zalihi"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:12,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:37,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/inline-filter.php:78,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/list-filter.php:109
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:198
msgid "Active Filters"
msgstr "Aktivni filteri"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:22
msgid "Active Filters Label"
msgstr "Oznaka aktivnih filtera"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/inline-filter.php:26,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/list-filter.php:31
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:32
msgid "Reset Filters"
msgstr "Resetiraj filtere"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/filter-presenter.php:149,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/price-filter.php:188
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/Preview.js:59
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Preview.js:183
msgid "Reset Filter"
msgstr "Resetiraj filter"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:728,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:24,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:46,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:142
msgid "Color"
msgstr "Boja"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:78
msgid "Short Name"
msgstr "Kratko ime"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:245
msgid "Sorry, this product is unavailable. Please choose a different combination."
msgstr "Žao nam je, ovaj proizvod nije dostupan. Molimo odaberite drugu kombinaciju."

#: framework/features/blocks/dynamic-data/views/woo-field.php:26,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:246,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:481,
#: framework/premium/extensions/woocommerce-extra/features/swatches/includes/swatch-element-render.php:47,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:57
#: static/js/editor/blocks/dynamic-data/preview-parts/woo/StockPreview.js:7
msgid "Out of Stock"
msgstr "Nema na zalihi"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:540
msgid "Display Variations Inline"
msgstr "Prikaz varijacija u liniji"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:636,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:736,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:833
msgid "Swatches"
msgstr "Uzorkovanje"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:16,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:250
msgid "Color Swatches"
msgstr "Uzorci boja"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:20,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:74,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:129,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:183
msgid "Swatch Shape"
msgstr "Oblik uzorka"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:26,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:80,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:135,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:189
msgid "Round"
msgstr "Okruglo"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:87,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:142,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:196
msgid "Single Page Swatch Size"
msgstr "Veličina uzorka na pojedinačnoj stranici"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:42,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:96,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:151,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:205
msgid "Widget Swatch Size"
msgstr "Veličina uzorka widgeta"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:111,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:166,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:220
msgid "Archive Cards Swatch Size"
msgstr "Veličina uzorka na arhivskim karticama"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:70,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:294
msgid "Image Swatches"
msgstr "Uzorci slika"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:125,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:338
msgid "Button Swatches"
msgstr "Uzorci gumba"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:395
msgid "Wishlist Button"
msgstr "Gumb liste želja"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:58
msgid "Specific Product Variation "
msgstr ""

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:61
msgid "This option will allow you to add a speciffic product variation to wishlist."
msgstr "Ova opcija omogućit će vam dodavanje određene varijacije proizvoda na listu želja."

#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:601,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/views/table-no-results.php:3,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:19
msgid "Browse products"
msgstr "Pregledaj proizvode"

#: framework/features/blocks/contact-info/options.php:533,
#: framework/premium/features/premium-header/items/contacts/options.php:417
msgid "Link Icons"
msgstr "Ikone poveznica"

#: framework/premium/features/premium-header/items/contacts/view.php:59
msgid "Download"
msgstr "Preuzmi"

#: framework/premium/features/premium-header/items/divider/options.php:22,
#: framework/premium/features/premium-header/items/divider/options.php:51,
#: framework/premium/features/premium-header/items/divider/options.php:66,
#: framework/premium/features/premium-header/items/divider/options.php:81
msgid "Style & Color"
msgstr "Stil i boja"

#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:87
msgid "Dropdown Arrow"
msgstr "Strelica padajućeg izbornika"

#: framework/premium/features/premium-header/items/menu-tertiary/config.php:4
msgid "Menu 3"
msgstr "Izbornik 3"

#: framework/features/blocks/search/options.php:119,
#: framework/premium/features/premium-header/items/search-input/options.php:111
msgid "Live Results Product Price"
msgstr "Cijena proizvoda u rezultatima uživo"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:274
msgid "Add New Size Guide"
msgstr "Dodaj novi vodič za veličinu"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:275
msgid "Edit Size Guide"
msgstr "Uredi vodič za veličinu"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:276
msgid "New Size Guide"
msgstr "Novi vodič za veličinu"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:278
msgid "View Size Guide"
msgstr "Pregled vodiča za veličinu"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:279
msgid "Search Size Guides"
msgstr "Pretraži vodiče za veličinu"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/modal-view.php:25,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/side-view.php:34
msgid "Close Sizes Modal"
msgstr "Zatvori modal s veličinama"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:29
msgid "Size Guide Display Conditions"
msgstr "Uvjeti prikaza vodiča za veličinu"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:30
msgid "Choose where you want this size guide to be displayed."
msgstr "Odaberite gdje želite prikazati ovaj vodič za veličinu."

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:243
msgid "Sorry, no products matched your selection. Please choose a different combination."
msgstr "Žao nam je, nijedan proizvod ne odgovara vašem odabiru. Molimo odaberite drugu kombinaciju."

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:244
msgid "Please select some product options before adding this product to your cart."
msgstr "Molimo odaberite opcije proizvoda prije dodavanja u košaricu."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:76
msgid "Amount the client has to reach in order to get free shipping."
msgstr "Iznos koji kupac mora doseći kako bi ostvario besplatnu dostavu."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:104
msgid "The calculation method will be based on WooCommerce zones."
msgstr "Metoda izračuna temelji se na WooCommerce zonama."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:119
msgid "Discount Calculation"
msgstr "Izračun popusta"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:129
msgid "Include or exclude the discount code when calculating the shipping progress."
msgstr "Uključite ili isključite kod za popust prilikom izračuna napretka dostave."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:133,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:158
msgid "Default Message"
msgstr "Zadana poruka"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:143
msgid "You can use dynamic code tags such as {price} inside this option."
msgstr "Možete koristiti dinamičke kodne oznake poput {price} unutar ove opcije."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:174
msgid "Success Message"
msgstr "Poruka uspjeha"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:5,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:271,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:277
msgid "Size Guides"
msgstr "Vodiči za veličinu"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:16
msgid "Size Guide Placement"
msgstr "Položaj vodiča za veličinu"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:23
msgid "Side Panel"
msgstr "Bočna ploča"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:33
msgid "Reveal From"
msgstr "Prikaži iz"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:303,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:30
msgid "Columns & Products"
msgstr "Stupci i proizvodi"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:330,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:57
msgid "Number of products"
msgstr "Broj proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:32
msgid "Share Box Icons Color"
msgstr "Boja ikona okvira za dijeljenje"

#: framework/helpers/exts-configs.php:330,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:180,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:4
msgid "Free Shipping Bar"
msgstr "Traka za besplatnu dostavu"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:183
msgid "Show if cart is empty"
msgstr "Prikaži ako je košarica prazna"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:382,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:176
msgid "Congratulations! You got free shipping 🎉"
msgstr "Čestitamo! Ostvarili ste besplatnu dostavu 🎉"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:359,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:135
msgid "Add {price} more to get free shipping!"
msgstr "Dodajte još {price} za besplatnu dostavu!"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:15
msgid "Show In Cart Page"
msgstr "Prikaži na stranici košarice"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:21
msgid "Show In Checkout Page"
msgstr "Prikaži na stranici naplate"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:27
msgid "Show In Mini Cart"
msgstr "Prikaži u mini košarici"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:34
msgid "Calculation Method"
msgstr "Metoda izračuna"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:69
msgid "Goal Amount"
msgstr "Ciljani iznos"

#: framework/premium/extensions/woocommerce-extra/features/filters/feature.php:126
msgid "Automatically scroll page to top after user interaction."
msgstr "Automatski pomakni stranicu na vrh nakon korisničke interakcije."

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-cart/feature.php:167
msgid "Shopping Cart"
msgstr "Košarica"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-cart/feature.php:169
msgid "Close cart drawer"
msgstr "Zatvori ladicu košarice"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/feature.php:152,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:525
msgid "WooCommerce Filters Canvas"
msgstr "WooCommerce platno s filtrima"

#: framework/premium/extensions/shortcuts/customizer.php:477,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:4
msgid "Filters Canvas"
msgstr "Platno s filtrima"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:177
msgid "Panel Height"
msgstr "Visina panela"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:187
msgid "Auto"
msgstr "Automatski"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:198
msgid "Custom Height"
msgstr "Prilagođena visina"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:216
msgid "Panel Columns"
msgstr "Stupci panela"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:279,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:100
msgid "Panel Backdrop"
msgstr "Pozadina panela"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:516
msgid "Widget Area Source"
msgstr "Izvor područja widgeta"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:524
msgid "WooCommerce Sidebar"
msgstr "WooCommerce bočna traka"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:110,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:242
msgid "Days"
msgstr "Dani"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:111,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:256
msgid "Hours"
msgstr "Sati"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:115,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:270
msgid "Min"
msgstr "Min"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:116,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:284
msgid "Sec"
msgstr "Sek"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:298,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:330
msgid "Hurry up! This sale ends in"
msgstr "Požurite! Ova rasprodaja završava za"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:324
msgid "Countdown Box"
msgstr "Okvir s odbrojavanjem"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/helpers.php:36
msgid "Quick view"
msgstr "Brzi pregled"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:19
msgid "Additional Actions"
msgstr "Dodatne radnje"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:25
msgid "Buttons Type"
msgstr "Vrsta gumba"

#: framework/extensions/trending/customizer.php:449,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:80,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:727
msgid "Button"
msgstr "Gumb"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:72
msgid "Modal Trigger"
msgstr "Okidač modala"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:82
msgid "Card"
msgstr "Kartica"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:91
msgid "Modal Width"
msgstr "Širina modala"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:102
msgid "Product Navigation"
msgstr "Navigacija proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:108
msgid "Display next/previous buttons that will help to easily navigate through products."
msgstr "Prikaz gumba sljedeći/prethodni za jednostavno kretanje kroz proizvode."

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:175,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:412,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:130,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:138
msgid "Title Font"
msgstr "Font naslova"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:210,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:442,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:168
msgid "Price Font"
msgstr "Font cijene"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:219,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:450,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:176
msgid "Price Color"
msgstr "Boja cijene"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:219
msgid "Add To Cart Button"
msgstr "Gumb Dodaj u košaricu"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:291
msgid "View Cart Button"
msgstr "Gumb Pogledaj košaricu"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:361
msgid "Product Page Button"
msgstr "Gumb na stranici proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:46
msgid "How many days the products will be marked as \"New\" after creation."
msgstr "Koliko dana će proizvodi biti označeni kao \"Novi\" nakon kreiranja."

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:136,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:142
msgid "Product Tabs"
msgstr "Kartice proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:137
msgid "Product Tab"
msgstr "Kartica proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:139
msgid "Add New Product Tab"
msgstr "Dodaj novu karticu proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:140
msgid "Edit Product Tab"
msgstr "Uredi karticu proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:141
msgid "New Product Tab"
msgstr "Nova kartica proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:143
msgid "View Product Tab"
msgstr "Pregled kartice proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:144
msgid "Search Product Tabs"
msgstr "Pretraži kartice proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:10
msgid "Product Tab Display Conditions"
msgstr "Uvjeti prikaza kartice proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:11
msgid "Choose where you want this product tab to be displayed."
msgstr "Odaberite gdje želite prikazati ovu karticu proizvoda."

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:27
msgid "Tab Order"
msgstr "Redoslijed kartica"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:32
msgid "Default tabs order: Description - 10, Additional Information - 20, Reviews - 30."
msgstr "Zadani redoslijed kartica: Opis - 10, Dodatne informacije - 20, Recenzije - 30."

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:106
msgid "Payment Gateways"
msgstr "Platni sustavi"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:112,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:65
msgid "Shipping Methods"
msgstr "Metode dostave"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:265
msgid "Thank you Page"
msgstr "Stranica zahvale"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:480,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:486
msgid "Thank You Pages"
msgstr "Stranice zahvale"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:481
msgid "Thank You Page"
msgstr "Stranica zahvale"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:483
msgid "Add New Thank You Page"
msgstr "Dodaj novu stranicu zahvale"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:484
msgid "Edit Thank You Page"
msgstr "Uredi stranicu zahvale"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:485
msgid "New Thank You Page"
msgstr "Nova stranica zahvale"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:487
msgid "View Thank You Page"
msgstr "Pregled stranice zahvale"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:488
msgid "Search Thank You Pages"
msgstr "Pretraži stranice zahvale"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:78
msgid "Payment Gateway"
msgstr "Platni sustav"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:106
msgid "Priority"
msgstr "Prioritet"

#: framework/premium/extensions/woocommerce-extra/features/cart-page/options.php:5
msgid "Coupon Form"
msgstr "Obrazac za unos kupona"

#: framework/premium/extensions/woocommerce-extra/features/filters/feature.php:119
msgid "AJAX Filtering"
msgstr "AJAX filtriranje"

#: framework/premium/extensions/woocommerce-extra/features/filters/feature.php:125
msgid "Scroll to Top"
msgstr "Povratak na vrh"

#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:276
msgid "Compare Products"
msgstr "Usporedi proizvode"

#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:282
msgid "Close Compare Modal"
msgstr "Zatvori modal za usporedbu"

#: framework/premium/extensions/woocommerce-extra/features/compare/helpers.php:93,
#: framework/premium/extensions/woocommerce-extra/features/compare/helpers.php:152
msgid "Add to compare"
msgstr "Dodaj za usporedbu"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:46
msgid "Compare Placement"
msgstr "Položaj usporedbe"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:53
#: static/js/editor/blocks/breadcrumbs/Preview.js:46
msgid "Page"
msgstr "Stranica"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:62
msgid "Select Page"
msgstr "Odaberi stranicu"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:69
msgid "Select a page where the compare table will be outputted."
msgstr "Odaberite stranicu na kojoj će se prikazati tablica za usporedbu."

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:75
msgid "Compare Table Fields"
msgstr "Polja tablice za usporedbu"

#: framework/features/blocks/dynamic-data/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:162
msgid "Length"
msgstr "Dužina"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:130,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:186
msgid "Attributes"
msgstr "Atributi"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:266,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:470
msgid "Availability"
msgstr "Dostupnost"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:469
msgid "Modal Border Radius"
msgstr "Polumjer obruba modala"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:366
msgid "Compare Bar"
msgstr "Traka za usporedbu"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:394
msgid "Button Icon"
msgstr "Ikona gumba"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:449
msgid "Compare Bar Display Conditions"
msgstr "Uvjeti prikaza trake za usporedbu"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:450
msgid "Add one or more conditions to display the Compare bar."
msgstr "Dodajte jedan ili više uvjeta za prikaz trake za usporedbu."

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:463,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:223,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:295,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:365
msgid "Button Font Color"
msgstr "Boja fonta gumba"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:494,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:253,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:325,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:393
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:250
#: static/js/editor/blocks/search/Edit.js:311
msgid "Button Background Color"
msgstr "Boja pozadine gumba"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:15,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:5
msgid "New Badge"
msgstr "Nova oznaka"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:55,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:53
msgid "Featured Badge"
msgstr "Istaknuta oznaka"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/feature.php:168,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:82
msgid "HOT"
msgstr "Vruće"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/feature.php:202,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:35
msgid "NEW"
msgstr "Novo"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:32,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:79
msgid "Badge Label"
msgstr "Oznaka"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:40
msgid "Label Duration"
msgstr "Trajanje oznake"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:57
msgid "Allow users to upload images when leaving a review."
msgstr "Omogućite korisnicima prijenos slika prilikom ostavljanja recenzije."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:73
msgid "Image Lightbox"
msgstr "Lightbox slika"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:74
msgid "Allow users to open attached review images in lightbox."
msgstr "Omogućite korisnicima otvaranje priloženih slika recenzije u lightboxu."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:88
msgid "Review Voting"
msgstr "Glasanje za recenzije"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:89
msgid "Allow users to upvote reviews."
msgstr "Omogućite korisnicima da glasaju za recenzije."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:105
msgid "Allowed Users"
msgstr "Dozvoljeni korisnici"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:106
msgid "Set which users are allowed to vote."
msgstr "Postavite koji korisnici smiju glasati."

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:5
msgid "Affiliate Products"
msgstr "Partnerski proizvodi"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:12
msgid "Product Archive"
msgstr "Arhiva proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:16,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:70
msgid "Image Affiliate Link"
msgstr "Poveznica slike proizvoda za affiliate"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:27,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:48,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:81
msgid "Open In New Tab"
msgstr "Otvori u novoj kartici"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:36
msgid "Title Affiliate Link"
msgstr "Poveznica naslova proizvoda za affiliate"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:90
msgid "Open Button Link In New Tab"
msgstr "Otvori poveznicu gumba u novoj kartici"

#: framework/premium/extensions/woocommerce-extra/features/cart-page/options.php:14
msgid "Quantity Auto Update"
msgstr "Automatsko ažuriranje količine"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:37
msgid "Product brands base"
msgstr "Osnovna taksonomija brendova proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:49
msgid "brand"
msgstr "brend"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:500,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:532
msgid "About Brands"
msgstr "O brendovima"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:501,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:534
msgid "About %s"
msgstr "O %s"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:558,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:578,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:640,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:707,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:718,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:493
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:146
msgid "Brands"
msgstr "Brendovi"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:562,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:150,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:177,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:221,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:239,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:257,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:275
msgid "Sticky Row"
msgstr "Ljepljivi redak"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:593,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:644
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:257
msgid "Logo Size"
msgstr "Veličina logotipa"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:605,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:656
msgid "Logos Gap"
msgstr "Razmak između logotipa"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:708
msgid "Brand"
msgstr "Brend"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:709
msgid "Search Brands"
msgstr "Pretraži brendove"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:710
msgid "All Brands"
msgstr "Svi brendovi"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:711
msgid "Parent Brand"
msgstr "Nadređeni brend"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:712
msgid "Parent Brand:"
msgstr "Nadređeni brend:"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:713
msgid "View Brand"
msgstr "Pregled brenda"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:714
msgid "Edit Brand"
msgstr "Uredi brend"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:715
msgid "Update Brand"
msgstr "Ažuriraj brend"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:716
msgid "Add New Brand"
msgstr "Dodaj novi brend"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:717
msgid "New Brand Name"
msgstr "Naziv novog brenda"

#: framework/premium/extensions/woocommerce-extra/features/brands/options.php:5
msgid "Product Brand Tab"
msgstr "Kartica brenda proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/brands/options.php:20
msgid "Brand Name In Tab Title"
msgstr "Naziv brenda u naslovu kartice"

#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:5
msgid "Product Image"
msgstr "Slika proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:54
msgid "Quantity Input"
msgstr "Unos količine"

#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:228
msgid "Compare Button"
msgstr "Gumb za usporedbu"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:139
msgid "Text Hover"
msgstr "Tekst prilikom lebdenja"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:144
msgid "Background Initial"
msgstr "Početna pozadina"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:376
msgid "Star"
msgstr "Zvijezda"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:376
msgid "Stars"
msgstr "Zvijezde"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:442
msgid "%s%% of customers recommend this product."
msgstr "%s%% kupaca preporučuje ovaj proizvod."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:604
msgid "%s of %s found this review helpful"
msgstr "%s od %s je pronašlo ovu recenziju korisnom"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:666,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:44
msgid "Review Title"
msgstr "Naslov recenzije"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:694
msgid "Upload Image (Optional)"
msgstr "Prenesi sliku (Opcionalno)"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:11
msgid "Reviews Order"
msgstr "Redoslijed recenzija"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:18
msgid "Oldest First"
msgstr "Najstarije prvo"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:19
msgid "Newest First"
msgstr "Najnovije prvo"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:20
msgid "Low Rating First"
msgstr "Najniže ocijenjene prvo"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:21
msgid "High Rating First"
msgstr "Najviše ocijenjene prvo"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:22
msgid "Most Relevant"
msgstr "Najrelevantnije"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:32
msgid "Average Score"
msgstr "Prosječna ocjena"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:33
msgid "Display an average score for all reviews."
msgstr "Prikaz prosječne ocjene svih recenzija."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:45
msgid "Allow users to add a title when leaving a review."
msgstr "Omogućite korisnicima dodavanje naslova prilikom ostavljanja recenzije."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:56
msgid "Image Upload"
msgstr "Prijenos slike"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/view.php:78
msgid "Color mode switch"
msgstr "Preklopnik načina boja"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:92
msgid "Items Counter"
msgstr "Brojač stavki"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:20,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:13
msgid "Slider"
msgstr "Klizač"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:41
msgid "Columns & Posts"
msgstr "Stupci i objave"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:49,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:311,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:38
msgid "Number of columns"
msgstr "Broj stupaca"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:68
msgid "Number of posts"
msgstr "Broj objava"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:91,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:344,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:75
msgid "Autoplay"
msgstr "Automatska reprodukcija"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:112,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:356,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:91
msgid "Delay (Seconds)"
msgstr "Kašnjenje (sekunde)"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:113,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:357,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:92
msgid "Specify the amount of time (in seconds) to delay between automatically cycling an item."
msgstr "Odredite vremensko kašnjenje (u sekundama) između automatskog izmjenjivanja stavki."

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:140
msgid "Choose the default color mode that a user will see when it visits your site."
msgstr "Odaberite zadani način boja koji će korisnik vidjeti prilikom posjeta vašoj web stranici."

#: framework/premium/features/content-blocks/options/popup.php:577
msgid "Enable this option if you want to lock the page scroll while the popup is triggered."
msgstr "Omogućite ovu opciju ako želite zaključati pomicanje stranice dok je skočni prozor aktiviran."

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/config.php:4
msgid "Color Switch"
msgstr "Preklopnik boja"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:39
msgid "Reverse Icon State"
msgstr "Obrnuto stanje ikone"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:102
msgid "Dark Mode Label"
msgstr "Oznaka tamnog načina"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:114
msgid "Light Mode Label"
msgstr "Oznaka svijetlog načina"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:118,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/view.php:54
msgid "Light Mode"
msgstr "Svijetli način"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:128
msgid "Default Color Mode"
msgstr "Zadani način boja"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:136
msgid "Light"
msgstr "Svijetlo"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:137
msgid "Dark"
msgstr "Tamno"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:138
msgid "OS Aware"
msgstr "Prepoznavanje OS-a"

#: framework/premium/features/content-blocks/options/popup.php:326
msgid "Set after how many days the popup will relaunch if the additional close trigger is met."
msgstr "Postavite nakon koliko dana će se skočni prozor ponovno prikazati ako je ispunjen dodatni okidač zatvaranja."

#: framework/premium/features/content-blocks/options/popup.php:446
msgid "Load Content With AJAX"
msgstr "Učitaj sadržaj putem AJAX-a"

#: framework/premium/features/content-blocks/options/popup.php:450
msgid "Enable this option if you want to load the popup content using AJAX."
msgstr "Omogućite ovu opciju ako želite učitati sadržaj skočnog prozora pomoću AJAX-a."

#: framework/premium/features/content-blocks/options/popup.php:459
msgid "Reload Content"
msgstr "Ponovno učitaj sadržaj"

#: framework/premium/features/content-blocks/options/popup.php:466
#: static/js/options/ConditionsManager/ScheduleDate.js:78
msgid "Never"
msgstr "Nikada"

#: framework/premium/features/content-blocks/options/popup.php:467
msgid "Always"
msgstr "Uvijek"

#: framework/premium/features/content-blocks/options/popup.php:469
msgid "Set this option to always if you have dynamic content inside the popup in order to keep everything up to date."
msgstr "Postavite ovu opciju na Uvijek ako unutar skočnog prozora imate dinamički sadržaj kako bi sve ostalo ažurirano."

#: framework/premium/features/content-blocks/options/popup.php:495,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:637
msgid "Popup Visibility"
msgstr "Vidljivost skočnog prozora"

#: framework/premium/features/content-blocks/options/popup.php:573
msgid "Scroll Lock"
msgstr "Zaključavanje pomicanja"

#: framework/premium/features/content-blocks/options/popup.php:250
msgid "Set the button class selector that will trigger popup to close."
msgstr "Postavite klasu gumba koja će zatvoriti skočni prozor."

#: framework/premium/features/content-blocks/options/popup.php:280
msgid "Relaunch Trigger"
msgstr "Okidač za ponovno pokretanje"

#: framework/premium/features/content-blocks/options/popup.php:286
msgid "Never relaunch"
msgstr "Nikada ne pokreći ponovno"

#: framework/premium/features/content-blocks/options/popup.php:297
msgid "Days After Close"
msgstr "Dani nakon zatvaranja"

#: framework/premium/features/content-blocks/options/popup.php:303
msgid "Set after how many days the popup will relaunch."
msgstr "Postavite nakon koliko dana će se skočni prozor ponovno pokrenuti."

#: framework/premium/features/content-blocks/options/popup.php:313
msgid "Days After Form Submit"
msgstr "Dani nakon slanja obrasca"

#: framework/premium/features/content-blocks/options/popup.php:317
msgid "Days After Button Click"
msgstr "Dani nakon klika na gumb"

#: framework/premium/features/content-blocks/options/maintenance.php:16
msgid "Add one or more conditions to display the Maintenance block."
msgstr "Dodajte jedan ili više uvjeta za prikaz bloka održavanja."

#: framework/premium/features/content-blocks/options/popup.php:53
msgid "Popup Display Conditions"
msgstr "Uvjeti prikaza skočnog prozora"

#: framework/premium/features/content-blocks/options/popup.php:54
msgid "Choose where you want this popup to be displayed."
msgstr "Odaberite gdje želite prikazati ovaj skočni prozor."

#: framework/premium/features/content-blocks/options/popup.php:78
msgid "On element click"
msgstr "Na klik elementa"

#: framework/premium/features/content-blocks/options/popup.php:153
msgid "Close Popup On Scroll Back"
msgstr "Zatvori skočni prozor pri pomicanju natrag"

#: framework/premium/features/content-blocks/options/popup.php:216
msgid "Additional Close Trigger"
msgstr "Dodatni okidač za zatvaranje"

#: framework/premium/features/content-blocks/options/popup.php:223
msgid "On form submit"
msgstr "Prilikom slanja obrasca"

#: framework/premium/features/content-blocks/options/popup.php:224
msgid "On button click"
msgstr "Prilikom klika na gumb"

#: framework/premium/features/content-blocks/options/popup.php:235
msgid "The popup will auto-close if a form submit action is detected inside of it."
msgstr "Skočni prozor će se automatski zatvoriti ako se unutar njega detektira slanje obrasca."

#: framework/premium/features/content-blocks/options/popup.php:247
msgid "Button Class Selector"
msgstr "Klasa gumba okidača"

#: framework/premium/features/content-blocks/options/hook.php:41,
#: framework/premium/features/content-blocks/options/nothing_found.php:35
msgid "Choose where you want this content block to be displayed."
msgstr "Odaberite gdje želite prikazati ovaj blok sadržaja."

#: framework/premium/features/content-blocks/options/hook.php:283
msgid "Select a post/page to preview it's content inside the editor while building the hook."
msgstr "Odaberite objavu/stranicu za pregled njezinog sadržaja unutar uređivača prilikom izrade hooka."

#: framework/premium/features/content-blocks/options/maintenance.php:15
msgid "Maintenance Block Display Conditions"
msgstr "Uvjeti prikaza bloka održavanja"

#: framework/premium/features/media-video/options.php:110
msgid "Display a minimalistic view of the video player."
msgstr "Prikažite minimalistički prikaz video playera."

#: framework/premium/features/performance-typography/feature.php:116
msgid "Preconnect Google Fonts"
msgstr "Predučitaj Google fontove"

#: framework/premium/features/performance-typography/feature.php:127
msgid "Preconnect Adobe Typekit Fonts"
msgstr "Predučitaj Adobe Typekit fontove"

#: framework/features/header/items/account/config.php:4
msgid "Account"
msgstr "Račun"

#: framework/features/header/items/account/options.php:12
msgid "User Info"
msgstr "Korisničke informacije"

#: framework/features/header/items/account/options.php:60
msgid "User Avatar"
msgstr "Korisnički avatar"

#: framework/features/header/items/account/options.php:91,
#: framework/features/header/items/account/options.php:658,
#: framework/features/header/items/account/views/login.php:164,
#: framework/features/header/items/account/views/login.php:364
msgid "Edit Profile"
msgstr "Uredi profil"

#: framework/features/header/items/account/options.php:101,
#: framework/features/header/items/account/options.php:105,
#: framework/features/header/items/account/options.php:667,
#: framework/features/header/items/account/views/login.php:170,
#: framework/features/header/items/account/views/login.php:385
msgid "Log Out"
msgstr "Odjava"

#: framework/features/header/items/account/options.php:185,
#: framework/features/header/items/account/options.php:189,
#: framework/features/header/items/account/views/login.php:591
msgid "Dokan Dashboard"
msgstr "Dokan nadzorna ploča"

#: framework/features/header/items/account/options.php:199,
#: framework/features/header/items/account/options.php:203,
#: framework/features/header/items/account/views/login.php:625
msgid "Dokan Shop"
msgstr "Dokan trgovina"

#: framework/features/header/items/account/options.php:215,
#: framework/features/header/items/account/options.php:219,
#: framework/features/header/items/account/views/login.php:657
msgid "Tutor LMS Dashboard"
msgstr "Tutor LMS nadzorna ploča"

#: framework/features/header/items/account/options.php:235,
#: framework/features/header/items/account/views/login.php:684
msgid "bbPress Dashboard"
msgstr "bbPress nadzorna ploča"

#: framework/features/header/items/account/options.php:619,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:32
msgid "Link"
msgstr "Poveznica"

#: framework/features/header/items/account/options.php:630
msgid "Dropdown Items"
msgstr "Stavke padajućeg izbornika"

#: framework/features/header/items/account/options.php:687
msgid "Link To"
msgstr "Poveznica na"

#: framework/premium/extensions/color-mode-switch/includes/logo-enhancements.php:34
msgid "Dark Mode Logo"
msgstr "Logo tamnog načina"

#: framework/premium/extensions/woocommerce-extra/features/sku-layer.php:60,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:248,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:434
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:140
msgid "SKU"
msgstr "SKU"

#: framework/premium/features/content-blocks/options/404.php:84,
#: framework/premium/features/content-blocks/options/archive.php:161,
#: framework/premium/features/content-blocks/options/header.php:102,
#: framework/premium/features/content-blocks/options/hook.php:211,
#: framework/premium/features/content-blocks/options/maintenance.php:81,
#: framework/premium/features/content-blocks/options/nothing_found.php:102,
#: framework/premium/features/content-blocks/options/single.php:93
msgid "Wide"
msgstr "Široko"

#: framework/premium/features/content-blocks/options/archive.php:10
msgid "Replace Conditions"
msgstr "Zamijeni uvjete"

#: framework/premium/features/content-blocks/options/archive.php:15
msgid "Template Replace Conditions"
msgstr "Uvjeti zamjene predloška"

#: framework/premium/features/content-blocks/options/archive.php:16,
#: framework/premium/features/content-blocks/options/header.php:36,
#: framework/premium/features/content-blocks/options/single.php:16
msgid "Choose where you want this template to be displayed."
msgstr "Odaberite gdje želite prikazati ovaj predložak."

#: framework/premium/features/content-blocks/options/archive.php:17
msgid "Add Replace Condition"
msgstr "Dodaj uvjet zamjene"

#: framework/premium/features/content-blocks/options/archive.php:142,
#: framework/premium/features/content-blocks/options/single.php:74
msgid "Left Sidebar"
msgstr "Lijeva bočna traka"

#: framework/premium/features/content-blocks/options/archive.php:147,
#: framework/premium/features/content-blocks/options/single.php:79
msgid "Right Sidebar"
msgstr "Desna bočna traka"

#: framework/premium/features/content-blocks/options/header.php:35,
#: framework/premium/features/content-blocks/options/single.php:15
msgid "Template Display Conditions"
msgstr "Uvjeti prikaza predloška"

#: framework/premium/features/content-blocks/options/hook.php:40
msgid "Content Block Display Conditions"
msgstr "Uvjeti prikaza bloka sadržaja"

#. translators: placeholder here means the actual URL.
#: framework/premium/features/media-video/options.php:77
msgid "YouTube won't store information about visitors on your website unless they play the video. More info about this can be found %shere%s."
msgstr "YouTube neće pohraniti informacije o posjetiteljima vaše web stranice osim ako ne pokrenu video. Više informacija možete pronaći %shere%s."

#: framework/premium/features/media-video/options.php:91
msgid "Autoplay Video"
msgstr "Automatska reprodukcija videa"

#: framework/premium/features/media-video/options.php:94
msgid "Automatically start video playback after the gallery is loaded."
msgstr "Automatski započnite reprodukciju videa nakon što se galerija učita."

#: framework/premium/features/media-video/options.php:99
msgid "Loop Video"
msgstr "Ponavljanje videa"

#: framework/premium/features/media-video/options.php:102
msgid "Start video again after it ends."
msgstr "Pokrenite video ponovno nakon završetka."

#: framework/premium/features/media-video/options.php:107
msgid "Simplified Player"
msgstr "Pojednostavljeni player"

#: framework/premium/features/content-blocks/hooks-manager.php:720
msgid "After single product \"Add to cart\" button"
msgstr "Nakon gumba \"Dodaj u košaricu\" na pojedinačnoj stranici proizvoda"

#: framework/premium/features/content-blocks/hooks-manager.php:725
msgid "Before single product meta"
msgstr "Prije pojedinačne meta oznake proizvoda"

#: framework/premium/features/content-blocks/hooks-manager.php:730
msgid "After single product meta"
msgstr "Nakon pojedinačne meta oznake proizvoda"

#: framework/premium/features/media-video/options.php:6
msgid "Video Source"
msgstr "Izvor videa"

#: framework/premium/features/media-video/options.php:25
msgid "Upload Video"
msgstr "Prenesi video"

#: framework/premium/features/media-video/options.php:29
msgid "Upload an MP4 file into the media library."
msgstr "Prenesite MP4 datoteku u medijsku biblioteku."

#: framework/premium/features/media-video/options.php:42
msgid "YouTube Url"
msgstr "YouTube URL"

#: framework/premium/features/media-video/options.php:44
msgid "Enter a valid YouTube media URL."
msgstr "Unesite ispravnu YouTube poveznicu."

#: framework/premium/features/media-video/options.php:57
msgid "Vimeo Url"
msgstr "Vimeo URL"

#: framework/premium/features/media-video/options.php:59
msgid "Enter a valid Vimeo media URL."
msgstr "Unesite ispravnu Vimeo poveznicu."

#: framework/premium/features/media-video/options.php:72
msgid "YouTube Privacy Enhanced Mode"
msgstr "YouTube način poboljšane privatnosti"

#: framework/premium/features/content-blocks/hooks-manager.php:234
msgid "After first post meta"
msgstr "Nakon prve meta oznake objave"

#: framework/premium/features/content-blocks/hooks-manager.php:242
msgid "After second post meta"
msgstr "Nakon druge meta oznake objave"

#: framework/premium/features/content-blocks/hooks-manager.php:580
msgid "Offcanvas Cart - Empty State"
msgstr "Platno košarice - Prazno stanje"

#: framework/premium/features/content-blocks/hooks-manager.php:705
msgid "Before single product gallery"
msgstr "Prije pojedinačne galerije proizvoda"

#: framework/premium/features/content-blocks/hooks-manager.php:710
msgid "After single product gallery"
msgstr "Nakon pojedinačne galerije proizvoda"

#: framework/premium/features/content-blocks/hooks-manager.php:715
msgid "Before single product \"Add to cart\" button"
msgstr "Prije gumba \"Dodaj u košaricu\" na pojedinačnoj stranici proizvoda"

#: framework/premium/extensions/mega-menu/options.php:532,
#: framework/premium/extensions/mega-menu/options.php:875
msgid "Badge Settings"
msgstr "Postavke oznaka"

#: framework/premium/extensions/mega-menu/options.php:764
msgid "Column Background"
msgstr "Pozadina stupca"

#: framework/premium/extensions/shortcuts/customizer.php:673,
#: framework/premium/extensions/shortcuts/customizer.php:699,
#: framework/premium/extensions/shortcuts/views/bar.php:55,
#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:241,
#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:245,
#: framework/premium/extensions/woocommerce-extra/features/compare/helpers.php:110,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:36,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:418,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/bar.php:53,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/config.php:4,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:140,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/view.php:97
msgid "Compare"
msgstr "Usporedi"

#: framework/premium/extensions/shortcuts/customizer.php:1124
msgid "Item Background Color"
msgstr "Boja pozadine stavke"

#: framework/premium/extensions/shortcuts/customizer.php:1205
msgid "Wishlist Badge Color"
msgstr "Boja oznake liste želja"

#: framework/premium/extensions/shortcuts/customizer.php:1247
msgid "Compare Badge Color"
msgstr "Boja oznake usporedbe"

#: framework/premium/extensions/sidebars/extension.php:185
msgid "Remove Widget Area"
msgstr "Ukloni područje widgeta"

#: framework/premium/extensions/woocommerce-extra/config.php:21
msgid "This extension requires the WooCommerce plugin to be installed and activated."
msgstr "Ova ekstenzija zahtijeva da WooCommerce dodatak bude instaliran i aktiviran."

#: framework/premium/extensions/woocommerce-extra/extension.php:359
msgid "Cart Page"
msgstr "Stranica košarice"

#: framework/premium/features/content-blocks/admin-ui.php:269
msgid "Nothing Found"
msgstr "Ništa nije pronađeno"

#: framework/premium/features/content-blocks/admin-ui.php:270
msgid "Maintenance"
msgstr "Održavanje"

#: framework/premium/features/content-blocks/admin-ui.php:374
msgid "On click to element"
msgstr "Na klik elementa"

#: framework/extensions/product-reviews/extension.php:321,
#: framework/premium/features/content-blocks/content-block-layer.php:194,
#: framework/premium/features/content-blocks/content-block-layer.php:244,
#: framework/premium/extensions/woocommerce-extra/features/sku-layer.php:64,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:739,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:617,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:668,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:339,
#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:203,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:190,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:251,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:274,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:903,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-layer.php:246
msgid "Bottom Spacing"
msgstr "Razmak na dnu"

#: framework/premium/features/content-blocks/hooks-manager.php:194
msgid "Before first post meta"
msgstr "Prije prve meta oznake objave"

#: framework/premium/features/content-blocks/hooks-manager.php:202
msgid "Before second post meta"
msgstr "Prije druge meta oznake objave"

#: framework/features/conditions/rules/woo.php:80,
#: framework/features/conditions/rules/woo.php:95
msgid "Product with Taxonomy ID"
msgstr "Proizvod s ID-jem taksonomije"

#: framework/premium/extensions/color-mode-switch/extension.php:96
msgid "Dark Mode Color Palette"
msgstr "Tamni način rada - paleta boja"

#: framework/premium/extensions/mega-menu/options.php:72
msgid "Dropdown Custom Width"
msgstr "Prilagođena širina padajućeg izbornika"

#: framework/premium/extensions/mega-menu/options.php:309
msgid "AJAX Content Loading"
msgstr "Učitavanje sadržaja putem AJAX-a"

#: framework/premium/extensions/mega-menu/options.php:312
msgid "If you have complex data inside your mega menu you can enable this option in order to load the dropdown content with AJAX and improve the website loading time."
msgstr "Ako imate složene podatke unutar mega izbornika, omogućite ovu opciju kako biste učitali sadržaj izbornika pomoću AJAX-a i poboljšali brzinu učitavanja web stranice."

#: framework/premium/extensions/mega-menu/options.php:403
msgid "Content Visibility"
msgstr "Vidljivost sadržaja"

#: framework/premium/features/clone-cpt.php:127
msgid "Post creation failed, could not find original post: "
msgstr "Neuspjeh pri kreiranju objave, izvorna objava nije pronađena: "

#: framework/premium/features/clone-cpt.php:184
msgid "Post copy created."
msgstr "Kopija objave kreirana."

#: framework/premium/features/local-gravatars.php:34
msgid "Store Gravatars Locally"
msgstr "Pohrani Gravatarske slike lokalno"

#: framework/premium/features/local-gravatars.php:39
msgid "Store and load Gravatars locally for increased privacy and performance."
msgstr "Pohrani i učitaj Gravatarske slike lokalno radi povećane privatnosti i bolje performanse."

#: framework/premium/features/premium-header.php:314,
#: framework/premium/features/socials.php:13,
#: framework/features/blocks/contact-info/options.php:103,
#: framework/features/blocks/contact-info/options.php:168,
#: framework/features/blocks/contact-info/options.php:231,
#: framework/features/blocks/contact-info/options.php:294,
#: framework/features/blocks/contact-info/options.php:357,
#: framework/features/blocks/contact-info/options.php:420,
#: framework/features/blocks/contact-info/options.php:483,
#: framework/features/header/items/account/options.php:375,
#: framework/features/header/items/account/options.php:756,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:9
msgid "Icon Source"
msgstr "Izvor ikone"

#: framework/premium/features/socials.php:41
msgid "URL Source"
msgstr "Izvor URL-a"

#: framework/premium/features/socials.php:59
msgid "Custom URL"
msgstr "Prilagođeni URL"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:60
msgid "Form Style"
msgstr "Stil obrasca"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:68
msgid "Stacked"
msgstr "Naslagano"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:137,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:152
msgid "Your email *"
msgstr "Vaš e-mail *"

#: framework/extensions/newsletter-subscribe/providers/demo.php:16
msgid "Demo List"
msgstr "Demo popis"

#: framework/features/conditions/rules/basic.php:10,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:12
msgid "All Products"
msgstr "Svi proizvodi"

#: framework/features/conditions/rules/basic.php:40
msgid "All Singulars"
msgstr "Sve pojedinačne stranice"

#: framework/features/conditions/rules/basic.php:51
msgid "All Archives"
msgstr "Sve arhive"

#: framework/features/conditions/rules/date-time.php:5
msgid "Date & Time"
msgstr "Datum i vrijeme"

#: framework/features/conditions/rules/date-time.php:9
msgid "Range Date/Time"
msgstr "Raspon datuma/vremena"

#: framework/features/conditions/rules/date-time.php:14
#: static/js/options/ConditionsManager/ScheduleDate.js:143
msgid "Recurring Days"
msgstr "Ponavljajući dani"

#: framework/features/conditions/rules/requests.php:5
msgid "Requests"
msgstr "Zahtjevi"

#: framework/features/conditions/rules/requests.php:9
msgid "Request Referer"
msgstr "Referentni zahtjev"

#: framework/features/conditions/rules/requests.php:14
msgid "Request Cookie"
msgstr "Kolačić zahtjeva"

#: framework/features/conditions/rules/requests.php:19
msgid "Request URL"
msgstr "URL zahtjeva"

#: framework/features/conditions/rules/specific.php:31
msgid "Post with Author ID"
msgstr "Objava s ID-jem autora"

#: framework/features/conditions/rules/woo.php:75,
#: framework/features/conditions/rules/woo.php:90
msgid "Product ID"
msgstr "ID proizvoda"

#: framework/extensions/cookies-consent/customizer.php:199
msgid "Accept Button"
msgstr "Gumb prihvati"

#: framework/extensions/cookies-consent/customizer.php:266
msgid "Decline Button"
msgstr "Gumb odbij"

#: framework/extensions/cookies-consent/extension.php:105
msgid "Please accept the Privacy Policy in order to comment."
msgstr "Molimo prihvatite pravila privatnosti kako biste mogli komentirati."

#: framework/extensions/cookies-consent/extension.php:106
msgid "Comment Submission Failure"
msgstr "Neuspješno slanje komentara"

#: framework/extensions/newsletter-subscribe/customizer.php:28,
#: framework/extensions/newsletter-subscribe/helpers.php:24,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:15
msgid "Enter your email address below and subscribe to our newsletter"
msgstr "Unesite svoju e-mail adresu ispod i pretplatite se na naš newsletter"

#: framework/extensions/newsletter-subscribe/customizer.php:389
msgid "Container Shadow"
msgstr "Sjena kontejnera"

#: framework/extensions/trending/customizer.php:588
msgid "Module Title Font"
msgstr "Font naslova modula"

#: framework/extensions/trending/customizer.php:596
msgid "Module Title Color"
msgstr "Boja naslova modula"

#: framework/extensions/trending/customizer.php:647
msgid "Posts Title Font"
msgstr "Font naslova objava"

#: framework/extensions/trending/customizer.php:656
msgid "Posts Title Font Color"
msgstr "Boja fonta naslova objava"

#: framework/extensions/trending/customizer.php:871
msgid "Arrows Color"
msgstr "Boja strelica"

#: framework/premium/features/clone-cpt.php:42,
#: framework/premium/features/clone-cpt.php:45
msgid "Duplicate"
msgstr "Dupliciraj"

#: framework/premium/features/clone-cpt.php:55
msgid "No post to duplicate"
msgstr "Nema objava za dupliciranje"

#: framework/helpers/exts-configs.php:314
msgid "Create a customized order “Thank You” page for your customers, giving them a personalized experience."
msgstr "Kreirajte prilagođenu stranicu \"Hvala na narudžbi\" za vaše kupce, pružajući im personalizirano iskustvo."

#: framework/helpers/exts-configs.php:322,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:5
msgid "Advanced Reviews"
msgstr "Napredne recenzije"

#: framework/helpers/exts-configs.php:323
msgid "Enhance your WooCommerce reviews with rich content, images and a thumbs up system that help your shoppers find the perfect product."
msgstr "Poboljšajte WooCommerce recenzije bogatim sadržajem, slikama i sustavom glasanja kako biste pomogli kupcima pronaći savršen proizvod."

#: framework/extensions/cookies-consent/customizer.php:112
msgid "Forms Cookies Content"
msgstr "Kolačići sadržaja obrazaca"

#: framework/helpers/exts-configs.php:362
msgid "Better management for affiliate products with a few simple options that strengthen the external integration with these."
msgstr "Bolje upravljanje affiliate proizvodima s jednostavnim opcijama koje jačaju vanjsku integraciju."

#: framework/helpers/exts-configs.php:295
msgid "Custom Tabs"
msgstr "Prilagođene kartice"

#: framework/helpers/exts-configs.php:296
msgid "Present additional information about your products by adding new custom tabs to the product information section."
msgstr "Predstavite dodatne informacije o svojim proizvodima dodavanjem novih prilagođenih kartica u odjeljak s informacijama o proizvodu."

#: framework/helpers/exts-configs.php:304,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:58,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:62,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:272,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/helpers.php:47,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/modal-view.php:18,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/side-view.php:27
msgid "Size Guide"
msgstr "Vodič za veličinu"

#: framework/helpers/exts-configs.php:305
msgid "Show a size chart guide so that your visitors can pick the right size for them when ordering a product."
msgstr "Prikažite vodič za veličinu kako bi vaši posjetitelji mogli odabrati odgovarajuću veličinu prilikom naručivanja proizvoda."

#: framework/helpers/exts-configs.php:313
msgid "Custom Thank You Pages"
msgstr "Prilagođene stranice zahvale"

#: framework/helpers/exts-configs.php:279
msgid "Catch the attention of your clients by showcasing your product variations as colour, image or button swatches."
msgstr "Privucite pažnju svojih klijenata prikazivanjem varijacija proizvoda u obliku boja, slika ili gumba."

#: framework/helpers/exts-configs.php:286,
#: framework/features/conditions/rules/woo.php:42
msgid "Product Brands"
msgstr "Brendovi proizvoda"

#: framework/helpers/exts-configs.php:287
msgid "Categorise products by brands and show their logo in archive or single page so users could discover more about their makers."
msgstr "Kategorizirajte proizvode prema brendovima i prikažite njihov logotip u arhivi ili na pojedinačnoj stranici kako bi korisnici mogli saznati više o proizvođačima."

#: framework/helpers/exts-configs.php:361
msgid "Affiliate Product Links"
msgstr "Poveznice za affiliate proizvode"

#: framework/helpers/exts-configs.php:339
msgid "Replace the standard product gallery with additional layouts which can showcase the photos as a grid or even a slider."
msgstr "Zamijenite standardnu galeriju proizvoda dodatnim izgledima koji mogu prikazati slike u mreži ili čak kao klizač."

#: framework/helpers/exts-configs.php:354
msgid "Search by SKU"
msgstr "Pretraživanje prema SKU"

#: framework/helpers/exts-configs.php:355
msgid "Advanced searching for products by their SKU classification can be useful in cases of vast product catalogues."
msgstr "Napredno pretraživanje proizvoda prema SKU klasifikaciji može biti korisno u slučajevima velikih kataloga proizvoda."

#: framework/helpers/exts-configs.php:331
msgid "Add a visual cue that tells your visitors how much the cart total must be to be able to benefit of free shipping."
msgstr "Dodajte vizualni element koji će vašim posjetiteljima pokazati koliko je preostalo do iznosa koji će im omogućiti besplatnu dostavu."

#: framework/helpers/exts-configs.php:278,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:4
msgid "Variation Swatches"
msgstr "Varijacijski uzorci"

#: framework/helpers/exts-configs.php:271
msgid "Compare products with a clear and concise table system that gives your users a way to make a quick decision."
msgstr "Usporedite proizvode s jasnim i sažetim tabličnim sustavom koji omogućuje korisnicima brzu odluku."

#: framework/helpers/exts-configs.php:346
msgid "Product Share Box"
msgstr "Okvir za dijeljenje proizvoda"

#: framework/helpers/exts-configs.php:347
msgid "Enable social sharing abilities for products available on the site, letting even more users discover your great shop selection."
msgstr "Omogućite dijeljenje proizvoda na društvenim mrežama kako bi još više korisnika otkrilo vašu ponudu."

#: framework/helpers/exts-configs.php:338
msgid "Advanced Gallery"
msgstr "Napredna galerija"

#: framework/helpers/exts-configs.php:247
msgid "Preview the available products and let your users make quick and informative decisions about their purchase."
msgstr "Pregledajte dostupne proizvode i omogućite korisnicima brzo i informirano donošenje odluka o kupnji."

#: framework/helpers/exts-configs.php:254,
#: framework/premium/extensions/shortcuts/views/bar.php:54
msgid "Filters"
msgstr "Filteri"

#: framework/helpers/exts-configs.php:255
msgid "Drill down the product list with new filtering widgets, an off canvas area for them and showing the active filters on the page."
msgstr "Precizno filtrirajte popis proizvoda pomoću novih filter widgeta, izvanplatnenog područja i prikaza aktivnih filtera na stranici."

#: framework/helpers/exts-configs.php:263
msgid "A set of features that lets you create easily your dream products wishlists and share them with friends and family."
msgstr "Skup značajki koji omogućuje lako kreiranje lista želja i njihovo dijeljenje s prijateljima i obitelji."

#: framework/helpers/exts-configs.php:270
msgid "Compare View"
msgstr "Pregled za usporedbu"

#: framework/helpers/exts-configs.php:239
msgid "Adds the “add to cart” actions to the product page as a floating bar if the product summary has disappeared from view."
msgstr "Dodaje radnje \"dodaj u košaricu\" na stranicu proizvoda kao plutajuću traku ako je sažetak proizvoda nestao iz prikaza."

#: framework/helpers/exts-configs.php:246,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:58
msgid "Quick View"
msgstr "Brzi pregled"

#: framework/helpers/exts-configs.php:157
msgid "Let your guests easily filter the posts by their category or tags taxonomy terms, instantly drilling down the listings."
msgstr "Omogućite gostima jednostavno filtriranje objava prema kategoriji ili oznakama, trenutno pretražujući popise."

#: framework/helpers/exts-configs.php:163
msgid "Taxonomy Customisations"
msgstr "Prilagodbe taksonomije"

#: framework/helpers/exts-configs.php:164
msgid "Additional customisation options for your taxonomies such as hero backgrounds and custom colour labels."
msgstr "Dodatne opcije prilagodbe za vaše taksonomije, poput hero pozadina i prilagođenih oznaka boja."

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:227
msgctxt "Extension Brand Name"
msgid "Shop Extra"
msgstr ""

#: framework/helpers/exts-configs.php:143
msgid "Display the approximate reading time of an article, so that visitors know what to expect when starting to read the content."
msgstr "Prikaz približnog vremena čitanja članka kako bi posjetitelji znali što očekivati prije početka čitanja sadržaja."

#: framework/helpers/exts-configs.php:149
#: static/js/editor/blocks/dynamic-data/index.js:100
msgid "Dynamic Data"
msgstr "Dinamički podaci"

#: framework/helpers/exts-configs.php:150
msgid "Integrates custom fields solutions in the meta layers of a post and presents additional information."
msgstr "Integrira prilagođena polja u meta slojeve objave i prikazuje dodatne informacije."

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:83
msgctxt "Extension Brand Name"
msgid "Color Mode Switch"
msgstr ""

#: framework/helpers/exts-configs.php:84
msgid "Add a dark colour scheme and switch to your website, which will make it pleasant to look at in low light environments."
msgstr "Dodajte tamni način rada i preklopnik boja na svoju web stranicu kako bi bila ugodnija za gledanje u uvjetima slabog osvjetljenja."

#. translators: 1: link open 2: link close
#: framework/features/account-auth.php:253
msgid "Registration complete. Please check your email, then visit the %1$slogin page%2$s."
msgstr "Registracija završena. Provjerite svoj e-mail, zatim posjetite %1$sstranicu za prijavu%2$s."

#: framework/dashboard.php:34,
#: framework/features/header/items/account/options.php:23,
#: framework/features/header/items/account/options.php:73,
#: framework/features/header/items/account/options.php:77,
#: framework/features/header/items/account/options.php:649,
#: framework/features/header/items/account/views/login.php:158,
#: framework/features/header/items/account/views/login.php:335
msgid "Dashboard"
msgstr "Nadzorna ploča"

#: framework/dashboard.php:521
msgid "You do not have sufficient permissions to access this page."
msgstr "Nemate dovoljno dopuštenja za pristup ovoj stranici."

#: framework/theme-integration.php:221,
#: framework/features/blocks/share-box/options.php:25
msgid "X (Twitter)"
msgstr "X (Twitter)"

#: framework/theme-integration.php:234
msgid "Mastodon"
msgstr "Mastodon"

#: framework/features/conditions/rules/pages.php:39
msgid "Privacy Policy Page"
msgstr "Stranica pravila privatnosti"

#: framework/features/conditions/rules/pages.php:46
msgid "Author Archives"
msgstr "Arhiva autora"

#: framework/features/conditions/rules/woo.php:104,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:42
msgid "WooCommerce"
msgstr "WooCommerce"

#: framework/features/conditions/rules/woo.php:15
msgid "Shop Home"
msgstr "Početna trgovina"

#: framework/features/conditions/rules/woo.php:20,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:66
msgid "Single Product"
msgstr "Pojedinačni proizvod"

#: framework/features/conditions/rules/woo.php:25
msgid "Product Archives"
msgstr "Arhive proizvoda"

#: framework/features/conditions/rules/woo.php:30,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:91
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:27
msgid "Product Categories"
msgstr "Kategorije proizvoda"

#: framework/features/conditions/rules/woo.php:49
msgid "Product Tags"
msgstr "Oznake proizvoda"

#: framework/features/conditions/rules/cpt.php:53
msgid "Custom Post Types"
msgstr "Prilagođene vrste objava"

#: framework/features/conditions/rules/localization.php:15
msgid "Current Language"
msgstr "Trenutni jezik"

#: framework/features/conditions/rules/bbPress.php:11,
#: framework/features/header/items/account/options.php:231
msgid "bbPress"
msgstr "bbPress"

#: framework/features/conditions/rules/bbPress.php:15,
#: framework/features/header/items/account/options.php:24,
#: framework/features/header/items/account/options.php:87
msgid "Profile"
msgstr "Profil"

#: framework/extensions/newsletter-subscribe/helpers.php:142
msgid "First name"
msgstr "Ime"

#: framework/extensions/trending/customizer.php:537
msgid "Display Location"
msgstr "Mjesto prikaza"

#: framework/extensions/trending/customizer.php:545
msgid "Before Footer"
msgstr "Prije podnožja"

#: framework/extensions/trending/customizer.php:550
msgid "After Footer"
msgstr "Nakon podnožja"

#: framework/extensions/trending/customizer.php:555
msgid "After Header"
msgstr "Nakon zaglavlja"

#: framework/extensions/trending/customizer.php:572
msgid "Trending Block Display Conditions"
msgstr "Uvjeti prikaza bloka s trendovima"

#: framework/extensions/trending/customizer.php:573
msgid "Add one or more conditions to display the trending block."
msgstr "Dodajte jedan ili više uvjeta za prikaz bloka s trendovima."

#: framework/premium/features/content-blocks/admin-ui.php:641
msgid "Hide Hooks"
msgstr "Sakrij hookove"

#: framework/premium/features/content-blocks/admin-ui.php:642
msgid "Show Hooks"
msgstr "Prikaži hookove"

#: framework/premium/features/content-blocks/admin-ui.php:698
msgid "Hide Theme Hooks"
msgstr "Sakrij hookove teme"

#: framework/premium/features/content-blocks/admin-ui.php:699
msgid "Show Theme Hooks"
msgstr "Prikaži hookove teme"

#: framework/premium/features/content-blocks/admin-ui.php:707
msgid "Hide WooCommerce Hooks"
msgstr "Sakrij WooCommerce hookove"

#: framework/premium/features/content-blocks/admin-ui.php:708
msgid "Show WooCommerce Hooks"
msgstr "Prikaži WooCommerce hookove"

#: framework/features/header/modal/register.php:76
msgid "A link to set a new password will be sent to your email address."
msgstr "Poveznica za postavljanje nove lozinke bit će poslana na vašu e-mail adresu."

#: framework/premium/extensions/code-snippets/extension.php:31,
#: framework/premium/extensions/code-snippets/extension.php:86,
#: framework/premium/extensions/code-snippets/extension.php:131
msgid "Header scripts"
msgstr "Skripte zaglavlja"

#: framework/premium/extensions/code-snippets/extension.php:53,
#: framework/premium/extensions/code-snippets/extension.php:108,
#: framework/premium/extensions/code-snippets/extension.php:153
msgid "Footer scripts"
msgstr "Skripte podnožja"

#: framework/premium/extensions/mega-menu/options.php:377,
#: framework/premium/extensions/mega-menu/options.php:385,
#: framework/premium/features/content-blocks/content-block-layer.php:170,
#: framework/premium/features/content-blocks/content-block-layer.php:178,
#: framework/premium/features/content-blocks/content-block-layer.php:221,
#: framework/premium/features/content-blocks/content-block-layer.php:229,
#: framework/features/header/items/account/options.php:254,
#: framework/features/header/items/account/options.php:262,
#: framework/premium/features/premium-header/items/content-block/options.php:13,
#: framework/premium/features/premium-header/items/content-block/options.php:21
#: framework/premium/static/js/blocks/ContentBlock.js:110
msgid "Select Content Block"
msgstr "Odaberite blok sadržaja"

#: framework/premium/extensions/mega-menu/options.php:380,
#: framework/premium/features/content-blocks/content-block-layer.php:173,
#: framework/premium/features/content-blocks/content-block-layer.php:224,
#: framework/features/header/items/account/options.php:257,
#: framework/premium/features/premium-header/items/content-block/options.php:16
msgid "Create a new content Block/Hook"
msgstr "Kreirajte novi blok sadržaja/hook"

#: framework/premium/extensions/mega-menu/options.php:637
msgid "Heading Font"
msgstr "Font naslova"

#: framework/premium/features/content-blocks/admin-ui.php:155
msgid "Enable"
msgstr "Omogući"

#: framework/premium/features/content-blocks/admin-ui.php:156
msgid "Disable"
msgstr "Onemogući"

#: framework/premium/features/content-blocks/admin-ui.php:211
msgid "Enabled %s content block."
msgid_plural "Enabled %s content blocks."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: framework/premium/features/content-blocks/admin-ui.php:236
msgid "Disabled %s content block."
msgid_plural "Disabled %s content blocks."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: framework/premium/features/content-blocks/admin-ui.php:264
msgid "404 Page"
msgstr "Stranica 404"

#: framework/premium/features/content-blocks/admin-ui.php:267,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:16,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:64
msgid "Archive"
msgstr "Arhiva"

#: framework/premium/features/content-blocks/admin-ui.php:268,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:17,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:65
msgid "Single"
msgstr "Pojedinačno"

#: framework/premium/features/content-blocks/hooks-manager.php:39
msgid "WP body open"
msgstr "WP otvaranje tijela"

#: framework/premium/features/content-blocks/hooks-manager.php:340
msgid "Before related posts"
msgstr "Prije povezanih objava"

#: framework/premium/features/content-blocks/hooks-manager.php:341,
#: framework/premium/features/content-blocks/hooks-manager.php:348,
#: framework/premium/features/content-blocks/hooks-manager.php:355,
#: framework/premium/features/content-blocks/hooks-manager.php:362,
#: framework/premium/features/content-blocks/hooks-manager.php:369,
#: framework/premium/features/content-blocks/hooks-manager.php:376,
#: framework/premium/features/content-blocks/hooks-manager.php:383,
#: framework/premium/features/content-blocks/hooks-manager.php:390,
#: framework/premium/features/content-blocks/hooks-manager.php:398,
#: framework/premium/features/content-blocks/hooks-manager.php:405
msgid "Related posts"
msgstr "Povezane objave"

#: framework/premium/features/content-blocks/hooks-manager.php:347
msgid "Related posts top"
msgstr "Vrh povezanih objava"

#: framework/premium/features/content-blocks/hooks-manager.php:368
msgid "Card top"
msgstr "Vrh kartice"

#: framework/premium/features/content-blocks/hooks-manager.php:375
msgid "Before featured image"
msgstr "Prije istaknute slike"

#: framework/premium/features/content-blocks/hooks-manager.php:382
msgid "After featured image"
msgstr "Nakon istaknute slike"

#: framework/premium/features/content-blocks/hooks-manager.php:389
msgid "Card bottom"
msgstr "Dno kartice"

#: framework/premium/features/content-blocks/hooks-manager.php:397
msgid "Related posts bottom"
msgstr "Dno povezanih objava"

#: framework/premium/features/content-blocks/hooks-manager.php:404
msgid "After related posts"
msgstr "Nakon povezanih objava"

#: framework/premium/features/content-blocks/hooks-manager.php:568
msgid "Offcanvas Filters - Top"
msgstr "Izvanplatneni filteri - Vrh"

#: framework/premium/features/content-blocks/hooks-manager.php:574
msgid "Offcanvas Filters - Bottom"
msgstr "Izvanplatneni filteri - Dno"

#: framework/premium/features/content-blocks/options/archive.php:31,
#: framework/premium/features/content-blocks/options/single.php:30
msgid "Replacement Behavior"
msgstr "Ponašanje zamjene"

#: framework/premium/features/content-blocks/options/archive.php:38
msgid "Only Card"
msgstr "Samo kartica"

#: framework/premium/features/content-blocks/options/archive.php:39,
#: framework/premium/features/content-blocks/options/single.php:38
msgid "Full Page"
msgstr "Cijela stranica"

#: framework/premium/features/content-blocks/options/404.php:51,
#: framework/premium/features/content-blocks/options/archive.php:118,
#: framework/premium/features/content-blocks/options/maintenance.php:48,
#: framework/premium/features/content-blocks/options/nothing_found.php:69,
#: framework/premium/features/content-blocks/options/single.php:50
msgid "Page Structure"
msgstr "Struktura stranice"

#: framework/premium/features/content-blocks/options/single.php:37
msgid "Content Area"
msgstr "Područje sadržaja"

#: framework/premium/features/content-blocks/options/single.php:99
msgid "Content Area Vel Spacing"
msgstr "Okomiti razmak područja sadržaja"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:81
msgid "Filter Source"
msgstr "Izvor filtera"

#: framework/features/blocks/contact-info/options.php:590,
#: framework/premium/features/premium-header/items/contacts/options.php:6
msgid "Items Direction"
msgstr "Smjer stavki"

#: framework/features/blocks/contact-info/options.php:597,
#: framework/premium/features/premium-header/items/contacts/options.php:14
msgid "Horizontal"
msgstr "Vodoravno"

#: framework/features/blocks/contact-info/options.php:596,
#: framework/premium/features/premium-header/items/contacts/options.php:13
msgid "Vertical"
msgstr "Okomito"

#: framework/premium/features/premium-header/items/contacts/options.php:889,
#: framework/premium/features/premium-header/items/contacts/options.php:931,
#: framework/premium/features/premium-header/items/contacts/options.php:969,
#: framework/premium/features/premium-header/items/contacts/options.php:1007
#: static/js/editor/blocks/about-me/Edit.js:148
#: static/js/editor/blocks/contact-info/Edit.js:162
msgid "Icons Background Color"
msgstr "Boja pozadine ikona"

#: framework/premium/features/premium-header/items/contacts/options.php:893,
#: framework/premium/features/premium-header/items/contacts/options.php:935,
#: framework/premium/features/premium-header/items/contacts/options.php:973,
#: framework/premium/features/premium-header/items/contacts/options.php:1011
#: static/js/editor/blocks/about-me/Edit.js:179
#: static/js/editor/blocks/contact-info/Edit.js:193
msgid "Icons Border Color"
msgstr "Boja obruba ikona"

#: framework/premium/static/js/options/IconPicker/CustomIcon.js:22
msgid "Click to upload"
msgstr "Kliknite za prijenos"

#: framework/premium/static/js/options/IconPicker/Modal.js:135
msgid "All Icons"
msgstr "Sve ikone"

#: static/js/options/ConditionsManager/SingleCondition.js:296
msgid "All authors"
msgstr "Svi autori"

#: static/js/dashboard/screens/DemoInstall/components/Error.js:25
msgid "Can't Import Starter Site"
msgstr "Ne može se uvesti početna stranica"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:31
msgid "Unfortunately, your hosting configuration doesn't meet the minimum requirements for importing a starter site."
msgstr "Nažalost, vaša hosting konfiguracija ne ispunjava minimalne zahtjeve za uvoz početne stranice."

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/views/panel.php:62
msgid "Close filters modal"
msgstr "Zatvori modal filtera"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/view.php:45
msgid "Close quick view"
msgstr "Zatvori brzi pregled"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/helpers.php:45
msgid "Quick view toggle"
msgstr "Preklopnik brzog pregleda"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/helpers.php:32
msgid "Quick view icon"
msgstr "Ikona brzog pregleda"

#: framework/features/header/items/account/options.php:1724,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:314,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:185
msgid "Close Button Type"
msgstr "Vrsta gumba za zatvaranje"

#: framework/features/header/items/account/options.php:726,
#: framework/features/header/items/account/options.php:1731,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:323,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:23,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:194
msgid "Simple"
msgstr "Jednostavno"

#: framework/features/header/items/account/options.php:1732,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:324,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:195
msgid "Border"
msgstr "Obrub"

#: framework/features/header/items/account/options.php:1778,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:366,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:248,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:254,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:298,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:382,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:466
msgid "Border Color"
msgstr "Boja obruba"

#: framework/premium/features/content-blocks/hooks-manager.php:170
msgid "Before description"
msgstr "Prije opisa"

#: framework/premium/features/content-blocks/hooks-manager.php:178
msgid "Before breadcrumbs"
msgstr "Prije navigacijske staze"

#: framework/premium/features/content-blocks/hooks-manager.php:218
msgid "After description"
msgstr "Nakon opisa"

#: framework/premium/features/content-blocks/hooks-manager.php:226
msgid "After breadcrumbs"
msgstr "Nakon navigacijske staze"

#: framework/premium/features/content-blocks/hooks-manager.php:630
msgid "Before shop loop item actions"
msgstr "Prije radnji stavke trgovine"

#: framework/premium/features/content-blocks/hooks-manager.php:635
msgid "After shop loop item actions"
msgstr "Nakon radnji stavke trgovine"

#. translators: placeholder here means the actual URL.
#: framework/features/blocks/socials/options.php:24
msgid "Configure the social links in Customizer ➝ General ➝ %sSocial Network Accounts%s."
msgstr "Konfigurirajte društvene poveznice u Customizer ➝ General ➝ %sRačuni društvenih mreža%s."

#: framework/features/header/items/account/options.php:306
msgid "Customizing: Logged out State"
msgstr "Prilagodba: Odjavljeno stanje"

#: framework/features/header/items/account/options.php:1088
msgid "User Visibility"
msgstr "Vidljivost korisnika"

#: framework/features/header/items/account/options.php:1099,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:116,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:95
msgid "Logged In"
msgstr "Prijavljeni"

#: framework/features/header/items/account/options.php:1100,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:117,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:96
msgid "Logged Out"
msgstr "Odjavljeni"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1385
msgid "Custom Field"
msgstr "Prilagođeno polje"

#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:114
msgid "%s min"
msgid_plural "%s mins"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: framework/premium/features/content-blocks/options/archive.php:98
msgid "Default Card Layout"
msgstr "Zadani izgled kartice"

#: framework/premium/features/content-blocks/options/archive.php:103
msgid "Inherit card wrapper settings from Customizer (background color, spacing, shadow)."
msgstr "Naslijedi postavke kartice iz Customizera (boja pozadine, razmak, sjena)."

#: framework/premium/features/content-blocks/options/archive.php:53,
#: framework/premium/features/content-blocks/options/hook.php:277,
#: framework/premium/features/content-blocks/options/popup.php:483,
#: framework/premium/features/content-blocks/options/single.php:151
msgid "Dynamic Content Preview"
msgstr "Pregled dinamičkog sadržaja"

#: framework/premium/features/content-blocks/options/archive.php:60
msgid "Select a post/page to preview it's content inside the editor while building the archive."
msgstr "Odaberite objavu/stranicu za pregled njezinog sadržaja unutar uređivača prilikom izrade arhive."

#: framework/premium/features/content-blocks/options/archive.php:66
msgid "Editor/Card Width"
msgstr "Širina uređivača/kartice"

#: framework/premium/features/content-blocks/options/archive.php:77
msgid "Set the editor width for better understanging the layout you are building (just for preview purpose, this option won't apply in frontend)."
msgstr "Postavite širinu uređivača za bolje razumijevanje izgleda koji izrađujete (samo za pregled, ova opcija se neće primijeniti na frontend)."

#: framework/premium/features/content-blocks/options/popup.php:203
msgid "After X Pages"
msgstr "Nakon X stranica"

#: framework/premium/features/content-blocks/options/popup.php:209
msgid "Set after how many visited pages the popup block will appear."
msgstr "Postavite nakon koliko posjećenih stranica će se prikazati skočni blok."

#: framework/premium/features/content-blocks/options/popup.php:489,
#: framework/premium/features/content-blocks/options/single.php:157
msgid "Select a post/page to preview it's content inside the editor while building the post/page."
msgstr "Odaberite objavu/stranicu za pregled njezinog sadržaja unutar uređivača prilikom izrade objave/stranice."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:236
msgid "More information on how to generate an API key for Mailerlite can be found %shere%s. Please note that it is required at least one group to be created in your account for the integration to work. More info on how to create a group %shere%s."
msgstr "Više informacija o tome kako generirati API ključ za Mailerlite možete pronaći %shere%s. Napomena: potrebno je kreirati barem jednu grupu u vašem računu da bi integracija funkcionirala. Više informacija o kreiranju grupe %shere%s."

#: framework/premium/static/js/hooks/CodeEditor.js:59
msgid "Code Editor"
msgstr "Uređivač koda"

#: framework/premium/static/js/hooks/CreateHook.js:101
msgid "Template Type"
msgstr "Vrsta predloška"

#: framework/premium/static/js/hooks/CreateHook.js:116
msgid "Archive Template"
msgstr "Arhivski predložak"

#: framework/premium/static/js/hooks/CreateHook.js:124
msgid "Single Template"
msgstr "Pojedinačni predložak"

#: framework/premium/static/js/hooks/CreateHook.js:178
msgid "Hook Name"
msgstr "Naziv hooka"

#: framework/premium/static/js/hooks/CreateHook.js:182
msgid "Popup Name"
msgstr "Naziv skočnog prozora"

#: framework/premium/static/js/hooks/CreateHook.js:186
msgid "Template Name"
msgstr "Naziv predloška"

#: framework/premium/features/content-blocks/admin-ui.php:263,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:52,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:22
#: framework/premium/static/js/hooks/CreateHook.js:77
msgid "Popup"
msgstr "Skočni prozor"

#: framework/premium/static/js/hooks/CreateHook.js:86
msgid "Custom Template"
msgstr "Prilagođeni predložak"

#: framework/premium/static/js/options/IconPicker.js:20
msgid "Theme Icons"
msgstr "Ikone teme"

#: framework/premium/static/js/options/IconPicker.js:26
msgid "FontAwesome Brands"
msgstr "FontAwesome brendovi"

#: framework/premium/static/js/options/IconPicker.js:32
msgid "FontAwesome Solid"
msgstr "FontAwesome solid"

#: framework/premium/static/js/options/IconPicker.js:38
msgid "FontAwesome Regular"
msgstr "FontAwesome regular"

#: framework/premium/static/js/typography/providers/kadence.js:21
#: framework/premium/static/js/typography/providers/plus-addons.js:23
#: framework/premium/static/js/typography/providers/stackable.js:23
msgid "%s Local Google Fonts"
msgstr "%s Lokalni Google fontovi"

#: framework/premium/static/js/typography/providers/kadence.js:26
#: framework/premium/static/js/typography/providers/plus-addons.js:27
#: framework/premium/static/js/typography/providers/stackable.js:27
msgid "%s Typekit"
msgstr "%s Typekit"

#: framework/premium/static/js/typography/providers/kadence.js:31
#: framework/premium/static/js/typography/providers/stackable.js:31
msgid "%s Custom Fonts"
msgstr "%s Prilagođeni fontovi"

#: framework/premium/static/js/typography/providers/kadence.js:59
msgid "Normal"
msgstr "Normalno"

#: framework/premium/static/js/typography/providers/kadence.js:83
msgid "Inherit"
msgstr "Naslijedi"

#: framework/premium/static/js/typography/providers/plus-addons.js:31
msgid "%s Custom"
msgstr "%s Prilagođeno"

#: framework/premium/static/js/typography/providers/plus-addons.js:35
msgid "%s System"
msgstr "%s Sustav"

#: framework/premium/features/premium-header.php:22,
#: framework/premium/features/premium-header.php:58
msgid "Mobile Menu 1"
msgstr "Mobilni izbornik 1"

#: framework/premium/features/premium-header.php:59,
#: framework/premium/features/premium-header/items/mobile-menu-secondary/config.php:4
msgid "Mobile Menu 2"
msgstr "Mobilni izbornik 2"

#: framework/extensions/newsletter-subscribe/providers/active-campaign.php:154,
#: framework/extensions/newsletter-subscribe/providers/brevo.php:116,
#: framework/extensions/newsletter-subscribe/providers/campaign-monitor.php:136,
#: framework/extensions/newsletter-subscribe/providers/convertkit.php:97,
#: framework/extensions/newsletter-subscribe/providers/demo.php:40,
#: framework/extensions/newsletter-subscribe/providers/mailerlite-classic.php:104,
#: framework/extensions/newsletter-subscribe/providers/mailerlite-new.php:123,
#: framework/extensions/newsletter-subscribe/providers/mailpoet.php:93
msgid "Thank you for subscribing to our newsletter!"
msgstr "Hvala što ste se pretplatili na naš newsletter!"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:59
msgctxt "Extension Brand Name"
msgid "Adobe Fonts"
msgstr ""

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:70,
#: framework/premium/extensions/code-snippets/extension.php:24,
#: framework/premium/extensions/code-snippets/extension.php:79,
#: framework/premium/extensions/code-snippets/extension.php:122
msgctxt "Extension Brand Name"
msgid "Custom Code Snippets"
msgstr ""

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:96
msgctxt "Extension Brand Name"
msgid "Custom Fonts"
msgstr ""

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:108
msgctxt "Extension Brand Name"
msgid "Local Google Fonts"
msgstr ""

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:123
msgctxt "Extension Brand Name"
msgid "Advanced Menu"
msgstr ""

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:136
msgctxt "Extension Brand Name"
msgid "Post Types Extra"
msgstr ""

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:179,
#: framework/premium/extensions/shortcuts/config.php:5,
#: framework/premium/extensions/shortcuts/customizer.php:751
msgctxt "Extension Brand Name"
msgid "Shortcuts Bar"
msgstr ""

#: framework/premium/extensions/shortcuts/customizer.php:314
msgid "Set link to nofollow"
msgstr "Postavi poveznicu na nofollow"

#: framework/premium/extensions/shortcuts/customizer.php:320
msgid "Custom class"
msgstr "Prilagođena klasa"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:194
msgctxt "Extension Brand Name"
msgid "Multiple Sidebars"
msgstr ""

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:214
msgctxt "Extension Brand Name"
msgid "White Label"
msgstr ""

#: framework/features/blocks/share-box/options.php:132,
#: framework/features/blocks/socials/options.php:84
msgid "Icons Spacing"
msgstr "Razmak ikona"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/feature.php:157
msgid "Add widgets here."
msgstr "Dodajte widgete ovdje."

#. translators: 1: link open 2: link close
#: framework/features/account-auth.php:106
msgid "Check your email for the confirmation link, then visit the %slogin page%s."
msgstr "Provjerite svoj e-mail za poveznicu za potvrdu, zatim posjetite %sstranicu za prijavu%s."

#: framework/features/account-auth.php:226
msgid "Your account was created successfully. Your login details have been sent to your email address. Please visit the %1$slogin page%2$s."
msgstr "Vaš račun je uspješno kreiran. Podaci za prijavu su poslani na vašu e-mail adresu. Posjetite %1$sstranicu za prijavu%2$s."

#: framework/features/account-auth.php:238
msgid "Your account was created successfully and a password has been sent to your email address. Please visit the %1$slogin page%2$s."
msgstr "Vaš račun je uspješno kreiran, a lozinka je poslana na vašu e-mail adresu. Posjetite %1$sstranicu za prijavu%2$s."

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/cookies-consent/config.php:5,
#: framework/extensions/cookies-consent/customizer.php:5
msgctxt "Extension Brand Name"
msgid "Cookies Consent"
msgstr ""

#: framework/extensions/cookies-consent/customizer.php:76
msgid "Accept Button text"
msgstr "Tekst gumba za prihvaćanje"

#: framework/extensions/cookies-consent/customizer.php:85
msgid "Decline Button text"
msgstr "Tekst gumba za odbijanje"

#: framework/extensions/cookies-consent/customizer.php:88,
#: framework/extensions/cookies-consent/helpers.php:11
msgid "Decline"
msgstr "Odbij"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/newsletter-subscribe/config.php:5
msgctxt "Extension Brand Name"
msgid "Newsletter Subscribe"
msgstr ""

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/product-reviews/config.php:5
msgctxt "Extension Brand Name"
msgid "Product Reviews"
msgstr ""

#: framework/extensions/product-reviews/metabox.php:60
msgid "Please note that some of this information (price, sku, brand) won't be displayed on the front-end. It is solely used for Google's Schema.org markup."
msgstr "Napomena: Neke od ovih informacija (cijena, SKU, brend) neće biti prikazane na korisničkom sučelju. One se koriste isključivo za Google Schema.org označavanje."

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/trending/config.php:5,
#: framework/extensions/trending/customizer.php:160
msgctxt "Extension Brand Name"
msgid "Trending Posts"
msgstr ""

#: framework/extensions/trending/customizer.php:477,
#: framework/features/blocks/about-me/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:32,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:133,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:73
msgid "Image Size"
msgstr "Veličina slike"

#: framework/features/header/account-modal.php:25
msgid "Close account modal"
msgstr "Zatvori modal računa"

#: framework/features/header/header-options.php:68
msgid "Effect"
msgstr "Efekt"

#: framework/features/header/header-options.php:84
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:73
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:68
msgid "Offset"
msgstr "Pomak"

#: framework/premium/features/content-blocks/admin-ui.php:275
msgid "All types"
msgstr "Sve vrste"

#: framework/premium/features/content-blocks/admin-ui.php:378,
#: framework/premium/features/content-blocks/options/popup.php:82
msgid "After x pages"
msgstr "Nakon X stranica"

#: framework/premium/features/content-blocks/popups.php:376
msgid "Close popup"
msgstr "Zatvori skočni prozor"

#: framework/premium/features/media-video/options.php:13
msgid "Upload"
msgstr "Prenesi"

#: framework/premium/static/js/header/CreateHeader.js:117
msgid "Picker header"
msgstr "Zaglavlje birača"

#: framework/premium/static/js/header/CreateHeader.js:134
#: static/js/header/PanelsManager.js:58
msgid "Global Header"
msgstr "Globalno zaglavlje"

#: framework/premium/static/js/header/CreateHeader.js:173
msgid "Create New Header"
msgstr "Kreiraj novo zaglavlje"

#: framework/premium/static/js/header/CreateHeader.js:50
msgid "Create new header"
msgstr "Kreiraj novo zaglavlje"

#: framework/premium/static/js/header/CreateHeader.js:53
msgid "Create a new header and assign it to different pages or posts based on your conditions."
msgstr ""

#: framework/premium/static/js/header/CreateHeader.js:71
msgid "Header name"
msgstr "Da, nastavi"

#: framework/premium/static/js/hooks/CodeEditor.js:238
msgid "Yes, continue"
msgstr "Koristi uređivač koda"

#: framework/premium/static/js/hooks/CodeEditor.js:150
msgid "Use code editor"
msgstr "Izađi iz uređivača koda"

#: framework/premium/static/js/hooks/CodeEditor.js:153
msgid "Exit code editor"
msgstr ""

#: framework/premium/static/js/hooks/CodeEditor.js:165
msgid "Heads up!"
msgstr "Pažnja!"

#: framework/premium/static/js/hooks/CodeEditor.js:167
msgid "Enabling & disabling the code editor will erase everything from your post editor and this action is irreversible."
msgstr "Omogućavanje i onemogućavanje uređivača koda izbrisat će sve iz uređivača objava, a ova radnja je nepovratna."

#: framework/premium/static/js/hooks/CodeEditor.js:174
msgid "Are you sure you want to continue?"
msgstr "Jeste li sigurni da želite nastaviti?"

#: framework/premium/static/js/hooks/CreateHook.js:223
msgid "Create Content Block"
msgstr "Kreiraj blok sadržaja"

#: framework/premium/static/js/hooks/CreateHook.js:36
msgid "Please select the type of your content block and place it in the location you want based on your display and user conditions."
msgstr "Odaberite vrstu predloška..."

#: framework/premium/static/js/hooks/CreateHook.js:108
msgid "Select template type..."
msgstr "Odaberite vrstu bloka sadržaja i mjesto prikaza na temelju uvjeta prikaza i korisničkih pravila."

#: framework/premium/features/content-blocks/admin-ui.php:262
#: framework/premium/static/js/hooks/CreateHook.js:68
msgid "Custom Content/Hooks"
msgstr "Predložak stranice 404"

#: framework/premium/static/js/hooks/CreateHook.js:148
msgid "404 Page Template"
msgstr "Predložak zaglavlja"

#: framework/premium/static/js/hooks/CreateHook.js:132
msgid "Header Template"
msgstr "Predložak podnožja"

#: framework/premium/static/js/hooks/CreateHook.js:140
msgid "Footer Template"
msgstr "Promijeni ikonu"

#: framework/premium/static/js/options/IconPicker.js:137
msgid "Change Icon"
msgstr "Ukloni ikonu"

#: framework/premium/static/js/options/IconPicker.js:155
msgid "Remove Icon"
msgstr "Odaberi"

#: framework/premium/static/js/options/IconPicker.js:161
msgid "Select"
msgstr "Prenesi ikonu"

#: framework/premium/static/js/options/IconPicker/Modal.js:148
msgid "Upload Icon"
msgstr "Ubaci ikonu.Prilagođeni sadržaj/hookovi"

#: framework/premium/static/js/options/IconPicker/CustomIcon.js:34
msgid "For performance and customization reasons, only SVG files are allowed."
msgstr "Radi performansi i prilagodbe, dopuštene su samo SVG datoteke."

#: framework/premium/static/js/options/IconPicker/IconsList.js:24
msgid "Search icon"
msgstr "Dodaj novu lokaciju"

#: framework/premium/static/js/options/MultipleLocationsSelect.js:151
msgid "Add New Location"
msgstr "Odaberi lokaciju"

#: framework/premium/static/js/options/MultipleLocationsSelect.js:53
msgid "Select location"
msgstr "Početne stranice"

#: static/js/dashboard.js:64
msgid "Starter Sites"
msgstr "Proširenja"

#: static/js/dashboard.js:76
msgid "Extensions"
msgstr "Spremi uvjete"

#: static/js/header/EditConditions.js:151
#: static/js/options/DisplayCondition.js:98
msgid "Save Conditions"
msgstr "Sačuvaj Uvjete"

#: static/js/header/EditConditions.js:107
msgid "Add one or more conditions in order to display your header."
msgstr "Dodaj jedan ili više uvjeta za prikaz zaglavlja."

#: static/js/header/PanelsManager.js:174
msgid "Remove header"
msgstr "Ukloni zaglavlje"

#: static/js/header/PanelsManager.js:198
msgid "Remove Header"
msgstr "Ukloni zaglavlje"

#: static/js/header/PanelsManager.js:201
msgid "You are about to remove a custom header, are you sure you want to continue?"
msgstr "Trebate pomoć ili savjet?"

#: static/js/dashboard/helpers/SubmitSupport.js:18
msgid "Need help or advice?"
msgstr "Trebate pomoć?"

#: static/js/dashboard/helpers/SubmitSupport.js:21
msgid "Got a question or need help with the theme? You can always submit a support ticket or ask for help in our friendly Facebook community."
msgstr "Imate pitanje ili trebate pomoć s temom? Uvijek možete poslati zahtjev za podršku ili zatražiti pomoć u našoj Facebook zajednici."

#: static/js/dashboard/helpers/SubmitSupport.js:33
msgid "Submit a Support Ticket"
msgstr "Pridruži se Facebook zajednici"

#: static/js/dashboard/helpers/SubmitSupport.js:41
msgid "Join Facebook Community"
msgstr "Nadogradi sada"

#: static/js/dashboard/helpers/useUpsellModal.js:134
msgid "Upgrade Now"
msgstr "Odaberi pravilo"

#: static/js/options/ConditionsManager/SingleCondition.js:114
msgid "Select rule"
msgstr "Odaberi taksonomiju"

#: static/js/options/ConditionsManager/SingleCondition.js:204
msgid "Select taxonomy"
msgstr "Odaberi jezik"

#: static/js/options/ConditionsManager/SingleCondition.js:236
msgid "Select language"
msgstr "Odaberi korisnika"

#: static/js/options/ConditionsManager/SingleCondition.js:292
msgid "Select user"
msgstr "Trenutni korisnik"

#: static/js/options/ConditionsManager/SingleCondition.js:265
msgid "Current user"
msgstr "Dodaj uvjet prikaza"

#: static/js/options/DisplayCondition.js:28
msgid "Add Display Condition"
msgstr "Uključi"

#: framework/features/conditions-manager.php:242,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:126
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:283
#: static/js/options/ConditionsManager/SingleCondition.js:97
msgid "Include"
msgstr "Isključi"

#: framework/features/conditions-manager.php:242,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:127
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:291
#: static/js/options/ConditionsManager/SingleCondition.js:98
msgid "Exclude"
msgstr "Ukloni"

#: framework/premium/static/js/options/PreviewedPostsSelect.js:170
#: static/js/options/ConditionsManager/PostIdPicker.js:68
msgid "Type to search by ID or title..."
msgstr "Upišite za pretragu prema ID-u ili naslovu…"

#: framework/premium/static/js/options/PreviewedPostsSelect.js:174
#: static/js/options/ConditionsManager/PostIdPicker.js:74
msgid "Select post"
msgstr "Odaberi stranicu"

#: static/js/options/ConditionsManager/PostIdPicker.js:76
msgid "Select page"
msgstr "Opcije uvoza"

#: static/js/options/CustomizerOptionsManager.js:113
msgid "Import Options"
msgstr "Uvezi Prilagodbe"

#: static/js/options/CustomizerOptionsManager.js:116
msgid "Easily import the theme customizer settings."
msgstr "Kliknite ili ispustite datoteku za prijenos..."

#: static/js/options/CustomizerOptionsManager.js:140
msgid "Click or drop to upload a file..."
msgstr ""

#: static/js/options/CustomizerOptionsManager.js:239
msgid "Import Customizations"
msgstr "Opcije kopiranja"

#: static/js/options/CustomizerOptionsManager.js:249
msgid "Copy Options"
msgstr "Kopiraj Prilagodbe"

#: static/js/options/CustomizerOptionsManager.js:252
msgid "Copy and import your customizations from parent or child theme."
msgstr "Kopirajte i uvezite sve postavke iz roditeljske teme ili podređene teme."

#: static/js/options/CustomizerOptionsManager.js:308
msgid "Copy From Parent Theme"
msgstr "Kopiraj iz podređene teme"

#: static/js/options/CustomizerOptionsManager.js:314
msgid "Copy From Child Theme"
msgstr "Kopirajte Iz Podređene Teme"

#: static/js/options/CustomizerOptionsManager.js:321
msgid "You are about to copy all the settings from your parent theme into the child theme. Are you sure you want to continue?"
msgstr "Kopirate sve postavke iz podređene teme u roditeljsku. Jeste li sigurni da želite nastaviti?"

#: static/js/options/CustomizerOptionsManager.js:327
msgid "You are about to copy all the settings from your child theme into the parent theme. Are you sure you want to continue?"
msgstr "Kopirajte sve postavke iz roditeljske teme u podređenu teme. Da li ste sigurni?"

#: static/js/options/CustomizerOptionsManager.js:376
msgid "Yes, I am sure"
msgstr "Izvezi postavke"

#: static/js/options/CustomizerOptionsManager.js:390
msgid "Export Settings"
msgstr "Postavke prilagoditelja"

#: static/js/options/CustomizerOptionsManager.js:394
msgid "Choose what set of settings you want to export."
msgstr "Odaberite koje postavke želite izvesti."

#: static/js/options/CustomizerOptionsManager.js:439
msgid "Customizer settings"
msgstr "Postavke widgeta"

#: static/js/options/CustomizerOptionsManager.js:443
msgid "Widgets settings"
msgstr "Izvoz"

#: static/js/options/CustomizerOptionsManager.js:536
msgid "Export"
msgstr "Opcije izvoza"

#: static/js/options/CustomizerOptionsManager.js:87
msgid "Export Options"
msgstr "Izvezi prilagodbe"

#: static/js/options/CustomizerOptionsManager.js:90
msgid "Easily export the theme customizer settings."
msgstr "Jednostavno izvezite postavke prilagoditelja teme."

#: static/js/options/CustomizerOptionsManager.js:107
msgid "Export Customizations"
msgstr "Uvjeti prikaza prozirnog zaglavlja"

#: static/js/options/DisplayCondition.js:19
msgid "Transparent Header Display Conditions"
msgstr "Uvjeti Prikazivanja Transparetnog Zaglavlja"

#: static/js/options/DisplayCondition.js:23
msgid "Add one or more conditions to display the transparent header."
msgstr "Učitavanje početnih stranica..."

#: static/js/dashboard/screens/DemoInstall.js:166
msgid "Loading Starter Sites..."
msgstr ""

#: static/js/dashboard/screens/DemoInstall/components/Progress.js:29
msgid "Installing"
msgstr "Instalacija"

#: static/js/dashboard/screens/DemoInstall/components/Progress.js:34
msgid "Please be patient and don't refresh this page, the import process may take a while, this also depends on your server."
msgstr "Molimo budite strpljivi i nemojte osvježavati ovu stranicu, postupak uvoza može potrajati, ovisno o vašem serveru."

#: static/js/dashboard/screens/DemoInstall/DemoToInstall.js:306
msgid "Back"
msgstr "Instaliraj"

#: static/js/dashboard/screens/DemoInstall/DemoToInstall.js:346
msgid "Install"
msgstr "Dalje"

#: static/js/dashboard/screens/DemoInstall/DemoToInstall.js:347
msgid "Next"
msgstr "Uvezi"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:182
msgid "Import"
msgstr "Dostupno za"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:127
msgid "Available for"
msgstr "Pregled"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:166
msgid "Preview"
msgstr "Uredi"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:181
msgid "Modify"
msgstr ""

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:22
msgid "Starter Site Imported Successfully"
msgstr "Začetni Sajt Uspješno Instaliran"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:25
msgid "Now you can view your website or start customizing it"
msgstr ""

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:33
#: static/js/dashboard/screens/Extensions/CurrentExtension.js:342
msgid "Customize"
msgstr "Pregledaj stranicu"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:40
msgid "View site"
msgstr "Kopiranje izvora podređene teme"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:12
msgid "copying child theme sources"
msgstr "kopiranje izvora podređene teme"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:16
msgid "activating child theme"
msgstr "aktivacija podređene teme"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:21
msgid "installing plugin %s"
msgstr "instalacija dodatka %s"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:28
msgid "activating plugin %s"
msgstr ""

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:34
msgid "downloading demo widgets"
msgstr "preuzimanje demo widgeta"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:38
msgid "installing demo widgets"
msgstr "instalacija demo widgetaP"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:42
msgid "downloading demo options"
msgstr "preuzimanje demo opcijaI"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:46
msgid "importing images from customizer"
msgstr "uvoz slika iz prilagoditeljaP"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:50
msgid "import customizer options"
msgstr "uvoz opcija prilagoditelja"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:54
msgid "activating required extensions"
msgstr "aktivacija potrebnih proširenja"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:58
msgid "removing previously installed posts"
msgstr "uklanjanje prethodno instaliranih objava"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:62
msgid "removing previously installed taxonomies"
msgstr "uklanjanje zadanih WordPress taksonomija"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:66
msgid "removing default WordPress pages"
msgstr "uklanjanje zadanih WordPress stranica"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:70
msgid "resetting customizer options"
msgstr "resetiranje opcija prilagoditelja"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:74
msgid "resetting widgets"
msgstr "resetiranje widgeta"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:89
msgid "users"
msgstr "korisnici"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:90
msgid "terms"
msgstr "termini"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:91
msgid "images"
msgstr "slike"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:92
msgid "posts"
msgstr "objave"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:93
msgid "comments"
msgstr "komentari"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:118
msgid "Child theme"
msgstr "Brisanje sadržaja"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:143
#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:70
msgid "Erase content"
msgstr "Završni koraci"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:185
msgid "Final touches"
msgstr "Uvoz opcija"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:155
msgid "Import options"
msgstr "Uvoz widgeta"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:168
msgid "Import widgets"
msgstr "Uvoz sadržaja"

#: static/js/dashboard/screens/DemoInstall/Installer/contentCalculation.js:9
msgid "Import content"
msgstr "Uvoz sadržaja"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:108
msgid "You already have a child theme properly installed and activated. Move on."
msgstr "Već imate pravilno instaliranu i aktiviranu podređenu temu. Nastavite dalje."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:116
msgid "Learn more about child themes"
msgstr "Saznajte više o podređenim temama"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:70
msgid "We strongly recommend to install the child theme, this way you will have freedom to make changes without breaking the parent theme."
msgstr "Preporučujemo aktivaciju podređene teme kako biste imali slobodu mijenjanja bez narušavanja roditeljske teme."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:77
msgid "We strongly recommend to activate the child theme, this way you will have freedom to make changes without breaking the parent theme."
msgstr "Preporučujemo instalaciju podređene teme kako biste imali slobodu mijenjanja bez narušavanja roditeljske teme."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:97
msgid "Install Child Theme"
msgstr "Aktiviraj podređenu temu"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:98
msgid "Activate Child Theme"
msgstr "Uvezi sadržaj"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:56
msgid "Import Content"
msgstr "Uvoz Sadržaja"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:59
msgid "This will import posts, pages, comments, navigation menus, custom fields, terms and custom posts."
msgstr "Ovo će uvesti objave, stranice, komentare, navigacijske izbornike, prilagođena polja, termine i prilagođene objave."

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:104
msgid "Clean Install"
msgstr "Čista instalacija"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:106
msgid "This option will remove the previous imported content and will perform a fresh and clean install."
msgstr "Ova opcija uklanja prethodno uvezeni sadržaj i izvodi svježu i čistu instalaciju."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:147
msgid "This starter site is already installed"
msgstr "Početna stranica uklonjena"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:156
msgid "Starter Site Removed"
msgstr "Odbaci"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:165
msgid "Dismiss"
msgstr "Ukloni"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:174
msgid "What steps do you want to perform next?"
msgstr "Koje korake želite poduzeti sljedeće?"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:191
msgid "Remove"
msgstr "Ponovno instaliraj"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:200
msgid "Reinstall"
msgstr "Deaktiviraj demo dodatke"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:55
msgid "Deactivate demo plugins"
msgstr "Odaberi graditelj stranice"

#: static/js/dashboard/screens/DemoInstall/Wizzard/PickBuilder.js:62
msgid "Choose Page Builder"
msgstr "Izaberite editor stranice"

#: static/js/dashboard/screens/DemoInstall/Wizzard/PickBuilder.js:65
msgid "This starter site can be imported and used with one of these page builders. Please select one in order to continue."
msgstr "Ova početna stranica može se uvesti i koristiti s jednim od ovih graditelja stranica. Odaberite jedan za nastavak."

#: static/js/dashboard/screens/DemoInstall/Wizzard/Plugins.js:71
msgid "Install & Activate Plugins"
msgstr "Instaliraj i aktiviraj dodatke"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Plugins.js:73
msgid "The following plugins are required for this starter site in order to work properly."
msgstr "Učitavanje statusa proširenja..."

#: static/js/dashboard/screens/Extension.js:95
#: static/js/dashboard/screens/Extensions.js:86
msgid "Loading Extensions Status..."
msgstr "Učitavanje Statusa Proširenja"

#: static/js/dashboard/screens/Extensions/Sidebar.js:60
msgid "Free Extensions"
msgstr "Pro proširenja"

#: static/js/dashboard/screens/Extensions/Sidebar.js:68
msgid "Pro Extensions"
msgstr "Graditelj"

#: static/js/dashboard/screens/SiteExport.js:239
msgid "Builder"
msgstr "Izvezi stranicu"

#: static/js/dashboard/screens/SiteExport.js:311
msgid "Export site"
msgstr "Novo"

#: framework/premium/extensions/mega-menu/extension.php:325
msgid "New"
msgstr ""

#: framework/premium/extensions/mega-menu/options.php:16,
#: framework/premium/extensions/mega-menu/options.php:581
msgid "Mega Menu Settings"
msgstr "Širina padajućeg izbornika"

#: framework/premium/extensions/mega-menu/options.php:28
msgid "Dropdown Width"
msgstr "Širina sadržaja"

#: framework/premium/extensions/mega-menu/options.php:36,
#: framework/premium/extensions/mega-menu/options.php:49
msgid "Content Width"
msgstr "Puna širina"

#: framework/premium/extensions/mega-menu/options.php:37,
#: framework/premium/extensions/mega-menu/options.php:58
msgid "Full Width"
msgstr "Prilagođena širina"

#: framework/premium/extensions/mega-menu/options.php:38,
#: framework/premium/features/content-blocks/options/archive.php:86
msgid "Custom Width"
msgstr "Zadana širina"

#: framework/premium/extensions/mega-menu/options.php:57
msgid "Default Width"
msgstr "Stupci"

#: framework/premium/extensions/mega-menu/options.php:86,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:178,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:197
msgid "Columns"
msgstr "Prilagođeni sadržaj"

#: framework/premium/extensions/mega-menu/options.php:333
msgid "Custom Content"
msgstr "Prilagođeni sadržaj"

#: framework/premium/extensions/mega-menu/options.php:337
msgid "Content Type"
msgstr "Vrsta sadržaja"

#: framework/premium/extensions/mega-menu/options.php:344
msgid "Default (Menu Item)"
msgstr "Zadano (stavka izbornika)"

#: framework/premium/extensions/mega-menu/options.php:345
msgid "Custom Text"
msgstr "Prilagođeni tekst"

#: framework/premium/extensions/mega-menu/options.php:432,
#: framework/premium/extensions/mega-menu/options.php:791
msgid "Item Label Settings"
msgstr "Postavke oznake stavke"

#: framework/premium/extensions/mega-menu/options.php:437
msgid "Item Label"
msgstr "Oznaka stavke"

#: framework/premium/extensions/mega-menu/options.php:448
msgid "Enabled"
msgstr "Omogućeno"

#: framework/premium/extensions/mega-menu/options.php:449,
#: framework/premium/features/content-blocks/options/404.php:111,
#: framework/premium/features/content-blocks/options/archive.php:188,
#: framework/premium/features/content-blocks/options/header.php:129,
#: framework/premium/features/content-blocks/options/hook.php:238,
#: framework/premium/features/content-blocks/options/maintenance.php:108,
#: framework/premium/features/content-blocks/options/nothing_found.php:129,
#: framework/premium/features/content-blocks/options/single.php:120
msgid "Disabled"
msgstr "Onemogućeno"

#: framework/premium/extensions/mega-menu/options.php:450
msgid "Heading"
msgstr "Naslov"

#: framework/premium/extensions/mega-menu/options.php:456
msgid "Label Link"
msgstr "Poveznica oznake"

#: framework/extensions/trending/customizer.php:496,
#: framework/premium/extensions/mega-menu/options.php:550,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:154,
#: framework/premium/features/premium-header/items/contacts/options.php:526,
#: framework/premium/features/premium-header/items/language-switcher/options.php:105,
#: framework/premium/features/premium-header/items/search-input/options.php:223
msgid "Vertical Alignment"
msgstr "Okomito poravnanje"

#: framework/features/header/header-options.php:209,
#: framework/features/header/header-options.php:236,
#: framework/features/header/header-options.php:251,
#: framework/features/header/header-options.php:266,
#: framework/premium/extensions/mega-menu/options.php:585,
#: framework/premium/extensions/shortcuts/customizer.php:1181,
#: framework/premium/extensions/shortcuts/customizer.php:1223,
#: framework/premium/extensions/shortcuts/customizer.php:1265,
#: framework/features/header/items/account/options.php:1733,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:37,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:77,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:325,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:196,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:541,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:577,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:611,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:480,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:512,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:542
msgid "Background"
msgstr "Pozadina"

#: framework/premium/extensions/mega-menu/options.php:599
msgid "Link Color"
msgstr "Boja poveznice"

#: framework/premium/extensions/mega-menu/options.php:619,
#: framework/features/header/items/account/options.php:1933,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:634,
#: framework/premium/features/premium-header/items/contacts/options.php:641,
#: framework/premium/features/premium-header/items/contacts/options.php:681,
#: framework/premium/features/premium-header/items/contacts/options.php:720
#: static/js/editor/blocks/breadcrumbs/Edit.js:60
#: static/js/editor/blocks/contact-info/Edit.js:114
msgid "Link Initial"
msgstr "Početna boja poveznice"

#: framework/premium/extensions/mega-menu/options.php:624
msgid "Link Hover/Active"
msgstr "Poveznica pri lebdenju/aktivna"

#: framework/premium/extensions/mega-menu/options.php:629,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:149
msgid "Background Hover"
msgstr "Pozadina pri lebdenju"

#: framework/premium/extensions/mega-menu/options.php:646,
#: framework/premium/extensions/mega-menu/options.php:803
msgid "Heading Color"
msgstr "Boja naslova"

#: framework/premium/extensions/mega-menu/options.php:658,
#: framework/premium/extensions/mega-menu/options.php:677,
#: framework/premium/extensions/mega-menu/options.php:815
msgid "Initial Color"
msgstr "Početna boja"

#: framework/extensions/cookies-consent/customizer.php:147,
#: framework/extensions/newsletter-subscribe/customizer.php:196,
#: framework/premium/extensions/mega-menu/options.php:665,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:342
#: static/js/editor/blocks/about-me/Edit.js:89
#: static/js/editor/blocks/breadcrumbs/Edit.js:43
#: static/js/editor/blocks/contact-info/Edit.js:96
msgid "Text Color"
msgstr "Boja teksta"

#: framework/premium/extensions/mega-menu/options.php:684,
#: framework/premium/extensions/shortcuts/customizer.php:1282,
#: framework/premium/features/premium-header/items/search-input/options.php:904,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:161
msgid "Items Divider"
msgstr "Razdjelnik stavki"

#: framework/premium/extensions/mega-menu/options.php:699
msgid "Columns Divider"
msgstr "Razdjelnik stupaca"

#: framework/premium/extensions/mega-menu/options.php:714,
#: framework/premium/features/premium-header/items/search-input/options.php:886
msgid "Dropdown Shadow"
msgstr "Sjena padajućeg izbornika"

#: framework/premium/extensions/mega-menu/options.php:746
msgid "Column Settings"
msgstr "Postavke stupca"

#: framework/premium/extensions/mega-menu/options.php:750
msgid "Column Spacing"
msgstr "Razmak između stupaca"

#: framework/premium/extensions/mega-menu/options.php:825,
#: framework/features/header/items/account/options.php:1327,
#: framework/features/header/items/account/options.php:1366,
#: framework/features/header/items/account/options.php:1409,
#: framework/features/header/items/account/options.php:1450,
#: framework/features/header/items/account/options.php:1738,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:300,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:328,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:359,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:388,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:331,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:213,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:353,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:382,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:413,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:442,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:354,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:382,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:417,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:450,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:308,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:336,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:367,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:396
#: static/js/editor/blocks/share-box/Edit.js:81
#: static/js/editor/blocks/socials/Edit.js:81
msgid "Icon Color"
msgstr "Boja ikona"

#: framework/helpers/exts-configs.php:137
msgid "Enables support for Custom Fields inside archive cards and single page post title, adds a reading progress bar for your posts and lets you set featured images and colors for your categories archives."
msgstr "Omogućuje podršku za prilagođena polja unutar arhivskih kartica i naslova pojedinačnih objava, dodaje traku za praćenje napretka čitanja objava i omogućuje postavljanje istaknutih slika i boja za arhive kategorija."

#: framework/helpers/exts-configs.php:180,
#: framework/premium/extensions/shortcuts/config.php:6
msgid "Easily turn your websites into mobile first experiences. You can easily add the most important actions at the bottom of the screen for easy access."
msgstr "Jednostavno prilagodite web-stranicu za mobilne uređaje! Dodajte najvažnije radnje na dno ekrana radi lakšeg pristupa."

#: framework/premium/extensions/shortcuts/customizer.php:12,
#: framework/premium/extensions/shortcuts/customizer.php:38,
#: framework/premium/extensions/shortcuts/customizer.php:790,
#: framework/premium/extensions/shortcuts/extension.php:173,
#: framework/premium/extensions/shortcuts/views/bar.php:13,
#: framework/premium/extensions/shortcuts/views/bar.php:46
msgid "Home"
msgstr "Početna"

#: framework/features/blocks/contact-info/options.php:139,
#: framework/premium/extensions/shortcuts/customizer.php:71,
#: framework/premium/extensions/shortcuts/customizer.php:97,
#: framework/premium/extensions/shortcuts/customizer.php:799,
#: framework/premium/extensions/shortcuts/extension.php:182,
#: framework/premium/extensions/shortcuts/views/bar.php:22,
#: framework/premium/extensions/shortcuts/views/bar.php:47,
#: framework/premium/features/premium-header/items/contacts/options.php:127
msgid "Phone"
msgstr "Telefon"

#: framework/premium/extensions/shortcuts/customizer.php:201,
#: framework/premium/extensions/shortcuts/customizer.php:227,
#: framework/premium/extensions/shortcuts/views/bar.php:49
msgid "Scroll Top"
msgstr "Pomakni na vrh"

#: framework/premium/extensions/shortcuts/customizer.php:352,
#: framework/premium/extensions/shortcuts/customizer.php:378,
#: framework/premium/extensions/shortcuts/views/bar.php:50
msgid "Cart"
msgstr "Košarica"

#: framework/premium/extensions/shortcuts/customizer.php:411,
#: framework/premium/extensions/shortcuts/customizer.php:437
msgid "Shop"
msgstr "Trgovina"

#: framework/helpers/exts-configs.php:262,
#: framework/premium/extensions/shortcuts/customizer.php:545,
#: framework/premium/extensions/shortcuts/customizer.php:571,
#: framework/premium/extensions/shortcuts/views/bar.php:52,
#: framework/features/header/items/account/views/login.php:520,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:309,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:406,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:410,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:438,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:442,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/helpers.php:168,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:17,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/config.php:4,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:141,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/view.php:100
msgid "Wishlist"
msgstr "Lista želja"

#: framework/premium/extensions/shortcuts/customizer.php:784
msgid "Shortcuts"
msgstr "Prečaci"

#: framework/premium/extensions/shortcuts/customizer.php:839,
#: framework/features/header/items/account/options.php:520,
#: framework/features/header/items/account/options.php:983,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:38,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:91,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:92
msgid "Label Visibility"
msgstr "Vidljivost oznake"

#: framework/premium/extensions/shortcuts/customizer.php:879,
#: framework/features/header/items/account/options.php:554,
#: framework/features/header/items/account/options.php:1017,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:88,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:122,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:123
msgid "Label Position"
msgstr "Pozicija oznake"

#: framework/premium/extensions/shortcuts/customizer.php:888,
#: framework/premium/features/content-blocks/hooks-manager.php:477,
#: framework/features/header/items/account/options.php:563,
#: framework/features/header/items/account/options.php:1035,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:97,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:21,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:131,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:132
msgid "Bottom"
msgstr "Dno"

#: framework/premium/extensions/mega-menu/options.php:482,
#: framework/premium/extensions/shortcuts/customizer.php:925,
#: framework/features/header/items/account/options.php:508,
#: framework/features/header/items/account/options.php:882,
#: framework/features/header/items/account/options.php:968,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:46,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:439,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:202,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:42,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:73,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:74
msgid "Icon Size"
msgstr "Veličina ikone"

#: framework/premium/extensions/shortcuts/customizer.php:936,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:383
msgid "Container Height"
msgstr "Visina spremnika"

#: framework/premium/extensions/shortcuts/customizer.php:951
msgid "Container Max Width"
msgstr "Maksimalna širina spremnika"

#: framework/premium/extensions/shortcuts/customizer.php:975
msgid "Scroll Interaction"
msgstr "Interakcija s pomicanjem"

#: framework/premium/extensions/shortcuts/customizer.php:981
msgid "Hide"
msgstr "Sakrij"

#: framework/premium/extensions/shortcuts/customizer.php:1023
msgid "Shortcuts Bar Display Conditions"
msgstr "Uvjeti prikaza trake prečaca"

#: framework/premium/extensions/shortcuts/customizer.php:1024
msgid "Add one or more conditions to display the shortcuts bar."
msgstr "Dodajte jedan ili više uvjeta za prikaz trake prečaca."

#: framework/premium/extensions/shortcuts/customizer.php:1048,
#: framework/features/header/items/account/options.php:1895,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:184,
#: framework/premium/features/premium-header/items/contacts/options.php:577,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:105,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:212,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:42
msgid "Font"
msgstr "Font"

#: framework/features/blocks/about-me/options.php:187,
#: framework/features/blocks/share-box/options.php:141,
#: framework/features/blocks/socials/options.php:93,
#: framework/premium/extensions/shortcuts/customizer.php:1092,
#: framework/premium/features/premium-header/items/contacts/options.php:751,
#: framework/premium/features/premium-header/items/contacts/options.php:780,
#: framework/premium/features/premium-header/items/contacts/options.php:811,
#: framework/premium/features/premium-header/items/contacts/options.php:840
#: static/js/editor/blocks/about-me/Edit.js:117
#: static/js/editor/blocks/contact-info/Edit.js:133
msgid "Icons Color"
msgstr "Boja ikona"

#: framework/premium/extensions/shortcuts/customizer.php:1163
msgid "Cart Badge Color"
msgstr "Boja oznake košarice"

#: framework/premium/extensions/shortcuts/customizer.php:1302
msgid "Items Divider Height"
msgstr "Visina razdjelnika stavki"

#: framework/premium/extensions/shortcuts/customizer.php:1316,
#: framework/features/header/items/account/options.php:2016,
#: framework/premium/features/content-blocks/options/popup.php:545,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:701,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:150,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:175
msgid "Shadow"
msgstr "Sjena"

#: framework/helpers/exts-configs.php:195
msgid "Create unlimited personalized sets of widget areas and display them on any page or post using our conditional logic functionality."
msgstr "Kreirajte neograničene personalizirane skupove widget područja i prikažite ih na bilo kojoj stranici ili objavi pomoću naše funkcije uvjetne logike."

#: framework/helpers/exts-configs.php:199
msgid "Create New Sidebar"
msgstr "Kreiraj novu bočnu traku"

#: framework/premium/extensions/sidebars/form.php:3
#: framework/premium/extensions/sidebars/static/js/main.js:50
msgid "Create Sidebar/Widget Area"
msgstr "Kreiraj bočnu traku/područje widgeta"

#: framework/premium/extensions/sidebars/form.php:6
msgid "In order to create a new sidebar/widget area simply enter a name in the input below and click the Create Sidebar button."
msgstr "Za kreiranje nove bočne trake/područja widgeta jednostavno unesite naziv i kliknite gumb Kreiraj bočnu traku."

#: framework/premium/extensions/sidebars/form.php:16
#: framework/premium/extensions/sidebars/static/js/main.js:66
msgid "Create Sidebar"
msgstr "Kreiraj bočnu traku"

#: framework/premium/extensions/sidebars/form.php:20
msgid "Available Sidebars/Widget Areas"
msgstr "Dostupne bočne trake/područja widgeta"

#: framework/helpers/exts-configs.php:215
msgid "Replace Blocksy's branding with your own. Easily hide licensing info and other sections of the theme and companion plugin from your clients and make your final product look more professional."
msgstr "Zamijenite Blocksyjev brending vlastitim. Jednostavno sakrijte podatke o licenci i druge sekcije teme i dodatka kako biste vaš konačni proizvod učinili profesionalnijim."

#: framework/helpers/exts-configs.php:228
msgid "Make the shopping experience better for your visitors! Add features such as Product Quick View, Wishlist functionality and a Floating Add to Cart button. Customize the single product gallery/slider and the layout."
msgstr "Poboljšajte iskustvo kupovine za svoje posjetitelje! Dodajte značajke kao što su Brzi pregled proizvoda, Lista želja i Plutajući gumb \"Dodaj u košaricu\". Prilagodite galeriju proizvoda/slajder i izgled stranice."

#: framework/premium/extensions/woocommerce-extra/features/quick-view/feature.php:14
msgid "Quick View Button"
msgstr "Gumb za brzi pregled"

#: framework/features/header/items/account/options.php:467,
#: framework/features/header/items/account/options.php:841,
#: framework/features/header/items/account/options.php:929,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/archive-card/feature.php:13,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:66,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:111,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:65,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:66
msgid "Type 3"
msgstr "Vrsta 3"

#: framework/features/header/items/account/options.php:477,
#: framework/features/header/items/account/options.php:851,
#: framework/features/header/items/account/options.php:939,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:71,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:116
msgid "Type 4"
msgstr "Vrsta 4"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/views/panel.php:61
msgid "Available Filters"
msgstr "Dostupni filteri"

#: framework/premium/extensions/woocommerce-extra/extension.php:128
msgid "Quick view title before"
msgstr "Brzi pregled – naslov prije"

#: framework/premium/extensions/woocommerce-extra/extension.php:133
msgid "Quick view title after"
msgstr "Brzi pregled – naslov poslije"

#: framework/premium/extensions/woocommerce-extra/extension.php:138
msgid "Quick view price before"
msgstr "Brzi pregled – cijena prije"

#: framework/premium/extensions/woocommerce-extra/extension.php:143
msgid "Quick view price after"
msgstr "Brzi pregled – cijena poslije"

#: framework/premium/extensions/woocommerce-extra/extension.php:148
msgid "Quick view summary before"
msgstr "Brzi pregled – sažetak prije"

#: framework/premium/extensions/woocommerce-extra/extension.php:153
msgid "Quick view summary after"
msgstr "Brzi pregled – sažetak poslije"

#: framework/helpers/exts-configs.php:238,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:4
msgid "Floating Cart"
msgstr "Plutajuća košarica"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:15
msgid "Position"
msgstr "Pozicija"

#: framework/premium/features/content-blocks/hooks-manager.php:439,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:20
msgid "Top"
msgstr "Vrh"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:68
msgid "Product Title Visibility"
msgstr "Vidljivost naslova proizvoda"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:30
msgid "Floating Cart Visibility"
msgstr "Vidljivost plutajuće košarice"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/view.php:77
msgid "Go to product page"
msgstr "Idi na stranicu proizvoda"

#: framework/premium/extensions/shortcuts/customizer.php:507,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/helpers.php:40,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:111
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:108
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:137
msgid "Filter"
msgstr "Filter"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:532
msgid "Filter Widgets"
msgstr "Filter widgeti"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:536
msgid "Widgets"
msgstr "Widgeti"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:580
msgid "Widgets Vertical Spacing"
msgstr "Okomiti razmak widgeta"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:598
msgid "Widgets Font"
msgstr "Font widgeta"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:606
msgid "Widgets Font Color"
msgstr "Boja fonta widgeta"

#: framework/features/header/items/account/options.php:1927,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:134,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:628,
#: framework/premium/features/premium-header/items/contacts/options.php:635,
#: framework/premium/features/premium-header/items/contacts/options.php:676,
#: framework/premium/features/premium-header/items/contacts/options.php:715
msgid "Text Initial"
msgstr "Početni tekst"

#: framework/features/header/items/account/options.php:1939,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:639,
#: framework/premium/features/premium-header/items/contacts/options.php:647,
#: framework/premium/features/premium-header/items/contacts/options.php:687,
#: framework/premium/features/premium-header/items/contacts/options.php:726
#: static/js/editor/blocks/breadcrumbs/Edit.js:66
#: static/js/editor/blocks/contact-info/Edit.js:123
msgid "Link Hover"
msgstr "Poveznica pri lebdenju"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:123
msgid "Panel Reveal"
msgstr "Prikaz panela"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:131,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:39
msgid "Left Side"
msgstr "Lijeva strana"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:132,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:40
msgid "Right Side"
msgstr "Desna strana"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:137,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:45
msgid "Panel Width"
msgstr "Širina panela"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:295,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:163
msgid "Panel Shadow"
msgstr "Sjena panela"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:263,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:76
msgid "Panel Background"
msgstr "Pozadina panela"

#: framework/premium/features/content-blocks/options/popup.php:615,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:779
msgid "Close Icon Color"
msgstr "Boja ikone za zatvaranje"

#: framework/premium/features/content-blocks/options/popup.php:646
msgid "Close Icon Background"
msgstr "Pozadina ikone za zatvaranje"

#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:175
#: static/js/editor/blocks/share-box/index.js:47
msgid "Share Box"
msgstr "Okvir za dijeljenje"

#: framework/premium/features/content-blocks/hooks-manager.php:11
msgid "WP head"
msgstr "WP zaglavlje"

#: framework/premium/features/content-blocks/hooks-manager.php:13,
#: framework/premium/features/content-blocks/hooks-manager.php:22,
#: framework/premium/features/content-blocks/hooks-manager.php:32,
#: framework/premium/features/content-blocks/hooks-manager.php:41
msgid "Head"
msgstr "Zaglavlje"

#: framework/premium/features/content-blocks/hooks-manager.php:20
msgid "WP head start"
msgstr "Početak WP zaglavlja"

#: framework/premium/features/content-blocks/hooks-manager.php:30
msgid "WP head end"
msgstr "Kraj WP zaglavlja"

#: framework/premium/features/content-blocks/hooks-manager.php:48
msgid "Header before"
msgstr "Prije zaglavlja"

#: framework/premium/features/content-blocks/admin-ui.php:265,
#: framework/premium/features/content-blocks/hooks-manager.php:50,
#: framework/premium/features/content-blocks/hooks-manager.php:59
msgid "Header"
msgstr "Zaglavlje"

#: framework/premium/features/content-blocks/hooks-manager.php:57
msgid "Header after"
msgstr "Nakon zaglavlja"

#: framework/premium/features/content-blocks/hooks-manager.php:66
msgid "Desktop top"
msgstr "Vrh za desktop"

#: framework/premium/features/content-blocks/hooks-manager.php:68,
#: framework/premium/features/content-blocks/hooks-manager.php:77,
#: framework/premium/features/content-blocks/hooks-manager.php:86,
#: framework/premium/features/content-blocks/hooks-manager.php:95
msgid "Header offcanvas"
msgstr "Izvanplatneno zaglavlje"

#: framework/premium/features/content-blocks/hooks-manager.php:75
msgid "Desktop bottom"
msgstr "Dno za desktop"

#: framework/premium/features/content-blocks/hooks-manager.php:84
msgid "Mobile top"
msgstr "Vrh za mobilne uređaje"

#: framework/premium/features/content-blocks/hooks-manager.php:93
msgid "Mobile bottom"
msgstr "Dno za mobilne uređaje"

#: framework/premium/features/content-blocks/hooks-manager.php:102
msgid "Sidebar before"
msgstr "Prije bočne trake"

#: framework/premium/features/content-blocks/hooks-manager.php:103,
#: framework/premium/features/content-blocks/hooks-manager.php:110,
#: framework/premium/features/content-blocks/hooks-manager.php:117,
#: framework/premium/features/content-blocks/hooks-manager.php:124
msgid "Left/Right sidebar"
msgstr "Lijeva/desna bočna traka"

#: framework/premium/features/content-blocks/hooks-manager.php:109
msgid "Sidebar start"
msgstr "Početak bočne trake"

#: framework/premium/features/content-blocks/hooks-manager.php:116
msgid "Sidebar end"
msgstr "Kraj bočne trake"

#: framework/premium/features/content-blocks/hooks-manager.php:123
msgid "Sidebar after"
msgstr "Nakon bočne trake"

#: framework/premium/features/content-blocks/hooks-manager.php:130
msgid "Dynamic sidebar before"
msgstr "Prije dinamičke bočne trake"

#: framework/premium/features/content-blocks/hooks-manager.php:131,
#: framework/premium/features/content-blocks/hooks-manager.php:138,
#: framework/premium/features/content-blocks/hooks-manager.php:146
msgid "All widget areas"
msgstr "Sva područja widgeta"

#: framework/premium/features/content-blocks/hooks-manager.php:137
msgid "Dynamic sidebar"
msgstr "Dinamička bočna traka"

#: framework/premium/features/content-blocks/hooks-manager.php:145
msgid "Dynamic sidebar after"
msgstr "Nakon dinamičke bočne trake"

#: framework/premium/features/content-blocks/hooks-manager.php:154
msgid "Before section"
msgstr "Prije sekcije"

#: framework/premium/features/content-blocks/hooks-manager.php:155,
#: framework/premium/features/content-blocks/hooks-manager.php:163,
#: framework/premium/features/content-blocks/hooks-manager.php:171,
#: framework/premium/features/content-blocks/hooks-manager.php:179,
#: framework/premium/features/content-blocks/hooks-manager.php:187,
#: framework/premium/features/content-blocks/hooks-manager.php:195,
#: framework/premium/features/content-blocks/hooks-manager.php:203,
#: framework/premium/features/content-blocks/hooks-manager.php:211,
#: framework/premium/features/content-blocks/hooks-manager.php:219,
#: framework/premium/features/content-blocks/hooks-manager.php:227,
#: framework/premium/features/content-blocks/hooks-manager.php:235,
#: framework/premium/features/content-blocks/hooks-manager.php:243,
#: framework/premium/features/content-blocks/hooks-manager.php:251,
#: framework/premium/features/content-blocks/hooks-manager.php:259
msgid "Page/post title"
msgstr "Naslov stranice/objave"

#: framework/premium/features/content-blocks/hooks-manager.php:162,
#: framework/premium/features/content-blocks/hooks-manager.php:312,
#: framework/premium/features/content-blocks/hooks-manager.php:354
msgid "Before title"
msgstr "Prije naslova"

#: framework/premium/features/content-blocks/hooks-manager.php:186
msgid "Before post meta"
msgstr "Prije meta podataka objave"

#: framework/premium/features/content-blocks/hooks-manager.php:210,
#: framework/premium/features/content-blocks/hooks-manager.php:319,
#: framework/premium/features/content-blocks/hooks-manager.php:361
msgid "After title"
msgstr "Nakon naslova"

#: framework/premium/features/content-blocks/hooks-manager.php:250
msgid "After post meta"
msgstr "Nakon meta podataka objave"

#: framework/premium/features/content-blocks/hooks-manager.php:258
msgid "After section"
msgstr "Nakon sekcije"

#: framework/premium/features/content-blocks/hooks-manager.php:266
msgid "Before content"
msgstr "Prije sadržaja"

#: framework/premium/features/content-blocks/hooks-manager.php:274,
#: framework/premium/features/content-blocks/hooks-manager.php:447
msgid "Top content"
msgstr "Gornji sadržaj"

#: framework/premium/features/content-blocks/hooks-manager.php:282,
#: framework/premium/features/content-blocks/hooks-manager.php:469
msgid "Bottom content"
msgstr "Donji sadržaj"

#: framework/premium/features/content-blocks/hooks-manager.php:290
msgid "After content"
msgstr "Nakon sadržaja"

#: framework/premium/features/content-blocks/hooks-manager.php:298
msgid "Before comments"
msgstr "Prije komentara"

#: framework/premium/features/content-blocks/hooks-manager.php:299,
#: framework/premium/features/content-blocks/hooks-manager.php:306,
#: framework/premium/features/content-blocks/hooks-manager.php:313,
#: framework/premium/features/content-blocks/hooks-manager.php:320,
#: framework/premium/features/content-blocks/hooks-manager.php:327,
#: framework/premium/features/content-blocks/hooks-manager.php:334
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:60
msgid "Comments"
msgstr "Komentari"

#: framework/premium/features/content-blocks/hooks-manager.php:305
msgid "Top comments"
msgstr "Gornji komentari"

#: framework/premium/features/content-blocks/hooks-manager.php:326
msgid "Bottom comments"
msgstr "Donji komentari"

#: framework/premium/features/content-blocks/hooks-manager.php:333
msgid "After comments"
msgstr "Nakon komentara"

#: framework/premium/features/content-blocks/hooks-manager.php:411,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:705
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:298
msgid "Before"
msgstr "Prije"

#: framework/premium/features/content-blocks/hooks-manager.php:412,
#: framework/premium/features/content-blocks/hooks-manager.php:419
msgid "Loop"
msgstr "Petlja"

#: framework/premium/features/content-blocks/hooks-manager.php:418,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:716
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:307
msgid "After"
msgstr "Nakon"

#: framework/premium/features/content-blocks/hooks-manager.php:425
msgid "Start"
msgstr "Početak"

#: framework/premium/features/content-blocks/hooks-manager.php:426,
#: framework/premium/features/content-blocks/hooks-manager.php:433
msgid "Loop card"
msgstr "Kartica petlje"

#: framework/premium/features/content-blocks/hooks-manager.php:432
msgid "End"
msgstr "Kraj"

#: framework/premium/features/content-blocks/hooks-manager.php:455
msgid "After certain number of blocks"
msgstr "Nakon određenog broja blokova"

#: framework/premium/features/content-blocks/hooks-manager.php:462
msgid "Before certain number of headings"
msgstr "Prije određenog broja naslova"

#: framework/premium/features/content-blocks/hooks-manager.php:485
msgid "Login form start"
msgstr "Početak obrasca za prijavu"

#: framework/premium/features/content-blocks/hooks-manager.php:486,
#: framework/premium/features/content-blocks/hooks-manager.php:493,
#: framework/premium/features/content-blocks/hooks-manager.php:500,
#: framework/premium/features/content-blocks/hooks-manager.php:507,
#: framework/premium/features/content-blocks/hooks-manager.php:514,
#: framework/premium/features/content-blocks/hooks-manager.php:521,
#: framework/premium/features/content-blocks/hooks-manager.php:528,
#: framework/premium/features/content-blocks/hooks-manager.php:535,
#: framework/premium/features/content-blocks/hooks-manager.php:542,
#: framework/premium/features/content-blocks/hooks-manager.php:549
msgid "Auth forms"
msgstr "Autorizacijski obrasci"

#: framework/premium/features/content-blocks/hooks-manager.php:492
msgid "Login form end"
msgstr "Kraj obrasca za prijavu"

#: framework/premium/features/content-blocks/hooks-manager.php:499
msgid "Login form modal start"
msgstr "Početak modalnog obrasca za prijavu"

#: framework/premium/features/content-blocks/hooks-manager.php:506
msgid "Login form modal end"
msgstr "Kraj modalnog obrasca za prijavu"

#: framework/premium/features/content-blocks/hooks-manager.php:513
msgid "Register form start"
msgstr "Početak obrasca za registraciju"

#: framework/premium/features/content-blocks/hooks-manager.php:520
msgid "Register form end"
msgstr "Kraj obrasca za registraciju"

#: framework/premium/features/content-blocks/hooks-manager.php:527
msgid "Register form modal start"
msgstr "Početak modalnog obrasca za registraciju"

#: framework/premium/features/content-blocks/hooks-manager.php:534
msgid "Register form modal end"
msgstr "Kraj modalnog obrasca za registraciju"

#: framework/premium/features/content-blocks/hooks-manager.php:541
msgid "Lost password form modal start"
msgstr "Početak modalnog obrasca za izgubljenu lozinku"

#: framework/premium/features/content-blocks/hooks-manager.php:548
msgid "Lost password form modal end"
msgstr "Kraj modalnog obrasca za izgubljenu lozinku"

#: framework/premium/features/content-blocks/hooks-manager.php:556
msgid "Before main content"
msgstr "Prije glavnog sadržaja"

#: framework/premium/features/content-blocks/hooks-manager.php:562
msgid "After main content"
msgstr "Nakon glavnog sadržaja"

#: framework/premium/features/content-blocks/hooks-manager.php:583
msgid "WooCommerce Global"
msgstr "WooCommerce Global"

#: framework/premium/features/content-blocks/hooks-manager.php:588
msgid "Archive description"
msgstr "Opis arhive"

#: framework/premium/features/content-blocks/hooks-manager.php:593
msgid "Before shop loop"
msgstr "Prije petlje trgovine"

#: framework/premium/features/content-blocks/hooks-manager.php:610
msgid "Before shop loop item title"
msgstr "Prije naslova stavke trgovine"

#: framework/premium/features/content-blocks/hooks-manager.php:615
msgid "After shop loop item title"
msgstr "Nakon naslova stavke trgovine"

#: framework/premium/features/content-blocks/hooks-manager.php:620
msgid "Before shop loop item price"
msgstr "Prije cijene stavke trgovine"

#: framework/premium/features/content-blocks/hooks-manager.php:625
msgid "After shop loop item price"
msgstr "Nakon cijene stavke trgovine"

#: framework/premium/features/content-blocks/hooks-manager.php:640
msgid "After shop loop"
msgstr "Nakon petlje trgovine"

#: framework/premium/features/content-blocks/hooks-manager.php:642
msgid "WooCommerce Archive"
msgstr "WooCommerce Arhiva"

#: framework/premium/features/content-blocks/hooks-manager.php:647
msgid "Before single product"
msgstr "Prije pojedinačnog proizvoda"

#: framework/premium/features/content-blocks/hooks-manager.php:665
msgid "Product meta start"
msgstr "Početak meta podataka proizvoda"

#: framework/premium/features/content-blocks/hooks-manager.php:669
msgid "Product meta end"
msgstr "Kraj meta podataka proizvoda"

#: framework/premium/features/content-blocks/hooks-manager.php:673
msgid "Share"
msgstr "Podijeli"

#: framework/premium/features/content-blocks/hooks-manager.php:677
msgid "After single product"
msgstr "Nakon pojedinačnog proizvoda"

#: framework/premium/features/content-blocks/hooks-manager.php:683
msgid "Before single product excerpt"
msgstr "Prije sažetka pojedinačnog proizvoda"

#: framework/premium/features/content-blocks/hooks-manager.php:688
msgid "After single product excerpt"
msgstr "Nakon sažetka pojedinačnog proizvoda"

#: framework/premium/features/content-blocks/hooks-manager.php:693
msgid "Before single product tabs"
msgstr "Prije kartica pojedinačnog proizvoda"

#: framework/premium/features/content-blocks/hooks-manager.php:699
msgid "After single product tabs"
msgstr "Nakon kartica pojedinačnog proizvoda"

#: framework/premium/features/content-blocks/hooks-manager.php:738
msgid "Cart is empty"
msgstr "Košarica je prazna"

#: framework/premium/features/content-blocks/hooks-manager.php:742
msgid "Before cart"
msgstr "Prije košarice"

#: framework/premium/features/content-blocks/hooks-manager.php:746
msgid "Before cart table"
msgstr "Prije tablice košarice"

#: framework/premium/features/content-blocks/hooks-manager.php:750
msgid "Before cart contents"
msgstr "Prije sadržaja košarice"

#: framework/premium/features/content-blocks/hooks-manager.php:754
msgid "Cart contents"
msgstr "Sadržaj košarice"

#: framework/premium/features/content-blocks/hooks-manager.php:758
msgid "After cart contents"
msgstr "Nakon sadržaja košarice"

#: framework/premium/features/content-blocks/hooks-manager.php:762
msgid "Cart coupon"
msgstr "Kupon za košaricu"

#: framework/premium/features/content-blocks/hooks-manager.php:766
msgid "Cart actions"
msgstr "Radnje u košarici"

#: framework/premium/features/content-blocks/hooks-manager.php:770
msgid "After cart table"
msgstr "Nakon tablice košarice"

#: framework/premium/features/content-blocks/hooks-manager.php:774
msgid "Cart collaterals"
msgstr "Dodatne informacije o košarici"

#: framework/premium/features/content-blocks/hooks-manager.php:778
msgid "Before cart totals"
msgstr "Prije zbroja košarice"

#: framework/premium/features/content-blocks/hooks-manager.php:782
msgid "Cart totals before order total"
msgstr "Ukupni iznos košarice prije ukupnog iznosa narudžbe"

#: framework/premium/features/content-blocks/hooks-manager.php:786
msgid "Cart totals after order total"
msgstr "Ukupni iznos košarice nakon ukupnog iznosa narudžbe"

#: framework/premium/features/content-blocks/hooks-manager.php:790
msgid "Proceed to checkout"
msgstr "Nastavi na blagajnu"

#: framework/premium/features/content-blocks/hooks-manager.php:794
msgid "After cart totals"
msgstr "Nakon zbroja košarice"

#: framework/premium/features/content-blocks/hooks-manager.php:798
msgid "After cart"
msgstr "Nakon košarice"

#: framework/premium/features/content-blocks/hooks-manager.php:803
msgid "Before Mini Cart"
msgstr "Prije mini košarice"

#: framework/premium/features/content-blocks/hooks-manager.php:808
msgid "Before Mini Cart Contents"
msgstr "Prije sadržaja mini košarice"

#: framework/premium/features/content-blocks/hooks-manager.php:813
msgid "Mini Cart Contents"
msgstr "Sadržaj mini košarice"

#: framework/premium/features/content-blocks/hooks-manager.php:818
msgid "Widget Shopping Cart Before Buttons"
msgstr "Widget košarice prije gumba"

#: framework/premium/features/content-blocks/hooks-manager.php:823
msgid "Widget Shopping Cart After Buttons"
msgstr "Widget košarice nakon gumba"

#: framework/premium/features/content-blocks/hooks-manager.php:828
msgid "After Mini Cart"
msgstr "Nakon mini košarice"

#: framework/premium/features/content-blocks/hooks-manager.php:830
msgid "WooCommerce Cart"
msgstr "WooCommerce košarica"

#: framework/premium/features/content-blocks/hooks-manager.php:836
msgid "Before checkout form"
msgstr "Prije obrasca naplate"

#: framework/premium/features/content-blocks/hooks-manager.php:840
msgid "Before customer details"
msgstr "Prije podataka o kupcu"

#: framework/premium/features/content-blocks/hooks-manager.php:844
msgid "After customer details"
msgstr "Nakon podataka o kupcu"

#: framework/premium/features/content-blocks/hooks-manager.php:848
msgid "Checkout billing"
msgstr "Naplata"

#: framework/premium/features/content-blocks/hooks-manager.php:852
msgid "Before checkout billing form"
msgstr "Obrazac za naplatu prije same naplate"

#: framework/premium/features/content-blocks/hooks-manager.php:856
msgid "After checkout billing form"
msgstr "Obrazac naplate nakon same naplate"

#: framework/premium/features/content-blocks/hooks-manager.php:860
msgid "Before order notes"
msgstr "Prije bilješki narudžbe"

#: framework/premium/features/content-blocks/hooks-manager.php:864
msgid "After order notes"
msgstr "Nakon bilješki narudžbe"

#: framework/premium/features/content-blocks/hooks-manager.php:868
msgid "Checkout shipping"
msgstr "Naplata dostave"

#: framework/premium/features/content-blocks/hooks-manager.php:872
msgid "Checkout before order review"
msgstr "Naplata prije pregleda narudžbe"

#: framework/premium/features/content-blocks/hooks-manager.php:876
msgid "Checkout order review"
msgstr "Pregled narudžbe"

#: framework/premium/features/content-blocks/hooks-manager.php:880
msgid "Review order before cart contents"
msgstr "Pregled narudžbe prije sadržaja košarice"

#: framework/premium/features/content-blocks/hooks-manager.php:884
msgid "Review order after cart contents"
msgstr "Pregled narudžbe nakon sadržaja košarice"

#: framework/premium/features/content-blocks/hooks-manager.php:888
msgid "Review order before order total"
msgstr "Pregled narudžbe prije ukupnog iznosa"

#: framework/premium/features/content-blocks/hooks-manager.php:892
msgid "Review order after order total"
msgstr "Pregled narudžbe nakon ukupnog iznosa"

#: framework/premium/features/content-blocks/hooks-manager.php:896
msgid "Review order before payment"
msgstr "Pregled narudžbe prije plaćanja"

#: framework/premium/features/content-blocks/hooks-manager.php:900
msgid "Review order before submit"
msgstr "Pregled narudžbe prije slanja"

#: framework/premium/features/content-blocks/hooks-manager.php:904
msgid "Review order after submit"
msgstr "Pregled narudžbe nakon slanja"

#: framework/premium/features/content-blocks/hooks-manager.php:908
msgid "Review order after payment"
msgstr "Pregled narudžbe nakon plaćanja"

#: framework/premium/features/content-blocks/hooks-manager.php:912
msgid "Checkout after order review"
msgstr "Naplata nakon pregleda narudžbe"

#: framework/premium/features/content-blocks/hooks-manager.php:916
msgid "After checkout form"
msgstr "Nakon obrasca za naplatu"

#: framework/premium/features/content-blocks/hooks-manager.php:919
msgid "WooCommerce Checkout"
msgstr "WooCommerce naplata"

#: framework/premium/features/content-blocks/hooks-manager.php:925
msgid "Before my account"
msgstr "Prije mog računa"

#: framework/premium/features/content-blocks/hooks-manager.php:929
msgid "Before account navigation"
msgstr "Prije navigacije računa"

#: framework/premium/features/content-blocks/hooks-manager.php:933
msgid "Account navigation"
msgstr "Navigacija računa"

#: framework/premium/features/content-blocks/hooks-manager.php:937
msgid "After account navigation"
msgstr "Nakon navigacije računa"

#: framework/premium/features/content-blocks/hooks-manager.php:941
msgid "Account content"
msgstr "Sadržaj računa"

#: framework/premium/features/content-blocks/hooks-manager.php:945
msgid "Account dashboard"
msgstr "Nadzorna ploča računa"

#: framework/premium/features/content-blocks/hooks-manager.php:949
msgid "After my account"
msgstr "Nakon mog računa"

#: framework/premium/features/content-blocks/hooks-manager.php:951,
#: framework/features/header/items/account/options.php:169,
#: framework/features/header/items/account/options.php:284,
#: framework/features/header/items/account/options.php:285
msgid "WooCommerce Account"
msgstr "WooCommerce račun"

#: framework/premium/features/content-blocks/hooks-manager.php:977
msgid "WP footer"
msgstr "WP podnožje"

#: framework/premium/features/content-blocks/admin-ui.php:266,
#: framework/premium/features/content-blocks/hooks-manager.php:978,
#: framework/premium/features/content-blocks/hooks-manager.php:986,
#: framework/premium/features/content-blocks/hooks-manager.php:994
msgid "Footer"
msgstr "Podnožje"

#: framework/premium/features/content-blocks/hooks-manager.php:985
msgid "Footer before"
msgstr "Prije podnožja"

#: framework/premium/features/content-blocks/hooks-manager.php:993
msgid "Footer after"
msgstr "Nakon podnožja"

#: framework/premium/features/content-blocks/hooks-manager.php:1009
msgid "Custom Hook (%s)"
msgstr "Prilagođeni hook (%s)"

#: framework/premium/features/content-blocks/hooks-manager.php:1015,
#: framework/premium/features/content-blocks/options/hook.php:116
#: framework/premium/static/js/options/MultipleLocationsSelect.js:94
msgid "After Block Number"
msgstr "Nakon broja bloka"

#: framework/premium/features/content-blocks/hooks-manager.php:1021,
#: framework/premium/features/content-blocks/options/hook.php:133
#: framework/premium/static/js/options/MultipleLocationsSelect.js:116
msgid "Before Heading Number"
msgstr "Prije broja naslova"

#: static/js/editor/blocks/about-me/index.js:45
msgid "About Me"
msgstr "O meni"

#: framework/features/blocks/about-me/options.php:16
msgid "About me"
msgstr "O meni"

#: framework/features/blocks/about-me/options.php:49,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:31,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:729,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:68,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:99,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:143,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:166,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:166
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:96
msgid "Image"
msgstr "Slika"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:126
msgid "Upload Image"
msgstr "Prenesi sliku"

#: framework/features/blocks/about-me/options.php:53,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:96,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:106,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:809,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:819,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:72
msgid "Select Image"
msgstr "Odaberi sliku"

#: framework/features/blocks/about-me/options.php:54
msgid "Change Image"
msgstr "Promijeni sliku"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:46,
#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:20,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:123,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:112,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:65
msgid "Image Ratio"
msgstr "Omjer slike"

#: framework/features/blocks/contact-info/options.php:520,
#: framework/premium/extensions/shortcuts/customizer.php:307
msgid "Open link in new tab"
msgstr "Otvori poveznicu u novoj kartici"

#: framework/features/blocks/contact-info/options.php:35
#: static/js/editor/blocks/contact-info/index.js:54
msgid "Contact Info"
msgstr "Kontakt informacije"

#: framework/features/blocks/contact-info/options.php:51,
#: framework/features/blocks/contact-info/options.php:81,
#: framework/features/blocks/contact-info/view.php:15,
#: framework/premium/features/premium-header/items/contacts/options.php:57,
#: framework/premium/features/premium-header/items/contacts/options.php:88,
#: framework/premium/features/premium-header/items/contacts/view.php:35
msgid "Address:"
msgstr "Adresa:"

#: framework/features/blocks/contact-info/options.php:59,
#: framework/features/blocks/contact-info/options.php:146,
#: framework/features/blocks/contact-info/view.php:23,
#: framework/premium/features/premium-header/items/contacts/options.php:65,
#: framework/premium/features/premium-header/items/contacts/options.php:133,
#: framework/premium/features/premium-header/items/contacts/view.php:43
msgid "Phone:"
msgstr "Telefon:"

#: framework/features/blocks/contact-info/options.php:67,
#: framework/features/blocks/contact-info/options.php:209,
#: framework/features/blocks/contact-info/view.php:31,
#: framework/premium/features/premium-header/items/contacts/options.php:73,
#: framework/premium/features/premium-header/items/contacts/options.php:178,
#: framework/premium/features/premium-header/items/contacts/view.php:51
msgid "Mobile:"
msgstr "Mobitel:"

#: framework/features/blocks/contact-info/options.php:75,
#: framework/premium/features/premium-header/items/contacts/options.php:82
msgid "Address"
msgstr "Adresa"

#: framework/features/blocks/contact-info/options.php:94,
#: framework/features/blocks/contact-info/options.php:159,
#: framework/features/blocks/contact-info/options.php:222,
#: framework/features/blocks/contact-info/options.php:285,
#: framework/features/blocks/contact-info/options.php:348,
#: framework/features/blocks/contact-info/options.php:411,
#: framework/features/blocks/contact-info/options.php:474,
#: framework/premium/features/premium-header/items/contacts/options.php:107,
#: framework/premium/features/premium-header/items/contacts/options.php:152,
#: framework/premium/features/premium-header/items/contacts/options.php:197,
#: framework/premium/features/premium-header/items/contacts/options.php:242,
#: framework/premium/features/premium-header/items/contacts/options.php:288,
#: framework/premium/features/premium-header/items/contacts/options.php:333,
#: framework/premium/features/premium-header/items/contacts/options.php:378
msgid "Link (optional)"
msgstr "Poveznica (neobavezno)"

#: framework/features/blocks/contact-info/options.php:265,
#: framework/premium/features/premium-header/items/contacts/options.php:217
msgid "Work Hours"
msgstr "Radno vrijeme"

#: framework/features/blocks/contact-info/options.php:272,
#: framework/premium/features/premium-header/items/contacts/options.php:223
msgid "Opening hours"
msgstr "Radno vrijeme"

#: framework/features/blocks/contact-info/options.php:328,
#: framework/premium/features/premium-header/items/contacts/options.php:263
msgid "Fax"
msgstr "Faks"

#: framework/features/blocks/contact-info/options.php:335,
#: framework/premium/features/premium-header/items/contacts/options.php:269
msgid "Fax:"
msgstr "Faks:"

#: framework/features/blocks/contact-info/options.php:398,
#: framework/premium/features/premium-header/items/contacts/options.php:314
msgid "Email:"
msgstr "E-mail:"

#: framework/features/blocks/contact-info/options.php:454,
#: framework/premium/features/premium-header/items/contacts/options.php:353
msgid "Website"
msgstr "Web stranica"

#: framework/features/blocks/contact-info/options.php:461,
#: framework/premium/features/premium-header/items/contacts/options.php:359
msgid "Website:"
msgstr "Web stranica:"

#: framework/features/blocks/about-me/options.php:85,
#: framework/premium/features/content-blocks/options/archive.php:73
msgid "Small"
msgstr "Mali"

#: framework/features/blocks/about-me/options.php:87
msgid "Large"
msgstr "Veliki"

#: framework/features/blocks/about-me/options.php:200,
#: framework/features/blocks/contact-info/options.php:557,
#: framework/features/blocks/share-box/options.php:154,
#: framework/features/blocks/socials/options.php:106,
#: framework/premium/features/premium-header/items/contacts/options.php:445
msgid "Icons Shape Type"
msgstr "Oblik ikona"

#: framework/features/blocks/about-me/options.php:97,
#: framework/features/blocks/about-me/options.php:205,
#: framework/features/blocks/contact-info/options.php:565,
#: framework/features/blocks/share-box/options.php:161,
#: framework/features/blocks/socials/options.php:113,
#: framework/premium/features/premium-header/items/contacts/options.php:452
msgid "Rounded"
msgstr "Zaobljeno"

#: framework/features/blocks/about-me/options.php:98,
#: framework/features/blocks/about-me/options.php:206,
#: framework/features/blocks/contact-info/options.php:566,
#: framework/features/blocks/share-box/options.php:162,
#: framework/features/blocks/socials/options.php:114,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:27,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:136,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:190,
#: framework/premium/features/premium-header/items/contacts/options.php:453
#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:86
msgid "Square"
msgstr "Kvadratno"

#: framework/features/blocks/about-me/options.php:215,
#: framework/features/blocks/contact-info/options.php:575,
#: framework/features/blocks/share-box/options.php:171,
#: framework/features/blocks/socials/options.php:123,
#: framework/premium/features/premium-header/items/contacts/options.php:464
msgid "Shape Fill Type"
msgstr "Ispuna oblika"

#: framework/features/blocks/about-me/options.php:220,
#: framework/features/blocks/contact-info/options.php:582,
#: framework/features/blocks/share-box/options.php:178,
#: framework/features/blocks/socials/options.php:130,
#: framework/premium/features/premium-header/items/contacts/options.php:472
msgid "Solid"
msgstr "Puna"

#: framework/features/blocks/about-me/options.php:219,
#: framework/features/blocks/contact-info/options.php:581,
#: framework/features/blocks/share-box/options.php:177,
#: framework/features/blocks/socials/options.php:129,
#: framework/premium/features/premium-header/items/contacts/options.php:471
msgid "Outline"
msgstr "Obris"

#: framework/extensions/trending/customizer.php:273,
#: framework/extensions/trending/customizer.php:287
#: static/js/editor/blocks/query/Edit.js:169
msgid "Post Type"
msgstr "Vrsta objave"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:149
msgid "Most commented"
msgstr "Najkomentiranije"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:165
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:144
msgid "Random"
msgstr "Nasumično"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:117
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:112
msgid "Order by"
msgstr "Poredaj po"

#: framework/features/blocks/dynamic-data/options.php:109,
#: framework/features/blocks/dynamic-data/views/wp-field.php:196
msgid "% comments"
msgstr "% komentara"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:80
msgid "Author Avatar"
msgstr "Avatar autora"

#: framework/features/blocks/socials/options.php:15
msgid "Social Icons"
msgstr "Društvene ikone"

#: framework/features/blocks/about-me/options.php:124
msgid "You can configure social URLs in %s."
msgstr "Možete konfigurirati URL-ove društvenih mreža u %s."

#: framework/features/blocks/about-me/options.php:156,
#: framework/features/blocks/socials/options.php:62,
#: framework/premium/features/premium-header/items/contacts/options.php:405
msgid "Open links in new tab"
msgstr "Otvori poveznice u novoj kartici"

#: framework/features/blocks/about-me/options.php:163,
#: framework/features/blocks/contact-info/options.php:527,
#: framework/features/blocks/share-box/options.php:117,
#: framework/features/blocks/socials/options.php:69,
#: framework/premium/features/premium-header/items/contacts/options.php:411
msgid "Set links to nofollow"
msgstr "Postavi poveznice na nofollow"

#: framework/features/header/items/account/options.php:4
msgid "Profile Page"
msgstr "Profilna stranica"

#: framework/features/header/items/account/options.php:5
msgid "Dashboard Page"
msgstr "Nadzorna ploča"

#: framework/features/header/items/account/options.php:7,
#: framework/features/header/items/account/options.php:36,
#: framework/features/header/items/account/options.php:117,
#: framework/features/header/items/account/options.php:280,
#: framework/features/header/items/account/views/login.php:396
msgid "Custom Link"
msgstr "Prilagođena poveznica"

#: framework/features/header/items/account/options.php:6
msgid "Logout"
msgstr "Odjava"

#: framework/features/header/items/account/options.php:279
msgid "Modal"
msgstr "Modalni prozor"

#: framework/features/header/items/account/options.php:295
msgid "Customizing: Logged in State"
msgstr "Prilagodba: Prijavljeno stanje"

#: framework/features/header/items/account/options.php:319
msgid "Logged In Options"
msgstr "Opcije za prijavljene korisnike"

#: framework/features/header/items/account/options.php:324
msgid "Logged Out Options"
msgstr "Opcije za odjavljene korisnike"

#: framework/features/header/items/account/options.php:611,
#: framework/features/header/items/account/options.php:1059
msgid "Account Action"
msgstr "Akcija računa"

#: framework/features/header/items/account/options.php:145
msgid "Select Menu"
msgstr "Odaberi izbornik"

#: framework/features/header/items/account/options.php:151
msgid "Select menu..."
msgstr "Odaberi izbornik..."

#. translators: placeholder here means the actual URL.
#: framework/features/header/items/account/options.php:155
msgid "Manage your menus in the %sMenus screen%s."
msgstr "Upravljajte svojim izbornicima u %sEkranu izbornika%s."

#: framework/features/header/items/account/options.php:702,
#: framework/features/header/items/account/options.php:1073
msgid "Custom Page Link"
msgstr "Prilagođena poveznica stranice"

#: framework/features/header/items/account/options.php:340,
#: framework/features/header/items/account/options.php:739
msgid "Account Image"
msgstr "Slika računa"

#: framework/features/header/items/account/options.php:346
msgid "Avatar"
msgstr "Avatar"

#: framework/features/header/items/account/options.php:357
msgid "Avatar Size"
msgstr "Veličina avatara"

#: framework/features/header/items/account/options.php:487,
#: framework/features/header/items/account/options.php:861,
#: framework/features/header/items/account/options.php:949
msgid "Type 5"
msgstr "Vrsta 5"

#: framework/features/header/items/account/options.php:497,
#: framework/features/header/items/account/options.php:871,
#: framework/features/header/items/account/options.php:959
msgid "Type 6"
msgstr "Vrsta 6"

#: framework/features/header/items/account/options.php:570
msgid "Label Type"
msgstr "Vrsta oznake"

#: framework/features/header/items/account/options.php:587,
#: framework/features/header/items/account/options.php:1045,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:136,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:137
msgid "Label Text"
msgstr "Tekst oznake"

#: framework/features/header/items/account/options.php:173,
#: framework/features/header/items/account/options.php:597,
#: framework/features/header/items/account/views/login.php:100,
#: framework/features/header/items/account/views/login.php:472
msgid "My Account"
msgstr "Moj račun"

#: framework/features/header/items/account/options.php:1125,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:163,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:167,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:170
msgid "Label Font"
msgstr "Font oznake"

#: framework/features/header/items/account/options.php:1135,
#: framework/features/header/items/account/options.php:1174,
#: framework/features/header/items/account/options.php:1218,
#: framework/features/header/items/account/options.php:1260
msgid "Label Color"
msgstr "Boja oznake"

#: framework/features/header/header-options.php:214,
#: framework/features/header/items/account/options.php:1140,
#: framework/features/header/items/account/options.php:1332,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:179,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:305,
#: framework/premium/features/premium-header/items/contacts/options.php:590,
#: framework/premium/features/premium-header/items/contacts/options.php:756,
#: framework/premium/features/premium-header/items/contacts/options.php:901,
#: framework/premium/features/premium-header/items/divider/options.php:27,
#: framework/premium/features/premium-header/items/search-input/options.php:271,
#: framework/premium/features/premium-header/items/search-input/options.php:401,
#: framework/premium/features/premium-header/items/search-input/options.php:531,
#: framework/premium/features/premium-header/items/search-input/options.php:667,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:227,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:358,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:57,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:183,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:359,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:492,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:186,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:313,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:434
msgid "Default State"
msgstr "Zadano stanje"

#: framework/features/header/header-options.php:219,
#: framework/features/header/items/account/options.php:1148,
#: framework/features/header/items/account/options.php:1340,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:184,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:310,
#: framework/premium/features/premium-header/items/contacts/options.php:595,
#: framework/premium/features/premium-header/items/contacts/options.php:761,
#: framework/premium/features/premium-header/items/contacts/options.php:909,
#: framework/premium/features/premium-header/items/divider/options.php:32,
#: framework/premium/features/premium-header/items/search-input/options.php:276,
#: framework/premium/features/premium-header/items/search-input/options.php:406,
#: framework/premium/features/premium-header/items/search-input/options.php:536,
#: framework/premium/features/premium-header/items/search-input/options.php:672,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:232,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:363,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:62,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:191,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:364,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:500,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:191,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:318,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:442
msgid "Transparent State"
msgstr "Prozirno stanje"

#: framework/features/header/header-options.php:227,
#: framework/features/header/items/account/options.php:1161,
#: framework/features/header/items/account/options.php:1353,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:193,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:319,
#: framework/premium/features/premium-header/items/contacts/options.php:604,
#: framework/premium/features/premium-header/items/contacts/options.php:770,
#: framework/premium/features/premium-header/items/contacts/options.php:919,
#: framework/premium/features/premium-header/items/divider/options.php:41,
#: framework/premium/features/premium-header/items/search-input/options.php:285,
#: framework/premium/features/premium-header/items/search-input/options.php:415,
#: framework/premium/features/premium-header/items/search-input/options.php:545,
#: framework/premium/features/premium-header/items/search-input/options.php:681,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:241,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:372,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:71,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:204,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:373,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:510,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:200,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:327,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:452
msgid "Sticky State"
msgstr "Ljepljivo stanje"

#: framework/features/header/items/account/options.php:1499
msgid "Item Margin"
msgstr "Razmak stavki"

#: framework/features/header/items/account/options.php:1514
msgid "Modal Options"
msgstr "Opcije modalnog prozora"

#: framework/features/header/items/account/options.php:1705,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:451
msgid "Modal Shadow"
msgstr "Sjena modalnog prozora"

#: framework/features/header/items/account/options.php:1677,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:422
msgid "Modal Background"
msgstr "Pozadina modalnog prozora"

#: framework/features/header/items/account/options.php:1691,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:437
msgid "Modal Backdrop"
msgstr "Pozadina preklopa modalnog prozora"

#: framework/features/blocks/contact-info/options.php:13,
#: framework/features/header/items/account/options.php:2059,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:441,
#: framework/premium/features/premium-header/items/contacts/options.php:486,
#: framework/premium/features/premium-header/items/contacts/options.php:541,
#: framework/premium/features/premium-header/items/language-switcher/options.php:120,
#: framework/premium/features/premium-header/items/language-switcher/options.php:251,
#: framework/premium/features/premium-header/items/search-input/options.php:239,
#: framework/premium/features/premium-header/items/search-input/options.php:936,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:644,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:578
msgid "Element Visibility"
msgstr "Vidljivost elementa"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:655
msgid "You have no %s fields declared for this custom post type."
msgstr "Nemate definirana polja %s za ovu vrstu prilagođene objave."

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:667,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1401,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1412
msgid "Field"
msgstr "Polje"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:679,
#: framework/premium/features/premium-header/items/language-switcher/options/common.php:24
msgid "Label"
msgstr "Oznaka"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:728
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:319
msgid "Fallback"
msgstr "Zamjenski sadržaj"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:755
msgid "%s Field"
msgstr "%s Polje"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1399
msgid "%s %s Font"
msgstr "%s %s Font"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1410
msgid "%s %s Color"
msgstr "%s %s Boja"

#: framework/helpers/exts-configs.php:142,
#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:11,
#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:68
msgid "Read Time"
msgstr "Vrijeme čitanja"

#: framework/premium/extensions/post-types-extra/features/filtering/helpers.php:191,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:195,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:842
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:65
msgid "All"
msgstr "Sve"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:92,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:803
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:75
msgid "Featured Image"
msgstr "Istaknuta slika"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:110
msgid "Accent Color"
msgstr "Naglašena boja"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/helpers.php:150,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/helpers.php:209
msgid "Add to wishlist"
msgstr "Dodaj na listu želja"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:9
msgid "Select a page"
msgstr "Odaberi stranicu"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:23
msgid "Show Wishlist Page To"
msgstr "Prikaži stranicu liste želja korisnicima"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:29
msgid "Logged Users"
msgstr "Prijavljeni korisnici"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:30
msgid "All Users"
msgstr "Svi korisnici"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:40
msgid "Wishlist Page"
msgstr "Stranica liste želja"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:46
msgid "The page you select here will display the wish list for your logged out users."
msgstr "Stranica koju ovdje odaberete prikazat će listu želja za odjavljene korisnike."

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:25
msgid "Archive Page"
msgstr "Arhivska stranica"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:227,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:240,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:276,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:273,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:303,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:332,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:405,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:435,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:464,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:103,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:133,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:162
msgid "Hover/Active"
msgstr "Lebdenje/Aktivno"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/views/table-no-results.php:6
msgid "You don't have any products in your wish list yet."
msgstr "Još nemate proizvoda na listi želja."

#: framework/premium/extensions/woocommerce-extra/features/archive-card/feature.php:56,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:54,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:82
msgid "Add to cart"
msgstr "Dodaj u košaricu"

#: framework/premium/extensions/woocommerce-extra/features/compare/views/bar.php:101,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:142,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:145,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/views/columns/product-remove-button.php:12
msgid "Remove Product"
msgstr "Ukloni proizvod"

#: framework/premium/extensions/woocommerce-extra/includes/woo-import-export.php:36
msgid "Blocksy Variation Images"
msgstr "Blocksy varijacijske slike"

#: framework/premium/features/content-blocks/options/404.php:33,
#: framework/premium/features/content-blocks/options/header.php:55,
#: framework/premium/features/content-blocks/options/hook.php:159,
#: framework/premium/features/content-blocks/options/maintenance.php:30,
#: framework/premium/features/content-blocks/options/nothing_found.php:51,
#: framework/premium/features/content-blocks/options/popup.php:41
msgid "Container Structure"
msgstr "Struktura kontejnera"

#: framework/premium/features/content-blocks/options/404.php:65,
#: framework/premium/features/content-blocks/options/archive.php:132,
#: framework/premium/features/content-blocks/options/header.php:83,
#: framework/premium/features/content-blocks/options/hook.php:192,
#: framework/premium/features/content-blocks/options/maintenance.php:62,
#: framework/premium/features/content-blocks/options/nothing_found.php:83,
#: framework/premium/features/content-blocks/options/single.php:64
msgid "Narrow Width"
msgstr "Uža širina"

#: framework/premium/features/content-blocks/options/404.php:70,
#: framework/premium/features/content-blocks/options/archive.php:137,
#: framework/premium/features/content-blocks/options/header.php:88,
#: framework/premium/features/content-blocks/options/hook.php:197,
#: framework/premium/features/content-blocks/options/maintenance.php:67,
#: framework/premium/features/content-blocks/options/nothing_found.php:88,
#: framework/premium/features/content-blocks/options/single.php:69
msgid "Normal Width"
msgstr "Normalna širina"

#: framework/premium/features/content-blocks/options/404.php:76,
#: framework/premium/features/content-blocks/options/archive.php:153,
#: framework/premium/features/content-blocks/options/header.php:94,
#: framework/premium/features/content-blocks/options/hook.php:203,
#: framework/premium/features/content-blocks/options/maintenance.php:73,
#: framework/premium/features/content-blocks/options/nothing_found.php:94,
#: framework/premium/features/content-blocks/options/single.php:85
msgid "Content Area Style"
msgstr "Stil područja sadržaja"

#: framework/premium/features/content-blocks/options/404.php:90,
#: framework/premium/features/content-blocks/options/archive.php:167,
#: framework/premium/features/content-blocks/options/header.php:108,
#: framework/premium/features/content-blocks/options/hook.php:217,
#: framework/premium/features/content-blocks/options/maintenance.php:87,
#: framework/premium/features/content-blocks/options/nothing_found.php:108
msgid "Content Area Vertical Spacing"
msgstr "Okomiti razmak područja sadržaja"

#: framework/premium/features/content-blocks/options/404.php:102,
#: framework/premium/features/content-blocks/options/archive.php:179,
#: framework/premium/features/content-blocks/options/header.php:120,
#: framework/premium/features/content-blocks/options/hook.php:229,
#: framework/premium/features/content-blocks/options/maintenance.php:99,
#: framework/premium/features/content-blocks/options/nothing_found.php:120,
#: framework/premium/features/content-blocks/options/single.php:111
msgid "Top & Bottom"
msgstr "Gore i dolje"

#: framework/premium/features/content-blocks/options/404.php:105,
#: framework/premium/features/content-blocks/options/archive.php:182,
#: framework/premium/features/content-blocks/options/header.php:123,
#: framework/premium/features/content-blocks/options/hook.php:232,
#: framework/premium/features/content-blocks/options/maintenance.php:102,
#: framework/premium/features/content-blocks/options/nothing_found.php:123,
#: framework/premium/features/content-blocks/options/single.php:114
msgid "Only Top"
msgstr "Samo gore"

#: framework/premium/features/content-blocks/options/404.php:108,
#: framework/premium/features/content-blocks/options/archive.php:185,
#: framework/premium/features/content-blocks/options/header.php:126,
#: framework/premium/features/content-blocks/options/hook.php:235,
#: framework/premium/features/content-blocks/options/maintenance.php:105,
#: framework/premium/features/content-blocks/options/nothing_found.php:126,
#: framework/premium/features/content-blocks/options/single.php:117
msgid "Only Bottom"
msgstr "Samo dolje"

#: framework/premium/features/content-blocks/options/hook.php:60
msgid "Location & Priority"
msgstr "Lokacija i prioritet"

#: framework/premium/features/content-blocks/options/hook.php:23,
#: framework/premium/features/content-blocks/options/hook.php:100
#: framework/premium/static/js/options/MultipleLocationsSelect.js:76
msgid "Custom Hook"
msgstr "Prilagođeni hook"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:730
msgid "Mixed"
msgstr "Miješano"

#: framework/premium/features/content-blocks/options/popup.php:438,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:628
msgid "Popup Position"
msgstr "Pozicija skočnog prozora"

#: framework/premium/features/content-blocks/options/popup.php:384,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:573
msgid "Popup Size"
msgstr "Veličina skočnog prozora"

#: framework/premium/features/content-blocks/options/popup.php:390,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:580
msgid "Small Size"
msgstr "Mala veličina"

#: framework/premium/features/content-blocks/options/popup.php:391,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:581
msgid "Medium Size"
msgstr "Srednja veličina"

#: framework/premium/features/content-blocks/options/popup.php:392,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:582
msgid "Large Size"
msgstr "Velika veličina"

#: framework/premium/features/content-blocks/options/popup.php:393,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:583
msgid "Custom Size"
msgstr "Prilagođena veličina"

#: framework/premium/features/content-blocks/options/popup.php:403,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:593
msgid "Max Width"
msgstr "Maksimalna širina"

#: framework/premium/features/content-blocks/options/popup.php:419,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:609
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:510
msgid "Max Height"
msgstr "Maksimalna visina"

#: framework/premium/features/content-blocks/options/popup.php:339,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:525
msgid "Popup Animation"
msgstr "Animacija skočnog prozora"

#: framework/premium/features/content-blocks/options/popup.php:345,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:531
msgid "Fade in fade out"
msgstr "Nestajanje i pojavljivanje"

#: framework/premium/features/content-blocks/options/popup.php:346,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:532
msgid "Zoom in zoom out"
msgstr "Zumiranje i izzumiranje"

#: framework/premium/features/content-blocks/options/popup.php:347,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:533
msgid "Slide in from left"
msgstr "Klizanje s lijeva"

#: framework/premium/features/content-blocks/options/popup.php:348,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:534
msgid "Slide in from right"
msgstr "Klizanje s desna"

#: framework/premium/features/content-blocks/options/popup.php:349,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:535
msgid "Slide in from top"
msgstr "Klizanje s vrha"

#: framework/premium/features/content-blocks/options/popup.php:350,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:536
msgid "Slide in from bottom"
msgstr "Klizanje s dna"

#: framework/premium/features/content-blocks/options/popup.php:355,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:542
msgid "Animation Speed"
msgstr "Brzina animacije"

#: framework/premium/features/content-blocks/options/popup.php:372,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:560
msgid "Entrance Value"
msgstr "Ulazna vrijednost"

#: framework/premium/features/content-blocks/options/popup.php:69
msgid "Trigger Condition"
msgstr "Uvjet pokretanja"

#: framework/premium/features/content-blocks/options/popup.php:93,
#: framework/premium/features/content-blocks/options/popup.php:111
msgid "Element Class"
msgstr "Klasa elementa"

#: framework/premium/features/content-blocks/options/popup.php:99,
#: framework/premium/features/content-blocks/options/popup.php:117
msgid "Separate each class by comma if you have multiple elements."
msgstr "Razdvojite svaku klasu zarezom ako imate više elemenata."

#: framework/premium/features/content-blocks/options/popup.php:129
msgid "Scroll Direction"
msgstr "Smjer pomicanja"

#: framework/premium/features/content-blocks/options/popup.php:134
msgid "Scroll Down"
msgstr "Pomakni dolje"

#: framework/premium/features/content-blocks/options/popup.php:135
msgid "Scroll Up"
msgstr "Pomakni gore"

#: framework/premium/features/content-blocks/options/popup.php:140
msgid "Scroll Distance"
msgstr "Udaljenost pomicanja"

#: framework/premium/features/content-blocks/options/popup.php:149
msgid "Set the scroll distance till the popup block will appear."
msgstr "Postavite udaljenost pomicanja prije nego se pojavi skočni prozor."

#: framework/premium/features/content-blocks/options/popup.php:167
msgid "Inactivity Time"
msgstr "Vrijeme neaktivnosti"

#: framework/premium/features/content-blocks/options/popup.php:173
msgid "Set the inactivity time (in seconds) till the popup block will appear."
msgstr "Postavite vrijeme neaktivnosti (u sekundama) prije nego se pojavi skočni prozor."

#: framework/premium/features/content-blocks/options/popup.php:185
msgid "After X Time"
msgstr "Nakon X vremena"

#: framework/premium/features/content-blocks/options/popup.php:191
msgid "Set after how much time (in seconds) the popup block will appear."
msgstr "Postavite nakon koliko vremena (u sekundama) će se pojaviti skočni prozor."

#: framework/premium/features/content-blocks/options/popup.php:522,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:676
msgid "Padding"
msgstr "Unutarnji razmak"

#: framework/features/header/items/account/options.php:2034,
#: framework/premium/features/content-blocks/options/popup.php:533,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:298,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:688,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:455,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:330,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:194
msgid "Border Radius"
msgstr "Polumjer obruba"

#: framework/premium/features/content-blocks/options/popup.php:563,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:666
msgid "Popup Offset"
msgstr "Pomak skočnog prozora"

#: framework/premium/features/content-blocks/options/popup.php:581
msgid "Container Overflow"
msgstr "Prelijevanje sadržaja kontejnera"

#: framework/premium/features/content-blocks/options/popup.php:588
msgid "Hidden"
msgstr "Skriveno"

#: framework/premium/features/content-blocks/options/popup.php:589
msgid "Visible"
msgstr "Vidljivo"

#: framework/premium/features/content-blocks/options/popup.php:590
msgid "Scroll"
msgstr "Pomicanje"

#: framework/premium/features/content-blocks/options/popup.php:592
msgid "Control what happens to the content that is too big to fit into the popup."
msgstr "Kontrolirajte što se događa sa sadržajem koji je prevelik da stane u skočni prozor."

#: framework/premium/features/content-blocks/options/popup.php:596
msgid "Close Button"
msgstr "Gumb za zatvaranje"

#: framework/premium/features/content-blocks/options/popup.php:604
msgid "Inside"
msgstr "Unutra"

#: framework/premium/features/content-blocks/options/popup.php:605
msgid "Outside"
msgstr "Vani"

#: framework/premium/features/content-blocks/options/popup.php:679,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:720,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:299,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:72
msgid "Popup Background"
msgstr "Pozadina skočnog prozora"

#: framework/premium/features/content-blocks/options/popup.php:693
msgid "Popup Backdrop Background"
msgstr "Pozadina preklopa skočnog prozora"

#: framework/helpers/exts-configs.php:156,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:25
msgid "Posts Filter"
msgstr "Filter objava"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:59
msgid "Filtering Behavior"
msgstr "Ponašanje filtriranja"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:67
msgid "Instant Reload"
msgstr "Trenutno osvježavanje"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:68
msgid "Page Reload"
msgstr "Osvježavanje stranice"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:103
msgid "Items Horizontal Spacing"
msgstr "Vodoravni razmak stavki"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:115,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:79
msgid "Items Vertical Spacing"
msgstr "Okomiti razmak stavki"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:127
msgid "Container Bottom Spacing"
msgstr "Donji razmak kontejnera"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:139,
#: framework/premium/features/premium-header/items/contacts/options.php:510,
#: framework/premium/features/premium-header/items/language-switcher/options.php:90,
#: framework/premium/features/premium-header/items/search-input/options.php:207
msgid "Horizontal Alignment"
msgstr "Vodoravno poravnanje"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:284
msgid "Button Padding"
msgstr "Unutarnji razmak gumba"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:4
msgid "Read Progress"
msgstr "Napredak čitanja"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:24
msgid "Indicator Height"
msgstr "Visina indikatora"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:34
msgid "Auto Hide"
msgstr "Automatsko sakrivanje"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:38
msgid "Automatically hide the read progress bar once you arrive at the bottom of the article."
msgstr "Automatski sakrij traku napretka čitanja kada dosegnete dno članka."

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:71
msgid "Main Color"
msgstr "Glavna boja"

#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:83,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:84
msgid "Icon Badge"
msgstr "Oznaka ikone"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:174,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:178,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:181
msgid "Label Font Color"
msgstr "Boja fonta oznake"

#: framework/premium/features/premium-header/items/contacts/config.php:4
msgid "Contacts"
msgstr "Kontakti"

#: framework/features/blocks/about-me/options.php:178,
#: framework/features/blocks/contact-info/options.php:548,
#: framework/premium/features/premium-header/items/contacts/options.php:435,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:16
msgid "Items Spacing"
msgstr "Razmak stavki"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:420,
#: framework/premium/features/premium-header/items/contacts/options.php:1051,
#: framework/premium/features/premium-header/items/content-block/options.php:40,
#: framework/premium/features/premium-header/items/divider/options.php:107,
#: framework/premium/features/premium-header/items/divider/options.php:125,
#: framework/premium/features/premium-header/items/search-input/options.php:804,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:478,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:172,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:625,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:557
msgid "Margin"
msgstr "Margina"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:106,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/view.php:45
msgid "Dark Mode"
msgstr "Tamni način"

#: framework/features/header/items/account/options.php:68,
#: framework/features/header/items/account/options.php:1999,
#: framework/premium/features/premium-header/items/divider/config.php:4
msgid "Divider"
msgstr "Razdjelnik"

#: framework/premium/features/premium-header/items/divider/options.php:6
msgid "Size"
msgstr "Veličina"

#: framework/features/conditions/rules/localization.php:11,
#: framework/premium/features/premium-header/items/language-switcher/config.php:14
msgid "Languages"
msgstr "Jezici"

#: framework/premium/features/premium-header/items/language-switcher/options.php:274
msgid "Top Level Options"
msgstr "Opcije najviše razine"

#: framework/features/header/items/account/options.php:618,
#: framework/premium/features/premium-header/items/language-switcher/options.php:170
msgid "Dropdown"
msgstr "Padajući izbornik"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:23
msgid "Flag"
msgstr "Zastavica"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:39
msgid "Label Style"
msgstr "Stil oznake"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:46
msgid "Long"
msgstr "Dugačko"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:47
msgid "Short"
msgstr "Kratko"

#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:26
msgid "Hide Current Language"
msgstr "Sakrij trenutni jezik"

#: framework/features/header/items/account/options.php:1890,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:59
msgid "Dropdown Options"
msgstr "Opcije padajućeg izbornika"

#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:70
msgid "Dropdown Top Offset"
msgstr "Pomak vrha padajućeg izbornika"

#: framework/premium/features/premium-header/items/search-input/config.php:4
msgid "Search Box"
msgstr "Pretraživačko polje"

#: framework/features/blocks/search/options.php:46,
#: framework/features/blocks/search/options.php:52,
#: framework/features/blocks/search/options.php:64,
#: framework/features/blocks/search/options.php:145,
#: framework/premium/features/premium-header/items/search-input/options.php:45
msgid "Placeholder Text"
msgstr "Tekst rezerviranog mjesta"

#: framework/premium/features/premium-header/items/search-input/options.php:55
msgid "Input Maximum Width"
msgstr "Maksimalna širina unosa"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:87,
#: framework/features/blocks/search/options.php:70,
#: framework/premium/features/premium-header/items/search-input/options.php:67
msgid "Input Height"
msgstr "Visina unosa"

#: framework/features/blocks/search/options.php:97,
#: framework/premium/features/premium-header/items/search-input/options.php:88
msgid "Live Results"
msgstr "Rezultati uživo"

#: framework/features/blocks/search/options.php:109,
#: framework/premium/features/premium-header/items/search-input/options.php:100
msgid "Live Results Images"
msgstr "Slike rezultata uživo"

#: framework/features/blocks/search/options.php:185,
#: framework/premium/features/premium-header/items/search-input/options.php:175
msgid "Search Through Criteria"
msgstr "Pretraživanje po kriterijima"

#: framework/features/blocks/search/options.php:187,
#: framework/premium/features/premium-header/items/search-input/options.php:176
msgid "Chose in which post types do you want to perform searches."
msgstr "Odaberite u kojim vrstama objava želite vršiti pretragu."

#: framework/premium/features/premium-header/items/search-input/options.php:396,
#: framework/premium/features/premium-header/items/search-input/options.php:425,
#: framework/premium/features/premium-header/items/search-input/options.php:457,
#: framework/premium/features/premium-header/items/search-input/options.php:487
msgid "Input Icon Color"
msgstr "Boja ikone unosa"

#: framework/premium/features/premium-header/items/search-input/options.php:833
#: static/js/editor/blocks/search/Edit.js:346
msgid "Dropdown Text Color"
msgstr "Boja teksta padajućeg izbornika"

#: framework/premium/features/premium-header/items/search-input/options.php:864
msgid "Dropdown Background"
msgstr "Pozadina padajućeg izbornika"

#: framework/premium/features/premium-header/items/widget-area-1/config.php:4
msgid "Widget Area"
msgstr "Područje widgeta"

#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker.js:14
#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker/Implementation.js:72
#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:362
#: static/js/dashboard/NoTheme.js:64 static/js/dashboard/VersionMismatch.js:61
#: static/js/dashboard/screens/SiteExport.js:310
#: static/js/notifications/VersionMismatchNotice.js:73
msgid "Loading..."
msgstr "Učitavanje..."

#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker/Implementation.js:73
msgid "Invalid API Key..."
msgstr "Neispravan API ključ..."

#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker/Implementation.js:101
#: framework/extensions/newsletter-subscribe/dashboard-static/js/ListPicker.js:119
msgid "Select list..."
msgstr "Odaberi popis..."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:218
msgid "More information on how to generate an API key for Mailchimp can be found %shere%s."
msgstr "Više informacija o tome kako generirati API ključ za Mailchimp možete pronaći %shere%s."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:286
msgid "More information on how to generate an API key for ConvertKit can be found %shere%s."
msgstr "Više informacija o tome kako generirati API ključ za ConvertKit možete pronaći %shere%s."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:184
msgid "API Key"
msgstr "API ključ"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:363
#: framework/extensions/product-reviews/static/js/ProductReviews.js:94
#: framework/premium/extensions/mega-menu/static/js/SettingsManager.js:280
#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:111
#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:910
#: framework/premium/static/js/footer/EditConditions.js:143
#: framework/premium/static/js/media-video/components/EditVideoMeta.js:106
msgid "Save Settings"
msgstr "Spremi postavke"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:153
msgid "Pick Mailing Service"
msgstr "Odaberi uslugu slanja e-pošte"

#: framework/extensions/product-reviews/static/js/ProductReviews.js:22
msgid "Product Reviews Settings"
msgstr "Postavke recenzija proizvoda"

#: framework/extensions/product-reviews/static/js/ProductReviews.js:25
msgid "Configure the slugs for single and category pages of the product review custom post type."
msgstr "Konfigurirajte sluge za pojedinačne i kategorijske stranice prilagođene vrste objava recenzija proizvoda."

#: framework/extensions/product-reviews/static/js/ProductReviews.js:43
msgid "Single Slug"
msgstr "Slug pojedinačne objave"

#: framework/extensions/product-reviews/static/js/ProductReviews.js:49
msgid "Category Slug"
msgstr "Slug kategorije"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:84
msgid "Adobe Fonts Settings"
msgstr "Postavke Adobe fontova"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:116
msgid "Project ID"
msgstr "ID projekta"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:158
msgid "Fetch Fonts"
msgstr "Dohvati fontove"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:182
#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:54
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:39
msgid "Variations"
msgstr "Varijacije"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/CustomFonts.js:74
msgid "Custom Fonts Settings"
msgstr "Postavke prilagođenih fontova"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/CustomFonts.js:76
msgid "Here you can see all your custom fonts that can be used in all typography options across the theme."
msgstr "Ovdje možete vidjeti sve svoje prilagođene fontove koji se mogu koristiti u svim opcijama tipografije unutar teme."

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:28
msgid "Dynamic Font"
msgstr "Dinamički font"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:37
msgid "Variable font"
msgstr "Varijabilni font"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:78
#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:322
msgid "Edit Font"
msgstr "Uredi font"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:100
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:78
msgid "Remove Font"
msgstr "Ukloni font"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:216
msgid "Change"
msgstr "Promijeni"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:217
msgid "Choose"
msgstr "Odaberi"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:127
#: static/js/options/ConditionsManager/SingleCondition.js:95
msgid "Select variation"
msgstr "Odaberi varijaciju"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:147
msgid "Regular"
msgstr "Regular"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:154
#: framework/premium/static/js/typography/providers/kadence.js:71
msgid "Italic"
msgstr "Kurziv"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/RegularTab.js:30
msgid "Upload only the %s.woff2%s or %s.ttf%s font file formats (see browser coverage %shere%s). Use %sthis converter tool%s if you don't have these font formats."
msgstr "Prenesite samo formate datoteka fontova %s.woff2%s ili %s.ttf%s (vidi pokrivenost preglednika %shere%s). Koristite %sovaj alat za pretvorbu%s ako nemate te formate fontova."

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:90
msgid "Font Name"
msgstr "Naziv fonta"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:459
msgid "Save Custom Font"
msgstr "Spremi prilagođeni font"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:79
msgid "Add Variation"
msgstr "Dodaj varijaciju"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:258
msgid "Download a font and serve it directly from your server, this is handy for those who want to comply with GDPR regulations or serve the font via CDN."
msgstr "Preuzmite font i poslužite ga izravno s vašeg poslužitelja, što je korisno za one koji žele biti u skladu s GDPR regulativom ili poslužiti font putem CDN-a."

#: framework/premium/extensions/mega-menu/static/js/SettingsManager.js:189
msgid "Item Settings"
msgstr "Postavke stavke"

#: framework/premium/extensions/sidebars/static/js/BlockWidgetControls.js:60
msgid "Remove Sidebar"
msgstr "Ukloni bočnu traku"

#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:65
msgid "%s - Sidebar Display Conditions"
msgstr "%s - Uvjeti prikaza bočne trake"

#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:73
msgid "Add one or more conditions in order to display your sidebar."
msgstr "Dodajte jedan ili više uvjeta kako biste prikazali svoju bočnu traku."

#: framework/premium/extensions/sidebars/static/js/main.js:53
msgid "Enter a name in the input below and hit the Create Sidebar button."
msgstr "Unesite naziv u polje ispod i pritisnite gumb Stvori bočnu traku."

#: framework/premium/extensions/sidebars/static/js/main.js:60
msgid "Sidebar name"
msgstr "Naziv bočne trake"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:108
msgid "Advanced"
msgstr "Napredno"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:123
msgid "Agency Details"
msgstr "Detalji agencije"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:131
msgid "Agency Name"
msgstr "Naziv agencije"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:159
msgid "Agency URL"
msgstr "URL agencije"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:187
msgid "Agency Support/Contact Form URL"
msgstr "URL obrasca za podršku/kontakt"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:220
msgid "Theme Details"
msgstr "Detalji teme"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:228
msgid "Theme Name"
msgstr "Naziv teme"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:256
msgid "Theme Description"
msgstr "Opis teme"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:286
msgid "Theme Screenshot URL"
msgstr "URL snimke zaslona teme"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:384
msgid "You can insert the link to a self hosted image or upload one. The recommended image size is 1200px wide by 900px tall."
msgstr "Možete unijeti poveznicu na vlastitu sliku ili prenijeti jednu. Preporučena veličina slike je 1200px širine i 900px visine."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:393
msgid "Theme Icon URL"
msgstr "URL ikone teme"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:489
msgid "You can insert the link to a self hosted image or upload one. The recommended image size is 18px wide by 18px tall."
msgstr "Možete unijeti poveznicu na vlastitu sliku ili prenijeti jednu. Preporučena veličina slike je 18px širine i 18px visine."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:498
msgid "Gutenberg Options Panel Icon URL"
msgstr "URL ikone panela opcija Gutenberg"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:596
msgid "You can insert the link to a self hosted image or upload one. Please note that only icons in SVG format are allowed here to not break the editor interactiveness."
msgstr "Možete unijeti poveznicu na vlastitu sliku ili prenijeti jednu. Napominjemo da su ovdje dopuštene samo SVG ikone kako bi se zadržala interaktivnost uređivača."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:616
msgid "Plugin Name"
msgstr "Naziv dodatka"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:644
msgid "Plugin Description"
msgstr "Opis dodatka"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:674
msgid "Plugin Thumbnail URL"
msgstr "URL minijature dodatka"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:764
msgid "Choose File"
msgstr "Odaberi datoteku"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:772
msgid "You can insert the link to a self hosted image or upload one. The recommended image size is 256px wide by 256px tall."
msgstr "Možete unijeti poveznicu na vlastitu sliku ili prenijeti jednu. Preporučena veličina slike je 256px širine i 256px visine."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:795
msgid "Hide Account Menu Item"
msgstr "Sakrij stavku izbornika računa"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:803
msgid "Hide Starter Sites Tab"
msgstr "Sakrij karticu početnih stranica"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:810
msgid "Hide Useful Plugins Tab"
msgstr "Sakrij karticu korisnih dodataka"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:818
msgid "Hide Changelog Tab"
msgstr "Sakrij karticu dnevnika promjena"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:826
msgid "Hide Support Section"
msgstr "Sakrij odjeljak podrške"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:876
msgid "Hide White Label Extension"
msgstr "Sakrij White Label ekstenziju"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:891
msgid "Please note that the white label extension will be hidden if this option is enabled. In order to bring it back you have to hit the SHIFT key and click on the dashboard logo."
msgstr "Napominjemo da će White Label ekstenzija biti skrivena ako omogućite ovu opciju. Da biste je vratili, morate pritisnuti tipku SHIFT i kliknuti na logotip nadzorne ploče."

#: framework/premium/static/js/footer/CloneItem.js:66
#: framework/premium/static/js/header/CloneItem.js:66
msgid "Clone Item"
msgstr "Kloniraj stavku"

#: framework/premium/static/js/footer/CloneItem.js:90
#: framework/premium/static/js/header/CloneItem.js:90
msgid "Remove Item"
msgstr "Ukloni stavku"

#: framework/premium/static/js/footer/CreateFooter.js:107
#: framework/premium/static/js/header/CreateHeader.js:106
msgid "Copy elements & styles from"
msgstr "Kopiraj elemente i stilove iz"

#: framework/premium/static/js/footer/CreateFooter.js:118
msgid "Picker Footer"
msgstr "Podnožje birača"

#: framework/premium/static/js/footer/CreateFooter.js:133
#: framework/premium/static/js/footer/PanelsManager.js:52
msgid "Global Footer"
msgstr "Globalno podnožje"

#: framework/premium/static/js/footer/CreateFooter.js:137
#: framework/premium/static/js/header/CreateHeader.js:138
msgid "Secondary"
msgstr "Sekundarno"

#: framework/premium/static/js/footer/CreateFooter.js:141
#: framework/premium/static/js/header/CreateHeader.js:142
msgid "Centered"
msgstr "Centrirano"

#: framework/premium/static/js/footer/CreateFooter.js:172
msgid "Create New Footer"
msgstr "Stvori novo podnožje"

#: framework/premium/static/js/footer/CreateFooter.js:50
msgid "Create new footer"
msgstr "Stvori novo podnožje"

#: framework/premium/static/js/footer/CreateFooter.js:53
msgid "Create a new footer and assign it to different pages or posts based on your conditions."
msgstr "Stvorite novo podnožje i dodijelite ga različitim stranicama ili objavama na temelju vaših uvjeta."

#: framework/premium/static/js/footer/CreateFooter.js:72
msgid "Footer name"
msgstr "Naziv podnožja"

#: framework/premium/static/js/footer/EditConditions.js:100
msgid "Add one or more conditions in order to display your footer."
msgstr "Dodajte jedan ili više uvjeta kako biste prikazali svoje podnožje."

#: framework/premium/static/js/footer/EditConditions.js:84
#: static/js/header/EditConditions.js:88
msgid "Add/Edit Conditions"
msgstr "Dodaj/uredi uvjete"

#: framework/premium/static/js/footer/PanelsManager.js:169
msgid "Remove footer"
msgstr "Ukloni podnožje"

#: framework/premium/static/js/footer/PanelsManager.js:193
msgid "Remove Footer"
msgstr "Ukloni podnožje"

#: framework/premium/static/js/footer/PanelsManager.js:196
msgid "You are about to remove a custom footer, are you sure you want to continue?"
msgstr "Upravo ćete ukloniti prilagođeno podnožje, jeste li sigurni da želite nastaviti?"

#: framework/premium/static/js/footer/PanelsManager.js:212
#: framework/premium/static/js/hooks/CodeEditor.js:189
#: static/js/header/PanelsManager.js:217
#: static/js/options/CustomizerOptionsManager.js:463
msgid "Cancel"
msgstr "Odustani"

#: framework/premium/static/js/footer/PanelsManager.js:228
#: static/js/header/PanelsManager.js:233
msgid "Confirm"
msgstr "Potvrdi"

#: framework/premium/static/js/footer/PanelsManager.js:68
#: static/js/header/PanelsManager.js:74
#: static/js/options/DisplayCondition.js:61
msgid "Edit Conditions"
msgstr "Uredi uvjete"

#. translators: %s: PHP version
#: blocksy-companion.php:182
msgid "Blocksy requires PHP version %s+, plugin is currently NOT RUNNING."
msgstr "Blocksy zahtijeva PHP verziju %s+, dodatak trenutno NE RADI."

#. translators: %s: WordPress version
#: blocksy-companion.php:193
msgid "Blocksy requires WordPress version %s+. Because you are using an earlier version, the plugin is currently NOT RUNNING."
msgstr "Blocksy zahtijeva WordPress verziju %s+. Budući da koristite stariju verziju, dodatak trenutno NE RADI."

#: framework/premium/extensions/adobe-typekit/extension.php:46
msgid "Adobe Typekit"
msgstr "Adobe Typekit"

#: framework/premium/extensions/custom-fonts/extension.php:154
msgid "Custom Fonts"
msgstr "Prilagođeni fontovi"

#: framework/premium/extensions/local-google-fonts/extension.php:122
msgid "Local Google Fonts"
msgstr "Lokalni Google fontovi"

#: framework/theme-integration.php:220,
#: framework/features/blocks/share-box/options.php:19
msgid "Facebook"
msgstr "Facebook"

#: framework/theme-integration.php:222,
#: framework/features/blocks/share-box/options.php:37
msgid "LinkedIn"
msgstr "LinkedIn"

#: framework/theme-integration.php:223
msgid "Dribbble"
msgstr "Dribbble"

#: framework/theme-integration.php:224
msgid "Instagram"
msgstr "Instagram"

#: framework/theme-integration.php:225,
#: framework/features/blocks/share-box/options.php:31
msgid "Pinterest"
msgstr "Pinterest"

#: framework/theme-integration.php:226
msgid "WordPress"
msgstr "WordPress"

#: framework/theme-integration.php:227
msgid "GitHub"
msgstr "GitHub"

#: framework/theme-integration.php:228,
#: framework/features/blocks/about-me/options.php:86,
#: framework/premium/features/content-blocks/options/archive.php:74
msgid "Medium"
msgstr "Medium"

#: framework/theme-integration.php:229,
#: framework/premium/features/media-video/options.php:14
msgid "YouTube"
msgstr "YouTube"

#: framework/theme-integration.php:230,
#: framework/premium/features/media-video/options.php:15
msgid "Vimeo"
msgstr "Vimeo"

#: framework/theme-integration.php:231,
#: framework/features/blocks/share-box/options.php:55
msgid "VKontakte"
msgstr "VKontakte"

#: framework/theme-integration.php:232,
#: framework/features/blocks/share-box/options.php:61
msgid "Odnoklassniki"
msgstr "Odnoklassniki"

#: framework/theme-integration.php:233
msgid "TikTok"
msgstr "TikTok"

#: framework/theme-integration.php:278
msgid "Companion"
msgstr "Pratitelj"

#: framework/theme-integration.php:296
msgid "PRO"
msgstr "PRO"

#: framework/features/account-auth.php:119,
#: framework/features/account-auth.php:267
msgid "Check your email"
msgstr "Provjeri svoju e-poštu"

#: framework/features/account-auth.php:273
msgid "Registration Form"
msgstr "Obrazac za registraciju"

#: framework/features/account-auth.php:274
msgid "Register For This Site"
msgstr "Registrirajte se na ovu stranicu"

#: framework/features/conditions/rules/cpt.php:17
msgid "%s Single"
msgstr "%s Pojedinačno"

#: framework/features/conditions/rules/cpt.php:27
msgid "%s Archive"
msgstr "%s Arhiva"

#: framework/features/conditions/rules/cpt.php:40
msgid "%s %s Taxonomy"
msgstr "%s %s Taksonomija"

#: framework/features/conditions/rules/basic.php:3
msgid "Entire Website"
msgstr "Cijela web stranica"

#: framework/features/conditions/rules/basic.php:57
msgid "Basic"
msgstr "Osnovno"

#: framework/extensions/trending/customizer.php:4,
#: framework/features/blocks/query/block.php:19,
#: framework/features/blocks/search/options.php:6,
#: framework/features/conditions/rules/posts.php:33,
#: framework/premium/features/premium-header/items/search-input/options.php:4
msgid "Posts"
msgstr "Objave"

#: framework/features/conditions/rules/posts.php:27,
#: framework/premium/features/content-blocks/hooks-manager.php:440,
#: framework/premium/features/content-blocks/hooks-manager.php:448,
#: framework/premium/features/content-blocks/hooks-manager.php:456,
#: framework/premium/features/content-blocks/hooks-manager.php:463,
#: framework/premium/features/content-blocks/hooks-manager.php:470,
#: framework/premium/features/content-blocks/hooks-manager.php:478
msgid "Single Post"
msgstr "Pojedinačna objava"

#: framework/features/conditions/rules/posts.php:15
msgid "Post Categories"
msgstr "Kategorije objava"

#: framework/features/conditions/rules/posts.php:20
msgid "Post Tags"
msgstr "Oznake objava"

#: framework/features/blocks/search/options.php:7,
#: framework/features/conditions/rules/pages.php:52,
#: framework/premium/features/premium-header/items/search-input/options.php:5
msgid "Pages"
msgstr "Stranice"

#: framework/features/conditions/rules/pages.php:8
msgid "Single Page"
msgstr "Pojedinačna stranica"

#: framework/features/conditions/rules/specific.php:48
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:69
msgid "Specific"
msgstr "Specifično"

#: framework/features/conditions/rules/specific.php:10
msgid "Post ID"
msgstr "ID objave"

#: framework/features/conditions/rules/specific.php:15
msgid "Page ID"
msgstr "ID stranice"

#: framework/features/conditions/rules/specific.php:20
#: static/js/options/ConditionsManager/PostIdPicker.js:79
msgid "Custom Post Type ID"
msgstr "ID prilagođene vrste objave"

#: framework/features/conditions/rules/specific.php:38,
#: framework/features/conditions/rules/woo.php:65
msgid "Taxonomy ID"
msgstr "ID taksonomije"

#: framework/features/conditions/rules/specific.php:25
msgid "Post with Taxonomy ID"
msgstr "Objava s ID-jem taksonomije"

#: framework/features/conditions/rules/pages.php:15
msgid "404"
msgstr ""

#: framework/features/blocks/search/options.php:60,
#: framework/features/blocks/search/options.php:66,
#: framework/features/blocks/search/view.php:80,
#: framework/features/blocks/search/view.php:261,
#: framework/features/conditions/rules/pages.php:22,
#: framework/premium/features/premium-header/items/search-input/options.php:48,
#: framework/premium/features/premium-header/items/search-input/view.php:126
#: static/js/editor/blocks/query/edit/PatternSelectionModal.js:92
#: static/js/editor/blocks/search/Preview.js:23
#: static/js/editor/blocks/tax-query/edit/PatternSelectionModal.js:78
msgid "Search"
msgstr "Pretraga"

#: framework/features/conditions/rules/pages.php:27
msgid "Blog"
msgstr "Blog"

#: framework/features/conditions/rules/pages.php:33
msgid "Front Page"
msgstr "Početna stranica"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:70
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:157
msgid "Author"
msgstr "Autor"

#: framework/features/conditions/rules/user-auth.php:20
msgid "User Auth"
msgstr "Autorizacija korisnika"

#: framework/features/conditions/rules/user-auth.php:24
msgid "User Logged In"
msgstr "Korisnik prijavljen"

#: framework/features/conditions/rules/user-auth.php:29
msgid "User Logged Out"
msgstr "Korisnik odjavljen"

#: framework/features/conditions/rules/user-auth.php:34
msgid "User Role"
msgstr "Uloga korisnika"

#: framework/premium/features/content-blocks/options/404.php:19,
#: framework/premium/features/content-blocks/options/header.php:19,
#: framework/premium/features/content-blocks/options/hook.php:24,
#: framework/premium/features/content-blocks/options/nothing_found.php:19,
#: framework/premium/features/content-blocks/options/popup.php:25
msgid "Other"
msgstr "Ostalo"

#: framework/features/conditions-manager.php:307
msgid "Language"
msgstr "Jezik"

#: framework/features/demo-install.php:98
msgid "Your PHP installation doesn't have support for XML. Please install the <i>xml</i> or <i>simplexml</i> PHP extension in order to be able to install starter sites. You might need to contact your hosting provider to assist you in doing so."
msgstr "Vaša PHP instalacija nema podršku za XML. Molimo instalirajte <i>xml</i> ili <i>simplexml</i> PHP ekstenziju kako biste mogli instalirati početne stranice. Možda ćete morati kontaktirati svog hosting providera za pomoć."

#: framework/features/dynamic-css.php:48
msgid "Dynamic CSS Output"
msgstr "Dinamički CSS izlaz"

#: framework/features/dynamic-css.php:52
msgid "The strategy of outputting the dynamic CSS. File - all the CSS code will be placed in a static file, otherwise it will be placed inline in head."
msgstr "Strategija ispisivanja dinamičkog CSS-a. Datoteka - sav CSS kod će biti smješten u statičku datoteku, inače će biti umetnut unutar head oznake."

#: framework/features/dynamic-css.php:54
msgid "File"
msgstr "Datoteka"

#: framework/features/dynamic-css.php:55,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:67,
#: framework/premium/features/premium-header/items/language-switcher/options.php:165
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:159
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:204
msgid "Inline"
msgstr "Inline"

#: framework/features/google-analytics.php:69
msgid "Google Analytics v4"
msgstr "Google Analytics v4"

#: framework/features/google-analytics.php:74
msgid "Link your Google Analytics 4 tracking ID. More info and instructions can be found %shere%s."
msgstr "Povežite svoj Google Analytics 4 ID za praćenje. Više informacija i upute možete pronaći %shere%s."

#: framework/features/opengraph-meta-data.php:13
msgid "OpenGraph Meta Data"
msgstr "OpenGraph Meta podaci"

#: framework/features/opengraph-meta-data.php:17
msgid "Enable the OpenGraph rich meta data features for your website."
msgstr "Omogućite OpenGraph značajke bogatih meta podataka za vašu web stranicu."

#: framework/features/opengraph-meta-data.php:26
msgid "Facebook Page URL"
msgstr "URL Facebook stranice"

#: framework/features/opengraph-meta-data.php:33
msgid "Facebook App ID"
msgstr "Facebook App ID"

#: framework/features/opengraph-meta-data.php:40
msgid "Twitter Username"
msgstr "Twitter korisničko ime"

#: framework/extensions/cookies-consent/config.php:6
msgid "Display a cookie acceptance box in order to comply with the privacy regulations in your country."
msgstr "Prikazivanje okvira za prihvaćanje kolačića kako biste bili u skladu s propisima o privatnosti u vašoj zemlji."

#: framework/extensions/cookies-consent/customizer.php:15,
#: framework/extensions/newsletter-subscribe/customizer.php:12,
#: framework/extensions/product-reviews/extension.php:382,
#: framework/extensions/product-reviews/metabox.php:6,
#: framework/extensions/trending/customizer.php:173,
#: framework/features/header/header-options.php:6,
#: framework/premium/extensions/mega-menu/options.php:6,
#: framework/premium/extensions/shortcuts/customizer.php:761,
#: framework/features/header/items/account/options.php:330,
#: framework/premium/features/content-blocks/options/404.php:55,
#: framework/premium/features/content-blocks/options/archive.php:122,
#: framework/premium/features/content-blocks/options/header.php:73,
#: framework/premium/features/content-blocks/options/hook.php:182,
#: framework/premium/features/content-blocks/options/maintenance.php:52,
#: framework/premium/features/content-blocks/options/nothing_found.php:73,
#: framework/premium/features/content-blocks/options/popup.php:30,
#: framework/premium/features/content-blocks/options/single.php:54,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:6,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:35,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:279,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:520,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:41,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:120,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:378,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:20,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:66,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:11,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:10,
#: framework/premium/features/premium-header/items/contacts/options.php:45,
#: framework/premium/features/premium-header/items/content-block/options.php:5,
#: framework/premium/features/premium-header/items/language-switcher/options.php:278,
#: framework/premium/features/premium-header/items/search-input/options.php:40,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:19,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:163,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:63,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:5,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:5
#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:352
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:111
#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:104
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:43
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:38
msgid "General"
msgstr "Općenito"

#: framework/extensions/cookies-consent/customizer.php:28,
#: framework/premium/extensions/shortcuts/customizer.php:773,
#: framework/features/header/items/account/options.php:447,
#: framework/features/header/items/account/options.php:821,
#: framework/features/header/items/account/options.php:909,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:23,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:48,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:375,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:31,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:56,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:101,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:55,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:56
msgid "Type 1"
msgstr "Vrsta 1"

#: framework/extensions/cookies-consent/customizer.php:33,
#: framework/premium/extensions/shortcuts/customizer.php:778,
#: framework/features/header/items/account/options.php:457,
#: framework/features/header/items/account/options.php:831,
#: framework/features/header/items/account/options.php:919,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:28,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:53,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:376,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:36,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:61,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:106,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:60,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:61
msgid "Type 2"
msgstr "Vrsta 2"

#: framework/extensions/cookies-consent/customizer.php:40
msgid "Cookie period"
msgstr "Razdoblje kolačića"

#: framework/extensions/cookies-consent/customizer.php:48
msgid "One hour"
msgstr "Jedan sat"

#: framework/extensions/cookies-consent/customizer.php:49
msgid "One day"
msgstr "Jedan dan"

#: framework/extensions/cookies-consent/customizer.php:50
msgid "One week"
msgstr "Jedna sedmica"

#: framework/extensions/cookies-consent/customizer.php:51
msgid "One month"
msgstr "Jedan mjesec"

#: framework/extensions/cookies-consent/customizer.php:52
msgid "Three months"
msgstr "Tri mjeseca"

#: framework/extensions/cookies-consent/customizer.php:53
msgid "Six months"
msgstr "Šest mjeseci"

#: framework/extensions/cookies-consent/customizer.php:54
msgid "One year"
msgstr "Jedna godina"

#: framework/extensions/cookies-consent/customizer.php:55
msgid "Forever"
msgstr "Zauvijek"

#: framework/extensions/cookies-consent/customizer.php:62,
#: framework/features/blocks/contact-info/options.php:87,
#: framework/features/blocks/contact-info/options.php:152,
#: framework/features/blocks/contact-info/options.php:215,
#: framework/features/blocks/contact-info/options.php:278,
#: framework/features/blocks/contact-info/options.php:341,
#: framework/features/blocks/contact-info/options.php:404,
#: framework/features/blocks/contact-info/options.php:467,
#: framework/premium/features/content-blocks/hooks-manager.php:267,
#: framework/premium/features/content-blocks/hooks-manager.php:275,
#: framework/premium/features/content-blocks/hooks-manager.php:283,
#: framework/premium/features/content-blocks/hooks-manager.php:291,
#: framework/premium/features/premium-header/items/contacts/options.php:97,
#: framework/premium/features/premium-header/items/contacts/options.php:142,
#: framework/premium/features/premium-header/items/contacts/options.php:187,
#: framework/premium/features/premium-header/items/contacts/options.php:232,
#: framework/premium/features/premium-header/items/contacts/options.php:278,
#: framework/premium/features/premium-header/items/contacts/options.php:323,
#: framework/premium/features/premium-header/items/contacts/options.php:368
msgid "Content"
msgstr "Sadržaj"

#: framework/extensions/cookies-consent/customizer.php:64,
#: framework/extensions/cookies-consent/helpers.php:7
msgid "We use cookies to ensure that we give you the best experience on our website."
msgstr "Koristimo kolačiće kako bismo vam osigurali najbolje iskustvo na našoj web stranici."

#: framework/features/blocks/search/options.php:58
msgid "Button Text"
msgstr "Tekst gumba"

#: framework/extensions/cookies-consent/customizer.php:80,
#: framework/extensions/cookies-consent/helpers.php:10
msgid "Accept"
msgstr "Prihvati"

#: framework/extensions/cookies-consent/customizer.php:142,
#: framework/extensions/newsletter-subscribe/customizer.php:170,
#: framework/extensions/product-reviews/extension.php:422,
#: framework/extensions/trending/customizer.php:582,
#: framework/features/header/header-options.php:203,
#: framework/premium/extensions/mega-menu/options.php:567,
#: framework/premium/extensions/shortcuts/customizer.php:1032,
#: framework/features/header/items/account/options.php:1107,
#: framework/premium/features/content-blocks/options/404.php:119,
#: framework/premium/features/content-blocks/options/archive.php:196,
#: framework/premium/features/content-blocks/options/header.php:137,
#: framework/premium/features/content-blocks/options/hook.php:246,
#: framework/premium/features/content-blocks/options/maintenance.php:116,
#: framework/premium/features/content-blocks/options/nothing_found.php:137,
#: framework/premium/features/content-blocks/options/popup.php:517,
#: framework/premium/features/content-blocks/options/single.php:128,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:147,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:178,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:169,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:406,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:661,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:289,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:458,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:108,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:253,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:570,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:124,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:132,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:190,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:66,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:245,
#: framework/premium/features/premium-header/items/contacts/options.php:571,
#: framework/premium/features/premium-header/items/content-block/options.php:36,
#: framework/premium/features/premium-header/items/language-switcher/options.php:284,
#: framework/premium/features/premium-header/items/search-input/options.php:260,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:66,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:100,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:152,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:154
msgid "Design"
msgstr "Dizajn"

#: framework/extensions/cookies-consent/customizer.php:203,
#: framework/extensions/cookies-consent/customizer.php:270,
#: framework/premium/extensions/mega-menu/options.php:879,
#: framework/premium/extensions/shortcuts/customizer.php:1058,
#: framework/features/header/items/account/options.php:1518,
#: framework/features/header/items/account/options.php:1902,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:202,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:233,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:262,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:195,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:113,
#: framework/premium/features/premium-header/items/contacts/options.php:585,
#: framework/premium/features/premium-header/items/contacts/options.php:614,
#: framework/premium/features/premium-header/items/contacts/options.php:655,
#: framework/premium/features/premium-header/items/contacts/options.php:694,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:111,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:222,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:251,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:281,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:310,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:52,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:81,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:111,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:140,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:217,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:261,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:303,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:209,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:240,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:269
msgid "Font Color"
msgstr "Boja fonta"

#: framework/extensions/cookies-consent/customizer.php:164,
#: framework/extensions/cookies-consent/customizer.php:191,
#: framework/extensions/cookies-consent/customizer.php:220,
#: framework/extensions/cookies-consent/customizer.php:251,
#: framework/extensions/cookies-consent/customizer.php:287,
#: framework/extensions/cookies-consent/customizer.php:316,
#: framework/extensions/newsletter-subscribe/customizer.php:188,
#: framework/extensions/newsletter-subscribe/customizer.php:213,
#: framework/extensions/newsletter-subscribe/customizer.php:245,
#: framework/extensions/newsletter-subscribe/customizer.php:276,
#: framework/extensions/newsletter-subscribe/customizer.php:313,
#: framework/extensions/newsletter-subscribe/customizer.php:345,
#: framework/extensions/trending/customizer.php:610,
#: framework/extensions/trending/customizer.php:673,
#: framework/extensions/trending/customizer.php:726,
#: framework/extensions/trending/customizer.php:766,
#: framework/extensions/trending/customizer.php:798,
#: framework/extensions/trending/customizer.php:848,
#: framework/extensions/trending/customizer.php:889,
#: framework/premium/extensions/mega-menu/options.php:845,
#: framework/premium/extensions/mega-menu/options.php:892,
#: framework/premium/extensions/mega-menu/options.php:911,
#: framework/premium/extensions/shortcuts/customizer.php:1076,
#: framework/premium/extensions/shortcuts/customizer.php:1110,
#: framework/premium/extensions/shortcuts/customizer.php:1142,
#: framework/features/header/items/account/options.php:1198,
#: framework/features/header/items/account/options.php:1242,
#: framework/features/header/items/account/options.php:1284,
#: framework/features/header/items/account/options.php:1389,
#: framework/features/header/items/account/options.php:1432,
#: framework/features/header/items/account/options.php:1473,
#: framework/features/header/items/account/options.php:1539,
#: framework/features/header/items/account/options.php:1572,
#: framework/features/header/items/account/options.php:1610,
#: framework/features/header/items/account/options.php:1653,
#: framework/features/header/items/account/options.php:1758,
#: framework/features/header/items/account/options.php:1801,
#: framework/features/header/items/account/options.php:1852,
#: framework/features/header/items/account/options.php:1974,
#: framework/premium/features/content-blocks/options/popup.php:632,
#: framework/premium/features/content-blocks/options/popup.php:663,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:219,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:250,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:279,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:345,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:376,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:405,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1430,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:220,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:234,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:270,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:196,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:232,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:433,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:463,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:797,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:480,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:511,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:126,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:349,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:383,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:422,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:151,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:180,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:159,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:189,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:209,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:239,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:269,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:311,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:341,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:381,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:409,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:27,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:50,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:215,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:231,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:267,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:308,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:82,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:275,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:319,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:363,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:403,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:442,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:487,
#: framework/premium/features/premium-header/items/contacts/options.php:797,
#: framework/premium/features/premium-header/items/contacts/options.php:828,
#: framework/premium/features/premium-header/items/contacts/options.php:857,
#: framework/premium/features/premium-header/items/contacts/options.php:956,
#: framework/premium/features/premium-header/items/contacts/options.php:994,
#: framework/premium/features/premium-header/items/contacts/options.php:1032,
#: framework/premium/features/premium-header/items/search-input/options.php:313,
#: framework/premium/features/premium-header/items/search-input/options.php:345,
#: framework/premium/features/premium-header/items/search-input/options.php:375,
#: framework/premium/features/premium-header/items/search-input/options.php:443,
#: framework/premium/features/premium-header/items/search-input/options.php:475,
#: framework/premium/features/premium-header/items/search-input/options.php:505,
#: framework/premium/features/premium-header/items/search-input/options.php:573,
#: framework/premium/features/premium-header/items/search-input/options.php:605,
#: framework/premium/features/premium-header/items/search-input/options.php:635,
#: framework/premium/features/premium-header/items/search-input/options.php:708,
#: framework/premium/features/premium-header/items/search-input/options.php:738,
#: framework/premium/features/premium-header/items/search-input/options.php:768,
#: framework/premium/features/premium-header/items/search-input/options.php:851,
#: framework/premium/features/premium-header/items/search-input/options.php:879,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:84,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:105,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:128,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:154,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:268,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:298,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:327,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:399,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:430,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:459,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:98,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:128,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:157,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:241,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:285,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:327,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:403,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:438,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:471,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:226,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:257,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:286,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:353,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:384,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:413
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:261
#: static/js/editor/blocks/about-me/Edit.js:189
#: static/js/editor/blocks/contact-info/Edit.js:203
#: static/js/editor/blocks/search/Edit.js:409
#: static/js/editor/blocks/share-box/Edit.js:152
#: static/js/editor/blocks/socials/Edit.js:152
msgid "Initial"
msgstr "Početno stanje"

#: framework/extensions/cookies-consent/customizer.php:170,
#: framework/extensions/cookies-consent/customizer.php:226,
#: framework/extensions/cookies-consent/customizer.php:257,
#: framework/extensions/cookies-consent/customizer.php:292,
#: framework/extensions/cookies-consent/customizer.php:321,
#: framework/extensions/newsletter-subscribe/customizer.php:219,
#: framework/extensions/newsletter-subscribe/customizer.php:350,
#: framework/extensions/trending/customizer.php:678,
#: framework/extensions/trending/customizer.php:731,
#: framework/extensions/trending/customizer.php:772,
#: framework/extensions/trending/customizer.php:804,
#: framework/extensions/trending/customizer.php:895,
#: framework/premium/extensions/mega-menu/options.php:850,
#: framework/premium/extensions/shortcuts/customizer.php:1082,
#: framework/premium/extensions/shortcuts/customizer.php:1116,
#: framework/premium/extensions/shortcuts/customizer.php:1148,
#: framework/features/header/items/account/options.php:1207,
#: framework/features/header/items/account/options.php:1250,
#: framework/features/header/items/account/options.php:1292,
#: framework/features/header/items/account/options.php:1398,
#: framework/features/header/items/account/options.php:1440,
#: framework/features/header/items/account/options.php:1481,
#: framework/features/header/items/account/options.php:1545,
#: framework/features/header/items/account/options.php:1764,
#: framework/features/header/items/account/options.php:1810,
#: framework/features/header/items/account/options.php:1861,
#: framework/features/header/items/account/options.php:1979,
#: framework/premium/features/content-blocks/options/popup.php:638,
#: framework/premium/features/content-blocks/options/popup.php:669,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:225,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:255,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:284,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:351,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:381,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:410,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1436,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:802,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:486,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:517,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:354,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:389,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:428,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:245,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:275,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:317,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:347,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:386,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:414,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:63,
#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:56,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:236,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:273,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:314,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:281,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:325,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:369,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:409,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:448,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:493,
#: framework/premium/features/premium-header/items/contacts/options.php:803,
#: framework/premium/features/premium-header/items/contacts/options.php:833,
#: framework/premium/features/premium-header/items/contacts/options.php:862,
#: framework/premium/features/premium-header/items/contacts/options.php:961,
#: framework/premium/features/premium-header/items/contacts/options.php:999,
#: framework/premium/features/premium-header/items/contacts/options.php:1037,
#: framework/premium/features/premium-header/items/search-input/options.php:856,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:133,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:250,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:293,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:335,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:409,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:443,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:476,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:232,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:262,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:291,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:359,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:389,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:418
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:270
#: static/js/editor/blocks/about-me/Edit.js:198
#: static/js/editor/blocks/contact-info/Edit.js:212
#: static/js/editor/blocks/search/Edit.js:368
#: static/js/editor/blocks/share-box/Edit.js:161
#: static/js/editor/blocks/socials/Edit.js:161
msgid "Hover"
msgstr "Hover"

#: framework/extensions/newsletter-subscribe/customizer.php:328,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:253
msgid "Button Color"
msgstr "Boja gumba"

#: framework/extensions/cookies-consent/customizer.php:178,
#: framework/extensions/cookies-consent/customizer.php:234,
#: framework/extensions/cookies-consent/customizer.php:299,
#: framework/premium/extensions/mega-menu/options.php:899,
#: framework/features/header/items/account/options.php:1829,
#: framework/features/header/items/account/options.php:1953,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:134,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:405,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:289,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:421,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:92,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:141
msgid "Background Color"
msgstr "Boja pozadine"

#: framework/extensions/cookies-consent/customizer.php:98
msgid "Maximum Width"
msgstr "Maksimalna širina"

#: framework/extensions/cookies-consent/customizer.php:119
msgid "I accept the %sPrivacy Policy%s*"
msgstr "Prihvaćam %sPravila privatnosti%s*"

#: framework/extensions/cookies-consent/customizer.php:123
msgid "This text will appear under each comment form and subscribe form."
msgstr "Ovaj tekst će se pojaviti ispod svakog obrasca za komentare i pretplatu."

#: framework/extensions/cookies-consent/helpers.php:55
msgid "I accept the %sPrivacy Policy%s"
msgstr "Prihvaćam %sPravila privatnosti%s"

#: framework/features/blocks/about-me/options.php:128
msgid "Customizer"
msgstr "Prilagoditelj"

#: framework/extensions/newsletter-subscribe/config.php:6
msgid "Easily capture new leads for your newsletter with the help of a widget, shortcode or even a block inserted on your pages or posts."
msgstr "Jednostavno prikupite nove pretplatnike za vaš newsletter pomoću widgeta, kratkog koda ili čak bloka umetnutog na vaše stranice ili objave."

#: framework/extensions/newsletter-subscribe/customizer.php:4
msgid "Subscribe Form"
msgstr "Obrazac za pretplatu"

#: framework/extensions/newsletter-subscribe/customizer.php:18,
#: framework/features/blocks/about-me/options.php:15,
#: framework/features/blocks/contact-info/options.php:34,
#: framework/features/blocks/contact-info/options.php:80,
#: framework/features/blocks/contact-info/options.php:145,
#: framework/features/blocks/contact-info/options.php:208,
#: framework/features/blocks/contact-info/options.php:271,
#: framework/features/blocks/contact-info/options.php:334,
#: framework/features/blocks/contact-info/options.php:397,
#: framework/features/blocks/contact-info/options.php:460,
#: framework/features/blocks/share-box/options.php:14,
#: framework/features/blocks/socials/options.php:14,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:582,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:327,
#: framework/premium/features/premium-header/items/contacts/options.php:87,
#: framework/premium/features/premium-header/items/contacts/options.php:132,
#: framework/premium/features/premium-header/items/contacts/options.php:177,
#: framework/premium/features/premium-header/items/contacts/options.php:222,
#: framework/premium/features/premium-header/items/contacts/options.php:268,
#: framework/premium/features/premium-header/items/contacts/options.php:313,
#: framework/premium/features/premium-header/items/contacts/options.php:358
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:45
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:125
msgid "Title"
msgstr "Naslov"

#: framework/extensions/newsletter-subscribe/customizer.php:20,
#: framework/extensions/newsletter-subscribe/helpers.php:21,
#: framework/extensions/newsletter-subscribe/helpers.php:69
msgid "Newsletter Updates"
msgstr "Novosti putem newslettera"

#: framework/extensions/newsletter-subscribe/customizer.php:26,
#: framework/features/blocks/about-me/options.php:64,
#: framework/features/blocks/dynamic-data/options.php:142,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:78,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:159,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:248
msgid "Description"
msgstr "Opis"

#: framework/extensions/newsletter-subscribe/helpers.php:72
msgid "Enter your email address below to subscribe to our newsletter"
msgstr "Unesite svoju e-mail adresu ispod kako biste se pretplatili na naš newsletter"

#: framework/extensions/newsletter-subscribe/customizer.php:41,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:34
msgid "List Source"
msgstr "Izvor liste"

#: framework/extensions/newsletter-subscribe/customizer.php:48,
#: framework/extensions/product-reviews/metabox.php:17,
#: framework/extensions/trending/customizer.php:232,
#: framework/extensions/trending/customizer.php:306,
#: framework/extensions/trending/customizer.php:448,
#: framework/features/header/header-options.php:76,
#: framework/premium/features/premium-header.php:322,
#: framework/premium/features/socials.php:20,
#: framework/premium/features/socials.php:48,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:40,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:81,
#: framework/features/blocks/contact-info/options.php:110,
#: framework/features/blocks/contact-info/options.php:175,
#: framework/features/blocks/contact-info/options.php:238,
#: framework/features/blocks/contact-info/options.php:301,
#: framework/features/blocks/contact-info/options.php:364,
#: framework/features/blocks/contact-info/options.php:427,
#: framework/features/blocks/contact-info/options.php:490,
#: framework/features/header/items/account/options.php:385,
#: framework/features/header/items/account/options.php:766,
#: framework/premium/features/content-blocks/options/hook.php:166,
#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:19,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:12,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:17,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:17
msgid "Default"
msgstr "Zadano"

#: framework/extensions/newsletter-subscribe/customizer.php:49,
#: framework/extensions/trending/customizer.php:236,
#: framework/premium/features/premium-header.php:323,
#: framework/premium/features/socials.php:21,
#: framework/premium/features/socials.php:49,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:41,
#: framework/features/blocks/about-me/options.php:27,
#: framework/features/blocks/about-me/options.php:194,
#: framework/features/blocks/contact-info/options.php:111,
#: framework/features/blocks/contact-info/options.php:176,
#: framework/features/blocks/contact-info/options.php:239,
#: framework/features/blocks/contact-info/options.php:302,
#: framework/features/blocks/contact-info/options.php:365,
#: framework/features/blocks/contact-info/options.php:428,
#: framework/features/blocks/contact-info/options.php:491,
#: framework/features/blocks/dynamic-data/options.php:64,
#: framework/features/blocks/share-box/options.php:148,
#: framework/features/blocks/socials/options.php:100,
#: framework/features/conditions/rules/custom.php:33,
#: framework/premium/extensions/shortcuts/customizer.php:260,
#: framework/premium/extensions/shortcuts/customizer.php:287,
#: framework/features/header/items/account/options.php:25,
#: framework/features/header/items/account/options.php:389,
#: framework/features/header/items/account/options.php:770,
#: framework/premium/features/content-blocks/options/archive.php:75,
#: framework/premium/features/content-blocks/options/hook.php:168,
#: framework/premium/features/content-blocks/options/popup.php:287,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:616,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:196,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:188,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:41,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:843,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:18,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:18
#: static/js/editor/blocks/dynamic-data/utils.js:10
msgid "Custom"
msgstr "Prilagođeno"

#: framework/extensions/newsletter-subscribe/customizer.php:61,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:50
#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:199
msgid "List ID"
msgstr "ID liste"

#: framework/extensions/newsletter-subscribe/customizer.php:79,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:109
msgid "Name Field"
msgstr "Polje za ime"

#: framework/extensions/newsletter-subscribe/customizer.php:117,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:121
msgid "Name Label"
msgstr "Oznaka za ime"

#: framework/extensions/newsletter-subscribe/customizer.php:119,
#: framework/extensions/newsletter-subscribe/extension.php:208,
#: framework/extensions/newsletter-subscribe/helpers.php:37,
#: framework/extensions/newsletter-subscribe/helpers.php:81,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:122,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:147
msgid "Your name"
msgstr "Vaše ime"

#: framework/extensions/newsletter-subscribe/customizer.php:128,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:136
msgid "Mail Label"
msgstr "Oznaka za e-mail"

#: framework/extensions/newsletter-subscribe/customizer.php:130,
#: framework/extensions/newsletter-subscribe/extension.php:209,
#: framework/extensions/newsletter-subscribe/helpers.php:41,
#: framework/extensions/newsletter-subscribe/helpers.php:82
msgid "Your email"
msgstr "Vaša e-mail adresa"

#: framework/extensions/newsletter-subscribe/customizer.php:137,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:142,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:414
msgid "Button Label"
msgstr "Oznaka gumba"

#: framework/extensions/newsletter-subscribe/customizer.php:139,
#: framework/extensions/newsletter-subscribe/extension.php:203,
#: framework/extensions/newsletter-subscribe/helpers.php:31,
#: framework/extensions/newsletter-subscribe/helpers.php:76,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:143,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:21
msgid "Subscribe"
msgstr "Pretplati se"

#: framework/extensions/newsletter-subscribe/customizer.php:149,
#: framework/extensions/trending/customizer.php:516,
#: framework/premium/extensions/shortcuts/customizer.php:48,
#: framework/premium/extensions/shortcuts/customizer.php:113,
#: framework/premium/extensions/shortcuts/customizer.php:178,
#: framework/premium/extensions/shortcuts/customizer.php:237,
#: framework/premium/extensions/shortcuts/customizer.php:326,
#: framework/premium/extensions/shortcuts/customizer.php:388,
#: framework/premium/extensions/shortcuts/customizer.php:447,
#: framework/premium/extensions/shortcuts/customizer.php:517,
#: framework/premium/extensions/shortcuts/customizer.php:581,
#: framework/premium/extensions/shortcuts/customizer.php:645,
#: framework/premium/extensions/shortcuts/customizer.php:709,
#: framework/premium/extensions/shortcuts/customizer.php:996,
#: framework/premium/features/content-blocks/options/header.php:167,
#: framework/premium/features/content-blocks/options/hook.php:300,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:155,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:423,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:43
msgid "Visibility"
msgstr "Vidljivost"

#: framework/extensions/newsletter-subscribe/customizer.php:160,
#: framework/extensions/trending/customizer.php:528,
#: framework/features/header/header-options.php:108,
#: framework/features/header/header-options.php:190,
#: framework/features/blocks/contact-info/options.php:24,
#: framework/features/blocks/search/options.php:168,
#: framework/premium/extensions/mega-menu/options.php:413,
#: framework/premium/extensions/shortcuts/customizer.php:62,
#: framework/premium/extensions/shortcuts/customizer.php:127,
#: framework/premium/extensions/shortcuts/customizer.php:192,
#: framework/premium/extensions/shortcuts/customizer.php:251,
#: framework/premium/extensions/shortcuts/customizer.php:341,
#: framework/premium/extensions/shortcuts/customizer.php:402,
#: framework/premium/extensions/shortcuts/customizer.php:461,
#: framework/premium/extensions/shortcuts/customizer.php:531,
#: framework/premium/extensions/shortcuts/customizer.php:595,
#: framework/premium/extensions/shortcuts/customizer.php:659,
#: framework/premium/extensions/shortcuts/customizer.php:723,
#: framework/premium/extensions/shortcuts/customizer.php:861,
#: framework/premium/extensions/shortcuts/customizer.php:918,
#: framework/premium/extensions/shortcuts/customizer.php:1010,
#: framework/features/header/items/account/options.php:532,
#: framework/features/header/items/account/options.php:995,
#: framework/premium/features/content-blocks/options/header.php:178,
#: framework/premium/features/content-blocks/options/hook.php:311,
#: framework/premium/features/content-blocks/options/popup.php:506,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:70,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:168,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:396,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:651,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:436,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:42,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:61,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:80,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:98,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:90,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:258,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:286,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:49,
#: framework/premium/features/premium-header/items/contacts/options.php:32,
#: framework/premium/features/premium-header/items/contacts/options.php:553,
#: framework/premium/features/premium-header/items/language-switcher/options.php:131,
#: framework/premium/features/premium-header/items/search-input/options.php:157,
#: framework/premium/features/premium-header/items/search-input/options.php:250,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:56,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:104,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:105
msgid "Desktop"
msgstr "Računalo"

#: framework/extensions/newsletter-subscribe/customizer.php:161,
#: framework/extensions/trending/customizer.php:529,
#: framework/features/blocks/contact-info/options.php:25,
#: framework/features/blocks/search/options.php:169,
#: framework/premium/extensions/shortcuts/customizer.php:63,
#: framework/premium/extensions/shortcuts/customizer.php:128,
#: framework/premium/extensions/shortcuts/customizer.php:193,
#: framework/premium/extensions/shortcuts/customizer.php:252,
#: framework/premium/extensions/shortcuts/customizer.php:342,
#: framework/premium/extensions/shortcuts/customizer.php:403,
#: framework/premium/extensions/shortcuts/customizer.php:462,
#: framework/premium/extensions/shortcuts/customizer.php:532,
#: framework/premium/extensions/shortcuts/customizer.php:596,
#: framework/premium/extensions/shortcuts/customizer.php:660,
#: framework/premium/extensions/shortcuts/customizer.php:724,
#: framework/premium/extensions/shortcuts/customizer.php:862,
#: framework/premium/extensions/shortcuts/customizer.php:919,
#: framework/premium/extensions/shortcuts/customizer.php:1011,
#: framework/features/header/items/account/options.php:533,
#: framework/features/header/items/account/options.php:996,
#: framework/features/header/items/account/options.php:2069,
#: framework/premium/features/content-blocks/options/header.php:179,
#: framework/premium/features/content-blocks/options/hook.php:312,
#: framework/premium/features/content-blocks/options/popup.php:507,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:71,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:452,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:169,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:397,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:652,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:437,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:43,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:62,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:99,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:91,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:259,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:287,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:50,
#: framework/premium/features/premium-header/items/contacts/options.php:33,
#: framework/premium/features/premium-header/items/contacts/options.php:497,
#: framework/premium/features/premium-header/items/contacts/options.php:554,
#: framework/premium/features/premium-header/items/language-switcher/options.php:132,
#: framework/premium/features/premium-header/items/language-switcher/options.php:261,
#: framework/premium/features/premium-header/items/search-input/options.php:158,
#: framework/premium/features/premium-header/items/search-input/options.php:251,
#: framework/premium/features/premium-header/items/search-input/options.php:947,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:57,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:105,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:655,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:106,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:589
msgid "Tablet"
msgstr "Tablet"

#: framework/extensions/newsletter-subscribe/customizer.php:162,
#: framework/extensions/trending/customizer.php:530,
#: framework/features/header/header-options.php:110,
#: framework/features/header/header-options.php:192,
#: framework/features/blocks/contact-info/options.php:26,
#: framework/features/blocks/contact-info/options.php:202,
#: framework/features/blocks/search/options.php:170,
#: framework/premium/extensions/mega-menu/options.php:414,
#: framework/premium/extensions/shortcuts/customizer.php:64,
#: framework/premium/extensions/shortcuts/customizer.php:129,
#: framework/premium/extensions/shortcuts/customizer.php:194,
#: framework/premium/extensions/shortcuts/customizer.php:253,
#: framework/premium/extensions/shortcuts/customizer.php:343,
#: framework/premium/extensions/shortcuts/customizer.php:404,
#: framework/premium/extensions/shortcuts/customizer.php:463,
#: framework/premium/extensions/shortcuts/customizer.php:533,
#: framework/premium/extensions/shortcuts/customizer.php:597,
#: framework/premium/extensions/shortcuts/customizer.php:661,
#: framework/premium/extensions/shortcuts/customizer.php:725,
#: framework/premium/extensions/shortcuts/customizer.php:863,
#: framework/premium/extensions/shortcuts/customizer.php:920,
#: framework/premium/extensions/shortcuts/customizer.php:1012,
#: framework/features/header/items/account/options.php:534,
#: framework/features/header/items/account/options.php:997,
#: framework/features/header/items/account/options.php:2070,
#: framework/premium/features/content-blocks/options/header.php:180,
#: framework/premium/features/content-blocks/options/hook.php:313,
#: framework/premium/features/content-blocks/options/popup.php:508,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:72,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:453,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:170,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:398,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:653,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:438,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:44,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:63,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:82,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:100,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:92,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:260,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:288,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:51,
#: framework/premium/features/premium-header/items/contacts/options.php:34,
#: framework/premium/features/premium-header/items/contacts/options.php:172,
#: framework/premium/features/premium-header/items/contacts/options.php:498,
#: framework/premium/features/premium-header/items/contacts/options.php:555,
#: framework/premium/features/premium-header/items/language-switcher/options.php:133,
#: framework/premium/features/premium-header/items/language-switcher/options.php:262,
#: framework/premium/features/premium-header/items/search-input/options.php:159,
#: framework/premium/features/premium-header/items/search-input/options.php:252,
#: framework/premium/features/premium-header/items/search-input/options.php:948,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:58,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:106,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:656,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:107,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:590
msgid "Mobile"
msgstr "Mobilni uređaj"

#: framework/extensions/newsletter-subscribe/customizer.php:175,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:183,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:420,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:138,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:146
msgid "Title Color"
msgstr "Boja naslova"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:197
msgid "Description Color"
msgstr "Boja opisa"

#: framework/extensions/newsletter-subscribe/customizer.php:227,
#: framework/features/header/items/account/options.php:1553,
#: framework/premium/features/premium-header/items/search-input/options.php:266,
#: framework/premium/features/premium-header/items/search-input/options.php:295,
#: framework/premium/features/premium-header/items/search-input/options.php:327,
#: framework/premium/features/premium-header/items/search-input/options.php:357
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:128
#: static/js/editor/blocks/search/Edit.js:196
msgid "Input Font Color"
msgstr "Boja fonta unosa"

#: framework/extensions/newsletter-subscribe/customizer.php:251,
#: framework/extensions/newsletter-subscribe/customizer.php:282,
#: framework/extensions/newsletter-subscribe/customizer.php:318,
#: framework/features/header/items/account/options.php:1579,
#: framework/features/header/items/account/options.php:1617,
#: framework/features/header/items/account/options.php:1663,
#: framework/premium/features/premium-header/items/search-input/options.php:319,
#: framework/premium/features/premium-header/items/search-input/options.php:350,
#: framework/premium/features/premium-header/items/search-input/options.php:380,
#: framework/premium/features/premium-header/items/search-input/options.php:449,
#: framework/premium/features/premium-header/items/search-input/options.php:480,
#: framework/premium/features/premium-header/items/search-input/options.php:510,
#: framework/premium/features/premium-header/items/search-input/options.php:579,
#: framework/premium/features/premium-header/items/search-input/options.php:610,
#: framework/premium/features/premium-header/items/search-input/options.php:640,
#: framework/premium/features/premium-header/items/search-input/options.php:713,
#: framework/premium/features/premium-header/items/search-input/options.php:743,
#: framework/premium/features/premium-header/items/search-input/options.php:773
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:210
#: static/js/editor/blocks/search/Edit.js:269
msgid "Focus"
msgstr "Fokus"

#: framework/extensions/newsletter-subscribe/customizer.php:259,
#: framework/features/header/items/account/options.php:1588,
#: framework/premium/features/premium-header/items/search-input/options.php:526,
#: framework/premium/features/premium-header/items/search-input/options.php:555,
#: framework/premium/features/premium-header/items/search-input/options.php:587,
#: framework/premium/features/premium-header/items/search-input/options.php:617
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:159
#: static/js/editor/blocks/search/Edit.js:223
msgid "Input Border Color"
msgstr "Boja obruba unosa"

#: framework/extensions/newsletter-subscribe/customizer.php:296,
#: framework/features/header/items/account/options.php:1631,
#: framework/premium/features/premium-header/items/search-input/options.php:662,
#: framework/premium/features/premium-header/items/search-input/options.php:690,
#: framework/premium/features/premium-header/items/search-input/options.php:720,
#: framework/premium/features/premium-header/items/search-input/options.php:750
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:190
#: static/js/editor/blocks/search/Edit.js:250
msgid "Input Background Color"
msgstr "Boja pozadine unosa"

#: framework/extensions/newsletter-subscribe/customizer.php:357,
#: framework/extensions/trending/customizer.php:903,
#: framework/premium/extensions/shortcuts/customizer.php:1335,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:525,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:209
msgid "Container Background"
msgstr "Pozadina kontejnera"

#: framework/extensions/newsletter-subscribe/customizer.php:373,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:193
msgid "Container Border"
msgstr "Obrub kontejnera"

#: framework/extensions/newsletter-subscribe/customizer.php:408,
#: framework/extensions/trending/customizer.php:919
msgid "Container Inner Spacing"
msgstr "Unutarnji razmak kontejnera"

#: framework/extensions/newsletter-subscribe/customizer.php:422,
#: framework/premium/extensions/shortcuts/customizer.php:1368,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:237
msgid "Container Border Radius"
msgstr "Polumjer obruba kontejnera"

#: framework/extensions/newsletter-subscribe/extension.php:155
msgid "Disable Subscribe Form"
msgstr "Onemogući obrazac za pretplatu"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:253,
#: framework/features/blocks/about-me/options.php:58,
#: framework/features/header/items/account/options.php:578,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:167
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:128
msgid "Name"
msgstr "Ime"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:262,
#: framework/features/blocks/contact-info/options.php:391,
#: framework/features/blocks/dynamic-data/options.php:143,
#: framework/features/blocks/share-box/options.php:97,
#: framework/features/header/modal/register.php:47,
#: framework/premium/extensions/shortcuts/customizer.php:136,
#: framework/premium/extensions/shortcuts/customizer.php:162,
#: framework/premium/extensions/shortcuts/views/bar.php:48,
#: framework/premium/features/premium-header/items/contacts/options.php:308,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:168
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:23
msgid "Email"
msgstr "E-mail"

#: framework/extensions/product-reviews/extension.php:540,
#: framework/extensions/product-reviews/extension.php:541,
#: framework/extensions/product-reviews/extension.php:544,
#: framework/extensions/product-reviews/extension.php:546
msgid "Product Reviews"
msgstr "Recenzije proizvoda"

#: framework/extensions/product-reviews/config.php:6
msgid "This extension lets you easily create an affiliate marketing type of website by giving you options to create a personalized product review and use your affiliate links to direct your readers to the purchase page."
msgstr "Ova ekstenzija vam omogućuje jednostavno kreiranje web stranice za affiliate marketing pružajući opcije za personalizirane recenzije proizvoda i korištenje vaših affiliate poveznica kako biste usmjerili čitatelje na stranicu za kupnju."

#: framework/extensions/product-reviews/extension.php:318,
#: framework/extensions/product-reviews/views/single-top.php:139
msgid "Overall Score"
msgstr "Ukupna ocjena"

#: framework/extensions/product-reviews/extension.php:376
msgid "Review Summary"
msgstr "Sažetak recenzije"

#: framework/extensions/product-reviews/extension.php:387
msgid "Scores Box Width"
msgstr "Širina okvira s ocjenama"

#: framework/extensions/product-reviews/extension.php:397
msgid "Read More Button"
msgstr "Gumb \"Pročitaj više\""

#: framework/extensions/product-reviews/extension.php:408
msgid "Buy Now Button"
msgstr "Gumb \"Kupi sada\""

#: framework/extensions/product-reviews/extension.php:256,
#: framework/extensions/product-reviews/extension.php:427
msgid "Star Rating Color"
msgstr "Boja zvjezdica ocjene"

#: framework/extensions/product-reviews/extension.php:274,
#: framework/extensions/product-reviews/extension.php:444,
#: framework/extensions/product-reviews/extension.php:472,
#: framework/extensions/product-reviews/extension.php:493,
#: framework/premium/extensions/mega-menu/options.php:855,
#: framework/features/header/items/account/options.php:1985,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:221,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:227,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:88,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:94,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:286,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:330,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:375,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:414,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:454,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:498
#: static/js/dashboard/screens/Extensions/Sidebar.js:120
msgid "Active"
msgstr "Aktivno"

#: framework/extensions/product-reviews/extension.php:280,
#: framework/extensions/product-reviews/extension.php:450
msgid "Inactive"
msgstr "Neaktivno"

#: framework/extensions/product-reviews/extension.php:458
msgid "Overll Score Text"
msgstr "Tekst ukupne ocjene"

#: framework/extensions/product-reviews/extension.php:480
msgid "Overll Score Background"
msgstr "Pozadina ukupne ocjene"

#: framework/extensions/product-reviews/extension.php:545
msgid "Product Review"
msgstr "Recenzija proizvoda"

#: framework/extensions/product-reviews/extension.php:547
msgid "Parent Product Review"
msgstr "Roditeljska recenzija proizvoda"

#: framework/extensions/product-reviews/extension.php:548
msgid "All Reviews"
msgstr "Sve recenzije"

#: framework/extensions/product-reviews/extension.php:549
msgid "View Product Review"
msgstr "Pregled recenzije proizvoda"

#: framework/extensions/product-reviews/extension.php:550
msgid "Add New Product Review"
msgstr "Dodaj novu recenziju proizvoda"

#: framework/extensions/product-reviews/extension.php:551
msgid "Add New Review"
msgstr "Dodaj novu recenziju"

#: framework/extensions/product-reviews/extension.php:552
msgid "Edit Product Review"
msgstr "Uredi recenziju proizvoda"

#: framework/extensions/product-reviews/extension.php:553
msgid "Update Product Review"
msgstr "Ažuriraj recenziju proizvoda"

#: framework/extensions/product-reviews/extension.php:554
msgid "Search Product Review"
msgstr "Pretraži recenziju proizvoda"

#: framework/extensions/product-reviews/extension.php:555
msgid "Not Found"
msgstr "Nije pronađeno"

#: framework/extensions/product-reviews/extension.php:556
msgid "Not found in Trash"
msgstr "Nije pronađeno u smeću"

#: framework/extensions/product-reviews/extension.php:590,
#: framework/extensions/product-reviews/extension.php:600,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:100,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:190
msgid "Categories"
msgstr "Kategorije"

#: framework/extensions/product-reviews/extension.php:591,
#: framework/extensions/trending/customizer.php:36,
#: framework/extensions/trending/customizer.php:117
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:88
msgid "Category"
msgstr "Kategorija"

#: framework/extensions/product-reviews/extension.php:592
msgid "Search Category"
msgstr "Pretraži kategoriju"

#: framework/extensions/product-reviews/extension.php:593
#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:88
msgid "All Categories"
msgstr "Sve kategorije"

#: framework/extensions/product-reviews/extension.php:594
msgid "Parent Category"
msgstr "Roditeljska kategorija"

#: framework/extensions/product-reviews/extension.php:595
msgid "Parent Category:"
msgstr "Roditeljska kategorija:"

#: framework/extensions/product-reviews/extension.php:596
msgid "Edit Category"
msgstr "Uredi kategoriju"

#: framework/extensions/product-reviews/extension.php:597
msgid "Update Category"
msgstr "Ažuriraj kategoriju"

#: framework/extensions/product-reviews/extension.php:598
msgid "Add New Category"
msgstr "Dodaj novu kategoriju"

#: framework/extensions/product-reviews/extension.php:599
msgid "New Category Name"
msgstr "Naziv nove kategorije"

#. translators: %s is the theme name.
#: framework/extensions/product-reviews/extension.php:618
msgid "%s Settings"
msgstr "%s Postavke"

#: framework/dashboard.php:476, framework/dashboard.php:477,
#: framework/extensions/product-reviews/extension.php:619
msgid "Blocksy"
msgstr "Blocksy"

#: framework/extensions/product-reviews/helpers.php:30,
#: framework/extensions/product-reviews/metabox.php:181,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:230,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:394
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:132
msgid "Rating"
msgstr "Ocjena"

#: framework/extensions/product-reviews/metabox.php:10
msgid "Review Entity"
msgstr "Entitet recenzije"

#: framework/extensions/product-reviews/metabox.php:18,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/table.php:39,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:34,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:52,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:33,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:33
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:11
msgid "Product"
msgstr "Proizvod"

#: framework/extensions/product-reviews/metabox.php:19
msgid "Book"
msgstr "Knjiga"

#: framework/extensions/product-reviews/metabox.php:21
msgid "Creative Work Season"
msgstr "Sezona kreativnog djela"

#: framework/extensions/product-reviews/metabox.php:22
msgid "Creative Work Series"
msgstr "Serija kreativnog djela"

#: framework/extensions/product-reviews/metabox.php:23
msgid "Episode"
msgstr "Epizoda"

#: framework/extensions/product-reviews/metabox.php:25
msgid "Game"
msgstr "Igra"

#: framework/extensions/product-reviews/metabox.php:27
msgid "Local Business"
msgstr "Lokalni posao"

#: framework/extensions/product-reviews/metabox.php:28
msgid "Media Object"
msgstr "Medijski objekt"

#: framework/extensions/product-reviews/metabox.php:29
msgid "Movie"
msgstr "Film"

#: framework/extensions/product-reviews/metabox.php:30
msgid "Music Playlist"
msgstr "Glazbena lista"

#: framework/extensions/product-reviews/metabox.php:31
msgid "Music Recording"
msgstr "Glazbeni zapis"

#: framework/extensions/product-reviews/metabox.php:32
msgid "Organization"
msgstr "Organizacija"

#: framework/extensions/product-reviews/metabox.php:38
msgid "More info about review entity and how to choose the right one can be found %shere%s."
msgstr "Više informacija o entitetima recenzije i kako odabrati pravi možete pronaći %shere%s."

#: framework/extensions/product-reviews/metabox.php:69
msgid "Product Price"
msgstr "Cijena proizvoda"

#: framework/extensions/product-reviews/metabox.php:76
msgid "Product SKU"
msgstr "SKU proizvoda"

#: framework/extensions/product-reviews/metabox.php:83
msgid "Product Brand"
msgstr "Brend proizvoda"

#: framework/extensions/product-reviews/metabox.php:96
msgid "Gallery"
msgstr "Galerija"

#: framework/extensions/product-reviews/metabox.php:107
msgid "Affiliate Button Label"
msgstr "Oznaka affiliate gumba"

#: framework/extensions/product-reviews/metabox.php:109,
#: framework/extensions/product-reviews/views/single-top.php:156
msgid "Buy Now"
msgstr "Kupi sada"

#: framework/extensions/product-reviews/metabox.php:114
msgid "Affiliate Link"
msgstr "Affiliate poveznica"

#: framework/extensions/product-reviews/metabox.php:120
msgid "Open Link In New Tab"
msgstr "Otvori poveznicu u novoj kartici"

#: framework/extensions/product-reviews/metabox.php:127
msgid "Sponsored Attribute"
msgstr "Atribut sponzoriranog sadržaja"

#: framework/extensions/product-reviews/metabox.php:150
msgid "Read More Button Label"
msgstr "Oznaka gumba \"Pročitaj više\""

#: framework/extensions/product-reviews/metabox.php:152,
#: framework/extensions/product-reviews/views/single-top.php:162
msgid "Read More"
msgstr "Pročitaj više"

#: framework/extensions/product-reviews/metabox.php:172
msgid "Short Description"
msgstr "Kratak opis"

#: framework/extensions/product-reviews/metabox.php:187
msgid "Scores"
msgstr "Ocjene"

#: framework/extensions/product-reviews/metabox.php:227
msgid "Product specs"
msgstr "Specifikacije proizvoda"

#: framework/extensions/product-reviews/metabox.php:252,
#: framework/extensions/product-reviews/views/single-top.php:262
msgid "Pros"
msgstr "Prednosti"

#: framework/extensions/product-reviews/metabox.php:272,
#: framework/extensions/product-reviews/views/single-top.php:285
msgid "Cons"
msgstr "Nedostaci"

#: framework/extensions/trending/customizer.php:169
msgid "Trending Posts"
msgstr "Popularne objave"

#: framework/extensions/trending/config.php:6
msgid "Highlight your most popular posts or products based on the number of comments or reviews they have gotten in the specified period of time."
msgstr "Istaknite svoje najpopularnije objave ili proizvode na temelju broja komentara ili recenzija koje su primili u određenom vremenskom periodu."

#: framework/extensions/trending/customizer.php:8,
#: framework/features/blocks/search/options.php:16,
#: framework/premium/extensions/shortcuts/views/bar.php:51,
#: framework/premium/features/premium-header/items/search-input/options.php:14
msgid "Products"
msgstr "Proizvodi"

#: framework/extensions/trending/customizer.php:37
msgid "All categories"
msgstr "Sve kategorije"

#: framework/extensions/trending/customizer.php:42
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:210
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:34
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:66
#: static/js/editor/blocks/tax-query/Edit.js:156
msgid "Taxonomy"
msgstr "Taksonomija"

#: framework/extensions/trending/customizer.php:43
msgid "All taxonomies"
msgstr "Sve taksonomije"

#: framework/extensions/trending/customizer.php:179
msgid "Module Title"
msgstr "Naslov modula"

#: framework/extensions/trending/customizer.php:182,
#: framework/extensions/trending/helpers.php:352
msgid "Trending now"
msgstr "Trenutno popularno"

#: framework/extensions/trending/customizer.php:187
msgid "Module Title Tag"
msgstr "Oznaka naslova modula"

#: framework/extensions/trending/customizer.php:324,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:284
msgid "Source"
msgstr "Izvor"

#: framework/extensions/trending/customizer.php:329
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:229
msgid "Taxonomies"
msgstr "Taksonomije"

#: framework/extensions/trending/customizer.php:330
msgid "Custom Query"
msgstr "Prilagođeni upit"

#: framework/extensions/trending/customizer.php:354
msgid "Posts ID"
msgstr "ID objava"

#: framework/extensions/trending/customizer.php:358
msgid "Separate posts ID by comma. How to find the %spost ID%s."
msgstr "Razdvojite ID-je objava zarezom. Kako pronaći %sID objave%s."

#: framework/extensions/trending/customizer.php:375
msgid "Trending From"
msgstr "Popularno od"

#: framework/extensions/trending/customizer.php:383
msgid "All Time"
msgstr "Sve vrijeme"

#: framework/extensions/trending/customizer.php:384
msgid "Last 24 Hours"
msgstr "Posljednja 24 sata"

#: framework/extensions/trending/customizer.php:385
msgid "Last 7 Days"
msgstr "Posljednjih 7 dana"

#: framework/extensions/trending/customizer.php:386
msgid "Last Month"
msgstr "Prošli mjesec"

#: framework/features/header/account-modal.php:37,
#: framework/features/header/items/account/options.php:1053,
#: framework/features/header/items/account/views/logout.php:8
msgid "Login"
msgstr "Prijava"

#: framework/features/header/account-modal.php:41
msgid "Sign Up"
msgstr "Registracija"

#: framework/features/header/account-modal.php:61
msgid "Back to login"
msgstr "Povratak na prijavu"

#: framework/features/header/header-options.php:11
msgid "Sticky Functionality"
msgstr "Ljepljiva funkcionalnost"

#: framework/features/header/header-options.php:37
msgid "Only Main Row"
msgstr "Samo glavni red"

#: framework/features/header/header-options.php:42
msgid "Top & Main Row"
msgstr "Gornji i glavni red"

#: framework/features/header/header-options.php:47
msgid "All Rows"
msgstr "Svi redovi"

#: framework/features/header/header-options.php:52
msgid "Main & Bottom Row"
msgstr "Glavni i donji red"

#: framework/features/header/header-options.php:57
msgid "Only Top Row"
msgstr "Samo gornji red"

#: framework/features/header/header-options.php:62
msgid "Only Bottom Row"
msgstr "Samo donji red"

#: framework/features/header/header-options.php:77
msgid "Slide Down"
msgstr "Klizanje prema dolje"

#: framework/features/header/header-options.php:78
msgid "Fade"
msgstr "Nestajanje"

#: framework/features/header/header-options.php:79
msgid "Auto Hide/Show"
msgstr "Automatsko skrivanje/prikazivanje"

#: framework/features/header/header-options.php:97,
#: framework/features/header/header-options.php:179
msgid "Enable on"
msgstr "Omogući na"

#: framework/features/header/header-options.php:125
msgid "Transparent Functionality"
msgstr "Prozirna funkcionalnost"

#: framework/extensions/trending/customizer.php:561,
#: framework/features/header/header-options.php:163,
#: framework/premium/extensions/shortcuts/customizer.php:1017,
#: framework/premium/features/content-blocks/options/header.php:31,
#: framework/premium/features/content-blocks/options/hook.php:36,
#: framework/premium/features/content-blocks/options/maintenance.php:5,
#: framework/premium/features/content-blocks/options/nothing_found.php:29,
#: framework/premium/features/content-blocks/options/nothing_found.php:34,
#: framework/premium/features/content-blocks/options/popup.php:49,
#: framework/premium/features/content-blocks/options/single.php:10,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:443,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:5,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:102,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:24
#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:50
#: framework/premium/static/js/footer/EditConditions.js:97
#: static/js/header/EditConditions.js:103
msgid "Display Conditions"
msgstr "Uvjeti prikaza"

#: framework/premium/features/content-blocks/admin-ui.php:653
msgid "Hooks Locations"
msgstr "Lokacije hookova"

#: framework/premium/extensions/shortcuts/customizer.php:765,
#: framework/premium/features/content-blocks/admin-ui.php:342,
#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:12,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:5
msgid "Type"
msgstr "Vrsta"

#: framework/premium/features/content-blocks/admin-ui.php:343
msgid "Location/Trigger"
msgstr "Lokacija/Okidač"

#: framework/premium/features/content-blocks/admin-ui.php:344,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:80,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:143
msgid "Conditions"
msgstr "Uvjeti"

#: framework/premium/features/content-blocks/admin-ui.php:345
msgid "Output"
msgstr "Izlaz"

#: framework/premium/features/content-blocks/admin-ui.php:346
msgid "Enable/Disable"
msgstr "Omogući/Onemogući"

#: framework/features/blocks/about-me/options.php:204,
#: framework/features/blocks/contact-info/options.php:564,
#: framework/features/blocks/share-box/options.php:160,
#: framework/features/blocks/socials/options.php:112,
#: framework/premium/extensions/shortcuts/customizer.php:980,
#: framework/premium/features/content-blocks/admin-ui.php:371,
#: framework/features/header/items/account/options.php:22,
#: framework/features/header/items/account/options.php:348,
#: framework/features/header/items/account/options.php:746,
#: framework/premium/features/content-blocks/options/hook.php:9,
#: framework/premium/features/content-blocks/options/hook.php:77,
#: framework/premium/features/content-blocks/options/hook.php:167,
#: framework/premium/features/content-blocks/options/popup.php:75,
#: framework/premium/features/content-blocks/options/popup.php:222,
#: framework/premium/features/content-blocks/options/popup.php:603,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/select.php:23,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:97,
#: framework/premium/features/premium-header/items/contacts/options.php:451
#: framework/premium/static/js/blocks/ContentBlock.js:56
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:170
msgid "None"
msgstr "Ništa"

#: framework/premium/features/content-blocks/admin-ui.php:372,
#: framework/premium/features/content-blocks/options/popup.php:76
msgid "On scroll"
msgstr "Prilikom pomicanja"

#: framework/premium/features/content-blocks/admin-ui.php:373,
#: framework/premium/features/content-blocks/options/popup.php:77
msgid "On scroll to element"
msgstr "Prilikom pomicanja do elementa"

#: framework/premium/features/content-blocks/admin-ui.php:375,
#: framework/premium/features/content-blocks/options/popup.php:79
msgid "On page load"
msgstr "Prilikom učitavanja stranice"

#: framework/premium/features/content-blocks/admin-ui.php:376,
#: framework/premium/features/content-blocks/options/popup.php:80
msgid "After inactivity"
msgstr "Nakon neaktivnosti"

#: framework/premium/features/content-blocks/admin-ui.php:377,
#: framework/premium/features/content-blocks/options/popup.php:81
msgid "After x time"
msgstr "Nakon x vremena"

#: framework/premium/features/content-blocks/admin-ui.php:379,
#: framework/premium/features/content-blocks/options/popup.php:83
msgid "On page exit intent"
msgstr "Prilikom pokušaja izlaska sa stranice"

#: framework/premium/features/content-blocks/admin-ui.php:383
msgid "Down"
msgstr "Dolje"

#: framework/premium/features/content-blocks/admin-ui.php:384
msgid "Up"
msgstr "Gore"

#: framework/premium/features/content-blocks.php:193,
#: framework/premium/features/content-blocks.php:199
msgid "Content Blocks"
msgstr "Blokovi sadržaja"

#: framework/premium/features/content-blocks.php:194,
#: framework/premium/extensions/mega-menu/options.php:346,
#: framework/premium/features/content-blocks/content-block-layer.php:163,
#: framework/premium/features/content-blocks/content-block-layer.php:214,
#: framework/features/header/items/account/options.php:247,
#: framework/premium/features/premium-header/items/content-block/config.php:4
#: framework/premium/static/js/blocks/ContentBlock.js:78
msgid "Content Block"
msgstr "Blok sadržaja"

#: framework/premium/features/content-blocks.php:195,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:138,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:482,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:273
msgid "Add New"
msgstr "Dodaj novi"

#: framework/premium/features/content-blocks.php:196
msgid "Add New Content Block"
msgstr "Dodaj novi blok sadržaja"

#: framework/premium/features/content-blocks.php:197
#: framework/premium/static/js/blocks/ContentBlock.js:89
msgid "Edit Content Block"
msgstr "Uredi blok sadržaja"

#: framework/premium/features/content-blocks.php:198
#: framework/premium/static/js/hooks/CreateHook.js:33
msgid "New Content Block"
msgstr "Novi blok sadržaja"

#: framework/premium/features/content-blocks.php:200
msgid "View Content Block"
msgstr "Pregled bloka sadržaja"

#: framework/premium/features/content-blocks.php:201
msgid "Search Content Blocks"
msgstr "Pretraži blokove sadržaja"

#: framework/premium/features/content-blocks.php:202,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:145,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:489,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:280
msgid "Nothing found"
msgstr "Ništa nije pronađeno"

#: framework/premium/features/content-blocks.php:203,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:146,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:490,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:281
msgid "Nothing found in Trash"
msgstr "Ništa nije pronađeno u smeću"

#: framework/premium/features/premium-footer.php:14,
#: framework/premium/features/premium-footer.php:28
msgid "Footer Menu 1"
msgstr "Izbornik podnožja 1"

#: framework/premium/features/premium-footer.php:29,
#: framework/premium/features/premium-footer/items/menu-secondary/config.php:4
msgid "Footer Menu 2"
msgstr "Izbornik podnožja 2"

#: framework/premium/features/premium-header.php:57
msgid "Header Menu 3"
msgstr "Izbornik zaglavlja 3"

#: framework/premium/features/premium-header.php:202
msgid "Header Widget Area "
msgstr "Područje widgeta zaglavlja "

#: framework/extensions/trending/customizer.php:251,
#: framework/premium/features/premium-header.php:258,
#: framework/premium/features/premium-header.php:339,
#: framework/premium/features/premium-header.php:359,
#: framework/premium/features/premium-header.php:376,
#: framework/premium/features/socials.php:31,
#: framework/features/blocks/contact-info/options.php:121,
#: framework/features/blocks/contact-info/options.php:186,
#: framework/features/blocks/contact-info/options.php:249,
#: framework/features/blocks/contact-info/options.php:312,
#: framework/features/blocks/contact-info/options.php:375,
#: framework/features/blocks/contact-info/options.php:438,
#: framework/features/blocks/contact-info/options.php:501,
#: framework/features/blocks/search/options.php:86,
#: framework/premium/extensions/mega-menu/options.php:463,
#: framework/features/header/items/account/options.php:347,
#: framework/features/header/items/account/options.php:404,
#: framework/features/header/items/account/options.php:745,
#: framework/features/header/items/account/options.php:785,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:693,
#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:20,
#: framework/premium/features/premium-header/items/contacts/options.php:114,
#: framework/premium/features/premium-header/items/contacts/options.php:159,
#: framework/premium/features/premium-header/items/contacts/options.php:204,
#: framework/premium/features/premium-header/items/contacts/options.php:249,
#: framework/premium/features/premium-header/items/contacts/options.php:295,
#: framework/premium/features/premium-header/items/contacts/options.php:340,
#: framework/premium/features/premium-header/items/contacts/options.php:385,
#: framework/premium/features/premium-header/items/search-input/options.php:79,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:30,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:28,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:28
msgid "Icon"
msgstr "Ikona"

#: framework/premium/features/premium-header.php:277,
#: framework/features/blocks/about-me/options.php:168,
#: framework/features/blocks/contact-info/options.php:538,
#: framework/features/blocks/share-box/options.php:122,
#: framework/features/blocks/socials/options.php:74,
#: framework/premium/features/premium-header/items/contacts/options.php:426
msgid "Icons Size"
msgstr "Veličina ikona"

#: framework/premium/features/premium-header.php:289,
#: framework/premium/extensions/mega-menu/options.php:509
msgid "Icon Position"
msgstr "Pozicija ikona"

#: framework/premium/features/premium-header.php:296,
#: framework/premium/extensions/mega-menu/options.php:516,
#: framework/premium/extensions/shortcuts/customizer.php:886,
#: framework/features/header/items/account/options.php:561,
#: framework/features/header/items/account/options.php:1027,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:95,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:129,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:130
msgid "Left"
msgstr "Lijevo"

#: framework/premium/features/premium-header.php:297,
#: framework/premium/extensions/mega-menu/options.php:517,
#: framework/premium/extensions/shortcuts/customizer.php:887,
#: framework/features/header/items/account/options.php:562,
#: framework/features/header/items/account/options.php:1031,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:96,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:130,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:131
msgid "Right"
msgstr "Desno"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:14
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/index.js:56
msgid "Newsletter"
msgstr "Newsletter"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:13,
#: framework/features/blocks/contact-info/options.php:39,
#: framework/premium/extensions/mega-menu/options.php:544,
#: framework/premium/extensions/shortcuts/customizer.php:1187,
#: framework/premium/extensions/shortcuts/customizer.php:1229,
#: framework/premium/extensions/shortcuts/customizer.php:1271,
#: framework/features/header/items/account/options.php:577,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:32,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:72,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:98,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:547,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:582,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:616,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:486,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:517,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:547
#: static/js/editor/blocks/breadcrumbs/Edit.js:54
#: static/js/editor/blocks/contact-info/Edit.js:107
msgid "Text"
msgstr "Tekst"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:19,
#: framework/features/blocks/about-me/options.php:67
msgid "You can add here some arbitrary HTML code."
msgstr "Ovdje možete dodati proizvoljan HTML kod."

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:74
msgid "Container Style"
msgstr "Stil kontejnera"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:82,
#: framework/premium/features/content-blocks/options/404.php:85,
#: framework/premium/features/content-blocks/options/archive.php:162,
#: framework/premium/features/content-blocks/options/header.php:103,
#: framework/premium/features/content-blocks/options/hook.php:212,
#: framework/premium/features/content-blocks/options/maintenance.php:82,
#: framework/premium/features/content-blocks/options/nothing_found.php:103,
#: framework/premium/features/content-blocks/options/single.php:94,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:22
msgid "Boxed"
msgstr "Ograničeno"

#: framework/extensions/product-reviews/views/single-top.php:238
msgid "Specs"
msgstr "Specifikacije"

#: framework/extensions/newsletter-subscribe/helpers.php:147
msgid "Email address"
msgstr "E-mail adresa"

#: framework/features/header/modal/login.php:28,
#: framework/features/header/modal/register.php:53
msgid "Password"
msgstr "Lozinka"

#: framework/features/header/modal/login.php:53
msgid "Remember Me"
msgstr "Zapamti me"

#: framework/features/header/modal/login.php:57
msgid "Forgot Password?"
msgstr "Zaboravili ste lozinku?"

#: framework/features/header/modal/login.php:79
msgid "Log In"
msgstr "Prijavi se"

#: framework/features/header/modal/login.php:23,
#: framework/features/header/modal/lostpassword.php:5
msgid "Username or Email Address"
msgstr "Korisničko ime ili e-mail adresa"

#: framework/features/header/modal/lostpassword.php:13
msgid "Get New Password"
msgstr "Dobij novu lozinku"

#: framework/features/header/modal/register.php:41
msgid "Username"
msgstr "Korisničko ime"

#: framework/features/header/modal/register.php:106
msgid "Registration confirmation will be emailed to you."
msgstr "Potvrda registracije bit će vam poslana e-mailom."

#: framework/features/header/modal/register.php:112
msgid "Register"
msgstr "Registracija"

#: framework/helpers/exts-configs.php:60
msgid "Connect your Adobe Fonts project and use the selected fonts throughout Blocksy and your favorite page builder."
msgstr "Povežite svoj Adobe Fonts projekt i koristite odabrane fontove unutar Blocksy-a i omiljenog graditelja stranica."

#: framework/helpers/exts-configs.php:71
msgid "Inject custom code snippets throughout your website. The extension works globally or on a per post/page basis."
msgstr "Umetnite prilagođene isječke koda na cijelu web stranicu ili na pojedinačne objave/stranice."

#: framework/premium/extensions/code-snippets/extension.php:42,
#: framework/premium/extensions/code-snippets/extension.php:97,
#: framework/premium/extensions/code-snippets/extension.php:142
msgid "After body open scripts"
msgstr "Skripte nakon otvaranja tijela"

#: framework/helpers/exts-configs.php:97
msgid "Upload an unlimited number of custom fonts or variable fonts and use them throughout Blocksy and your favorite page builder."
msgstr "Prenesite neograničen broj prilagođenih ili varijabilnih fontova i koristite ih u Blocksy-u i svom omiljenom graditelju stranica."

#: framework/helpers/exts-configs.php:109
msgid "Serve your chosen Google Fonts from your own web server. This will increase the loading speed and makes sure your website complies with the privacy regulations."
msgstr "Poslužite odabrane Google fontove sa svog web poslužitelja. To će povećati brzinu učitavanja i osigurati usklađenost vaše web stranice s pravilima o privatnosti."

#: framework/helpers/exts-configs.php:124
msgid "Create beautiful personalised menus that set your website apart from the others. Add icons and badges to your entries and even add Content Blocks inside your dropdowns."
msgstr "Kreirajte prekrasne personalizirane izbornike koji će vašu web stranicu istaknuti. Dodajte ikone i oznake svojim stavkama te čak umetnite blokove sadržaja unutar padajućih izbornika."

#: framework/premium/extensions/mega-menu/extension.php:160
msgid "Menu Item Settings"
msgstr "Postavke stavki izbornika"