# Translation of Blocksy Companion Pro in Vietnamese
# This file is distributed under the same license as the Blocksy Companion Pro package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-06-15 13:04:36+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: vi_VN\n"
"Project-Id-Version: Blocksy Companion Pro\n"

#: static/js/dashboard/helpers/useUpsellModal.js:88
msgid "Business or Agency"
msgstr "Doanh nghiệp ho���c đại lý"

#: static/js/options/ConditionsManager/SingleCondition.js:482
msgid "Select value"
msgstr "Chọn giá trị"

#: framework/helpers/exts-configs.php:385
msgid "Product Waitlist"
msgstr "Danh sách chờ sản phẩm"

#: framework/helpers/exts-configs.php:386
msgid "Allow your customers to sign up for a waitlist for products that are out of stock and get notified when they are back in stock."
msgstr "Cho phép khách hàng của bạn đăng ký vào danh sách chờ cho các sản phẩm đã hết hàng và nhận thông báo khi chúng có hàng trở lại."

#: framework/features/blocks/share-box/options.php:103
msgid "Clipboard"
msgstr "Bộ nhớ tạm"

#: framework/premium/extensions/shortcuts/customizer.php:609,
#: framework/premium/extensions/shortcuts/customizer.php:635,
#: framework/premium/extensions/shortcuts/views/bar.php:53,
#: framework/features/header/items/account/views/login.php:554,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:4,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/feature.php:189,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/feature.php:193,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-account.php:67
msgid "Waitlist"
msgstr "Danh sách chờ"

#: framework/premium/extensions/shortcuts/customizer.php:895
msgid "Tooltip Visibility"
msgstr "Hiển thị chú thích"

#: framework/premium/extensions/shortcuts/customizer.php:1351
msgid "Container Backdrop Blur"
msgstr "Làm mờ nền của khung chứa"

#: framework/premium/features/content-blocks/hooks-manager.php:957
msgid "Added to Cart: Before product"
msgstr "Đã thêm vào giỏ hàng: Trước sản phẩm"

#: framework/premium/features/content-blocks/hooks-manager.php:961
msgid "Added to Cart: Before actions"
msgstr "Đã thêm vào giỏ hàng: Trước hành động"

#: framework/premium/features/content-blocks/hooks-manager.php:965
msgid "Added to Cart: Before suggested products"
msgstr "Đã thêm vào giỏ hàng: Trước sản phẩm đề xuất"

#: framework/premium/features/content-blocks/hooks-manager.php:969
msgid "Added to Cart: After suggested products"
msgstr "Đã thêm vào giỏ hàng: Sau sản phẩm đề xuất"

#: framework/premium/features/content-blocks/hooks-manager.php:971
msgid "WooCommerce: Added to Cart"
msgstr "WooCommerce: Đã thêm vào giỏ hàng"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:294
msgid "Upsell Products"
msgstr "Sản phẩm bán thêm"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:295
msgid "Cross-sell Products"
msgstr "Sản phẩm bán chéo"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:506
msgid "Auto Close Panel"
msgstr "Tự động đóng bảng điều khiển"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:507
msgid "Automatically close the panel when a filter option is selected."
msgstr "Tự động đóng bảng điều khiển khi một tùy chọn bộ lọc được chọn."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:15
msgid "Form Type"
msgstr "Loại biểu mẫu"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:29
msgid "Form Max Width"
msgstr "Chiều rộng tối đa của biểu mẫu"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:41
msgid "Enable For Backorders"
msgstr "Bật cho các đơn đặt hàng trước"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:42
msgid "Allow users to join the waitlist even if the product is on backorder."
msgstr "Cho phép người dùng tham gia danh sách chờ ngay cả khi sản phẩm đang trong tình trạng đặt hàng trước."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:55
msgid "Show Users Count"
msgstr "Hiển thị số lượng người dùng"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:56
msgid "Display a counter that reflects the current number of users on the waitlist."
msgstr "Hiển thị bộ đếm phản ánh số lượng người dùng hiện tại trong danh sách chờ."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:69
msgid "Logged In Users Only"
msgstr "Chỉ hiển thị cho người dùng đã đăng nhập"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:70
msgid "Display the waitlist feature exclusively to users who are logged in."
msgstr "Hiển thị tính năng danh sách chờ chỉ cho người dùng đã đăng nhập."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:83
msgid "Subscription Confirmation"
msgstr "Xác nhận đăng ký"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:84
msgid "Specify which users should verify their waitlist subscription through email confirmation."
msgstr "Chỉ định người dùng cần xác nhận đăng ký danh sách chờ thông qua email."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:107
msgid "Waitlist Form Display Conditions"
msgstr "Điều kiện hiển thị biểu mẫu danh sách chờ"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:108
msgid "Choose where you want this Waitlist Form to be displayed."
msgstr "Chọn nơi bạn muốn hiển thị biểu mẫu danh sách chờ."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:160
msgid "Message Font"
msgstr "Phông chữ thông điệp"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:168
msgid "Message Color"
msgstr "Màu thông điệp"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:225
msgid "Container Padding"
msgstr "Lề trong vùng chứa"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/table.php:40,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:46
msgid "Actions"
msgstr "Hành động"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:96,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:200,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:221,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:282
msgid "Invalid request"
msgstr "Yêu cầu không hợp lệ"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:106
msgid "No waitlist found"
msgstr "Không tìm thấy danh sách chờ"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:127,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:128,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:162
msgid "Waitlists"
msgstr "Danh sách chờ"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:153
msgid "Number of items per page"
msgstr "Số lượng mục mỗi trang"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:171
msgid "Waitlist for %s"
msgstr "Danh sách chờ cho %s"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:213
msgid "Invalid email"
msgstr "Email không hợp lệ"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:229
msgid "You are already on the waitlist"
msgstr "Bạn đã có trong danh sách chờ"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:267
msgid "You have been added to the waitlist"
msgstr "Bạn đã được thêm vào danh sách chờ"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:291
msgid "You have been removed from the waitlist"
msgstr "Bạn đã được xóa khỏi danh sách chờ"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-layer.php:33
msgid "%s %s joined the waitlist for this item."
msgstr "%s %s Biểu mẫu danh sách chờ"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-layer.php:243
msgid "Waitlist Form"
msgstr "Biểu mẫu danh sách chờ"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-mailer.php:78
msgid "Your waitlist subscription has been successfully canceled."
msgstr "Đăng ký danh sách chờ của bạn đã được hủy thành công."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-mailer.php:117
msgid "Your waitlist subscription has been successfully confirmed."
msgstr "Đăng ký danh sách chờ của bạn đã được xác nhận thành công."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-table.php:11
msgid "Search Products"
msgstr "Tìm kiếm sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:14
msgid "Export Subscribers"
msgstr "Xuất danh sách người đăng ký"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:63
msgid "Guest"
msgstr "Khách"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:75
msgid "Edit this customer"
msgstr "Chỉnh sửa khách hàng này"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:76
msgid "Edit"
msgstr "Chỉnh sửa"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:115,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:182
msgid "Delete"
msgstr "Xóa"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:123
msgid "View"
msgstr "Xem"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:153
msgid "%s ago"
msgstr "Cách đây %s"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:169
msgid "Is registered"
msgstr "Đã đăng ký"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:170
msgid "Date created"
msgstr "Ngày tạo"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:8
msgid "Waitlist - Back in Stock Notification"
msgstr "Danh sách chờ - Thông báo sản phẩm có hàng lại"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:9
msgid "This email is sent when a product is back in stock"
msgstr "Email này được gửi khi sản phẩm có hàng trở lại"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:10
msgid "A product you are waiting for is back in stock"
msgstr "Sản phẩm bạn đang chờ đã có hàng trở lại"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:11
msgid "Good news! The product you have been waiting for is now back in stock!"
msgstr "Tin tốt! Sản phẩm bạn đang chờ hiện đã có hàng trở lại!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:8
msgid "Waitlist - Confirm Subscription"
msgstr "Danh sách chờ - Xác nhận đăng ký"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:9
msgid "This email is sent when a user subscribes to a product stock alert and should confirm their subscription"
msgstr "Email này được gửi khi người dùng đăng ký thông báo về sản phẩm và cần xác nhận đăng ký"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:10
msgid "Confirm waitlist subscription"
msgstr "Xác nhận đăng ký danh sách chờ"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:11
msgid "Get notified when {product_title} is back in stock"
msgstr "Nhận thông báo khi {product_title} có hàng trở lại"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:8
msgid "Waitlist - Subscription Confirmed"
msgstr "Danh sách chờ - Đăng ký đã được xác nhận"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:9
msgid "This email is sent after a user confirmed the subscription to a product stock alert"
msgstr "Email này được gửi sau khi người dùng xác nhận đăng ký thông báo sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:10
msgid "Waitlist subscription confirmed"
msgstr "Đăng ký danh sách chờ đã được xác nhận"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:11
msgid "You will be notified when {product_title} is back in stock"
msgstr "Bạn sẽ nhận được thông báo khi {product_title} có hàng trở lại"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/waitlist-email.php:35,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/waitlist-email.php:139
msgid "Customer"
msgstr "Khách hàng"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:27
msgid "Enter your email"
msgstr "Nhập email của bạn"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:40
msgid "Join Waitlist"
msgstr "Tham gia danh sách chờ"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:53
msgid "This product is currently sold out!"
msgstr "Sản phẩm này hiện đã hết hàng!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:61
msgid "No worries! Please enter your e-mail address and we will promptly notify you as soon as the item is back in stock."
msgstr "Đừng lo! Vui lòng nhập địa chỉ email của bạn và chúng tôi sẽ nhanh chóng thông báo cho bạn ngay khi sản phẩm có hàng trở lại."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:69
msgid "Great! You have been added to the waitlist for this product. Please check your inbox and confirm the subscription to this waitlist."
msgstr "Tuyệt vời! Bạn đã được thêm vào danh sách chờ cho sản phẩm này. Vui lòng kiểm tra hộp thư đến và xác nhận đăng ký danh sách chờ này."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:97
msgid "Great! You have been added to the waitlist for this product. You will receive an email as soon as the item is back in stock."
msgstr "Tuyệt vời! Bạn đã được thêm vào danh sách chờ cho sản phẩm này. Bạn sẽ nhận được email ngay khi sản phẩm có hàng trở lại."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:151,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:86
msgid "Unsubscribe"
msgstr "Hủy đăng ký"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:65
msgid "Yes"
msgstr "Có"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:65
msgid "No"
msgstr "Không"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:21
msgid "You don't have any products in your waitlist yet."
msgstr "Bạn chưa có sản phẩm nào trong danh sách chờ."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:42
msgid "Confirmed"
msgstr "Đã xác nhận"

#. translators: %s User name.
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:19,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:17,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:17,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:12,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:10,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-subscription-confirmed.php:10
msgid "Hi, %s!"
msgstr "Chào, %s!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:31,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:19
msgid "Great news! The %s from your waitlist is now back in stock!"
msgstr "Tin tốt! %s từ danh sách chờ của bạn hiện đã có hàng trở lại!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:42,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:24
msgid "Click the link below to secure your purchase before it is gone!"
msgstr "Nhấp vào liên kết dưới đây để đảm bảo mua hàng của bạn trước khi sản phẩm hết hàng!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:26,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:15
msgid "You have requested to join the waitlist for this item:"
msgstr "Bạn đã yêu cầu tham gia danh sách chờ cho sản phẩm này:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:62
msgid "Click the button below to confirm your subscription. Once confirmed, we will notify you when the item is back in stock."
msgstr "Nhấp vào nút bên dưới để xác nhận đăng ký của bạn. Sau khi xác nhận, chúng tôi sẽ thông báo cho bạn khi sản phẩm có hàng trở lại."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:66
msgid "Confirm Subscription"
msgstr "Xác nhận đăng ký"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:70,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:26
msgid "Please note, the confirmation period is 2 days."
msgstr "Lưu ý, thời gian xác nhận là 2 ngày."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:77,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:65
msgid "If you don't want to receive any further notifications, please %s"
msgstr "Nếu bạn không muốn nhận thêm thông báo, vui lòng %s"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:78,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:66
msgid "unsubscribe"
msgstr "hủy đăng ký"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:26,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-subscription-confirmed.php:15
msgid "You have been successfully added to the waitlist for the following item:"
msgstr "Bạn đã được thêm thành công vào danh sách chờ cho sản phẩm sau:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:28,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:17
msgid "Product:"
msgstr "Sản phẩm:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:29,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:18
msgid "Price:"
msgstr "Giá:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:30
msgid "Add to cart:"
msgstr "Thêm vào giỏ hàng:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:19
msgid "Product link:"
msgstr "Liên kết sản phẩm:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:23
msgid "Click the link below to confirm your subscription. Once confirmed, we will notify you when the item is back in stock."
msgstr "Nhấp vào liên kết dưới đây để xác nhận đăng ký của bạn. Sau khi xác nhận, chúng tôi sẽ thông báo cho bạn khi sản phẩm có hàng trở lại."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:32,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-subscription-confirmed.php:25
msgid "If you don't want to receive any further notifications, please unsubscribe by clicking on this link - %s"
msgstr "Nếu bạn không muốn nhận thêm thông báo, vui lòng hủy đăng ký bằng cách nhấp vào liên kết này - %s"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:127
msgid "API URL"
msgstr "URL API"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:304
msgid "More information on how to generate an API key for ActiveCampaign can be found %shere%s."
msgstr "Thông tin thêm về cách tạo khóa API cho ActiveCampaign có thể được tìm thấy %stại đây%s."

#: static/js/dashboard/helpers/useUpsellModal.js:18
#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:111
msgid "Business"
msgstr "Doanh nghiệp"

#: framework/helpers/exts-configs.php:377,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:4
msgid "Added to Cart Popup"
msgstr "Thông báo đã thêm vào giỏ hàng"

#: framework/helpers/exts-configs.php:378
msgid "Show a dynamic confirmation popup with product recommendations whenever items are added to the cart."
msgstr "Hiển thị một hộp thoại xác nhận động kèm theo gợi ý sản phẩm mỗi khi có mặt hàng được thêm vào giỏ hàng."

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:15
msgid "Trigger Popup On"
msgstr "Kích hoạt Popup khi"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:26
msgid "Product Page"
msgstr "Trang sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:91
msgid "Description Length"
msgstr "Độ dài mô tả"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:102
msgid "Cart Button"
msgstr "Nút giỏ hàng"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:112
msgid "Checkout Button"
msgstr "Nút thanh toán"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:121
msgid "Continue Shopping Button"
msgstr "Nút tiếp tục mua sắm"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:140
msgid "Shipping Info"
msgstr "Thông tin vận chuyển"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:149
msgid "Tax Info"
msgstr "Thông tin thuế"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:158
msgid "Total Info"
msgstr "Thông tin tổng cộng"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:264
msgid "Suggested Products"
msgstr "Sản phẩm gợi ý"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:292,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/suggested-products.php:51
msgid "Related Products"
msgstr "Sản phẩm liên quan"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:293
msgid "Recently Viewed Products"
msgstr "Sản phẩm đã xem gần đây"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:368
msgid "Products Card Type"
msgstr "Loại thẻ sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:382
msgid "Products Visibility"
msgstr "Hiển thị sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:516
msgid "Popup Options"
msgstr "Tùy chọn Popup"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:736,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:752,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:315,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:96
msgid "Popup Backdrop"
msgstr "Phông nền Popup"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:768
msgid "Close Icon Size"
msgstr "Kích thước biểu tượng đóng"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/view.php:95
msgid "Product succesfully added to your cart!"
msgstr "Sản phẩm đã được thêm vào giỏ hàng của bạn!"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/view.php:102
msgid "Close Modal"
msgstr "Thoát hộp bật lên"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:331,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:124
msgid "Popup Shadow"
msgstr "Đổ bóng Popup"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:350,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:143
msgid "Popup Border Radius"
msgstr "Bo viền Popup"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/added-product.php:227
msgid "Shipping Cost"
msgstr "Chi phí vận chuyển"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/added-product.php:236
msgid "Tax Amount"
msgstr "Số tiền thuế"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/added-product.php:245
msgid "Cart Total"
msgstr "Tổng giỏ hàng"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/product-actions.php:24
msgid "View Cart"
msgstr "Xem giỏ hàng"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/product-actions.php:34
msgid "Checkout"
msgstr "Thanh toán"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/product-actions.php:44
msgid "Continue Shopping"
msgstr "Tiếp tục mua sắm"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/suggested-products.php:53
msgid "Recently Viewed"
msgstr "Đã xem gần đây"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:340
msgid "More information on how to create a list in MailPoet can be found %shere%s."
msgstr "Thông tin thêm về cách tạo danh sách trong MailPoet có thể được tìm thấy %stại đây%s."

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:9
msgid "Color Mode"
msgstr "Chế độ màu"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:17
msgid "One Color"
msgstr "Một màu"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:18
msgid "Dual Color"
msgstr "Hai màu"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:28
msgid "Colors"
msgstr "Màu sắc"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:50
msgid "Color 1"
msgstr "Màu 1"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:58
msgid "Color 2"
msgstr "Màu 2"

#: framework/features/blocks/share-box/options.php:110,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:88
msgid "Tooltip"
msgstr "Chú thích"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:108
msgid "Tooltip Text"
msgstr "Văn bản chú thích"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:111
msgid "{term_name}"
msgstr "{term_name}"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:122
msgid "Tooltip Image"
msgstr "Hình ảnh chú thích"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:135
msgid "Subtype"
msgstr "Loại phụ"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:174
msgid "here"
msgstr "tại đây"

#: framework/premium/features/premium-header/items/contacts/options.php:21
msgid "Item Visibility"
msgstr "Hiển thị mục"

#: framework/premium/features/premium-header/items/language-switcher/options.php:36
msgid "Hide Missing Language"
msgstr "Ẩn ngôn ngữ bị thiếu"

#: framework/premium/extensions/woocommerce-extra/features/filters/filter-types/taxonomies.php:52
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Preview.js:94
msgid "Please wait until the lookup table is generated."
msgstr "Vui lòng đợi cho đến khi bảng tra cứu được tạo."

#: framework/premium/extensions/woocommerce-extra/features/filters/filter-types/taxonomies.php:472
msgid "Collapse"
msgstr "Thu gọn"

#: framework/premium/extensions/woocommerce-extra/features/filters/filter-types/taxonomies.php:473
msgid "Expand"
msgstr "Mở rộng"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/filter-presenter.php:64
msgid "category"
msgstr "danh mục"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/filter-presenter.php:108
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Preview.js:32
msgid "Find by %s"
msgstr "Tìm theo %s"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:434
msgid "Regenerate the product taxonomies lookup table"
msgstr "Tạo lại bảng tra cứu phân loại sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:435
msgid "This tool will regenerate the product taxonomies lookup table data from existing product(s) data. This process may take a while."
msgstr "Công cụ này sẽ tái tạo dữ liệu bảng tra cứu phân loại sản phẩm từ dữ liệu sản phẩm hiện có. Quá trình này có thể mất một thời gian."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:446
msgid "Product taxonomies lookup table data is regenerating"
msgstr "Dữ liệu bảng tra cứu phân loại sản phẩm đang được tái tạo"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:459
msgid "Regenerate"
msgstr "Tái tạo"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:453
msgid "Filling in progress (%d)"
msgstr "Đang tiến hành điền (%d)"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:494
msgid "Resume the product taxonomies lookup table regeneration"
msgstr "Tiếp tục quá trình tái tạo bảng tra cứu phân loại sản phẩm"

#. translators: %1$s = count of products already processed.
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:497
msgid "This tool will resume the product taxonomies lookup table regeneration at the point in which it was aborted (%1$s products were already processed)."
msgstr "Công cụ này sẽ tiếp tục quá trình tái tạo bảng tra cứu phân loại sản phẩm tại thời điểm nó bị hủy bỏ (%1$s sản phẩm đã được xử lý)."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:516
msgid "Product taxonomies lookup table regeneration process has been resumed."
msgstr "Quá trình tái tạo bảng tra cứu phân loại sản phẩm đã được tiếp tục."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:518
msgid "Resume"
msgstr "Tiếp tục"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:466
msgid "Abort the product taxonomies lookup table regeneration"
msgstr "Hủy quá trình tái tạo bảng tra cứu phân loại sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:486
msgid "Product taxonomies lookup table regeneration process has been aborted."
msgstr "Quá trình tái tạo bảng tra cứu phân loại sản phẩm đã bị hủy."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:488
msgid "Abort"
msgstr "Hủy"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:158
msgid "Container Border Color"
msgstr "Màu viền của vùng chứa"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:189
msgid "Container Background Color"
msgstr "Màu nền của vùng chứa"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:143
msgid "API Version"
msgstr "Phiên bản API"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:268
msgid "More information on how to generate an API key for Brevo can be found %shere%s."
msgstr "Thông tin thêm về cách tạo khóa API cho Brevo có thể được tìm thấy %stại đây%s."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:834
msgid "Hide Documentation Links"
msgstr "Ẩn liên kết tài liệu"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:842
msgid "Hide Video Links"
msgstr "Ẩn liên kết video"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:104
msgid "Display the currently active filters."
msgstr "Hiển thị các bộ lọc hiện đang hoạt động."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:47
msgid "Category 1"
msgstr "Danh mục 1"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:53
msgid "Category 2"
msgstr "Danh mục 2"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:66
msgid "Attribute 1"
msgstr "Thuộc tính 1"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:72
msgid "Attribute 2"
msgstr "Thuộc tính 2"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:12
msgid "Date"
msgstr "Ngày"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:10
msgid "Filter by Price Controls"
msgstr "Lọc theo điều khiển giá"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:106
msgid "Filter by Price"
msgstr "Lọc theo giá"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:11
msgid "Widget for filtering the WooCommerce products by price."
msgstr "Widget để lọc sản phẩm WooCommerce theo giá."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:58
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:473
msgid "Show Tooltip"
msgstr "Hiển thị gợi ý"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:70
msgid "Show Prices"
msgstr "Hiển thị giá"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Disabled.js:6
msgid "Please select a valid taxonomy."
msgstr "Vui lòng chọn một phân loại hợp lệ."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:174
msgid "Filter Settings"
msgstr "Cài đặt bộ lọc"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:253
msgid "Show Search Box"
msgstr "Hiển thị hộp tìm kiếm"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:496
msgid "Container Maximum Height"
msgstr "Chiều cao tối đa của vùng chứa"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:55
msgid "Exclude Speciffic Items"
msgstr "Loại trừ các mục cụ thể"

#: static/js/dashboard/VersionMismatch.js:62
#: static/js/notifications/VersionMismatchNotice.js:74
msgid "Update Blocksy Theme Now"
msgstr "Cập nhật chủ đề Blocksy ngay bây giờ"

#: static/js/dashboard/screens/DemoInstall.js:183
msgid "Your site is misconfigured and AJAX requests are not reaching your backend. Please click %shere%s to find the common causes and possible solutions to this.<br> Error code - %s"
msgstr "Trang web của bạn đã được cấu hình sai và các yêu cầu AJAX không đến được backend của bạn. Vui lòng nhấn %svào đây%s để tìm hiểu nguyên nhân phổ biến và các giải pháp có thể cho điều này.<br> Mã lỗi - %s"

#: static/js/dashboard/screens/DemoInstall.js:201
msgid "Failed to retrieve starter sites list.<br> Error code - %s"
msgstr "Không thể lấy danh sách các mẫu web bắt đầu.<br> Mã lỗi - %s"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:129
msgid "Installing %s"
msgstr "Đang cài đặt %s"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:345
msgid "Preparing data..."
msgstr "Đang chuẩn bị dữ liệu..."

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:89
msgid "Required plan"
msgstr "Kế hoạch cần thiết"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:13
msgid "All Plans"
msgstr "Tất cả các kế hoạch"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:15
msgid "Pro"
msgstr "Chuyên nghiệp"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:182
msgid "All Builders"
msgstr "Tất cả các nhà xây dựng"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:206
msgid "Search for a starter site..."
msgstr "Tìm kiếm một mẫu web bắt đầu..."

#: static/js/dashboard/screens/SiteExport.js:176
msgid "Export Site"
msgstr "Xuất trang web"

#: static/js/dashboard/screens/SiteExport.js:191
msgid "Starter site"
msgstr "Mẫu web bắt đầu"

#: static/js/dashboard/screens/SiteExport.js:203
msgid "Select a starter site"
msgstr "Chọn một mẫu web bắt đầu"

#: static/js/editor/blocks/about-me/index.js:15
msgid "About Me Controls"
msgstr "Điều khiển về tôi"

#: static/js/editor/blocks/about-me/index.js:43
msgid "Showcase your personal information across your website."
msgstr "Trưng bày thông tin cá nhân của bạn trên toàn bộ trang web của bạn."

#: static/js/editor/blocks/breadcrumbs/Preview.js:63
msgid "Subpage"
msgstr "Trang phụ"

#: static/js/editor/blocks/breadcrumbs/index.js:11
msgid "Breadcrumbs"
msgstr "Điều hướng trang"

#: static/js/editor/blocks/breadcrumbs/index.js:12
msgid "Display navigational links, showing users their path within the site."
msgstr "Hiển thị các liên kết điều hướng, cho người dùng thấy đường đi của họ trong trang web."

#: static/js/editor/blocks/contact-info/index.js:15
msgid "Contact Info Controls"
msgstr "Điều khiển thông tin liên hệ"

#: static/js/editor/blocks/contact-info/index.js:52
msgid "Display essential contact details to your visitors."
msgstr "Hiển thị chi tiết liên hệ cần thiết cho khách truy cập của bạn."

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:102
msgid "9:16"
msgstr "9:16"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:106
msgid "3:4"
msgstr "3:4"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:110
msgid "2:3"
msgstr "2:3"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:133
msgid "Width"
msgstr "Chiều rộng"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:157
msgid "Height"
msgstr "Chiều cao"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:178
msgid "Scale"
msgstr "Tỉ lệ"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:209
msgid "Resolution"
msgstr "Độ phân giải"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:215
msgid "Select the size of the source image."
msgstr "Chọn kích thước của hình ảnh nguồn."

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:57
msgid "Image Settings"
msgstr "Cài đặt hình ảnh"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:77
msgid "Aspect Ratio"
msgstr "Tỉ lệ khung hình"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:82
msgid "Original"
msgstr "Gốc"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:90
msgid "16:9"
msgstr "16:9"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:94
msgid "4:3"
msgstr "4:3"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:98
msgid "3:2"
msgstr "3:2"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:100
msgid "Icon/Logo"
msgstr "Biểu tượng/Logo"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:138
msgid "Expand on click"
msgstr "Mở rộng khi nhấp"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:151
msgid "Video thumbnail"
msgstr "Hình thu nhỏ video"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:161
msgid "Image Hover Effect"
msgstr "Hiệu ứng di chuột qua hình ảnh"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:174
msgid "Zoom In"
msgstr "Phóng to"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:178
msgid "Zoom Out"
msgstr "Thu nhỏ"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:202
msgid "Alternative Text"
msgstr "Văn bản thay thế"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:215
msgid "Describe the purpose of the image."
msgstr "Mô tả mục đích của hình ảnh."

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:221
msgid "Leave empty if decorative."
msgstr "Để trống nếu là trang trí."

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:238
msgid "Image size"
msgstr "Kích thước hình ảnh"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:271
msgid "Logo Gap"
msgstr "Khoảng cách Logo"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:323
msgid "Custom field fallback"
msgstr "Trường tùy chỉnh dự phòng"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:343
msgid "Term additional class"
msgstr "Lớp bổ sung cho thuật ngữ"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:350
msgid "Additional class for term items. Useful for styling."
msgstr "Lớp bổ sung cho các mục thuật ngữ. Hữu ích cho việc thiết kế."

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:45
msgid "Content Source"
msgstr "Nguồn nội dung"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:51
msgid "Search for field"
msgstr "Tìm kiếm trường"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:87
msgid "Image Source"
msgstr "Nguồn hình ảnh"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:14
msgid "Change heading level"
msgstr "Thay đổi cấp độ tiêu đề"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:38
msgid "Heading 1"
msgstr "Tiêu đề 1"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:39
msgid "Heading 2"
msgstr "Tiêu đề 2"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:40
msgid "Heading 3"
msgstr "Tiêu đề 3"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:41
msgid "Heading 4"
msgstr "Tiêu đề 4"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:42
msgid "Heading 5"
msgstr "Tiêu đề 5"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:43
msgid "Heading 6"
msgstr "Tiêu đề 6"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:44
msgid "Paragraph"
msgstr "Đoạn văn"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:45
msgid "Span"
msgstr "Khoảng"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:46
msgid "Div"
msgstr "Div"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:101
msgid "Archive Image"
msgstr "Hình ảnh lưu trữ"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:38
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:136
msgid "Stock Status"
msgstr "Trạng thái hàng tồn kho"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:24
#: static/js/editor/blocks/dynamic-data/preview-parts/wp/TermTextPreview.js:8
msgid "Term Title"
msgstr "Tên phân loại"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:28
#: static/js/editor/blocks/dynamic-data/preview-parts/wp/TermTextPreview.js:9
msgid "Term Description"
msgstr "Mô tả phân loại"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:32
msgid "Term Image"
msgstr "Hình ảnh thuật ngữ"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:36
#: static/js/editor/blocks/dynamic-data/preview-parts/wp/TermTextPreview.js:10
msgid "Term Count"
msgstr "Số lượng nội dung"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:50
msgid "Excerpt"
msgstr "Tóm tắt"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:55
msgid "Post Date"
msgstr "Ngày đăng"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:65
msgid "Terms"
msgstr "Mục phân loại"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:91
#: static/js/editor/blocks/dynamic-data/preview-parts/WpFieldPreview.js:46
msgid "Archive Title"
msgstr "Tiêu đề lưu trữ"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:96
#: static/js/editor/blocks/dynamic-data/preview-parts/WpFieldPreview.js:50
msgid "Archive Description"
msgstr "Mô tả lưu trữ"

#: static/js/editor/blocks/dynamic-data/index.js:17
msgid "Fetch and display content from various sources."
msgstr "Lấy và hiển thị nội dung từ nhiều nguồn."

#: static/js/editor/blocks/dynamic-data/index.js:33
msgid "Dynamic Title"
msgstr "Tiêu đề động"

#: static/js/editor/blocks/dynamic-data/index.js:37
msgid "Dynamic Excerpt"
msgstr "Tóm tắt động"

#: static/js/editor/blocks/dynamic-data/index.js:41
msgid "Dynamic Post Date"
msgstr "Ngày đăng động"

#: static/js/editor/blocks/dynamic-data/index.js:45
msgid "Dynamic Comments"
msgstr "Bình luận động"

#: static/js/editor/blocks/dynamic-data/index.js:49
msgid "Dynamic Terms"
msgstr "Thuật ngữ động"

#: static/js/editor/blocks/dynamic-data/index.js:53
msgid "Dynamic Author"
msgstr "Tác giả động"

#: static/js/editor/blocks/dynamic-data/index.js:57
msgid "Dynamic Featured Image"
msgstr "Hình ảnh nổi bật động"

#: static/js/editor/blocks/dynamic-data/index.js:61
msgid "Dynamic Author Avatar"
msgstr "Hình đại diện tác giả động"

#: static/js/editor/blocks/dynamic-data/index.js:65
msgid "Dynamic Price"
msgstr "Giá động"

#: static/js/editor/blocks/dynamic-data/index.js:69
msgid "Dynamic Stock Status"
msgstr "Trạng thái hàng tồn kho động"

#: static/js/editor/blocks/dynamic-data/index.js:73
msgid "Dynamic Brands"
msgstr "Thương hiệu động"

#: static/js/editor/blocks/dynamic-data/index.js:77
msgid "Dynamic SKU"
msgstr "SKU động"

#: static/js/editor/blocks/dynamic-data/index.js:81
msgid "Dynamic Rating"
msgstr "Đánh giá động"

#: static/js/editor/blocks/dynamic-data/index.js:85
msgid "Dynamic Term Title"
msgstr "Tiêu đề thuật ngữ động"

#: static/js/editor/blocks/dynamic-data/index.js:89
msgid "Dynamic Term Description"
msgstr "Mô tả thuật ngữ động"

#: static/js/editor/blocks/dynamic-data/index.js:93
msgid "Dynamic Term Count"
msgstr "Số lượng thuật ngữ động"

#: static/js/editor/blocks/dynamic-data/index.js:97
msgid "Dynamic Term Image"
msgstr "Hình ảnh thuật ngữ động"

#: static/js/editor/blocks/dynamic-data/preview-parts/woo/RatingPreview.js:13
msgid "Rated %s out of 5"
msgstr "Được đánh giá %s trên 5"

#: static/js/editor/blocks/dynamic-data/utils.js:15
msgid "Unknown"
msgstr "Không rõ"

#: static/js/editor/blocks/post-template/Edit.js:170
#: static/js/editor/blocks/tax-template/Edit.js:152
msgid "List view"
msgstr "Xem dạng danh sách"

#: static/js/editor/blocks/post-template/Edit.js:176
#: static/js/editor/blocks/tax-template/Edit.js:158
msgid "Grid view"
msgstr "Xem dạng lưới"

#: static/js/editor/blocks/post-template/Edit.js:209
#: static/js/editor/blocks/tax-template/Edit.js:190
msgid "Tablet Columns"
msgstr "Cột máy tính bảng"

#: static/js/editor/blocks/post-template/Edit.js:225
#: static/js/editor/blocks/tax-template/Edit.js:206
msgid "Mobile Columns"
msgstr "Cột di động"

#: static/js/editor/blocks/post-template/index.js:13
msgid "Post Template"
msgstr "Mẫu bài viết"

#: static/js/editor/blocks/query/Edit.js:120
#: static/js/editor/blocks/tax-query/Edit.js:123
msgid "Reset layout"
msgstr "Đặt lại bố cục"

#: static/js/editor/blocks/query/Edit.js:186
msgid "Pagination"
msgstr "Phân trang"

#: static/js/editor/blocks/query/Edit.js:208
#: static/js/editor/blocks/tax-query/Edit.js:189
msgid "Block ID"
msgstr "ID khối"

#: static/js/editor/blocks/query/Edit.js:214
#: static/js/editor/blocks/tax-query/Edit.js:195
msgid "Please look at the documentation for more information on why this is useful."
msgstr "Vui lòng xem tài liệu để biết thêm thông tin về lý do tại sao điều này hữu ích."

#: static/js/editor/blocks/query/edit/PatternSelectionModal.js:82
#: static/js/editor/blocks/tax-query/edit/PatternSelectionModal.js:68
msgid "Choose a pattern"
msgstr "Chọn một mẫu"

#: static/js/editor/blocks/query/edit/PatternSelectionModal.js:91
#: static/js/editor/blocks/tax-query/edit/PatternSelectionModal.js:77
msgid "Search for patterns"
msgstr "Tìm kiếm mẫu"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:133
msgid "Publish Date"
msgstr "Ngày xuất bản"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:173
msgid "Menu Order"
msgstr "Thứ tự menu"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:216
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:187
msgid "Order"
msgstr "Thứ tự"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:224
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:195
msgid "Descending"
msgstr "Giảm dần"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:232
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:203
msgid "Ascending"
msgstr "Tăng dần"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:275
msgid "Sticky Posts"
msgstr "Bài viết ghim"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:299
msgid "Only"
msgstr "Chỉ"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:318
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:222
msgid "Parameters"
msgstr "Tham số"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:105
#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:91
msgid "Create Custom Layout"
msgstr "Tạo bố cục tùy chỉnh"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:37
#: static/js/editor/blocks/query/index.js:12
msgid "Advanced Posts"
msgstr "Bài viết nâng cao"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:41
msgid "Inherit the Customizer layout, start with a pattern or create a custom layout"
msgstr "Kế thừa bố cục của tùy biến, bắt đầu với một mẫu hoặc tạo một bố cục tùy chỉnh"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:56
msgid "Inherit From Customizer"
msgstr "Kế thừa từ tùy biến"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:66
#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:57
msgid "Choose Pattern"
msgstr "Chọn mẫu"

#: static/js/editor/blocks/query/edit/TaxonomyControls.js:167
msgid "Search for a term"
msgstr "Tìm kiếm một thuật ngữ"

#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:47
msgid "Include %s"
msgstr "Bao gồm %s"

#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:75
msgid "Related"
msgstr "Liên quan"

#: static/js/editor/blocks/query/index.js:13
#: static/js/editor/blocks/tax-query/index.js:13
msgid "Create advanced queries based on your specified criterias."
msgstr "Tạo các truy vấn nâng cao dựa trên các tiêu chí bạn đã chỉ định."

#: static/js/editor/blocks/search/Edit.js:163
msgid "Button Outside"
msgstr "Nút Bên ngoài"

#: static/js/editor/blocks/search/Edit.js:180
msgid "Use button with text"
msgstr "Sử dụng nút với văn bản"

#: static/js/editor/blocks/search/Edit.js:282
msgid "Button Icon Color"
msgstr "Màu biểu tượng nút"

#: static/js/editor/blocks/search/Edit.js:379
msgid "Dropdown Background Color"
msgstr "Màu nền Dropdown"

#: static/js/editor/blocks/search/Edit.js:400
msgid "Dropdown Shadow Color"
msgstr "Màu bóng Dropdown"

#: static/js/editor/blocks/search/Preview.js:24
msgid "Select category"
msgstr "Chọn danh mục"

#: static/js/editor/blocks/search/index.js:16
msgid "Advanced Search"
msgstr "Tìm kiếm nâng cao"

#: static/js/editor/blocks/search/index.js:17
msgid "Quickly find specific content on your site."
msgstr "Nhanh chóng tìm thấy nội dung cụ thể trên trang web của bạn."

#: static/js/editor/blocks/share-box/Edit.js:110
#: static/js/editor/blocks/socials/Edit.js:110
msgid "Icons Background Colors"
msgstr "Màu nền biểu tượng"

#: static/js/editor/blocks/share-box/Edit.js:142
#: static/js/editor/blocks/socials/Edit.js:142
msgid "Icons Border Colors"
msgstr "Màu viền biểu tượng"

#: static/js/editor/blocks/share-box/index.js:15
msgid "Share Box Controls"
msgstr "Điều khiển hộp chia sẻ"

#: static/js/editor/blocks/share-box/index.js:45
msgid "Share content on social media, boosting visibility & engagement."
msgstr "Chia sẻ nội dung trên mạng xã hội, tăng tầm nhìn & sự tham gia."

#: static/js/editor/blocks/socials/index.js:15
msgid "Socials Controls"
msgstr "Điều khiển mạng xã hội"

#: static/js/editor/blocks/socials/index.js:45
msgid "Display your social media profiles and boost the site engagement."
msgstr "Hiển thị hồ sơ mạng xã hội của bạn và tăng sự tham gia trang web."

#: static/js/editor/blocks/socials/index.js:47
#: static/js/editor/blocks/widgets-wrapper/index.js:58
msgid "Socials"
msgstr "Mạng xã hội"

#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:120
msgid "ID"
msgstr "ID"

#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:136
msgid "Count"
msgstr "Đếm"

#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:40
#: static/js/editor/blocks/tax-query/index.js:12
msgid "Advanced Taxonomies"
msgstr "Phân loại nâng cao"

#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:44
msgid "Start with a pattern or create a custom layout"
msgstr "Bắt đầu với một mẫu hoặc tạo bố cục tùy chỉnh"

#: static/js/editor/blocks/tax-template/index.js:13
msgid "Taxonomy Template"
msgstr "Mẫu phân loại"

#: static/js/editor/blocks/widgets-wrapper/Edit.js:81
msgid "Expandable Container"
msgstr "Container có thể mở rộng"

#: static/js/editor/blocks/widgets-wrapper/index.js:40
msgid "Widgets Wrapper"
msgstr "Bao bọc Widget"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:41
msgid "Parameters options"
msgstr "Tùy chọn tham số"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:46
msgid "All options are currently hidden"
msgstr "Tất cả các tùy chọn hiện đang bị ẩn"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:82
msgid "All options reset"
msgstr "Đặt lại tất cả các tùy chọn"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:87
msgid "Reset all"
msgstr "Đặt lại tất cả"

#: static/js/options/ConditionsManager/ExpireCondition.js:119
msgid "The expiration date cannot be set earlier than the start date."
msgstr "Ngày hết hạn không thể được đặt sớm hơn ngày bắt đầu."

#: framework/features/demo-install.php:155,
#: framework/features/demo-install/content-installer.php:164,
#: framework/features/demo-install/content-installer.php:159,
#: framework/features/demo-install/demo-register.php:9
msgid "No demo name provided."
msgstr "Không có tên demo được cung cấp."

#: framework/helpers/exts-configs.php:369,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:248,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:271,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:4
msgid "Stock Scarcity"
msgstr "Khan hiếm hàng tồn kho"

#: framework/helpers/exts-configs.php:370
msgid "Show the remaining stock of a product to create a sense of urgency and encourage your visitors to make a purchase."
msgstr "Hiển thị số lượng hàng tồn kho còn lại của một sản phẩm để tạo cảm giác gấp rút và khuyến khích khách truy cập của bạn thực hiện mua hàng."

#: framework/views/theme-mismatch.php:36
#: static/js/dashboard/VersionMismatch.js:19
#: static/js/notifications/VersionMismatchNotice.js:27
msgid "Action required - please update Blocksy theme to the latest version!"
msgstr "Yêu cầu hành động - vui lòng cập nhật chủ đề Blocksy lên phiên bản mới nhất!"

#: framework/views/theme-mismatch.php:41
#: static/js/dashboard/VersionMismatch.js:25
#: static/js/notifications/VersionMismatchNotice.js:35
msgid "We detected that you are using an outdated version of Blocksy theme."
msgstr "Chúng tôi phát hiện ra rằng bạn đang sử dụng phiên bản lỗi thời của chủ đề Blocksy."

#: framework/views/theme-mismatch.php:45
#: static/js/dashboard/VersionMismatch.js:32
#: static/js/notifications/VersionMismatchNotice.js:44
msgid "In order to take full advantage of all features the core has to offer - please install and activate the latest version of Blocksy theme."
msgstr "Để tận dụng tối đa tất cả các tính năng mà lõi phải cung cấp - vui lòng cài đặt và kích hoạt phiên bản mới nhất của chủ đề Blocksy."

#: framework/extensions/newsletter-subscribe/customizer.php:94,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:127
msgid "Make Name Field Required"
msgstr "Yêu cầu trường tên"

#: framework/extensions/trending/customizer.php:121
msgid "Tag"
msgstr "Thẻ"

#: framework/extensions/trending/customizer.php:142
msgid "Taxonomy Source"
msgstr "Nguồn phân loại"

#: framework/extensions/trending/customizer.php:216
msgid "Module Title Icon Source"
msgstr "Nguồn biểu tượng tiêu đề module"

#: framework/extensions/trending/customizer.php:303
msgid "Products Status"
msgstr "Trạng thái sản phẩm"

#: framework/extensions/trending/customizer.php:307
msgid "On Sale"
msgstr "Đang giảm giá"

#: framework/extensions/trending/customizer.php:308
msgid "Top Rated"
msgstr "Đánh giá hàng đầu"

#: framework/extensions/trending/customizer.php:309
msgid "Best Sellers"
msgstr "Bán chạy nhất"

#: framework/extensions/trending/customizer.php:409
msgid "Show Product Price"
msgstr "Hiển thị giá sản phẩm"

#: framework/extensions/trending/customizer.php:423
msgid "Show Taxonomy"
msgstr "Hiển thị phân loại"

#: framework/extensions/trending/customizer.php:441
msgid "Taxonomy Style"
msgstr "Loại phân loại"

#: framework/extensions/trending/customizer.php:450
msgid "Underline"
msgstr "Gạch dưới"

#: framework/extensions/trending/customizer.php:465,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:55
msgid "Image Width"
msgstr "Chiều rộng hình ảnh"

#: framework/extensions/trending/customizer.php:693,
#: framework/extensions/trending/customizer.php:825
msgid "Taxonomy Font"
msgstr "Phông chữ phân loại"

#: framework/extensions/trending/customizer.php:708,
#: framework/extensions/trending/customizer.php:748
msgid "Taxonomies Font Color"
msgstr "Màu phông chữ phân loại"

#: framework/extensions/trending/customizer.php:780
msgid "Taxonomies Button Color"
msgstr "Màu nút phân loại"

#: framework/extensions/trending/customizer.php:835
msgid "Taxonomy Font Color"
msgstr "Màu phông chữ phân loại"

#: framework/extensions/trending/customizer.php:860,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:248,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:476,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:494
msgid "Image Border Radius"
msgstr "Bán kính viền hình ảnh"

#: framework/features/blocks/blocks.php:19
msgctxt "Block pattern category"
msgid "Blocksy"
msgstr "Blocksy"

#: framework/features/blocks/blocks.php:24
msgid "Patterns that contain buttons and call to actions."
msgstr "Các mẫu chứa nút và kêu gọi hành động."

#: framework/features/blocks/blocks.php:71
msgid "Home Page Text"
msgstr "Văn bản trang chủ"

#: framework/features/demo-install/child-theme.php:9
msgid "Sorry, you don't have permission to install child themes."
msgstr "Xin lỗi, bạn không có quyền cài đặt chủ đề con."

#: framework/features/demo-install/content-eraser.php:23
msgid "Sorry, you don't have permission to erase content."
msgstr "Xin lỗi, bạn không có quyền xóa nội dung."

#: framework/features/demo-install/content-installer.php:69
msgid "Sorry, you don't have permission to install content."
msgstr "Xin lỗi, bạn không có quyền cài đặt nội dung."

#: framework/features/demo-install/content-installer.php:195,
#: framework/features/demo-install/content-installer.php:189
msgid "No demo data found."
msgstr "Không tìm thấy dữ liệu demo."

#: framework/features/demo-install/content-installer.php:351,
#: framework/features/demo-install/content-installer.php:346
msgid "No pages to assign."
msgstr "Không có trang để gán."

#: framework/features/demo-install/install-finish.php:23
msgid "Sorry, you don't have permission to finish the installation."
msgstr "Xin lỗi, bạn không có quyền hoàn thành việc cài đặt."

#: framework/features/demo-install/options-import.php:38
msgid "Sorry, you don't have permission to install options."
msgstr "Xin lỗi, bạn không có quyền cài đặt tùy chọn."

#: framework/features/demo-install/options-import.php:50,
#: framework/features/demo-install/options-import.php:45,
#: framework/features/demo-install/options-import.php:80,
#: framework/features/demo-install/options-import.php:75,
#: framework/features/demo-install/widgets-import.php:48,
#: framework/features/demo-install/widgets-import.php:43
msgid "No demo to install"
msgstr "Không có demo để cài đặt"

#: framework/features/demo-install/plugins-uninstaller.php:9
msgid "Sorry, you don't have permission to uninstall plugins."
msgstr "Xin lỗi, bạn không có quyền gỡ cài đặt plugin."

#: framework/features/demo-install/required-plugins.php:37
msgid "Sorry, you don't have permission to install plugins."
msgstr "Xin lỗi, bạn không có quyền cài đặt plugin."

#: framework/features/demo-install/required-plugins.php:49,
#: framework/features/demo-install/required-plugins.php:44
msgid "No plugins to install."
msgstr "Không có plugin để cài đặt."

#: framework/features/demo-install/widgets-import.php:36
msgid "Sorry, you don't have permission to install widgets."
msgstr "Xin lỗi, bạn không có quyền cài đặt widget."

#: framework/features/demo-install/widgets-import.php:79,
#: framework/features/demo-install/widgets-import.php:73
msgid "No widgets to install."
msgstr "Không có widget để cài đặt."

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:98
msgid "Fields Gap"
msgstr "Khoảng cách trường"

#: framework/features/blocks/about-me/options.php:21
msgid "User Source"
msgstr "Nguồn người dùng"

#: framework/features/blocks/about-me/options.php:26
msgid "Dynamic"
msgstr "Động"

#: framework/features/blocks/about-me/options.php:37
msgid "User"
msgstr "Người dùng"

#: framework/features/blocks/about-me/options.php:92
msgid "Image Shape"
msgstr "Hình dạng hình ảnh"

#: framework/features/blocks/about-me/options.php:104
msgid "Alignment"
msgstr "Căn chỉnh"

#: framework/features/blocks/about-me/options.php:119,
#: framework/features/blocks/socials/options.php:19
msgid "Social Channels"
msgstr "Kênh xã hội"

#: framework/features/blocks/about-me/options.php:195,
#: framework/features/blocks/share-box/options.php:149,
#: framework/features/blocks/socials/options.php:101
msgid "Official"
msgstr "Chính thức"

#: framework/features/blocks/about-me/view.php:197
msgid "View Profile"
msgstr "Xem hồ sơ"

#: framework/features/blocks/contact-info/options.php:44
msgid "Contact Information"
msgstr "Thông tin liên hệ"

#: framework/features/blocks/dynamic-data/options.php:24,
#: framework/features/blocks/dynamic-data/options.php:49
msgid "Date type"
msgstr "Loại ngày"

#: framework/features/blocks/dynamic-data/options.php:31
msgid "Published Date"
msgstr "Ngày xuất bản"

#: framework/features/blocks/dynamic-data/options.php:32
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:141
msgid "Modified Date"
msgstr "Ngày sửa đổi"

#: framework/features/blocks/dynamic-data/options.php:39
msgid "Default format"
msgstr "Định dạng mặc định"

#: framework/features/blocks/dynamic-data/options.php:41
msgid "Example: January 24, 2022"
msgstr "Ví dụ: 24 tháng 1, 2022"

#: framework/features/blocks/dynamic-data/options.php:75
msgid "Custom date format"
msgstr "Định dạng ngày tùy chỉnh"

#: framework/features/blocks/dynamic-data/options.php:79
msgid "Enter a date or time"
msgstr "Nhập ngày hoặc thời gian"

#: framework/features/blocks/dynamic-data/options.php:96,
#: framework/features/blocks/dynamic-data/options.php:97,
#: framework/features/blocks/dynamic-data/views/wp-field.php:194
msgid "No comments"
msgstr "Không có bình luận"

#: framework/features/blocks/dynamic-data/options.php:102,
#: framework/features/blocks/dynamic-data/options.php:103,
#: framework/features/blocks/dynamic-data/views/wp-field.php:195
msgid "One comment"
msgstr "Một bình luận"

#: framework/features/blocks/dynamic-data/options.php:108
msgid "Multiple comments"
msgstr "Nhiều bình luận"

#: framework/features/blocks/dynamic-data/options.php:120
msgid "Separator"
msgstr "Dấu phân cách"

#: framework/features/blocks/dynamic-data/options.php:132
msgid "Author Field"
msgstr "Trường tác giả"

#: framework/features/blocks/dynamic-data/options.php:139
msgid "Nickname"
msgstr "Biệt danh"

#: framework/features/blocks/dynamic-data/options.php:138
msgid "Display Name"
msgstr "Tên hiển thị"

#: framework/features/blocks/dynamic-data/options.php:140
msgid "First Name"
msgstr "Tên"

#: framework/features/blocks/dynamic-data/options.php:141
msgid "Last Name"
msgstr "Họ"

#: framework/features/blocks/dynamic-data/options.php:172
msgid "Link to post"
msgstr "Liên kết đến bài viết"

#: framework/features/blocks/dynamic-data/options.php:176
msgid "Link to author page"
msgstr "Liên kết đến trang tác giả"

#: framework/features/blocks/dynamic-data/options.php:180
msgid "Link to term page"
msgstr "Liên kết đến trang thuật ngữ"

#: framework/features/blocks/dynamic-data/options.php:184
msgid "Link to archive page"
msgstr "Liên kết đến trang lưu trữ"

#: framework/features/blocks/dynamic-data/options.php:197
msgid "Open in new tab"
msgstr "Mở trong tab mới"

#: framework/features/blocks/dynamic-data/options.php:203
msgid "Link Rel"
msgstr "Liên kết Rel"

#: framework/features/blocks/dynamic-data/options.php:220
msgid "Terms accent color"
msgstr "Màu sắc nhấn mạnh thuật ngữ"

#: framework/features/blocks/search/options.php:147,
#: framework/features/blocks/search/view.php:265
msgid "Select Category"
msgstr "Chọn danh mục"

#: framework/features/blocks/search/options.php:175,
#: framework/premium/features/premium-header/items/search-input/options.php:164
msgid "Taxonomy Children"
msgstr "Thuật ngữ con"

#: framework/features/blocks/search/options.php:205,
#: framework/premium/features/premium-header/items/search-input/options.php:192
msgid "Search Through Taxonomies"
msgstr "Tìm kiếm qua các thuật ngữ"

#: framework/features/blocks/search/options.php:209,
#: framework/premium/features/premium-header/items/search-input/options.php:196
msgid "Search through taxonomies from selected custom post types."
msgstr "Tìm kiếm qua phân loại từ các loại nội dung tùy chỉnh đã chọn."

#: framework/features/blocks/share-box/options.php:15
msgid "Share Icons"
msgstr "Biểu tượng chia sẻ"

#: framework/features/blocks/share-box/options.php:43
msgid "Reddit"
msgstr "Reddit"

#: framework/features/blocks/share-box/options.php:49
msgid "Hacker News"
msgstr "Hacker News"

#: framework/features/blocks/share-box/options.php:67
msgid "Telegram"
msgstr "Telegram"

#: framework/features/blocks/share-box/options.php:73
msgid "Viber"
msgstr "Viber"

#: framework/features/blocks/share-box/options.php:79
msgid "WhatsApp"
msgstr "WhatsApp"

#: framework/features/blocks/share-box/options.php:85
msgid "Flipboard"
msgstr "Flipboard"

#: framework/features/blocks/share-box/options.php:91
msgid "Line"
msgstr "Line"

#: framework/features/conditions/rules/archive-loop.php:6
msgid "Archive Item with Taxonomy ID"
msgstr "Mục lưu trữ với ID thuật ngữ"

#: framework/features/conditions/rules/archive-loop.php:13
msgid "WooCommerce Archive Item with Taxonomy ID"
msgstr "Mục lưu trữ WooCommerce với ID thuật ngữ"

#: framework/features/conditions/rules/archive-loop.php:19
msgid "Archive Loop Speciffic"
msgstr "Lặp lưu trữ cụ thể"

#: framework/features/conditions/rules/posts.php:10
msgid "Post Archives"
msgstr "Lưu trữ bài viết"

#: framework/features/conditions/rules/woo.php:54
msgid "Single Product ID"
msgstr "ID sản phẩm đơn"

#: framework/features/conditions/rules/woo.php:59
msgid "Single Product with Taxonomy ID"
msgstr "Sản phẩm đơn với ID thuật ngữ"

#: framework/premium/extensions/woocommerce-extra/utils.php:141
msgid "Private: %s"
msgstr "Riêng tư: %s"

#: framework/premium/extensions/woocommerce-extra/utils.php:131
msgid "Protected: %s"
msgstr "Được bảo vệ: %s"

#: framework/premium/features/content-blocks/hooks-manager.php:733
msgid "WooCommerce Single Product"
msgstr "Sản phẩm đơn WooCommerce"

#: framework/premium/features/media-video/options.php:119
msgid "Video Size"
msgstr "Kích thước video"

#: framework/premium/features/media-video/options.php:127
#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:192
msgid "Contain"
msgstr "Chứa"

#: framework/premium/features/media-video/options.php:128
#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:187
msgid "Cover"
msgstr "Bìa"

#. translators: placeholder here means the actual URL.
#: framework/premium/features/media-video/options.php:132
msgid "Choose how the video will fill its container. More info about this can be found %shere%s."
msgstr "Chọn cách video sẽ điền vào vùng chứa của nó. Thông tin thêm về điều này có thể được tìm thấy %stại đây%s."

#: framework/features/blocks/dynamic-data/views/avatar-field.php:30
msgid "%s Avatar"
msgstr "%s Avatar"

#: framework/features/blocks/query/block-patterns/posts-layout-1.php:4
msgid "Posts - Layout 1"
msgstr "Bài viết - Bố cục 1"

#: framework/features/blocks/query/block-patterns/posts-layout-2.php:4
msgid "Posts - Layout 2"
msgstr "Bài viết - Bố cục 2"

#: framework/features/blocks/query/block-patterns/posts-layout-3.php:4
msgid "Posts - Layout 3"
msgstr "Bài viết - Bố cục 3"

#: framework/features/blocks/query/block-patterns/posts-layout-4.php:4
msgid "Posts - Layout 4"
msgstr "Bài viết - Bố cục 4"

#: framework/features/blocks/query/block-patterns/posts-layout-5.php:4
msgid "Posts - Layout 5"
msgstr "Bài viết - Bố cục 5"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-1.php:4
msgid "Taxonomies - Layout 1"
msgstr "Thuật ngữ - Bố cục 1"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-2.php:4
msgid "Taxonomies - Layout 2"
msgstr "Thuật ngữ - Bố cục 2"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-3.php:4
msgid "Taxonomies - Layout 3"
msgstr "Thuật ngữ - Bố cục 3"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-4.php:4
msgid "Taxonomies - Layout 4"
msgstr "Thuật ngữ - Bố cục 4"

#: framework/features/header/items/account/options.php:15
msgid "Action Link"
msgstr "Liên kết hành động"

#: framework/features/header/items/account/options.php:49
msgid "Additional User Info"
msgstr "Thông tin người dùng bổ sung"

#: framework/features/header/items/account/options.php:53
msgid "Available fields: {user_email}, {user_name}, {user_role}"
msgstr "Các trường có sẵn: {user_email}, {user_name}, {user_role}"

#: framework/features/header/items/account/options.php:142
msgid "Menu"
msgstr "Menu"

#: framework/features/header/items/account/options.php:717
msgid "Items Hover Effect"
msgstr "Hiệu ứng di chuột qua các mục"

#: framework/features/header/items/account/options.php:727
msgid "Boxed Color"
msgstr "Màu đóng hộp"

#: framework/features/header/items/account/options.php:1945
msgid "Link Active"
msgstr "Liên kết đang hoạt động"

#: framework/premium/features/content-blocks/options/popup.php:262
msgid "Close Trigger Delay"
msgstr "Độ trễ kích hoạt đóng"

#: framework/premium/features/content-blocks/options/popup.php:269
msgid "Set the close delay time (in seconds) after the form submit action is detected."
msgstr "Đặt thời gian độ trễ đóng (theo giây) sau khi hành động gửi biểu mẫu được phát hiện."

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:100,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:813
msgid "Featured Icon/Logo"
msgstr "Biểu tượng/Logo nổi bật"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:570
msgid "Upvote"
msgstr "Bình chọn lên"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:584
msgid "Downvote"
msgstr "Bình chọn xuống"

#: framework/premium/extensions/woocommerce-extra/features/brands/brands-import-export.php:17,
#: framework/premium/extensions/woocommerce-extra/features/brands/brands-import-export.php:69
msgid "Blocksy Brands"
msgstr "Thương hiệu Blocksy"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/select.php:18
msgid "Choose page"
msgstr "Chọn trang"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/select.php:33
msgid "Choose a custom thank you page for this product."
msgstr "Chọn một trang cảm ơn tùy chỉnh cho sản phẩm này."

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:49
msgid "Product Image Visibility"
msgstr "Hiển thị hình ảnh sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:87
msgid "Product Price & Stock Visibility"
msgstr "Hiển thị giá & kho sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:42
msgid "Trigger Icon Type"
msgstr "Loại biểu tượng kích hoạt"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:77
msgid "Trigger Visibility"
msgstr "Hiển thị kích hoạt"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:107
msgid "Trigger Label"
msgstr "Nhãn kích hoạt"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:233
msgid "Panel Default State"
msgstr "Trạng thái mặc định của bảng điều khiển"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:240
msgid "Closed"
msgstr "Đã đóng"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:241
msgid "Opened"
msgstr "Đã mở"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:488
msgid "Panel AJAX Reveal"
msgstr "Bảng điều khiển hiển thị AJAX"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:140
msgid "Autoplay Gallery"
msgstr "Tự động phát thư viện"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:152
msgid "Delay (in seconds)"
msgstr "Trễ (theo giây)"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:219
msgid "Columns Spacing"
msgstr "Khoảng cách cột"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:247,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:275
msgid "Arrows Visibility"
msgstr "Hiển thị mũi tên"

#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:11
msgid "Prev/Next Arrow"
msgstr "Mũi tên trước/sau"

#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:41
msgid "Prev/Next Background"
msgstr "Nền trước/sau"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:372,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:160
msgid "Add {items} more items to get free shipping!"
msgstr "Thêm {items} mục nữa để nhận miễn phí vận chuyển!"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:52
msgid "Count Criteria"
msgstr "Tiêu chí đếm"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:69,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:59,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:246,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/price-filter.php:299,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:53,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:34,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:34
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:128
msgid "Price"
msgstr "Giá"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:60
msgid "Items"
msgstr "Mục"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:86
msgid "Goal Items"
msgstr "Mục mục tiêu"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:90
msgid "Amount of items the client has to buy in order to get free shipping."
msgstr "Số lượng mục mà khách hàng phải mua để nhận miễn phí vận chuyển."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:168
msgid "You can use dynamic code tags such as {items} inside this option."
msgstr "Bạn có thể sử dụng các thẻ mã động như {items} bên trong tùy chọn này."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:195,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:62
msgid "Bar Color"
msgstr "Màu thanh"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:187,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:29
msgid "🚨 Hurry up! Only {items} units left in stock!"
msgstr "🚨 Nhanh lên! Chỉ còn {items} đơn vị trong kho!"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:15
msgid "Stock Threshold"
msgstr "Ngưỡng kho"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:23
msgid "Show the stock scarcity module when product stock is below this number."
msgstr "Hiển thị mô-đun khan hiếm kho khi số lượng sản phẩm trong kho dưới con số này."

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:27
msgid "Message"
msgstr "Tin nhắn"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:43
msgid "Bar Height"
msgstr "Chiều cao thanh"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:686
msgid "Swatches removed"
msgstr "Đã xóa mẫu màu"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:693
msgid "Swatches saved"
msgstr "Đã lưu mẫu màu"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:755
msgid "Custom Attributes"
msgstr "Thuộc tính tùy chỉnh"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:761,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:800,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:873
msgid "Terms Limit"
msgstr "Giới hạn điều khoản"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:764,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:803
msgid "Set how many terms you want to display in this attribute."
msgstr "Đặt số lượng điều khoản bạn muốn hiển thị trong thuộc tính này."

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:774,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:813,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:887
#: static/js/editor/blocks/query/Edit.js:178
#: static/js/editor/blocks/tax-query/Edit.js:165
msgid "Limit"
msgstr "Giới hạn"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:876
msgid "Set how many terms you want to display in each attribute."
msgstr "Đặt số lượng điều khoản bạn muốn hiển thị trong mỗi thuộc tính."

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:179,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:462
msgid "Mixed Swatches"
msgstr "Mẫu màu hỗn hợp"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:233
msgid "Generate Variation URL"
msgstr "Tạo URL biến thể"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:239
msgid "Generate a shareable single product page URL with pre-selected variation attributes."
msgstr "Tạo URL trang sản phẩm đơn có thể chia sẻ với các thuộc tính biến thể được chọn trước."

#: static/js/options/ConditionsManager/SingleCondition.js:446
msgid "Display if query string is present in URL"
msgstr "Hiển thị nếu chuỗi truy vấn có trong URL"

#: static/js/options/DisplayCondition.js:62
msgid "Add Conditions"
msgstr "Thêm điều kiện"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:68
msgid "Upgrade to the agency plan and get instant access to this starter site and many other features."
msgstr "Nâng cấp lên gói đại lý và có quyền truy cập ngay vào mẫu web bắt đầu này cùng nhiều tính năng khác."

#: static/js/dashboard/screens/Extensions/CurrentExtension.js:316
msgid "Documentation"
msgstr "Tài liệu hướng dẫn"

#: static/js/dashboard/screens/Extensions/CurrentExtension.js:346
msgid "Manage"
msgstr "Quản lý"

#: static/js/dashboard/screens/Extensions/CurrentExtension.js:86
msgid "Video Tutorial"
msgstr "Video hướng dẫn"

#: static/js/options/ConditionsManager.js:207
msgid "Advanced Mode"
msgstr "Chế độ nâng cao"

#: static/js/options/ConditionsManager/PostIdPicker.js:78
msgid "Select product"
msgstr "Chọn sản phẩm"

#: static/js/options/ConditionsManager/ScheduleDate.js:149
msgid "Monday"
msgstr "Thứ hai"

#: static/js/options/ConditionsManager/ScheduleDate.js:154
msgid "Tuesday"
msgstr "Thứ ba"

#: static/js/options/ConditionsManager/ScheduleDate.js:159
msgid "Wednesday"
msgstr "Thứ tư"

#: static/js/options/ConditionsManager/ScheduleDate.js:164
msgid "Thursday"
msgstr "Thứ năm"

#: static/js/options/ConditionsManager/ScheduleDate.js:169
msgid "Friday"
msgstr "Thứ sáu"

#: static/js/options/ConditionsManager/ScheduleDate.js:174
msgid "Saturday"
msgstr "Thứ bảy"

#: static/js/options/ConditionsManager/ScheduleDate.js:179
msgid "Sunday"
msgstr "Chủ nhật"

#: static/js/options/ConditionsManager/ScheduleDate.js:21
msgid "Mon"
msgstr "T2"

#: static/js/options/ConditionsManager/ScheduleDate.js:211
msgid "Start Time"
msgstr "Thời gian bắt đầu"

#: static/js/options/ConditionsManager/ScheduleDate.js:22
msgid "Tue"
msgstr "T3"

#: static/js/options/ConditionsManager/ScheduleDate.js:23
msgid "Wed"
msgstr "T4"

#: static/js/options/ConditionsManager/ScheduleDate.js:234
msgid "Stop Time"
msgstr "Thời gian dừng"

#: static/js/options/ConditionsManager/ScheduleDate.js:24
msgid "Thu"
msgstr "T5"

#: static/js/options/ConditionsManager/ScheduleDate.js:25
msgid "Fri"
msgstr "T6"

#: static/js/options/ConditionsManager/ScheduleDate.js:26
msgid "Sat"
msgstr "T7"

#: static/js/options/ConditionsManager/ScheduleDate.js:27
msgid "Sun"
msgstr "CN"

#: static/js/options/ConditionsManager/ScheduleDate.js:58
msgid "Every day"
msgstr "Mỗi ngày"

#: static/js/options/ConditionsManager/ScheduleDate.js:66
msgid "Only weekends"
msgstr "Chỉ cuối tuần"

#: static/js/options/ConditionsManager/ScheduleDate.js:74
msgid "Only weekdays"
msgstr "Chỉ các ngày trong tuần"

#: static/js/options/ConditionsManager/SingleCondition.js:325
msgid "Select sub field"
msgstr "Chọn trường phụ"

#: static/js/options/ConditionsManager/SingleCondition.js:378
msgid "Display based on referer domain"
msgstr "Hiển thị dựa trên tên miền tham chiếu"

#: static/js/options/ConditionsManager/SingleCondition.js:412
msgid "Display if cookie is present"
msgstr "Hiển thị nếu có cookie"

#: static/js/dashboard/helpers/useUpsellModal.js:42
msgid "Upgrade to the agency plan and get instant access to this and many other features."
msgstr "Nâng cấp lên gói đại lý và có quyền truy cập ngay vào tính năng này cũng như nhiều tính năng khác."

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:53
msgid "This is a Pro starter site"
msgstr "Đây là mẫu web bắt đầu Pro"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:54
msgid "Upgrade to any pro plan and get instant access to this starter site and many other features."
msgstr "Nâng cấp lên bất kỳ gói pro nào và có quyền truy cập ngay vào mẫu web bắt đầu này cũng như nhiều tính năng khác."

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:61
msgid "Upgrade to the business or agency plan and get instant access to this starter site and many other features."
msgstr "Nâng cấp lên gói doanh nghiệp hoặc đại lý và nhận quyền truy cập ngay vào trang web mẫu này cùng với nhiều tính năng khác."

#: static/js/dashboard/helpers/useProExtensionInFree.js:14
msgid "This is a Pro extension"
msgstr "Đây là một tiện ích mở rộng Pro"

#: static/js/dashboard/helpers/useUpsellModal.js:103
#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:115
msgid "Agency"
msgstr "Đại lý"

#: static/js/dashboard/helpers/useUpsellModal.js:11
#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:14
msgid "Free"
msgstr "Miễn phí"

#: static/js/dashboard/helpers/useUpsellModal.js:122
msgid "Compare Plans"
msgstr "So sánh các kế hoạch"

#: static/js/dashboard/helpers/useUpsellModal.js:17
#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:107
msgid "Personal"
msgstr "Cá nhân"

#: static/js/dashboard/helpers/useUpsellModal.js:27
msgid "This is a Pro feature"
msgstr "Đây là một tính năng Pro"

#: static/js/dashboard/helpers/useUpsellModal.js:28
msgid "Upgrade to any pro plan and get instant access to this and many other feature."
msgstr "Nâng cấp lên bất kỳ gói Pro nào và có quyền truy cập ngay vào tính năng này cũng như nhiều tính năng khác."

#: static/js/dashboard/helpers/useUpsellModal.js:35
msgid "Upgrade to the business or agency plan and get instant access to this and many other features."
msgstr "Nâng cấp lên gói doanh nghiệp hoặc đại lý và nhận quyền truy cập ngay vào tính năng này cùng với nhiều tính năng khác."

#: static/js/dashboard/NoTheme.js:31
msgid "In order to take full advantage of all features it has to offer - please install and activate the Blocksy theme also."
msgstr "Để tận dụng tối đa tất cả các tính năng mà nó cung cấp - vui lòng cài đặt và kích hoạt giao diện Blocksy và Plugin Blocksy Companion."

#: static/js/dashboard/NoTheme.js:65
msgid "Install and activate the Blocksy theme"
msgstr "Cài đặt và kích hoạt Giao diện Blocksy"

#: static/js/dashboard/NoTheme.js:18
msgid "Action Required - Install Blocksy Theme"
msgstr "Yêu cầu hành động - Cài đặt giao diện Blocksy"

#: static/js/dashboard/NoTheme.js:24
msgid "Blocksy Companion is the complementary plugin to Blocksy theme. It adds a bunch of great features to the theme and acts as an unlocker for the Blocksy Pro package."
msgstr "Blocksy Companion là plugin bổ sung cho chủ đề Blocksy. Nó thêm một loạt các tính năng tuyệt vời vào chủ đề và hoạt động như một bộ mở khóa cho gói Blocksy Pro."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:423
msgid "Show Image Frame"
msgstr "Hiển thị khung hình ảnh"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:444
msgid "Show Label"
msgstr "Hiển thị nhãn"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:457
msgid "Show Counter"
msgstr "Hiển thị bộ đếm"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:164
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:82
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:526
msgid "Show Reset Button"
msgstr "Hiển thị nút đặt lại"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:168
msgid "Show or hide reset filter button."
msgstr "Hiển thị hoặc ẩn nút đặt lại bộ lọc."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:9
msgid "Shop Filters Controls"
msgstr "Kiểm soát bộ lọc cửa hàng"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:135
msgid "Shop Filters"
msgstr "Bộ lọc cửa hàng"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:10
msgid "Widget for filtering the WooCommerce products loop by category, attribute or brand."
msgstr "Tiện ích để lọc vòng lặp sản phẩm WooCommerce theo danh mục, thuộc tính hoặc thương hiệu."

#: framework/premium/static/js/blocks/ContentBlock.js:26
msgid "Insert a specific Content Block anywhere on the site."
msgstr "Chèn một Khối nội dung cụ thể ở bất kỳ đâu trên trang web."

#: framework/premium/static/js/hooks/CreateHook.js:156
msgid "Nothing Found Template"
msgstr "Không tìm thấy mẫu"

#: framework/premium/static/js/hooks/CreateHook.js:164
msgid "Maintenance Template"
msgstr "Mẫu bảo trì"

#: framework/premium/static/js/media-video/components/EditVideoMeta.js:44
msgid "Video Options"
msgstr "Tùy chọn Video"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:75
msgid "Display order overhiew section."
msgstr "Hiển thị phần tổng quan đơn hàng."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:90
msgid "Order Details"
msgstr "Chi tiết đơn hàng"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:94
msgid "Display order details section."
msgstr "Hiển thị phần chi tiết đơn hàng."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:177
msgid "Filter By"
msgstr "Lọc theo"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:92
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:221
msgid "Attribute"
msgstr "Thuộc tính"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:11
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:145
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:192
msgid "Display Type"
msgstr "Loại hiển thị"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:154
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:200
msgid "List"
msgstr "Danh sách"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:387
msgid "Image Aspect Ratio"
msgstr "Tỷ lệ khung hình ảnh"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:408
msgid "Image Max width"
msgstr "Chiều rộng tối đa của hình ảnh"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:233
msgid "Multiple Selections"
msgstr "Lựa chọn nhiều"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:237
msgid "Allow selecting multiple items in a filter."
msgstr "Cho phép chọn nhiều mục trong một bộ lọc."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:118
msgid "Select attribute"
msgstr "Chọn thuộc tính"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:305
msgid "Show Hierarchy"
msgstr "Hiển thị hệ thống phân cấp"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:321
msgid "Expandable"
msgstr "Có thể mở rộng"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:336
#: static/js/editor/blocks/widgets-wrapper/Edit.js:95
msgid "Expanded by Default"
msgstr "Mở rộng theo mặc định"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:283
msgid "Show Checkboxes"
msgstr "Hiển thị hộp kiểm"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:364
msgid "Show Brands Images"
msgstr "Hiển thị ảnh thương hiệu"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:368
msgid "Show Swatches"
msgstr "Hiển thị mẫu"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/VariableTab.js:30
msgid "Upload only the %s.woff2%s or %s.ttf%s font file formats. Don't convert variable fonts by yourself. Ask the font provider to hand a correct file or the %svariable%s font won't work."
msgstr "Chỉ tải lên định dạng tệp phông chữ %s.woff2%s hoặc %s.ttf%s. Đừng tự mình chuyển đổi các phông chữ khác nhau. Hãy yêu cầu nhà cung cấp phông chữ cung cấp tệp chính xác, nếu không phông chữ %svariable%s sẽ không hoạt động."

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/AdvancedTab.js:54
msgid "Preload Subsets"
msgstr "Tải trước tập hợp con"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/GeneralTab.js:10
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:65
msgid "Select Variations"
msgstr "Chọn biến thể"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:255
msgid "Local Google Fonts Settings"
msgstr "Cài đặt phông chữ Google cục bộ"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:267
msgid "Select font"
msgstr "Chọn phông chữ"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:335
msgid "Download Font"
msgstr "Tải phông chữ"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:217
msgid "Save Font"
msgstr "Lưu phông chữ"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:608
msgid "Companion Plugin Details"
msgstr "Chi tiết plugin Companion"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Disabled.js:7
msgid "Please select a valid attribute."
msgstr "Vui lòng chọn một thuộc tính hợp lệ."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:41
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:130
msgid "Exclude %s"
msgstr "Loại trừ %s"

#: framework/features/conditions/rules/woo.php:35
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:31
msgid "Product Attributes"
msgstr "Thuộc tính sản phẩm"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/CustomerDetails.js:10
msgid "Billing address"
msgstr "Địa chỉ thanh toán"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/CustomerDetails.js:34
msgid "Shipping address"
msgstr "Địa chỉ giao hàng"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:64
msgid "Subtotal"
msgstr "Tổng phụ"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:77
msgid "Shipping"
msgstr "Vận chuyển"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:79
msgid "Free shipping"
msgstr "Miễn phí vận chuyển"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:83
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:42
msgid "Payment method"
msgstr "Phương thức thanh toán"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:85
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:43
msgid "Cash on delivery"
msgstr "Thanh toán khi nhận hàng"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:88
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:28
msgid "Total"
msgstr "Tổng cộng"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:8
msgid "Order number"
msgstr "Số đơn hàng"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:108
msgid "Customer Details"
msgstr "Chi tiết khách hàng"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:112
msgid "Display customer details section."
msgstr "Hiển thị phần chi tiết khách hàng."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:71
msgid "Order Overview"
msgstr "Tổng quan đơn hàng"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:89
msgid "Once you insert your %sProject ID%s and click the \"Fetch Fonts\" button, your fonts will become available in all theme’s typography options."
msgstr "Sau khi bạn chèn %sID Dự an%s và nhấp vào nút \"Tìm nạp phông chữ\", phông chữ của bạn sẽ có sẵn trong tất cả các tùy chọn kiểu chữ của giao diện."

#: framework/premium/extensions/custom-fonts/dashboard-static/js/CustomFonts.js:135
#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:320
msgid "Upload Font"
msgstr "Tải lên phông chữ"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/PreloadTab.js:14
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/AdvancedTab.js:19
msgid "Preload Variations"
msgstr "Tải trước các biến thể"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:356
msgid "Simple Font"
msgstr "Phông chữ đơn giản"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:360
msgid "Variable Font"
msgstr "Phông chữ Biến đổi"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:364
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:116
msgid "Preload"
msgstr "Tải trước"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:174
#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:20
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:342
msgid "Available Fonts"
msgstr "Phông chữ khả dụng"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:322
msgid "More information on how to generate an API key for Campaign Monitor can be found %shere%s."
msgstr "Bạn có thể tìm thêm thông tin về cách tạo API key cho Giám sát chiến dịch %stại đây%s."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:83
msgid "Connect Newsletter Provider"
msgstr "Kết nối nhà cung cấp bản tin"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:96
msgid "Provider"
msgstr "Các nhà cung cấp"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/ListPicker.js:91
msgid "Fetching..."
msgstr "Đang tìm nạp..."

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:164
msgid "Please enter a valid Project ID to get all fonts."
msgstr "Vui lòng nhập ID Dự Án hợp lệ để nhận tất cả các font."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:254
msgid "This provider is used only for testing purposes. It doesnt register any real subscribers."
msgstr "Nhà cung cấp này chỉ được sử dụng cho mục đích thử nghiệm. Nó không đăng ký bất kỳ người đăng ký thực sự nào."

#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:486,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:520,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:555,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:589,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:428,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:463,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:494,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:524
msgid "Badge Color"
msgstr "Màu huy hiệu"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:221
#: static/js/editor/blocks/search/Edit.js:281
msgid "Button Text Color"
msgstr "Màu văn bản nút"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/index.js:17
msgid "Newsletter Controls"
msgstr "Kiểm soát bản tin"

#: framework/features/blocks/search/options.php:126,
#: framework/premium/features/premium-header/items/search-input/options.php:119
msgid "Live Results Product Status"
msgstr "Kết quả trực tiếp Trạng thái sản phẩm"

#: framework/features/blocks/search/options.php:138,
#: framework/premium/features/premium-header/items/search-input/options.php:132
msgid "Taxonomy Filter"
msgstr "Bộ lọc phân loại"

#: framework/features/blocks/search/options.php:156,
#: framework/premium/features/premium-header/items/search-input/options.php:144
msgid "Filter Visibility"
msgstr "Hiển thị bộ lọc"

#: framework/premium/features/premium-header/items/search-input/options.php:795
msgid "Input Border Radius"
msgstr "Bán kính viền đầu vào"

#: framework/premium/features/premium-header/items/search-input/options.php:823
msgid "Dropdown Font"
msgstr "Phông chữ Menu thả xuống"

#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:604
msgid "You don't have any products in your compare list yet."
msgstr "Bạn chưa có sản phẩm nào trong danh sách so sánh."

#: framework/features/blocks/dynamic-data/views/woo-field.php:25,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:481
#: static/js/editor/blocks/dynamic-data/preview-parts/woo/StockPreview.js:6
msgid "In Stock"
msgstr "Trong kho"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:12,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:37,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/inline-filter.php:78,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/list-filter.php:109
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:198
msgid "Active Filters"
msgstr "Bộ lọc đang hoạt động"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:22
msgid "Active Filters Label"
msgstr "Nhãn bộ lọc đang hoạt động"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/inline-filter.php:26,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/list-filter.php:31
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:32
msgid "Reset Filters"
msgstr "Đặt lại các bộ lọc"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/filter-presenter.php:149,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/price-filter.php:188
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/Preview.js:59
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Preview.js:183
msgid "Reset Filter"
msgstr "Đặt lại bộ lọc"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:728,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:24,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:46,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:142
msgid "Color"
msgstr "Màu sắc"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:78
msgid "Short Name"
msgstr "Tên ngắn"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:245
msgid "Sorry, this product is unavailable. Please choose a different combination."
msgstr "Xin l���i, sản phẩm này không có sẵn. Vui lòng chọn một sự kết hợp khác."

#: framework/features/blocks/dynamic-data/views/woo-field.php:26,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:246,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:481,
#: framework/premium/extensions/woocommerce-extra/features/swatches/includes/swatch-element-render.php:47,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:57
#: static/js/editor/blocks/dynamic-data/preview-parts/woo/StockPreview.js:7
msgid "Out of Stock"
msgstr "Hết hàng"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:540
msgid "Display Variations Inline"
msgstr "Hiển thị biến thể nội tuyến"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:636,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:736,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:833
msgid "Swatches"
msgstr "Mẫu"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:16,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:250
msgid "Color Swatches"
msgstr "Mẫu màu"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:20,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:74,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:129,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:183
msgid "Swatch Shape"
msgstr "Hình dạng mẫu"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:26,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:80,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:135,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:189
msgid "Round"
msgstr "Tròn"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:87,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:142,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:196
msgid "Single Page Swatch Size"
msgstr "Kích thước mẫu trang đơn"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:42,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:96,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:151,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:205
msgid "Widget Swatch Size"
msgstr "Kích thước mẫu màu của tiện ích"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:111,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:166,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:220
msgid "Archive Cards Swatch Size"
msgstr "Lưu trữ kích thước mẫu thẻ"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:70,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:294
msgid "Image Swatches"
msgstr "Mẫu hình ảnh"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:125,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:338
msgid "Button Swatches"
msgstr "Mẫu nút"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:395
msgid "Wishlist Button"
msgstr "Nút Yêu thích"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:58
msgid "Specific Product Variation "
msgstr "Biến thể sản phẩm cụ thể "

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:61
msgid "This option will allow you to add a speciffic product variation to wishlist."
msgstr "Tùy chọn này sẽ cho phép bạn thêm một biến thể sản phẩm cụ thể vào danh sách yêu thích."

#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:601,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/views/table-no-results.php:3,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:19
msgid "Browse products"
msgstr "Duyệt sản phẩm"

#: framework/features/blocks/contact-info/options.php:533,
#: framework/premium/features/premium-header/items/contacts/options.php:417
msgid "Link Icons"
msgstr "Biểu tượng liên kết"

#: framework/premium/features/premium-header/items/contacts/view.php:59
msgid "Download"
msgstr "Tải về"

#: framework/premium/features/premium-header/items/divider/options.php:22,
#: framework/premium/features/premium-header/items/divider/options.php:51,
#: framework/premium/features/premium-header/items/divider/options.php:66,
#: framework/premium/features/premium-header/items/divider/options.php:81
msgid "Style & Color"
msgstr "Kiểu & Màu sắc"

#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:87
msgid "Dropdown Arrow"
msgstr "Mũi tên Menu thả xuống"

#: framework/premium/features/premium-header/items/menu-tertiary/config.php:4
msgid "Menu 3"
msgstr "Menu 3"

#: framework/features/blocks/search/options.php:119,
#: framework/premium/features/premium-header/items/search-input/options.php:111
msgid "Live Results Product Price"
msgstr "Kết quả trực tiếp Giá sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:274
msgid "Add New Size Guide"
msgstr "Thêm hướng dẫn kích thước"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:275
msgid "Edit Size Guide"
msgstr "Sửa hướng dẫn kích thước"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:276
msgid "New Size Guide"
msgstr "Hướng dẫn kích thước mới"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:278
msgid "View Size Guide"
msgstr "Xem Hướng dẫn Kích thước"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:279
msgid "Search Size Guides"
msgstr "Tìm kiếm hướng dẫn kích thước"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/modal-view.php:25,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/side-view.php:34
msgid "Close Sizes Modal"
msgstr "Kích thước nút đóng hộp bật lên"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:29
msgid "Size Guide Display Conditions"
msgstr "Điều kiện hiển thị hướng dẫn kích thước"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:30
msgid "Choose where you want this size guide to be displayed."
msgstr "Chọn nơi bạn muốn hiển thị hướng dẫn kích thước này."

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:243
msgid "Sorry, no products matched your selection. Please choose a different combination."
msgstr "Rất tiếc, không có sản phẩm nào phù hợp với lựa chọn của bạn. Vui lòng chọn một sự kết hợp khác."

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:244
msgid "Please select some product options before adding this product to your cart."
msgstr "Vui lòng chọn một số tùy chọn sản phẩm trước khi thêm sản phẩm này vào giỏ hàng của bạn."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:76
msgid "Amount the client has to reach in order to get free shipping."
msgstr "Số tiền mà khách hàng phải đạt được để được giao hàng miễn phí."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:104
msgid "The calculation method will be based on WooCommerce zones."
msgstr "Phương pháp tính toán sẽ dựa trên các khu vực WooCommerce."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:119
msgid "Discount Calculation"
msgstr "Tính toán giảm giá"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:129
msgid "Include or exclude the discount code when calculating the shipping progress."
msgstr "Bao gồm hoặc loại trừ mã giảm giá khi tính toán tiến trình vận chuyển."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:133,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:158
msgid "Default Message"
msgstr "Thông điệp mặc định"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:143
msgid "You can use dynamic code tags such as {price} inside this option."
msgstr "Bạn có thể sử dụng các thẻ mã động như {price} bên trong tùy chọn này."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:174
msgid "Success Message"
msgstr "Thông báo thành công"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:5,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:271,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:277
msgid "Size Guides"
msgstr "Hướng dẫn kích thước"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:16
msgid "Size Guide Placement"
msgstr "Vị trí hướng dẫn kích thước"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:23
msgid "Side Panel"
msgstr "Bảng điều khiển bên"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:33
msgid "Reveal From"
msgstr "Tiết lộ từ"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:303,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:30
msgid "Columns & Products"
msgstr "Cột & Sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:330,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:57
msgid "Number of products"
msgstr "Số sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:32
msgid "Share Box Icons Color"
msgstr "Màu biểu tượng hộp chia sẻ"

#: framework/helpers/exts-configs.php:330,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:180,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:4
msgid "Free Shipping Bar"
msgstr "Thanh vận chuyển miễn phí"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:183
msgid "Show if cart is empty"
msgstr "Hiển thị nếu giỏ hàng trống"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:382,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:176
msgid "Congratulations! You got free shipping 🎉"
msgstr "Chúc mừng! Bạn đã nhận được miễn phí vận chuyển 🎉"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:359,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:135
msgid "Add {price} more to get free shipping!"
msgstr "Thêm {price} nữa để được giao hàng miễn phí!"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:15
msgid "Show In Cart Page"
msgstr "Hiển thị trên trang giỏ hàng"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:21
msgid "Show In Checkout Page"
msgstr "Hiển thị trên trang thanh toán"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:27
msgid "Show In Mini Cart"
msgstr "Hiển thị trong giỏ hàng nhỏ"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:34
msgid "Calculation Method"
msgstr "Phương pháp tính toán"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:69
msgid "Goal Amount"
msgstr "Số tiền mục tiêu"

#: framework/premium/extensions/woocommerce-extra/features/filters/feature.php:126
msgid "Automatically scroll page to top after user interaction."
msgstr "Tự động cuộn trang lên đầu sau khi người dùng tương tác."

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-cart/feature.php:167
msgid "Shopping Cart"
msgstr "Giỏ hàng"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-cart/feature.php:169
msgid "Close cart drawer"
msgstr "Đóng ngăn kéo giỏ hàng"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/feature.php:152,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:525
msgid "WooCommerce Filters Canvas"
msgstr "Bộ lọc Canvas WooCommerce"

#: framework/premium/extensions/shortcuts/customizer.php:477,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:4
msgid "Filters Canvas"
msgstr "Bộ lọc Canvas"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:177
msgid "Panel Height"
msgstr "Chiều cao bảng điều khiển"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:187
msgid "Auto"
msgstr "Tự động"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:198
msgid "Custom Height"
msgstr "Chiều cao tùy chỉnh"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:216
msgid "Panel Columns"
msgstr "Cột bảng điều khiển"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:279,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:100
msgid "Panel Backdrop"
msgstr "Phông nền Bảng điều khiển"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:516
msgid "Widget Area Source"
msgstr "Nguồn khu vực tiện ích"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:524
msgid "WooCommerce Sidebar"
msgstr "Thanh bên WooCommerce"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:110,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:242
msgid "Days"
msgstr "Ngày"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:111,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:256
msgid "Hours"
msgstr "Giờ"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:115,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:270
msgid "Min"
msgstr "Phút"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:116,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:284
msgid "Sec"
msgstr "Giây"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:298,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:330
msgid "Hurry up! This sale ends in"
msgstr "Nhanh lên! Đợt bán hàng này kết thúc trong"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:324
msgid "Countdown Box"
msgstr "Hộp đếm ngược"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/helpers.php:36
msgid "Quick view"
msgstr "Xem nhanh"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:19
msgid "Additional Actions"
msgstr "Hành động bổ sung"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:25
msgid "Buttons Type"
msgstr "Loại nút"

#: framework/extensions/trending/customizer.php:449,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:80,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:727
msgid "Button"
msgstr "Nút bấm"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:72
msgid "Modal Trigger"
msgstr "Kích hoạt hộp bật lên"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:82
msgid "Card"
msgstr "Thẻ"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:91
msgid "Modal Width"
msgstr "Độ rộng hộp bật lên"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:102
msgid "Product Navigation"
msgstr "Điều hướng Sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:108
msgid "Display next/previous buttons that will help to easily navigate through products."
msgstr "Hiển thị các nút tiếp theo/trước đó sẽ giúp dễ dàng điều hướng qua các sản phẩm."

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:175,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:412,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:130,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:138
msgid "Title Font"
msgstr "Phông chữ tiêu đề"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:210,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:442,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:168
msgid "Price Font"
msgstr "Phông chữ giá"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:219,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:450,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:176
msgid "Price Color"
msgstr "Màu giá"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:219
msgid "Add To Cart Button"
msgstr "Nút thêm vào giỏ hàng"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:291
msgid "View Cart Button"
msgstr "Nút xem giỏ hàng"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:361
msgid "Product Page Button"
msgstr "Nút trang sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:46
msgid "How many days the products will be marked as \"New\" after creation."
msgstr "Bao nhiêu ngày sản phẩm sẽ được đánh dấu là \"Mới\" sau khi tạo."

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:136,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:142
msgid "Product Tabs"
msgstr "Tab sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:137
msgid "Product Tab"
msgstr "Tab sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:139
msgid "Add New Product Tab"
msgstr "Thêm tab sản phẩm mới"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:140
msgid "Edit Product Tab"
msgstr "Chỉnh sửa tab sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:141
msgid "New Product Tab"
msgstr "Thêm tab sản phẩm mới"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:143
msgid "View Product Tab"
msgstr "Xem tab sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:144
msgid "Search Product Tabs"
msgstr "Tìm kiếm tab sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:10
msgid "Product Tab Display Conditions"
msgstr "Điều kiện hiển thị Tab sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:11
msgid "Choose where you want this product tab to be displayed."
msgstr "Chọn nơi bạn muốn hiển thị tab sản phẩm này."

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:27
msgid "Tab Order"
msgstr "Thứ tự tab"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:32
msgid "Default tabs order: Description - 10, Additional Information - 20, Reviews - 30."
msgstr "Thứ tự các tab mặc định: Mô tả - 10, Thông tin bổ sung - 20, Đánh giá - 30."

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:106
msgid "Payment Gateways"
msgstr "Các cổng thanh Toán"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:112,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:65
msgid "Shipping Methods"
msgstr "Phương thức vận chuyển"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:265
msgid "Thank you Page"
msgstr "Trang cảm ơn"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:480,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:486
msgid "Thank You Pages"
msgstr "Trang cảm ơn"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:481
msgid "Thank You Page"
msgstr "Trang cảm ơn"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:483
msgid "Add New Thank You Page"
msgstr "Thêm trang cảm ơn mới"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:484
msgid "Edit Thank You Page"
msgstr "Sửa trang cảm ơn"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:485
msgid "New Thank You Page"
msgstr "Trang cảm ơn mới"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:487
msgid "View Thank You Page"
msgstr "Xem Trang Cảm ơn"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:488
msgid "Search Thank You Pages"
msgstr "Tìm kiếm trang cảm ơn"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:78
msgid "Payment Gateway"
msgstr "Cổng Thanh Toán"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:106
msgid "Priority"
msgstr "Ưu Tiên"

#: framework/premium/extensions/woocommerce-extra/features/cart-page/options.php:5
msgid "Coupon Form"
msgstr "Biểu mẫu mã giảm giá"

#: framework/premium/extensions/woocommerce-extra/features/filters/feature.php:119
msgid "AJAX Filtering"
msgstr "Lọc AJAX"

#: framework/premium/extensions/woocommerce-extra/features/filters/feature.php:125
msgid "Scroll to Top"
msgstr "Cuộn lên đầu"

#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:276
msgid "Compare Products"
msgstr "So sánh sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:282
msgid "Close Compare Modal"
msgstr "Đóng hộp bật lên so sánh"

#: framework/premium/extensions/woocommerce-extra/features/compare/helpers.php:93,
#: framework/premium/extensions/woocommerce-extra/features/compare/helpers.php:152
msgid "Add to compare"
msgstr "Thêm vào so sánh"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:46
msgid "Compare Placement"
msgstr "Vị trí so sánh"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:53
#: static/js/editor/blocks/breadcrumbs/Preview.js:46
msgid "Page"
msgstr "Trang"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:62
msgid "Select Page"
msgstr "Chọn trang"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:69
msgid "Select a page where the compare table will be outputted."
msgstr "Chọn một trang nơi bảng so sánh sẽ được xuất ra."

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:75
msgid "Compare Table Fields"
msgstr "So sánh các trường bảng"

#: framework/features/blocks/dynamic-data/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:162
msgid "Length"
msgstr "Độ dài"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:130,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:186
msgid "Attributes"
msgstr "Các thuộc tính"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:266,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:470
msgid "Availability"
msgstr "Khả dụng"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:469
msgid "Modal Border Radius"
msgstr "Bo viền hộp bật lên"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:366
msgid "Compare Bar"
msgstr "Thanh so sánh"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:394
msgid "Button Icon"
msgstr "Biểu tượng nút"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:449
msgid "Compare Bar Display Conditions"
msgstr "Điều kiện hiển thị thanh so sánh"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:450
msgid "Add one or more conditions to display the Compare bar."
msgstr "Thêm một hoặc nhiều điều kiện để hiển thị thanh so sánh."

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:463,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:223,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:295,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:365
msgid "Button Font Color"
msgstr "Màu phông chữ nút"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:494,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:253,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:325,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:393
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:250
#: static/js/editor/blocks/search/Edit.js:311
msgid "Button Background Color"
msgstr "Màu nền nút"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:15,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:5
msgid "New Badge"
msgstr "Huy hiệu mới"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:55,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:53
msgid "Featured Badge"
msgstr "Huy hiệu nổi bật"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/feature.php:168,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:82
msgid "HOT"
msgstr "HOT"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/feature.php:202,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:35
msgid "NEW"
msgstr "MỚI"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:32,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:79
msgid "Badge Label"
msgstr "Nhãn huy hiệu"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:40
msgid "Label Duration"
msgstr "Thời lượng nhãn"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:57
msgid "Allow users to upload images when leaving a review."
msgstr "Cho phép người dùng tải lên hình ảnh, khi để lại đánh giá."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:73
msgid "Image Lightbox"
msgstr "Lightbox hình ảnh"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:74
msgid "Allow users to open attached review images in lightbox."
msgstr "Cho phép người dùng mở hình ảnh đánh giá đính kèm trong lightbox."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:88
msgid "Review Voting"
msgstr "Bình chọn đánh giá"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:89
msgid "Allow users to upvote reviews."
msgstr "Cho phép người dùng bình chọn cho đánh giá."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:105
msgid "Allowed Users"
msgstr "Người dùng được phép"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:106
msgid "Set which users are allowed to vote."
msgstr "Đặt người dùng nào được phép bình chọn."

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:5
msgid "Affiliate Products"
msgstr "Sản phẩm Affiliate"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:12
msgid "Product Archive"
msgstr "Lưu Trữ Sản Phẩm"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:16,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:70
msgid "Image Affiliate Link"
msgstr "Liên kết hình ảnh Affiliate"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:27,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:48,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:81
msgid "Open In New Tab"
msgstr "Mở trong tab mới"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:36
msgid "Title Affiliate Link"
msgstr "Tiêu đề liên kết Affiliate"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:90
msgid "Open Button Link In New Tab"
msgstr "Mở liên kết nút trong tab mới"

#: framework/premium/extensions/woocommerce-extra/features/cart-page/options.php:14
msgid "Quantity Auto Update"
msgstr "Cập nhật tự động số lượng"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:37
msgid "Product brands base"
msgstr "Cơ sở thương hiệu sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:49
msgid "brand"
msgstr "thương hiệu"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:500,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:532
msgid "About Brands"
msgstr "Sau Thương hiệu"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:501,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:534
msgid "About %s"
msgstr "Giới thiệu về %s"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:558,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:578,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:640,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:707,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:718,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:493
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:146
msgid "Brands"
msgstr "Thương hiệu"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:562,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:150,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:177,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:221,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:239,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:257,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:275
msgid "Sticky Row"
msgstr "Hàng ghim"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:593,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:644
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:257
msgid "Logo Size"
msgstr "Kích thước Logo"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:605,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:656
msgid "Logos Gap"
msgstr "Khoảng cách Logo"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:708
msgid "Brand"
msgstr "Thương hiệu"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:709
msgid "Search Brands"
msgstr "Tìm kiếm thương hiệu"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:710
msgid "All Brands"
msgstr "Tất cả thương hiệu"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:711
msgid "Parent Brand"
msgstr "Thương hiệu cha"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:712
msgid "Parent Brand:"
msgstr "Thương hiệu cha:"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:713
msgid "View Brand"
msgstr "Xem thương hiệu"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:714
msgid "Edit Brand"
msgstr "Chỉnh sửa thương hiệu"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:715
msgid "Update Brand"
msgstr "Cập nhật Thương hiệu"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:716
msgid "Add New Brand"
msgstr "Thêm thương hiệu mới"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:717
msgid "New Brand Name"
msgstr "Tên thương hiệu mới"

#: framework/premium/extensions/woocommerce-extra/features/brands/options.php:5
msgid "Product Brand Tab"
msgstr "Tab thương hiệu sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/brands/options.php:20
msgid "Brand Name In Tab Title"
msgstr "Tên thương hiệu trong tiêu đề tab"

#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:5
msgid "Product Image"
msgstr "Hình ảnh sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:54
msgid "Quantity Input"
msgstr "Nhập số lượng"

#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:228
msgid "Compare Button"
msgstr "Nút so sánh"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:139
msgid "Text Hover"
msgstr "Di chuột qua văn bản"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:144
msgid "Background Initial"
msgstr "Nền ban đầu"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:376
msgid "Star"
msgstr "Ngôi sao"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:376
msgid "Stars"
msgstr "Ngôi sao"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:442
msgid "%s%% of customers recommend this product."
msgstr "%s%% khách hàng giới thiệu sản phẩm này"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:604
msgid "%s of %s found this review helpful"
msgstr "%s trong số %s thấy đánh giá này hữu ích"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:666,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:44
msgid "Review Title"
msgstr "Tiêu đề đánh giá"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:694
msgid "Upload Image (Optional)"
msgstr "Tải hình ảnh lên (tùy chọn)"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:11
msgid "Reviews Order"
msgstr "Thứ tự đánh giá"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:18
msgid "Oldest First"
msgstr "Cũ nhất trước"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:19
msgid "Newest First"
msgstr "Mới nhất trước"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:20
msgid "Low Rating First"
msgstr "Đánh giá thấp đầu tiên"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:21
msgid "High Rating First"
msgstr "Đánh giá cao đầu tiên"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:22
msgid "Most Relevant"
msgstr "Phù hợp nhất"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:32
msgid "Average Score"
msgstr "Điểm trung bình"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:33
msgid "Display an average score for all reviews."
msgstr "Hiển thị điểm trung bình cho tất cả các đánh giá."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:45
msgid "Allow users to add a title when leaving a review."
msgstr "Cho phép người dùng thêm tiêu đề khi để lại đánh giá."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:56
msgid "Image Upload"
msgstr "Tải lên hình ảnh"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/view.php:78
msgid "Color mode switch"
msgstr "Chuyển đổi chế độ màu"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:92
msgid "Items Counter"
msgstr "Bộ đếm mục"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:20,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:13
msgid "Slider"
msgstr "Thanh trượt"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:41
msgid "Columns & Posts"
msgstr "Cột & Bài viết"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:49,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:311,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:38
msgid "Number of columns"
msgstr "Số cột"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:68
msgid "Number of posts"
msgstr "Số bài viết"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:91,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:344,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:75
msgid "Autoplay"
msgstr "Tự động phát"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:112,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:356,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:91
msgid "Delay (Seconds)"
msgstr "Độ trễ (giây)"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:113,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:357,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:92
msgid "Specify the amount of time (in seconds) to delay between automatically cycling an item."
msgstr "Chỉ định lượng thời gian (tính bằng giây) để trì hoãn giữa việc tự động chuyển một mục."

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:140
msgid "Choose the default color mode that a user will see when it visits your site."
msgstr "Chọn chế độ màu mặc định mà người dùng sẽ thấy khi truy cập trang web của bạn."

#: framework/premium/features/content-blocks/options/popup.php:577
msgid "Enable this option if you want to lock the page scroll while the popup is triggered."
msgstr "Bật tùy chọn này nếu bạn muốn khóa cuộn trang trong khi popup lên được kích hoạt."

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/config.php:4
msgid "Color Switch"
msgstr "Chuyển đổi màu"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:39
msgid "Reverse Icon State"
msgstr "Đảo ngược trạng thái biểu tượng"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:102
msgid "Dark Mode Label"
msgstr "Nhãn chế độ tối"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:114
msgid "Light Mode Label"
msgstr "Nhãn chế độ sáng"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:118,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/view.php:54
msgid "Light Mode"
msgstr "Chuyển chế độ sáng"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:128
msgid "Default Color Mode"
msgstr "Chế độ màu mặc định"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:136
msgid "Light"
msgstr "Sáng"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:137
msgid "Dark"
msgstr "Tối"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:138
msgid "OS Aware"
msgstr "Theo hệ điều hành"

#: framework/premium/features/content-blocks/options/popup.php:326
msgid "Set after how many days the popup will relaunch if the additional close trigger is met."
msgstr "Đặt sau bao nhiêu ngày, popup sẽ khởi chạy lại nếu điều kiện kích hoạt đóng thêm được đáp ứng."

#: framework/premium/features/content-blocks/options/popup.php:446
msgid "Load Content With AJAX"
msgstr "Tải nội dung với AJAX"

#: framework/premium/features/content-blocks/options/popup.php:450
msgid "Enable this option if you want to load the popup content using AJAX."
msgstr "Kích hoạt tùy chọn này nếu bạn muốn tải nội dung popup bằng AJAX."

#: framework/premium/features/content-blocks/options/popup.php:459
msgid "Reload Content"
msgstr "Tải lại nội dung"

#: framework/premium/features/content-blocks/options/popup.php:466
#: static/js/options/ConditionsManager/ScheduleDate.js:78
msgid "Never"
msgstr "Không bao giờ"

#: framework/premium/features/content-blocks/options/popup.php:467
msgid "Always"
msgstr "Luôn luôn"

#: framework/premium/features/content-blocks/options/popup.php:469
msgid "Set this option to always if you have dynamic content inside the popup in order to keep everything up to date."
msgstr "Đặt tùy chọn này thành luôn luôn nếu bạn có nội dung động bên trong popup để luôn cập nhật mọi thứ."

#: framework/premium/features/content-blocks/options/popup.php:495,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:637
msgid "Popup Visibility"
msgstr "Khả năng hiển thị popup"

#: framework/premium/features/content-blocks/options/popup.php:573
msgid "Scroll Lock"
msgstr "Khóa cuộn"

#: framework/premium/features/content-blocks/options/popup.php:250
msgid "Set the button class selector that will trigger popup to close."
msgstr "Đặt bộ chọn lớp nút sẽ kích hoạt popup để đóng."

#: framework/premium/features/content-blocks/options/popup.php:280
msgid "Relaunch Trigger"
msgstr "Kích hoạt chạy lại"

#: framework/premium/features/content-blocks/options/popup.php:286
msgid "Never relaunch"
msgstr "Không bao giờ chạy lại"

#: framework/premium/features/content-blocks/options/popup.php:297
msgid "Days After Close"
msgstr "Ngày sau khi đóng"

#: framework/premium/features/content-blocks/options/popup.php:303
msgid "Set after how many days the popup will relaunch."
msgstr "Đặt sau bao nhiêu ngày popup sẽ khởi chạy lại."

#: framework/premium/features/content-blocks/options/popup.php:313
msgid "Days After Form Submit"
msgstr "Ngày sau khi gửi biểu mẫu"

#: framework/premium/features/content-blocks/options/popup.php:317
msgid "Days After Button Click"
msgstr "Ngày sau khi nhấp vào nút"

#: framework/premium/features/content-blocks/options/maintenance.php:16
msgid "Add one or more conditions to display the Maintenance block."
msgstr "Thêm một hoặc nhiều điều kiện để hiển thị khối bảo trì."

#: framework/premium/features/content-blocks/options/popup.php:53
msgid "Popup Display Conditions"
msgstr "Điều Kiện Hiển Thị Popup"

#: framework/premium/features/content-blocks/options/popup.php:54
msgid "Choose where you want this popup to be displayed."
msgstr "Chọn nơi bạn muốn hiển thị popup này."

#: framework/premium/features/content-blocks/options/popup.php:78
msgid "On element click"
msgstr "Khi nhấp vào phần tử"

#: framework/premium/features/content-blocks/options/popup.php:153
msgid "Close Popup On Scroll Back"
msgstr "Đóng Popup khi cuộn lại"

#: framework/premium/features/content-blocks/options/popup.php:216
msgid "Additional Close Trigger"
msgstr "Kích hoạt đóng bổ sung"

#: framework/premium/features/content-blocks/options/popup.php:223
msgid "On form submit"
msgstr "Khi gửi biểu mẫu"

#: framework/premium/features/content-blocks/options/popup.php:224
msgid "On button click"
msgstr "Khi nhấp vào nút"

#: framework/premium/features/content-blocks/options/popup.php:235
msgid "The popup will auto-close if a form submit action is detected inside of it."
msgstr "Popup sẽ tự động đóng nếu phát hiện hành động gửi biểu mẫu bên trong nó."

#: framework/premium/features/content-blocks/options/popup.php:247
msgid "Button Class Selector"
msgstr "Bộ chọn lớp nút"

#: framework/premium/features/content-blocks/options/hook.php:41,
#: framework/premium/features/content-blocks/options/nothing_found.php:35
msgid "Choose where you want this content block to be displayed."
msgstr "Chọn nơi bạn muốn hiển thị khối nội dung này."

#: framework/premium/features/content-blocks/options/hook.php:283
msgid "Select a post/page to preview it's content inside the editor while building the hook."
msgstr "Chọn một bài viết/trang để xem trước nội dung của nó bên trong trình chỉnh sửa trong khi tạo hook."

#: framework/premium/features/content-blocks/options/maintenance.php:15
msgid "Maintenance Block Display Conditions"
msgstr "Điều kiện hiển thị khối bảo trì"

#: framework/premium/features/media-video/options.php:110
msgid "Display a minimalistic view of the video player."
msgstr "Hiển thị giao diện tối giản của trình phát video."

#: framework/premium/features/performance-typography/feature.php:116
msgid "Preconnect Google Fonts"
msgstr "Kết nối trước Google Fonts"

#: framework/premium/features/performance-typography/feature.php:127
msgid "Preconnect Adobe Typekit Fonts"
msgstr "Kết nối trước với phông chữ Adobe Typekit"

#: framework/features/header/items/account/config.php:4
msgid "Account"
msgstr "Tài khoản"

#: framework/features/header/items/account/options.php:12
msgid "User Info"
msgstr "Thông tin Người dùng"

#: framework/features/header/items/account/options.php:60
msgid "User Avatar"
msgstr "Hình đại diện người dùng"

#: framework/features/header/items/account/options.php:91,
#: framework/features/header/items/account/options.php:658,
#: framework/features/header/items/account/views/login.php:164,
#: framework/features/header/items/account/views/login.php:364
msgid "Edit Profile"
msgstr "Sửa hồ sơ"

#: framework/features/header/items/account/options.php:101,
#: framework/features/header/items/account/options.php:105,
#: framework/features/header/items/account/options.php:667,
#: framework/features/header/items/account/views/login.php:170,
#: framework/features/header/items/account/views/login.php:385
msgid "Log Out"
msgstr "Đăng xuất"

#: framework/features/header/items/account/options.php:185,
#: framework/features/header/items/account/options.php:189,
#: framework/features/header/items/account/views/login.php:591
msgid "Dokan Dashboard"
msgstr "Bảng điều khiển Dokan"

#: framework/features/header/items/account/options.php:199,
#: framework/features/header/items/account/options.php:203,
#: framework/features/header/items/account/views/login.php:625
msgid "Dokan Shop"
msgstr "Dokan Shop"

#: framework/features/header/items/account/options.php:215,
#: framework/features/header/items/account/options.php:219,
#: framework/features/header/items/account/views/login.php:657
msgid "Tutor LMS Dashboard"
msgstr "Bảng điều khiển Tutor LMS"

#: framework/features/header/items/account/options.php:235,
#: framework/features/header/items/account/views/login.php:684
msgid "bbPress Dashboard"
msgstr "bảng điều khiển bbPress"

#: framework/features/header/items/account/options.php:619,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:32
msgid "Link"
msgstr "Liên kết"

#: framework/features/header/items/account/options.php:630
msgid "Dropdown Items"
msgstr "Các mục Menu thả xuống"

#: framework/features/header/items/account/options.php:687
msgid "Link To"
msgstr "Liên kết đến"

#: framework/premium/extensions/color-mode-switch/includes/logo-enhancements.php:34
msgid "Dark Mode Logo"
msgstr "Logo chế độ tối"

#: framework/premium/extensions/woocommerce-extra/features/sku-layer.php:60,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:248,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:434
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:140
msgid "SKU"
msgstr "SKU"

#: framework/premium/features/content-blocks/options/404.php:84,
#: framework/premium/features/content-blocks/options/archive.php:161,
#: framework/premium/features/content-blocks/options/header.php:102,
#: framework/premium/features/content-blocks/options/hook.php:211,
#: framework/premium/features/content-blocks/options/maintenance.php:81,
#: framework/premium/features/content-blocks/options/nothing_found.php:102,
#: framework/premium/features/content-blocks/options/single.php:93
msgid "Wide"
msgstr "Rộng"

#: framework/premium/features/content-blocks/options/archive.php:10
msgid "Replace Conditions"
msgstr "Thay thế điều kiện"

#: framework/premium/features/content-blocks/options/archive.php:15
msgid "Template Replace Conditions"
msgstr "Điều kiện thay thế mẫu"

#: framework/premium/features/content-blocks/options/archive.php:16,
#: framework/premium/features/content-blocks/options/header.php:36,
#: framework/premium/features/content-blocks/options/single.php:16
msgid "Choose where you want this template to be displayed."
msgstr "Chọn nơi bạn muốn hiển thị mẫu này."

#: framework/premium/features/content-blocks/options/archive.php:17
msgid "Add Replace Condition"
msgstr "Thêm điều kiện thay thế"

#: framework/premium/features/content-blocks/options/archive.php:142,
#: framework/premium/features/content-blocks/options/single.php:74
msgid "Left Sidebar"
msgstr "Thanh bên bên trái"

#: framework/premium/features/content-blocks/options/archive.php:147,
#: framework/premium/features/content-blocks/options/single.php:79
msgid "Right Sidebar"
msgstr "Thanh bên bên phải"

#: framework/premium/features/content-blocks/options/header.php:35,
#: framework/premium/features/content-blocks/options/single.php:15
msgid "Template Display Conditions"
msgstr "Điều kiện hiển thị mẫu"

#: framework/premium/features/content-blocks/options/hook.php:40
msgid "Content Block Display Conditions"
msgstr "Điều kiện hiển thị khối nội dung"

#. translators: placeholder here means the actual URL.
#: framework/premium/features/media-video/options.php:77
msgid "YouTube won't store information about visitors on your website unless they play the video. More info about this can be found %shere%s."
msgstr "YouTube sẽ không lưu trữ thông tin về khách truy cập trên trang web của bạn trừ khi họ phát video. Bạn có thể tìm thêm thông tin về điều này %stại đây%s."

#: framework/premium/features/media-video/options.php:91
msgid "Autoplay Video"
msgstr "Video tự động phát"

#: framework/premium/features/media-video/options.php:94
msgid "Automatically start video playback after the gallery is loaded."
msgstr "Tự động bắt đầu phát video sau khi bộ sưu tập được tải."

#: framework/premium/features/media-video/options.php:99
msgid "Loop Video"
msgstr "Video vòng lặp"

#: framework/premium/features/media-video/options.php:102
msgid "Start video again after it ends."
msgstr "Bắt đầu video lại sau khi nó kết thúc."

#: framework/premium/features/media-video/options.php:107
msgid "Simplified Player"
msgstr "Trình phát đơn giản"

#: framework/premium/features/content-blocks/hooks-manager.php:720
msgid "After single product \"Add to cart\" button"
msgstr "Sau nút \"Thêm vào giỏ hàng\" của sản phẩm đơn"

#: framework/premium/features/content-blocks/hooks-manager.php:725
msgid "Before single product meta"
msgstr "Trước meta sản phẩm đơn"

#: framework/premium/features/content-blocks/hooks-manager.php:730
msgid "After single product meta"
msgstr "Sau meta sản phẩm đơn"

#: framework/premium/features/media-video/options.php:6
msgid "Video Source"
msgstr "Nguồn Video"

#: framework/premium/features/media-video/options.php:25
msgid "Upload Video"
msgstr "Tải lên Video"

#: framework/premium/features/media-video/options.php:29
msgid "Upload an MP4 file into the media library."
msgstr "Tải lên một tệp MP4 vào thư viện media."

#: framework/premium/features/media-video/options.php:42
msgid "YouTube Url"
msgstr "Url YouTube"

#: framework/premium/features/media-video/options.php:44
msgid "Enter a valid YouTube media URL."
msgstr "Nhập URL media YouTube hợp lệ."

#: framework/premium/features/media-video/options.php:57
msgid "Vimeo Url"
msgstr "Url Vimeo"

#: framework/premium/features/media-video/options.php:59
msgid "Enter a valid Vimeo media URL."
msgstr "Nhập URL media Vimeo hợp lệ."

#: framework/premium/features/media-video/options.php:72
msgid "YouTube Privacy Enhanced Mode"
msgstr "Chế độ nâng cao quyền riêng tư của YouTube"

#: framework/premium/features/content-blocks/hooks-manager.php:234
msgid "After first post meta"
msgstr "Sau meta bài viết đầu tiên"

#: framework/premium/features/content-blocks/hooks-manager.php:242
msgid "After second post meta"
msgstr "Sau meta bài viết thứ hai"

#: framework/premium/features/content-blocks/hooks-manager.php:580
msgid "Offcanvas Cart - Empty State"
msgstr "Giỏ hàng Offcanvas - Trạng thái trống"

#: framework/premium/features/content-blocks/hooks-manager.php:705
msgid "Before single product gallery"
msgstr "Trước Album ảnh sản phẩm đơn"

#: framework/premium/features/content-blocks/hooks-manager.php:710
msgid "After single product gallery"
msgstr "Sau Album ảnh sản phẩm đơn"

#: framework/premium/features/content-blocks/hooks-manager.php:715
msgid "Before single product \"Add to cart\" button"
msgstr "Trước nút \"Thêm vào giỏ hàng\" của sản phẩm đơn"

#: framework/premium/extensions/mega-menu/options.php:532,
#: framework/premium/extensions/mega-menu/options.php:875
msgid "Badge Settings"
msgstr "Cài đặt huy hiệu"

#: framework/premium/extensions/mega-menu/options.php:764
msgid "Column Background"
msgstr "Nền cột"

#: framework/premium/extensions/shortcuts/customizer.php:673,
#: framework/premium/extensions/shortcuts/customizer.php:699,
#: framework/premium/extensions/shortcuts/views/bar.php:55,
#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:241,
#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:245,
#: framework/premium/extensions/woocommerce-extra/features/compare/helpers.php:110,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:36,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:418,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/bar.php:53,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/config.php:4,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:140,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/view.php:97
msgid "Compare"
msgstr "So sánh"

#: framework/premium/extensions/shortcuts/customizer.php:1124
msgid "Item Background Color"
msgstr "Màu nền mục"

#: framework/premium/extensions/shortcuts/customizer.php:1205
msgid "Wishlist Badge Color"
msgstr "Màu huy hiệu yêu thích"

#: framework/premium/extensions/shortcuts/customizer.php:1247
msgid "Compare Badge Color"
msgstr "Màu huy hiệu so sánh"

#: framework/premium/extensions/sidebars/extension.php:185
msgid "Remove Widget Area"
msgstr "Xóa bỏ khu vực tiện ích"

#: framework/premium/extensions/woocommerce-extra/config.php:21
msgid "This extension requires the WooCommerce plugin to be installed and activated."
msgstr "Tiện ích mở rộng này yêu cầu cài đặt và kích hoạt plugin WooCommerce."

#: framework/premium/extensions/woocommerce-extra/extension.php:359
msgid "Cart Page"
msgstr "Trang giỏ hàng"

#: framework/premium/features/content-blocks/admin-ui.php:269
msgid "Nothing Found"
msgstr "Không tìm thấy gì"

#: framework/premium/features/content-blocks/admin-ui.php:270
msgid "Maintenance"
msgstr "Bảo trì"

#: framework/premium/features/content-blocks/admin-ui.php:374
msgid "On click to element"
msgstr "Khi nhấp vào phần tử"

#: framework/extensions/product-reviews/extension.php:321,
#: framework/premium/features/content-blocks/content-block-layer.php:194,
#: framework/premium/features/content-blocks/content-block-layer.php:244,
#: framework/premium/extensions/woocommerce-extra/features/sku-layer.php:64,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:739,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:617,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:668,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:339,
#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:203,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:190,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:251,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:274,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:903,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-layer.php:246
msgid "Bottom Spacing"
msgstr "Khoảng cách dưới cùng"

#: framework/premium/features/content-blocks/hooks-manager.php:194
msgid "Before first post meta"
msgstr "Trước meta bài viết đầu tiên"

#: framework/premium/features/content-blocks/hooks-manager.php:202
msgid "Before second post meta"
msgstr "Trước meta bài viết thứ hai"

#: framework/features/conditions/rules/woo.php:80,
#: framework/features/conditions/rules/woo.php:95
msgid "Product with Taxonomy ID"
msgstr "Sản phẩm với ID phân loại"

#: framework/premium/extensions/color-mode-switch/extension.php:96
msgid "Dark Mode Color Palette"
msgstr "Bảng màu chế độ tối"

#: framework/premium/extensions/mega-menu/options.php:72
msgid "Dropdown Custom Width"
msgstr "Chiều rộng tùy chỉnh Menu thả xuống"

#: framework/premium/extensions/mega-menu/options.php:309
msgid "AJAX Content Loading"
msgstr "Tải nội dung AJAX"

#: framework/premium/extensions/mega-menu/options.php:312
msgid "If you have complex data inside your mega menu you can enable this option in order to load the dropdown content with AJAX and improve the website loading time."
msgstr "Nếu bạn có dữ liệu phức tạp trong menu mở rộng, bạn có thể bật tùy chọn này để tải nội dung thả xuống bằng AJAX và cải thiện thời gian tải trang web."

#: framework/premium/extensions/mega-menu/options.php:403
msgid "Content Visibility"
msgstr "Hiển thị nội dung"

#: framework/premium/features/clone-cpt.php:127
msgid "Post creation failed, could not find original post: "
msgstr "Tạo bài viết thất bại, không thể tìm thấy bài viết gốc: "

#: framework/premium/features/clone-cpt.php:184
msgid "Post copy created."
msgstr "Bản sao bài viết đã được tạo."

#: framework/premium/features/local-gravatars.php:34
msgid "Store Gravatars Locally"
msgstr "Lưu trữ Gravatars cục bộ"

#: framework/premium/features/local-gravatars.php:39
msgid "Store and load Gravatars locally for increased privacy and performance."
msgstr "Lưu trữ và tải Gravatar cục bộ để tăng hiệu suất và quyền riêng tư."

#: framework/premium/features/premium-header.php:314,
#: framework/premium/features/socials.php:13,
#: framework/features/blocks/contact-info/options.php:103,
#: framework/features/blocks/contact-info/options.php:168,
#: framework/features/blocks/contact-info/options.php:231,
#: framework/features/blocks/contact-info/options.php:294,
#: framework/features/blocks/contact-info/options.php:357,
#: framework/features/blocks/contact-info/options.php:420,
#: framework/features/blocks/contact-info/options.php:483,
#: framework/features/header/items/account/options.php:375,
#: framework/features/header/items/account/options.php:756,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:9
msgid "Icon Source"
msgstr "Nguồn biểu tượng"

#: framework/premium/features/socials.php:41
msgid "URL Source"
msgstr "Nguồn URL"

#: framework/premium/features/socials.php:59
msgid "Custom URL"
msgstr "URL tùy chỉnh"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:60
msgid "Form Style"
msgstr "Kiểu biểu mẫu"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:68
msgid "Stacked"
msgstr "Chồng chất"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:137,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:152
msgid "Your email *"
msgstr "Email của bạn *"

#: framework/extensions/newsletter-subscribe/providers/demo.php:16
msgid "Demo List"
msgstr "Danh sách demo"

#: framework/features/conditions/rules/basic.php:10,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:12
msgid "All Products"
msgstr "Tất cả sản phẩm"

#: framework/features/conditions/rules/basic.php:40
msgid "All Singulars"
msgstr "Tất cả nội dung đơn lẻ"

#: framework/features/conditions/rules/basic.php:51
msgid "All Archives"
msgstr "Tất cả lưu trữ"

#: framework/features/conditions/rules/date-time.php:5
msgid "Date & Time"
msgstr "Ngày & Giờ"

#: framework/features/conditions/rules/date-time.php:9
msgid "Range Date/Time"
msgstr "Phạm vi Ngày/Giờ"

#: framework/features/conditions/rules/date-time.php:14
#: static/js/options/ConditionsManager/ScheduleDate.js:143
msgid "Recurring Days"
msgstr "Ngày định kỳ"

#: framework/features/conditions/rules/requests.php:5
msgid "Requests"
msgstr "Yêu cầu"

#: framework/features/conditions/rules/requests.php:9
msgid "Request Referer"
msgstr "Yêu cầu Referer"

#: framework/features/conditions/rules/requests.php:14
msgid "Request Cookie"
msgstr "Yêu cầu Cookie"

#: framework/features/conditions/rules/requests.php:19
msgid "Request URL"
msgstr "Yêu cầu URL"

#: framework/features/conditions/rules/specific.php:31
msgid "Post with Author ID"
msgstr "Bài viết với ID Tác Giả"

#: framework/features/conditions/rules/woo.php:75,
#: framework/features/conditions/rules/woo.php:90
msgid "Product ID"
msgstr "ID Sản phẩm"

#: framework/extensions/cookies-consent/customizer.php:199
msgid "Accept Button"
msgstr "Nút chấp nhận"

#: framework/extensions/cookies-consent/customizer.php:266
msgid "Decline Button"
msgstr "Nút từ chối"

#: framework/extensions/cookies-consent/extension.php:105
msgid "Please accept the Privacy Policy in order to comment."
msgstr "Vui lòng chấp nhận Chính Sách Bảo Mật để bình luận."

#: framework/extensions/cookies-consent/extension.php:106
msgid "Comment Submission Failure"
msgstr "Gửi bình luận thất bại"

#: framework/extensions/newsletter-subscribe/customizer.php:28,
#: framework/extensions/newsletter-subscribe/helpers.php:24,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:15
msgid "Enter your email address below and subscribe to our newsletter"
msgstr "Nhập địa chỉ email của bạn dưới đây và đăng ký bản tin của chúng tôi"

#: framework/extensions/newsletter-subscribe/customizer.php:389
msgid "Container Shadow"
msgstr "Bóng vùng chứa"

#: framework/extensions/trending/customizer.php:588
msgid "Module Title Font"
msgstr "Phông chữ tiêu đề Module"

#: framework/extensions/trending/customizer.php:596
msgid "Module Title Color"
msgstr "Màu tiêu đề Module"

#: framework/extensions/trending/customizer.php:647
msgid "Posts Title Font"
msgstr "Phông chữ Tiêu đề bài viết"

#: framework/extensions/trending/customizer.php:656
msgid "Posts Title Font Color"
msgstr "Màu phông chữ Tiêu Đề Bài viết"

#: framework/extensions/trending/customizer.php:871
msgid "Arrows Color"
msgstr "Màu mũi tên"

#: framework/premium/features/clone-cpt.php:42,
#: framework/premium/features/clone-cpt.php:45
msgid "Duplicate"
msgstr "Tạo bản sao"

#: framework/premium/features/clone-cpt.php:55
msgid "No post to duplicate"
msgstr "Không có bài viết để tạo bản sao"

#: framework/helpers/exts-configs.php:314
msgid "Create a customized order “Thank You” page for your customers, giving them a personalized experience."
msgstr "Tạo một trang “Cảm ơn” đơn hàng tùy chỉnh cho khách hàng của bạn, mang lại cho họ một trải nghiệm cá nhân hóa."

#: framework/helpers/exts-configs.php:322,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:5
msgid "Advanced Reviews"
msgstr "Đánh giá nâng cao"

#: framework/helpers/exts-configs.php:323
msgid "Enhance your WooCommerce reviews with rich content, images and a thumbs up system that help your shoppers find the perfect product."
msgstr "Nâng cao đánh giá WooCommerce của bạn với nội dung phong phú, hình ảnh và hệ thống thích giúp người mua hàng của bạn tìm thấy sản phẩm hoàn hảo."

#: framework/extensions/cookies-consent/customizer.php:112
msgid "Forms Cookies Content"
msgstr "Nội dung Forms Cookies"

#: framework/helpers/exts-configs.php:362
msgid "Better management for affiliate products with a few simple options that strengthen the external integration with these."
msgstr "Quản lý tốt hơn các sản phẩm liên kết với một số tùy chọn đơn giản giúp tăng cường khả năng tích hợp bên ngoài với các sản phẩm này."

#: framework/helpers/exts-configs.php:295
msgid "Custom Tabs"
msgstr "Tab tùy chỉnh"

#: framework/helpers/exts-configs.php:296
msgid "Present additional information about your products by adding new custom tabs to the product information section."
msgstr "Trình bày thông tin bổ sung về sản phẩm của bạn bằng cách thêm các tab tùy chỉnh mới vào phần thông tin sản phẩm."

#: framework/helpers/exts-configs.php:304,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:58,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:62,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:272,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/helpers.php:47,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/modal-view.php:18,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/side-view.php:27
msgid "Size Guide"
msgstr "Hướng dẫn kích thước"

#: framework/helpers/exts-configs.php:305
msgid "Show a size chart guide so that your visitors can pick the right size for them when ordering a product."
msgstr "Hiển thị hướng dẫn bảng kích thước để khách hàng của bạn có thể chọn kích cỡ phù hợp khi đặt mua sản phẩm."

#: framework/helpers/exts-configs.php:313
msgid "Custom Thank You Pages"
msgstr "Trang cảm ơn tùy chỉnh"

#: framework/helpers/exts-configs.php:279
msgid "Catch the attention of your clients by showcasing your product variations as colour, image or button swatches."
msgstr "Thu hút sự chú ý của khách hàng bằng cách giới thiệu các biến thể sản phẩm của bạn dưới dạng màu sắc, hình ảnh hoặc mẫu nút."

#: framework/helpers/exts-configs.php:286,
#: framework/features/conditions/rules/woo.php:42
msgid "Product Brands"
msgstr "Thương hiệu sản phẩm"

#: framework/helpers/exts-configs.php:287
msgid "Categorise products by brands and show their logo in archive or single page so users could discover more about their makers."
msgstr "Phân loại sản phẩm theo nhãn hiệu và hiển thị logo của họ trong kho lưu trữ hoặc một trang để người dùng có thể khám phá thêm về nhà sản xuất của họ."

#: framework/helpers/exts-configs.php:361
msgid "Affiliate Product Links"
msgstr "Link sản phẩm tiếp thị liên kết"

#: framework/helpers/exts-configs.php:339
msgid "Replace the standard product gallery with additional layouts which can showcase the photos as a grid or even a slider."
msgstr "Thay thế Album ảnh sản phẩm tiêu chuẩn bằng các bố cục bổ sung có thể trưng bày các hình ảnh dưới dạng lưới hoặc thậm chí là thanh trượt."

#: framework/helpers/exts-configs.php:354
msgid "Search by SKU"
msgstr "Tìm kiếm theo SKU"

#: framework/helpers/exts-configs.php:355
msgid "Advanced searching for products by their SKU classification can be useful in cases of vast product catalogues."
msgstr "Tìm kiếm nâng cao các sản phẩm theo phân loại SKU của chúng có thể hữu ích trong trường hợp danh mục sản phẩm rộng lớn."

#: framework/helpers/exts-configs.php:331
msgid "Add a visual cue that tells your visitors how much the cart total must be to be able to benefit of free shipping."
msgstr "Thêm một dấu hiệu hình ảnh cho biết khách truy cập của bạn cần mua hàng với tổng giá trị bao nhiêu để có thể hưởng lợi từ việc giao hàng miễn phí."

#: framework/helpers/exts-configs.php:278,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:4
msgid "Variation Swatches"
msgstr "Bảng màu biến thể"

#: framework/helpers/exts-configs.php:271
msgid "Compare products with a clear and concise table system that gives your users a way to make a quick decision."
msgstr "So sánh sản phẩm với hệ thống bảng rõ ràng và ngắn gọn giúp người dùng của bạn có cách để đưa ra quyết định nhanh chóng."

#: framework/helpers/exts-configs.php:346
msgid "Product Share Box"
msgstr "Hộp chia sẻ sản phẩm"

#: framework/helpers/exts-configs.php:347
msgid "Enable social sharing abilities for products available on the site, letting even more users discover your great shop selection."
msgstr "Kích hoạt khả năng chia sẻ xã hội cho các sản phẩm có sẵn trên trang web, cho phép ngay cả nhiều người dùng hơn khám phá lựa chọn cửa hàng tuyệt vời của bạn"

#: framework/helpers/exts-configs.php:338
msgid "Advanced Gallery"
msgstr "Album ảnh nâng cao"

#: framework/helpers/exts-configs.php:247
msgid "Preview the available products and let your users make quick and informative decisions about their purchase."
msgstr "Xem trước các sản phẩm có sẵn và để người dùng của bạn đưa ra quyết định nhanh chóng và thông minh về việc mua hàng của họ."

#: framework/helpers/exts-configs.php:254,
#: framework/premium/extensions/shortcuts/views/bar.php:54
msgid "Filters"
msgstr "Bộ lọc"

#: framework/helpers/exts-configs.php:255
msgid "Drill down the product list with new filtering widgets, an off canvas area for them and showing the active filters on the page."
msgstr "Lọc danh sách sản phẩm với các tiện ích lọc mới, một khu vực cửa sổ bên ngoài cho chúng và hiển thị các bộ lọc đang hoạt động trên trang."

#: framework/helpers/exts-configs.php:263
msgid "A set of features that lets you create easily your dream products wishlists and share them with friends and family."
msgstr "Một tập hợp các tính năng cho phép bạn dễ dàng tạo danh sách sản phẩm mong muốn và chia sẻ chúng với bạn bè và gia đình."

#: framework/helpers/exts-configs.php:270
msgid "Compare View"
msgstr "Xem so sánh"

#: framework/helpers/exts-configs.php:239
msgid "Adds the “add to cart” actions to the product page as a floating bar if the product summary has disappeared from view."
msgstr "Thêm hành động “thêm vào giỏ hàng\" vào trang sản phẩm dưới dạng thanh nổi nếu tóm tắt sản phẩm đã biến mất khỏi tầm nhìn."

#: framework/helpers/exts-configs.php:246,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:58
msgid "Quick View"
msgstr "Xem nhanh"

#: framework/helpers/exts-configs.php:157
msgid "Let your guests easily filter the posts by their category or tags taxonomy terms, instantly drilling down the listings."
msgstr "Cho phép khách của bạn dễ dàng lọc các bài viết theo danh mục hoặc từ khóa của họ, ngay lập tức lọc thông tin."

#: framework/helpers/exts-configs.php:163
msgid "Taxonomy Customisations"
msgstr "Tùy chỉnh phân loại"

#: framework/helpers/exts-configs.php:164
msgid "Additional customisation options for your taxonomies such as hero backgrounds and custom colour labels."
msgstr "Các tùy chọn tùy chỉnh bổ sung cho các phân loại của bạn như hình nền nổi bật và nhãn màu tùy chỉnh."

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:227
msgctxt "Extension Brand Name"
msgid "Shop Extra"
msgstr "Mở rộng cửa hàng"

#: framework/helpers/exts-configs.php:143
msgid "Display the approximate reading time of an article, so that visitors know what to expect when starting to read the content."
msgstr "Hiển thị thời gian đọc xấp xỉ của một bài viết, để khách truy cập biết được những gì họ cần kỳ vọng khi bắt đầu đọc nội dung."

#: framework/helpers/exts-configs.php:149
#: static/js/editor/blocks/dynamic-data/index.js:100
msgid "Dynamic Data"
msgstr "Dữ liệu động"

#: framework/helpers/exts-configs.php:150
msgid "Integrates custom fields solutions in the meta layers of a post and presents additional information."
msgstr "Tích hợp các giải pháp trường tùy chỉnh vào các lớp meta của một bài viết và trình bày thông tin bổ sung."

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:83
msgctxt "Extension Brand Name"
msgid "Color Mode Switch"
msgstr "Chuyển đổi chế độ màu"

#: framework/helpers/exts-configs.php:84
msgid "Add a dark colour scheme and switch to your website, which will make it pleasant to look at in low light environments."
msgstr "Thêm một bảng màu tối và chuyển đổi trang web của bạn, điều này sẽ làm cho nó dễ nhìn hơn trong môi trường ánh sáng yếu."

#. translators: 1: link open 2: link close
#: framework/features/account-auth.php:253
msgid "Registration complete. Please check your email, then visit the %1$slogin page%2$s."
msgstr "Đăng ký hoàn tất. Vui lòng kiểm tra email của bạn, sau đó truy cập %1$slogin trang%2$s."

#: framework/dashboard.php:34,
#: framework/features/header/items/account/options.php:23,
#: framework/features/header/items/account/options.php:73,
#: framework/features/header/items/account/options.php:77,
#: framework/features/header/items/account/options.php:649,
#: framework/features/header/items/account/views/login.php:158,
#: framework/features/header/items/account/views/login.php:335
msgid "Dashboard"
msgstr "Bảng tổng quan"

#: framework/dashboard.php:521
msgid "You do not have sufficient permissions to access this page."
msgstr "Bạn không có đủ quyền để truy cập trang này."

#: framework/theme-integration.php:221,
#: framework/features/blocks/share-box/options.php:25
msgid "X (Twitter)"
msgstr "X (Twitter)"

#: framework/theme-integration.php:234
msgid "Mastodon"
msgstr "Mastodon"

#: framework/features/conditions/rules/pages.php:39
msgid "Privacy Policy Page"
msgstr "Trang Chính sách bảo mật"

#: framework/features/conditions/rules/pages.php:46
msgid "Author Archives"
msgstr "Lưu trữ tác giả"

#: framework/features/conditions/rules/woo.php:104,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:42
msgid "WooCommerce"
msgstr "WooCommerce"

#: framework/features/conditions/rules/woo.php:15
msgid "Shop Home"
msgstr "Trang chủ cửa hàng"

#: framework/features/conditions/rules/woo.php:20,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:66
msgid "Single Product"
msgstr "Sản phẩm đơn"

#: framework/features/conditions/rules/woo.php:25
msgid "Product Archives"
msgstr "Sản phẩm lưu trữ"

#: framework/features/conditions/rules/woo.php:30,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:91
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:27
msgid "Product Categories"
msgstr "Danh mục sản phẩm"

#: framework/features/conditions/rules/woo.php:49
msgid "Product Tags"
msgstr "Thẻ sản phẩm"

#: framework/features/conditions/rules/cpt.php:53
msgid "Custom Post Types"
msgstr "Loại nội dung tùy chỉnh"

#: framework/features/conditions/rules/localization.php:15
msgid "Current Language"
msgstr "Ngôn ngữ hiện tại"

#: framework/features/conditions/rules/bbPress.php:11,
#: framework/features/header/items/account/options.php:231
msgid "bbPress"
msgstr "bbPress"

#: framework/features/conditions/rules/bbPress.php:15,
#: framework/features/header/items/account/options.php:24,
#: framework/features/header/items/account/options.php:87
msgid "Profile"
msgstr "Hồ sơ"

#: framework/extensions/newsletter-subscribe/helpers.php:142
msgid "First name"
msgstr "Tên"

#: framework/extensions/trending/customizer.php:537
msgid "Display Location"
msgstr "Hiển thị vị trí"

#: framework/extensions/trending/customizer.php:545
msgid "Before Footer"
msgstr "Trước chân trang"

#: framework/extensions/trending/customizer.php:550
msgid "After Footer"
msgstr "Sau chân trang"

#: framework/extensions/trending/customizer.php:555
msgid "After Header"
msgstr "Sau đầu trang"

#: framework/extensions/trending/customizer.php:572
msgid "Trending Block Display Conditions"
msgstr "Điều kiện hiển thị Block xu hướng"

#: framework/extensions/trending/customizer.php:573
msgid "Add one or more conditions to display the trending block."
msgstr "Thêm một hoặc nhiều điều kiện để hiển thị Block xu hướng."

#: framework/premium/features/content-blocks/admin-ui.php:641
msgid "Hide Hooks"
msgstr "Ẩn Hooks"

#: framework/premium/features/content-blocks/admin-ui.php:642
msgid "Show Hooks"
msgstr "Hiển thị Hooks"

#: framework/premium/features/content-blocks/admin-ui.php:698
msgid "Hide Theme Hooks"
msgstr "Ẩn Theme Hooks"

#: framework/premium/features/content-blocks/admin-ui.php:699
msgid "Show Theme Hooks"
msgstr "Hiển thị Theme Hooks"

#: framework/premium/features/content-blocks/admin-ui.php:707
msgid "Hide WooCommerce Hooks"
msgstr "Ẩn WooCommerce Hooks"

#: framework/premium/features/content-blocks/admin-ui.php:708
msgid "Show WooCommerce Hooks"
msgstr "Hiển thị WooCommerce Hooks"

#: framework/features/header/modal/register.php:76
msgid "A link to set a new password will be sent to your email address."
msgstr "Một liên kết để đặt mật khẩu mới sẽ được gửi đến địa chỉ email của bạn."

#: framework/premium/extensions/code-snippets/extension.php:31,
#: framework/premium/extensions/code-snippets/extension.php:86,
#: framework/premium/extensions/code-snippets/extension.php:131
msgid "Header scripts"
msgstr "Tệp lệnh đầu trang"

#: framework/premium/extensions/code-snippets/extension.php:53,
#: framework/premium/extensions/code-snippets/extension.php:108,
#: framework/premium/extensions/code-snippets/extension.php:153
msgid "Footer scripts"
msgstr "Tệp lệnh chân trang"

#: framework/premium/extensions/mega-menu/options.php:377,
#: framework/premium/extensions/mega-menu/options.php:385,
#: framework/premium/features/content-blocks/content-block-layer.php:170,
#: framework/premium/features/content-blocks/content-block-layer.php:178,
#: framework/premium/features/content-blocks/content-block-layer.php:221,
#: framework/premium/features/content-blocks/content-block-layer.php:229,
#: framework/features/header/items/account/options.php:254,
#: framework/features/header/items/account/options.php:262,
#: framework/premium/features/premium-header/items/content-block/options.php:13,
#: framework/premium/features/premium-header/items/content-block/options.php:21
#: framework/premium/static/js/blocks/ContentBlock.js:110
msgid "Select Content Block"
msgstr "Chọn khối nội dung"

#: framework/premium/extensions/mega-menu/options.php:380,
#: framework/premium/features/content-blocks/content-block-layer.php:173,
#: framework/premium/features/content-blocks/content-block-layer.php:224,
#: framework/features/header/items/account/options.php:257,
#: framework/premium/features/premium-header/items/content-block/options.php:16
msgid "Create a new content Block/Hook"
msgstr "Tạo một khối nội dung/Hook mới"

#: framework/premium/extensions/mega-menu/options.php:637
msgid "Heading Font"
msgstr "Phông chữ tiêu đề"

#: framework/premium/features/content-blocks/admin-ui.php:155
msgid "Enable"
msgstr "Bật"

#: framework/premium/features/content-blocks/admin-ui.php:156
msgid "Disable"
msgstr "Tắt"

#: framework/premium/features/content-blocks/admin-ui.php:211
msgid "Enabled %s content block."
msgid_plural "Enabled %s content blocks."
msgstr[0] "Đã kích hoạt %s khối nội dung."

#: framework/premium/features/content-blocks/admin-ui.php:236
msgid "Disabled %s content block."
msgid_plural "Disabled %s content blocks."
msgstr[0] "Đã vô hiệu hóa %s khối nội dung."

#: framework/premium/features/content-blocks/admin-ui.php:264
msgid "404 Page"
msgstr "Trang 404"

#: framework/premium/features/content-blocks/admin-ui.php:267,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:16,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:64
msgid "Archive"
msgstr "Lưu trữ"

#: framework/premium/features/content-blocks/admin-ui.php:268,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:17,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:65
msgid "Single"
msgstr "Đơn"

#: framework/premium/features/content-blocks/hooks-manager.php:39
msgid "WP body open"
msgstr "Mở thân trang WP"

#: framework/premium/features/content-blocks/hooks-manager.php:340
msgid "Before related posts"
msgstr "Trước bài viết liên quan"

#: framework/premium/features/content-blocks/hooks-manager.php:341,
#: framework/premium/features/content-blocks/hooks-manager.php:348,
#: framework/premium/features/content-blocks/hooks-manager.php:355,
#: framework/premium/features/content-blocks/hooks-manager.php:362,
#: framework/premium/features/content-blocks/hooks-manager.php:369,
#: framework/premium/features/content-blocks/hooks-manager.php:376,
#: framework/premium/features/content-blocks/hooks-manager.php:383,
#: framework/premium/features/content-blocks/hooks-manager.php:390,
#: framework/premium/features/content-blocks/hooks-manager.php:398,
#: framework/premium/features/content-blocks/hooks-manager.php:405
msgid "Related posts"
msgstr "Bài viết liên quan"

#: framework/premium/features/content-blocks/hooks-manager.php:347
msgid "Related posts top"
msgstr "Bài viết liên quan top"

#: framework/premium/features/content-blocks/hooks-manager.php:368
msgid "Card top"
msgstr "Card top"

#: framework/premium/features/content-blocks/hooks-manager.php:375
msgid "Before featured image"
msgstr "Trước ảnh đại diện"

#: framework/premium/features/content-blocks/hooks-manager.php:382
msgid "After featured image"
msgstr "Sau ảnh đại diện"

#: framework/premium/features/content-blocks/hooks-manager.php:389
msgid "Card bottom"
msgstr "Phần dưới của thẻ"

#: framework/premium/features/content-blocks/hooks-manager.php:397
msgid "Related posts bottom"
msgstr "Bài viết liên quan phía dưới"

#: framework/premium/features/content-blocks/hooks-manager.php:404
msgid "After related posts"
msgstr "Sau bài viết liên quan"

#: framework/premium/features/content-blocks/hooks-manager.php:568
msgid "Offcanvas Filters - Top"
msgstr "Bộ lọc trên Offcanvas"

#: framework/premium/features/content-blocks/hooks-manager.php:574
msgid "Offcanvas Filters - Bottom"
msgstr "Bộ lọc dưới Offcanvas"

#: framework/premium/features/content-blocks/options/archive.php:31,
#: framework/premium/features/content-blocks/options/single.php:30
msgid "Replacement Behavior"
msgstr "Hành vi thay thế"

#: framework/premium/features/content-blocks/options/archive.php:38
msgid "Only Card"
msgstr "Chỉ thẻ"

#: framework/premium/features/content-blocks/options/archive.php:39,
#: framework/premium/features/content-blocks/options/single.php:38
msgid "Full Page"
msgstr "Toàn trang"

#: framework/premium/features/content-blocks/options/404.php:51,
#: framework/premium/features/content-blocks/options/archive.php:118,
#: framework/premium/features/content-blocks/options/maintenance.php:48,
#: framework/premium/features/content-blocks/options/nothing_found.php:69,
#: framework/premium/features/content-blocks/options/single.php:50
msgid "Page Structure"
msgstr "Cấu trúc trang"

#: framework/premium/features/content-blocks/options/single.php:37
msgid "Content Area"
msgstr "Khu vực nội dung"

#: framework/premium/features/content-blocks/options/single.php:99
msgid "Content Area Vel Spacing"
msgstr "Khoảng cách Vel khu vực nội dung"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:81
msgid "Filter Source"
msgstr "Lọc nguồn"

#: framework/features/blocks/contact-info/options.php:590,
#: framework/premium/features/premium-header/items/contacts/options.php:6
msgid "Items Direction"
msgstr "Mục hướng"

#: framework/features/blocks/contact-info/options.php:597,
#: framework/premium/features/premium-header/items/contacts/options.php:14
msgid "Horizontal"
msgstr "Chiều ngang"

#: framework/features/blocks/contact-info/options.php:596,
#: framework/premium/features/premium-header/items/contacts/options.php:13
msgid "Vertical"
msgstr "Chiều dọc"

#: framework/premium/features/premium-header/items/contacts/options.php:889,
#: framework/premium/features/premium-header/items/contacts/options.php:931,
#: framework/premium/features/premium-header/items/contacts/options.php:969,
#: framework/premium/features/premium-header/items/contacts/options.php:1007
#: static/js/editor/blocks/about-me/Edit.js:148
#: static/js/editor/blocks/contact-info/Edit.js:162
msgid "Icons Background Color"
msgstr "Màu nền biểu tượng"

#: framework/premium/features/premium-header/items/contacts/options.php:893,
#: framework/premium/features/premium-header/items/contacts/options.php:935,
#: framework/premium/features/premium-header/items/contacts/options.php:973,
#: framework/premium/features/premium-header/items/contacts/options.php:1011
#: static/js/editor/blocks/about-me/Edit.js:179
#: static/js/editor/blocks/contact-info/Edit.js:193
msgid "Icons Border Color"
msgstr "Màu viền biểu tượng"

#: framework/premium/static/js/options/IconPicker/CustomIcon.js:22
msgid "Click to upload"
msgstr "Nhấp để tải lên"

#: framework/premium/static/js/options/IconPicker/Modal.js:135
msgid "All Icons"
msgstr "Tất cả biểu tượng"

#: static/js/options/ConditionsManager/SingleCondition.js:296
msgid "All authors"
msgstr "Tất cả tác giả"

#: static/js/dashboard/screens/DemoInstall/components/Error.js:25
msgid "Can't Import Starter Site"
msgstr "Không thể nhập mẫu web bắt đầu"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:31
msgid "Unfortunately, your hosting configuration doesn't meet the minimum requirements for importing a starter site."
msgstr "Thật không may, cấu hình hosting của bạn không đáp ứng các yêu cầu tối thiểu để nhập một mẫu web bắt đầu."

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/views/panel.php:62
msgid "Close filters modal"
msgstr "Đóng hộp bật lên bộ lọc"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/view.php:45
msgid "Close quick view"
msgstr "Đóng chế độ xem nhanh"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/helpers.php:45
msgid "Quick view toggle"
msgstr "Chuyển đổi xem nhanh"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/helpers.php:32
msgid "Quick view icon"
msgstr "Biểu tượng xem nhanh"

#: framework/features/header/items/account/options.php:1724,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:314,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:185
msgid "Close Button Type"
msgstr "Loại nút đóng"

#: framework/features/header/items/account/options.php:726,
#: framework/features/header/items/account/options.php:1731,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:323,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:23,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:194
msgid "Simple"
msgstr "Đơn giản"

#: framework/features/header/items/account/options.php:1732,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:324,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:195
msgid "Border"
msgstr "Viền"

#: framework/features/header/items/account/options.php:1778,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:366,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:248,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:254,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:298,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:382,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:466
msgid "Border Color"
msgstr "Màu viền"

#: framework/premium/features/content-blocks/hooks-manager.php:170
msgid "Before description"
msgstr "Trước mô tả"

#: framework/premium/features/content-blocks/hooks-manager.php:178
msgid "Before breadcrumbs"
msgstr "Trước điều hướng trang"

#: framework/premium/features/content-blocks/hooks-manager.php:218
msgid "After description"
msgstr "Sau mô tả"

#: framework/premium/features/content-blocks/hooks-manager.php:226
msgid "After breadcrumbs"
msgstr "Sau điều hướng trang"

#: framework/premium/features/content-blocks/hooks-manager.php:630
msgid "Before shop loop item actions"
msgstr "Trước các hành động mục vòng lặp cửa hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:635
msgid "After shop loop item actions"
msgstr "Sau các hành động mục vòng lặp cửa hàng"

#. translators: placeholder here means the actual URL.
#: framework/features/blocks/socials/options.php:24
msgid "Configure the social links in Customizer ➝ General ➝ %sSocial Network Accounts%s."
msgstr "Cấu hình các liên kết xã hội trong Customizer ➝ General ➝ %sTài khoản mạng xã hội%s."

#: framework/features/header/items/account/options.php:306
msgid "Customizing: Logged out State"
msgstr "Tùy chỉnh: Trạng thái đăng xuất"

#: framework/features/header/items/account/options.php:1088
msgid "User Visibility"
msgstr "Khả năng hiển thị của người dùng"

#: framework/features/header/items/account/options.php:1099,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:116,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:95
msgid "Logged In"
msgstr "Đăng nhập"

#: framework/features/header/items/account/options.php:1100,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:117,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:96
msgid "Logged Out"
msgstr "Đăng xuất"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1385
msgid "Custom Field"
msgstr "Trường tùy chỉnh"

#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:114
msgid "%s min"
msgid_plural "%s mins"
msgstr[0] "%s phút"

#: framework/premium/features/content-blocks/options/archive.php:98
msgid "Default Card Layout"
msgstr "Bố cục thẻ mặc định"

#: framework/premium/features/content-blocks/options/archive.php:103
msgid "Inherit card wrapper settings from Customizer (background color, spacing, shadow)."
msgstr "Kế thừa cài đặt trình bao bọc thẻ từ Tùy biến (màu nền, khoảng cách, bóng)."

#: framework/premium/features/content-blocks/options/archive.php:53,
#: framework/premium/features/content-blocks/options/hook.php:277,
#: framework/premium/features/content-blocks/options/popup.php:483,
#: framework/premium/features/content-blocks/options/single.php:151
msgid "Dynamic Content Preview"
msgstr "Xem trước nội dung động"

#: framework/premium/features/content-blocks/options/archive.php:60
msgid "Select a post/page to preview it's content inside the editor while building the archive."
msgstr "Chọn một bài viết/trang để xem trước nội dung bên trong trình chỉnh sửa khi xây dựng lưu trữ."

#: framework/premium/features/content-blocks/options/archive.php:66
msgid "Editor/Card Width"
msgstr "Chiều rộng biên tập/Thẻ"

#: framework/premium/features/content-blocks/options/archive.php:77
msgid "Set the editor width for better understanging the layout you are building (just for preview purpose, this option won't apply in frontend)."
msgstr "Đặt chiều rộng của trình chỉnh sửa để hiểu rõ hơn về bố cục bạn đang xây dựng (chỉ dùng để xem trước, tùy chọn này sẽ không áp dụng ở phía trước)."

#: framework/premium/features/content-blocks/options/popup.php:203
msgid "After X Pages"
msgstr "Sau X trang"

#: framework/premium/features/content-blocks/options/popup.php:209
msgid "Set after how many visited pages the popup block will appear."
msgstr "Đặt sau bao nhiêu trang đã truy cập, khối Popup sẽ xuất hiện."

#: framework/premium/features/content-blocks/options/popup.php:489,
#: framework/premium/features/content-blocks/options/single.php:157
msgid "Select a post/page to preview it's content inside the editor while building the post/page."
msgstr "Chọn một bài viết/trang để xem trước nội dung bên trong trình chỉnh sửa khi xây dựng lưu trữ."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:236
msgid "More information on how to generate an API key for Mailerlite can be found %shere%s. Please note that it is required at least one group to be created in your account for the integration to work. More info on how to create a group %shere%s."
msgstr "Thêm thông tin về cách tạo một khóa API cho Mailerlite có thể được tìm thấy %stại đây%s. Lưu ý ít nhất một nhóm phải được tạo trong tài khoản của bạn để tích hợp hoạt động. Thêm thông tin về cách tạo một nhóm %stại đây%s."

#: framework/premium/static/js/hooks/CodeEditor.js:59
msgid "Code Editor"
msgstr "Chỉnh sửa mã"

#: framework/premium/static/js/hooks/CreateHook.js:101
msgid "Template Type"
msgstr "Loại mẫu"

#: framework/premium/static/js/hooks/CreateHook.js:116
msgid "Archive Template"
msgstr "Mẫu lưu trữ"

#: framework/premium/static/js/hooks/CreateHook.js:124
msgid "Single Template"
msgstr "Mẫu đơn"

#: framework/premium/static/js/hooks/CreateHook.js:178
msgid "Hook Name"
msgstr "Tên Hook"

#: framework/premium/static/js/hooks/CreateHook.js:182
msgid "Popup Name"
msgstr "Tên Popup"

#: framework/premium/static/js/hooks/CreateHook.js:186
msgid "Template Name"
msgstr "Tên mẫu"

#: framework/premium/features/content-blocks/admin-ui.php:263,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:52,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:22
#: framework/premium/static/js/hooks/CreateHook.js:77
msgid "Popup"
msgstr "Popup"

#: framework/premium/static/js/hooks/CreateHook.js:86
msgid "Custom Template"
msgstr "Mẫu tùy chỉnh"

#: framework/premium/static/js/options/IconPicker.js:20
msgid "Theme Icons"
msgstr "Biểu tượng giao diện"

#: framework/premium/static/js/options/IconPicker.js:26
msgid "FontAwesome Brands"
msgstr "Thương hiệu FontAwesome"

#: framework/premium/static/js/options/IconPicker.js:32
msgid "FontAwesome Solid"
msgstr "FontAwesome đậm"

#: framework/premium/static/js/options/IconPicker.js:38
msgid "FontAwesome Regular"
msgstr "FontAwesome Thông thường"

#: framework/premium/static/js/typography/providers/kadence.js:21
#: framework/premium/static/js/typography/providers/plus-addons.js:23
#: framework/premium/static/js/typography/providers/stackable.js:23
msgid "%s Local Google Fonts"
msgstr "%s Phông chữ Google cục bộ"

#: framework/premium/static/js/typography/providers/kadence.js:26
#: framework/premium/static/js/typography/providers/plus-addons.js:27
#: framework/premium/static/js/typography/providers/stackable.js:27
msgid "%s Typekit"
msgstr "%s Typekit"

#: framework/premium/static/js/typography/providers/kadence.js:31
#: framework/premium/static/js/typography/providers/stackable.js:31
msgid "%s Custom Fonts"
msgstr "%s Phông chữ tùy chỉnh"

#: framework/premium/static/js/typography/providers/kadence.js:59
msgid "Normal"
msgstr "Bình thường"

#: framework/premium/static/js/typography/providers/kadence.js:83
msgid "Inherit"
msgstr "Kế thừa"

#: framework/premium/static/js/typography/providers/plus-addons.js:31
msgid "%s Custom"
msgstr "%s Tùy chỉnh"

#: framework/premium/static/js/typography/providers/plus-addons.js:35
msgid "%s System"
msgstr "%s Hệ thống"

#: framework/premium/features/premium-header.php:22,
#: framework/premium/features/premium-header.php:58
msgid "Mobile Menu 1"
msgstr "Mobile Menu 1"

#: framework/premium/features/premium-header.php:59,
#: framework/premium/features/premium-header/items/mobile-menu-secondary/config.php:4
msgid "Mobile Menu 2"
msgstr "Mobile Menu 2"

#: framework/extensions/newsletter-subscribe/providers/active-campaign.php:154,
#: framework/extensions/newsletter-subscribe/providers/brevo.php:116,
#: framework/extensions/newsletter-subscribe/providers/campaign-monitor.php:136,
#: framework/extensions/newsletter-subscribe/providers/convertkit.php:97,
#: framework/extensions/newsletter-subscribe/providers/demo.php:40,
#: framework/extensions/newsletter-subscribe/providers/mailerlite-classic.php:104,
#: framework/extensions/newsletter-subscribe/providers/mailerlite-new.php:123,
#: framework/extensions/newsletter-subscribe/providers/mailpoet.php:93
msgid "Thank you for subscribing to our newsletter!"
msgstr "Cảm ơn bạn đã Đăng ký nhận bản tin của chúng tôi!"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:59
msgctxt "Extension Brand Name"
msgid "Adobe Fonts"
msgstr "Adobe Fonts"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:70,
#: framework/premium/extensions/code-snippets/extension.php:24,
#: framework/premium/extensions/code-snippets/extension.php:79,
#: framework/premium/extensions/code-snippets/extension.php:122
msgctxt "Extension Brand Name"
msgid "Custom Code Snippets"
msgstr "Đoạn mã tùy chỉnh"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:96
msgctxt "Extension Brand Name"
msgid "Custom Fonts"
msgstr "Fonts tuỳ chỉnh"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:108
msgctxt "Extension Brand Name"
msgid "Local Google Fonts"
msgstr "Local Google Fonts"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:123
msgctxt "Extension Brand Name"
msgid "Advanced Menu"
msgstr "Menu nâng cao"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:136
msgctxt "Extension Brand Name"
msgid "Post Types Extra"
msgstr "Loại nội dung bổ sung"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:179,
#: framework/premium/extensions/shortcuts/config.php:5,
#: framework/premium/extensions/shortcuts/customizer.php:751
msgctxt "Extension Brand Name"
msgid "Shortcuts Bar"
msgstr "Thanh điều hướng nhanh"

#: framework/premium/extensions/shortcuts/customizer.php:314
msgid "Set link to nofollow"
msgstr "Đặt liên kết thành nofollow"

#: framework/premium/extensions/shortcuts/customizer.php:320
msgid "Custom class"
msgstr "Lớp tùy chỉnh"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:194
msgctxt "Extension Brand Name"
msgid "Multiple Sidebars"
msgstr "Nhiều thanh bên"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:214
msgctxt "Extension Brand Name"
msgid "White Label"
msgstr "Gắn nhãn trắng"

#: framework/features/blocks/share-box/options.php:132,
#: framework/features/blocks/socials/options.php:84
msgid "Icons Spacing"
msgstr "Khoảng cách biểu tượng "

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/feature.php:157
msgid "Add widgets here."
msgstr "Thêm tiện ích ở đây."

#. translators: 1: link open 2: link close
#: framework/features/account-auth.php:106
msgid "Check your email for the confirmation link, then visit the %slogin page%s."
msgstr "Kiểm tra email của bạn để lấy liên kết xác nhận, sau đó truy cập %strang đăng nhập%s."

#: framework/features/account-auth.php:226
msgid "Your account was created successfully. Your login details have been sent to your email address. Please visit the %1$slogin page%2$s."
msgstr "Tài khoản của bạn đã được tạo thành công. Chi tiết đăng nhập của bạn đã được gửi đến địa chỉ email của bạn. Vui lòng truy cập %1$strang đăng nhập%2$s."

#: framework/features/account-auth.php:238
msgid "Your account was created successfully and a password has been sent to your email address. Please visit the %1$slogin page%2$s."
msgstr "Tài khoản của bạn đã được tạo thành công và mật khẩu đã được gửi đến địa chỉ email của bạn. Vui lòng truy cập %1$strang đăng nhập%2$s."

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/cookies-consent/config.php:5,
#: framework/extensions/cookies-consent/customizer.php:5
msgctxt "Extension Brand Name"
msgid "Cookies Consent"
msgstr "Đồng ý cookies"

#: framework/extensions/cookies-consent/customizer.php:76
msgid "Accept Button text"
msgstr "Văn bản Nút chấp nhận"

#: framework/extensions/cookies-consent/customizer.php:85
msgid "Decline Button text"
msgstr "Tên nút từ chối"

#: framework/extensions/cookies-consent/customizer.php:88,
#: framework/extensions/cookies-consent/helpers.php:11
msgid "Decline"
msgstr "Từ chối"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/newsletter-subscribe/config.php:5
msgctxt "Extension Brand Name"
msgid "Newsletter Subscribe"
msgstr "Đăng ký bản tin"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/product-reviews/config.php:5
msgctxt "Extension Brand Name"
msgid "Product Reviews"
msgstr "Đánh giá sản phẩm"

#: framework/extensions/product-reviews/metabox.php:60
msgid "Please note that some of this information (price, sku, brand) won't be displayed on the front-end. It is solely used for Google's Schema.org markup."
msgstr "Vui lòng lưu ý rằng một số thông tin này (giá, sku, thương hiệu) sẽ không được hiển thị ở phía trước. Nó chỉ được sử dụng cho đánh dấu Schema.org của Google."

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/trending/config.php:5,
#: framework/extensions/trending/customizer.php:160
msgctxt "Extension Brand Name"
msgid "Trending Posts"
msgstr "Bài viết nổi bật"

#: framework/extensions/trending/customizer.php:477,
#: framework/features/blocks/about-me/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:32,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:133,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:73
msgid "Image Size"
msgstr "Kích thước ảnh"

#: framework/features/header/account-modal.php:25
msgid "Close account modal"
msgstr "Đóng hộp bật lên tài khoản"

#: framework/features/header/header-options.php:68
msgid "Effect"
msgstr "Hiệu ứng"

#: framework/features/header/header-options.php:84
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:73
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:68
msgid "Offset"
msgstr "Độ lệch"

#: framework/premium/features/content-blocks/admin-ui.php:275
msgid "All types"
msgstr "Tất cả loại"

#: framework/premium/features/content-blocks/admin-ui.php:378,
#: framework/premium/features/content-blocks/options/popup.php:82
msgid "After x pages"
msgstr "Sau x trang"

#: framework/premium/features/content-blocks/popups.php:376
msgid "Close popup"
msgstr "Thoát Popup"

#: framework/premium/features/media-video/options.php:13
msgid "Upload"
msgstr "Tải lên"

#: framework/premium/static/js/header/CreateHeader.js:117
msgid "Picker header"
msgstr "Đầu trang bộ chọn"

#: framework/premium/static/js/header/CreateHeader.js:134
#: static/js/header/PanelsManager.js:58
msgid "Global Header"
msgstr "Đầu trang cho toàn website"

#: framework/premium/static/js/header/CreateHeader.js:173
msgid "Create New Header"
msgstr "Tạo đầu trang mới"

#: framework/premium/static/js/header/CreateHeader.js:50
msgid "Create new header"
msgstr "Tạo đầu trang mới"

#: framework/premium/static/js/header/CreateHeader.js:53
msgid "Create a new header and assign it to different pages or posts based on your conditions."
msgstr "Tạo tiêu đề mới và gán nó cho các trang hoặc bài viết khác nhau dựa trên điều kiện của bạn."

#: framework/premium/static/js/header/CreateHeader.js:71
msgid "Header name"
msgstr "Tên đầu trang"

#: framework/premium/static/js/hooks/CodeEditor.js:238
msgid "Yes, continue"
msgstr "Vâng, tiếp tục"

#: framework/premium/static/js/hooks/CodeEditor.js:150
msgid "Use code editor"
msgstr "Sử dụng trình chỉnh sửa mã"

#: framework/premium/static/js/hooks/CodeEditor.js:153
msgid "Exit code editor"
msgstr "Thoát chỉnh sửa mã"

#: framework/premium/static/js/hooks/CodeEditor.js:165
msgid "Heads up!"
msgstr "Hướng lên đầu!"

#: framework/premium/static/js/hooks/CodeEditor.js:167
msgid "Enabling & disabling the code editor will erase everything from your post editor and this action is irreversible."
msgstr "Việc bật và tắt trình chỉnh sửa mã sẽ xóa mọi thứ khỏi trình chỉnh sửa bài viết của bạn và hành động này không thể thay đổi được."

#: framework/premium/static/js/hooks/CodeEditor.js:174
msgid "Are you sure you want to continue?"
msgstr "Bạn có chắc chắn muốn tiếp tục không?"

#: framework/premium/static/js/hooks/CreateHook.js:223
msgid "Create Content Block"
msgstr "Tạo khối nội dung"

#: framework/premium/static/js/hooks/CreateHook.js:36
msgid "Please select the type of your content block and place it in the location you want based on your display and user conditions."
msgstr "Vui lòng chọn loại khối nội dung của bạn và đặt nó ở vị trí bạn muốn dựa trên điều kiện hiển thị và người dùng của bạn."

#: framework/premium/static/js/hooks/CreateHook.js:108
msgid "Select template type..."
msgstr "Chọn loại mẫu..."

#: framework/premium/features/content-blocks/admin-ui.php:262
#: framework/premium/static/js/hooks/CreateHook.js:68
msgid "Custom Content/Hooks"
msgstr "Nội dung/Hooks tùy chỉnh"

#: framework/premium/static/js/hooks/CreateHook.js:148
msgid "404 Page Template"
msgstr "Mẫu trang 404"

#: framework/premium/static/js/hooks/CreateHook.js:132
msgid "Header Template"
msgstr "Mẫu đầu trang"

#: framework/premium/static/js/hooks/CreateHook.js:140
msgid "Footer Template"
msgstr "Mẫu chân trang"

#: framework/premium/static/js/options/IconPicker.js:137
msgid "Change Icon"
msgstr "Thay đổi biểu tượng"

#: framework/premium/static/js/options/IconPicker.js:155
msgid "Remove Icon"
msgstr "Xóa biểu tượng"

#: framework/premium/static/js/options/IconPicker.js:161
msgid "Select"
msgstr "Chọn"

#: framework/premium/static/js/options/IconPicker/Modal.js:148
msgid "Upload Icon"
msgstr "Tải lên biểu tượng"

#: framework/premium/static/js/options/IconPicker/CustomIcon.js:34
msgid "For performance and customization reasons, only SVG files are allowed."
msgstr "Vì lý do hiệu suất và tùy chỉnh, chỉ cho phép các tệp SVG."

#: framework/premium/static/js/options/IconPicker/IconsList.js:24
msgid "Search icon"
msgstr "Biểu tượng tìm kiếm"

#: framework/premium/static/js/options/MultipleLocationsSelect.js:151
msgid "Add New Location"
msgstr "Thêm vị trí mới"

#: framework/premium/static/js/options/MultipleLocationsSelect.js:53
msgid "Select location"
msgstr "Chọn vị trí"

#: static/js/dashboard.js:64
msgid "Starter Sites"
msgstr "Mẫu web bắt đầu"

#: static/js/dashboard.js:76
msgid "Extensions"
msgstr "Tiện ích"

#: static/js/header/EditConditions.js:151
#: static/js/options/DisplayCondition.js:98
msgid "Save Conditions"
msgstr "Lưu điều kiện"

#: static/js/header/EditConditions.js:107
msgid "Add one or more conditions in order to display your header."
msgstr "Thêm một hoặc nhiều điều kiện để hiển thị đầu trang của bạn."

#: static/js/header/PanelsManager.js:174
msgid "Remove header"
msgstr "Xoá đầu trang"

#: static/js/header/PanelsManager.js:198
msgid "Remove Header"
msgstr "Xoá đầu trang"

#: static/js/header/PanelsManager.js:201
msgid "You are about to remove a custom header, are you sure you want to continue?"
msgstr "Bạn sắp xóa tiêu đề tùy chỉnh, bạn có chắc chắn muốn tiếp tục không?"

#: static/js/dashboard/helpers/SubmitSupport.js:18
msgid "Need help or advice?"
msgstr "Cần giúp đỡ hoặc lời khuyên?"

#: static/js/dashboard/helpers/SubmitSupport.js:21
msgid "Got a question or need help with the theme? You can always submit a support ticket or ask for help in our friendly Facebook community."
msgstr "Có câu hỏi hoặc cần giúp đỡ với giao diện? Bạn luôn có thể gửi một vé hỗ trợ hoặc yêu cầu giúp đỡ trong cộng đồng Facebook thân thiện của chúng tôi."

#: static/js/dashboard/helpers/SubmitSupport.js:33
msgid "Submit a Support Ticket"
msgstr "Gửi một Ticket hỗ trợ"

#: static/js/dashboard/helpers/SubmitSupport.js:41
msgid "Join Facebook Community"
msgstr "Tham gia cộng đồng Facebook"

#: static/js/dashboard/helpers/useUpsellModal.js:134
msgid "Upgrade Now"
msgstr "Nâng cấp ngay"

#: static/js/options/ConditionsManager/SingleCondition.js:114
msgid "Select rule"
msgstr "Chọn quy tắc"

#: static/js/options/ConditionsManager/SingleCondition.js:204
msgid "Select taxonomy"
msgstr "Chọn taxonomy"

#: static/js/options/ConditionsManager/SingleCondition.js:236
msgid "Select language"
msgstr "Chọn ngôn ngữ"

#: static/js/options/ConditionsManager/SingleCondition.js:292
msgid "Select user"
msgstr "Chọn người dùng"

#: static/js/options/ConditionsManager/SingleCondition.js:265
msgid "Current user"
msgstr "Người dùng hiện tại"

#: static/js/options/DisplayCondition.js:28
msgid "Add Display Condition"
msgstr "Thêm điều kiện hiển thị"

#: framework/features/conditions-manager.php:242,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:126
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:283
#: static/js/options/ConditionsManager/SingleCondition.js:97
msgid "Include"
msgstr "Bao gồm"

#: framework/features/conditions-manager.php:242,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:127
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:291
#: static/js/options/ConditionsManager/SingleCondition.js:98
msgid "Exclude"
msgstr "Loại trừ "

#: framework/premium/static/js/options/PreviewedPostsSelect.js:170
#: static/js/options/ConditionsManager/PostIdPicker.js:68
msgid "Type to search by ID or title..."
msgstr "Nhập để tìm kiếm theo ID hoặc tiêu đề..."

#: framework/premium/static/js/options/PreviewedPostsSelect.js:174
#: static/js/options/ConditionsManager/PostIdPicker.js:74
msgid "Select post"
msgstr "Chọn bài viết"

#: static/js/options/ConditionsManager/PostIdPicker.js:76
msgid "Select page"
msgstr "Chọn trang"

#: static/js/options/CustomizerOptionsManager.js:113
msgid "Import Options"
msgstr "Nhập tùy chọn"

#: static/js/options/CustomizerOptionsManager.js:116
msgid "Easily import the theme customizer settings."
msgstr "Dễ dàng nhập cài đặt tùy chỉnh giao diện."

#: static/js/options/CustomizerOptionsManager.js:140
msgid "Click or drop to upload a file..."
msgstr "Nhấp hoặc kéo để tải lên một tệp..."

#: static/js/options/CustomizerOptionsManager.js:239
msgid "Import Customizations"
msgstr "Nhập tuỳ chỉnh"

#: static/js/options/CustomizerOptionsManager.js:249
msgid "Copy Options"
msgstr "Sao chép tuỳ chọn"

#: static/js/options/CustomizerOptionsManager.js:252
msgid "Copy and import your customizations from parent or child theme."
msgstr "Sao chép và nhập các tùy chỉnh của bạn từ giao diện chính hoặc giao diện con."

#: static/js/options/CustomizerOptionsManager.js:308
msgid "Copy From Parent Theme"
msgstr "Sao chép từ giao diện chính"

#: static/js/options/CustomizerOptionsManager.js:314
msgid "Copy From Child Theme"
msgstr "Sao chép từ giao diện con"

#: static/js/options/CustomizerOptionsManager.js:321
msgid "You are about to copy all the settings from your parent theme into the child theme. Are you sure you want to continue?"
msgstr "Bạn sắp sao chép tất cả cài đặt từ giao diện cha của mình vào giao diện con. Bạn có chắc chắn muốn tiếp tục không?"

#: static/js/options/CustomizerOptionsManager.js:327
msgid "You are about to copy all the settings from your child theme into the parent theme. Are you sure you want to continue?"
msgstr "Bạn sắp sao chép tất cả cài đặt từ giao diện con của mình vào giao diện cha. Bạn có chắc chắn muốn tiếp tục không?"

#: static/js/options/CustomizerOptionsManager.js:376
msgid "Yes, I am sure"
msgstr "Vâng, Tôi chắc chắn"

#: static/js/options/CustomizerOptionsManager.js:390
msgid "Export Settings"
msgstr "Cài đặt xuất"

#: static/js/options/CustomizerOptionsManager.js:394
msgid "Choose what set of settings you want to export."
msgstr "Chọn bộ cài đặt bạn muốn xuất."

#: static/js/options/CustomizerOptionsManager.js:439
msgid "Customizer settings"
msgstr "Cài đặt tùy chỉnh"

#: static/js/options/CustomizerOptionsManager.js:443
msgid "Widgets settings"
msgstr "Cài đặt tiện ích widget"

#: static/js/options/CustomizerOptionsManager.js:536
msgid "Export"
msgstr "Xuất"

#: static/js/options/CustomizerOptionsManager.js:87
msgid "Export Options"
msgstr "Tùy chọn xuất"

#: static/js/options/CustomizerOptionsManager.js:90
msgid "Easily export the theme customizer settings."
msgstr "Dễ dàng xuất cài đặt tùy chỉnh giao diện."

#: static/js/options/CustomizerOptionsManager.js:107
msgid "Export Customizations"
msgstr "Xuất tuỳ chỉnh"

#: static/js/options/DisplayCondition.js:19
msgid "Transparent Header Display Conditions"
msgstr "Điều kiện hiển thị đầu trang trong suốt"

#: static/js/options/DisplayCondition.js:23
msgid "Add one or more conditions to display the transparent header."
msgstr "Thêm một hoặc nhiều điều kiện để hiển thị đầu trang trong suốt."

#: static/js/dashboard/screens/DemoInstall.js:166
msgid "Loading Starter Sites..."
msgstr "Đang tải mẫu web bắt đầu..."

#: static/js/dashboard/screens/DemoInstall/components/Progress.js:29
msgid "Installing"
msgstr "Đang cài đặt..."

#: static/js/dashboard/screens/DemoInstall/components/Progress.js:34
msgid "Please be patient and don't refresh this page, the import process may take a while, this also depends on your server."
msgstr "Hãy kiên nhẫn và đừng làm mới trang này, quá trình nhập có thể mất một lúc, điều này cũng phụ thuộc vào máy chủ của bạn."

#: static/js/dashboard/screens/DemoInstall/DemoToInstall.js:306
msgid "Back"
msgstr "Quay lại"

#: static/js/dashboard/screens/DemoInstall/DemoToInstall.js:346
msgid "Install"
msgstr "Cài đặt"

#: static/js/dashboard/screens/DemoInstall/DemoToInstall.js:347
msgid "Next"
msgstr "Tiếp theo"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:182
msgid "Import"
msgstr "Nhập"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:127
msgid "Available for"
msgstr "Có sẵn cho"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:166
msgid "Preview"
msgstr "Xem thử"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:181
msgid "Modify"
msgstr "Sửa đổi"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:22
msgid "Starter Site Imported Successfully"
msgstr "Đã nhập thành công mẫu web bắt đầu"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:25
msgid "Now you can view your website or start customizing it"
msgstr "Bây giờ bạn có thể xem trang web của mình hoặc bắt đầu tùy chỉnh nó"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:33
#: static/js/dashboard/screens/Extensions/CurrentExtension.js:342
msgid "Customize"
msgstr "Tùy chỉnh"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:40
msgid "View site"
msgstr "Xem trang web"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:12
msgid "copying child theme sources"
msgstr "sao chép các nguồn giao diện con"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:16
msgid "activating child theme"
msgstr "kích hoạt giao diện con"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:21
msgid "installing plugin %s"
msgstr "cài đặt plugin %s"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:28
msgid "activating plugin %s"
msgstr "kích hoạt plugin %s"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:34
msgid "downloading demo widgets"
msgstr "tải xuống các tiện ích demo"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:38
msgid "installing demo widgets"
msgstr "cài đặt các tiện ích demo"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:42
msgid "downloading demo options"
msgstr "tải xuống các tùy chọn demo"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:46
msgid "importing images from customizer"
msgstr "nhập hình ảnh từ tùy biến"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:50
msgid "import customizer options"
msgstr "nhập các tùy chọn tùy biến"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:54
msgid "activating required extensions"
msgstr "kích hoạt các tiện ích bắt buộc"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:58
msgid "removing previously installed posts"
msgstr "xóa các bài viết đã cài đặt trước đó"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:62
msgid "removing previously installed taxonomies"
msgstr "xoá các phân loại đã cài đặt trước đó"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:66
msgid "removing default WordPress pages"
msgstr "xóa các trang WordPress mặc định"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:70
msgid "resetting customizer options"
msgstr "đặt lại các tùy chọn tùy biến"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:74
msgid "resetting widgets"
msgstr "đặt lại các tiện ích"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:89
msgid "users"
msgstr "Người dùng"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:90
msgid "terms"
msgstr "Điều khoản"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:91
msgid "images"
msgstr "Hình ảnh"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:92
msgid "posts"
msgstr "bài viết"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:93
msgid "comments"
msgstr "bình luận"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:118
msgid "Child theme"
msgstr "Giao diện con"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:143
#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:70
msgid "Erase content"
msgstr "Xóa nội dung"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:185
msgid "Final touches"
msgstr "Chỉnh sửa cuối"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:155
msgid "Import options"
msgstr "Tùy chọn nhập"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:168
msgid "Import widgets"
msgstr "Nhập tiện ích"

#: static/js/dashboard/screens/DemoInstall/Installer/contentCalculation.js:9
msgid "Import content"
msgstr "Nhập nội dung"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:108
msgid "You already have a child theme properly installed and activated. Move on."
msgstr "Bạn đã có một giao diện con được cài đặt và kích hoạt đúng cách. Tiến lên."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:116
msgid "Learn more about child themes"
msgstr "Tìm hiểu thêm về Giao diện con"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:70
msgid "We strongly recommend to install the child theme, this way you will have freedom to make changes without breaking the parent theme."
msgstr "Chúng tôi thực sự khuyên bạn nên cài đặt giao diện con, theo cách này bạn sẽ có quyền tự do thực hiện các thay đổi mà không phá vỡ giao diện cha."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:77
msgid "We strongly recommend to activate the child theme, this way you will have freedom to make changes without breaking the parent theme."
msgstr "Chúng tôi thực sự khuyên bạn nên kích hoạt giao diện con, theo cách này, bạn sẽ có quyền tự do thực hiện các thay đổi mà không phá vỡ giao diện cha."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:97
msgid "Install Child Theme"
msgstr "Cài đặt giao diện con"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:98
msgid "Activate Child Theme"
msgstr "Kích hoạt Giao diện con"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:56
msgid "Import Content"
msgstr "Nhập nội dung"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:59
msgid "This will import posts, pages, comments, navigation menus, custom fields, terms and custom posts."
msgstr "Điều này sẽ nhập các bài viết, trang, bình luận, menu điều hướng, trường tùy chỉnh, điều khoản và bài viết tùy chỉnh."

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:104
msgid "Clean Install"
msgstr "Cài đặt sạch"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:106
msgid "This option will remove the previous imported content and will perform a fresh and clean install."
msgstr "Tùy chọn này sẽ xóa nội dung đã nhập trước đó và sẽ thực hiện cài đặt mới và sạch."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:147
msgid "This starter site is already installed"
msgstr "Mẫu web bắt đầu đã được cài đặt"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:156
msgid "Starter Site Removed"
msgstr "Xoá mẫu web bắt đầu"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:165
msgid "Dismiss"
msgstr "Bỏ qua"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:174
msgid "What steps do you want to perform next?"
msgstr "Bạn muốn thực hiện những bước nào tiếp theo?"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:191
msgid "Remove"
msgstr "Gỡ bỏ"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:200
msgid "Reinstall"
msgstr "Cài đặt lại"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:55
msgid "Deactivate demo plugins"
msgstr "Hủy kích hoạt các plugin demo"

#: static/js/dashboard/screens/DemoInstall/Wizzard/PickBuilder.js:62
msgid "Choose Page Builder"
msgstr "Chọn Trình tạo Trang"

#: static/js/dashboard/screens/DemoInstall/Wizzard/PickBuilder.js:65
msgid "This starter site can be imported and used with one of these page builders. Please select one in order to continue."
msgstr "Mẫu web bắt đầu này có thể được nhập và sử dụng với một trong những trình xây dựng trang này. Vui lòng chọn một để tiếp tục."

#: static/js/dashboard/screens/DemoInstall/Wizzard/Plugins.js:71
msgid "Install & Activate Plugins"
msgstr "Cài và Kích hoạt Plugin"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Plugins.js:73
msgid "The following plugins are required for this starter site in order to work properly."
msgstr "Các plugin sau là bắt buộc đối với mẫu web bắt đầu để hoạt động bình thường."

#: static/js/dashboard/screens/Extension.js:95
#: static/js/dashboard/screens/Extensions.js:86
msgid "Loading Extensions Status..."
msgstr "Đang tải Trạng thái Tiện ích..."

#: static/js/dashboard/screens/Extensions/Sidebar.js:60
msgid "Free Extensions"
msgstr "Tiện ích miễn phí"

#: static/js/dashboard/screens/Extensions/Sidebar.js:68
msgid "Pro Extensions"
msgstr "Tiện ích Pro"

#: static/js/dashboard/screens/SiteExport.js:239
msgid "Builder"
msgstr "Builder"

#: static/js/dashboard/screens/SiteExport.js:311
msgid "Export site"
msgstr "Xuất trang web"

#: framework/premium/extensions/mega-menu/extension.php:325
msgid "New"
msgstr "Mới"

#: framework/premium/extensions/mega-menu/options.php:16,
#: framework/premium/extensions/mega-menu/options.php:581
msgid "Mega Menu Settings"
msgstr "Cài đặt Mega Menu"

#: framework/premium/extensions/mega-menu/options.php:28
msgid "Dropdown Width"
msgstr "Chiều rộng Menu thả xuống"

#: framework/premium/extensions/mega-menu/options.php:36,
#: framework/premium/extensions/mega-menu/options.php:49
msgid "Content Width"
msgstr "Chiều rộng nội dung"

#: framework/premium/extensions/mega-menu/options.php:37,
#: framework/premium/extensions/mega-menu/options.php:58
msgid "Full Width"
msgstr "Toàn màn hình"

#: framework/premium/extensions/mega-menu/options.php:38,
#: framework/premium/features/content-blocks/options/archive.php:86
msgid "Custom Width"
msgstr "Chiều rộng tùy chỉnh"

#: framework/premium/extensions/mega-menu/options.php:57
msgid "Default Width"
msgstr "Chiều rộng mặc định"

#: framework/premium/extensions/mega-menu/options.php:86,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:178,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:197
msgid "Columns"
msgstr "Cột"

#: framework/premium/extensions/mega-menu/options.php:333
msgid "Custom Content"
msgstr "Nội dung tùy chỉnh"

#: framework/premium/extensions/mega-menu/options.php:337
msgid "Content Type"
msgstr "Loại nội dung"

#: framework/premium/extensions/mega-menu/options.php:344
msgid "Default (Menu Item)"
msgstr "Mặc định (Mục menu)"

#: framework/premium/extensions/mega-menu/options.php:345
msgid "Custom Text"
msgstr "Văn bản tùy chỉnh"

#: framework/premium/extensions/mega-menu/options.php:432,
#: framework/premium/extensions/mega-menu/options.php:791
msgid "Item Label Settings"
msgstr "Cài đặt nhãn mục"

#: framework/premium/extensions/mega-menu/options.php:437
msgid "Item Label"
msgstr "Nhãn mục"

#: framework/premium/extensions/mega-menu/options.php:448
msgid "Enabled"
msgstr "Bật"

#: framework/premium/extensions/mega-menu/options.php:449,
#: framework/premium/features/content-blocks/options/404.php:111,
#: framework/premium/features/content-blocks/options/archive.php:188,
#: framework/premium/features/content-blocks/options/header.php:129,
#: framework/premium/features/content-blocks/options/hook.php:238,
#: framework/premium/features/content-blocks/options/maintenance.php:108,
#: framework/premium/features/content-blocks/options/nothing_found.php:129,
#: framework/premium/features/content-blocks/options/single.php:120
msgid "Disabled"
msgstr "Đã vô hiệu hóa"

#: framework/premium/extensions/mega-menu/options.php:450
msgid "Heading"
msgstr "Tiêu đề"

#: framework/premium/extensions/mega-menu/options.php:456
msgid "Label Link"
msgstr "Liên kết nhãn"

#: framework/extensions/trending/customizer.php:496,
#: framework/premium/extensions/mega-menu/options.php:550,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:154,
#: framework/premium/features/premium-header/items/contacts/options.php:526,
#: framework/premium/features/premium-header/items/language-switcher/options.php:105,
#: framework/premium/features/premium-header/items/search-input/options.php:223
msgid "Vertical Alignment"
msgstr "Căn chỉnh dọc"

#: framework/features/header/header-options.php:209,
#: framework/features/header/header-options.php:236,
#: framework/features/header/header-options.php:251,
#: framework/features/header/header-options.php:266,
#: framework/premium/extensions/mega-menu/options.php:585,
#: framework/premium/extensions/shortcuts/customizer.php:1181,
#: framework/premium/extensions/shortcuts/customizer.php:1223,
#: framework/premium/extensions/shortcuts/customizer.php:1265,
#: framework/features/header/items/account/options.php:1733,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:37,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:77,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:325,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:196,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:541,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:577,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:611,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:480,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:512,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:542
msgid "Background"
msgstr "Nền"

#: framework/premium/extensions/mega-menu/options.php:599
msgid "Link Color"
msgstr "Màu liên kết"

#: framework/premium/extensions/mega-menu/options.php:619,
#: framework/features/header/items/account/options.php:1933,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:634,
#: framework/premium/features/premium-header/items/contacts/options.php:641,
#: framework/premium/features/premium-header/items/contacts/options.php:681,
#: framework/premium/features/premium-header/items/contacts/options.php:720
#: static/js/editor/blocks/breadcrumbs/Edit.js:60
#: static/js/editor/blocks/contact-info/Edit.js:114
msgid "Link Initial"
msgstr "Liên kết ban đầu"

#: framework/premium/extensions/mega-menu/options.php:624
msgid "Link Hover/Active"
msgstr "Di chuột/Hoạt động liên kết"

#: framework/premium/extensions/mega-menu/options.php:629,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:149
msgid "Background Hover"
msgstr "Di chuột qua nền"

#: framework/premium/extensions/mega-menu/options.php:646,
#: framework/premium/extensions/mega-menu/options.php:803
msgid "Heading Color"
msgstr "Mầu tiêu đề"

#: framework/premium/extensions/mega-menu/options.php:658,
#: framework/premium/extensions/mega-menu/options.php:677,
#: framework/premium/extensions/mega-menu/options.php:815
msgid "Initial Color"
msgstr "Màu ban đầu"

#: framework/extensions/cookies-consent/customizer.php:147,
#: framework/extensions/newsletter-subscribe/customizer.php:196,
#: framework/premium/extensions/mega-menu/options.php:665,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:342
#: static/js/editor/blocks/about-me/Edit.js:89
#: static/js/editor/blocks/breadcrumbs/Edit.js:43
#: static/js/editor/blocks/contact-info/Edit.js:96
msgid "Text Color"
msgstr "Màu chữ"

#: framework/premium/extensions/mega-menu/options.php:684,
#: framework/premium/extensions/shortcuts/customizer.php:1282,
#: framework/premium/features/premium-header/items/search-input/options.php:904,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:161
msgid "Items Divider"
msgstr "Dải phân cách các mục"

#: framework/premium/extensions/mega-menu/options.php:699
msgid "Columns Divider"
msgstr "Dải phân cách cột"

#: framework/premium/extensions/mega-menu/options.php:714,
#: framework/premium/features/premium-header/items/search-input/options.php:886
msgid "Dropdown Shadow"
msgstr "Bóng Menu thả xuống"

#: framework/premium/extensions/mega-menu/options.php:746
msgid "Column Settings"
msgstr "Cài đặt cột"

#: framework/premium/extensions/mega-menu/options.php:750
msgid "Column Spacing"
msgstr "Khoảng cách cột"

#: framework/premium/extensions/mega-menu/options.php:825,
#: framework/features/header/items/account/options.php:1327,
#: framework/features/header/items/account/options.php:1366,
#: framework/features/header/items/account/options.php:1409,
#: framework/features/header/items/account/options.php:1450,
#: framework/features/header/items/account/options.php:1738,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:300,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:328,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:359,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:388,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:331,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:213,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:353,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:382,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:413,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:442,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:354,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:382,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:417,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:450,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:308,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:336,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:367,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:396
#: static/js/editor/blocks/share-box/Edit.js:81
#: static/js/editor/blocks/socials/Edit.js:81
msgid "Icon Color"
msgstr "Màu biểu tượng"

#: framework/helpers/exts-configs.php:137
msgid "Enables support for Custom Fields inside archive cards and single page post title, adds a reading progress bar for your posts and lets you set featured images and colors for your categories archives."
msgstr "Kích hoạt hỗ trợ cho trường tùy chỉnh bên trong thẻ lưu trữ và tiêu đề bài đăng trên trang đơn, thêm thanh tiến trình đọc cho bài viết của bạn và cho phép bạn đặt hình ảnh nổi bật và màu sắc cho lưu trữ danh mục của bạn."

#: framework/helpers/exts-configs.php:180,
#: framework/premium/extensions/shortcuts/config.php:6
msgid "Easily turn your websites into mobile first experiences. You can easily add the most important actions at the bottom of the screen for easy access."
msgstr "Dễ dàng biến trang web của bạn thành trải nghiệm di động trước. Bạn có thể dễ dàng thêm các hành động quan trọng nhất ở phía dưới màn hình để dễ dàng truy cập."

#: framework/premium/extensions/shortcuts/customizer.php:12,
#: framework/premium/extensions/shortcuts/customizer.php:38,
#: framework/premium/extensions/shortcuts/customizer.php:790,
#: framework/premium/extensions/shortcuts/extension.php:173,
#: framework/premium/extensions/shortcuts/views/bar.php:13,
#: framework/premium/extensions/shortcuts/views/bar.php:46
msgid "Home"
msgstr "Trang chủ"

#: framework/features/blocks/contact-info/options.php:139,
#: framework/premium/extensions/shortcuts/customizer.php:71,
#: framework/premium/extensions/shortcuts/customizer.php:97,
#: framework/premium/extensions/shortcuts/customizer.php:799,
#: framework/premium/extensions/shortcuts/extension.php:182,
#: framework/premium/extensions/shortcuts/views/bar.php:22,
#: framework/premium/extensions/shortcuts/views/bar.php:47,
#: framework/premium/features/premium-header/items/contacts/options.php:127
msgid "Phone"
msgstr "Điện Thoại"

#: framework/premium/extensions/shortcuts/customizer.php:201,
#: framework/premium/extensions/shortcuts/customizer.php:227,
#: framework/premium/extensions/shortcuts/views/bar.php:49
msgid "Scroll Top"
msgstr "Cuộn lên đầu"

#: framework/premium/extensions/shortcuts/customizer.php:352,
#: framework/premium/extensions/shortcuts/customizer.php:378,
#: framework/premium/extensions/shortcuts/views/bar.php:50
msgid "Cart"
msgstr "Giỏ hàng"

#: framework/premium/extensions/shortcuts/customizer.php:411,
#: framework/premium/extensions/shortcuts/customizer.php:437
msgid "Shop"
msgstr "Của hàng"

#: framework/helpers/exts-configs.php:262,
#: framework/premium/extensions/shortcuts/customizer.php:545,
#: framework/premium/extensions/shortcuts/customizer.php:571,
#: framework/premium/extensions/shortcuts/views/bar.php:52,
#: framework/features/header/items/account/views/login.php:520,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:309,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:406,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:410,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:438,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:442,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/helpers.php:168,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:17,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/config.php:4,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:141,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/view.php:100
msgid "Wishlist"
msgstr "Yêu thích"

#: framework/premium/extensions/shortcuts/customizer.php:784
msgid "Shortcuts"
msgstr "Mã ngắn"

#: framework/premium/extensions/shortcuts/customizer.php:839,
#: framework/features/header/items/account/options.php:520,
#: framework/features/header/items/account/options.php:983,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:38,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:91,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:92
msgid "Label Visibility"
msgstr "Hiển thị nhãn"

#: framework/premium/extensions/shortcuts/customizer.php:879,
#: framework/features/header/items/account/options.php:554,
#: framework/features/header/items/account/options.php:1017,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:88,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:122,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:123
msgid "Label Position"
msgstr "Vị trí nhãn"

#: framework/premium/extensions/shortcuts/customizer.php:888,
#: framework/premium/features/content-blocks/hooks-manager.php:477,
#: framework/features/header/items/account/options.php:563,
#: framework/features/header/items/account/options.php:1035,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:97,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:21,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:131,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:132
msgid "Bottom"
msgstr "Dưới cùng"

#: framework/premium/extensions/mega-menu/options.php:482,
#: framework/premium/extensions/shortcuts/customizer.php:925,
#: framework/features/header/items/account/options.php:508,
#: framework/features/header/items/account/options.php:882,
#: framework/features/header/items/account/options.php:968,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:46,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:439,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:202,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:42,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:73,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:74
msgid "Icon Size"
msgstr "Kích thước biểu tượng"

#: framework/premium/extensions/shortcuts/customizer.php:936,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:383
msgid "Container Height"
msgstr "Chiều cao vùng chứa"

#: framework/premium/extensions/shortcuts/customizer.php:951
msgid "Container Max Width"
msgstr "Chiều rộng tối đa vùng chứa"

#: framework/premium/extensions/shortcuts/customizer.php:975
msgid "Scroll Interaction"
msgstr "Tương tác cuộn"

#: framework/premium/extensions/shortcuts/customizer.php:981
msgid "Hide"
msgstr "Ẩn"

#: framework/premium/extensions/shortcuts/customizer.php:1023
msgid "Shortcuts Bar Display Conditions"
msgstr "Điều kiện hiển thị thanh lối tắt"

#: framework/premium/extensions/shortcuts/customizer.php:1024
msgid "Add one or more conditions to display the shortcuts bar."
msgstr "Thêm một hoặc nhiều điều kiện để hiển thị thanh lối tắt"

#: framework/premium/extensions/shortcuts/customizer.php:1048,
#: framework/features/header/items/account/options.php:1895,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:184,
#: framework/premium/features/premium-header/items/contacts/options.php:577,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:105,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:212,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:42
msgid "Font"
msgstr "Phông chữ"

#: framework/features/blocks/about-me/options.php:187,
#: framework/features/blocks/share-box/options.php:141,
#: framework/features/blocks/socials/options.php:93,
#: framework/premium/extensions/shortcuts/customizer.php:1092,
#: framework/premium/features/premium-header/items/contacts/options.php:751,
#: framework/premium/features/premium-header/items/contacts/options.php:780,
#: framework/premium/features/premium-header/items/contacts/options.php:811,
#: framework/premium/features/premium-header/items/contacts/options.php:840
#: static/js/editor/blocks/about-me/Edit.js:117
#: static/js/editor/blocks/contact-info/Edit.js:133
msgid "Icons Color"
msgstr "Màu biểu tượng"

#: framework/premium/extensions/shortcuts/customizer.php:1163
msgid "Cart Badge Color"
msgstr "Màu huy hiệu giỏ hàng"

#: framework/premium/extensions/shortcuts/customizer.php:1302
msgid "Items Divider Height"
msgstr "Chiều cao dải phân cách mục"

#: framework/premium/extensions/shortcuts/customizer.php:1316,
#: framework/features/header/items/account/options.php:2016,
#: framework/premium/features/content-blocks/options/popup.php:545,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:701,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:150,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:175
msgid "Shadow"
msgstr "Bóng"

#: framework/helpers/exts-configs.php:195
msgid "Create unlimited personalized sets of widget areas and display them on any page or post using our conditional logic functionality."
msgstr "Tạo không giới hạn các bộ khu vực tiện ích cá nhân hóa và hiển thị chúng trên bất kỳ trang hoặc bài viết nào bằng cách sử dụng chức năng logic có điều kiện của chúng tôi."

#: framework/helpers/exts-configs.php:199
msgid "Create New Sidebar"
msgstr "Tạo Thanh bên mới"

#: framework/premium/extensions/sidebars/form.php:3
#: framework/premium/extensions/sidebars/static/js/main.js:50
msgid "Create Sidebar/Widget Area"
msgstr "Tạo Thanh bên/Khu vực tiện ích"

#: framework/premium/extensions/sidebars/form.php:6
msgid "In order to create a new sidebar/widget area simply enter a name in the input below and click the Create Sidebar button."
msgstr "Để tạo một vùng tiện ích/thanh bên mới, chỉ cần nhập tên vào ô dưới đây và nhấp vào nút Tạo thanh bên."

#: framework/premium/extensions/sidebars/form.php:16
#: framework/premium/extensions/sidebars/static/js/main.js:66
msgid "Create Sidebar"
msgstr "Tạo thanh bên"

#: framework/premium/extensions/sidebars/form.php:20
msgid "Available Sidebars/Widget Areas"
msgstr "Thanh bên/ Khu vực tiện ích khả dụng"

#: framework/helpers/exts-configs.php:215
msgid "Replace Blocksy's branding with your own. Easily hide licensing info and other sections of the theme and companion plugin from your clients and make your final product look more professional."
msgstr "Thay thế thương hiệu Blocksy bằng thương hiệu của bạn. Dễ dàng ẩn thông tin giấy phép và các phần khác của chủ đề và plugin đi kèm từ khách hàng của bạn và làm cho sản phẩm cuối cùng của bạn trông chuyên nghiệp hơn."

#: framework/helpers/exts-configs.php:228
msgid "Make the shopping experience better for your visitors! Add features such as Product Quick View, Wishlist functionality and a Floating Add to Cart button. Customize the single product gallery/slider and the layout."
msgstr "Làm cho trải nghiệm mua sắm của khách truy cập tốt hơn! Thêm các tính năng như Xem nhanh sản phẩm, chức năng danh sách mong muốn và nút Thêm vào giỏ hàng nổi. Tùy chỉnh bộ sưu tập/slide sản phẩm đơn và bố cục."

#: framework/premium/extensions/woocommerce-extra/features/quick-view/feature.php:14
msgid "Quick View Button"
msgstr "Nút xem nhanh"

#: framework/features/header/items/account/options.php:467,
#: framework/features/header/items/account/options.php:841,
#: framework/features/header/items/account/options.php:929,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/archive-card/feature.php:13,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:66,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:111,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:65,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:66
msgid "Type 3"
msgstr "Loại 3"

#: framework/features/header/items/account/options.php:477,
#: framework/features/header/items/account/options.php:851,
#: framework/features/header/items/account/options.php:939,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:71,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:116
msgid "Type 4"
msgstr "Loại 4"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/views/panel.php:61
msgid "Available Filters"
msgstr "Bộ lọc khả dụng"

#: framework/premium/extensions/woocommerce-extra/extension.php:128
msgid "Quick view title before"
msgstr "Xem nhanh tiêu đề trước"

#: framework/premium/extensions/woocommerce-extra/extension.php:133
msgid "Quick view title after"
msgstr "Xem nhanh tiêu đề sau"

#: framework/premium/extensions/woocommerce-extra/extension.php:138
msgid "Quick view price before"
msgstr "Xem nhanh giá trước"

#: framework/premium/extensions/woocommerce-extra/extension.php:143
msgid "Quick view price after"
msgstr "Xem nhanh giá sau"

#: framework/premium/extensions/woocommerce-extra/extension.php:148
msgid "Quick view summary before"
msgstr "Xem nhanh tóm tắt trước"

#: framework/premium/extensions/woocommerce-extra/extension.php:153
msgid "Quick view summary after"
msgstr "Xem nhanh tóm tắt sau"

#: framework/helpers/exts-configs.php:238,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:4
msgid "Floating Cart"
msgstr "Giỏ hàng nổi"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:15
msgid "Position"
msgstr "Vị Trí"

#: framework/premium/features/content-blocks/hooks-manager.php:439,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:20
msgid "Top"
msgstr "Đầu"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:68
msgid "Product Title Visibility"
msgstr "Hiển thị tiêu đề sản phẩm"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:30
msgid "Floating Cart Visibility"
msgstr "Hiển thị giỏ hàng nổi"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/view.php:77
msgid "Go to product page"
msgstr "Đi đến trang sản phẩm"

#: framework/premium/extensions/shortcuts/customizer.php:507,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/helpers.php:40,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:111
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:108
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:137
msgid "Filter"
msgstr "Bộ lọc"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:532
msgid "Filter Widgets"
msgstr "Lọc tiện ích"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:536
msgid "Widgets"
msgstr "Các tiện ích"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:580
msgid "Widgets Vertical Spacing"
msgstr "Khoảng cách dọc tiện ích"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:598
msgid "Widgets Font"
msgstr "Màu phông chữ tiện ích"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:606
msgid "Widgets Font Color"
msgstr "Màu phông chữ tiện ích"

#: framework/features/header/items/account/options.php:1927,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:134,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:628,
#: framework/premium/features/premium-header/items/contacts/options.php:635,
#: framework/premium/features/premium-header/items/contacts/options.php:676,
#: framework/premium/features/premium-header/items/contacts/options.php:715
msgid "Text Initial"
msgstr "Văn bản ban đầu"

#: framework/features/header/items/account/options.php:1939,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:639,
#: framework/premium/features/premium-header/items/contacts/options.php:647,
#: framework/premium/features/premium-header/items/contacts/options.php:687,
#: framework/premium/features/premium-header/items/contacts/options.php:726
#: static/js/editor/blocks/breadcrumbs/Edit.js:66
#: static/js/editor/blocks/contact-info/Edit.js:123
msgid "Link Hover"
msgstr "Di chuột vào liên kết"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:123
msgid "Panel Reveal"
msgstr "Hiển thị Bảng điều khiển"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:131,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:39
msgid "Left Side"
msgstr "Bên trái"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:132,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:40
msgid "Right Side"
msgstr "Bên phải"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:137,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:45
msgid "Panel Width"
msgstr "Chiều Rộng Bảng điều khiển"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:295,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:163
msgid "Panel Shadow"
msgstr "Bóng Bảng điều khiển"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:263,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:76
msgid "Panel Background"
msgstr "Nền bảng điều khiển"

#: framework/premium/features/content-blocks/options/popup.php:615,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:779
msgid "Close Icon Color"
msgstr "Mầu biểu tượng thoát"

#: framework/premium/features/content-blocks/options/popup.php:646
msgid "Close Icon Background"
msgstr "Nền biểu tượng thoát"

#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:175
#: static/js/editor/blocks/share-box/index.js:47
msgid "Share Box"
msgstr "Hộp chia sẻ"

#: framework/premium/features/content-blocks/hooks-manager.php:11
msgid "WP head"
msgstr "WP head"

#: framework/premium/features/content-blocks/hooks-manager.php:13,
#: framework/premium/features/content-blocks/hooks-manager.php:22,
#: framework/premium/features/content-blocks/hooks-manager.php:32,
#: framework/premium/features/content-blocks/hooks-manager.php:41
msgid "Head"
msgstr "Đầu"

#: framework/premium/features/content-blocks/hooks-manager.php:20
msgid "WP head start"
msgstr "WP head start"

#: framework/premium/features/content-blocks/hooks-manager.php:30
msgid "WP head end"
msgstr "WP head end"

#: framework/premium/features/content-blocks/hooks-manager.php:48
msgid "Header before"
msgstr "Đầu trang trước"

#: framework/premium/features/content-blocks/admin-ui.php:265,
#: framework/premium/features/content-blocks/hooks-manager.php:50,
#: framework/premium/features/content-blocks/hooks-manager.php:59
msgid "Header"
msgstr "Đầu trang"

#: framework/premium/features/content-blocks/hooks-manager.php:57
msgid "Header after"
msgstr "Đầu trang sau"

#: framework/premium/features/content-blocks/hooks-manager.php:66
msgid "Desktop top"
msgstr "Đầu máy tính bàn"

#: framework/premium/features/content-blocks/hooks-manager.php:68,
#: framework/premium/features/content-blocks/hooks-manager.php:77,
#: framework/premium/features/content-blocks/hooks-manager.php:86,
#: framework/premium/features/content-blocks/hooks-manager.php:95
msgid "Header offcanvas"
msgstr "Đầu trang ngoài khung"

#: framework/premium/features/content-blocks/hooks-manager.php:75
msgid "Desktop bottom"
msgstr "Dưới máy tính bàn"

#: framework/premium/features/content-blocks/hooks-manager.php:84
msgid "Mobile top"
msgstr "Đầu di động"

#: framework/premium/features/content-blocks/hooks-manager.php:93
msgid "Mobile bottom"
msgstr "Dưới di động"

#: framework/premium/features/content-blocks/hooks-manager.php:102
msgid "Sidebar before"
msgstr "Thanh bên trước"

#: framework/premium/features/content-blocks/hooks-manager.php:103,
#: framework/premium/features/content-blocks/hooks-manager.php:110,
#: framework/premium/features/content-blocks/hooks-manager.php:117,
#: framework/premium/features/content-blocks/hooks-manager.php:124
msgid "Left/Right sidebar"
msgstr "Thanh bên trái/phải"

#: framework/premium/features/content-blocks/hooks-manager.php:109
msgid "Sidebar start"
msgstr "Bắt đầu thanh bên"

#: framework/premium/features/content-blocks/hooks-manager.php:116
msgid "Sidebar end"
msgstr "Kết thúc thanh bên"

#: framework/premium/features/content-blocks/hooks-manager.php:123
msgid "Sidebar after"
msgstr "Thanh bên sau"

#: framework/premium/features/content-blocks/hooks-manager.php:130
msgid "Dynamic sidebar before"
msgstr "Thanh bên động trước nội dung"

#: framework/premium/features/content-blocks/hooks-manager.php:131,
#: framework/premium/features/content-blocks/hooks-manager.php:138,
#: framework/premium/features/content-blocks/hooks-manager.php:146
msgid "All widget areas"
msgstr "Tất cả khu vực tiện ích"

#: framework/premium/features/content-blocks/hooks-manager.php:137
msgid "Dynamic sidebar"
msgstr "Thanh bên động"

#: framework/premium/features/content-blocks/hooks-manager.php:145
msgid "Dynamic sidebar after"
msgstr "Thanh bên động sau nội dung"

#: framework/premium/features/content-blocks/hooks-manager.php:154
msgid "Before section"
msgstr "Trước phần"

#: framework/premium/features/content-blocks/hooks-manager.php:155,
#: framework/premium/features/content-blocks/hooks-manager.php:163,
#: framework/premium/features/content-blocks/hooks-manager.php:171,
#: framework/premium/features/content-blocks/hooks-manager.php:179,
#: framework/premium/features/content-blocks/hooks-manager.php:187,
#: framework/premium/features/content-blocks/hooks-manager.php:195,
#: framework/premium/features/content-blocks/hooks-manager.php:203,
#: framework/premium/features/content-blocks/hooks-manager.php:211,
#: framework/premium/features/content-blocks/hooks-manager.php:219,
#: framework/premium/features/content-blocks/hooks-manager.php:227,
#: framework/premium/features/content-blocks/hooks-manager.php:235,
#: framework/premium/features/content-blocks/hooks-manager.php:243,
#: framework/premium/features/content-blocks/hooks-manager.php:251,
#: framework/premium/features/content-blocks/hooks-manager.php:259
msgid "Page/post title"
msgstr "Tiêu đề trang/bài viết"

#: framework/premium/features/content-blocks/hooks-manager.php:162,
#: framework/premium/features/content-blocks/hooks-manager.php:312,
#: framework/premium/features/content-blocks/hooks-manager.php:354
msgid "Before title"
msgstr "Trước tiêu đề"

#: framework/premium/features/content-blocks/hooks-manager.php:186
msgid "Before post meta"
msgstr "Trước meta bài viết"

#: framework/premium/features/content-blocks/hooks-manager.php:210,
#: framework/premium/features/content-blocks/hooks-manager.php:319,
#: framework/premium/features/content-blocks/hooks-manager.php:361
msgid "After title"
msgstr "Sau tiêu đề"

#: framework/premium/features/content-blocks/hooks-manager.php:250
msgid "After post meta"
msgstr "Sau meta bài viết"

#: framework/premium/features/content-blocks/hooks-manager.php:258
msgid "After section"
msgstr "Sau phần section"

#: framework/premium/features/content-blocks/hooks-manager.php:266
msgid "Before content"
msgstr "Trước nội dung"

#: framework/premium/features/content-blocks/hooks-manager.php:274,
#: framework/premium/features/content-blocks/hooks-manager.php:447
msgid "Top content"
msgstr "Đầu Nội dung"

#: framework/premium/features/content-blocks/hooks-manager.php:282,
#: framework/premium/features/content-blocks/hooks-manager.php:469
msgid "Bottom content"
msgstr "Trước nội dung"

#: framework/premium/features/content-blocks/hooks-manager.php:290
msgid "After content"
msgstr "Sau nội dung"

#: framework/premium/features/content-blocks/hooks-manager.php:298
msgid "Before comments"
msgstr "Trước bình luận"

#: framework/premium/features/content-blocks/hooks-manager.php:299,
#: framework/premium/features/content-blocks/hooks-manager.php:306,
#: framework/premium/features/content-blocks/hooks-manager.php:313,
#: framework/premium/features/content-blocks/hooks-manager.php:320,
#: framework/premium/features/content-blocks/hooks-manager.php:327,
#: framework/premium/features/content-blocks/hooks-manager.php:334
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:60
msgid "Comments"
msgstr "Bình luận"

#: framework/premium/features/content-blocks/hooks-manager.php:305
msgid "Top comments"
msgstr "Bình luận hàng đầu"

#: framework/premium/features/content-blocks/hooks-manager.php:326
msgid "Bottom comments"
msgstr "Bình luận dưới cùng"

#: framework/premium/features/content-blocks/hooks-manager.php:333
msgid "After comments"
msgstr "Sau bình luận"

#: framework/premium/features/content-blocks/hooks-manager.php:411,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:705
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:298
msgid "Before"
msgstr "Đằng trước"

#: framework/premium/features/content-blocks/hooks-manager.php:412,
#: framework/premium/features/content-blocks/hooks-manager.php:419
msgid "Loop"
msgstr "Vòng lặp"

#: framework/premium/features/content-blocks/hooks-manager.php:418,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:716
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:307
msgid "After"
msgstr "Đằng sau"

#: framework/premium/features/content-blocks/hooks-manager.php:425
msgid "Start"
msgstr "Bắt đầu"

#: framework/premium/features/content-blocks/hooks-manager.php:426,
#: framework/premium/features/content-blocks/hooks-manager.php:433
msgid "Loop card"
msgstr "Thẻ vòng lặp"

#: framework/premium/features/content-blocks/hooks-manager.php:432
msgid "End"
msgstr "Kết thúc"

#: framework/premium/features/content-blocks/hooks-manager.php:455
msgid "After certain number of blocks"
msgstr "Sau một số lượng khối nhất định"

#: framework/premium/features/content-blocks/hooks-manager.php:462
msgid "Before certain number of headings"
msgstr "Trước một số lượng tiêu đề nhất định"

#: framework/premium/features/content-blocks/hooks-manager.php:485
msgid "Login form start"
msgstr "Bắt đầu biểu mẫu đăng nhập"

#: framework/premium/features/content-blocks/hooks-manager.php:486,
#: framework/premium/features/content-blocks/hooks-manager.php:493,
#: framework/premium/features/content-blocks/hooks-manager.php:500,
#: framework/premium/features/content-blocks/hooks-manager.php:507,
#: framework/premium/features/content-blocks/hooks-manager.php:514,
#: framework/premium/features/content-blocks/hooks-manager.php:521,
#: framework/premium/features/content-blocks/hooks-manager.php:528,
#: framework/premium/features/content-blocks/hooks-manager.php:535,
#: framework/premium/features/content-blocks/hooks-manager.php:542,
#: framework/premium/features/content-blocks/hooks-manager.php:549
msgid "Auth forms"
msgstr "Biểu mẫu xác thực"

#: framework/premium/features/content-blocks/hooks-manager.php:492
msgid "Login form end"
msgstr "Kết thúc biểu mẫu đăng nhập"

#: framework/premium/features/content-blocks/hooks-manager.php:499
msgid "Login form modal start"
msgstr "Bắt đầu hộp bật lên form đăng nhập"

#: framework/premium/features/content-blocks/hooks-manager.php:506
msgid "Login form modal end"
msgstr "Kết thúc hộp bật lên form đăng nhập"

#: framework/premium/features/content-blocks/hooks-manager.php:513
msgid "Register form start"
msgstr "Bắt đầu biểu mẫu đăng ký"

#: framework/premium/features/content-blocks/hooks-manager.php:520
msgid "Register form end"
msgstr "Kết thúc biểu mẫu đăng ký"

#: framework/premium/features/content-blocks/hooks-manager.php:527
msgid "Register form modal start"
msgstr "Bắt đầu hộp bật lên form đăng ký"

#: framework/premium/features/content-blocks/hooks-manager.php:534
msgid "Register form modal end"
msgstr "Kết thúc hộp bật lên form đăng ký"

#: framework/premium/features/content-blocks/hooks-manager.php:541
msgid "Lost password form modal start"
msgstr "Bắt đầu hộp bật lên form quên mật khẩu"

#: framework/premium/features/content-blocks/hooks-manager.php:548
msgid "Lost password form modal end"
msgstr "Kết thúc hộp bật lên form quên mật khẩu"

#: framework/premium/features/content-blocks/hooks-manager.php:556
msgid "Before main content"
msgstr "Trước nội dung chính"

#: framework/premium/features/content-blocks/hooks-manager.php:562
msgid "After main content"
msgstr "Sau nội dung chính"

#: framework/premium/features/content-blocks/hooks-manager.php:583
msgid "WooCommerce Global"
msgstr "WooCommerce toàn cầu"

#: framework/premium/features/content-blocks/hooks-manager.php:588
msgid "Archive description"
msgstr "Mô tả lưu trữ"

#: framework/premium/features/content-blocks/hooks-manager.php:593
msgid "Before shop loop"
msgstr "Trước vòng lặp cửa hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:610
msgid "Before shop loop item title"
msgstr "Trước tiêu đề mục vòng lặp cửa hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:615
msgid "After shop loop item title"
msgstr "Sau tiêu đề mục vòng lặp cửa hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:620
msgid "Before shop loop item price"
msgstr "Trước giá mục vòng lặp cửa hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:625
msgid "After shop loop item price"
msgstr "Sau giá mục vòng lặp cửa hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:640
msgid "After shop loop"
msgstr "Sau vòng lặp cửa hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:642
msgid "WooCommerce Archive"
msgstr "Lưu trữ WooCommerce"

#: framework/premium/features/content-blocks/hooks-manager.php:647
msgid "Before single product"
msgstr "Trước sản phẩm đơn"

#: framework/premium/features/content-blocks/hooks-manager.php:665
msgid "Product meta start"
msgstr "Bắt đầu meta sản phẩm"

#: framework/premium/features/content-blocks/hooks-manager.php:669
msgid "Product meta end"
msgstr "Kết thúc meta sản phẩm"

#: framework/premium/features/content-blocks/hooks-manager.php:673
msgid "Share"
msgstr "Chia sẻ"

#: framework/premium/features/content-blocks/hooks-manager.php:677
msgid "After single product"
msgstr "Sau sản phẩm đơn"

#: framework/premium/features/content-blocks/hooks-manager.php:683
msgid "Before single product excerpt"
msgstr "Trước đoạn trích sản phẩm đơn"

#: framework/premium/features/content-blocks/hooks-manager.php:688
msgid "After single product excerpt"
msgstr "Sau đoạn trích sản phẩm đơn"

#: framework/premium/features/content-blocks/hooks-manager.php:693
msgid "Before single product tabs"
msgstr "Trước tab sản phẩm đơn"

#: framework/premium/features/content-blocks/hooks-manager.php:699
msgid "After single product tabs"
msgstr "Sau các tab sản phẩm đơn"

#: framework/premium/features/content-blocks/hooks-manager.php:738
msgid "Cart is empty"
msgstr "Giỏ hàng trống"

#: framework/premium/features/content-blocks/hooks-manager.php:742
msgid "Before cart"
msgstr "Trước giỏ hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:746
msgid "Before cart table"
msgstr "Trước bảng giỏ hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:750
msgid "Before cart contents"
msgstr "Trước nội dung giỏ hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:754
msgid "Cart contents"
msgstr "Nội dung giỏ hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:758
msgid "After cart contents"
msgstr "Sau nội dung giỏ hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:762
msgid "Cart coupon"
msgstr "Mã ưu đãi giỏ hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:766
msgid "Cart actions"
msgstr "Hành động giỏ hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:770
msgid "After cart table"
msgstr "Sau bảng giỏ hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:774
msgid "Cart collaterals"
msgstr "Bảo lãnh giỏ hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:778
msgid "Before cart totals"
msgstr "Trước tổng số giỏ hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:782
msgid "Cart totals before order total"
msgstr "Tổng giỏ hàng trước tổng đơn hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:786
msgid "Cart totals after order total"
msgstr "Tổng giỏ hàng sau tổng đơn hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:790
msgid "Proceed to checkout"
msgstr "Tiến hành thanh toán"

#: framework/premium/features/content-blocks/hooks-manager.php:794
msgid "After cart totals"
msgstr "Sau tổng giỏ hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:798
msgid "After cart"
msgstr "Sau giỏ hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:803
msgid "Before Mini Cart"
msgstr "Trước giỏ hàng nhỏ"

#: framework/premium/features/content-blocks/hooks-manager.php:808
msgid "Before Mini Cart Contents"
msgstr "Trước nội dung Giỏ hàng nhỏ"

#: framework/premium/features/content-blocks/hooks-manager.php:813
msgid "Mini Cart Contents"
msgstr "Nội dung giỏ hàng nhỏ"

#: framework/premium/features/content-blocks/hooks-manager.php:818
msgid "Widget Shopping Cart Before Buttons"
msgstr "Tiện ích giỏ hàng trước các nút"

#: framework/premium/features/content-blocks/hooks-manager.php:823
msgid "Widget Shopping Cart After Buttons"
msgstr "Tiện ích giỏ hàng sau các nút"

#: framework/premium/features/content-blocks/hooks-manager.php:828
msgid "After Mini Cart"
msgstr "Sau Giỏ hàng nhỏ"

#: framework/premium/features/content-blocks/hooks-manager.php:830
msgid "WooCommerce Cart"
msgstr "Giỏ hàng WooCommerce"

#: framework/premium/features/content-blocks/hooks-manager.php:836
msgid "Before checkout form"
msgstr "Trước biểu mẫu thanh toán"

#: framework/premium/features/content-blocks/hooks-manager.php:840
msgid "Before customer details"
msgstr "Trước chi tiết khách hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:844
msgid "After customer details"
msgstr "Sau chi tiết khách hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:848
msgid "Checkout billing"
msgstr "Thanh toán hóa đơn"

#: framework/premium/features/content-blocks/hooks-manager.php:852
msgid "Before checkout billing form"
msgstr "Trước biểu mẫu thanh toán"

#: framework/premium/features/content-blocks/hooks-manager.php:856
msgid "After checkout billing form"
msgstr "Sau biểu mẫu thanh toán"

#: framework/premium/features/content-blocks/hooks-manager.php:860
msgid "Before order notes"
msgstr "Trước ghi chú đơn hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:864
msgid "After order notes"
msgstr "Sau ghi chú đơn hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:868
msgid "Checkout shipping"
msgstr "Vận chuyển trang thanh toán"

#: framework/premium/features/content-blocks/hooks-manager.php:872
msgid "Checkout before order review"
msgstr "Thanh toán trước khi xem xét đơn hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:876
msgid "Checkout order review"
msgstr "Xem xét đơn hàng thanh toán"

#: framework/premium/features/content-blocks/hooks-manager.php:880
msgid "Review order before cart contents"
msgstr "Xem lại đơn hàng trước nội dung giỏ hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:884
msgid "Review order after cart contents"
msgstr "Xem lại đơn hàng sau nội dung giỏ hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:888
msgid "Review order before order total"
msgstr "Xem lại đơn hàng trước tổng đơn hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:892
msgid "Review order after order total"
msgstr "Xem lại đơn hàng sau tổng đơn hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:896
msgid "Review order before payment"
msgstr "Xem lại đơn hàng trước khi thanh toán"

#: framework/premium/features/content-blocks/hooks-manager.php:900
msgid "Review order before submit"
msgstr "Xem lại đơn hàng trước khi gửi"

#: framework/premium/features/content-blocks/hooks-manager.php:904
msgid "Review order after submit"
msgstr "Đánh giá đơn hàng sau khi gửi"

#: framework/premium/features/content-blocks/hooks-manager.php:908
msgid "Review order after payment"
msgstr "Đánh giá đơn hàng sau khi thanh toán"

#: framework/premium/features/content-blocks/hooks-manager.php:912
msgid "Checkout after order review"
msgstr "Thanh toán sau khi xem xét đơn hàng"

#: framework/premium/features/content-blocks/hooks-manager.php:916
msgid "After checkout form"
msgstr "Sau biểu mẫu thanh toán"

#: framework/premium/features/content-blocks/hooks-manager.php:919
msgid "WooCommerce Checkout"
msgstr "Thanh toán WooCommerce"

#: framework/premium/features/content-blocks/hooks-manager.php:925
msgid "Before my account"
msgstr "Trước tài khoản"

#: framework/premium/features/content-blocks/hooks-manager.php:929
msgid "Before account navigation"
msgstr "Trước khi điều hướng tài khoản"

#: framework/premium/features/content-blocks/hooks-manager.php:933
msgid "Account navigation"
msgstr "Điều hướng tài khoản"

#: framework/premium/features/content-blocks/hooks-manager.php:937
msgid "After account navigation"
msgstr "Sau điều hướng tài khoản"

#: framework/premium/features/content-blocks/hooks-manager.php:941
msgid "Account content"
msgstr "Nội dung tài khoản"

#: framework/premium/features/content-blocks/hooks-manager.php:945
msgid "Account dashboard"
msgstr "Bảng tổng quan tài khoản"

#: framework/premium/features/content-blocks/hooks-manager.php:949
msgid "After my account"
msgstr "Sau tài khoản"

#: framework/premium/features/content-blocks/hooks-manager.php:951,
#: framework/features/header/items/account/options.php:169,
#: framework/features/header/items/account/options.php:284,
#: framework/features/header/items/account/options.php:285
msgid "WooCommerce Account"
msgstr "Tài khoản WooCommerce"

#: framework/premium/features/content-blocks/hooks-manager.php:977
msgid "WP footer"
msgstr "WP footer"

#: framework/premium/features/content-blocks/admin-ui.php:266,
#: framework/premium/features/content-blocks/hooks-manager.php:978,
#: framework/premium/features/content-blocks/hooks-manager.php:986,
#: framework/premium/features/content-blocks/hooks-manager.php:994
msgid "Footer"
msgstr "Chân trang"

#: framework/premium/features/content-blocks/hooks-manager.php:985
msgid "Footer before"
msgstr "Chân trang trước"

#: framework/premium/features/content-blocks/hooks-manager.php:993
msgid "Footer after"
msgstr "Chân trang sau"

#: framework/premium/features/content-blocks/hooks-manager.php:1009
msgid "Custom Hook (%s)"
msgstr "Hook tùy chỉnh (%s)"

#: framework/premium/features/content-blocks/hooks-manager.php:1015,
#: framework/premium/features/content-blocks/options/hook.php:116
#: framework/premium/static/js/options/MultipleLocationsSelect.js:94
msgid "After Block Number"
msgstr "Sau số khối"

#: framework/premium/features/content-blocks/hooks-manager.php:1021,
#: framework/premium/features/content-blocks/options/hook.php:133
#: framework/premium/static/js/options/MultipleLocationsSelect.js:116
msgid "Before Heading Number"
msgstr "Trước số tiêu đề"

#: static/js/editor/blocks/about-me/index.js:45
msgid "About Me"
msgstr "Giới thiệu về tôi"

#: framework/features/blocks/about-me/options.php:16
msgid "About me"
msgstr "Về tôi"

#: framework/features/blocks/about-me/options.php:49,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:31,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:729,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:68,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:99,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:143,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:166,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:166
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:96
msgid "Image"
msgstr "Hình ảnh"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:126
msgid "Upload Image"
msgstr "Tải lên Hình ảnh"

#: framework/features/blocks/about-me/options.php:53,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:96,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:106,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:809,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:819,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:72
msgid "Select Image"
msgstr "Chọn hình ảnh"

#: framework/features/blocks/about-me/options.php:54
msgid "Change Image"
msgstr "Thay đổi hình ảnh"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:46,
#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:20,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:123,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:112,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:65
msgid "Image Ratio"
msgstr "Tỷ lệ hình ảnh"

#: framework/features/blocks/contact-info/options.php:520,
#: framework/premium/extensions/shortcuts/customizer.php:307
msgid "Open link in new tab"
msgstr "Mở liên kết trong tab mới"

#: framework/features/blocks/contact-info/options.php:35
#: static/js/editor/blocks/contact-info/index.js:54
msgid "Contact Info"
msgstr "Thông tin liên hệ"

#: framework/features/blocks/contact-info/options.php:51,
#: framework/features/blocks/contact-info/options.php:81,
#: framework/features/blocks/contact-info/view.php:15,
#: framework/premium/features/premium-header/items/contacts/options.php:57,
#: framework/premium/features/premium-header/items/contacts/options.php:88,
#: framework/premium/features/premium-header/items/contacts/view.php:35
msgid "Address:"
msgstr "Địa chỉ:"

#: framework/features/blocks/contact-info/options.php:59,
#: framework/features/blocks/contact-info/options.php:146,
#: framework/features/blocks/contact-info/view.php:23,
#: framework/premium/features/premium-header/items/contacts/options.php:65,
#: framework/premium/features/premium-header/items/contacts/options.php:133,
#: framework/premium/features/premium-header/items/contacts/view.php:43
msgid "Phone:"
msgstr "Số điện thoại:"

#: framework/features/blocks/contact-info/options.php:67,
#: framework/features/blocks/contact-info/options.php:209,
#: framework/features/blocks/contact-info/view.php:31,
#: framework/premium/features/premium-header/items/contacts/options.php:73,
#: framework/premium/features/premium-header/items/contacts/options.php:178,
#: framework/premium/features/premium-header/items/contacts/view.php:51
msgid "Mobile:"
msgstr "Mobile:"

#: framework/features/blocks/contact-info/options.php:75,
#: framework/premium/features/premium-header/items/contacts/options.php:82
msgid "Address"
msgstr "Địa chỉ"

#: framework/features/blocks/contact-info/options.php:94,
#: framework/features/blocks/contact-info/options.php:159,
#: framework/features/blocks/contact-info/options.php:222,
#: framework/features/blocks/contact-info/options.php:285,
#: framework/features/blocks/contact-info/options.php:348,
#: framework/features/blocks/contact-info/options.php:411,
#: framework/features/blocks/contact-info/options.php:474,
#: framework/premium/features/premium-header/items/contacts/options.php:107,
#: framework/premium/features/premium-header/items/contacts/options.php:152,
#: framework/premium/features/premium-header/items/contacts/options.php:197,
#: framework/premium/features/premium-header/items/contacts/options.php:242,
#: framework/premium/features/premium-header/items/contacts/options.php:288,
#: framework/premium/features/premium-header/items/contacts/options.php:333,
#: framework/premium/features/premium-header/items/contacts/options.php:378
msgid "Link (optional)"
msgstr "Liên kết (tùy chọn)"

#: framework/features/blocks/contact-info/options.php:265,
#: framework/premium/features/premium-header/items/contacts/options.php:217
msgid "Work Hours"
msgstr "Giờ làm việc"

#: framework/features/blocks/contact-info/options.php:272,
#: framework/premium/features/premium-header/items/contacts/options.php:223
msgid "Opening hours"
msgstr "Giờ mở cửa"

#: framework/features/blocks/contact-info/options.php:328,
#: framework/premium/features/premium-header/items/contacts/options.php:263
msgid "Fax"
msgstr "Fax"

#: framework/features/blocks/contact-info/options.php:335,
#: framework/premium/features/premium-header/items/contacts/options.php:269
msgid "Fax:"
msgstr "Fax:"

#: framework/features/blocks/contact-info/options.php:398,
#: framework/premium/features/premium-header/items/contacts/options.php:314
msgid "Email:"
msgstr "Email:"

#: framework/features/blocks/contact-info/options.php:454,
#: framework/premium/features/premium-header/items/contacts/options.php:353
msgid "Website"
msgstr "Website"

#: framework/features/blocks/contact-info/options.php:461,
#: framework/premium/features/premium-header/items/contacts/options.php:359
msgid "Website:"
msgstr "Website:"

#: framework/features/blocks/about-me/options.php:85,
#: framework/premium/features/content-blocks/options/archive.php:73
msgid "Small"
msgstr "Nhỏ"

#: framework/features/blocks/about-me/options.php:87
msgid "Large"
msgstr "Lớn"

#: framework/features/blocks/about-me/options.php:200,
#: framework/features/blocks/contact-info/options.php:557,
#: framework/features/blocks/share-box/options.php:154,
#: framework/features/blocks/socials/options.php:106,
#: framework/premium/features/premium-header/items/contacts/options.php:445
msgid "Icons Shape Type"
msgstr "Loại hình dạng biểu tượng"

#: framework/features/blocks/about-me/options.php:97,
#: framework/features/blocks/about-me/options.php:205,
#: framework/features/blocks/contact-info/options.php:565,
#: framework/features/blocks/share-box/options.php:161,
#: framework/features/blocks/socials/options.php:113,
#: framework/premium/features/premium-header/items/contacts/options.php:452
msgid "Rounded"
msgstr "Làm tròn"

#: framework/features/blocks/about-me/options.php:98,
#: framework/features/blocks/about-me/options.php:206,
#: framework/features/blocks/contact-info/options.php:566,
#: framework/features/blocks/share-box/options.php:162,
#: framework/features/blocks/socials/options.php:114,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:27,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:136,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:190,
#: framework/premium/features/premium-header/items/contacts/options.php:453
#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:86
msgid "Square"
msgstr "Hình vuông"

#: framework/features/blocks/about-me/options.php:215,
#: framework/features/blocks/contact-info/options.php:575,
#: framework/features/blocks/share-box/options.php:171,
#: framework/features/blocks/socials/options.php:123,
#: framework/premium/features/premium-header/items/contacts/options.php:464
msgid "Shape Fill Type"
msgstr "Loại tô hình dạng"

#: framework/features/blocks/about-me/options.php:220,
#: framework/features/blocks/contact-info/options.php:582,
#: framework/features/blocks/share-box/options.php:178,
#: framework/features/blocks/socials/options.php:130,
#: framework/premium/features/premium-header/items/contacts/options.php:472
msgid "Solid"
msgstr "Mầu đậm"

#: framework/features/blocks/about-me/options.php:219,
#: framework/features/blocks/contact-info/options.php:581,
#: framework/features/blocks/share-box/options.php:177,
#: framework/features/blocks/socials/options.php:129,
#: framework/premium/features/premium-header/items/contacts/options.php:471
msgid "Outline"
msgstr "Dàn ý"

#: framework/extensions/trending/customizer.php:273,
#: framework/extensions/trending/customizer.php:287
#: static/js/editor/blocks/query/Edit.js:169
msgid "Post Type"
msgstr "Loại bài viết"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:149
msgid "Most commented"
msgstr "Được bình luận nhiều nhất"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:165
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:144
msgid "Random"
msgstr "Ngẫu nhiên"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:117
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:112
msgid "Order by"
msgstr "Sắp xếp theo"

#: framework/features/blocks/dynamic-data/options.php:109,
#: framework/features/blocks/dynamic-data/views/wp-field.php:196
msgid "% comments"
msgstr "% bình luận"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:80
msgid "Author Avatar"
msgstr "Ảnh đại diện tác giả"

#: framework/features/blocks/socials/options.php:15
msgid "Social Icons"
msgstr "Biểu tượng xã hội"

#: framework/features/blocks/about-me/options.php:124
msgid "You can configure social URLs in %s."
msgstr "Bạn có thể cấu hình URL xã hội trong %s."

#: framework/features/blocks/about-me/options.php:156,
#: framework/features/blocks/socials/options.php:62,
#: framework/premium/features/premium-header/items/contacts/options.php:405
msgid "Open links in new tab"
msgstr "Mở liên kết trong tab mới"

#: framework/features/blocks/about-me/options.php:163,
#: framework/features/blocks/contact-info/options.php:527,
#: framework/features/blocks/share-box/options.php:117,
#: framework/features/blocks/socials/options.php:69,
#: framework/premium/features/premium-header/items/contacts/options.php:411
msgid "Set links to nofollow"
msgstr "Đặt liên kết thành nofollow"

#: framework/features/header/items/account/options.php:4
msgid "Profile Page"
msgstr "Trang cá nhân"

#: framework/features/header/items/account/options.php:5
msgid "Dashboard Page"
msgstr "Trang tổng quan"

#: framework/features/header/items/account/options.php:7,
#: framework/features/header/items/account/options.php:36,
#: framework/features/header/items/account/options.php:117,
#: framework/features/header/items/account/options.php:280,
#: framework/features/header/items/account/views/login.php:396
msgid "Custom Link"
msgstr "Liên kết tùy chỉnh"

#: framework/features/header/items/account/options.php:6
msgid "Logout"
msgstr "Đăng xuất"

#: framework/features/header/items/account/options.php:279
msgid "Modal"
msgstr "Hộp bật lên"

#: framework/features/header/items/account/options.php:295
msgid "Customizing: Logged in State"
msgstr "Tùy chỉnh: Trạng thái đã đăng nhập"

#: framework/features/header/items/account/options.php:319
msgid "Logged In Options"
msgstr "Tùy chọn đã đăng nhập"

#: framework/features/header/items/account/options.php:324
msgid "Logged Out Options"
msgstr "Tùy chọn đã đăng xuất"

#: framework/features/header/items/account/options.php:611,
#: framework/features/header/items/account/options.php:1059
msgid "Account Action"
msgstr "Hành động tài khoản"

#: framework/features/header/items/account/options.php:145
msgid "Select Menu"
msgstr "Chọn Menu"

#: framework/features/header/items/account/options.php:151
msgid "Select menu..."
msgstr "Chọn menu..."

#. translators: placeholder here means the actual URL.
#: framework/features/header/items/account/options.php:155
msgid "Manage your menus in the %sMenus screen%s."
msgstr "Quản lý menu của bạn trong màn hình %sMenus%s."

#: framework/features/header/items/account/options.php:702,
#: framework/features/header/items/account/options.php:1073
msgid "Custom Page Link"
msgstr "Tuỳ chỉnh liên kết trang"

#: framework/features/header/items/account/options.php:340,
#: framework/features/header/items/account/options.php:739
msgid "Account Image"
msgstr "Ảnh tài khoản"

#: framework/features/header/items/account/options.php:346
msgid "Avatar"
msgstr "Ảnh đại diện"

#: framework/features/header/items/account/options.php:357
msgid "Avatar Size"
msgstr "Kích thước ảnh đại diện"

#: framework/features/header/items/account/options.php:487,
#: framework/features/header/items/account/options.php:861,
#: framework/features/header/items/account/options.php:949
msgid "Type 5"
msgstr "Loại 5"

#: framework/features/header/items/account/options.php:497,
#: framework/features/header/items/account/options.php:871,
#: framework/features/header/items/account/options.php:959
msgid "Type 6"
msgstr "Loại 6"

#: framework/features/header/items/account/options.php:570
msgid "Label Type"
msgstr "Loại nhãn"

#: framework/features/header/items/account/options.php:587,
#: framework/features/header/items/account/options.php:1045,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:136,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:137
msgid "Label Text"
msgstr "Văn bản nhãn"

#: framework/features/header/items/account/options.php:173,
#: framework/features/header/items/account/options.php:597,
#: framework/features/header/items/account/views/login.php:100,
#: framework/features/header/items/account/views/login.php:472
msgid "My Account"
msgstr "Tài khoản"

#: framework/features/header/items/account/options.php:1125,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:163,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:167,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:170
msgid "Label Font"
msgstr "Phông chữ nhãn"

#: framework/features/header/items/account/options.php:1135,
#: framework/features/header/items/account/options.php:1174,
#: framework/features/header/items/account/options.php:1218,
#: framework/features/header/items/account/options.php:1260
msgid "Label Color"
msgstr "Màu nhãn"

#: framework/features/header/header-options.php:214,
#: framework/features/header/items/account/options.php:1140,
#: framework/features/header/items/account/options.php:1332,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:179,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:305,
#: framework/premium/features/premium-header/items/contacts/options.php:590,
#: framework/premium/features/premium-header/items/contacts/options.php:756,
#: framework/premium/features/premium-header/items/contacts/options.php:901,
#: framework/premium/features/premium-header/items/divider/options.php:27,
#: framework/premium/features/premium-header/items/search-input/options.php:271,
#: framework/premium/features/premium-header/items/search-input/options.php:401,
#: framework/premium/features/premium-header/items/search-input/options.php:531,
#: framework/premium/features/premium-header/items/search-input/options.php:667,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:227,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:358,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:57,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:183,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:359,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:492,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:186,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:313,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:434
msgid "Default State"
msgstr "Tình trạng mặc định"

#: framework/features/header/header-options.php:219,
#: framework/features/header/items/account/options.php:1148,
#: framework/features/header/items/account/options.php:1340,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:184,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:310,
#: framework/premium/features/premium-header/items/contacts/options.php:595,
#: framework/premium/features/premium-header/items/contacts/options.php:761,
#: framework/premium/features/premium-header/items/contacts/options.php:909,
#: framework/premium/features/premium-header/items/divider/options.php:32,
#: framework/premium/features/premium-header/items/search-input/options.php:276,
#: framework/premium/features/premium-header/items/search-input/options.php:406,
#: framework/premium/features/premium-header/items/search-input/options.php:536,
#: framework/premium/features/premium-header/items/search-input/options.php:672,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:232,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:363,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:62,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:191,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:364,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:500,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:191,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:318,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:442
msgid "Transparent State"
msgstr "Trạng thái trong suốt"

#: framework/features/header/header-options.php:227,
#: framework/features/header/items/account/options.php:1161,
#: framework/features/header/items/account/options.php:1353,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:193,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:319,
#: framework/premium/features/premium-header/items/contacts/options.php:604,
#: framework/premium/features/premium-header/items/contacts/options.php:770,
#: framework/premium/features/premium-header/items/contacts/options.php:919,
#: framework/premium/features/premium-header/items/divider/options.php:41,
#: framework/premium/features/premium-header/items/search-input/options.php:285,
#: framework/premium/features/premium-header/items/search-input/options.php:415,
#: framework/premium/features/premium-header/items/search-input/options.php:545,
#: framework/premium/features/premium-header/items/search-input/options.php:681,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:241,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:372,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:71,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:204,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:373,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:510,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:200,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:327,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:452
msgid "Sticky State"
msgstr "Trạng thái ghim"

#: framework/features/header/items/account/options.php:1499
msgid "Item Margin"
msgstr "Lề ngoài mục"

#: framework/features/header/items/account/options.php:1514
msgid "Modal Options"
msgstr "Tùy chọn hộp bật lên"

#: framework/features/header/items/account/options.php:1705,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:451
msgid "Modal Shadow"
msgstr "Bóng đổ hộp bật lên"

#: framework/features/header/items/account/options.php:1677,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:422
msgid "Modal Background"
msgstr "Nền hộp bật lên"

#: framework/features/header/items/account/options.php:1691,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:437
msgid "Modal Backdrop"
msgstr "Phông nền hộp bật lên"

#: framework/features/blocks/contact-info/options.php:13,
#: framework/features/header/items/account/options.php:2059,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:441,
#: framework/premium/features/premium-header/items/contacts/options.php:486,
#: framework/premium/features/premium-header/items/contacts/options.php:541,
#: framework/premium/features/premium-header/items/language-switcher/options.php:120,
#: framework/premium/features/premium-header/items/language-switcher/options.php:251,
#: framework/premium/features/premium-header/items/search-input/options.php:239,
#: framework/premium/features/premium-header/items/search-input/options.php:936,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:644,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:578
msgid "Element Visibility"
msgstr "Khả năng hiển thị phần tử"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:655
msgid "You have no %s fields declared for this custom post type."
msgstr "Bạn không có trường %s nào được khai báo cho loại bài viết tùy chỉnh này."

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:667,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1401,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1412
msgid "Field"
msgstr "Trường"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:679,
#: framework/premium/features/premium-header/items/language-switcher/options/common.php:24
msgid "Label"
msgstr "Nhãn"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:728
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:319
msgid "Fallback"
msgstr "Dự phòng"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:755
msgid "%s Field"
msgstr "%s Trường"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1399
msgid "%s %s Font"
msgstr "%s %s Phông chữ"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1410
msgid "%s %s Color"
msgstr "%s %s Màu"

#: framework/helpers/exts-configs.php:142,
#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:11,
#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:68
msgid "Read Time"
msgstr "Thời gian đọc"

#: framework/premium/extensions/post-types-extra/features/filtering/helpers.php:191,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:195,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:842
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:65
msgid "All"
msgstr "Tất cả"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:92,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:803
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:75
msgid "Featured Image"
msgstr "Hình ảnh nổi bật"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:110
msgid "Accent Color"
msgstr "Màu nhấn"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/helpers.php:150,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/helpers.php:209
msgid "Add to wishlist"
msgstr "Thêm vào yêu thích"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:9
msgid "Select a page"
msgstr "Chọn một trang"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:23
msgid "Show Wishlist Page To"
msgstr "Hiển thị trang danh sách yêu thích đến"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:29
msgid "Logged Users"
msgstr "Người dùng đã đăng nhập"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:30
msgid "All Users"
msgstr "Tất cả người dùng"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:40
msgid "Wishlist Page"
msgstr "Trang danh sách yêu thích"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:46
msgid "The page you select here will display the wish list for your logged out users."
msgstr "Trang bạn chọn ở đây sẽ hiển thị danh sách yêu thích cho người dùng đã đăng xuất của bạn."

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:25
msgid "Archive Page"
msgstr "Trang lưu trữ"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:227,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:240,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:276,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:273,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:303,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:332,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:405,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:435,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:464,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:103,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:133,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:162
msgid "Hover/Active"
msgstr "Di chuột/Kích hoạt"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/views/table-no-results.php:6
msgid "You don't have any products in your wish list yet."
msgstr "Bạn chưa có sản phẩm nào trong danh sách yêu thích."

#: framework/premium/extensions/woocommerce-extra/features/archive-card/feature.php:56,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:54,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:82
msgid "Add to cart"
msgstr "Thêm vào giỏ hàng"

#: framework/premium/extensions/woocommerce-extra/features/compare/views/bar.php:101,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:142,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:145,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/views/columns/product-remove-button.php:12
msgid "Remove Product"
msgstr "Xóa sản phẩm"

#: framework/premium/extensions/woocommerce-extra/includes/woo-import-export.php:36
msgid "Blocksy Variation Images"
msgstr "Hình ảnh biến thể Blocksy"

#: framework/premium/features/content-blocks/options/404.php:33,
#: framework/premium/features/content-blocks/options/header.php:55,
#: framework/premium/features/content-blocks/options/hook.php:159,
#: framework/premium/features/content-blocks/options/maintenance.php:30,
#: framework/premium/features/content-blocks/options/nothing_found.php:51,
#: framework/premium/features/content-blocks/options/popup.php:41
msgid "Container Structure"
msgstr "Cấu trúc vùng chứa"

#: framework/premium/features/content-blocks/options/404.php:65,
#: framework/premium/features/content-blocks/options/archive.php:132,
#: framework/premium/features/content-blocks/options/header.php:83,
#: framework/premium/features/content-blocks/options/hook.php:192,
#: framework/premium/features/content-blocks/options/maintenance.php:62,
#: framework/premium/features/content-blocks/options/nothing_found.php:83,
#: framework/premium/features/content-blocks/options/single.php:64
msgid "Narrow Width"
msgstr "Chiều rộng hẹp"

#: framework/premium/features/content-blocks/options/404.php:70,
#: framework/premium/features/content-blocks/options/archive.php:137,
#: framework/premium/features/content-blocks/options/header.php:88,
#: framework/premium/features/content-blocks/options/hook.php:197,
#: framework/premium/features/content-blocks/options/maintenance.php:67,
#: framework/premium/features/content-blocks/options/nothing_found.php:88,
#: framework/premium/features/content-blocks/options/single.php:69
msgid "Normal Width"
msgstr "Chiều rộng bình thường"

#: framework/premium/features/content-blocks/options/404.php:76,
#: framework/premium/features/content-blocks/options/archive.php:153,
#: framework/premium/features/content-blocks/options/header.php:94,
#: framework/premium/features/content-blocks/options/hook.php:203,
#: framework/premium/features/content-blocks/options/maintenance.php:73,
#: framework/premium/features/content-blocks/options/nothing_found.php:94,
#: framework/premium/features/content-blocks/options/single.php:85
msgid "Content Area Style"
msgstr "Loại khu vực nội dung"

#: framework/premium/features/content-blocks/options/404.php:90,
#: framework/premium/features/content-blocks/options/archive.php:167,
#: framework/premium/features/content-blocks/options/header.php:108,
#: framework/premium/features/content-blocks/options/hook.php:217,
#: framework/premium/features/content-blocks/options/maintenance.php:87,
#: framework/premium/features/content-blocks/options/nothing_found.php:108
msgid "Content Area Vertical Spacing"
msgstr "Khoảng cách dọc khu vực nội dung"

#: framework/premium/features/content-blocks/options/404.php:102,
#: framework/premium/features/content-blocks/options/archive.php:179,
#: framework/premium/features/content-blocks/options/header.php:120,
#: framework/premium/features/content-blocks/options/hook.php:229,
#: framework/premium/features/content-blocks/options/maintenance.php:99,
#: framework/premium/features/content-blocks/options/nothing_found.php:120,
#: framework/premium/features/content-blocks/options/single.php:111
msgid "Top & Bottom"
msgstr "Trên cùng & Dưới cùng"

#: framework/premium/features/content-blocks/options/404.php:105,
#: framework/premium/features/content-blocks/options/archive.php:182,
#: framework/premium/features/content-blocks/options/header.php:123,
#: framework/premium/features/content-blocks/options/hook.php:232,
#: framework/premium/features/content-blocks/options/maintenance.php:102,
#: framework/premium/features/content-blocks/options/nothing_found.php:123,
#: framework/premium/features/content-blocks/options/single.php:114
msgid "Only Top"
msgstr "Chỉ đầu trang"

#: framework/premium/features/content-blocks/options/404.php:108,
#: framework/premium/features/content-blocks/options/archive.php:185,
#: framework/premium/features/content-blocks/options/header.php:126,
#: framework/premium/features/content-blocks/options/hook.php:235,
#: framework/premium/features/content-blocks/options/maintenance.php:105,
#: framework/premium/features/content-blocks/options/nothing_found.php:126,
#: framework/premium/features/content-blocks/options/single.php:117
msgid "Only Bottom"
msgstr "Chỉ dưới cùng"

#: framework/premium/features/content-blocks/options/hook.php:60
msgid "Location & Priority"
msgstr "Vị trí & ưu tiên"

#: framework/premium/features/content-blocks/options/hook.php:23,
#: framework/premium/features/content-blocks/options/hook.php:100
#: framework/premium/static/js/options/MultipleLocationsSelect.js:76
msgid "Custom Hook"
msgstr "Hook tùy chỉnh"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:730
msgid "Mixed"
msgstr "Hỗn hợp"

#: framework/premium/features/content-blocks/options/popup.php:438,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:628
msgid "Popup Position"
msgstr "Vị trí popup"

#: framework/premium/features/content-blocks/options/popup.php:384,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:573
msgid "Popup Size"
msgstr "Kích thước popup"

#: framework/premium/features/content-blocks/options/popup.php:390,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:580
msgid "Small Size"
msgstr "Kích cỡ nhỏ"

#: framework/premium/features/content-blocks/options/popup.php:391,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:581
msgid "Medium Size"
msgstr "Kích thước trung bình"

#: framework/premium/features/content-blocks/options/popup.php:392,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:582
msgid "Large Size"
msgstr "Kích thước lớn"

#: framework/premium/features/content-blocks/options/popup.php:393,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:583
msgid "Custom Size"
msgstr "Kích thước tùy chỉnh"

#: framework/premium/features/content-blocks/options/popup.php:403,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:593
msgid "Max Width"
msgstr "Chiều rộng tối đa"

#: framework/premium/features/content-blocks/options/popup.php:419,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:609
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:510
msgid "Max Height"
msgstr "Chiều cao tối đa"

#: framework/premium/features/content-blocks/options/popup.php:339,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:525
msgid "Popup Animation"
msgstr "Hiệu ứng động cửa sổ bật lên"

#: framework/premium/features/content-blocks/options/popup.php:345,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:531
msgid "Fade in fade out"
msgstr "Dần dần mờ dần"

#: framework/premium/features/content-blocks/options/popup.php:346,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:532
msgid "Zoom in zoom out"
msgstr "Phóng to thu nhỏ"

#: framework/premium/features/content-blocks/options/popup.php:347,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:533
msgid "Slide in from left"
msgstr "Trượt vào từ trái"

#: framework/premium/features/content-blocks/options/popup.php:348,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:534
msgid "Slide in from right"
msgstr "Trượt vào từ phải"

#: framework/premium/features/content-blocks/options/popup.php:349,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:535
msgid "Slide in from top"
msgstr "Trượt vào từ trên"

#: framework/premium/features/content-blocks/options/popup.php:350,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:536
msgid "Slide in from bottom"
msgstr "Trượt vào từ dưới"

#: framework/premium/features/content-blocks/options/popup.php:355,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:542
msgid "Animation Speed"
msgstr "Tốc độ hiệu ứng động"

#: framework/premium/features/content-blocks/options/popup.php:372,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:560
msgid "Entrance Value"
msgstr "Giá trị nhập"

#: framework/premium/features/content-blocks/options/popup.php:69
msgid "Trigger Condition"
msgstr "Điều kiện kích hoạt"

#: framework/premium/features/content-blocks/options/popup.php:93,
#: framework/premium/features/content-blocks/options/popup.php:111
msgid "Element Class"
msgstr "Lớp phần tử"

#: framework/premium/features/content-blocks/options/popup.php:99,
#: framework/premium/features/content-blocks/options/popup.php:117
msgid "Separate each class by comma if you have multiple elements."
msgstr "Tách mỗi lớp bằng dấu phẩy nếu bạn có nhiều phần tử."

#: framework/premium/features/content-blocks/options/popup.php:129
msgid "Scroll Direction"
msgstr "Hướng cuộn"

#: framework/premium/features/content-blocks/options/popup.php:134
msgid "Scroll Down"
msgstr "Cuộn xuống"

#: framework/premium/features/content-blocks/options/popup.php:135
msgid "Scroll Up"
msgstr "Cuộn lên"

#: framework/premium/features/content-blocks/options/popup.php:140
msgid "Scroll Distance"
msgstr "Khoảng cách cuộn"

#: framework/premium/features/content-blocks/options/popup.php:149
msgid "Set the scroll distance till the popup block will appear."
msgstr "Đặt khoảng cách cuộn cho đến khi khối cửa sổ bật lên xuất hiện."

#: framework/premium/features/content-blocks/options/popup.php:167
msgid "Inactivity Time"
msgstr "Thời gian không hoạt động"

#: framework/premium/features/content-blocks/options/popup.php:173
msgid "Set the inactivity time (in seconds) till the popup block will appear."
msgstr "Đặt thời gian không hoạt động (theo giây) cho đến khi khối cửa sổ bật lên xuất hiện."

#: framework/premium/features/content-blocks/options/popup.php:185
msgid "After X Time"
msgstr "Sau X thời gian"

#: framework/premium/features/content-blocks/options/popup.php:191
msgid "Set after how much time (in seconds) the popup block will appear."
msgstr "Đặt sau bao nhiêu thời gian (theo giây), khối cửa sổ bật lên sẽ xuất hiện."

#: framework/premium/features/content-blocks/options/popup.php:522,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:676
msgid "Padding"
msgstr "Lề trong"

#: framework/features/header/items/account/options.php:2034,
#: framework/premium/features/content-blocks/options/popup.php:533,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:298,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:688,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:455,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:330,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:194
msgid "Border Radius"
msgstr "Bán kính viền"

#: framework/premium/features/content-blocks/options/popup.php:563,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:666
msgid "Popup Offset"
msgstr "Khoảng cách cửa sổ bật lên"

#: framework/premium/features/content-blocks/options/popup.php:581
msgid "Container Overflow"
msgstr "Tràn vùng trứa"

#: framework/premium/features/content-blocks/options/popup.php:588
msgid "Hidden"
msgstr "Ẩn"

#: framework/premium/features/content-blocks/options/popup.php:589
msgid "Visible"
msgstr "Có thể nhìn thấy"

#: framework/premium/features/content-blocks/options/popup.php:590
msgid "Scroll"
msgstr "Cuộn"

#: framework/premium/features/content-blocks/options/popup.php:592
msgid "Control what happens to the content that is too big to fit into the popup."
msgstr "Kiểm soát những gì xảy ra với nội dung quá lớn để vừa vào cửa sổ bật lên."

#: framework/premium/features/content-blocks/options/popup.php:596
msgid "Close Button"
msgstr "Nút Thoát"

#: framework/premium/features/content-blocks/options/popup.php:604
msgid "Inside"
msgstr "Bên trong"

#: framework/premium/features/content-blocks/options/popup.php:605
msgid "Outside"
msgstr "Bên ngoài"

#: framework/premium/features/content-blocks/options/popup.php:679,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:720,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:299,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:72
msgid "Popup Background"
msgstr "Nền Popup"

#: framework/premium/features/content-blocks/options/popup.php:693
msgid "Popup Backdrop Background"
msgstr "Nền Phông Nền Popup"

#: framework/helpers/exts-configs.php:156,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:25
msgid "Posts Filter"
msgstr "Bộ lọc bài viết"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:59
msgid "Filtering Behavior"
msgstr "Hành vi lọc"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:67
msgid "Instant Reload"
msgstr "Tải lại tức thì"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:68
msgid "Page Reload"
msgstr "Tải lại trang"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:103
msgid "Items Horizontal Spacing"
msgstr "Khoảng cách ngang mục"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:115,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:79
msgid "Items Vertical Spacing"
msgstr "Khoảng cách dọc mục"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:127
msgid "Container Bottom Spacing"
msgstr "Khoảng cách dưới vùng chứa"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:139,
#: framework/premium/features/premium-header/items/contacts/options.php:510,
#: framework/premium/features/premium-header/items/language-switcher/options.php:90,
#: framework/premium/features/premium-header/items/search-input/options.php:207
msgid "Horizontal Alignment"
msgstr "Căn chỉnh theo chiều ngang"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:284
msgid "Button Padding"
msgstr "Lề trong của nút"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:4
msgid "Read Progress"
msgstr "Tiến trình đọc"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:24
msgid "Indicator Height"
msgstr "Chiều cao chỉ số"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:34
msgid "Auto Hide"
msgstr "Tự động ẩn"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:38
msgid "Automatically hide the read progress bar once you arrive at the bottom of the article."
msgstr "Tự động ẩn thanh tiến trình đọc khi bạn đến cuối bài viết."

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:71
msgid "Main Color"
msgstr "Màu chính"

#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:83,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:84
msgid "Icon Badge"
msgstr "Huy hiệu biểu tượng"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:174,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:178,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:181
msgid "Label Font Color"
msgstr "Màu phông chữ nhãn"

#: framework/premium/features/premium-header/items/contacts/config.php:4
msgid "Contacts"
msgstr "Liên hệ"

#: framework/features/blocks/about-me/options.php:178,
#: framework/features/blocks/contact-info/options.php:548,
#: framework/premium/features/premium-header/items/contacts/options.php:435,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:16
msgid "Items Spacing"
msgstr "Khoảng cách mục"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:420,
#: framework/premium/features/premium-header/items/contacts/options.php:1051,
#: framework/premium/features/premium-header/items/content-block/options.php:40,
#: framework/premium/features/premium-header/items/divider/options.php:107,
#: framework/premium/features/premium-header/items/divider/options.php:125,
#: framework/premium/features/premium-header/items/search-input/options.php:804,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:478,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:172,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:625,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:557
msgid "Margin"
msgstr "Lề ngoài"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:106,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/view.php:45
msgid "Dark Mode"
msgstr "Chuyển chế độ tối"

#: framework/features/header/items/account/options.php:68,
#: framework/features/header/items/account/options.php:1999,
#: framework/premium/features/premium-header/items/divider/config.php:4
msgid "Divider"
msgstr "Dải phân cách"

#: framework/premium/features/premium-header/items/divider/options.php:6
msgid "Size"
msgstr "Kích thước"

#: framework/features/conditions/rules/localization.php:11,
#: framework/premium/features/premium-header/items/language-switcher/config.php:14
msgid "Languages"
msgstr "Ngôn ngữ"

#: framework/premium/features/premium-header/items/language-switcher/options.php:274
msgid "Top Level Options"
msgstr "Tùy chọn cấp độ cao nhất"

#: framework/features/header/items/account/options.php:618,
#: framework/premium/features/premium-header/items/language-switcher/options.php:170
msgid "Dropdown"
msgstr "Menu thả xuống"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:23
msgid "Flag"
msgstr "Lá cờ"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:39
msgid "Label Style"
msgstr "Loại nhãn"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:46
msgid "Long"
msgstr "Dài"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:47
msgid "Short"
msgstr "Ngắn"

#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:26
msgid "Hide Current Language"
msgstr "Ẩn ngôn ngữ hiện tại"

#: framework/features/header/items/account/options.php:1890,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:59
msgid "Dropdown Options"
msgstr "Tùy chọn Menu thả xuống"

#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:70
msgid "Dropdown Top Offset"
msgstr "Khoảng cách trên menu thả xuống"

#: framework/premium/features/premium-header/items/search-input/config.php:4
msgid "Search Box"
msgstr "Hộp tìm kiếm"

#: framework/features/blocks/search/options.php:46,
#: framework/features/blocks/search/options.php:52,
#: framework/features/blocks/search/options.php:64,
#: framework/features/blocks/search/options.php:145,
#: framework/premium/features/premium-header/items/search-input/options.php:45
msgid "Placeholder Text"
msgstr "Văn bản tạm thời"

#: framework/premium/features/premium-header/items/search-input/options.php:55
msgid "Input Maximum Width"
msgstr "Chiều rộng tối đa đầu vào"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:87,
#: framework/features/blocks/search/options.php:70,
#: framework/premium/features/premium-header/items/search-input/options.php:67
msgid "Input Height"
msgstr "Chiều cao đầu vào"

#: framework/features/blocks/search/options.php:97,
#: framework/premium/features/premium-header/items/search-input/options.php:88
msgid "Live Results"
msgstr "Kết quả trực tiếp"

#: framework/features/blocks/search/options.php:109,
#: framework/premium/features/premium-header/items/search-input/options.php:100
msgid "Live Results Images"
msgstr "Hình ảnh kết quả trực tiếp"

#: framework/features/blocks/search/options.php:185,
#: framework/premium/features/premium-header/items/search-input/options.php:175
msgid "Search Through Criteria"
msgstr "Tìm kiếm thông qua tiêu chí"

#: framework/features/blocks/search/options.php:187,
#: framework/premium/features/premium-header/items/search-input/options.php:176
msgid "Chose in which post types do you want to perform searches."
msgstr "Chọn loại nội dung mà bạn muốn thực hiện tìm kiếm."

#: framework/premium/features/premium-header/items/search-input/options.php:396,
#: framework/premium/features/premium-header/items/search-input/options.php:425,
#: framework/premium/features/premium-header/items/search-input/options.php:457,
#: framework/premium/features/premium-header/items/search-input/options.php:487
msgid "Input Icon Color"
msgstr "Màu biểu tượng đầu vào"

#: framework/premium/features/premium-header/items/search-input/options.php:833
#: static/js/editor/blocks/search/Edit.js:346
msgid "Dropdown Text Color"
msgstr "Màu văn bản menu thả xuống"

#: framework/premium/features/premium-header/items/search-input/options.php:864
msgid "Dropdown Background"
msgstr "Nền menu thả xuống"

#: framework/premium/features/premium-header/items/widget-area-1/config.php:4
msgid "Widget Area"
msgstr "Khu vực tiện ích"

#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker.js:14
#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker/Implementation.js:72
#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:362
#: static/js/dashboard/NoTheme.js:64 static/js/dashboard/VersionMismatch.js:61
#: static/js/dashboard/screens/SiteExport.js:310
#: static/js/notifications/VersionMismatchNotice.js:73
msgid "Loading..."
msgstr "Đang tải..."

#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker/Implementation.js:73
msgid "Invalid API Key..."
msgstr "Mã API không hợp lệ..."

#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker/Implementation.js:101
#: framework/extensions/newsletter-subscribe/dashboard-static/js/ListPicker.js:119
msgid "Select list..."
msgstr "Chọn danh sách..."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:218
msgid "More information on how to generate an API key for Mailchimp can be found %shere%s."
msgstr "Thêm thông tin về cách tạo một API key cho Mailchimp có thể được tìm thấy %stại đây%s."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:286
msgid "More information on how to generate an API key for ConvertKit can be found %shere%s."
msgstr "Thêm thông tin về cách tạo một API key cho ConvertKit có thể được tìm thấy %stại đây%s."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:184
msgid "API Key"
msgstr "API Key"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:363
#: framework/extensions/product-reviews/static/js/ProductReviews.js:94
#: framework/premium/extensions/mega-menu/static/js/SettingsManager.js:280
#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:111
#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:910
#: framework/premium/static/js/footer/EditConditions.js:143
#: framework/premium/static/js/media-video/components/EditVideoMeta.js:106
msgid "Save Settings"
msgstr "Lưu cài đặt"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:153
msgid "Pick Mailing Service"
msgstr "Chọn dịch vụ gửi email"

#: framework/extensions/product-reviews/static/js/ProductReviews.js:22
msgid "Product Reviews Settings"
msgstr "Cài đặt đánh giá sản phẩm"

#: framework/extensions/product-reviews/static/js/ProductReviews.js:25
msgid "Configure the slugs for single and category pages of the product review custom post type."
msgstr "Cấu hình các tên định danh cho trang đơn và danh mục của loại bài viết tùy chỉnh đánh giá sản phẩm."

#: framework/extensions/product-reviews/static/js/ProductReviews.js:43
msgid "Single Slug"
msgstr "Tên định danh đơn"

#: framework/extensions/product-reviews/static/js/ProductReviews.js:49
msgid "Category Slug"
msgstr "Tên định danh danh mục"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:84
msgid "Adobe Fonts Settings"
msgstr "Cài đặt phông chữ Adobe"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:116
msgid "Project ID"
msgstr "ID dự án"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:158
msgid "Fetch Fonts"
msgstr "Tải phông chữ"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:182
#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:54
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:39
msgid "Variations"
msgstr "Biến thể"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/CustomFonts.js:74
msgid "Custom Fonts Settings"
msgstr "Cài đặt phông chữ tùy chỉnh"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/CustomFonts.js:76
msgid "Here you can see all your custom fonts that can be used in all typography options across the theme."
msgstr "Tại đây, bạn có thể thấy tất cả các phông chữ tùy chỉnh có thể được sử dụng trong tất cả các tùy chọn kiểu chữ trong toàn bộ giao diện."

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:28
msgid "Dynamic Font"
msgstr "Phông chữ động"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:37
msgid "Variable font"
msgstr "Phông chữ biến đổi"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:78
#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:322
msgid "Edit Font"
msgstr "Sửa phông chữ"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:100
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:78
msgid "Remove Font"
msgstr "Xóa phông chữ"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:216
msgid "Change"
msgstr "Thay đổi"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:217
msgid "Choose"
msgstr "Chọn"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:127
#: static/js/options/ConditionsManager/SingleCondition.js:95
msgid "Select variation"
msgstr "Chọn biến thể"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:147
msgid "Regular"
msgstr "Thông thường"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:154
#: framework/premium/static/js/typography/providers/kadence.js:71
msgid "Italic"
msgstr "Nghiêng"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/RegularTab.js:30
msgid "Upload only the %s.woff2%s or %s.ttf%s font file formats (see browser coverage %shere%s). Use %sthis converter tool%s if you don't have these font formats."
msgstr "Chỉ tải lên các định dạng tệp phông chữ %s.woff2%s hoặc %s.ttf%s (xem phạm vi trình duyệt %stại đây%s). Sử dụng %scông cụ chuyển đổi này%s nếu bạn không có các định dạng phông chữ này."

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:90
msgid "Font Name"
msgstr "Tên phông chữ"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:459
msgid "Save Custom Font"
msgstr "Lưu phông chữ tùy chỉnh"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:79
msgid "Add Variation"
msgstr "Thêm biến thể"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:258
msgid "Download a font and serve it directly from your server, this is handy for those who want to comply with GDPR regulations or serve the font via CDN."
msgstr "Tải xuống phông chữ và phân phối phông chữ trực tiếp từ máy chủ của bạn, điều này rất hữu ích cho những ai muốn tuân thủ các quy định của GDPR hoặc phân phối phông chữ qua CDN."

#: framework/premium/extensions/mega-menu/static/js/SettingsManager.js:189
msgid "Item Settings"
msgstr "Cài đặt mục"

#: framework/premium/extensions/sidebars/static/js/BlockWidgetControls.js:60
msgid "Remove Sidebar"
msgstr "Xóa thanh bên"

#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:65
msgid "%s - Sidebar Display Conditions"
msgstr "%s - Điều kiện hiển thị thanh bên"

#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:73
msgid "Add one or more conditions in order to display your sidebar."
msgstr "Thêm một hoặc nhiều điều kiện để hiển thị thanh bên của bạn."

#: framework/premium/extensions/sidebars/static/js/main.js:53
msgid "Enter a name in the input below and hit the Create Sidebar button."
msgstr "Nhập tên vào ô dưới đây và nhấn nút tạo thanh bên."

#: framework/premium/extensions/sidebars/static/js/main.js:60
msgid "Sidebar name"
msgstr "Tên thanh bên"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:108
msgid "Advanced"
msgstr "Nâng cao"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:123
msgid "Agency Details"
msgstr "Chi tiết Đại lý"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:131
msgid "Agency Name"
msgstr "Tên Đại Lý"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:159
msgid "Agency URL"
msgstr "URL Đại lý"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:187
msgid "Agency Support/Contact Form URL"
msgstr "URL Hỗ trợ của Đại lý/Biểu mẫu liên hệ"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:220
msgid "Theme Details"
msgstr "Chi tiết giao diện"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:228
msgid "Theme Name"
msgstr "Tên giao diện"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:256
msgid "Theme Description"
msgstr "Mô tả giao diện"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:286
msgid "Theme Screenshot URL"
msgstr "URL ảnh chụp màn hình giao diện"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:384
msgid "You can insert the link to a self hosted image or upload one. The recommended image size is 1200px wide by 900px tall."
msgstr "Bạn có thể chèn liên kết tới hình ảnh tự lưu trữ hoặc tải lên hình ảnh đó. Kích thước ảnh được đề xuất là rộng 1200px x cao 900px."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:393
msgid "Theme Icon URL"
msgstr "URL biểu tượng giao diện"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:489
msgid "You can insert the link to a self hosted image or upload one. The recommended image size is 18px wide by 18px tall."
msgstr "Bạn có thể chèn liên kết tới hình ảnh tự lưu trữ hoặc tải lên hình ảnh đó. Kích thước hình ảnh được đề xuất là rộng 18px x cao 18px."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:498
msgid "Gutenberg Options Panel Icon URL"
msgstr "URL biểu tượng bảng điều khiển tùy chọn Gutenberg"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:596
msgid "You can insert the link to a self hosted image or upload one. Please note that only icons in SVG format are allowed here to not break the editor interactiveness."
msgstr "Bạn có thể chèn liên kết tới hình ảnh tự lưu trữ hoặc tải lên hình ảnh đó. Lưu ý chỉ cho phép các biểu tượng ở định dạng SVG ở đây để không phá vỡ tính tương tác của trình chỉnh sửa."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:616
msgid "Plugin Name"
msgstr "Tên plugin"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:644
msgid "Plugin Description"
msgstr "Mô tả plugin"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:674
msgid "Plugin Thumbnail URL"
msgstr "URL ảnh đại diện plugin"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:764
msgid "Choose File"
msgstr "Chọn tệp tin"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:772
msgid "You can insert the link to a self hosted image or upload one. The recommended image size is 256px wide by 256px tall."
msgstr "Bạn có thể chèn liên kết tới hình ảnh tự lưu trữ hoặc tải lên hình ảnh đó. Kích thước ảnh được đề xuất là rộng 256px x cao 256px."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:795
msgid "Hide Account Menu Item"
msgstr "Ẩn mục menu tài khoản"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:803
msgid "Hide Starter Sites Tab"
msgstr "Ẩn Tab mẫu web bắt đầu"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:810
msgid "Hide Useful Plugins Tab"
msgstr "Ẩn tab Tiện ích mở rộng hữu ích"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:818
msgid "Hide Changelog Tab"
msgstr "Ẩn tab nhật ký thay đổi"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:826
msgid "Hide Support Section"
msgstr "Ẩn phần hỗ trợ"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:876
msgid "Hide White Label Extension"
msgstr "Ẩn tiện ích mở rộng gắn nhãn trắng"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:891
msgid "Please note that the white label extension will be hidden if this option is enabled. In order to bring it back you have to hit the SHIFT key and click on the dashboard logo."
msgstr "Lưu ý tiện ích mở rộng gắn nhãn trắng sẽ bị ẩn nếu tùy chọn này được kích hoạt. Để mở lại, bạn phải nhấn phím SHIFT và nhấp vào biểu tượng bảng điều khiển."

#: framework/premium/static/js/footer/CloneItem.js:66
#: framework/premium/static/js/header/CloneItem.js:66
msgid "Clone Item"
msgstr "Tạo bản sao mục"

#: framework/premium/static/js/footer/CloneItem.js:90
#: framework/premium/static/js/header/CloneItem.js:90
msgid "Remove Item"
msgstr "Xóa mục"

#: framework/premium/static/js/footer/CreateFooter.js:107
#: framework/premium/static/js/header/CreateHeader.js:106
msgid "Copy elements & styles from"
msgstr "Sao chép các phần tử & kiểu biểu mẫu"

#: framework/premium/static/js/footer/CreateFooter.js:118
msgid "Picker Footer"
msgstr "Chân trang bộ chọn"

#: framework/premium/static/js/footer/CreateFooter.js:133
#: framework/premium/static/js/footer/PanelsManager.js:52
msgid "Global Footer"
msgstr "Chân trang toàn cầu"

#: framework/premium/static/js/footer/CreateFooter.js:137
#: framework/premium/static/js/header/CreateHeader.js:138
msgid "Secondary"
msgstr "Thứ cấp"

#: framework/premium/static/js/footer/CreateFooter.js:141
#: framework/premium/static/js/header/CreateHeader.js:142
msgid "Centered"
msgstr "Đã căn giữa"

#: framework/premium/static/js/footer/CreateFooter.js:172
msgid "Create New Footer"
msgstr "Tạo chân trang mới"

#: framework/premium/static/js/footer/CreateFooter.js:50
msgid "Create new footer"
msgstr "Tạo chân trang mới"

#: framework/premium/static/js/footer/CreateFooter.js:53
msgid "Create a new footer and assign it to different pages or posts based on your conditions."
msgstr "Tạo chân trang mới và gán nó cho các trang hoặc bài viết khác nhau dựa trên điều kiện của bạn."

#: framework/premium/static/js/footer/CreateFooter.js:72
msgid "Footer name"
msgstr "Tên Chân trang"

#: framework/premium/static/js/footer/EditConditions.js:100
msgid "Add one or more conditions in order to display your footer."
msgstr "Thêm một hoặc nhiều điều kiện để hiển thị chân trang của bạn."

#: framework/premium/static/js/footer/EditConditions.js:84
#: static/js/header/EditConditions.js:88
msgid "Add/Edit Conditions"
msgstr "Thêm/Chỉnh sửa Điều kiện"

#: framework/premium/static/js/footer/PanelsManager.js:169
msgid "Remove footer"
msgstr "Xóa chân trang"

#: framework/premium/static/js/footer/PanelsManager.js:193
msgid "Remove Footer"
msgstr "Xóa Chân trang"

#: framework/premium/static/js/footer/PanelsManager.js:196
msgid "You are about to remove a custom footer, are you sure you want to continue?"
msgstr "Bạn sắp xóa chân trang tùy chỉnh, bạn có chắc chắn muốn tiếp tục không?"

#: framework/premium/static/js/footer/PanelsManager.js:212
#: framework/premium/static/js/hooks/CodeEditor.js:189
#: static/js/header/PanelsManager.js:217
#: static/js/options/CustomizerOptionsManager.js:463
msgid "Cancel"
msgstr "Hủy"

#: framework/premium/static/js/footer/PanelsManager.js:228
#: static/js/header/PanelsManager.js:233
msgid "Confirm"
msgstr "Xác nhận"

#: framework/premium/static/js/footer/PanelsManager.js:68
#: static/js/header/PanelsManager.js:74
#: static/js/options/DisplayCondition.js:61
msgid "Edit Conditions"
msgstr "Sửa điều kiện"

#. translators: %s: PHP version
#: blocksy-companion.php:182
msgid "Blocksy requires PHP version %s+, plugin is currently NOT RUNNING."
msgstr "Blocksy yêu cầu phiên bản PHP %s+, plugin hiện KHÔNG CHẠY."

#. translators: %s: WordPress version
#: blocksy-companion.php:193
msgid "Blocksy requires WordPress version %s+. Because you are using an earlier version, the plugin is currently NOT RUNNING."
msgstr "Blocksy yêu cầu phiên bản WordPress %s+. Bởi vì bạn đang sử dụng phiên bản cũ hơn, plugin hiện KHÔNG CHẠY."

#: framework/premium/extensions/adobe-typekit/extension.php:46
msgid "Adobe Typekit"
msgstr "Adobe Typekit"

#: framework/premium/extensions/custom-fonts/extension.php:154
msgid "Custom Fonts"
msgstr "Phông chữ tùy chỉnh"

#: framework/premium/extensions/local-google-fonts/extension.php:122
msgid "Local Google Fonts"
msgstr "Phông chữ Google cục bộ"

#: framework/theme-integration.php:220,
#: framework/features/blocks/share-box/options.php:19
msgid "Facebook"
msgstr "Facebook"

#: framework/theme-integration.php:222,
#: framework/features/blocks/share-box/options.php:37
msgid "LinkedIn"
msgstr "LinkedIn"

#: framework/theme-integration.php:223
msgid "Dribbble"
msgstr "Dribbble"

#: framework/theme-integration.php:224
msgid "Instagram"
msgstr "Instagram"

#: framework/theme-integration.php:225,
#: framework/features/blocks/share-box/options.php:31
msgid "Pinterest"
msgstr "Pinterest"

#: framework/theme-integration.php:226
msgid "WordPress"
msgstr "WordPress"

#: framework/theme-integration.php:227
msgid "GitHub"
msgstr "GitHub"

#: framework/theme-integration.php:228,
#: framework/features/blocks/about-me/options.php:86,
#: framework/premium/features/content-blocks/options/archive.php:74
msgid "Medium"
msgstr "Trung bình"

#: framework/theme-integration.php:229,
#: framework/premium/features/media-video/options.php:14
msgid "YouTube"
msgstr "YouTube"

#: framework/theme-integration.php:230,
#: framework/premium/features/media-video/options.php:15
msgid "Vimeo"
msgstr "Vimeo"

#: framework/theme-integration.php:231,
#: framework/features/blocks/share-box/options.php:55
msgid "VKontakte"
msgstr "VKontakte"

#: framework/theme-integration.php:232,
#: framework/features/blocks/share-box/options.php:61
msgid "Odnoklassniki"
msgstr "Odnoklassniki"

#: framework/theme-integration.php:233
msgid "TikTok"
msgstr "TikTok"

#: framework/theme-integration.php:278
msgid "Companion"
msgstr "Blocksy Companion"

#: framework/theme-integration.php:296
msgid "PRO"
msgstr "PRO"

#: framework/features/account-auth.php:119,
#: framework/features/account-auth.php:267
msgid "Check your email"
msgstr "Kiểm tra email của bạn"

#: framework/features/account-auth.php:273
msgid "Registration Form"
msgstr "Biểu mẫu đăng ký"

#: framework/features/account-auth.php:274
msgid "Register For This Site"
msgstr "Đăng ký cho trang web này"

#: framework/features/conditions/rules/cpt.php:17
msgid "%s Single"
msgstr "%s Đơn"

#: framework/features/conditions/rules/cpt.php:27
msgid "%s Archive"
msgstr "%s Lưu trữ"

#: framework/features/conditions/rules/cpt.php:40
msgid "%s %s Taxonomy"
msgstr "%s %s Phân loại"

#: framework/features/conditions/rules/basic.php:3
msgid "Entire Website"
msgstr "Toàn bộ trang web"

#: framework/features/conditions/rules/basic.php:57
msgid "Basic"
msgstr "Cơ bản"

#: framework/extensions/trending/customizer.php:4,
#: framework/features/blocks/query/block.php:19,
#: framework/features/blocks/search/options.php:6,
#: framework/features/conditions/rules/posts.php:33,
#: framework/premium/features/premium-header/items/search-input/options.php:4
msgid "Posts"
msgstr "Bài viết"

#: framework/features/conditions/rules/posts.php:27,
#: framework/premium/features/content-blocks/hooks-manager.php:440,
#: framework/premium/features/content-blocks/hooks-manager.php:448,
#: framework/premium/features/content-blocks/hooks-manager.php:456,
#: framework/premium/features/content-blocks/hooks-manager.php:463,
#: framework/premium/features/content-blocks/hooks-manager.php:470,
#: framework/premium/features/content-blocks/hooks-manager.php:478
msgid "Single Post"
msgstr "Bài viết đơn"

#: framework/features/conditions/rules/posts.php:15
msgid "Post Categories"
msgstr "Chuyên mục bài viết"

#: framework/features/conditions/rules/posts.php:20
msgid "Post Tags"
msgstr "Thẻ bài viết"

#: framework/features/blocks/search/options.php:7,
#: framework/features/conditions/rules/pages.php:52,
#: framework/premium/features/premium-header/items/search-input/options.php:5
msgid "Pages"
msgstr "Trang"

#: framework/features/conditions/rules/pages.php:8
msgid "Single Page"
msgstr "Trang đơn"

#: framework/features/conditions/rules/specific.php:48
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:69
msgid "Specific"
msgstr "Riêng biệt"

#: framework/features/conditions/rules/specific.php:10
msgid "Post ID"
msgstr "ID bài viết"

#: framework/features/conditions/rules/specific.php:15
msgid "Page ID"
msgstr "ID Trang"

#: framework/features/conditions/rules/specific.php:20
#: static/js/options/ConditionsManager/PostIdPicker.js:79
msgid "Custom Post Type ID"
msgstr "ID Loại bài viết tùy chỉnh"

#: framework/features/conditions/rules/specific.php:38,
#: framework/features/conditions/rules/woo.php:65
msgid "Taxonomy ID"
msgstr "ID phân loại"

#: framework/features/conditions/rules/specific.php:25
msgid "Post with Taxonomy ID"
msgstr "Đăng với ID phân loại"

#: framework/features/conditions/rules/pages.php:15
msgid "404"
msgstr "404"

#: framework/features/blocks/search/options.php:60,
#: framework/features/blocks/search/options.php:66,
#: framework/features/blocks/search/view.php:80,
#: framework/features/blocks/search/view.php:261,
#: framework/features/conditions/rules/pages.php:22,
#: framework/premium/features/premium-header/items/search-input/options.php:48,
#: framework/premium/features/premium-header/items/search-input/view.php:126
#: static/js/editor/blocks/query/edit/PatternSelectionModal.js:92
#: static/js/editor/blocks/search/Preview.js:23
#: static/js/editor/blocks/tax-query/edit/PatternSelectionModal.js:78
msgid "Search"
msgstr "Tìm kiếm"

#: framework/features/conditions/rules/pages.php:27
msgid "Blog"
msgstr "Blog"

#: framework/features/conditions/rules/pages.php:33
msgid "Front Page"
msgstr "Trang chính"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:70
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:157
msgid "Author"
msgstr "Tác giả"

#: framework/features/conditions/rules/user-auth.php:20
msgid "User Auth"
msgstr "Xác thực người dùng"

#: framework/features/conditions/rules/user-auth.php:24
msgid "User Logged In"
msgstr "Người dùng đã đăng nhập"

#: framework/features/conditions/rules/user-auth.php:29
msgid "User Logged Out"
msgstr "Người dùng đã Đăng xuất"

#: framework/features/conditions/rules/user-auth.php:34
msgid "User Role"
msgstr "Vai trò Người dùng"

#: framework/premium/features/content-blocks/options/404.php:19,
#: framework/premium/features/content-blocks/options/header.php:19,
#: framework/premium/features/content-blocks/options/hook.php:24,
#: framework/premium/features/content-blocks/options/nothing_found.php:19,
#: framework/premium/features/content-blocks/options/popup.php:25
msgid "Other"
msgstr "Khác"

#: framework/features/conditions-manager.php:307
msgid "Language"
msgstr "Ngôn ngữ"

#: framework/features/demo-install.php:98
msgid "Your PHP installation doesn't have support for XML. Please install the <i>xml</i> or <i>simplexml</i> PHP extension in order to be able to install starter sites. You might need to contact your hosting provider to assist you in doing so."
msgstr "Cài đặt PHP của bạn không có hỗ trợ cho XML. Vui lòng cài đặt phần mở rộng <i>xml</i> hoặc <i>simplexml</i> PHP để có thể cài đặt mẫu web bắt đầu. Bạn có thể cần liên hệ với nhà cung cấp dịch vụ lưu trữ của mình để hỗ trợ bạn làm việc này."

#: framework/features/dynamic-css.php:48
msgid "Dynamic CSS Output"
msgstr "Đầu ra CSS động"

#: framework/features/dynamic-css.php:52
msgid "The strategy of outputting the dynamic CSS. File - all the CSS code will be placed in a static file, otherwise it will be placed inline in head."
msgstr "Chiến lược xuất CSS động. File - tất cả code CSS sẽ được đặt trong một file tĩnh, nếu không, nó sẽ được đặt cùng trong dòng đầu."

#: framework/features/dynamic-css.php:54
msgid "File"
msgstr "Tệp tin"

#: framework/features/dynamic-css.php:55,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:67,
#: framework/premium/features/premium-header/items/language-switcher/options.php:165
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:159
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:204
msgid "Inline"
msgstr "Nội tuyến"

#: framework/features/google-analytics.php:69
msgid "Google Analytics v4"
msgstr "Google Analytics v4"

#: framework/features/google-analytics.php:74
msgid "Link your Google Analytics 4 tracking ID. More info and instructions can be found %shere%s."
msgstr "Liên kết ID theo dõi Google Analytics 4 của bạn. Thông tin và hướng dẫn thêm có thể được tìm thấy %stại đây%s"

#: framework/features/opengraph-meta-data.php:13
msgid "OpenGraph Meta Data"
msgstr "OpenGraph Meta Data"

#: framework/features/opengraph-meta-data.php:17
msgid "Enable the OpenGraph rich meta data features for your website."
msgstr "Kích hoạt tính năng meta data OpenGraph phong phú cho trang web của bạn."

#: framework/features/opengraph-meta-data.php:26
msgid "Facebook Page URL"
msgstr "Đường dẫn trang Facebook"

#: framework/features/opengraph-meta-data.php:33
msgid "Facebook App ID"
msgstr "ID Facebook App"

#: framework/features/opengraph-meta-data.php:40
msgid "Twitter Username"
msgstr "Tên người dùng Twitter"

#: framework/extensions/cookies-consent/config.php:6
msgid "Display a cookie acceptance box in order to comply with the privacy regulations in your country."
msgstr "Hiển thị hộp chấp nhận cookie để tuân thủ quy định về quyền riêng tư ở quốc gia của bạn."

#: framework/extensions/cookies-consent/customizer.php:15,
#: framework/extensions/newsletter-subscribe/customizer.php:12,
#: framework/extensions/product-reviews/extension.php:382,
#: framework/extensions/product-reviews/metabox.php:6,
#: framework/extensions/trending/customizer.php:173,
#: framework/features/header/header-options.php:6,
#: framework/premium/extensions/mega-menu/options.php:6,
#: framework/premium/extensions/shortcuts/customizer.php:761,
#: framework/features/header/items/account/options.php:330,
#: framework/premium/features/content-blocks/options/404.php:55,
#: framework/premium/features/content-blocks/options/archive.php:122,
#: framework/premium/features/content-blocks/options/header.php:73,
#: framework/premium/features/content-blocks/options/hook.php:182,
#: framework/premium/features/content-blocks/options/maintenance.php:52,
#: framework/premium/features/content-blocks/options/nothing_found.php:73,
#: framework/premium/features/content-blocks/options/popup.php:30,
#: framework/premium/features/content-blocks/options/single.php:54,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:6,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:35,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:279,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:520,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:41,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:120,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:378,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:20,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:66,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:11,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:10,
#: framework/premium/features/premium-header/items/contacts/options.php:45,
#: framework/premium/features/premium-header/items/content-block/options.php:5,
#: framework/premium/features/premium-header/items/language-switcher/options.php:278,
#: framework/premium/features/premium-header/items/search-input/options.php:40,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:19,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:163,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:63,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:5,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:5
#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:352
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:111
#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:104
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:43
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:38
msgid "General"
msgstr "Tổng quan"

#: framework/extensions/cookies-consent/customizer.php:28,
#: framework/premium/extensions/shortcuts/customizer.php:773,
#: framework/features/header/items/account/options.php:447,
#: framework/features/header/items/account/options.php:821,
#: framework/features/header/items/account/options.php:909,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:23,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:48,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:375,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:31,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:56,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:101,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:55,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:56
msgid "Type 1"
msgstr "Loại 1"

#: framework/extensions/cookies-consent/customizer.php:33,
#: framework/premium/extensions/shortcuts/customizer.php:778,
#: framework/features/header/items/account/options.php:457,
#: framework/features/header/items/account/options.php:831,
#: framework/features/header/items/account/options.php:919,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:28,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:53,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:376,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:36,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:61,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:106,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:60,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:61
msgid "Type 2"
msgstr "Loại 2"

#: framework/extensions/cookies-consent/customizer.php:40
msgid "Cookie period"
msgstr "Cookie period"

#: framework/extensions/cookies-consent/customizer.php:48
msgid "One hour"
msgstr "Một giờ"

#: framework/extensions/cookies-consent/customizer.php:49
msgid "One day"
msgstr "Một ngày"

#: framework/extensions/cookies-consent/customizer.php:50
msgid "One week"
msgstr "Một tuần"

#: framework/extensions/cookies-consent/customizer.php:51
msgid "One month"
msgstr "Một tháng"

#: framework/extensions/cookies-consent/customizer.php:52
msgid "Three months"
msgstr "Ba tháng"

#: framework/extensions/cookies-consent/customizer.php:53
msgid "Six months"
msgstr "Sáu tháng"

#: framework/extensions/cookies-consent/customizer.php:54
msgid "One year"
msgstr "Một năm"

#: framework/extensions/cookies-consent/customizer.php:55
msgid "Forever"
msgstr "Vĩnh viễn"

#: framework/extensions/cookies-consent/customizer.php:62,
#: framework/features/blocks/contact-info/options.php:87,
#: framework/features/blocks/contact-info/options.php:152,
#: framework/features/blocks/contact-info/options.php:215,
#: framework/features/blocks/contact-info/options.php:278,
#: framework/features/blocks/contact-info/options.php:341,
#: framework/features/blocks/contact-info/options.php:404,
#: framework/features/blocks/contact-info/options.php:467,
#: framework/premium/features/content-blocks/hooks-manager.php:267,
#: framework/premium/features/content-blocks/hooks-manager.php:275,
#: framework/premium/features/content-blocks/hooks-manager.php:283,
#: framework/premium/features/content-blocks/hooks-manager.php:291,
#: framework/premium/features/premium-header/items/contacts/options.php:97,
#: framework/premium/features/premium-header/items/contacts/options.php:142,
#: framework/premium/features/premium-header/items/contacts/options.php:187,
#: framework/premium/features/premium-header/items/contacts/options.php:232,
#: framework/premium/features/premium-header/items/contacts/options.php:278,
#: framework/premium/features/premium-header/items/contacts/options.php:323,
#: framework/premium/features/premium-header/items/contacts/options.php:368
msgid "Content"
msgstr "Nội dung"

#: framework/extensions/cookies-consent/customizer.php:64,
#: framework/extensions/cookies-consent/helpers.php:7
msgid "We use cookies to ensure that we give you the best experience on our website."
msgstr "Chúng tôi sử dụng cookie để đảm bảo sẽ mang đến cho bạn trải nghiệm tốt nhất trên trang web của chúng tôi."

#: framework/features/blocks/search/options.php:58
msgid "Button Text"
msgstr "Văn bản nút"

#: framework/extensions/cookies-consent/customizer.php:80,
#: framework/extensions/cookies-consent/helpers.php:10
msgid "Accept"
msgstr "Chấp nhận"

#: framework/extensions/cookies-consent/customizer.php:142,
#: framework/extensions/newsletter-subscribe/customizer.php:170,
#: framework/extensions/product-reviews/extension.php:422,
#: framework/extensions/trending/customizer.php:582,
#: framework/features/header/header-options.php:203,
#: framework/premium/extensions/mega-menu/options.php:567,
#: framework/premium/extensions/shortcuts/customizer.php:1032,
#: framework/features/header/items/account/options.php:1107,
#: framework/premium/features/content-blocks/options/404.php:119,
#: framework/premium/features/content-blocks/options/archive.php:196,
#: framework/premium/features/content-blocks/options/header.php:137,
#: framework/premium/features/content-blocks/options/hook.php:246,
#: framework/premium/features/content-blocks/options/maintenance.php:116,
#: framework/premium/features/content-blocks/options/nothing_found.php:137,
#: framework/premium/features/content-blocks/options/popup.php:517,
#: framework/premium/features/content-blocks/options/single.php:128,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:147,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:178,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:169,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:406,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:661,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:289,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:458,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:108,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:253,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:570,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:124,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:132,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:190,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:66,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:245,
#: framework/premium/features/premium-header/items/contacts/options.php:571,
#: framework/premium/features/premium-header/items/content-block/options.php:36,
#: framework/premium/features/premium-header/items/language-switcher/options.php:284,
#: framework/premium/features/premium-header/items/search-input/options.php:260,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:66,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:100,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:152,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:154
msgid "Design"
msgstr "Thiết kế"

#: framework/extensions/cookies-consent/customizer.php:203,
#: framework/extensions/cookies-consent/customizer.php:270,
#: framework/premium/extensions/mega-menu/options.php:879,
#: framework/premium/extensions/shortcuts/customizer.php:1058,
#: framework/features/header/items/account/options.php:1518,
#: framework/features/header/items/account/options.php:1902,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:202,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:233,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:262,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:195,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:113,
#: framework/premium/features/premium-header/items/contacts/options.php:585,
#: framework/premium/features/premium-header/items/contacts/options.php:614,
#: framework/premium/features/premium-header/items/contacts/options.php:655,
#: framework/premium/features/premium-header/items/contacts/options.php:694,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:111,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:222,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:251,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:281,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:310,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:52,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:81,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:111,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:140,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:217,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:261,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:303,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:209,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:240,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:269
msgid "Font Color"
msgstr "Mầu phông chữ"

#: framework/extensions/cookies-consent/customizer.php:164,
#: framework/extensions/cookies-consent/customizer.php:191,
#: framework/extensions/cookies-consent/customizer.php:220,
#: framework/extensions/cookies-consent/customizer.php:251,
#: framework/extensions/cookies-consent/customizer.php:287,
#: framework/extensions/cookies-consent/customizer.php:316,
#: framework/extensions/newsletter-subscribe/customizer.php:188,
#: framework/extensions/newsletter-subscribe/customizer.php:213,
#: framework/extensions/newsletter-subscribe/customizer.php:245,
#: framework/extensions/newsletter-subscribe/customizer.php:276,
#: framework/extensions/newsletter-subscribe/customizer.php:313,
#: framework/extensions/newsletter-subscribe/customizer.php:345,
#: framework/extensions/trending/customizer.php:610,
#: framework/extensions/trending/customizer.php:673,
#: framework/extensions/trending/customizer.php:726,
#: framework/extensions/trending/customizer.php:766,
#: framework/extensions/trending/customizer.php:798,
#: framework/extensions/trending/customizer.php:848,
#: framework/extensions/trending/customizer.php:889,
#: framework/premium/extensions/mega-menu/options.php:845,
#: framework/premium/extensions/mega-menu/options.php:892,
#: framework/premium/extensions/mega-menu/options.php:911,
#: framework/premium/extensions/shortcuts/customizer.php:1076,
#: framework/premium/extensions/shortcuts/customizer.php:1110,
#: framework/premium/extensions/shortcuts/customizer.php:1142,
#: framework/features/header/items/account/options.php:1198,
#: framework/features/header/items/account/options.php:1242,
#: framework/features/header/items/account/options.php:1284,
#: framework/features/header/items/account/options.php:1389,
#: framework/features/header/items/account/options.php:1432,
#: framework/features/header/items/account/options.php:1473,
#: framework/features/header/items/account/options.php:1539,
#: framework/features/header/items/account/options.php:1572,
#: framework/features/header/items/account/options.php:1610,
#: framework/features/header/items/account/options.php:1653,
#: framework/features/header/items/account/options.php:1758,
#: framework/features/header/items/account/options.php:1801,
#: framework/features/header/items/account/options.php:1852,
#: framework/features/header/items/account/options.php:1974,
#: framework/premium/features/content-blocks/options/popup.php:632,
#: framework/premium/features/content-blocks/options/popup.php:663,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:219,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:250,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:279,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:345,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:376,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:405,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1430,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:220,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:234,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:270,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:196,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:232,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:433,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:463,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:797,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:480,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:511,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:126,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:349,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:383,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:422,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:151,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:180,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:159,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:189,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:209,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:239,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:269,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:311,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:341,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:381,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:409,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:27,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:50,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:215,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:231,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:267,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:308,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:82,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:275,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:319,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:363,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:403,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:442,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:487,
#: framework/premium/features/premium-header/items/contacts/options.php:797,
#: framework/premium/features/premium-header/items/contacts/options.php:828,
#: framework/premium/features/premium-header/items/contacts/options.php:857,
#: framework/premium/features/premium-header/items/contacts/options.php:956,
#: framework/premium/features/premium-header/items/contacts/options.php:994,
#: framework/premium/features/premium-header/items/contacts/options.php:1032,
#: framework/premium/features/premium-header/items/search-input/options.php:313,
#: framework/premium/features/premium-header/items/search-input/options.php:345,
#: framework/premium/features/premium-header/items/search-input/options.php:375,
#: framework/premium/features/premium-header/items/search-input/options.php:443,
#: framework/premium/features/premium-header/items/search-input/options.php:475,
#: framework/premium/features/premium-header/items/search-input/options.php:505,
#: framework/premium/features/premium-header/items/search-input/options.php:573,
#: framework/premium/features/premium-header/items/search-input/options.php:605,
#: framework/premium/features/premium-header/items/search-input/options.php:635,
#: framework/premium/features/premium-header/items/search-input/options.php:708,
#: framework/premium/features/premium-header/items/search-input/options.php:738,
#: framework/premium/features/premium-header/items/search-input/options.php:768,
#: framework/premium/features/premium-header/items/search-input/options.php:851,
#: framework/premium/features/premium-header/items/search-input/options.php:879,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:84,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:105,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:128,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:154,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:268,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:298,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:327,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:399,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:430,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:459,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:98,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:128,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:157,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:241,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:285,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:327,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:403,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:438,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:471,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:226,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:257,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:286,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:353,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:384,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:413
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:261
#: static/js/editor/blocks/about-me/Edit.js:189
#: static/js/editor/blocks/contact-info/Edit.js:203
#: static/js/editor/blocks/search/Edit.js:409
#: static/js/editor/blocks/share-box/Edit.js:152
#: static/js/editor/blocks/socials/Edit.js:152
msgid "Initial"
msgstr "Ban đầu"

#: framework/extensions/cookies-consent/customizer.php:170,
#: framework/extensions/cookies-consent/customizer.php:226,
#: framework/extensions/cookies-consent/customizer.php:257,
#: framework/extensions/cookies-consent/customizer.php:292,
#: framework/extensions/cookies-consent/customizer.php:321,
#: framework/extensions/newsletter-subscribe/customizer.php:219,
#: framework/extensions/newsletter-subscribe/customizer.php:350,
#: framework/extensions/trending/customizer.php:678,
#: framework/extensions/trending/customizer.php:731,
#: framework/extensions/trending/customizer.php:772,
#: framework/extensions/trending/customizer.php:804,
#: framework/extensions/trending/customizer.php:895,
#: framework/premium/extensions/mega-menu/options.php:850,
#: framework/premium/extensions/shortcuts/customizer.php:1082,
#: framework/premium/extensions/shortcuts/customizer.php:1116,
#: framework/premium/extensions/shortcuts/customizer.php:1148,
#: framework/features/header/items/account/options.php:1207,
#: framework/features/header/items/account/options.php:1250,
#: framework/features/header/items/account/options.php:1292,
#: framework/features/header/items/account/options.php:1398,
#: framework/features/header/items/account/options.php:1440,
#: framework/features/header/items/account/options.php:1481,
#: framework/features/header/items/account/options.php:1545,
#: framework/features/header/items/account/options.php:1764,
#: framework/features/header/items/account/options.php:1810,
#: framework/features/header/items/account/options.php:1861,
#: framework/features/header/items/account/options.php:1979,
#: framework/premium/features/content-blocks/options/popup.php:638,
#: framework/premium/features/content-blocks/options/popup.php:669,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:225,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:255,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:284,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:351,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:381,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:410,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1436,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:802,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:486,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:517,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:354,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:389,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:428,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:245,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:275,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:317,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:347,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:386,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:414,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:63,
#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:56,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:236,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:273,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:314,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:281,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:325,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:369,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:409,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:448,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:493,
#: framework/premium/features/premium-header/items/contacts/options.php:803,
#: framework/premium/features/premium-header/items/contacts/options.php:833,
#: framework/premium/features/premium-header/items/contacts/options.php:862,
#: framework/premium/features/premium-header/items/contacts/options.php:961,
#: framework/premium/features/premium-header/items/contacts/options.php:999,
#: framework/premium/features/premium-header/items/contacts/options.php:1037,
#: framework/premium/features/premium-header/items/search-input/options.php:856,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:133,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:250,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:293,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:335,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:409,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:443,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:476,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:232,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:262,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:291,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:359,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:389,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:418
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:270
#: static/js/editor/blocks/about-me/Edit.js:198
#: static/js/editor/blocks/contact-info/Edit.js:212
#: static/js/editor/blocks/search/Edit.js:368
#: static/js/editor/blocks/share-box/Edit.js:161
#: static/js/editor/blocks/socials/Edit.js:161
msgid "Hover"
msgstr "Di chuột"

#: framework/extensions/newsletter-subscribe/customizer.php:328,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:253
msgid "Button Color"
msgstr "Màu nút"

#: framework/extensions/cookies-consent/customizer.php:178,
#: framework/extensions/cookies-consent/customizer.php:234,
#: framework/extensions/cookies-consent/customizer.php:299,
#: framework/premium/extensions/mega-menu/options.php:899,
#: framework/features/header/items/account/options.php:1829,
#: framework/features/header/items/account/options.php:1953,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:134,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:405,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:289,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:421,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:92,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:141
msgid "Background Color"
msgstr "Màu nền"

#: framework/extensions/cookies-consent/customizer.php:98
msgid "Maximum Width"
msgstr "Chiều rộng tối đa"

#: framework/extensions/cookies-consent/customizer.php:119
msgid "I accept the %sPrivacy Policy%s*"
msgstr "Tôi chấp nhận %sChính sách bảo mật%s*"

#: framework/extensions/cookies-consent/customizer.php:123
msgid "This text will appear under each comment form and subscribe form."
msgstr "Văn bản này sẽ xuất hiện dưới mỗi biểu mẫu bình luận và biểu mẫu đăng ký."

#: framework/extensions/cookies-consent/helpers.php:55
msgid "I accept the %sPrivacy Policy%s"
msgstr "Tôi chấp nhận %sChính sách bảo mật%s"

#: framework/features/blocks/about-me/options.php:128
msgid "Customizer"
msgstr "Người tùy chỉnh"

#: framework/extensions/newsletter-subscribe/config.php:6
msgid "Easily capture new leads for your newsletter with the help of a widget, shortcode or even a block inserted on your pages or posts."
msgstr "Dễ dàng tạo khách hàng tiềm năng mới cho bản tin của bạn với sự giúp đỡ của một tiện ích, mã rút gọn hoặc thậm chí một khối được chèn vào trang hoặc bài viết của bạn."

#: framework/extensions/newsletter-subscribe/customizer.php:4
msgid "Subscribe Form"
msgstr "Biểu mẫu đăng ký"

#: framework/extensions/newsletter-subscribe/customizer.php:18,
#: framework/features/blocks/about-me/options.php:15,
#: framework/features/blocks/contact-info/options.php:34,
#: framework/features/blocks/contact-info/options.php:80,
#: framework/features/blocks/contact-info/options.php:145,
#: framework/features/blocks/contact-info/options.php:208,
#: framework/features/blocks/contact-info/options.php:271,
#: framework/features/blocks/contact-info/options.php:334,
#: framework/features/blocks/contact-info/options.php:397,
#: framework/features/blocks/contact-info/options.php:460,
#: framework/features/blocks/share-box/options.php:14,
#: framework/features/blocks/socials/options.php:14,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:582,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:327,
#: framework/premium/features/premium-header/items/contacts/options.php:87,
#: framework/premium/features/premium-header/items/contacts/options.php:132,
#: framework/premium/features/premium-header/items/contacts/options.php:177,
#: framework/premium/features/premium-header/items/contacts/options.php:222,
#: framework/premium/features/premium-header/items/contacts/options.php:268,
#: framework/premium/features/premium-header/items/contacts/options.php:313,
#: framework/premium/features/premium-header/items/contacts/options.php:358
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:45
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:125
msgid "Title"
msgstr "Tiêu đề"

#: framework/extensions/newsletter-subscribe/customizer.php:20,
#: framework/extensions/newsletter-subscribe/helpers.php:21,
#: framework/extensions/newsletter-subscribe/helpers.php:69
msgid "Newsletter Updates"
msgstr "Cập nhật bản tin"

#: framework/extensions/newsletter-subscribe/customizer.php:26,
#: framework/features/blocks/about-me/options.php:64,
#: framework/features/blocks/dynamic-data/options.php:142,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:78,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:159,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:248
msgid "Description"
msgstr "Mô tả"

#: framework/extensions/newsletter-subscribe/helpers.php:72
msgid "Enter your email address below to subscribe to our newsletter"
msgstr "Nhập địa chỉ email của bạn dưới đây để Đăng ký nhận bản tin của chúng tôi"

#: framework/extensions/newsletter-subscribe/customizer.php:41,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:34
msgid "List Source"
msgstr "Nguồn danh sách"

#: framework/extensions/newsletter-subscribe/customizer.php:48,
#: framework/extensions/product-reviews/metabox.php:17,
#: framework/extensions/trending/customizer.php:232,
#: framework/extensions/trending/customizer.php:306,
#: framework/extensions/trending/customizer.php:448,
#: framework/features/header/header-options.php:76,
#: framework/premium/features/premium-header.php:322,
#: framework/premium/features/socials.php:20,
#: framework/premium/features/socials.php:48,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:40,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:81,
#: framework/features/blocks/contact-info/options.php:110,
#: framework/features/blocks/contact-info/options.php:175,
#: framework/features/blocks/contact-info/options.php:238,
#: framework/features/blocks/contact-info/options.php:301,
#: framework/features/blocks/contact-info/options.php:364,
#: framework/features/blocks/contact-info/options.php:427,
#: framework/features/blocks/contact-info/options.php:490,
#: framework/features/header/items/account/options.php:385,
#: framework/features/header/items/account/options.php:766,
#: framework/premium/features/content-blocks/options/hook.php:166,
#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:19,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:12,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:17,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:17
msgid "Default"
msgstr "Mặc định"

#: framework/extensions/newsletter-subscribe/customizer.php:49,
#: framework/extensions/trending/customizer.php:236,
#: framework/premium/features/premium-header.php:323,
#: framework/premium/features/socials.php:21,
#: framework/premium/features/socials.php:49,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:41,
#: framework/features/blocks/about-me/options.php:27,
#: framework/features/blocks/about-me/options.php:194,
#: framework/features/blocks/contact-info/options.php:111,
#: framework/features/blocks/contact-info/options.php:176,
#: framework/features/blocks/contact-info/options.php:239,
#: framework/features/blocks/contact-info/options.php:302,
#: framework/features/blocks/contact-info/options.php:365,
#: framework/features/blocks/contact-info/options.php:428,
#: framework/features/blocks/contact-info/options.php:491,
#: framework/features/blocks/dynamic-data/options.php:64,
#: framework/features/blocks/share-box/options.php:148,
#: framework/features/blocks/socials/options.php:100,
#: framework/features/conditions/rules/custom.php:33,
#: framework/premium/extensions/shortcuts/customizer.php:260,
#: framework/premium/extensions/shortcuts/customizer.php:287,
#: framework/features/header/items/account/options.php:25,
#: framework/features/header/items/account/options.php:389,
#: framework/features/header/items/account/options.php:770,
#: framework/premium/features/content-blocks/options/archive.php:75,
#: framework/premium/features/content-blocks/options/hook.php:168,
#: framework/premium/features/content-blocks/options/popup.php:287,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:616,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:196,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:188,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:41,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:843,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:18,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:18
#: static/js/editor/blocks/dynamic-data/utils.js:10
msgid "Custom"
msgstr "Tùy chỉnh"

#: framework/extensions/newsletter-subscribe/customizer.php:61,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:50
#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:199
msgid "List ID"
msgstr "ID danh sách"

#: framework/extensions/newsletter-subscribe/customizer.php:79,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:109
msgid "Name Field"
msgstr "Trường Tên"

#: framework/extensions/newsletter-subscribe/customizer.php:117,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:121
msgid "Name Label"
msgstr "Nhãn Tên"

#: framework/extensions/newsletter-subscribe/customizer.php:119,
#: framework/extensions/newsletter-subscribe/extension.php:208,
#: framework/extensions/newsletter-subscribe/helpers.php:37,
#: framework/extensions/newsletter-subscribe/helpers.php:81,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:122,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:147
msgid "Your name"
msgstr "Tên của bạn"

#: framework/extensions/newsletter-subscribe/customizer.php:128,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:136
msgid "Mail Label"
msgstr "Nhãn mail"

#: framework/extensions/newsletter-subscribe/customizer.php:130,
#: framework/extensions/newsletter-subscribe/extension.php:209,
#: framework/extensions/newsletter-subscribe/helpers.php:41,
#: framework/extensions/newsletter-subscribe/helpers.php:82
msgid "Your email"
msgstr "Email của bạn"

#: framework/extensions/newsletter-subscribe/customizer.php:137,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:142,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:414
msgid "Button Label"
msgstr "Nhãn nút"

#: framework/extensions/newsletter-subscribe/customizer.php:139,
#: framework/extensions/newsletter-subscribe/extension.php:203,
#: framework/extensions/newsletter-subscribe/helpers.php:31,
#: framework/extensions/newsletter-subscribe/helpers.php:76,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:143,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:21
msgid "Subscribe"
msgstr "Đăng ký"

#: framework/extensions/newsletter-subscribe/customizer.php:149,
#: framework/extensions/trending/customizer.php:516,
#: framework/premium/extensions/shortcuts/customizer.php:48,
#: framework/premium/extensions/shortcuts/customizer.php:113,
#: framework/premium/extensions/shortcuts/customizer.php:178,
#: framework/premium/extensions/shortcuts/customizer.php:237,
#: framework/premium/extensions/shortcuts/customizer.php:326,
#: framework/premium/extensions/shortcuts/customizer.php:388,
#: framework/premium/extensions/shortcuts/customizer.php:447,
#: framework/premium/extensions/shortcuts/customizer.php:517,
#: framework/premium/extensions/shortcuts/customizer.php:581,
#: framework/premium/extensions/shortcuts/customizer.php:645,
#: framework/premium/extensions/shortcuts/customizer.php:709,
#: framework/premium/extensions/shortcuts/customizer.php:996,
#: framework/premium/features/content-blocks/options/header.php:167,
#: framework/premium/features/content-blocks/options/hook.php:300,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:155,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:423,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:43
msgid "Visibility"
msgstr "Hiển thị"

#: framework/extensions/newsletter-subscribe/customizer.php:160,
#: framework/extensions/trending/customizer.php:528,
#: framework/features/header/header-options.php:108,
#: framework/features/header/header-options.php:190,
#: framework/features/blocks/contact-info/options.php:24,
#: framework/features/blocks/search/options.php:168,
#: framework/premium/extensions/mega-menu/options.php:413,
#: framework/premium/extensions/shortcuts/customizer.php:62,
#: framework/premium/extensions/shortcuts/customizer.php:127,
#: framework/premium/extensions/shortcuts/customizer.php:192,
#: framework/premium/extensions/shortcuts/customizer.php:251,
#: framework/premium/extensions/shortcuts/customizer.php:341,
#: framework/premium/extensions/shortcuts/customizer.php:402,
#: framework/premium/extensions/shortcuts/customizer.php:461,
#: framework/premium/extensions/shortcuts/customizer.php:531,
#: framework/premium/extensions/shortcuts/customizer.php:595,
#: framework/premium/extensions/shortcuts/customizer.php:659,
#: framework/premium/extensions/shortcuts/customizer.php:723,
#: framework/premium/extensions/shortcuts/customizer.php:861,
#: framework/premium/extensions/shortcuts/customizer.php:918,
#: framework/premium/extensions/shortcuts/customizer.php:1010,
#: framework/features/header/items/account/options.php:532,
#: framework/features/header/items/account/options.php:995,
#: framework/premium/features/content-blocks/options/header.php:178,
#: framework/premium/features/content-blocks/options/hook.php:311,
#: framework/premium/features/content-blocks/options/popup.php:506,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:70,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:168,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:396,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:651,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:436,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:42,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:61,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:80,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:98,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:90,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:258,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:286,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:49,
#: framework/premium/features/premium-header/items/contacts/options.php:32,
#: framework/premium/features/premium-header/items/contacts/options.php:553,
#: framework/premium/features/premium-header/items/language-switcher/options.php:131,
#: framework/premium/features/premium-header/items/search-input/options.php:157,
#: framework/premium/features/premium-header/items/search-input/options.php:250,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:56,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:104,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:105
msgid "Desktop"
msgstr "Máy tính"

#: framework/extensions/newsletter-subscribe/customizer.php:161,
#: framework/extensions/trending/customizer.php:529,
#: framework/features/blocks/contact-info/options.php:25,
#: framework/features/blocks/search/options.php:169,
#: framework/premium/extensions/shortcuts/customizer.php:63,
#: framework/premium/extensions/shortcuts/customizer.php:128,
#: framework/premium/extensions/shortcuts/customizer.php:193,
#: framework/premium/extensions/shortcuts/customizer.php:252,
#: framework/premium/extensions/shortcuts/customizer.php:342,
#: framework/premium/extensions/shortcuts/customizer.php:403,
#: framework/premium/extensions/shortcuts/customizer.php:462,
#: framework/premium/extensions/shortcuts/customizer.php:532,
#: framework/premium/extensions/shortcuts/customizer.php:596,
#: framework/premium/extensions/shortcuts/customizer.php:660,
#: framework/premium/extensions/shortcuts/customizer.php:724,
#: framework/premium/extensions/shortcuts/customizer.php:862,
#: framework/premium/extensions/shortcuts/customizer.php:919,
#: framework/premium/extensions/shortcuts/customizer.php:1011,
#: framework/features/header/items/account/options.php:533,
#: framework/features/header/items/account/options.php:996,
#: framework/features/header/items/account/options.php:2069,
#: framework/premium/features/content-blocks/options/header.php:179,
#: framework/premium/features/content-blocks/options/hook.php:312,
#: framework/premium/features/content-blocks/options/popup.php:507,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:71,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:452,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:169,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:397,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:652,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:437,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:43,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:62,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:99,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:91,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:259,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:287,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:50,
#: framework/premium/features/premium-header/items/contacts/options.php:33,
#: framework/premium/features/premium-header/items/contacts/options.php:497,
#: framework/premium/features/premium-header/items/contacts/options.php:554,
#: framework/premium/features/premium-header/items/language-switcher/options.php:132,
#: framework/premium/features/premium-header/items/language-switcher/options.php:261,
#: framework/premium/features/premium-header/items/search-input/options.php:158,
#: framework/premium/features/premium-header/items/search-input/options.php:251,
#: framework/premium/features/premium-header/items/search-input/options.php:947,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:57,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:105,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:655,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:106,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:589
msgid "Tablet"
msgstr "Máy tính bảng"

#: framework/extensions/newsletter-subscribe/customizer.php:162,
#: framework/extensions/trending/customizer.php:530,
#: framework/features/header/header-options.php:110,
#: framework/features/header/header-options.php:192,
#: framework/features/blocks/contact-info/options.php:26,
#: framework/features/blocks/contact-info/options.php:202,
#: framework/features/blocks/search/options.php:170,
#: framework/premium/extensions/mega-menu/options.php:414,
#: framework/premium/extensions/shortcuts/customizer.php:64,
#: framework/premium/extensions/shortcuts/customizer.php:129,
#: framework/premium/extensions/shortcuts/customizer.php:194,
#: framework/premium/extensions/shortcuts/customizer.php:253,
#: framework/premium/extensions/shortcuts/customizer.php:343,
#: framework/premium/extensions/shortcuts/customizer.php:404,
#: framework/premium/extensions/shortcuts/customizer.php:463,
#: framework/premium/extensions/shortcuts/customizer.php:533,
#: framework/premium/extensions/shortcuts/customizer.php:597,
#: framework/premium/extensions/shortcuts/customizer.php:661,
#: framework/premium/extensions/shortcuts/customizer.php:725,
#: framework/premium/extensions/shortcuts/customizer.php:863,
#: framework/premium/extensions/shortcuts/customizer.php:920,
#: framework/premium/extensions/shortcuts/customizer.php:1012,
#: framework/features/header/items/account/options.php:534,
#: framework/features/header/items/account/options.php:997,
#: framework/features/header/items/account/options.php:2070,
#: framework/premium/features/content-blocks/options/header.php:180,
#: framework/premium/features/content-blocks/options/hook.php:313,
#: framework/premium/features/content-blocks/options/popup.php:508,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:72,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:453,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:170,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:398,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:653,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:438,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:44,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:63,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:82,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:100,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:92,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:260,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:288,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:51,
#: framework/premium/features/premium-header/items/contacts/options.php:34,
#: framework/premium/features/premium-header/items/contacts/options.php:172,
#: framework/premium/features/premium-header/items/contacts/options.php:498,
#: framework/premium/features/premium-header/items/contacts/options.php:555,
#: framework/premium/features/premium-header/items/language-switcher/options.php:133,
#: framework/premium/features/premium-header/items/language-switcher/options.php:262,
#: framework/premium/features/premium-header/items/search-input/options.php:159,
#: framework/premium/features/premium-header/items/search-input/options.php:252,
#: framework/premium/features/premium-header/items/search-input/options.php:948,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:58,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:106,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:656,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:107,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:590
msgid "Mobile"
msgstr "Di động"

#: framework/extensions/newsletter-subscribe/customizer.php:175,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:183,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:420,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:138,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:146
msgid "Title Color"
msgstr "Màu chữ tiêu đề"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:197
msgid "Description Color"
msgstr "Màu mô tả"

#: framework/extensions/newsletter-subscribe/customizer.php:227,
#: framework/features/header/items/account/options.php:1553,
#: framework/premium/features/premium-header/items/search-input/options.php:266,
#: framework/premium/features/premium-header/items/search-input/options.php:295,
#: framework/premium/features/premium-header/items/search-input/options.php:327,
#: framework/premium/features/premium-header/items/search-input/options.php:357
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:128
#: static/js/editor/blocks/search/Edit.js:196
msgid "Input Font Color"
msgstr "Màu phông chữ đầu vào"

#: framework/extensions/newsletter-subscribe/customizer.php:251,
#: framework/extensions/newsletter-subscribe/customizer.php:282,
#: framework/extensions/newsletter-subscribe/customizer.php:318,
#: framework/features/header/items/account/options.php:1579,
#: framework/features/header/items/account/options.php:1617,
#: framework/features/header/items/account/options.php:1663,
#: framework/premium/features/premium-header/items/search-input/options.php:319,
#: framework/premium/features/premium-header/items/search-input/options.php:350,
#: framework/premium/features/premium-header/items/search-input/options.php:380,
#: framework/premium/features/premium-header/items/search-input/options.php:449,
#: framework/premium/features/premium-header/items/search-input/options.php:480,
#: framework/premium/features/premium-header/items/search-input/options.php:510,
#: framework/premium/features/premium-header/items/search-input/options.php:579,
#: framework/premium/features/premium-header/items/search-input/options.php:610,
#: framework/premium/features/premium-header/items/search-input/options.php:640,
#: framework/premium/features/premium-header/items/search-input/options.php:713,
#: framework/premium/features/premium-header/items/search-input/options.php:743,
#: framework/premium/features/premium-header/items/search-input/options.php:773
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:210
#: static/js/editor/blocks/search/Edit.js:269
msgid "Focus"
msgstr "Tập trung"

#: framework/extensions/newsletter-subscribe/customizer.php:259,
#: framework/features/header/items/account/options.php:1588,
#: framework/premium/features/premium-header/items/search-input/options.php:526,
#: framework/premium/features/premium-header/items/search-input/options.php:555,
#: framework/premium/features/premium-header/items/search-input/options.php:587,
#: framework/premium/features/premium-header/items/search-input/options.php:617
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:159
#: static/js/editor/blocks/search/Edit.js:223
msgid "Input Border Color"
msgstr "Màu nền đầu vào"

#: framework/extensions/newsletter-subscribe/customizer.php:296,
#: framework/features/header/items/account/options.php:1631,
#: framework/premium/features/premium-header/items/search-input/options.php:662,
#: framework/premium/features/premium-header/items/search-input/options.php:690,
#: framework/premium/features/premium-header/items/search-input/options.php:720,
#: framework/premium/features/premium-header/items/search-input/options.php:750
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:190
#: static/js/editor/blocks/search/Edit.js:250
msgid "Input Background Color"
msgstr "Màu nền đầu vào"

#: framework/extensions/newsletter-subscribe/customizer.php:357,
#: framework/extensions/trending/customizer.php:903,
#: framework/premium/extensions/shortcuts/customizer.php:1335,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:525,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:209
msgid "Container Background"
msgstr "Nền vùng chứa"

#: framework/extensions/newsletter-subscribe/customizer.php:373,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:193
msgid "Container Border"
msgstr "Viền Container"

#: framework/extensions/newsletter-subscribe/customizer.php:408,
#: framework/extensions/trending/customizer.php:919
msgid "Container Inner Spacing"
msgstr "Kho���ng cách trong vùng chứa"

#: framework/extensions/newsletter-subscribe/customizer.php:422,
#: framework/premium/extensions/shortcuts/customizer.php:1368,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:237
msgid "Container Border Radius"
msgstr "Góc bo đường viền vùng chứa"

#: framework/extensions/newsletter-subscribe/extension.php:155
msgid "Disable Subscribe Form"
msgstr "Tắt biểu mẫu đăng ký"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:253,
#: framework/features/blocks/about-me/options.php:58,
#: framework/features/header/items/account/options.php:578,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:167
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:128
msgid "Name"
msgstr "Tên"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:262,
#: framework/features/blocks/contact-info/options.php:391,
#: framework/features/blocks/dynamic-data/options.php:143,
#: framework/features/blocks/share-box/options.php:97,
#: framework/features/header/modal/register.php:47,
#: framework/premium/extensions/shortcuts/customizer.php:136,
#: framework/premium/extensions/shortcuts/customizer.php:162,
#: framework/premium/extensions/shortcuts/views/bar.php:48,
#: framework/premium/features/premium-header/items/contacts/options.php:308,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:168
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:23
msgid "Email"
msgstr "Email"

#: framework/extensions/product-reviews/extension.php:540,
#: framework/extensions/product-reviews/extension.php:541,
#: framework/extensions/product-reviews/extension.php:544,
#: framework/extensions/product-reviews/extension.php:546
msgid "Product Reviews"
msgstr "Đánh giá sản phẩm"

#: framework/extensions/product-reviews/config.php:6
msgid "This extension lets you easily create an affiliate marketing type of website by giving you options to create a personalized product review and use your affiliate links to direct your readers to the purchase page."
msgstr "Phần mở rộng này giúp bạn dễ dàng tạo ra một trang web loại tiếp thị liên kết bằng cách cung cấp cho bạn các tùy chọn để tạo một bài đánh giá sản phẩm cá nhân và sử dụng liên kết liên kết của bạn để đưa độc giả của bạn đến trang mua hàng."

#: framework/extensions/product-reviews/extension.php:318,
#: framework/extensions/product-reviews/views/single-top.php:139
msgid "Overall Score"
msgstr "Điểm tổng thể"

#: framework/extensions/product-reviews/extension.php:376
msgid "Review Summary"
msgstr "Tóm tắt đánh giá"

#: framework/extensions/product-reviews/extension.php:387
msgid "Scores Box Width"
msgstr "Chiều rộng hộp điểm số"

#: framework/extensions/product-reviews/extension.php:397
msgid "Read More Button"
msgstr "Nút xem thêm"

#: framework/extensions/product-reviews/extension.php:408
msgid "Buy Now Button"
msgstr "Nút mua ngay"

#: framework/extensions/product-reviews/extension.php:256,
#: framework/extensions/product-reviews/extension.php:427
msgid "Star Rating Color"
msgstr "Màu sao đánh giá"

#: framework/extensions/product-reviews/extension.php:274,
#: framework/extensions/product-reviews/extension.php:444,
#: framework/extensions/product-reviews/extension.php:472,
#: framework/extensions/product-reviews/extension.php:493,
#: framework/premium/extensions/mega-menu/options.php:855,
#: framework/features/header/items/account/options.php:1985,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:221,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:227,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:88,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:94,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:286,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:330,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:375,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:414,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:454,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:498
#: static/js/dashboard/screens/Extensions/Sidebar.js:120
msgid "Active"
msgstr "Hoạt động"

#: framework/extensions/product-reviews/extension.php:280,
#: framework/extensions/product-reviews/extension.php:450
msgid "Inactive"
msgstr "Không hoạt động"

#: framework/extensions/product-reviews/extension.php:458
msgid "Overll Score Text"
msgstr "Văn bản điểm tổng thể"

#: framework/extensions/product-reviews/extension.php:480
msgid "Overll Score Background"
msgstr "Nền điểm tổng thể"

#: framework/extensions/product-reviews/extension.php:545
msgid "Product Review"
msgstr "Đánh giá sản phẩm"

#: framework/extensions/product-reviews/extension.php:547
msgid "Parent Product Review"
msgstr "Đánh giá sản phẩm cha"

#: framework/extensions/product-reviews/extension.php:548
msgid "All Reviews"
msgstr "Tất cả đánh giá"

#: framework/extensions/product-reviews/extension.php:549
msgid "View Product Review"
msgstr "Xem đánh giá sản phẩm"

#: framework/extensions/product-reviews/extension.php:550
msgid "Add New Product Review"
msgstr "Thêm đánh giá mới về sản phẩm"

#: framework/extensions/product-reviews/extension.php:551
msgid "Add New Review"
msgstr "Thêm đánh giá mới"

#: framework/extensions/product-reviews/extension.php:552
msgid "Edit Product Review"
msgstr "Sửa đánh giá sản phẩm"

#: framework/extensions/product-reviews/extension.php:553
msgid "Update Product Review"
msgstr "Cập nhật đánh giá sản phẩm"

#: framework/extensions/product-reviews/extension.php:554
msgid "Search Product Review"
msgstr "Tìm kiếm đánh giá sản phẩm"

#: framework/extensions/product-reviews/extension.php:555
msgid "Not Found"
msgstr "Không tìm thấy"

#: framework/extensions/product-reviews/extension.php:556
msgid "Not found in Trash"
msgstr "Không có trong thùng rác"

#: framework/extensions/product-reviews/extension.php:590,
#: framework/extensions/product-reviews/extension.php:600,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:100,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:190
msgid "Categories"
msgstr "Danh mục"

#: framework/extensions/product-reviews/extension.php:591,
#: framework/extensions/trending/customizer.php:36,
#: framework/extensions/trending/customizer.php:117
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:88
msgid "Category"
msgstr "Danh mục"

#: framework/extensions/product-reviews/extension.php:592
msgid "Search Category"
msgstr "Tìm kiếm danh mục"

#: framework/extensions/product-reviews/extension.php:593
#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:88
msgid "All Categories"
msgstr "Tất cả danh mục"

#: framework/extensions/product-reviews/extension.php:594
msgid "Parent Category"
msgstr "Danh mục cha"

#: framework/extensions/product-reviews/extension.php:595
msgid "Parent Category:"
msgstr "Danh mục cha:"

#: framework/extensions/product-reviews/extension.php:596
msgid "Edit Category"
msgstr "Sửa danh mục"

#: framework/extensions/product-reviews/extension.php:597
msgid "Update Category"
msgstr "Cập nhật danh mục"

#: framework/extensions/product-reviews/extension.php:598
msgid "Add New Category"
msgstr "Thêm danh mục mới"

#: framework/extensions/product-reviews/extension.php:599
msgid "New Category Name"
msgstr "Tên danh mục mới"

#. translators: %s is the theme name.
#: framework/extensions/product-reviews/extension.php:618
msgid "%s Settings"
msgstr "%s Cài đặt"

#: framework/dashboard.php:476, framework/dashboard.php:477,
#: framework/extensions/product-reviews/extension.php:619
msgid "Blocksy"
msgstr "Blocksy"

#: framework/extensions/product-reviews/helpers.php:30,
#: framework/extensions/product-reviews/metabox.php:181,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:230,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:394
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:132
msgid "Rating"
msgstr "Xếp hạng"

#: framework/extensions/product-reviews/metabox.php:10
msgid "Review Entity"
msgstr "Lượt nhập đánh giá"

#: framework/extensions/product-reviews/metabox.php:18,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/table.php:39,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:34,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:52,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:33,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:33
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:11
msgid "Product"
msgstr "Sản phẩm"

#: framework/extensions/product-reviews/metabox.php:19
msgid "Book"
msgstr "Sách"

#: framework/extensions/product-reviews/metabox.php:21
msgid "Creative Work Season"
msgstr "Mùa làm việc sáng tạo"

#: framework/extensions/product-reviews/metabox.php:22
msgid "Creative Work Series"
msgstr "Chuỗi công việc sáng tạo"

#: framework/extensions/product-reviews/metabox.php:23
msgid "Episode"
msgstr "Tập phim"

#: framework/extensions/product-reviews/metabox.php:25
msgid "Game"
msgstr "Trò chơi"

#: framework/extensions/product-reviews/metabox.php:27
msgid "Local Business"
msgstr "Địa điểm kinh doanh"

#: framework/extensions/product-reviews/metabox.php:28
msgid "Media Object"
msgstr "Đối tượng Media"

#: framework/extensions/product-reviews/metabox.php:29
msgid "Movie"
msgstr "Phim"

#: framework/extensions/product-reviews/metabox.php:30
msgid "Music Playlist"
msgstr "Danh sách phát nhạc"

#: framework/extensions/product-reviews/metabox.php:31
msgid "Music Recording"
msgstr "Ghi âm nhạc"

#: framework/extensions/product-reviews/metabox.php:32
msgid "Organization"
msgstr "Tổ chức"

#: framework/extensions/product-reviews/metabox.php:38
msgid "More info about review entity and how to choose the right one can be found %shere%s."
msgstr "Thông tin thêm về đơn vị đánh giá và cách lựa chọn đúng có thể được tìm thấy %stại đây%s."

#: framework/extensions/product-reviews/metabox.php:69
msgid "Product Price"
msgstr "Giá sản phẩm"

#: framework/extensions/product-reviews/metabox.php:76
msgid "Product SKU"
msgstr "Mã sản phẩm"

#: framework/extensions/product-reviews/metabox.php:83
msgid "Product Brand"
msgstr "Thương hiệu sản phẩm"

#: framework/extensions/product-reviews/metabox.php:96
msgid "Gallery"
msgstr "Gallery"

#: framework/extensions/product-reviews/metabox.php:107
msgid "Affiliate Button Label"
msgstr "Nhãn Nút Affiliate"

#: framework/extensions/product-reviews/metabox.php:109,
#: framework/extensions/product-reviews/views/single-top.php:156
msgid "Buy Now"
msgstr "Mua Ngay"

#: framework/extensions/product-reviews/metabox.php:114
msgid "Affiliate Link"
msgstr "Liên kết affiliate"

#: framework/extensions/product-reviews/metabox.php:120
msgid "Open Link In New Tab"
msgstr "Mở liên kết trong tab mới"

#: framework/extensions/product-reviews/metabox.php:127
msgid "Sponsored Attribute"
msgstr "Thuộc tính được tài trợ"

#: framework/extensions/product-reviews/metabox.php:150
msgid "Read More Button Label"
msgstr "Nhãn nút đọc thêm "

#: framework/extensions/product-reviews/metabox.php:152,
#: framework/extensions/product-reviews/views/single-top.php:162
msgid "Read More"
msgstr "Đọc tiếp"

#: framework/extensions/product-reviews/metabox.php:172
msgid "Short Description"
msgstr "Mô tả ngắn"

#: framework/extensions/product-reviews/metabox.php:187
msgid "Scores"
msgstr "Điểm"

#: framework/extensions/product-reviews/metabox.php:227
msgid "Product specs"
msgstr "Thông số kỹ thuật sản phẩm"

#: framework/extensions/product-reviews/metabox.php:252,
#: framework/extensions/product-reviews/views/single-top.php:262
msgid "Pros"
msgstr "Ưu điểm"

#: framework/extensions/product-reviews/metabox.php:272,
#: framework/extensions/product-reviews/views/single-top.php:285
msgid "Cons"
msgstr "Nhược điểm"

#: framework/extensions/trending/customizer.php:169
msgid "Trending Posts"
msgstr "Bài viết nổi bật"

#: framework/extensions/trending/config.php:6
msgid "Highlight your most popular posts or products based on the number of comments or reviews they have gotten in the specified period of time."
msgstr "Làm nổi bật các bài viết hoặc sản phẩm phổ biến nhất của bạn dựa trên số lượng nhận xét hoặc đánh giá mà họ nhận được trong khoảng thời gian cụ thể."

#: framework/extensions/trending/customizer.php:8,
#: framework/features/blocks/search/options.php:16,
#: framework/premium/extensions/shortcuts/views/bar.php:51,
#: framework/premium/features/premium-header/items/search-input/options.php:14
msgid "Products"
msgstr "Sản phẩm"

#: framework/extensions/trending/customizer.php:37
msgid "All categories"
msgstr "Tất cả danh mục"

#: framework/extensions/trending/customizer.php:42
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:210
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:34
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:66
#: static/js/editor/blocks/tax-query/Edit.js:156
msgid "Taxonomy"
msgstr "Phân loại"

#: framework/extensions/trending/customizer.php:43
msgid "All taxonomies"
msgstr "Tất cả phân loại"

#: framework/extensions/trending/customizer.php:179
msgid "Module Title"
msgstr "Tiêu đề Mô-đun"

#: framework/extensions/trending/customizer.php:182,
#: framework/extensions/trending/helpers.php:352
msgid "Trending now"
msgstr "Xu hướng hiện tại"

#: framework/extensions/trending/customizer.php:187
msgid "Module Title Tag"
msgstr "Tag tiêu đề module"

#: framework/extensions/trending/customizer.php:324,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:284
msgid "Source"
msgstr "Nguồn"

#: framework/extensions/trending/customizer.php:329
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:229
msgid "Taxonomies"
msgstr "Phân loại"

#: framework/extensions/trending/customizer.php:330
msgid "Custom Query"
msgstr "Truy vấn tùy chỉnh"

#: framework/extensions/trending/customizer.php:354
msgid "Posts ID"
msgstr "ID bài viết"

#: framework/extensions/trending/customizer.php:358
msgid "Separate posts ID by comma. How to find the %spost ID%s."
msgstr "Phân tách ID bài viết bằng dấu phẩy. Cách tìm %sID bài viết%s."

#: framework/extensions/trending/customizer.php:375
msgid "Trending From"
msgstr "Xu hướng từ"

#: framework/extensions/trending/customizer.php:383
msgid "All Time"
msgstr "Mọi lúc"

#: framework/extensions/trending/customizer.php:384
msgid "Last 24 Hours"
msgstr "24 giờ trước"

#: framework/extensions/trending/customizer.php:385
msgid "Last 7 Days"
msgstr "7 ngày qua"

#: framework/extensions/trending/customizer.php:386
msgid "Last Month"
msgstr "Tháng trước"

#: framework/features/header/account-modal.php:37,
#: framework/features/header/items/account/options.php:1053,
#: framework/features/header/items/account/views/logout.php:8
msgid "Login"
msgstr "Đăng nhập"

#: framework/features/header/account-modal.php:41
msgid "Sign Up"
msgstr "Đăng ký"

#: framework/features/header/account-modal.php:61
msgid "Back to login"
msgstr "Quay lại đăng nhập"

#: framework/features/header/header-options.php:11
msgid "Sticky Functionality"
msgstr "Chức năng ghim"

#: framework/features/header/header-options.php:37
msgid "Only Main Row"
msgstr "Chỉ hàng chính"

#: framework/features/header/header-options.php:42
msgid "Top & Main Row"
msgstr "Hàng trên cùng & hàng chính"

#: framework/features/header/header-options.php:47
msgid "All Rows"
msgstr "Tất cả các hàng"

#: framework/features/header/header-options.php:52
msgid "Main & Bottom Row"
msgstr "Hàng chính & hàng dưới cùng"

#: framework/features/header/header-options.php:57
msgid "Only Top Row"
msgstr "Chỉ hàng trên cùng"

#: framework/features/header/header-options.php:62
msgid "Only Bottom Row"
msgstr "Chỉ hàng dưới cùng"

#: framework/features/header/header-options.php:77
msgid "Slide Down"
msgstr "Trượt xuống"

#: framework/features/header/header-options.php:78
msgid "Fade"
msgstr "Mờ dần"

#: framework/features/header/header-options.php:79
msgid "Auto Hide/Show"
msgstr "Tự động ẩn/hiện"

#: framework/features/header/header-options.php:97,
#: framework/features/header/header-options.php:179
msgid "Enable on"
msgstr "Bật trên"

#: framework/features/header/header-options.php:125
msgid "Transparent Functionality"
msgstr "Tính năng trong suốt"

#: framework/extensions/trending/customizer.php:561,
#: framework/features/header/header-options.php:163,
#: framework/premium/extensions/shortcuts/customizer.php:1017,
#: framework/premium/features/content-blocks/options/header.php:31,
#: framework/premium/features/content-blocks/options/hook.php:36,
#: framework/premium/features/content-blocks/options/maintenance.php:5,
#: framework/premium/features/content-blocks/options/nothing_found.php:29,
#: framework/premium/features/content-blocks/options/nothing_found.php:34,
#: framework/premium/features/content-blocks/options/popup.php:49,
#: framework/premium/features/content-blocks/options/single.php:10,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:443,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:5,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:102,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:24
#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:50
#: framework/premium/static/js/footer/EditConditions.js:97
#: static/js/header/EditConditions.js:103
msgid "Display Conditions"
msgstr "Điều kiện hiển thị"

#: framework/premium/features/content-blocks/admin-ui.php:653
msgid "Hooks Locations"
msgstr "Vị trí Hooks"

#: framework/premium/extensions/shortcuts/customizer.php:765,
#: framework/premium/features/content-blocks/admin-ui.php:342,
#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:12,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:5
msgid "Type"
msgstr "Loại"

#: framework/premium/features/content-blocks/admin-ui.php:343
msgid "Location/Trigger"
msgstr "Vị trí/Kích hoạt"

#: framework/premium/features/content-blocks/admin-ui.php:344,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:80,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:143
msgid "Conditions"
msgstr "Điều kiện"

#: framework/premium/features/content-blocks/admin-ui.php:345
msgid "Output"
msgstr "Mã ngắn"

#: framework/premium/features/content-blocks/admin-ui.php:346
msgid "Enable/Disable"
msgstr "Bật/Tắt"

#: framework/features/blocks/about-me/options.php:204,
#: framework/features/blocks/contact-info/options.php:564,
#: framework/features/blocks/share-box/options.php:160,
#: framework/features/blocks/socials/options.php:112,
#: framework/premium/extensions/shortcuts/customizer.php:980,
#: framework/premium/features/content-blocks/admin-ui.php:371,
#: framework/features/header/items/account/options.php:22,
#: framework/features/header/items/account/options.php:348,
#: framework/features/header/items/account/options.php:746,
#: framework/premium/features/content-blocks/options/hook.php:9,
#: framework/premium/features/content-blocks/options/hook.php:77,
#: framework/premium/features/content-blocks/options/hook.php:167,
#: framework/premium/features/content-blocks/options/popup.php:75,
#: framework/premium/features/content-blocks/options/popup.php:222,
#: framework/premium/features/content-blocks/options/popup.php:603,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/select.php:23,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:97,
#: framework/premium/features/premium-header/items/contacts/options.php:451
#: framework/premium/static/js/blocks/ContentBlock.js:56
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:170
msgid "None"
msgstr "Không dùng"

#: framework/premium/features/content-blocks/admin-ui.php:372,
#: framework/premium/features/content-blocks/options/popup.php:76
msgid "On scroll"
msgstr "Khi cuộn"

#: framework/premium/features/content-blocks/admin-ui.php:373,
#: framework/premium/features/content-blocks/options/popup.php:77
msgid "On scroll to element"
msgstr "Khi cuộn đến phần tử"

#: framework/premium/features/content-blocks/admin-ui.php:375,
#: framework/premium/features/content-blocks/options/popup.php:79
msgid "On page load"
msgstr "Khi tải trang"

#: framework/premium/features/content-blocks/admin-ui.php:376,
#: framework/premium/features/content-blocks/options/popup.php:80
msgid "After inactivity"
msgstr "Sau khi không hoạt động"

#: framework/premium/features/content-blocks/admin-ui.php:377,
#: framework/premium/features/content-blocks/options/popup.php:81
msgid "After x time"
msgstr "Sau x thời gian"

#: framework/premium/features/content-blocks/admin-ui.php:379,
#: framework/premium/features/content-blocks/options/popup.php:83
msgid "On page exit intent"
msgstr "khi có ý thoát trang"

#: framework/premium/features/content-blocks/admin-ui.php:383
msgid "Down"
msgstr "Xuống"

#: framework/premium/features/content-blocks/admin-ui.php:384
msgid "Up"
msgstr "Lên"

#: framework/premium/features/content-blocks.php:193,
#: framework/premium/features/content-blocks.php:199
msgid "Content Blocks"
msgstr "Khối nội dung"

#: framework/premium/features/content-blocks.php:194,
#: framework/premium/extensions/mega-menu/options.php:346,
#: framework/premium/features/content-blocks/content-block-layer.php:163,
#: framework/premium/features/content-blocks/content-block-layer.php:214,
#: framework/features/header/items/account/options.php:247,
#: framework/premium/features/premium-header/items/content-block/config.php:4
#: framework/premium/static/js/blocks/ContentBlock.js:78
msgid "Content Block"
msgstr "Khối nội dung"

#: framework/premium/features/content-blocks.php:195,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:138,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:482,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:273
msgid "Add New"
msgstr "Thêm mới"

#: framework/premium/features/content-blocks.php:196
msgid "Add New Content Block"
msgstr "Thêm mới khối nội dung"

#: framework/premium/features/content-blocks.php:197
#: framework/premium/static/js/blocks/ContentBlock.js:89
msgid "Edit Content Block"
msgstr "Sửa khối nội dung"

#: framework/premium/features/content-blocks.php:198
#: framework/premium/static/js/hooks/CreateHook.js:33
msgid "New Content Block"
msgstr "Khối nội dung mới"

#: framework/premium/features/content-blocks.php:200
msgid "View Content Block"
msgstr "Xem khối nội dung"

#: framework/premium/features/content-blocks.php:201
msgid "Search Content Blocks"
msgstr "Tìm kiếm khối nội dung"

#: framework/premium/features/content-blocks.php:202,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:145,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:489,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:280
msgid "Nothing found"
msgstr "Không tìm thấy gì"

#: framework/premium/features/content-blocks.php:203,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:146,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:490,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:281
msgid "Nothing found in Trash"
msgstr "Không tìm thấy gì trong Thùng rác"

#: framework/premium/features/premium-footer.php:14,
#: framework/premium/features/premium-footer.php:28
msgid "Footer Menu 1"
msgstr "Menu chân trang 1"

#: framework/premium/features/premium-footer.php:29,
#: framework/premium/features/premium-footer/items/menu-secondary/config.php:4
msgid "Footer Menu 2"
msgstr "Menu chân trang 2"

#: framework/premium/features/premium-header.php:57
msgid "Header Menu 3"
msgstr "Menu đầu trang 3"

#: framework/premium/features/premium-header.php:202
msgid "Header Widget Area "
msgstr "Khu vực tiện ích đầu trang "

#: framework/extensions/trending/customizer.php:251,
#: framework/premium/features/premium-header.php:258,
#: framework/premium/features/premium-header.php:339,
#: framework/premium/features/premium-header.php:359,
#: framework/premium/features/premium-header.php:376,
#: framework/premium/features/socials.php:31,
#: framework/features/blocks/contact-info/options.php:121,
#: framework/features/blocks/contact-info/options.php:186,
#: framework/features/blocks/contact-info/options.php:249,
#: framework/features/blocks/contact-info/options.php:312,
#: framework/features/blocks/contact-info/options.php:375,
#: framework/features/blocks/contact-info/options.php:438,
#: framework/features/blocks/contact-info/options.php:501,
#: framework/features/blocks/search/options.php:86,
#: framework/premium/extensions/mega-menu/options.php:463,
#: framework/features/header/items/account/options.php:347,
#: framework/features/header/items/account/options.php:404,
#: framework/features/header/items/account/options.php:745,
#: framework/features/header/items/account/options.php:785,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:693,
#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:20,
#: framework/premium/features/premium-header/items/contacts/options.php:114,
#: framework/premium/features/premium-header/items/contacts/options.php:159,
#: framework/premium/features/premium-header/items/contacts/options.php:204,
#: framework/premium/features/premium-header/items/contacts/options.php:249,
#: framework/premium/features/premium-header/items/contacts/options.php:295,
#: framework/premium/features/premium-header/items/contacts/options.php:340,
#: framework/premium/features/premium-header/items/contacts/options.php:385,
#: framework/premium/features/premium-header/items/search-input/options.php:79,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:30,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:28,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:28
msgid "Icon"
msgstr "Biểu tượng"

#: framework/premium/features/premium-header.php:277,
#: framework/features/blocks/about-me/options.php:168,
#: framework/features/blocks/contact-info/options.php:538,
#: framework/features/blocks/share-box/options.php:122,
#: framework/features/blocks/socials/options.php:74,
#: framework/premium/features/premium-header/items/contacts/options.php:426
msgid "Icons Size"
msgstr "Kích thước biểu tượng"

#: framework/premium/features/premium-header.php:289,
#: framework/premium/extensions/mega-menu/options.php:509
msgid "Icon Position"
msgstr "Vị trí biểu tượng"

#: framework/premium/features/premium-header.php:296,
#: framework/premium/extensions/mega-menu/options.php:516,
#: framework/premium/extensions/shortcuts/customizer.php:886,
#: framework/features/header/items/account/options.php:561,
#: framework/features/header/items/account/options.php:1027,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:95,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:129,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:130
msgid "Left"
msgstr "Bên trái"

#: framework/premium/features/premium-header.php:297,
#: framework/premium/extensions/mega-menu/options.php:517,
#: framework/premium/extensions/shortcuts/customizer.php:887,
#: framework/features/header/items/account/options.php:562,
#: framework/features/header/items/account/options.php:1031,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:96,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:130,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:131
msgid "Right"
msgstr "Bên phải"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:14
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/index.js:56
msgid "Newsletter"
msgstr "Bản tin"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:13,
#: framework/features/blocks/contact-info/options.php:39,
#: framework/premium/extensions/mega-menu/options.php:544,
#: framework/premium/extensions/shortcuts/customizer.php:1187,
#: framework/premium/extensions/shortcuts/customizer.php:1229,
#: framework/premium/extensions/shortcuts/customizer.php:1271,
#: framework/features/header/items/account/options.php:577,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:32,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:72,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:98,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:547,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:582,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:616,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:486,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:517,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:547
#: static/js/editor/blocks/breadcrumbs/Edit.js:54
#: static/js/editor/blocks/contact-info/Edit.js:107
msgid "Text"
msgstr "Văn bản"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:19,
#: framework/features/blocks/about-me/options.php:67
msgid "You can add here some arbitrary HTML code."
msgstr "Bạn có thể thêm vào đây một số code HTML tùy ý."

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:74
msgid "Container Style"
msgstr "Loại vùng chứa"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:82,
#: framework/premium/features/content-blocks/options/404.php:85,
#: framework/premium/features/content-blocks/options/archive.php:162,
#: framework/premium/features/content-blocks/options/header.php:103,
#: framework/premium/features/content-blocks/options/hook.php:212,
#: framework/premium/features/content-blocks/options/maintenance.php:82,
#: framework/premium/features/content-blocks/options/nothing_found.php:103,
#: framework/premium/features/content-blocks/options/single.php:94,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:22
msgid "Boxed"
msgstr "Đóng hộp"

#: framework/extensions/product-reviews/views/single-top.php:238
msgid "Specs"
msgstr "Thông số kỹ thuật"

#: framework/extensions/newsletter-subscribe/helpers.php:147
msgid "Email address"
msgstr "Địa chỉ email"

#: framework/features/header/modal/login.php:28,
#: framework/features/header/modal/register.php:53
msgid "Password"
msgstr "Mật khẩu"

#: framework/features/header/modal/login.php:53
msgid "Remember Me"
msgstr "Ghi nhớ"

#: framework/features/header/modal/login.php:57
msgid "Forgot Password?"
msgstr "Quên mật khẩu?"

#: framework/features/header/modal/login.php:79
msgid "Log In"
msgstr "Đăng nhập"

#: framework/features/header/modal/login.php:23,
#: framework/features/header/modal/lostpassword.php:5
msgid "Username or Email Address"
msgstr "Tên người dùng hoặc địa chỉ email"

#: framework/features/header/modal/lostpassword.php:13
msgid "Get New Password"
msgstr "Lấy mật khẩu mới"

#: framework/features/header/modal/register.php:41
msgid "Username"
msgstr "Tên người dùng"

#: framework/features/header/modal/register.php:106
msgid "Registration confirmation will be emailed to you."
msgstr "Email xác nhận sẽ được gửi tới hộp thư của bạn."

#: framework/features/header/modal/register.php:112
msgid "Register"
msgstr "Đăng ký"

#: framework/helpers/exts-configs.php:60
msgid "Connect your Adobe Fonts project and use the selected fonts throughout Blocksy and your favorite page builder."
msgstr "Kết nối dự án Adobe Fonts của bạn và sử dụng các phông chữ đã chọn trong Blocksy và trình tạo trang yêu thích của bạn."

#: framework/helpers/exts-configs.php:71
msgid "Inject custom code snippets throughout your website. The extension works globally or on a per post/page basis."
msgstr "Chèn code snippet trên toàn bộ trang web của bạn. Tiện ích mở rộng hoạt động trên toàn cầu hoặc trên cơ sở mỗi bài viết /trang."

#: framework/premium/extensions/code-snippets/extension.php:42,
#: framework/premium/extensions/code-snippets/extension.php:97,
#: framework/premium/extensions/code-snippets/extension.php:142
msgid "After body open scripts"
msgstr "Mở mã script sau thân trang"

#: framework/helpers/exts-configs.php:97
msgid "Upload an unlimited number of custom fonts or variable fonts and use them throughout Blocksy and your favorite page builder."
msgstr "Tải lên số lượng font tùy chỉnh hoặc font thay đổi không giới hạn và sử dụng chúng trong Blocksy và trình tạo trang yêu thích của bạn."

#: framework/helpers/exts-configs.php:109
msgid "Serve your chosen Google Fonts from your own web server. This will increase the loading speed and makes sure your website complies with the privacy regulations."
msgstr "Chọn Google Font lưu trên máy chủ web của bạn. Điều này sẽ giúp tăng tốc độ tải và đảm bảo trang web của bạn tuân thủ các quy định về quyền riêng tư."

#: framework/helpers/exts-configs.php:124
msgid "Create beautiful personalised menus that set your website apart from the others. Add icons and badges to your entries and even add Content Blocks inside your dropdowns."
msgstr "Tạo menu cá nhân hóa đẹp mắt giúp trang web của bạn khác biệt với các menu khác. Thêm biểu tượng và huy hiệu vào các mục của bạn và thậm chí thêm khối nội dung bên trong danh sách thả xuống của bạn."

#: framework/premium/extensions/mega-menu/extension.php:160
msgid "Menu Item Settings"
msgstr "Cài đặt mục menu"