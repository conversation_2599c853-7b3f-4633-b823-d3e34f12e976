(function(t){function e(e){for(var o,s,r=e[0],c=e[1],l=e[2],u=0,d=[];u<r.length;u++)s=r[u],Object.prototype.hasOwnProperty.call(a,s)&&a[s]&&d.push(a[s][0]),a[s]=0;for(o in c)Object.prototype.hasOwnProperty.call(c,o)&&(t[o]=c[o]);p&&p(e);while(d.length)d.shift()();return i.push.apply(i,l||[]),n()}function n(){for(var t,e=0;e<i.length;e++){for(var n=i[e],o=!0,s=1;s<n.length;s++){var r=n[s];0!==a[r]&&(o=!1)}o&&(i.splice(e--,1),t=c(c.s=n[0]))}return t}var o={},s={app:0},a={app:0},i=[];function r(t){return c.p+"js/"+({}[t]||t)+".js"}function c(e){if(o[e])return o[e].exports;var n=o[e]={i:e,l:!1,exports:{}};return t[e].call(n.exports,n,n.exports,c),n.l=!0,n.exports}c.e=function(t){var e=[],n={"chunk-2723f679":1,"chunk-2b7b6b2b":1,"chunk-389f4675":1,"chunk-61540af4":1,"chunk-6bd81dbc":1,"chunk-766366b8":1,"chunk-7bf1d985":1,"chunk-e11526e4":1};s[t]?e.push(s[t]):0!==s[t]&&n[t]&&e.push(s[t]=new Promise((function(e,n){for(var o="css/"+({}[t]||t)+".css",a=c.p+o,i=document.getElementsByTagName("link"),r=0;r<i.length;r++){var l=i[r],u=l.getAttribute("data-href")||l.getAttribute("href");if("stylesheet"===l.rel&&(u===o||u===a))return e()}var d=document.getElementsByTagName("style");for(r=0;r<d.length;r++){l=d[r],u=l.getAttribute("data-href");if(u===o||u===a)return e()}var p=document.createElement("link");p.rel="stylesheet",p.type="text/css",p.onload=e,p.onerror=function(e){var o=e&&e.target&&e.target.src||a,i=new Error("Loading CSS chunk "+t+" failed.\n("+o+")");i.code="CSS_CHUNK_LOAD_FAILED",i.request=o,delete s[t],p.parentNode.removeChild(p),n(i)},p.href=a;var h=document.getElementsByTagName("head")[0];h.appendChild(p)})).then((function(){s[t]=0})));var o=a[t];if(0!==o)if(o)e.push(o[2]);else{var i=new Promise((function(e,n){o=a[t]=[e,n]}));e.push(o[2]=i);var l,u=document.createElement("script");u.charset="utf-8",u.timeout=120,c.nc&&u.setAttribute("nonce",c.nc),u.src=r(t);var d=new Error;l=function(e){u.onerror=u.onload=null,clearTimeout(p);var n=a[t];if(0!==n){if(n){var o=e&&("load"===e.type?"missing":e.type),s=e&&e.target&&e.target.src;d.message="Loading chunk "+t+" failed.\n("+o+": "+s+")",d.name="ChunkLoadError",d.type=o,d.request=s,n[1](d)}a[t]=void 0}};var p=setTimeout((function(){l({type:"timeout",target:u})}),12e4);u.onerror=u.onload=l,document.head.appendChild(u)}return Promise.all(e)},c.m=t,c.c=o,c.d=function(t,e,n){c.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},c.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},c.t=function(t,e){if(1&e&&(t=c(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(c.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)c.d(n,o,function(e){return t[e]}.bind(null,o));return n},c.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return c.d(e,"a",e),e},c.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},c.p="/wp-content/plugins/cookie-law-info/admin/dist/",c.oe=function(t){throw console.error(t),t};var l=window["webpackJsonp"]=window["webpackJsonp"]||[],u=l.push.bind(l);l.push=e,l=l.slice();for(var d=0;d<l.length;d++)e(l[d]);var p=u;i.push([0,"chunk-vendors"]),n()})({0:function(t,e,n){t.exports=n("56d7")},"00b4":function(t,e,n){"use strict";n("b920")},"01e9":function(t,e,n){},"045e":function(t,e,n){"use strict";n("be7c")},"07a4":function(t,e,n){"use strict";var o=n("2b0e"),s=n("2f62"),a=n("f9c4"),i=n("87ea");const r={namespaced:!0,state:{options:window.ckyConfig.settings,info:!1,status:!0,errors:{},expand:window.ckyNoticeExpand.status,gcm:window.ckyGcmConfig.settings},getters:{get:t=>t.options,getInfo:t=>t.info,getExpandStatus:t=>t.expand,getGcmInfo:t=>t.gcm},mutations:{set:(t,e)=>{Object.prototype.hasOwnProperty.call(t.options,e.key)&&(t.options[e.key]=e.value)},setInfo:(t,e)=>{t.info=e},setGcmInfo:(t,e)=>{t.gcm=e},update:(t,e)=>{window.ckyConfig.settings=e,t.options=e},updateGcm:(t,e)=>{window.ckyGcmConfig.settings=e,t.gcm=e},updateStatus:(t,e=!0)=>{t.status=e},setErrors:(t,e)=>{t.errors=e}},actions:{reInit:async({commit:t})=>{await a["a"].get({path:"settings"}).then(e=>{e.account&&t("update",e)})},loadInfo:async({commit:t,state:e,dispatch:n},o=!1)=>{let s=!1;return s=!0,await a["a"].get({path:"settings/info",params:{force:o}}).then(o=>{if(o){t("setInfo",o),t("updateStatus",!0);const s=e.info&&e.info.languages?e.info.languages:[],a=s&&s.default?s.default:"en";Object(i["l"])(a),n("checkURLMismatch")}}).catch(async e=>{e&&e.data&&e.data.status&&t("updateStatus",!1)}),s},set:({commit:t},e={})=>{e["key"]&&e["value"]&&t("set",e)},setInfo:({commit:t},e={})=>{e["key"]&&e["value"]&&t("setInfo",e)},save:async({commit:t,state:e,dispatch:n},{clear:o=!0}={})=>{const s=e.options;let i=!1;return await a["a"].post({path:"settings",data:s,params:{clear:o}}).then(e=>{e&&(t("update",e),n("loadInfo"),i=!0)}),i},checkURLMismatch({state:t,commit:e}){if(!1!==t.options.account.connected&&!Object(i["a"])()){let t={urlMismatch:!0};e("setErrors",t)}},setGcmInfo:async({commit:t},e={})=>{t("setGcmInfo",e)}}};var c=r;const l={namespaced:!0,state:{items:!1,current:!1,preview:!1,presets:[],configs:[],template:[],errors:{}},getters:{getBanners:t=>t.items,getCurrentBanner:t=>t.current,getPreview:t=>t.preview,getPresets:t=>t.presets,getConfigs:t=>t.configs,getTemplate:t=>t.template,getErrors:t=>t.errors},mutations:{setBanners:(t,e)=>{t.items=e},setCurrentBanner:(t,e)=>{t.current=e},setPreview:(t,e)=>{t.preview=e},setPresets:(t,e)=>{t.presets=e},setConfigs:(t,e)=>{t.configs=e},setTemplate:(t,e)=>{t.template=e},updateBanners:(t,e)=>{t.items=e},setErrors:(t,e)=>{t.errors=e}},actions:{reInit:async({commit:t})=>{try{const e=await a["a"].get({path:"banners"});if(e){const n=[];e.forEach((function(t){let e={};e.id=t.id||"",e.name=t.name||"",e.slug=t.slug||"",e.default=t.default||!1,e.status=t.status||!1,e.properties=t.properties||{},e.contents=t.contents||{},n.push(e)})),t("updateBanners",n)}}catch(e){console.log(e)}},setBanners:async({commit:t},e={})=>{t("setBanners",e)},setCurrentBanner:async({commit:t},e={})=>{t("setCurrentBanner",e)},setPreview:async({commit:t},e={})=>{t("setPreview",e)},setPresets:async({commit:t},e={})=>{t("setPresets",e)},setConfigs:async({commit:t},e={})=>{t("setConfigs",e)},setTemplate:async({commit:t},e={})=>{t("setTemplate",e)},saveBanner:async({dispatch:t},e={})=>{let n=!1;if(!Object.prototype.hasOwnProperty.call(e,"banner"))return;const o=e.banner,s=e.params&&e.params||{};try{const e=await a["a"].put({path:"/banners/"+o.id,data:o,params:s});e.id&&(n=!0,t("reInit"))}catch(i){console.log(i)}return n},setErrors:async({commit:t},e={})=>{t("setErrors",e)}}};var u=l;const d={namespaced:!0,state:{selected:[],available:window.ckyLanguages,current:"",default:""},getters:{getSelected:t=>t.selected,getCurrent:t=>t.current,getAvailable:t=>t.available,getDefault:t=>t.available.find((function(e){return e.code==t.default}))},mutations:{setSelected:(t,e)=>{t.selected=e},setCurrent:(t,e)=>{t.current=e},setAvailable:(t,e)=>{t.available=e},setDefault:(t,e)=>{t.default=e}},actions:{setSelected:async({commit:t},e={})=>{t("setSelected",e)},setCurrent:async({commit:t},e={})=>{t("setCurrent",e)},setDefault:async({commit:t},e={})=>{t("setDefault",e)},saveSelected:async({state:t},{remove:e=!1,clear:n=!0}={})=>{const o=t.selected;if(!e)try{await a["a"].post({path:"languages/translations",data:o})}catch(r){console.log(r)}let s=Object(i["e"])("languages");return s.selected=o,Object(i["m"])("languages",s),await Object(i["k"])(n)},saveDefault:async({state:t},{clear:e=!0}={})=>{const n=t.default;let o=Object(i["e"])("languages");return o.default=n,Object(i["m"])("languages",o),await Object(i["k"])(e)},loadLanguages:async({commit:t})=>{const e=Object(i["e"])("languages");t("setSelected",e.selected),t("setDefault",e.default),t("setCurrent",e.default)}}};var p=d,h=n("a9e4");const g={namespaced:!0,state:{items:!1},getters:{getItems:t=>t.items},mutations:{setItems:(t,e)=>{t.items=e}},actions:{reInit:async({commit:t})=>{try{const e=await h["a"].getCookieCategories(),n=[];e instanceof Array&&(e.forEach((function(t){n.push(t)})),t("setItems",n))}catch(e){console.error(e)}},bulkUpdate:async({state:t,dispatch:e})=>{let n=!1,o=await h["a"].bulkUpdateCookieCategory(t.items);return o.length>0&&(n=!0,e("reInit")),n}}};var y=g;o["a"].use(s["a"]);e["a"]=new s["a"].Store({namespaced:!0,modules:{banners:u,settings:c,languages:p,cookies:y}})},"08f7":function(t,e,n){"use strict";n("ebe0")},"090a":function(t,e,n){"use strict";n("2dd4")},1087:function(t,e,n){"use strict";n("8a3e")},1381:function(t,e,n){t.exports=n.p+"img/custom-icon.svg"},1759:function(t,e,n){"use strict";n("da4c")},"17aa":function(t,e,n){"use strict";var o=function(){var t=this;t._self._c;return t._m(0)},s=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"cky-card-loader-container"},[e("div",{staticClass:"cky-card-loader"},[e("div",{staticClass:"cky-card-loader--line"}),e("div",{staticClass:"cky-card-loader--line"}),e("div",{staticClass:"cky-card-loader--line"}),e("div",{staticClass:"cky-card-loader--line"}),e("div",{staticClass:"cky-card-loader--line cky-card-loader--rect"})])])}],a={name:"CkyCardLoader"},i=a,r=(n("482e"),n("2877")),c=Object(r["a"])(i,o,s,!1,null,"4bc5d6ea",null);e["a"]=c.exports},"17f4":function(t,e,n){},"1f3d":function(t,e,n){"use strict";var o=function(){var t=this,e=t._self._c;return e("span",t._g({staticClass:"cky-icon-base",style:t.getStyles},t.$listeners),[e(t.icon,{tag:"component",attrs:{width:t.width,height:t.height,color:t.color}})],1)},s=[],a=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 1250 250",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M92.9114 47.9141C134.161 47.9141 164.995 70.8307 174.995 108.331H128.745C121.661 93.3307 108.328 86.2474 92.4948 86.2474C66.6615 86.2474 48.7448 105.414 48.7448 136.247C48.7448 167.081 66.6615 186.247 92.4948 186.247C108.328 186.247 121.661 178.747 128.745 164.164H174.995C164.995 201.664 134.161 224.581 92.9114 224.581C41.6614 224.164 5.82812 187.914 5.82812 135.831C5.82812 83.7474 41.6614 47.9141 92.9114 47.9141Z",fill:t.color}}),e("path",{attrs:{d:"M259.161 225.003C218.745 225.003 188.328 197.919 188.328 154.169C188.328 110.419 219.578 83.3359 259.995 83.3359C300.411 83.3359 331.661 110.419 331.661 154.169C331.661 197.919 299.578 225.003 259.161 225.003ZM259.161 188.336C274.161 188.336 288.328 177.086 288.328 154.169C288.328 130.836 274.578 120.003 259.578 120.003C244.161 120.003 230.828 130.836 230.828 154.169C231.245 177.086 243.745 188.336 259.161 188.336Z",fill:t.color}}),e("path",{attrs:{d:"M415.411 225.003C374.995 225.003 344.578 197.919 344.578 154.169C344.578 110.419 375.828 83.3359 416.245 83.3359C456.661 83.3359 487.911 110.419 487.911 154.169C487.911 197.919 455.828 225.003 415.411 225.003ZM415.411 188.336C430.411 188.336 444.578 177.086 444.578 154.169C444.578 130.836 430.828 120.003 415.828 120.003C400.411 120.003 387.078 130.836 387.078 154.169C387.078 177.086 399.995 188.336 415.411 188.336Z",fill:t.color}}),e("path",{attrs:{d:"M502.914 47.9141H544.997V141.247L586.664 85.4141H638.747L581.664 154.164L639.581 222.914H587.497L545.414 164.997V222.914H503.331V47.9141H502.914Z",fill:t.color}}),e("path",{attrs:{d:"M648.746 48.3307C648.746 35.4141 658.746 25.4141 673.746 25.4141C688.746 25.4141 698.746 35.4141 698.746 48.3307C698.746 60.8307 688.746 70.8307 673.746 70.8307C658.746 70.8307 648.746 60.8307 648.746 48.3307ZM652.913 84.9974H694.996V222.497H652.913V84.9974Z",fill:t.color}}),e("path",{attrs:{d:"M780.001 225.003C739.585 225.003 710.418 197.919 710.418 154.169C710.418 110.419 739.168 83.3359 780.001 83.3359C820.001 83.3359 848.751 110.003 848.751 152.086C848.751 155.836 848.335 160.419 847.918 164.586H752.501C754.168 182.503 765.001 190.836 778.751 190.836C790.418 190.836 797.085 185.003 800.835 177.503H845.835C838.751 204.169 814.585 225.003 780.001 225.003ZM752.918 140.836H805.835C805.835 125.836 794.168 117.086 780.001 117.086C765.835 117.086 755.418 125.419 752.918 140.836Z",fill:t.color}}),e("path",{attrs:{d:"M1043.33 225.003C1002.91 225.003 973.746 197.919 973.746 154.169C973.746 110.419 1002.5 83.3359 1043.33 83.3359C1083.33 83.3359 1112.08 110.003 1112.08 152.086C1112.08 155.836 1111.66 160.419 1111.25 164.586H1015.83C1017.5 182.503 1028.33 190.836 1042.08 190.836C1053.75 190.836 1060.41 185.003 1064.16 177.503H1109.16C1102.08 204.169 1077.5 225.003 1043.33 225.003ZM1015.83 140.836H1068.75C1068.75 125.836 1057.08 117.086 1042.91 117.086C1029.16 117.086 1018.33 125.419 1015.83 140.836Z",fill:t.color}}),e("path",{attrs:{d:"M1188.33 225.003C1151.67 225.003 1127.08 204.586 1125 177.919H1166.67C1167.5 187.503 1176.25 193.753 1187.92 193.753C1198.75 193.753 1204.58 188.753 1204.58 182.503C1204.58 160.419 1129.58 176.253 1129.58 125.836C1129.58 102.503 1149.58 83.3359 1185.42 83.3359C1220.83 83.3359 1240.83 102.919 1243.33 130.003H1204.17C1202.92 120.836 1195.83 114.586 1183.75 114.586C1173.75 114.586 1168.33 118.336 1168.33 125.003C1168.33 147.086 1242.92 131.253 1243.75 182.919C1244.17 206.669 1222.92 225.003 1188.33 225.003Z",fill:t.color}}),e("path",{attrs:{d:"M892.496 87.5H848.746L888.746 161.667H932.496L892.496 87.5Z",fill:t.color}}),e("path",{attrs:{d:"M887.906 159.586L889.156 161.669H932.906L904.156 109.586L887.906 159.586Z",fill:t.color}}),e("path",{attrs:{d:"M967.91 25L889.16 161.667H932.91L1011.66 25H967.91Z",fill:t.color}}),e("path",{attrs:{d:"M889.16 182.086H931.66V225.003H889.16V182.086Z",fill:t.color}})])},i=[],r={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},c=r,l=n("2877"),u=Object(l["a"])(c,a,i,!1,null,null,null),d=u.exports,p=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 142 150",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("g",{attrs:{"clip-path":"url(#clip0)"}},[e("path",{attrs:{d:"M20.9961 0H93.5913C99.3652 0 104.614 2.35596 108.423 6.16455C112.231 9.97315 114.587 15.2222 114.587 20.9961V67.8223C114.587 73.5962 112.231 78.8452 108.423 82.6538C104.614 86.4624 99.3652 88.8184 93.5913 88.8184H57.2876L25.4028 116.223C23.9258 117.493 21.6919 117.322 20.4224 115.833C19.7998 115.1 19.519 114.197 19.5801 113.306L21.2769 88.8062H20.9961C15.2222 88.8062 9.97315 86.4502 6.16455 82.6416C2.35596 78.8452 0 73.5962 0 67.8223V20.9961C0 15.2222 2.35596 9.97315 6.16455 6.16455C9.96094 2.35596 15.21 0 20.9961 0ZM124.89 34.1553C129.004 34.9487 132.69 36.9751 135.547 39.8315C139.27 43.5547 141.589 48.6939 141.589 54.3579V101.184C141.589 106.848 139.282 111.987 135.547 115.71C131.824 119.434 126.685 121.753 121.021 121.753H120.276L122.009 146.704C122.058 147.485 121.826 148.291 121.277 148.926C120.166 150.232 118.201 150.378 116.907 149.268L84.8999 120.947H42.7368L63.501 99.8291H107.495C117.09 99.8291 124.951 91.98 124.951 82.3731V35.5347C124.939 35.0708 124.927 34.6069 124.89 34.1553ZM31.3477 53.3203C29.3945 53.3203 27.8076 51.7334 27.8076 49.7803C27.8076 47.8272 29.3945 46.2402 31.3477 46.2402H68.396C70.3491 46.2402 71.936 47.8272 71.936 49.7803C71.936 51.7334 70.3491 53.3203 68.396 53.3203H31.3477ZM31.3477 35.791C29.3945 35.791 27.8076 34.2041 27.8076 32.251C27.8076 30.2979 29.3945 28.7109 31.3477 28.7109H83.8623C85.8154 28.7109 87.4024 30.2979 87.4024 32.251C87.4024 34.2041 85.8154 35.791 83.8623 35.791H31.3477ZM93.5791 7.08008H20.9961C17.1753 7.08008 13.6963 8.64258 11.1694 11.1694C8.64258 13.6963 7.08008 17.1631 7.08008 20.9961V67.8223C7.08008 71.6431 8.64258 75.1221 11.1694 77.6489C13.6963 80.1758 17.1753 81.7383 20.9961 81.7383H25.0732V81.7505L25.3052 81.7627C27.2461 81.897 28.7231 83.5816 28.5889 85.5225L27.2095 105.371L53.5034 82.7637C54.1382 82.1289 55.0171 81.7383 55.9937 81.7383H93.5791C97.3999 81.7383 100.879 80.1758 103.406 77.6489C105.933 75.1221 107.495 71.6431 107.495 67.8223V20.9961C107.495 17.1753 105.933 13.6963 103.406 11.1694C100.891 8.64258 97.4121 7.08008 93.5791 7.08008Z",fill:"white"}})]),e("defs",[e("clipPath",{attrs:{id:"clip0"}},[e("rect",{attrs:{width:"141.577",height:"150",fill:"white"}})])])])},h=[],g={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},y=g,f=Object(l["a"])(y,p,h,!1,null,null,null),m=f.exports,k=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 150 150",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M61.0383 94.1116C50.3883 90.4066 42.6933 80.2666 42.6933 68.3266C42.6933 53.2816 54.9033 41.0716 69.9783 41.0716C81.5733 41.0716 91.5183 48.3316 95.4483 58.5916C99.8433 57.3316 104.373 56.7016 109.038 56.7016C115.728 56.7016 122.193 58.0216 128.313 60.6016C131.898 62.1166 135.258 64.0516 138.363 66.3166C138.243 61.0066 137.868 58.8466 137.868 58.8466C137.328 55.8616 134.448 52.8466 131.493 52.1566L129.258 51.6466C124.038 50.0716 119.373 46.5916 116.418 41.5066C113.463 36.3916 112.773 30.5716 114.063 25.2316L114.753 23.1316C115.638 20.2366 114.498 16.2466 112.143 14.2516C112.143 14.2516 110.043 12.4666 104.163 9.07655C98.2533 5.68655 95.7183 4.77155 95.7183 4.77155C92.8233 3.75155 88.8033 4.72655 86.7033 6.96155L85.1583 8.62655C81.1983 12.3916 75.8583 14.6566 69.9333 14.6566C64.0233 14.6566 58.6533 12.3616 54.6633 8.56655L53.1633 6.96155C51.0933 4.72655 47.0433 3.75155 44.1783 4.78655C44.1783 4.78655 41.5983 5.73155 35.7033 9.09155C29.7933 12.5416 27.7533 14.2966 27.7533 14.2966C25.4283 16.2466 24.2733 20.2066 25.1433 23.1466L25.7733 25.2766C27.0333 30.6166 26.3733 36.4216 23.4183 41.5366C20.4633 46.6516 15.7833 50.1466 10.5183 51.7066L8.35828 52.2016C5.40328 52.8616 2.49328 55.8766 1.98328 58.8916C1.98328 58.8916 1.48828 61.5616 1.48828 68.4016C1.48828 75.2116 1.98328 77.9116 1.98328 77.9116C2.52328 80.9266 5.40328 83.9116 8.35828 84.6016L10.4583 85.0965C15.7083 86.6415 20.4483 90.1516 23.3733 95.2966C26.3283 100.412 27.0183 106.232 25.7283 111.572L25.1283 113.642C24.2433 116.537 25.3833 120.527 27.7383 122.522C27.7383 122.522 29.8383 124.307 35.7183 127.697C41.6283 131.117 44.1633 132.002 44.1633 132.002C47.0283 133.037 51.0483 132.062 53.1483 129.827L54.6183 128.252C56.8533 126.122 59.5533 124.457 62.5383 123.392C60.4983 117.902 59.4633 112.142 59.4633 106.247C59.5383 102.077 60.0633 98.0266 61.0383 94.1116Z",fill:t.color}}),e("path",{attrs:{d:"M109.052 66.7188C87.2566 66.7188 69.6016 84.3738 69.6016 106.169C69.6016 127.964 87.2566 145.619 109.052 145.619C130.847 145.619 148.502 127.964 148.502 106.169C148.502 84.3738 130.847 66.7188 109.052 66.7188ZM109.787 136.814C106.367 136.814 103.607 133.964 103.607 130.469C103.607 126.959 106.367 124.124 109.787 124.124C113.207 124.124 115.967 126.974 115.967 130.469C115.967 133.979 113.207 136.814 109.787 136.814ZM127.052 96.9288C126.197 98.5638 125.192 99.9738 124.007 101.174C122.837 102.374 120.737 104.369 117.692 107.174C116.852 107.984 116.177 108.674 115.682 109.274C115.172 109.874 114.797 110.429 114.557 110.909C114.302 111.404 114.122 111.914 113.987 112.409C113.837 112.904 113.642 113.789 113.387 115.049C112.922 117.719 111.467 119.039 108.992 119.039C107.702 119.039 106.637 118.604 105.752 117.749C104.867 116.894 104.432 115.589 104.432 113.879C104.432 111.719 104.747 109.859 105.377 108.314C106.007 106.739 106.877 105.359 107.927 104.174C108.992 103.004 110.432 101.564 112.232 99.9288C113.807 98.4888 114.962 97.4237 115.652 96.6887C116.372 95.9688 116.942 95.1438 117.437 94.2438C117.932 93.3588 118.157 92.3838 118.157 91.3188C118.157 89.2788 117.407 87.5237 115.952 86.1137C114.482 84.7037 112.592 83.9838 110.267 83.9838C107.537 83.9838 105.557 84.7037 104.267 86.1137C102.977 87.5237 101.882 89.6238 101.027 92.3988C100.187 95.2938 98.5816 96.7338 96.2566 96.7338C94.8766 96.7338 93.7066 96.2238 92.7466 95.2188C91.8016 94.2138 91.3066 93.1188 91.3066 91.9488C91.3066 89.5038 92.0566 87.0438 93.5716 84.5688C95.0716 82.0638 97.2766 80.0088 100.172 78.3588C103.067 76.7238 106.427 75.8838 110.282 75.8838C113.867 75.8838 117.032 76.5738 119.762 77.9538C122.492 79.3338 124.622 81.1938 126.107 83.5188C127.607 85.8738 128.342 88.4238 128.342 91.1838C128.312 93.3738 127.907 95.2938 127.052 96.9288Z",fill:t.color}})])},b=[],C={props:{width:{type:String,default:"44"},height:{type:String,default:"38"},color:{type:String,default:"currentColor"}}},w=C,v=Object(l["a"])(w,k,b,!1,null,null,null),_=v.exports,x=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 150 150",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("g",{attrs:{"clip-path":"url(#clip0)"}},[e("path",{attrs:{d:"M146.921 0.0209041C117.639 -0.504096 84.2456 14.8021 63.1082 38.5459C42.9082 38.9272 23.2395 47.2084 8.75835 61.6897C7.9146 62.5209 7.6146 63.7647 7.98335 64.8897C8.35835 66.0209 9.33334 66.8397 10.5083 67.0084L34.5958 70.4584L31.6207 73.7896C30.5145 75.0271 30.5707 76.9084 31.7458 78.0834L71.9144 118.252C72.5206 118.858 73.3206 119.165 74.1269 119.165C74.8707 119.165 75.6144 118.902 76.2081 118.371L79.5393 115.395L82.9893 139.483C83.1581 140.659 84.0831 141.521 85.2018 141.896C85.4894 141.989 85.7894 142.033 86.0956 142.033C86.9831 142.033 87.8894 141.645 88.5144 141.027C102.796 126.746 111.077 107.077 111.458 86.8771C135.227 65.6959 150.658 32.3146 149.976 3.07091C149.933 1.4084 148.589 0.064654 146.921 0.0209041ZM118.12 53.9709C115.077 57.0147 111.077 58.5397 107.071 58.5397C103.064 58.5397 99.0643 57.0147 96.0206 53.9709C89.9331 47.8771 89.9331 37.9647 96.0206 31.8709C102.114 25.7772 112.027 25.7772 118.12 31.8709C124.214 37.9647 124.214 47.8834 118.12 53.9709Z",fill:"white"}}),e("path",{attrs:{d:"M17.0253 105.662C10.3378 112.349 1.15661 142.561 0.131609 145.98C-0.199641 147.081 0.106608 148.274 0.912857 149.086C1.51286 149.686 2.3066 150.006 3.12535 150.006C3.42535 150.006 3.72535 149.962 4.02535 149.874C7.44409 148.85 37.6565 139.668 44.344 132.981C51.8753 125.45 51.8753 113.193 44.344 105.662C36.8066 98.1304 24.5566 98.1368 17.0253 105.662Z",fill:"white"}})]),e("defs",[e("clipPath",{attrs:{id:"clip0"}},[e("rect",{attrs:{width:"150",height:"150",fill:"white"}})])])])},$=[],L={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},P=L,S=Object(l["a"])(P,x,$,!1,null,null,null),M=S.exports,O=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 130 130"}},[e("path",{attrs:{fill:"#E5E7EA","fill-rule":"evenodd",d:"M60.5 28.362c2.95-2.475 3.352-6.895.863-9.861-2.49-2.967-6.913-3.338-9.863-.863a6.99 6.99 0 0 0-.862 9.862 6.99 6.99 0 0 0 9.862.862M40.178 76.841c5.705-4.787 6.465-13.296 1.664-19.018-4.788-5.706-13.314-6.452-19.019-1.665-5.722 4.802-6.452 13.314-1.664 19.02a13.482 13.482 0 0 0 19.02 1.663m69.284-8.467c6.564-5.508 7.419-15.273 1.91-21.837-5.494-6.548-15.272-7.419-21.836-1.91-6.548 5.494-7.405 15.288-1.91 21.836 5.507 6.564 15.288 7.405 21.836 1.91m-28.678 44.522c3.792-3.182 4.306-8.87 1.11-12.68-3.197-3.81-8.888-4.291-12.68-1.109a8.997 8.997 0 0 0-1.11 12.68 8.997 8.997 0 0 0 12.68 1.109m25.996 1.898c-27.506 23.08-68.493 19.495-91.574-8.011-23.08-27.507-19.494-68.495 8.012-91.575s68.493-19.495 91.574 8.012c23.08 27.506 19.494 68.494-8.012 91.574"}})])},j=[],A={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},B=A,T=Object(l["a"])(B,O,j,!1,null,null,null),H=T.exports,I=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 170 180",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M154.058 34.9258C154.036 33.556 153.437 32.3411 152.506 31.4648L120.631 1.46484C119.636 0.526042 118.257 0 116.875 0H21.25C18.3157 0 15.9375 2.23828 15.9375 5V61.1927C9.75342 63.2578 5.3125 68.8151 5.3125 75.3333V135.333C5.3125 141.852 9.75342 147.409 15.9375 149.474V165.667C15.9375 168.428 18.3157 170.667 21.25 170.667H148.75C151.685 170.667 154.062 168.428 154.062 165.667V149.474C160.246 147.409 164.688 141.852 164.688 135.333V75.3333C164.688 68.8151 160.246 63.2578 154.062 61.1927V35C154.062 34.9753 154.058 34.9505 154.058 34.9258ZM122.188 17.0716L135.923 30H122.188V17.0716ZM111.562 10V35C111.562 37.7617 113.94 40 116.875 40H143.438V60.3333H26.5625V10H111.562ZM26.5625 160.667V150.333H143.438V160.667H26.5625ZM154.062 135.333C154.062 138.09 151.679 140.333 148.75 140.333H21.25C18.3212 140.333 15.9375 138.09 15.9375 135.333V75.3333C15.9375 72.5768 18.3212 70.3333 21.25 70.3333H148.75C151.679 70.3333 154.062 72.5768 154.062 75.3333V135.333Z",fill:t.color}}),e("path",{attrs:{d:"M111.562 10V35C111.562 37.7617 113.94 40 116.875 40H143.438V60.3333H26.5625V10H111.562Z",fill:t.color}}),e("path",{attrs:{d:"M26.5625 160.666V150.332H143.438V160.666H26.5625Z",fill:t.color}}),e("path",{attrs:{d:"M154.062 135.332C154.062 138.089 151.679 140.332 148.75 140.332H21.25C18.3212 140.332 15.9375 138.089 15.9375 135.332V75.332C15.9375 72.5755 18.3212 70.332 21.25 70.332H148.75C151.679 70.332 154.062 72.5755 154.062 75.332V135.332Z",fill:t.color}}),e("path",{attrs:{d:"M42.5 90.332C45.4288 90.332 47.8125 92.5755 47.8125 95.332C47.8125 98.0938 50.1907 100.332 53.125 100.332C56.0593 100.332 58.4375 98.0938 58.4375 95.332C58.4375 87.0612 51.2877 80.332 42.5 80.332C33.7123 80.332 26.5625 87.0612 26.5625 95.332V115.332C26.5625 123.603 33.7123 130.332 42.5 130.332C51.2877 130.332 58.4375 123.603 58.4375 115.332C58.4375 112.57 56.0593 110.332 53.125 110.332C50.1907 110.332 47.8125 112.57 47.8125 115.332C47.8125 118.089 45.4288 120.332 42.5 120.332C39.5712 120.332 37.1875 118.089 37.1875 115.332V95.332C37.1875 92.5755 39.5712 90.332 42.5 90.332Z",fill:"white"}}),e("path",{attrs:{d:"M85 90.332C87.9288 90.332 90.3125 92.5755 90.3125 95.332C90.3125 98.0938 92.6907 100.332 95.625 100.332C98.5593 100.332 100.938 98.0938 100.938 95.332C100.938 87.0612 93.7877 80.332 85 80.332C76.2123 80.332 69.0625 87.0612 69.0625 95.332C69.0625 103.603 76.2123 110.332 85 110.332C87.9288 110.332 90.3125 112.576 90.3125 115.332C90.3125 118.089 87.9288 120.332 85 120.332C82.0712 120.332 79.6875 118.089 79.6875 115.332C79.6875 112.57 77.3093 110.332 74.375 110.332C71.4407 110.332 69.0625 112.57 69.0625 115.332C69.0625 123.603 76.2123 130.332 85 130.332C93.7877 130.332 100.938 123.603 100.938 115.332C100.938 107.061 93.7877 100.332 85 100.332C82.0712 100.332 79.6875 98.0885 79.6875 95.332C79.6875 92.5755 82.0712 90.332 85 90.332Z",fill:"white"}}),e("path",{attrs:{d:"M127.5 90.332C130.429 90.332 132.812 92.5755 132.812 95.332C132.812 98.0938 135.191 100.332 138.125 100.332C141.06 100.332 143.438 98.0938 143.438 95.332C143.438 87.0612 136.288 80.332 127.5 80.332C118.712 80.332 111.562 87.0612 111.562 95.332C111.562 103.603 118.712 110.332 127.5 110.332C130.429 110.332 132.812 112.576 132.812 115.332C132.812 118.089 130.429 120.332 127.5 120.332C124.572 120.332 122.188 118.089 122.188 115.332C122.188 112.57 119.81 110.332 116.875 110.332C113.941 110.332 111.562 112.57 111.562 115.332C111.562 123.603 118.712 130.332 127.5 130.332C136.288 130.332 143.438 123.603 143.438 115.332C143.438 107.061 136.288 100.332 127.5 100.332C124.572 100.332 122.188 98.0885 122.188 95.332C122.188 92.5755 124.572 90.332 127.5 90.332Z",fill:"white"}})])},V=[],E={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},N=E,D=Object(l["a"])(N,I,V,!1,null,null,null),Z=D.exports,U=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 150 188",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("g",[e("path",{attrs:{d:"M119.744 178.977H30.2557C16.1572 178.977 4.6875 167.508 4.6875 153.409V25.5682C4.6875 11.4698 16.1572 0 30.2557 0H69.7066C74.8282 0 79.6422 1.99352 83.2617 5.61701L139.696 62.0508C143.315 65.6743 145.309 70.4883 145.312 75.6059V153.409C145.312 167.508 133.843 178.977 119.744 178.977ZM30.2557 12.7841C23.2085 12.7841 17.4716 18.521 17.4716 25.5682V153.409C17.4716 160.456 23.2085 166.193 30.2557 166.193H119.744C126.791 166.193 132.529 160.456 132.529 153.409V75.6059C132.529 73.9 131.865 72.294 130.655 71.0875L74.225 14.6538C73.0185 13.4472 71.4125 12.7841 69.7066 12.7841H30.2557Z",fill:t.color}}),e("path",{attrs:{d:"M30.2528 12.7852C23.2056 12.7852 17.4688 18.522 17.4688 25.5692V153.41C17.4688 160.457 23.2056 166.194 30.2528 166.194H119.741C126.788 166.194 132.526 160.457 132.526 153.41V75.6069C132.526 73.9011 131.862 72.295 130.652 71.0886L74.2221 14.6548C73.0156 13.4483 71.4096 12.7852 69.7037 12.7852H30.2528Z",fill:t.color}}),e("path",{attrs:{d:"M30.2557 0H119.744C133.843 0 145.312 11.4697 145.312 25.5682V153.409C145.312 167.508 133.843 178.977 119.744 178.977H80.2934C75.1717 178.977 70.3578 176.984 66.7383 173.361L10.3045 116.927C6.68501 113.303 4.69149 108.489 4.6875 103.371V25.5682C4.6875 11.4697 16.1572 0 30.2557 0ZM119.744 166.193C126.791 166.193 132.529 160.457 132.529 153.409V25.5682C132.529 18.521 126.791 12.7841 119.744 12.7841H30.2557C23.2085 12.7841 17.4716 18.521 17.4716 25.5682V103.371C17.4716 105.077 18.1348 106.684 19.3452 107.889L75.775 164.324C76.9815 165.53 78.5875 166.193 80.2934 166.193H119.744Z",fill:t.color}}),e("path",{attrs:{d:"M119.741 166.194C126.788 166.194 132.526 160.458 132.526 153.41V25.5692C132.526 18.522 126.788 12.7852 119.741 12.7852H30.2528C23.2056 12.7852 17.4688 18.522 17.4688 25.5692V103.372C17.4688 105.078 18.1319 106.685 19.3423 107.89L75.7722 164.325C76.9786 165.531 78.5847 166.194 80.2905 166.194H119.741Z",fill:t.color}}),e("path",{attrs:{d:"M106.613 71.1591H43.403C39.9107 71.1591 37.082 68.2986 37.082 64.767C37.082 61.2355 39.9107 58.375 43.403 58.375H106.613C110.105 58.375 112.933 61.2355 112.933 64.767C112.933 68.2986 110.105 71.1591 106.613 71.1591Z",fill:"white"}}),e("path",{attrs:{d:"M106.965 102.691H43.0444C39.5128 102.691 36.6523 99.8302 36.6523 96.2987C36.6523 92.7667 39.5128 89.9062 43.0444 89.9062H106.965C110.497 89.9062 113.357 92.7667 113.357 96.2987C113.357 99.8302 110.497 102.691 106.965 102.691Z",fill:"white"}}),e("path",{attrs:{d:"M106.965 134.229H43.0444C39.5128 134.229 36.6523 131.369 36.6523 127.837C36.6523 124.306 39.5128 121.445 43.0444 121.445H106.965C110.497 121.445 113.357 124.306 113.357 127.837C113.357 131.369 110.497 134.229 106.965 134.229Z",fill:"white"}})])])},R=[],F={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},G=F,q=Object(l["a"])(G,U,R,!1,null,null,null),z=q.exports,Y=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 160 180",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M112.099 85.8059H47.901C24.2446 85.8059 5 66.5574 5 42.901C5 19.2446 24.2446 0 47.901 0H112.099C135.755 0 155 19.2446 155 42.901C155 66.5574 135.755 85.8059 112.099 85.8059ZM47.901 12.4385C31.1052 12.4385 17.4385 26.1052 17.4385 42.901C17.4385 59.6968 31.1052 73.3636 47.901 73.3636H112.099C128.899 73.3636 142.562 59.6968 142.562 42.901C142.562 26.1052 128.899 12.4385 112.099 12.4385H47.901Z",fill:t.color}}),e("path",{attrs:{d:"M47.9039 12.4375C31.1081 12.4375 17.4414 26.1042 17.4414 42.9C17.4414 59.6958 31.1081 73.3626 47.9039 73.3626H112.102C128.902 73.3626 142.565 59.6958 142.565 42.9C142.565 26.1042 128.902 12.4375 112.102 12.4375H47.9039Z",fill:t.color}}),e("path",{attrs:{d:"M87.5 45.0007C87.5 54.6524 95.3481 62.5 105 62.5C114.652 62.5 122.5 54.6507 122.5 44.9993C122.5 35.3476 114.652 27.5 105 27.5C95.3478 27.5 87.5 35.3493 87.5 45.0007ZM96.849 44.9993C96.849 40.5077 100.507 36.8488 105 36.8488C109.493 36.8488 113.151 40.5077 113.151 44.9993C113.151 49.4908 109.492 53.1498 105 53.1498C100.508 53.1498 96.849 49.4908 96.849 44.9993Z",fill:"white",stroke:"#ADADAD","stroke-width":"0.5"}}),e("path",{attrs:{d:"M112.099 179.751H47.9049C24.2446 179.751 5 160.507 5 136.846C5 113.186 24.2446 93.9453 47.9049 93.9453H112.099C135.755 93.9453 155 113.19 155 136.846C155 160.503 135.755 179.751 112.099 179.751ZM47.9049 106.384C31.1052 106.384 17.4385 120.051 17.4385 136.846C17.4385 153.642 31.1052 167.309 47.901 167.309H112.095C128.895 167.309 142.558 153.642 142.558 136.846C142.558 120.051 128.891 106.384 112.095 106.384H47.9049Z",fill:t.color}}),e("path",{attrs:{d:"M47.9078 106.383C31.1081 106.383 17.4414 120.05 17.4414 136.845C17.4414 153.641 31.1081 167.308 47.9039 167.308H112.098C128.898 167.308 142.561 153.641 142.561 136.845C142.561 120.05 128.894 106.383 112.098 106.383H47.9078Z",fill:t.color}}),e("path",{attrs:{d:"M37.5 135.001C37.5 144.652 45.3481 152.5 55 152.5C64.6522 152.5 72.5 144.651 72.5 134.999C72.5 125.348 64.6519 117.5 55 117.5C45.3478 117.5 37.5 125.349 37.5 135.001ZM46.849 134.999C46.849 130.508 50.5069 126.849 55 126.849C59.4931 126.849 63.151 130.508 63.151 134.999C63.151 139.491 59.4919 143.15 55 143.15C50.5081 143.15 46.849 139.491 46.849 134.999Z",fill:"white",stroke:"#ADADAD","stroke-width":"0.5"}})])},W=[],J={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},Q=J,K=Object(l["a"])(Q,Y,W,!1,null,null,null),X=K.exports,tt=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 384 512"}},[e("g",{attrs:{fill:t.color}},[e("path",{attrs:{d:"M231.6 256l130.1-130.1c4.7-4.7 4.7-12.3 0-17l-22.6-22.6c-4.7-4.7-12.3-4.7-17 0L192 216.4 61.9 86.3c-4.7-4.7-12.3-4.7-17 0l-22.6 22.6c-4.7 4.7-4.7 12.3 0 17L152.4 256 22.3 386.1c-4.7 4.7-4.7 12.3 0 17l22.6 22.6c4.7 4.7 12.3 4.7 17 0L192 295.6l130.1 130.1c4.7 4.7 12.3 4.7 17 0l22.6-22.6c4.7-4.7 4.7-12.3 0-17L231.6 256z"}})])])},et=[],nt={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"inherit"}}},ot=nt,st=Object(l["a"])(ot,tt,et,!1,null,null,null),at=st.exports,it=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 111 111",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M110.582 17.2926C110.577 12.6648 108.771 8.31676 105.496 5.04875C98.7401 -1.69178 87.7571 -1.68248 81.009 5.07411L11.3007 75.0629C9.0333 77.3287 7.30253 80.1335 6.29517 83.1751L6.23009 83.3728L0 110.769L27.4726 104.619L27.6771 104.551C30.7195 103.544 33.5261 101.815 35.7994 99.5422L105.517 29.5449C108.787 26.271 110.586 21.9195 110.582 17.2926ZM15.2347 98.4968L12.3216 95.582L14.425 86.3298L24.5097 96.4204L15.2347 98.4968ZM99.3942 23.4382L31.7108 91.3937L19.4543 79.1296L65.8174 32.5805L72.8774 39.6405L78.9917 33.5262L71.919 26.4535L78.4965 19.8507L85.5818 26.9361L91.6961 20.8217L84.5981 13.7237L87.1309 11.1808C90.5071 7.80041 96.0061 7.79534 99.3883 11.1698C101.028 12.8059 101.932 14.9829 101.935 17.2994C101.936 19.6166 101.036 21.7945 99.3942 23.4382ZM45.4885 102.028H110.582V110.676H36.871L45.4885 102.028Z",fill:t.color}})])},rt=[],ct={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},lt=ct,ut=Object(l["a"])(lt,it,rt,!1,null,null,null),dt=ut.exports,pt=function(){var t=this,e=t._self._c;return e("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:t.width,height:t.height,fill:"none",viewBox:"0 0 16 16"}},[e("path",{attrs:{d:"M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z",fill:t.color}}),e("path",{attrs:{"fill-rule":"evenodd",d:"M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z",fill:t.color}})])},ht=[],gt={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},yt=gt,ft=Object(l["a"])(yt,pt,ht,!1,null,null,null),mt=ft.exports,kt=function(){var t=this,e=t._self._c;return e("svg",{staticStyle:{margin:"auto",background:"none",display:"block","shape-rendering":"auto"},attrs:{xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",preserveAspectRatio:"xMidYMid",viewBox:"24 24 52 52",width:t.width,height:t.height}},[e("g",{attrs:{transform:"rotate(0 50 50)"}},[e("rect",{attrs:{x:"47",y:"24",rx:"2.7600000000000002",ry:"2.7600000000000002",width:"6",height:"12",fill:"#000000"}},[e("animate",{attrs:{attributeName:"opacity",values:"1;0",keyTimes:"0;1",dur:"1.0204081632653061s",begin:"-0.935374149659864s",repeatCount:"indefinite"}})])]),e("g",{attrs:{transform:"rotate(30 50 50)"}},[e("rect",{attrs:{x:"47",y:"24",rx:"2.7600000000000002",ry:"2.7600000000000002",width:"6",height:"12",fill:"#000000"}},[e("animate",{attrs:{attributeName:"opacity",values:"1;0",keyTimes:"0;1",dur:"1.0204081632653061s",begin:"-0.8503401360544218s",repeatCount:"indefinite"}})])]),e("g",{attrs:{transform:"rotate(60 50 50)"}},[e("rect",{attrs:{x:"47",y:"24",rx:"2.7600000000000002",ry:"2.7600000000000002",width:"6",height:"12",fill:"#000000"}},[e("animate",{attrs:{attributeName:"opacity",values:"1;0",keyTimes:"0;1",dur:"1.0204081632653061s",begin:"-0.7653061224489796s",repeatCount:"indefinite"}})])]),e("g",{attrs:{transform:"rotate(90 50 50)"}},[e("rect",{attrs:{x:"47",y:"24",rx:"2.7600000000000002",ry:"2.7600000000000002",width:"6",height:"12",fill:"#000000"}},[e("animate",{attrs:{attributeName:"opacity",values:"1;0",keyTimes:"0;1",dur:"1.0204081632653061s",begin:"-0.6802721088435374s",repeatCount:"indefinite"}})])]),e("g",{attrs:{transform:"rotate(120 50 50)"}},[e("rect",{attrs:{x:"47",y:"24",rx:"2.7600000000000002",ry:"2.7600000000000002",width:"6",height:"12",fill:"#000000"}},[e("animate",{attrs:{attributeName:"opacity",values:"1;0",keyTimes:"0;1",dur:"1.0204081632653061s",begin:"-0.5952380952380952s",repeatCount:"indefinite"}})])]),e("g",{attrs:{transform:"rotate(150 50 50)"}},[e("rect",{attrs:{x:"47",y:"24",rx:"2.7600000000000002",ry:"2.7600000000000002",width:"6",height:"12",fill:"#000000"}},[e("animate",{attrs:{attributeName:"opacity",values:"1;0",keyTimes:"0;1",dur:"1.0204081632653061s",begin:"-0.5102040816326531s",repeatCount:"indefinite"}})])]),e("g",{attrs:{transform:"rotate(180 50 50)"}},[e("rect",{attrs:{x:"47",y:"24",rx:"2.7600000000000002",ry:"2.7600000000000002",width:"6",height:"12",fill:"#000000"}},[e("animate",{attrs:{attributeName:"opacity",values:"1;0",keyTimes:"0;1",dur:"1.0204081632653061s",begin:"-0.4251700680272109s",repeatCount:"indefinite"}})])]),e("g",{attrs:{transform:"rotate(210 50 50)"}},[e("rect",{attrs:{x:"47",y:"24",rx:"2.7600000000000002",ry:"2.7600000000000002",width:"6",height:"12",fill:"#000000"}},[e("animate",{attrs:{attributeName:"opacity",values:"1;0",keyTimes:"0;1",dur:"1.0204081632653061s",begin:"-0.3401360544217687s",repeatCount:"indefinite"}})])]),e("g",{attrs:{transform:"rotate(240 50 50)"}},[e("rect",{attrs:{x:"47",y:"24",rx:"2.7600000000000002",ry:"2.7600000000000002",width:"6",height:"12",fill:"#000000"}},[e("animate",{attrs:{attributeName:"opacity",values:"1;0",keyTimes:"0;1",dur:"1.0204081632653061s",begin:"-0.25510204081632654s",repeatCount:"indefinite"}})])]),e("g",{attrs:{transform:"rotate(270 50 50)"}},[e("rect",{attrs:{x:"47",y:"24",rx:"2.7600000000000002",ry:"2.7600000000000002",width:"6",height:"12",fill:"#000000"}},[e("animate",{attrs:{attributeName:"opacity",values:"1;0",keyTimes:"0;1",dur:"1.0204081632653061s",begin:"-0.17006802721088435s",repeatCount:"indefinite"}})])]),e("g",{attrs:{transform:"rotate(300 50 50)"}},[e("rect",{attrs:{x:"47",y:"24",rx:"2.7600000000000002",ry:"2.7600000000000002",width:"6",height:"12",fill:"#000000"}},[e("animate",{attrs:{attributeName:"opacity",values:"1;0",keyTimes:"0;1",dur:"1.0204081632653061s",begin:"-0.08503401360544217s",repeatCount:"indefinite"}})])]),e("g",{attrs:{transform:"rotate(330 50 50)"}},[e("rect",{attrs:{x:"47",y:"24",rx:"2.7600000000000002",ry:"2.7600000000000002",width:"6",height:"12",fill:"#000000"}},[e("animate",{attrs:{attributeName:"opacity",values:"1;0",keyTimes:"0;1",dur:"1.0204081632653061s",begin:"0s",repeatCount:"indefinite"}})])])])},bt=[],Ct={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},wt=Ct,vt=Object(l["a"])(wt,kt,bt,!1,null,null,null),_t=vt.exports,xt=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 13 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M10 7.33V10C10 10.2652 9.89464 10.5196 9.7071 10.7071C9.51957 10.8946 9.26521 11 9 11H2C1.73478 11 1.48043 10.8946 1.29289 10.7071C1.10536 10.5196 1 10.2652 1 10V3C1 2.73478 1.10536 2.48043 1.29289 2.29289C1.48043 2.10536 1.73478 2 2 2H4.67",stroke:t.color,"stroke-opacity":"0.7","stroke-linecap":"square"}}),e("path",{attrs:{d:"M9.40039 1L11.4004 3L6.40039 8H4.40039V6L9.40039 1Z",stroke:t.color,"stroke-opacity":"0.7","stroke-linecap":"square"}})])},$t=[],Lt={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},Pt=Lt,St=Object(l["a"])(Pt,xt,$t,!1,null,null,null),Mt=St.exports,Ot=function(){var t=this,e=t._self._c;return e("div",[e("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:t.width,height:t.height,viewBox:"0 0 56 56",fill:"none"}},[e("path",{attrs:{d:"M9.41992 12.1953H25.2252",stroke:"#134FB0","stroke-width":"2","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{d:"M9.41797 22.7324H32.7859",stroke:"#134FB0","stroke-width":"2","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{d:"M9.41992 36.7812L23.6694 36.7813",stroke:"#134FB0","stroke-width":"2","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{d:"M9.41992 29.7559H25.6957",stroke:"#134FB0","stroke-width":"2","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{d:"M45.9583 20.9082V1.6582L2.39453 1.6582V54.3424H28.7347",stroke:"#134FB0","stroke-width":"2","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{d:"M42.8986 26.3436L46.9121 28.005C46.1167 29.0515 45.6857 30.3296 45.6849 31.6441C45.6842 32.9586 46.1138 34.2372 46.908 35.2846C47.7023 36.332 48.8175 37.0906 50.0835 37.4446C51.3494 37.7985 52.6964 37.7284 53.9187 37.2448L53.9985 37.4435C54.4566 38.5496 54.4566 39.7925 53.9985 40.8986C53.2511 42.0825 52.7374 43.0115 52.4575 43.6856C52.1736 44.3719 51.8425 45.4834 51.4643 47.0204C51.0058 48.1264 50.1267 49.0049 49.0204 49.4628C47.4433 49.8581 46.3317 50.1896 45.6856 50.4575C44.9713 50.7535 44.0423 51.2671 42.8986 51.9985C41.7925 52.4566 40.5496 52.4566 39.4435 51.9985C38.2366 51.242 37.3076 50.7284 36.6565 50.4575C35.9472 50.1646 34.8356 49.8335 33.3217 49.4643C32.2157 49.0058 31.3372 48.1267 30.8793 47.0204C30.481 45.4383 30.1495 44.3267 29.8846 43.6856C29.5856 42.9653 29.072 42.0363 28.3436 40.8986C27.8855 39.7925 27.8855 38.5496 28.3436 37.4435C29.065 36.3239 29.5786 35.3949 29.8846 34.6565C30.1424 34.0345 30.4735 32.9229 30.8778 31.3217C31.3363 30.2157 32.2154 29.3372 33.3217 28.8793C34.8737 28.4941 35.9853 28.1625 36.6565 27.8846C37.3457 27.5987 38.2747 27.085 39.4435 26.3436C40.5496 25.8855 41.7925 25.8855 42.8986 26.3436Z",fill:"#57BD8D",stroke:"#57BD8D","stroke-width":"1.25","stroke-miterlimit":"22.9256","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{d:"M35.1504 40.6747V40.6897M41.1698 46.6941V46.7092M41.1698 39.1698V39.1849M47.1893 42.1796V42.1946M39.665 33.1504V33.1654",stroke:"white","stroke-width":"1.25","stroke-miterlimit":"22.9256","stroke-linecap":"round","stroke-linejoin":"round"}})])])},jt=[],At={name:"CookiePolicy",props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},Bt=At,Tt=Object(l["a"])(Bt,Ot,jt,!1,null,null,null),Ht=Tt.exports,It=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 56 56",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M29.0189 20.2314V23.8901C29.0189 26.9322 27.5937 29.7701 25.2268 31.4411L22.2737 33.5259L19.3206 31.4411C16.9536 29.7701 15.5284 26.9322 15.5284 23.8901V17.0904C19.2075 14.9958 23.5211 14.7279 27.3625 16.2867",fill:"#57BD8D"}}),e("path",{attrs:{d:"M29.0189 20.2314V23.8901C29.0189 26.9322 27.5937 29.7701 25.2268 31.4411L22.2737 33.5259L19.3206 31.4411C16.9536 29.7701 15.5284 26.9322 15.5284 23.8901V17.0904C19.2075 14.9958 23.5211 14.7279 27.3625 16.2867L28.9164 17.0904L29.0189 20.2314Z",stroke:"#57BD8D","stroke-width":"1.25","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{d:"M29.9625 2.11816H3.78017C2.81796 2.11816 2.03796 2.93826 2.03796 3.94996L2.03796 52.9706C2.03796 53.9823 2.81796 54.8024 3.78017 54.8024H41.7151C42.6774 54.8024 43.4574 53.9823 43.4574 52.9706V16.3069C43.4574 15.8035 43.2672 15.3207 42.9286 14.9648L31.239 2.67409C30.9005 2.31814 30.4413 2.11816 29.9625 2.11816Z",stroke:"#134FB0","stroke-width":"2","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{d:"M8.23358 47.8887H10.4531",stroke:"#134FB0","stroke-width":"2","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{d:"M8.23358 40.5186H10.4531",stroke:"#134FB0","stroke-width":"2","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{d:"M15.9838 40.5186H37.2615",stroke:"#134FB0","stroke-width":"2","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{d:"M15.9839 47.8887H37.2616",stroke:"#134FB0","stroke-width":"2","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{d:"M30.7095 3.13086L30.7095 12.84C30.7095 13.7608 31.4194 14.5072 32.2952 14.5072H42.5099",stroke:"#134FB0","stroke-width":"2","stroke-miterlimit":"10","stroke-linecap":"round","stroke-linejoin":"round"}})])},Vt=[],Et={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},Nt=Et,Dt=Object(l["a"])(Nt,It,Vt,!1,null,null,null),Zt=Dt.exports,Ut=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 12 13",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M5.5 10C7.98528 10 10 7.98528 10 5.5C10 3.01472 7.98528 1 5.5 1C3.01472 1 1 3.01472 1 5.5C1 7.98528 3.01472 10 5.5 10Z",stroke:t.color,"stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{d:"M11 12L8.5 9",stroke:t.color,"stroke-linecap":"round","stroke-linejoin":"round"}})])},Rt=[],Ft={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},Gt=Ft,qt=Object(l["a"])(Gt,Ut,Rt,!1,null,null,null),zt=qt.exports,Yt=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("rect",{attrs:{y:"1.06641",width:"16",height:"2.13333",rx:"1",fill:t.color}}),e("rect",{attrs:{y:"12.7998",width:"16",height:"2.13333",rx:"1",fill:t.color}}),e("rect",{attrs:{y:"7.4668",width:"16",height:"2.13333",rx:"1",fill:t.color}}),e("path",{attrs:{d:"M13.664 2.13333C13.664 3.28073 12.6663 4.21667 11.4283 4.21667C10.1902 4.21667 9.19258 3.28073 9.19258 2.13333C9.19258 0.985933 10.1902 0.05 11.4283 0.05C12.6663 0.05 13.664 0.985933 13.664 2.13333Z",fill:t.color,stroke:"#565662","stroke-width":"0.1"}}),e("ellipse",{attrs:{cx:"11.4285",cy:"2.13307",rx:"1.14286",ry:"1.06667",fill:"white"}}),e("path",{attrs:{d:"M13.664 13.8667C13.664 15.0141 12.6663 15.9501 11.4283 15.9501C10.1902 15.9501 9.19258 15.0141 9.19258 13.8667C9.19258 12.7193 10.1902 11.7834 11.4283 11.7834C12.6663 11.7834 13.664 12.7193 13.664 13.8667Z",fill:t.color,stroke:"#565662","stroke-width":"0.1"}}),e("path",{attrs:{d:"M6.80805 8.53372C6.80805 9.68112 5.81039 10.6171 4.57234 10.6171C3.33428 10.6171 2.33662 9.68112 2.33662 8.53372C2.33662 7.38632 3.33428 6.45039 4.57234 6.45039C5.81039 6.45039 6.80805 7.38632 6.80805 8.53372Z",fill:t.color,stroke:"#565662","stroke-width":"0.1"}}),e("ellipse",{attrs:{cx:"4.57206",cy:"8.53346",rx:"1.14286",ry:"1.06667",fill:"white"}}),e("ellipse",{attrs:{cx:"11.4285",cy:"13.8665",rx:"1.14286",ry:"1.06667",fill:"white"}})])},Wt=[],Jt={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},Qt=Jt,Kt=Object(l["a"])(Qt,Yt,Wt,!1,null,null,null),Xt=Kt.exports,te=function(){var t=this,e=t._self._c;return e("svg",{staticStyle:{"enable-background":"new 0 0 52 52"},attrs:{version:"1.1",id:"Capa_1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",x:"0px",y:"0px",viewBox:"0 0 52 52","xml:space":"preserve",width:t.width,height:t.height}},[e("g",[e("circle",{attrs:{fill:t.color,cx:"25",cy:"25",r:"25"}}),e("path"),e("path",{attrs:{d:"M38.252,15.336l-15.369,17.29l-9.259-7.407c-0.43-0.345-1.061-0.274-1.405,0.156c-0.345,0.432-0.275,1.061,0.156,1.406\n\tl10,8C22.559,34.928,22.78,35,23,35c0.276,0,0.551-0.114,0.748-0.336l16-18c0.367-0.412,0.33-1.045-0.083-1.411\n\tC39.251,14.885,38.62,14.922,38.252,15.336z",stroke:"#fff","stroke-width":"3"}})])])},ee=[],ne={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},oe=ne,se=Object(l["a"])(oe,te,ee,!1,null,null,null),ae=se.exports,ie=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 44 38",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M22.7239 1.8166C14.9248 1.8166 8.34688 7.05377 6.31884 14.2046C5.89885 15.6855 5.67383 17.2492 5.67383 18.8667C5.67383 23.3583 7.40962 27.4428 10.2484 30.4886C13.3619 33.8293 17.7986 35.9168 22.7239 35.9168C32.1404 35.9168 39.774 28.2832 39.774 18.8667C39.774 17.4438 39.5999 16.0626 39.2721 14.7429C37.4282 7.31839 30.7177 1.8166 22.7239 1.8166ZM4.87576 13.7954C7.08198 6.01624 14.2364 0.316605 22.7239 0.316605C31.423 0.316605 38.7218 6.30383 40.7279 14.3813C41.0848 15.8184 41.274 17.321 41.274 18.8667C41.274 29.1116 32.9689 37.4168 22.7239 37.4168C17.3653 37.4168 12.5365 35.1437 9.1511 31.5114C6.06378 28.1988 4.17383 23.7526 4.17383 18.8667C4.17383 17.1095 4.41838 15.4081 4.87576 13.7954Z",fill:"#1863DC"}}),e("path",{attrs:{d:"M40.012 0.5H31.7324C30.1733 0.5 28.9094 1.76389 28.9094 3.32299V11.6026C28.9094 13.1617 30.1733 14.4256 31.7324 14.4256H40.012C41.5711 14.4256 42.835 13.1617 42.835 11.6026V3.32299C42.835 1.76389 41.5711 0.5 40.012 0.5Z",fill:"white"}}),e("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M31.7324 1C30.4495 1 29.4094 2.04004 29.4094 3.32299V11.6026C29.4094 12.8856 30.4495 13.9256 31.7324 13.9256H40.012C41.295 13.9256 42.335 12.8856 42.335 11.6026V3.32299C42.335 2.04003 41.295 1 40.012 1H31.7324ZM28.4094 3.32299C28.4094 1.48775 29.8972 0 31.7324 0H40.012C41.8473 0 43.335 1.48775 43.335 3.32299V11.6026C43.335 13.4379 41.8473 14.9256 40.012 14.9256H31.7324C29.8972 14.9256 28.4094 13.4379 28.4094 11.6026V3.32299Z",fill:"#1863DC"}}),e("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M24.1107 25.2002C24.1135 25.5592 23.8248 25.8525 23.4658 25.8553C23.1485 25.8578 22.8237 26.0626 22.6845 26.349C22.5451 26.6358 22.5841 27.0175 22.7785 27.2701L22.7788 27.2705C22.8717 27.3914 23.0022 27.5003 23.2024 27.6516C23.2223 27.6666 23.2432 27.6822 23.2648 27.6985C23.4389 27.8289 23.6663 27.9993 23.8602 28.2068C24.1203 28.4853 24.3056 28.8665 24.3532 29.2558C24.4015 29.6503 24.3111 30.1549 23.8711 30.4896L23.8708 30.4899C23.5192 30.7569 23.1135 30.781 22.7905 30.7187C22.4717 30.6572 22.1798 30.5052 21.9544 30.3314C21.8245 30.2312 21.6827 30.1013 21.5724 30.0003C21.5293 29.9608 21.4909 29.9257 21.46 29.8985C21.3217 29.7769 21.225 29.7095 21.1361 29.6724L21.387 29.0727L21.1366 29.6725C20.9729 29.6042 20.7491 29.6263 20.4968 29.8217C20.2413 30.0196 20.0531 30.3314 20.0085 30.6267L20.0084 30.6271C19.9847 30.7835 19.9806 30.9369 19.9754 31.1342C19.974 31.1882 19.9725 31.2455 19.9704 31.3071C19.9618 31.5677 19.943 31.8934 19.8394 32.2235C19.5557 33.1285 18.7378 33.7322 17.9042 33.92C17.0703 34.1078 16.2255 33.9263 15.528 33.6039C14.1555 32.9707 13.1063 31.6977 12.7463 30.2302C12.3862 28.7622 12.729 27.1475 13.6525 25.9519C13.872 25.6678 14.2802 25.6154 14.5643 25.8349C14.8484 26.0543 14.9008 26.4625 14.6813 26.7466C13.9995 27.6292 13.7432 28.8375 14.0089 29.9205C14.2746 31.0039 15.0598 31.9564 16.0729 32.4236L16.0733 32.4238C16.5952 32.665 17.1424 32.759 17.6186 32.6518C18.0952 32.5444 18.4808 32.2113 18.5989 31.8346L18.599 31.8342C18.6464 31.6836 18.6631 31.5085 18.6711 31.2642C18.6724 31.2249 18.6735 31.1825 18.6746 31.1377C18.6796 30.9329 18.686 30.677 18.7231 30.432L19.3658 30.5295L18.7231 30.4324C18.7231 30.4323 18.7231 30.4321 18.7231 30.432C18.8217 29.7809 19.2024 29.1799 19.7007 28.7939C20.2024 28.4054 20.9176 28.1724 21.6375 28.4729L21.6379 28.4731C21.925 28.5932 22.1492 28.7735 22.3185 28.9223C22.3943 28.9889 22.4543 29.0442 22.5076 29.0933C22.5931 29.172 22.661 29.2346 22.7484 29.302C22.8457 29.3771 22.9542 29.4263 23.0366 29.4422C23.0477 29.4444 23.0573 29.4457 23.0652 29.4466C23.0654 29.4543 23.0651 29.461 23.0647 29.4665C23.0637 29.4787 23.062 29.484 23.062 29.4838C23.0637 29.4787 23.0719 29.4642 23.0841 29.4549L23.4776 29.9722L23.0844 29.4546C23.0899 29.4505 23.0943 29.4482 23.0973 29.447C23.1003 29.4466 23.1015 29.4461 23.1014 29.4459C23.1013 29.4458 23.0998 29.446 23.0973 29.447C23.0922 29.4478 23.0821 29.4484 23.0652 29.4466C23.0651 29.4372 23.0644 29.4262 23.0628 29.4136C23.05 29.3087 22.9904 29.1801 22.9102 29.0942C22.7994 28.9756 22.6672 28.8761 22.4796 28.7347C22.46 28.7199 22.4397 28.7047 22.4188 28.6889C22.2193 28.5382 21.9591 28.3373 21.7481 28.0627C21.7482 28.0628 21.7483 28.0629 21.7484 28.0631L22.2634 27.6666L21.7481 28.0627C21.253 27.4193 21.1606 26.5106 21.5153 25.7807C21.8703 25.0502 22.6441 24.5618 23.4556 24.5554C23.8146 24.5525 24.1079 24.8413 24.1107 25.2002Z",fill:"#1863DC"}}),e("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M36.6782 15.8416C36.8516 16.2177 36.6873 16.6633 36.3111 16.8367L31.53 19.041C31.1539 19.2145 30.7083 19.0501 30.5349 18.674C30.3615 18.2978 30.5258 17.8523 30.902 17.6789L35.6831 15.4745C36.0593 15.3011 36.5048 15.4654 36.6782 15.8416Z",fill:"#1863DC"}}),e("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M38.8044 20.4531C38.9778 20.8292 38.8134 21.2747 38.4372 21.4481L33.6552 23.6525C33.279 23.8259 32.8335 23.6615 32.6601 23.2853C32.4867 22.9092 32.6511 22.4637 33.0273 22.2903L37.8093 20.0859C38.1855 19.9125 38.631 20.0769 38.8044 20.4531Z",fill:"#1863DC"}}),e("path",{attrs:{d:"M25.8735 18.3365L30.2711 16.3092L34.3254 25.1037L29.9278 27.131C27.5005 28.25 24.6218 27.188 23.5028 24.7607C22.3838 22.3335 23.4459 19.4547 25.8731 18.3357L25.8735 18.3365Z",fill:"white"}}),e("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M25.8731 18.3357C25.8448 18.3488 25.8167 18.3621 25.7887 18.3756C25.5095 18.5108 25.2489 18.6696 25.0083 18.8484C23.2093 20.1857 22.5274 22.6448 23.5028 24.7607C24.6218 27.188 27.5006 28.25 29.9278 27.131L34.3254 25.1037L30.2711 16.3092L25.8731 18.3357ZM26.363 17.0097L30.7606 14.9824L35.6522 25.5932L30.3465 28.0392C27.4177 29.3894 23.9449 28.1082 22.5947 25.1794C21.2445 22.2506 22.5257 18.7778 25.4545 17.4276L26.363 17.0097Z",fill:"#1863DC"}}),e("path",{attrs:{d:"M18.5401 21.9744C18.5401 16.9928 14.5017 12.9543 9.52004 12.9543C4.53841 12.9543 0.5 16.9928 0.5 21.9744C0.5 26.956 4.53841 30.9944 9.52004 30.9944C14.5017 30.9944 18.5401 26.956 18.5401 21.9744Z",fill:"white"}}),e("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M9.52004 13.4543C4.81455 13.4543 1 17.2689 1 21.9744C1 26.6799 4.81455 30.4944 9.52004 30.4944C14.2255 30.4944 18.0401 26.6799 18.0401 21.9744C18.0401 17.2689 14.2255 13.4543 9.52004 13.4543ZM0 21.9744C0 16.7166 4.26227 12.4543 9.52004 12.4543C14.7778 12.4543 19.0401 16.7166 19.0401 21.9744C19.0401 27.2322 14.7778 31.4944 9.52004 31.4944C4.26227 31.4944 0 27.2322 0 21.9744Z",fill:"#1863DC"}}),e("path",{attrs:{d:"M2.4003 21.9731C2.4003 24.7792 4.03134 27.2058 6.39657 28.3559L3.01361 19.0879C2.62163 19.9688 2.39941 20.9456 2.39941 21.974L2.4003 21.9731ZM14.2789 21.6149C14.2789 20.7385 13.9642 20.1314 13.694 19.6594C13.3358 19.0746 12.9981 18.5813 12.9981 17.9964C12.9981 17.3449 13.4931 16.7378 14.1891 16.7378C14.2202 16.7378 14.2504 16.7413 14.2815 16.7431C13.0194 15.5876 11.3386 14.8819 9.49332 14.8819C7.0152 14.8819 4.83664 16.1529 3.56825 18.0791C3.73447 18.0844 5.00463 18.0364 5.00463 18.0364L7.72984 26.3294L9.28177 21.6753L8.01694 17.9964H10.3617L13.0318 26.2707L13.7402 23.9064C14.0984 22.9846 14.2789 22.222 14.2789 21.614V21.6149ZM9.61598 22.5944L7.48808 28.7764C8.12449 28.963 8.79557 29.0652 9.49243 29.0652C10.3173 29.0652 11.1101 28.9221 11.847 28.6635C11.8283 28.6333 11.8097 28.6004 11.7954 28.5657L9.61687 22.5944H9.61598ZM15.7135 18.5715C15.7446 18.7972 15.7615 19.039 15.7615 19.3012C15.7615 20.0203 15.6264 20.8292 15.2211 21.8398L13.0558 28.1017C15.1642 26.8724 16.581 24.589 16.581 21.9731C16.5828 20.7394 16.2672 19.5803 15.7135 18.5706V18.5715Z",fill:"url(#paint0_linear_468_260)","fill-opacity":"0.76"}}),e("path",{attrs:{d:"M2.4003 21.9731C2.4003 24.7792 4.03134 27.2058 6.39657 28.3559L3.01361 19.0879C2.62163 19.9688 2.39941 20.9456 2.39941 21.974L2.4003 21.9731ZM14.2789 21.6149C14.2789 20.7385 13.9642 20.1314 13.694 19.6594C13.3358 19.0746 12.9981 18.5813 12.9981 17.9964C12.9981 17.3449 13.4931 16.7378 14.1891 16.7378C14.2202 16.7378 14.2504 16.7413 14.2815 16.7431C13.0194 15.5876 11.3386 14.8819 9.49332 14.8819C7.0152 14.8819 4.83664 16.1529 3.56825 18.0791C3.73447 18.0844 5.00463 18.0364 5.00463 18.0364L7.72984 26.3294L9.28177 21.6753L8.01694 17.9964H10.3617L13.0318 26.2707L13.7402 23.9064C14.0984 22.9846 14.2789 22.222 14.2789 21.614V21.6149ZM9.61598 22.5944L7.48808 28.7764C8.12449 28.963 8.79557 29.0652 9.49243 29.0652C10.3173 29.0652 11.1101 28.9221 11.847 28.6635C11.8283 28.6333 11.8097 28.6004 11.7954 28.5657L9.61687 22.5944H9.61598ZM15.7135 18.5715C15.7446 18.7972 15.7615 19.039 15.7615 19.3012C15.7615 20.0203 15.6264 20.8292 15.2211 21.8398L13.0558 28.1017C15.1642 26.8724 16.581 24.589 16.581 21.9731C16.5828 20.7394 16.2672 19.5803 15.7135 18.5706V18.5715Z",fill:"#1863DC"}}),e("path",{attrs:{d:"M34.3651 5.63068H32.1039L34.1713 9.48562H36.4326L34.3651 5.63068Z",fill:"#1863DC"}}),e("path",{attrs:{d:"M34.1287 9.37915L34.1927 9.48759H36.4539L34.9678 6.78015L34.1278 9.37915H34.1287Z",fill:"#1863DC"}}),e("path",{attrs:{d:"M38.2636 2.3829L34.1936 9.48659H36.4548L40.5249 2.3829H38.2636Z",fill:"#1863DC"}}),e("path",{attrs:{d:"M34.1926 10.5484H36.3899V12.7794H34.1926V10.5484Z",fill:"#1863DC"}}),e("defs",[e("linearGradient",{attrs:{id:"paint0_linear_468_260",x1:"-2.36705",y1:"14.9864",x2:"24.3253",y2:"20.2349",gradientUnits:"userSpaceOnUse"}},[e("stop",{attrs:{"stop-color":"#99BCE3"}}),e("stop",{attrs:{offset:"1","stop-color":"#EDF5FF"}})],1)],1)])},re=[],ce={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},le=ce,ue=Object(l["a"])(le,ie,re,!1,null,null,null),de=ue.exports,pe=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M12 4.79999L16.8 12L22.8 7.19999L20.4 19.2H3.6L1.2 7.19999L7.2 12L12 4.79999Z",stroke:t.color,"stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}})])},he=[],ge={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},ye=ge,fe=Object(l["a"])(ye,pe,he,!1,null,null,null),me=fe.exports,ke=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 32 33",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M14.6667 25.8333C20.5577 25.8333 25.3333 21.0577 25.3333 15.1667C25.3333 9.27563 20.5577 4.5 14.6667 4.5C8.77563 4.5 4 9.27563 4 15.1667C4 21.0577 8.77563 25.8333 14.6667 25.8333Z",stroke:"#1160C6","stroke-width":"2"}}),e("path",{attrs:{d:"M28.0002 28.5002L22.2002 22.7002",stroke:"#1160C6","stroke-width":"2"}})])},be=[],Ce={props:{width:{type:String,default:"32px"},height:{type:String,default:"100%"}}},we=Ce,ve=Object(l["a"])(we,ke,be,!1,null,null,null),_e=ve.exports,xe=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 32 33",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{id:"Vector",d:"M16 3.5C8.832 3.5 3 9.332 3 16.5C3 23.668 8.832 29.5 16 29.5C23.168 29.5 29 23.668 29 16.5C29 9.332 23.168 3.5 16 3.5ZM16 5.5C22.065 5.5 27 10.435 27 16.5C27 22.565 22.065 27.5 16 27.5C9.935 27.5 5 22.565 5 16.5C5 10.435 9.935 5.5 16 5.5ZM14 9.5C13.7348 9.5 13.4804 9.60536 13.2929 9.79289C13.1054 9.98043 13 10.2348 13 10.5C13 10.7652 13.1054 11.0196 13.2929 11.2071C13.4804 11.3946 13.7348 11.5 14 11.5C14.2652 11.5 14.5196 11.3946 14.7071 11.2071C14.8946 11.0196 15 10.7652 15 10.5C15 10.2348 14.8946 9.98043 14.7071 9.79289C14.5196 9.60536 14.2652 9.5 14 9.5ZM19.5 10.5C19.1022 10.5 18.7206 10.658 18.4393 10.9393C18.158 11.2206 18 11.6022 18 12C18 12.3978 18.158 12.7794 18.4393 13.0607C18.7206 13.342 19.1022 13.5 19.5 13.5C19.8978 13.5 20.2794 13.342 20.5607 13.0607C20.842 12.7794 21 12.3978 21 12C21 11.6022 20.842 11.2206 20.5607 10.9393C20.2794 10.658 19.8978 10.5 19.5 10.5ZM11 13.5C10.4696 13.5 9.96086 13.7107 9.58579 14.0858C9.21071 14.4609 9 14.9696 9 15.5C9 16.0304 9.21071 16.5391 9.58579 16.9142C9.96086 17.2893 10.4696 17.5 11 17.5C11.5304 17.5 12.0391 17.2893 12.4142 16.9142C12.7893 16.5391 13 16.0304 13 15.5C13 14.9696 12.7893 14.4609 12.4142 14.0858C12.0391 13.7107 11.5304 13.5 11 13.5ZM17 15.5C16.7348 15.5 16.4804 15.6054 16.2929 15.7929C16.1054 15.9804 16 16.2348 16 16.5C16 16.7652 16.1054 17.0196 16.2929 17.2071C16.4804 17.3946 16.7348 17.5 17 17.5C17.2652 17.5 17.5196 17.3946 17.7071 17.2071C17.8946 17.0196 18 16.7652 18 16.5C18 16.2348 17.8946 15.9804 17.7071 15.7929C17.5196 15.6054 17.2652 15.5 17 15.5ZM22 16.5C21.7348 16.5 21.4804 16.6054 21.2929 16.7929C21.1054 16.9804 21 17.2348 21 17.5C21 17.7652 21.1054 18.0196 21.2929 18.2071C21.4804 18.3946 21.7348 18.5 22 18.5C22.2652 18.5 22.5196 18.3946 22.7071 18.2071C22.8946 18.0196 23 17.7652 23 17.5C23 17.2348 22.8946 16.9804 22.7071 16.7929C22.5196 16.6054 22.2652 16.5 22 16.5ZM12.5 19.5C12.1022 19.5 11.7206 19.658 11.4393 19.9393C11.158 20.2206 11 20.6022 11 21C11 21.3978 11.158 21.7794 11.4393 22.0607C11.7206 22.342 12.1022 22.5 12.5 22.5C12.8978 22.5 13.2794 22.342 13.5607 22.0607C13.842 21.7794 14 21.3978 14 21C14 20.6022 13.842 20.2206 13.5607 19.9393C13.2794 19.658 12.8978 19.5 12.5 19.5ZM19.5 20.5C19.1022 20.5 18.7206 20.658 18.4393 20.9393C18.158 21.2206 18 21.6022 18 22C18 22.3978 18.158 22.7794 18.4393 23.0607C18.7206 23.342 19.1022 23.5 19.5 23.5C19.8978 23.5 20.2794 23.342 20.5607 23.0607C20.842 22.7794 21 22.3978 21 22C21 21.6022 20.842 21.2206 20.5607 20.9393C20.2794 20.658 19.8978 20.5 19.5 20.5Z",fill:"#1C5C98"}})])},$e=[],Le={props:{width:{type:String,default:"32px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},Pe=Le,Se=Object(l["a"])(Pe,xe,$e,!1,null,null,null),Me=Se.exports,Oe=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 32 33",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M18.5442 5.8335H26.2775V13.8335H18.5442V5.8335ZM5.65527 19.1668H13.3886V27.1668H5.65527V19.1668Z",stroke:"#087357","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{d:"M22.4106 27.167C24.5461 27.167 26.2773 25.3761 26.2773 23.167C26.2773 20.9579 24.5461 19.167 22.4106 19.167C20.2751 19.167 18.5439 20.9579 18.5439 23.167C18.5439 25.3761 20.2751 27.167 22.4106 27.167Z",stroke:"#087357","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{d:"M9.52194 13.8335C11.6574 13.8335 13.3886 12.0426 13.3886 9.8335C13.3886 7.62436 11.6574 5.8335 9.52194 5.8335C7.38644 5.8335 5.65527 7.62436 5.65527 9.8335C5.65527 12.0426 7.38644 13.8335 9.52194 13.8335Z",stroke:"#087357","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}})])},je=[],Ae={props:{width:{type:String,default:"32px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},Be=Ae,Te=Object(l["a"])(Be,Oe,je,!1,null,null,null),He=Te.exports,Ie=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 33 33",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{id:"Vector",d:"M19.1663 3.1665H8.49967C7.79243 3.1665 7.11415 3.44746 6.61406 3.94755C6.11396 4.44765 5.83301 5.12593 5.83301 5.83317V27.1665C5.83301 27.8737 6.11396 28.552 6.61406 29.0521C7.11415 29.5522 7.79243 29.8332 8.49967 29.8332H24.4997C25.2069 29.8332 25.8852 29.5522 26.3853 29.0521C26.8854 28.552 27.1663 27.8737 27.1663 27.1665V11.1665L19.1663 3.1665Z",stroke:"#A6742A","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{id:"Vector_2",d:"M19.167 3.1665V11.1665H27.167",stroke:"#A6742A","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{id:"Vector_3",d:"M21.8337 17.8335H11.167",stroke:"#A6742A","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{id:"Vector_4",d:"M21.8337 23.1665H11.167",stroke:"#A6742A","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{id:"Vector_5",d:"M13.8337 12.5H12.5003H11.167",stroke:"#A6742A","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}})])},Ve=[],Ee={props:{width:{type:String,default:"32px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}}},Ne=Ee,De=Object(l["a"])(Ne,Ie,Ve,!1,null,null,null),Ze=De.exports,Ue=function(){var t=this,e=t._self._c;return e("svg",{attrs:{width:t.width,height:t.height,viewBox:"0 0 18 13",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[e("path",{attrs:{d:"M7.33301 6.50016C7.33301 6.94219 7.5086 7.36611 7.82116 7.67867C8.13372 7.99123 8.55765 8.16683 8.99967 8.16683C9.4417 8.16683 9.86562 7.99123 10.1782 7.67867C10.4907 7.36611 10.6663 6.94219 10.6663 6.50016C10.6663 6.05814 10.4907 5.63421 10.1782 5.32165C9.86562 5.00909 9.4417 4.8335 8.99967 4.8335C8.55765 4.8335 8.13372 5.00909 7.82116 5.32165C7.5086 5.63421 7.33301 6.05814 7.33301 6.50016Z",stroke:"#4E4B66","stroke-width":"1.33333","stroke-linecap":"round","stroke-linejoin":"round"}}),e("path",{attrs:{d:"M16.5 6.5C14.5 9.83333 12 11.5 9 11.5C6 11.5 3.5 9.83333 1.5 6.5C3.5 3.16667 6 1.5 9 1.5C12 1.5 14.5 3.16667 16.5 6.5Z",stroke:"#4E4B66","stroke-width":"1.33333","stroke-linecap":"round","stroke-linejoin":"round"}})])},Re=[],Fe={props:{width:{type:String,default:"18px"},height:{type:String,default:"100%"}}},Ge=Fe,qe=Object(l["a"])(Ge,Ue,Re,!1,null,null,null),ze=qe.exports,Ye={name:"CkyIcon",props:{icon:{type:String,default:"logo"},width:{type:String,default:"18px"},height:{type:String,default:"100%"},color:{type:String,default:"currentColor"}},computed:{getStyles(){return{height:this.height+"px",width:this.width+"px"}}},components:{logo:d,chat:m,help:_,rocket:M,blank:H,note:z,css:Z,layout:X,close:at,edit:dt,trash:mt,spinner:_t,editAlt:Mt,cookiePolicy:Ht,privacyPolicy:Zt,search:zt,general:Xt,successCircle:ae,connect:de,crown:me,pages:Ze,scan:_e,cookie:Me,categories:He,eye:ze}},We=Ye,Je=(n("3627"),Object(l["a"])(We,o,s,!1,null,"f59e890e",null));e["a"]=Je.exports},"22d4":function(t,e,n){},"23fe":function(t,e,n){},2946:function(t,e,n){},"2cf9":function(t,e,n){"use strict";n("6b1c")},"2dd4":function(t,e,n){},3627:function(t,e,n){"use strict";n("cc73")},3840:function(t,e,n){"use strict";var o=n("561c");o["setLocaleData"](window.ckyTranslations.translations,"cookie-law-info"),e["a"]=o},"42a8":function(t,e,n){t.exports=n.p+"img/star.svg"},"462b":function(t,e,n){"use strict";var o=function(){var t=this,e=t._self._c;return e("transition",{attrs:{name:"cky-notice-fade"}},[t.isShown?e("div",{class:t.noticeClass},[e("div",{staticClass:"cky-admin-notice-content"},[e("div",{staticClass:"cky-admin-notice-message"},[t.$slots.header?e("h5",{staticClass:"cky-admin-notice-header"},[t._t("header")],2):t._e(),t._t("default")],2)]),e("div",{staticClass:"cky-admin-notice-close"},[t.isDismissable?e("button",{staticClass:"cky-close",attrs:{type:"button","aria-label":"Close"},on:{click:t.close}},[e("span",{attrs:{"aria-hidden":"true"}},[t._v("×")])]):t._e()])]):t._e()])},s=[],a={name:"CkyNotice",props:{type:{type:String,default:"success"},isDismissable:{type:Boolean,default:!1},showIcon:{type:Boolean,default:!1}},data(){return{isShown:!0}},methods:{close(){this.$emit("onDismiss"),this.isShown&&(this.isShown=!1)}},computed:{noticeClass(){return{"cky-admin-notice":!0,"cky-admin-notice-info":"info"===this.type,"cky-admin-notice-error":"error"===this.type,"cky-admin-notice-success":"success"===this.type,"cky-admin-notice-warning":"warning"===this.type,"cky-admin-notice-default":"default"===this.type,"cky-admin-notice-has-icon":this.showIcon,"cky-admin-notice-dismissable":this.isDismissable}}}},i=a,r=n("2877"),c=Object(r["a"])(i,o,s,!1,null,null,null);e["a"]=c.exports},"482e":function(t,e,n){"use strict";n("8025")},"568d":function(t,e,n){var o={"./cky-button.vue":"5985","./cky-input.vue":"a07a","./cky-popper.vue":"7f7f"};function s(t){var e=a(t);return n(e)}function a(t){if(!n.o(o,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return o[t]}s.keys=function(){return Object.keys(o)},s.resolve=a,t.exports=s,s.id="568d"},"56d7":function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return Ee}));n("83f4");var o=n("2b0e"),s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cky-app-wrap",attrs:{id:"cky-app"}},[e("div",{staticClass:"cky-app-nav-bar"},[e("cky-notification"),e("cky-header"),t.$route.meta.hideNav?t._e():e("cky-nav-menu")],1),e("div",{class:t.bodyClass},[e("cky-table-missing-notice"),"dashboard"!==t.$route.name||t.connected?t._e():e("notice-promotion"),t.apiFailed?e("cky-notice",{staticClass:"cky-admin-notice-api-error",attrs:{type:"error",showIcon:!0}},[e("p",{domProps:{innerHTML:t._s(t.errorMessages.apiFetchFailed)}})]):t._e(),t._l(t.errors,(function(n,o){return e("cky-notice",{key:o,staticClass:"cky-admin-notice-api-error",attrs:{type:"error",showIcon:!0}},[e("p",{domProps:{innerHTML:t._s(t.errorMessages[o])}})])})),e("transition",{attrs:{name:"fade"}},[e("router-view")],1)],2)])},a=[],i=n("2f62"),r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cky-header"},[e("div",{staticClass:"cky-row cky-align-center"},[e("div",{staticClass:"cky-col-4"},[e("a",{attrs:{href:"https://www.cookieyes.com/"}},[e("cky-icon",{attrs:{width:"120px",icon:"logo",color:"#ffffff"}})],1)]),e("div",{staticClass:"cky-col-8"},[e("div",{staticClass:"cky-card-header-actions"},[e("a",{staticClass:"cky-link cky-align-center",attrs:{target:"_blank",href:"https://www.cookieyes.com/documentation/how-to-install-cookieyes-wordpress-plugin/"}},[e("cky-icon",{attrs:{width:"15px",icon:"help",color:"#ffffff"}}),t._v(" "+t._s(t.$i18n.__("Help Guides","cookie-law-info"))+" ")],1),e("a",{staticClass:"cky-link cky-align-center",attrs:{target:"_blank",href:"https://www.cookieyes.com/support/"}},[e("cky-icon",{attrs:{width:"15px",icon:"chat",color:"#ffffff"}}),t._v(" "+t._s(t.$i18n.__("Support","cookie-law-info"))+" ")],1)])])])])},c=[],l=n("1f3d"),u=n("c068"),d={name:"CkyHeader",mixins:[u["a"]],components:{CkyIcon:l["a"]},data(){return{syncing:!1}},computed:{account(){return this.getOption("account")}},created(){this.$root.$on("afterConnection",()=>{this.syncing=!0}),this.$root.$on("afterSyncing",async()=>{this.syncing=!1})}},p=d,h=(n("9373"),n("2877")),g=Object(h["a"])(p,r,c,!1,null,null,null),y=g.exports,f=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cky-nav-menu"},[e("div",{staticClass:"cky-row cky-align-center"},[e("div",{staticClass:"cky-col-7"},t._l(t.menus,(function(t,n){return e("cky-menu-item",{key:n,attrs:{data:t}})})),1),t.isSuspended?t._e():e("div",{staticClass:"cky-col-5 cky-upgrade-col"},[e("pageviews"),e("upgrade")],1)])])},m=[],k=function(){var t=this,e=t._self._c;return t.visible()?e("router-link",{staticClass:"cky-nav-menu-item",class:["cky-nav-menu-item",{}],attrs:{to:t.data.to}},[t._v(" "+t._s(t.data.text)+" ")]):t._e()},b=[],C={name:"CkyMenuItem",props:{data:Object},data(){return{syncing:!1}},methods:{visible:function(){return!!this.data.to&&!(!this.data.native&&this.account.connected||this.data.hidden&&!this.account.connected)}},computed:{account(){return this.getOption("account")}},mounted(){this.$root.$on("beforeConnection",()=>{this.syncing=!0}),this.$root.$on("afterSyncing",()=>{this.syncing=!1})}},w=C,v=(n("89d0"),Object(h["a"])(w,k,b,!1,null,null,null)),_=v.exports,x=function(){var t=this,e=t._self._c;return t.pageViewsEnabled?e("div",{staticClass:"cky-flex cky-justify-end cky-direction--column cky-text-right"},[e("p",{staticClass:"cky-text-sm cky-text-secondary-dark"},[t._v(" "+t._s(t.$i18n.__("Current plan:","cookie-law-info"))+" "),e("b",[t._v(t._s(t.info.plan.name))]),t.info.website.is_trial?e("span",[e("b",[t._v(" "+t._s(t.$i18n.__("(Trial)","cookie-law-info"))+" ")]),e("span",{staticClass:"cky-color-red"},[t._v(" "+t._s(t.$i18n.__("Expires in","cookie-law-info"))+" "),e("b",[t._v(t._s(t.remainingDays))]),t._v(" "+t._s(t.$i18n.__("days","cookie-law-info"))+" ")])]):t._e()]),e("p",{staticClass:"cky-text-xs cky-text-secondary-light"},[t.pageViews.limit?e("span",[t._v(" "+t._s(t.formatNumber(t.pageViews.count))+"/"+t._s(t.formatNumber(t.pageViews.limit))+" ("+t._s(t.pageviewsPercentage)+"%) "+t._s(t.$i18n.__("pageviews used","cookie-law-info"))+" ")]):e("span",[t._v(" "+t._s(t.formatNumber(t.pageViews.count))+" "+t._s(t.$i18n.__("pageviews used","cookie-law-info"))+" ")]),e("cky-popper",{attrs:{position:t.position,ishtml:!0,content:t.contents.pageview}})],1),e("p")]):t._e()},$=[],L={name:"Pageviews",props:{},data(){return{position:"bottom"}},methods:{formatNumber(t){return t.toLocaleString("en-US")}},computed:{info(){return this.getInfo()},pageViews(){return this.info.pageviews},pageviewsPercentage(){return this.pageViews?Math.round(100*this.pageViews.count/this.pageViews.limit):0},pageViewsEnabled(){return!!this.info&&!!this.info.pageviews},contents(){return{pageview:this.$i18n.sprintf(this.$i18n.__('Pageviews will reset on<br><b>%1$s</b>.<br><a class="cky-external-link" href="%2$s" target="_blank">Learn more</a>',"cookie-law-info"),this.pageViews.ends_at,"https://www.cookieyes.com/documentation/pageviews-info/")}},remainingDays(){if(!this.info.pageviews.ends_at)return 0;const t=new Date(this.info.pageviews.ends_at),e=new Date,n=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate())),o=t-n,s=Math.ceil(o/864e5);return Math.max(0,s)}}},P=L,S=Object(h["a"])(P,x,$,!1,null,null,null),M=S.exports,O=function(){var t=this,e=t._self._c;return t.account.connected?e("div",[e("a",{staticClass:"cky-button cky-button-small cky-button-upgrade cky-button-icon cky-center",style:{width:t.buttonWidth},attrs:{href:t.getURL(),target:"_blank"}},["ultimate"!==t.plan.toLowerCase()?e("cky-icon",{attrs:{icon:"crown",width:"16"}}):t._e(),t._v(" "+t._s(t.canStartOptoutTrial?t.$i18n.__("Try Pro for free","cookie-law-info"):t.$i18n.__("Upgrade","cookie-law-info"))+" ")],1)]):t._e()},j=[],A={name:"Upgrade",components:{CkyIcon:l["a"]},methods:{getURL(){return"ultimate"===this.plan.toLowerCase()?"https://www.cookieyes.com/support/?query=enterprise&ref=cypluginupgrade#enterprise":`${window.ckyGlobals.webApp.url}/settings?upgrade_id=${this.account.website_id}&openUpgrade=true&upgrade_source=cypluginupgrade`}},computed:{account(){return this.getOption("account")},plan(){return!!this.getInfo("plan")&&this.getInfo("plan").name||"free"},...Object(i["d"])("settings",["info"]),canStartOptoutTrial(){return this.info&&this.info.website&&this.info.website.canStartOptoutTrial},buttonWidth(){return this.canStartOptoutTrial?"167px":"107px"}}},B=A,T=(n("08f7"),Object(h["a"])(B,O,j,!1,null,null,null)),H=T.exports,I={name:"CkyNavMenu",components:{CkyMenuItem:_,Pageviews:M,Upgrade:H},data(){return{menus:[{text:this.$i18n.__("Dashboard","cookie-law-info"),to:{name:"dashboard"},native:!0},{text:this.$i18n.__("Cookie Banner","cookie-law-info"),to:{name:"customize"}},{text:this.$i18n.__("Cookie Manager","cookie-law-info"),to:{name:"cookies"}},{text:this.$i18n.__("Languages","cookie-law-info"),to:{name:"languages"}},{text:this.$i18n.__("Policy Generators","cookie-law-info"),to:{name:"policies"}},{text:this.$i18n.__("Google Consent Mode (GCM)","cookie-law-info"),to:{name:"gcm"},native:!0,hidden:!0},{text:this.$i18n.__("Site Settings","cookie-law-info"),to:{name:"settings"},native:!0,hidden:!0}]}},computed:{isSuspended(){return this.getInfo("website")&&"suspended"===this.getInfo("website").status}}},V=I,E=(n("090a"),Object(h["a"])(V,f,m,!1,null,null,null)),N=E.exports,D=n("462b"),Z=function(){var t=this,e=t._self._c;return e("transition",{attrs:{name:"cky-slide-top"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.message,expression:"message"}],class:t.notificationClass,attrs:{role:"alert",id:t.id,"aria-live":"assertive"}},[e("div",{staticClass:"cky-notification-content"},[e("p",[e("span",{domProps:{innerHTML:t._s(t.message)}})])]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.options.dismiss.show,expression:"options.dismiss.show"}],staticClass:"cky-notification-close"},[e("button",{staticClass:"cky-close",attrs:{type:"button","aria-label":"Close"},on:{click:t.close}},[e("span",{attrs:{"aria-hidden":"true"}},[t._v("×")])])])])])},U=[],R=n("a2b6"),F={name:"CkyNotification",data(){return{id:"cky-toast-notification",message:"",type:"success",callback:!1,options:{type:"success",autoclose:{show:!0,timeout:5e3},dismiss:{show:!1}}}},mounted(){this.$root.$on("triggerNotification",t=>{this.setupNotice(t)})},computed:{notificationClass(){return{"cky-notification":!0,"cky-notification-info":"info"===this.type,"cky-notification-error":"error"===this.type,"cky-notification-success":"success"===this.type,"cky-notification-warning":"warning"===this.type}}},methods:{getMessage(t){return t.message||""},async setupNotice(t){this.type=t.type||"success",this.options.type=this.type,this.options.dismiss.show=t.dismiss||!1,this.options.autoclose.show=!this.options.dismiss.show,this.message=this.getMessage(t),this.options.autoclose.show&&(await Object(R["d"])(this.options.autoclose.timeout),this.message=!1,"function"==typeof this.callback&&this.callback())},close(){this.message=!1}}},G=F,q=(n("7f2b"),Object(h["a"])(G,Z,U,!1,null,"b1fe306c",null)),z=q.exports,Y=function(){var t=this,e=t._self._c;return t.tablesMissing?e("cky-notice",{staticStyle:{"margin-top":"20px","margin-bottom":"-10px"},attrs:{type:"error"}},[e("div",{staticClass:"cky-align-center"},[e("p",{staticStyle:{margin:"0"}},[e("strong",[t._v(" "+t._s(t.$i18n.__("Missing database tables: Some features may be disabled or not work as expected since one or more required tables are missing from the database.","cookie-law-info"))+" ")])]),e("cky-button",{ref:"ckyButtonreInstallTables",staticClass:"cky-button",staticStyle:{"margin-left":"25px"},nativeOn:{click:function(e){return t.reInstallTables.apply(null,arguments)}}},[e("template",{slot:"loader"},[t._v(t._s(t.$i18n.__("Checking...","cookie-law-info"))+" ")]),t._v(" "+t._s(t.$i18n.__("Check again","cookie-law-info"))+" ")],2)],1)]):t._e()},W=[],J=n("f9c4"),Q={name:"CkyTableMissingNotice",components:{CkyNotice:D["a"]},data(){return{}},methods:{async reInstallTables(){try{this.$refs.ckyButtonreInstallTables.startLoading();const t=await J["a"].post({path:"/settings/reinstall"});!0===t.success&&window.location.reload()}catch(t){console.log(t)}}},computed:{...Object(i["d"])("settings",["info"]),tablesMissing(){return!!this.info.tables_missing}}},K=Q,X=Object(h["a"])(K,Y,W,!1,null,null,null),tt=X.exports,et=function(){var t=this,e=t._self._c;return t.show?e("div",{staticClass:"cky-promo-notice-bar"},[e("div",{staticClass:"cky-promo-notice-content"},[t._m(0),t._m(1),e("div",{staticClass:"cky-promo-notice-actions"},[e("button",{staticClass:"cky-button cky-promo-notice-btn",on:{click:t.onbtnClick}},[t._v("Install free widget now")]),e("button",{staticClass:"cky-promo-notice-close",on:{click:t.dismissNotice}},[t._v("×")])])])]):t._e()},nt=[function(){var t=this,e=t._self._c;return e("span",{staticClass:"cky-promo-notice-icon"},[e("span",{staticClass:"cky-promo-notice-icon-inner"},[e("img",{attrs:{src:n("6a11"),alt:"Widget Icon",width:"57",height:"57"}})])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"cky-promo-notice-title-desc"},[e("div",{staticClass:"cky-promo-notice-title-row"},[e("span",{staticClass:"cky-promo-notice-title"},[t._v("Make your site more accessible for users")]),e("span",{staticClass:"cky-promo-notice-divider"}),e("span",{staticClass:"cky-promo-notice-deadline"},[t._v("Deadline: June 28, 2025")])]),e("div",{staticClass:"cky-row"},[e("div",{staticClass:"cky-col-12"},[e("span",{staticClass:"cky-promo-notice-desc"},[t._v(" The European Accessibility Act (EAA) sets new rules for EU websites. Use our widget to make your site more accessible! ")])])])])}],ot={name:"NoticePromotion",data(){return{show:this.shouldShow()}},methods:{shouldShow(){return!!window.ckyPromoNotices&&!!window.ckyPromoNotices.notice_banner},async dismissNotice(){try{await J["a"].post({path:"/settings/promo-notices/notice_banner",data:{expiry:0}}),window.ckyPromoNotices&&window.ckyPromoNotices.notice_banner&&delete window.ckyPromoNotices.notice_banner,this.show=!1}catch(t){console.error("Failed to dismiss promo notice:",t)}},onbtnClick(){window.open("https://wordpress.org/plugins/accessibility-widget/","_blank","noopener,noreferrer")}}},st=ot,at=(n("8a08"),Object(h["a"])(st,et,nt,!1,null,null,null)),it=at.exports,rt={name:"CkyApp",components:{CkyHeader:y,CkyNavMenu:N,CkyNotice:D["a"],CkyNotification:z,CkyTableMissingNotice:tt,NoticePromotion:it},data(){return{loading:!0,errorMessages:{apiFetchFailed:this.$i18n.sprintf(this.$i18n.__('Unable to reach your web app account at the moment. Please reload the page to retry. If the issue persists, check out the <a href="%1$s" target="_blank">common issues causing this error</a> and try applying the suggested solutions.',"cookie-law-info"),"https://www.cookieyes.com/documentation/cy-wordpress-plugin-troubleshooting-guide/"),urlMismatch:this.$i18n.sprintf(this.$i18n.__('Looks like your website URL has changed. To ensure the proper functioning of your banner, update the registered URL on your CookieYes account (navigate to the <a href="%1$s" target="_blank">Organisations & Sites</a> page and click the More button associated with your site). Then, reload this page to retry. If the issue persists, please  <a href="%2$s" target="_blank">contact us</a>.',"cookie-law-info"),this.$router.getAppRedirectURL("settings/organizations-and-sites"),"https://www.cookieyes.com/support/")}}},computed:{apiFailed(){return!this.$store.state.settings.status},cardLoader(){return!(!1!==this.$store.state.settings.info&&!this.loading)},errors(){return this.$store.state.settings.errors},hasErrors(){return!!Object.keys(this.$store.state.settings.errors).length},bodyClass(){return this.$route.meta.hideNav?"cky-app-body-without-nav":"cky-app-body"},account(){return this.getOption&&this.getOption("account")||{}},connected(){return!!this.account.connected}},methods:{...Object(i["b"])("settings",["loadInfo","reInit"]),...Object(i["b"])("languages",["loadLanguages"])},async created(){try{await this.loadLanguages(),await this.loadInfo(!0),await this.reInit()}catch(t){this.$root.$emit("triggerNotification",{type:"error",message:this.$i18n.__("An unexpected error occurred please try reloading the page or logging in again.","cookie-law-info")})}}},ct=rt,lt=(n("2cf9"),Object(h["a"])(ct,s,a,!1,null,null,null)),ut=lt.exports,dt=function(){var t=this,e=t._self._c;return e("current-component")},pt=[],ht={components:{CurrentComponent:()=>n.e("chunk-2b7b6b2b").then(n.bind(null,"9573"))}},gt=ht,yt=Object(h["a"])(gt,dt,pt,!1,null,null,null),ft=yt.exports,mt=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cky-section cky-connect-section cky-zero-padding cky-zero--margin"},[e("div",{staticClass:"cky-row"},[e("div",{staticClass:"cky-col-12"},[e("cky-connect-success")],1)]),e("div",{staticClass:"cky-section-header cky-align-center cky-justify-between"}),e("div",{staticClass:"cky-row"},[e("div",{staticClass:"cky-col-1 cky-justify-end cky-zero-padding"},[e("button",{staticClass:"cky-back-button",on:{click:function(e){return t.$router.go(-1)}}})]),e("div",{staticClass:"cky-col-10"},[e("div",{staticClass:"cky-section-content"},[e("cky-card",{attrs:{loading:t.cardLoader},scopedSlots:t._u([{key:"body",fn:function(){return[e("div",{staticClass:"cky-section-plans"},[e("div",{staticClass:"cky-section-row"},[e("h3",{staticClass:"cky-plans-heading"},[t._v(" "+t._s(t.$i18n.__("Create a new account and connect to web app","cookie-law-info"))+" ")]),e("div",{staticClass:"cky-button-group"},[e("button",{class:["cky-button",t.showMonthly?"cky-active":""],on:{click:t.changeActive}},[t._v(" "+t._s(t.$i18n.__("Monthly","cookie-law-info"))+" ")]),e("button",{class:["cky-button",t.showMonthly?"":"cky-active"],on:{click:t.changeActive}},[e("div",{staticClass:"cky-get-free-label"},[t._v(" "+t._s(t.$i18n.__("2 months free","cookie-law-info"))+" ")]),t._v(" "+t._s(t.$i18n.__("Annually","cookie-law-info"))+" ")])]),t.loaded?e("div",{staticClass:"cky-currency"},[e("select",{directives:[{name:"model",rawName:"v-model",value:t.selected,expression:"selected"}],staticClass:"cky-select",on:{change:[function(e){var n=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.selected=e.target.multiple?n:n[0]},function(e){return t.onChange()}]}},t._l(t.currencies,(function(n){return e("option",{key:n.code,domProps:{value:n.code}},[t._v(" "+t._s(n.symbol+" "+n.code)+" ")])})),0)]):t._e()]),t.loading?t._e():e("div",{staticClass:"cky-card-row"},[e("div",{staticClass:"cky-info-widget-container",class:t.dynamicClass},t._l(t.plans,(function(o){return e("div",{key:o.slug,staticClass:"cky-info-widget"},[e("div",{staticClass:"cky-plan-pricing"},[e("div",{staticClass:"cky-widget-row"},[e("h3",{staticClass:"cky-plan-name"},[t._v(" "+t._s(o.name)+" ")]),"pro"===o.slug?e("div",{staticClass:"cky-popular"},[e("img",{attrs:{src:n("42a8")}}),e("p",[t._v(" "+t._s(t.$i18n.__("POPULAR","cookie-law-info"))+" ")])]):t._e()]),e("p",{staticClass:"cky-widget-desc"},[t._v(" "+t._s(o.description)+" ")]),e("div",{staticClass:"cky-widget-row"},["free"===o.slug?e("h1",{staticClass:"cky-price"},[t._v(" "+t._s(t.symbol+"0")+" ")]):e("h1",{staticClass:"cky-price"},[t._v(" "+t._s(t.symbol+t.getPrice(o.slug))+" ")]),"free"===o.slug?e("p",[t._v(" "+t._s(t.$i18n.__("Free forever","cookie-law-info"))+" ")]):t.showMonthly?e("p",[t._v(" "+t._s(t.contents.showMonthly)+" ")]):e("p",[t._v(" "+t._s(t.contents.showYearly)+" ")])]),e("cky-button",{nativeOn:{click:function(e){return t.connectToWebapp(o.slug)}}},[t._v(" "+t._s("free"===o.slug?t.$i18n.__("Get started","cookie-law-info"):t.$i18n.__("Start free trial","cookie-law-info"))+" ")])],1),e("div",{staticClass:"cky-plan-features"},[e("p",[e("b",[t._v(" "+t._s(t.$i18n.__("Usage","cookie-law-info"))+" ")])]),e("div",{staticClass:"cky-usage-list"},[e("ul",t._l(t.usageFeatures(o.slug),(function(n,o){return e("li",{key:o},[e("span",[e("span",{domProps:{innerHTML:t._s(t.getContent(o,n))}}),n.showPopper?e("cky-popper",{attrs:{ishtml:!0,content:n.popperContent}}):t._e()],1)])})),0)]),e("p",[e("b",[t._v(" "+t._s(t.getHeading(o.slug))+" ")])]),e("div",{staticClass:"cky-features-list"},[e("ul",[t._l(t.planFeatureArrays[o.slug].slice(0,4),(function([n,o],s){return e("li",{key:s},[e("span",[t._v(" "+t._s(t.getContent(n,o))+" "),o.showPopper?e("cky-popper",{attrs:{ishtml:!0,content:o.popperContent}}):t._e()],1)])})),t._l(t.planFeatureArrays[o.slug].slice(4),(function([n,o],s){return e("li",{directives:[{name:"show",rawName:"v-show",value:t.toggleSettings,expression:"toggleSettings"}],key:"extra-"+s},[e("span",[t._v(" "+t._s(t.getContent(n,o))+" "),o.showPopper?e("cky-popper",{attrs:{ishtml:!0,content:o.popperContent}}):t._e()],1)])}))],2)])])])})),0)]),e("div",{staticClass:"cky-card-row cky-card-accordion"},[e("label",{staticClass:"cky-app-accordion-title",class:{rotated:t.toggleSettings},on:{click:t.toggleAdvanced}},[t.toggleSettings?[t._v(t._s(t.$i18n.__("See less","cookie-law-info")))]:[t._v(t._s(t.$i18n.__("See all benefits","cookie-law-info")))]],2)]),e("hr"),e("div",{staticClass:"cky-card-row cky-card-moneyback"},[e("img",{attrs:{src:n("dcb3"),alt:""}}),e("p",[t._v(" "+t._s(t.$i18n.__("15-day money-back guarantee","cookie-law-info"))+" ")])])])]},proxy:!0}])})],1)])])])},kt=[],bt=n("9610"),Ct=n("919d"),wt={mixins:[u["a"]],components:{ckyCard:bt["a"],CkyConnectSuccess:Ct["a"]},data(){return{loading:!1,loaded:!1,toggleSettings:!1,showMonthly:!0,currencies:[],planDetails:[],selected:"USD",symbol:"$",available:"all",contents:{showMonthly:this.$i18n.__("/month/domain","cookie-law-info"),showYearly:this.$i18n.__("/year/domain","cookie-law-info"),custom_css:this.$i18n.__("Custom CSS","cookie-law-info"),custom_branding:this.$i18n.__("Option to add custom logo on banner","cookie-law-info"),schedule_scan:this.$i18n.__("Scheduled scan","cookie-law-info"),static_ip_scan:this.$i18n.__("Static IP scan","cookie-law-info"),config_geo_rules:this.$i18n.__("Geo-target banner","cookie-law-info"),popup_layout:this.$i18n.__("Pop-up banner layout","cookie-law-info"),respect_gpc:this.$i18n.__("Support for Global Privacy Control (GPC)","cookie-law-info"),hide_banner_on_specific_pages:this.$i18n.__("Disable banner on specific pages","cookie-law-info"),iab:this.$i18n.__("IAB TCF Support","cookie-law-info"),gacm_support:this.$i18n.__("Google's Additional Consent Mode","cookie-law-info"),revisit_custom_branding:this.$i18n.__("Custom revisit consent button","cookie-law-info"),scan_behind_login:this.$i18n.__("Scan behind login","cookie-law-info"),remove_powered_by:this.$i18n.__("Disable “Powered by” branding","cookie-law-info"),pages_per_scan:this.$i18n.__("&nbsp;pages per scan","cookie-law-info"),pageview_per_month:this.$i18n.__("&nbsp;pageviews / month","cookie-law-info"),unlimited_pageviews:this.$i18n.__("<b>Unlimited</b>&nbsp;pageviews","cookie-law-info"),staging_mode:this.$i18n.__("Staging mode","cookie-law-info"),subdomain_consent_sharing:this.$i18n.__("Subdomain consent sharing","cookie-law-info"),multi_user_management:this.$i18n.__("Multi-user management","cookie-law-info"),gcm_support:this.$i18n.__("Support for Google Consent Mode (GCM) v2","cookie-law-info"),policy_generators:this.$i18n.__("Cookie/Privacy Policy Generators","cookie-law-info"),renew_user_consent:this.$i18n.__("Renew user consents","cookie-law-info"),do_not_track_support:this.$i18n.__("Do Not Track (DNT) support","cookie-law-info"),"2fa":this.$i18n.__("Two-factor authentication (2FA)","cookie-law-info"),automatic_cookie_blocking:this.$i18n.__("Automatic cookie blocking","cookie-law-info"),language_limit:this.$i18n.__("Multilingual banner","cookie-law-info"),unlimited_scans:this.$i18n.__("<b>Unlimited</b>&nbsp;cookie scans","cookie-law-info"),scans_per_month:this.$i18n.__("&nbsp;cookie scans / month","cookie-law-info"),chat_support:this.$i18n.__("Chat support","cookie-law-info"),opt_in_banner_for_gdpr:this.$i18n.__("Opt-in consent banner for GDPR","cookie-law-info"),opt_out_banner_for_us_state_laws:this.$i18n.__("Opt-out banner for US State laws","cookie-law-info"),per_category_consent:this.$i18n.__("Per-category consent","cookie-law-info"),revisit_consent:this.$i18n.__("Revisit consent button","cookie-law-info"),advanced_customisation:this.$i18n.__("Advanced customisation","cookie-law-info"),basic_customisation:this.$i18n.__("Basic customisation","cookie-law-info")},usageList:["scans_per_month","pages_per_scan","page_view_limit"]}},methods:{toggleAdvanced(){this.toggleSettings=!this.toggleSettings},changeActive(){this.showMonthly=!this.showMonthly},onChange(){this.currencies.forEach(t=>{t.code===this.selected&&(this.symbol=t.symbol)})},getFeatures(t){const e={...this.planDetails.plan[t].features},n={staging_mode:this.$i18n.__("Allows you to add your staging site URL to the main site, enabling you to set up and test the cookie banner in your staging environment.","cookie-law-info"),automatic_cookie_blocking:this.$i18n.__("Automatically scan, detect and block third-party cookie scripts until your site visitors give consent.","cookie-law-info"),scan_behind_login:this.$i18n.__('To scan your web page behind a login or a cookie wall, <a href="https://www.cookieyes.com/support" target="_blank">contact us</a> and provide your <b>login URL</b> along with the <b>test login credentials</b>. This will enable us to configure our scanner to scan behind your login pages.',"cookie-law-info"),gacm_support:this.$i18n.__("Google's Additional Consent (AC) Mode allows the collection of consents for Google's Ad Technology Providers (ATPs) that are not yet registered on the IAB Europe Global Vendor List (GVL). <b>AC Mode is intended only for use alongside IAB TCF.</b>","cookie-law-info"),remove_powered_by:this.$i18n.__("Remove CookieYes logo from your cookie banner.","cookie-law-info"),custom_css:this.$i18n.__("Add CSS code and customise the way your cookie banner looks.","cookie-law-info"),config_geo_rules:this.$i18n.__("Geo-target your cookie banner and display it as per your visitor's location.","cookie-law-info"),static_ip_scan:this.$i18n.__('If your website\'s firewall or configuration restricts external access, enable static IP scan for your site, ensuring that the scan requests consistently originate from the same IP. Additionally, <a href="https://www.cookieyes.com/documentation/whitelisting-cookieyes-scanner/" target="_blank">whitelist the static IP addresses</a> of the CookieYes scanner.',"cookie-law-info"),revisit_custom_branding:this.$i18n.__("Replace the default icon of the revisit consent button with your custom icon.","cookie-law-info"),revisit_consent:this.$i18n.__("Displays a floating button on your website allowing your visitors to withdraw or change their consent at any time.","cookie-law-info"),respect_gpc:this.$i18n.__("Global Privacy Control (GPC) is a web standard (necessary for CCPA/CPRA compliance) that allows users to signal to websites that they do not want their data to be collected or shared. If enabled, CookieYes will respect your user’s GPC setting.","cookie-law-info"),renew_user_consent:this.$i18n.__("This action will trigger the cookie banner to reappear for all existing users who have already given consent.","cookie-law-info"),subdomain_consent_sharing:this.$i18n.__("Allows you to share your visitor consent information among different subdomains of your website.","cookie-law-info"),do_not_track_support:this.$i18n.__("DNT is a web browser setting that enables you to opt-out of tracking by websites. CookieYes will respect your visitor’s DNT setting.","cookie-law-info"),page_view_limit:this.$i18n.__("A pageview is every instance that a page on a website with CookieYes code is loaded or reloaded.","cookie-law-info"),pages_per_scan:this.$i18n.__("The number of pages of your website that will be scanned.","cookie-law-info")},o={};for(const[s,a]of Object.entries(e))("object"===typeof a&&!1===a.value||!1===a||void 0===this.getContent(s,a))&&"advanced_customisation"!==s||("advanced_customisation"!==s||!1!==a?o[s]=s in n?{value:a,showPopper:!0,popperContent:n[s]}:a:o["basic_customisation"]={value:!0});return o},allFeatures(t){const e=this.getFeatures(t);return Object.fromEntries(Object.entries(e).filter(([t])=>!this.usageList.includes(t)))},getContent(t,e){return"object"===typeof e&&null!==e&&(e=e.value),"pages_per_scan"===t?`<b>${e}</b>`+this.contents.pages_per_scan:"page_view_limit"===t?0===e?this.contents.unlimited_pageviews:`<b>${e.toLocaleString("en-US")}</b>`+this.contents.pageview_per_month:"language_limit"===t?1!==e?this.contents.language_limit:void 0:"scans_per_month"===t?e>0?`<b>${e}</b>`+this.contents.scans_per_month:this.contents.unlimited_scans:this.contents[t]},getHeading(t){switch(t){case"free":return this.$i18n.__("Key features in Free","cookie-law-info");case"basic":return this.plans.some(t=>"free"===t.slug)?this.$i18n.__("Everything in Free, plus","cookie-law-info"):this.$i18n.__("Key features in Basic","cookie-law-info");case"pro":return this.plans.some(t=>"basic"===t.slug)?this.$i18n.__("Everything in Basic, plus","cookie-law-info"):this.$i18n.__("Key features in Pro","cookie-law-info");case"ultimate":return this.plans.some(t=>"pro"===t.slug)?this.$i18n.__("Everything in Pro, plus","cookie-law-info"):this.$i18n.__("Key features in Ultimate","cookie-law-info")}},getPrice(t){return this.showMonthly?this.planDetails.plan[t]["monthly"][this.selected]:this.planDetails.plan[t]["yearly"][this.selected]},getPageViews(t){return this.planDetails.plan[t]["features"]["page_view_limit"]},updateStatus(t=!0){this.$store.state.settings.status=t},async getCurrencyData(){let t=[];try{const e=await J["a"].get({path:"dashboard/currencies"});if(t=e,t.length<=0)return void this.updateStatus(!1);t&&(this.currencies=t)}catch(e){console.error(e)}this.loaded=!0},async getPlanData(){this.loading=!0;let t=[];try{const e=await J["a"].get({path:"dashboard/plans"});if(t=e,Object.keys(t).length<=0)return this.updateStatus(!1),void(this.loading=!1);t&&(this.planDetails=t)}catch(e){console.error(e)}this.loading=!1},connectToWebapp(t){const e=["basic","pro","ultimate"];"free"===t?this.connectToApp():e.includes(t)&&(this.showMonthly?t+="-monthly":t+="-yearly",this.connectToPaidPlan(t,this.selected))},usageFeatures(t){return Object.fromEntries(Object.entries(this.getFeatures(t)).filter(([t])=>this.usageList.includes(t)))}},beforeRouteLeave(t,e,n){this.updateStatus(),n()},computed:{...Object(i["d"])("settings",["info"]),cardLoader(){return!this.info||this.loading},plans(){const t=[{slug:"free",name:"Free",description:this.$i18n.__("For blogs and personal websites","cookie-law-info")},{slug:"basic",name:"Basic",description:this.$i18n.__("For small business and startups","cookie-law-info")},{slug:"pro",name:"Pro",description:this.$i18n.__("For medium business with growing traffic","cookie-law-info")},{slug:"ultimate",name:"Ultimate",description:this.$i18n.__("For large business with high traffic","cookie-law-info")}];switch(this.available){case"premium":return t.slice(1);case"pro":return t.slice(2);case"ultimate":return t.slice(3);case"all":default:return t}},planFeatureArrays(){return Object.fromEntries(Object.keys(this.planFeatures).map(t=>[t,Object.entries(this.planFeatures[t])]))},dynamicClass(){const t=this.plans.length;return"cky-info-items-"+t},planFeatures(){const t={};let e={};return this.plans.forEach(n=>{const o=this.allFeatures(n.slug),s=Object.fromEntries(Object.entries(o).filter(([t])=>!(t in e)));t[n.slug]=s,e={...e,...o}}),t}},created(){this.getPlanData(),this.getCurrencyData(),this.available=this.$route.query&&this.$route.query.available?this.$route.query.available:"all"}},vt=wt,_t=Object(h["a"])(vt,mt,kt,!1,null,null,null),xt=_t.exports,$t=function(){var t=this,e=t._self._c;return e("div",[e("current-component",{ref:"customize"}),e("exit-intent-popup",{ref:"exitIntentPopup"})],1)},Lt=[],Pt=function(){var t=this,e=t._self._c;return e("cky-modal",{ref:"exitIntentPopup",staticClass:"cky-exit-popup",scopedSlots:t._u([{key:"header",fn:function(){return[e("h4",[t._v(" "+t._s(t.$i18n.__("Leave page?","cookie-law-info"))+" ")])]},proxy:!0},{key:"body",fn:function(){return[e("p",[t._v(" "+t._s(t.$i18n.__("You’ve made changes that haven’t been published yet.","cookie-law-info"))+" ")])]},proxy:!0},{key:"footer",fn:function(){return[e("div",{staticClass:"cky-app-modal-actions cky-justify-end"},[e("button",{staticClass:"cky-button cky-button-outline-secondary",on:{click:t.discardChanges}},[t._v(" "+t._s(t.$i18n.__("Discard changes & leave this page","cookie-law-info"))+" ")]),e("cky-button",{staticClass:"cky-button-primary",on:{click:t.stayOnPage}},[t._v(" "+t._s(t.$i18n.__("Stay on page","cookie-law-info"))+" ")])],1)]},proxy:!0}])})},St=[],Mt=n("8a80"),Ot={name:"exitIntentPopup",components:{CkyModal:Mt["a"]},methods:{discardChanges(){this.$refs.exitIntentPopup.close(),this.$emit("discardChanges")},stayOnPage(){this.$refs.exitIntentPopup.close(),this.$emit("stayOnPage")},show(){this.$refs.exitIntentPopup.show()}}},jt=Ot,At=Object(h["a"])(jt,Pt,St,!1,null,null,null),Bt=At.exports,Tt={components:{CurrentComponent:()=>n.e("chunk-6bd81dbc").then(n.bind(null,"3636")),exitIntentPopup:Bt},async beforeRouteLeave(t,e,n){if(!this.$refs.customize.hasChanges())return n();this.$refs.exitIntentPopup.show();const o=await new Promise(t=>{this.$refs.exitIntentPopup.$on("stayOnPage",()=>{t(!1)}),this.$refs.exitIntentPopup.$on("discardChanges",()=>{t(!0)})});n(!!o)}},Ht=Tt,It=Object(h["a"])(Ht,$t,Lt,!1,null,null,null),Vt=It.exports,Et=function(){var t=this,e=t._self._c;return e("current-component")},Nt=[],Dt={components:{CurrentComponent:()=>n.e("chunk-2723f679").then(n.bind(null,"943d"))}},Zt=Dt,Ut=Object(h["a"])(Zt,Et,Nt,!1,null,null,null),Rt=Ut.exports,Ft=function(){var t=this,e=t._self._c;return e("current-component")},Gt=[],qt={components:{CurrentComponent:()=>n.e("chunk-61540af4").then(n.bind(null,"281e"))}},zt=qt,Yt=Object(h["a"])(zt,Ft,Gt,!1,null,null,null),Wt=Yt.exports,Jt=function(){var t=this,e=t._self._c;return e("div",[e("current-component",{ref:"gcm"}),e("exit-intent-popup",{ref:"exitIntentPopup"})],1)},Qt=[],Kt={components:{CurrentComponent:()=>n.e("chunk-bac749f6").then(n.bind(null,"955e")),exitIntentPopup:Bt},async beforeRouteLeave(t,e,n){if(!this.$refs.gcm.hasChanges())return n();this.$refs.exitIntentPopup.show();const o=await new Promise(t=>{this.$refs.exitIntentPopup.$on("stayOnPage",()=>{t(!1)}),this.$refs.exitIntentPopup.$on("discardChanges",()=>{this.$refs.gcm.resetChanges(),t(!0)})});n(!!o)}},Xt=Kt,te=Object(h["a"])(Xt,Jt,Qt,!1,null,null,null),ee=te.exports,ne=function(){var t=this,e=t._self._c;return e("current-component")},oe=[],se={components:{CurrentComponent:()=>n.e("chunk-389f4675").then(n.bind(null,"ea26"))}},ae=se,ie=Object(h["a"])(ae,ne,oe,!1,null,null,null),re=ie.exports,ce=function(){var t=this,e=t._self._c;return e("current-component")},le=[],ue={components:{CurrentComponent:()=>n.e("chunk-766366b8").then(n.bind(null,"ad98"))}},de=ue,pe=Object(h["a"])(de,ce,le,!1,null,null,null),he=pe.exports,ge=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cky-section cky-zero-padding cky-zero--margin"},[e("div",{staticClass:"cky-section-header cky-align-center cky-justify-between"},[e("div",{staticClass:"cky-section-title"},[e("router-link",{staticClass:"cky-breadcrumbs-title",attrs:{to:"/languages"}},[t._v(t._s(t.$i18n.__("Back to Language List","cookie-law-info")))])],1),e("div",{staticClass:"cky-section-header-actions cky-align-center"},[e("div",{staticClass:"cky-align-center cky-justify-end"},[e("label",{staticClass:"cky-toggle",attrs:{for:"cky-banner-preview"}},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.preview,expression:"preview"}],attrs:{type:"checkbox",id:"cky-banner-preview"},domProps:{checked:Array.isArray(t.preview)?t._i(t.preview,null)>-1:t.preview},on:{change:function(e){var n=t.preview,o=e.target,s=!!o.checked;if(Array.isArray(n)){var a=null,i=t._i(n,a);o.checked?i<0&&(t.preview=n.concat([a])):i>-1&&(t.preview=n.slice(0,i).concat(n.slice(i+1)))}else t.preview=s}}}),e("span",{staticClass:"cky-toggle-text"},[t._v(t._s(t.$i18n.__("Banner Preview","cookie-law-info")))]),e("span",{staticClass:"cky-toggle-slider",attrs:{"aria-hidden":"true"}})]),e("cky-button",{ref:"ckyButtonSaveLanguage",staticClass:"cky-button cky-button-green",staticStyle:{"margin-left":"15px"},nativeOn:{click:function(e){return t.saveConfig.apply(null,arguments)}}},[t._v(" "+t._s(t.$i18n.__("Publish Changes","cookie-law-info"))+" ")])],1)])]),e("div",{staticClass:"cky-section-content"},[e("div",{staticClass:"cky-edit-content"},[e("div",{staticClass:"cky-row cky-justify-between cky-button-section"},[e("div",{staticClass:"cky-col-6"},[t.loading?e("div",{staticClass:"cky-loader-languages"},[e("cky-card-loader")],1):t._e()]),e("div",{staticClass:"cky-col-6"},[t.loading?e("div",{staticClass:"cky-loader-languages"},[e("cky-card-loader")],1):t._e()])]),e("div",{staticClass:"cky-row"},[t.defaultLanguage!==t.currentLanguage?e("div",{staticClass:"cky-col"},[t.loading?e("div",{staticClass:"cky-loader-languages"},[e("cky-card-loader"),e("cky-card-loader"),e("cky-card-loader")],1):e("div",{staticClass:"cky-edit-content-lists"},[e("h5",{staticStyle:{"margin-bottom":"20px"}},[t._v(" "+t._s(t.$i18n.__("Default language:","cookie-law-info"))+" "),e("b",[t._v(t._s(t.getLanguageName(t.defaultLanguage)))])]),e("tab-content-accordion",{attrs:{language:t.defaultLanguage,translate:!0,disabled:!0}})],1)]):t._e(),e("div",{staticClass:"cky-col"},[t.loading?e("div",{staticClass:"cky-loader-languages"},[e("cky-card-loader"),e("cky-card-loader"),e("cky-card-loader")],1):e("div",{staticClass:"cky-edit-content-lists"},[e("div",{staticClass:"cky-align-center"},[e("h5",[t._v(t._s(t.$i18n.__("Edit content in:","cookie-law-info")))]),e("select",{directives:[{name:"model",rawName:"v-model",value:t.currentLanguage,expression:"currentLanguage"}],staticClass:"cky-select",on:{change:function(e){var n=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.currentLanguage=e.target.multiple?n:n[0]}}},t._l(t.selectedLanguages,(function(n){return e("option",{key:n,domProps:{value:n}},[t._v(" "+t._s(t.getLanguageName(n))+" ")])})),0)]),e("tab-content-accordion",{attrs:{language:t.currentLanguage,translate:!0}})],1)])])])]),e("exitIntentPopup",{ref:"exitIntentPopup"})],1)},ye=[],fe=n("c4aa"),me=n("8259"),ke=n("f61e"),be=n("c702"),Ce=n("17aa"),we=n("63ea"),ve=n.n(we),_e={name:"Customize",components:{TabContentAccordion:be["a"],CkyCardLoader:Ce["a"],exitIntentPopup:Bt},data(){return{bannerPreview:!1,loading:!0,initialStoreState:null}},methods:{hasChanges(){return this.initialStoreState&&!ve()(this.initialStoreState,this.banner.contents[this.currentLanguage])},handleBeforeUnload(t){this.hasChanges()&&(t.preventDefault(),t.returnValue="")},async loadCookies(){try{await this.$store.dispatch("cookies/reInit")}catch(t){console.error(t)}},showIcon(t){return!!t.icon},loadBanner:async function(){await fe["a"].getActiveBanner()},saveConfig:async function(){this.$refs.ckyButtonSaveLanguage.startLoading(),await fe["a"].save(),this.$root.$emit("triggerNotification",{type:"success",message:"Successfully saved"}),this.$refs.ckyButtonSaveLanguage.stopLoading(),this.initialStoreState=JSON.parse(JSON.stringify(this.banner.contents[this.currentLanguage]))},async loadLanguage(){const t=this.$route.params.language||"en";this.$store.state.languages.current=t},async changeLanguage(t){this.$store.state.languages.current=t},updateBannerContents:Object(R["b"])((async function(){me["a"].startLoading(),await fe["a"].updateContentPreview(),me["a"].stopLoading()}),500),getLanguageName(t){return ke["a"].getName(t)}},computed:{...Object(i["d"])("languages",["default","selected","available"]),currentTabComponent:function(){return"tab-"+this.currentTab.toLowerCase()},banner(){return this.$store.state.banners.current},banners(){return this.$store.state.banners.items||{}},preview:{get(){return this.$store.state.banners.preview},set(t){this.$store.state.banners.preview=t}},defaultLanguage(){return this.default},currentLanguage:{get(){return this.$store.state.languages.current},set(t){this.$store.state.languages.current=t}},selectedLanguages:function(){return this.$store.state.languages.selected}},async created(){this.loading=!0,await this.loadLanguage(),await this.loadBanner(),await fe["a"].loadTemplate(),this.loading=!1,this.initialStoreState=JSON.parse(JSON.stringify(this.banner.contents[this.currentLanguage])),await this.loadCookies()},mounted(){window.addEventListener("beforeunload",this.handleBeforeUnload)},watch:{currentLanguage:{async handler(){await this.updateBannerContents(),await fe["a"].showPreview(!0)},deep:!0},"banner.contents":{handler(){this.updateBannerContents()},deep:!0},preview:{handler(){fe["a"].showPreview()}}},beforeDestroy(){fe["a"].closePreview(!1),this.$store.state.languages.current=this.$store.state.languages.default,window.removeEventListener("beforeunload",this.handleBeforeUnload)},async beforeRouteLeave(t,e,n){if(!this.hasChanges())return n();this.$refs.exitIntentPopup.show();const o=await new Promise(t=>{this.$refs.exitIntentPopup.$on("stayOnPage",()=>{t(!1)}),this.$refs.exitIntentPopup.$on("discardChanges",()=>{t(!0)})});n(!!o)}},xe=_e,$e=(n("feb3"),Object(h["a"])(xe,ge,ye,!1,null,null,null)),Le=$e.exports;const Pe=[{path:"/dashboard",name:"dashboard",component:ft,children:[{path:"plans",name:"plans",component:xt,meta:{hideNav:!0}}]},{path:"/customize",name:"customize",component:Vt},{path:"/cookies",name:"cookies",component:Rt},{path:"/policies",name:"policies",component:Wt},{path:"/gcm",name:"gcm",component:ee},{path:"/settings",name:"settings",component:re},{path:"/languages",name:"languages",component:he,children:[{path:"edit/:language",name:"edit",component:Le}]}];var Se=n("8c4f"),Me=n("e19f"),Oe=n("07a4"),je=n("3840"),Ae=n("8103"),Be=n.n(Ae),Te=n("bba4"),He=n.n(Te),Ie=n("87ea");o["a"].use(Se["a"]),o["a"].config.productionTip=!1,o["a"].prototype.$i18n=je["a"],o["a"].prototype.$globals=window.ckyGlobals;const Ve=n("568d");Ve.keys().forEach(t=>{const e=Ve(t),n=Be()(He()(t.replace(/^\.\//,"").replace(/\.\w+$/,"").replace(/Index$/,"")));o["a"].component(n,e.default||e)}),o["a"].mixin({methods:{sprintf:Me["sprintf"],getOption:Ie["e"],setOption:Ie["m"],saveOptions:Ie["k"],getInfo:Ie["d"],loadInfo:Ie["g"],getDefaultLanguage:Ie["c"],setDefaultLanguage:Ie["l"],getAssetsURL:Ie["b"],purgeCache:Ie["h"],isRTLLanguage:Ie["f"]}}),o["a"].directive("click-outside",{bind:function(t,e,n){t.eventOnClick=function(o){t==o.target||t.contains(o.target)||n.context[e.expression](o)},document.body.addEventListener("click",t.eventOnClick)},unbind:function(t){document.body.removeEventListener("click",t.eventOnClick)}});class Ee{constructor(){this.Vue=o["a"],this.Router=Se["a"],this.App=ut}}window.ckyVue=new Ee;const Ne=new window.ckyVue.Router({routes:Pe});Ne.getRouteByName=function(t){return!!t&&Pe.find(e=>t===e.name)},Ne.redirectToDashboard=function(t){if(t&&"dashboard"===t)return;const e=Ne.getRouteByName("dashboard");Ne.push({name:e.name,query:e.query})},Ne.beforeEach((t,e,n)=>{const o=["/dashboard","/gcm","/settings"];if(t.path&&"/"===t.path)n({name:"dashboard"});else{const e=Object(Ie["e"])("account");e.connected?o.some(e=>e===t.path)?n():n({name:"dashboard"}):n()}}),String.prototype.addQuery=function(t){return this+Object.keys(t).reduce((function(e,n,o){return e+(0==o?"?":"&")+(Array.isArray(t[n])?t[n].reduce((function(e,o,s){return e+n+"="+encodeURIComponent(o)+(s!=t[n].length-1?"&":"")}),""):n+"="+encodeURIComponent(t[n]))}),"")},Ne.getQueryParams=function(t){let e=t?t.split("?")[1]:window.location.search.slice(1),n={};if(e){e=e.split("#")[0];let t=e.split("&");for(let e=0;e<t.length;e++){let o=t[e].split("="),s=void 0,a=o[0].replace(/\[\d*\]/,(function(t){return s=t.slice(1,-1),""})),i="undefined"===typeof o[1]||o[1];a=a.toLowerCase(),i=i.toLowerCase(),n[a]?("string"===typeof n[a]&&(n[a]=[n[a]]),"undefined"===typeof s?n[a].push(i):n[a][s]=i):n[a]=i}}return n},Ne.redirectMenu=function(t,e=!0){if(t===window.location.href&&!1===e)return;const n=Ne.getQueryParams(t);let o=n.page&&n.page||!1;if(!o)return;if(o.match(/cookie-law-info/)&&(o=o&&o.replace("cookie-law-info-","")||""),"cookie-law-info"===o)return void Ne.redirectToDashboard();const s=Ne.getRouteByName(o);Ne.push({name:s.name,query:s.query})},Ne.redirectToApp=function(t="dashboard"){const e=Ne.getAppRedirectURL(t);Ne.redirectToURL(e)},Ne.getAppRedirectURL=function(t="dashboard"){const e=Object(Ie["e"])("account");if(!e.connected)return!1;let n=window.ckyGlobals.webApp.url+"/"+t,o={website:e.website_id};return n=n.addQuery(o),n},Ne.redirectToAppWithQuery=function(t){const e=Object(Ie["e"])("account");if(!e.connected)return!1;let n=window.ckyGlobals.webApp.url+"/dashboard";n=n.addQuery(t),Ne.redirectToURL(n)},Ne.redirectToURL=function(t){try{const e=document.createElement("a");e.target="_blank",e.href=t,e.rel="noopener noreferrer",e.click()}catch(e){alert("Your browser blocked the authorization window from opening. Please check your popup settings.")}},window.ckyGlobals.vueApp=new window.ckyVue.Vue({render:t=>t(ut),store:Oe["a"],router:Ne}).$mount("#cky-app")},5985:function(t,e,n){"use strict";n.r(e);var o=function(){var t=this,e=t._self._c;return e("button",{class:t.buttonClass,attrs:{disabled:t.loading}},[t.loading?t._t("loader",(function(){return[t._v(t._s(t.$i18n.__("Loading","cookie-law-info")))]})):t._t("default"),t.loading?e("span",{staticClass:"cky-btn-spinner"},[e("span"),e("span"),e("span"),e("span")]):t._e()],2)},s=[],a={name:"CkyButton",components:{},data(){return{loading:!1}},methods:{startLoading(){this.loading=!0},stopLoading(){this.loading=!1}},computed:{buttonClass(){return{"cky-button":!0,"cky-button-loading":!0===this.loading}}}},i=a,r=(n("7004"),n("2877")),c=Object(r["a"])(i,o,s,!1,null,null,null);e["default"]=c.exports},"6a11":function(t,e,n){t.exports=n.p+"img/widget-icon.svg"},"6b1c":function(t,e,n){},7004:function(t,e,n){"use strict";n("a951")},"7ad7":function(t,e,n){"use strict";n("8cf5")},"7cd5":function(t,e,n){t.exports=n.p+"img/crown.svg"},"7f2b":function(t,e,n){"use strict";n("2946")},"7f7f":function(t,e,n){"use strict";n.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{ref:"popperContainer",staticClass:"cky-popper-container",on:{mouseleave:function(e){t.hover&&t.closePopper()}}},[e("div",{ref:"triggerNode",staticClass:"cky-poppper-trigger",on:{mouseover:function(e){t.hover&&t.openPopper()}}},[t._t("default",(function(){return[e("div",{staticClass:"cky-popper-icon"})]}))],2),e("Transition",{attrs:{name:"fade"}},[t.shouldShowPopper?e("div",{ref:"popperNode",staticClass:"cky-popper"},[t._t("content",(function(){return[t.ishtml?e("div",{domProps:{innerHTML:t._s(t.content)}}):e("div",[t._v(" "+t._s(t.content)+" ")])]})),e("div",{staticClass:"cky-popper-arrow",attrs:{"data-popper-arrow":""}})],2):t._e()])],1)},s=[],a=n("45d1"),i=n("0929"),r=n("9835"),c={name:"CkyPopper",components:{},props:{content:{type:String,default:""},position:{type:String,default:"top"},hover:{type:Boolean,default:!0},ishtml:{type:Boolean,default:!1},isScrollable:{type:Boolean,default:!1}},data(){return{popperId:null,popper:null,isOpen:!1}},computed:{shouldShowPopper(){return this.isOpen}},ready(){this.$nextTick(()=>{this.showPopper&&this.initPopper()})},methods:{initPopper(){this.$nextTick((function(){this.popper=Object(a["a"])(this.$el,this.$el.querySelector(".cky-popper"),{placement:this.position||"bottom",removeOnDestroy:!0,modifiers:[i["a"],{name:"arrow",options:{padding:10}},r["a"],{name:"offset",options:{offset:[0,8]}}]})}))},destroyPopper(){this.popper&&(this.popper.destroy(),this.popper=null)},openPopper(){this.isScrollable&&(document.querySelector(".cky-app-wrap .cky-dropdown-languages ul").style.overflow="hidden"),this.isOpen=!0,this.initPopper()},closePopper(){this.isScrollable&&(document.querySelector(".cky-app-wrap .cky-dropdown-languages ul").style.overflow="scroll"),this.isOpen=!1}}},l=c,u=(n("1759"),n("2877")),d=Object(u["a"])(l,o,s,!1,null,null,null);e["default"]=d.exports},8025:function(t,e,n){},8259:function(t,e,n){"use strict";var o=n("07a4"),s=n("f9c4"),a=n("87ea");const i=window.ckyBannerConfig._shortCodes;function r(t){const e=document.querySelector("[data-cky-tag="+t+"]");return e||!1}function c(){return o["a"].state.banners.current.properties.settings.applicableLaw}function l(){return o["a"].state.banners.current.properties.settings.type}function u(){return"classic"===l()?"pushdown":o["a"].state.banners.current.properties.settings.preferenceCenterType}function d(){const t=r("notice"),e=t&&t.closest(".cky-consent-container")||!1;return e&&e||!1}function p(){const t=d();t&&t.classList.add("cky-hide")}function h(){const t=d();t&&t.classList.remove("cky-hide")}function g(){const t=document.querySelector(".cky-overlay");t&&t.classList.add("cky-hide")}function y(){const t=document.querySelector(".cky-overlay");t&&t.classList.remove("cky-hide")}function f(){const t=document.querySelector(".cky-overlay");t&&t.classList.toggle("cky-hide")}function m(){if("classic"===l())return d();p();const t="ccpa"===c()?r("optout-popup"):r("detail");return t&&t.closest(".cky-modal")||!1}function k(){const t=m();t&&t.classList.remove(w()),"classic"!==l()&&(g(),h())}function b(){const t=m();if(t&&t.classList.add(w()),t){const e=t.querySelector(".cky-preference-center");if(e){e.setAttribute("role","dialog"),e.setAttribute("aria-modal","true");const t="ccpa"===c()?"Opt-out Preferences":"Customise Consent Preferences";e.setAttribute("aria-label",t)}}"classic"!==l()&&y()}function C(){const t=m();if(t){if(t.classList.toggle(w()),"classic"===l()){const e=t.querySelector(".cky-preference-center");if(e){e.setAttribute("role","dialog"),e.setAttribute("aria-modal","true");const t="ccpa"===c()?"Opt-out Preferences":"Customise Consent Preferences";e.setAttribute("aria-label",t)}}"pushdown"!==u()&&f()}}function w(){return"pushdown"===u()?"cky-consent-bar-expand":"cky-modal-open"}function v(){const t=r("notice");if(!t)return!1;const e=t.closest(".cky-consent-container");if(!e)return!1;e.setAttribute("aria-label","We value your privacy"),e.setAttribute("role","region");const n=o["a"].state.banners.current.properties.settings.type;let s=o["a"].state.banners.current.properties.settings.position,a=n;"popup"===a&&(s="center"),a="pushdown"===u()?"classic":a;const i=`cky-${a}-${s}`;e.classList.add(i);const c=r("revisit-consent");if(!c)return!1;const l="cky-revisit-"+o["a"].state.banners.current.properties.config.revisitConsent.position;c.classList.add(l)}function _(){const t="ccpa"===c()?r("optout-popup"):r("detail");if(!t)return!1;const e=t.closest(".cky-modal");if(!e)return!1;if("pushdown"!==u()&&"popup"!==u()){const t=o["a"].state.banners.current.properties.settings.preferenceCenterType,n="cky-"+t;e.classList.add(n)}const n=e.querySelector(".cky-preference-center");if(n){n.setAttribute("role","dialog"),n.setAttribute("aria-modal","true");const t="ccpa"===c()?"Opt-out Preferences":"Customise Consent Preferences";n.setAttribute("aria-label",t)}}function x(){$("settings-button",()=>C()),$("detail-close",()=>k()),$("detail-save-button",()=>k()),$("optout-cancel-button",()=>k()),$("optout-confirm-button",()=>k()),$("detail-category-preview-save-button",()=>k()),$("donotsell-button",()=>b()),$("optout-close",()=>k()),L()}function $(t,e){document.querySelectorAll("[data-cky-tag="+t+"]").forEach(t=>{t&&t.addEventListener("click",e)})}function L(){const t=o["a"].state.banners.current;t.properties.config.auditTable.status&&document.querySelectorAll(".cky-accordion").forEach(t=>t.addEventListener("click",(function(t){"checkbox"!==t.target.type&&this.classList.toggle("cky-accordion-active")})))}function P(){const t=c(),e=o["a"].state.banners.current,n=o["a"].state.languages.current,s="ccpa"===t?"optoutPopup":"preferenceCenter",a=i.find(t=>"cky_show_desc"===t.key),r=i.find(t=>"cky_hide_desc"===t.key);if(!a||!r)return;const l=r.content,u=a.content,d=(new DOMParser).parseFromString(l,"text/html"),p=d.querySelector('[data-cky-tag="hide-desc-button"]');p.innerHTML=e.contents[n][s].elements.showLess;const h=(new DOMParser).parseFromString(u,"text/html"),g=h.querySelector('[data-cky-tag="show-desc-button"]');g.innerHTML=e.contents[n][s].elements.showMore;const y=window.innerWidth<376?150:300,f=document.querySelector(`[data-cky-tag="${"gdpr"===t?"detail":"optout"}-description"]`),m=f.textContent;if(m.length<y)return;const k=f.innerHTML,b=(new DOMParser).parseFromString(k,"text/html"),C=b.querySelectorAll("body > p");if(C.length<=1)return;let w="";for(let o=0;o<C.length;o++){if(o===C.length-1)return;const t=C[o];if(`${w}${t.outerHTML}`.length>y&&t.insertAdjacentHTML("beforeend","...&nbsp;"+h.body.innerHTML),w=`${w}${t.outerHTML}`,w.length>y)break}function v(){f.innerHTML=`${k}${d.body.innerHTML}`,$("hide-desc-button",_)}function _(){f.innerHTML=w,$("show-desc-button",v)}_()}function S(t,e){const n=t.querySelector("[data-cky-tag="+e+"]");if(!n)return!1;let o=n,s="";n.getAttribute("src")||(o=n.querySelector("img")),s=o.src;const a=s.lastIndexOf("/"),i=s.substring(a+1);o.src=window.ckyGlobals.assetsURL+i}function M(t){const e=t.querySelector('[data-cky-tag="detail"] .cky-footer-shadow'),n=t.querySelector('[data-cky-tag="detail"]');if(!e)return;const o=n&&n.style.backgroundColor||"#ffffff";e.style.background=`linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, ${o} 100%)`}function O(t=!1){const e=o["a"].state.cookies.items,n=o["a"].state.banners.current;let s=n.properties.config.preferenceCenter.elements.categories.elements.toggle;A("ckySwitch",s),s=n.properties.config.categoryPreview.elements.toggle,s&&A("ckyCategoryDirect",s);for(const o of e){const e=o.prior_consent||"necessary"===o.slug,n="necessary"===o.slug;j(o,e,n,t),H(o)}}function j(t,e,n,s=!1){const a=o["a"].state.banners.current,i=a.properties.config.preferenceCenter.elements.categories.elements.toggle,r=i.states.active.styles["background-color"],c=i.states.inactive.styles["background-color"];["ckyCategoryDirect","ckySwitch"].map(o=>{const a=document.getElementById(`${o}${t.slug}`);a&&(B(a,t),a.checked=e,a.disabled=n,a.style.backgroundColor=e?r:c,s||a.addEventListener("change",({currentTarget:t})=>{const e=t.checked;t.style.backgroundColor=e?r:c}))})}function A(t,e){const n=e.states.active.styles["background-color"],o=e.states.inactive.styles["background-color"];document.querySelectorAll(`[id^="${t}"]`).forEach((function(t){let e=t.checked;t.addEventListener("change",({currentTarget:t})=>{const e=t.checked;t.style.backgroundColor=e?n:o}),t.style.backgroundColor=e?n:o}))}function B(t,e={}){"detail-category-toggle"===t.parentElement.getAttribute("data-cky-tag")?T(t,e):"detail-category-preview-toggle"===t.parentElement.getAttribute("data-cky-tag")&&I(t,e)}function T(t,e){const n=o["a"].state.banners.current;let s=t.closest(".cky-accordion-item");if(!s)return;const a=s.querySelector(".cky-switch"),i=s.querySelector(".cky-always-active");"necessary"===e.slug?a&&a.remove():(i&&i.remove(),("classic"===l()&&n.properties.config.categoryPreview.status||e.cookie_list&&0===e.cookie_list.length)&&a&&a.remove())}function H(t){const e=o["a"].state.banners.current;if(!1===e.properties.config.auditTable.status){const e=document.querySelector(`#ckyDetailCategory${t.slug} [data-cky-tag="audit-table"]`);e&&e.remove();const n=document.querySelector(`#ckyDetailCategory${t.slug} .cky-accordion-chevron`);n&&n.classList.add("cky-accordion-chevron-hide")}}function I(t,e){e.cookie_list&&0===e.cookie_list.length&&"necessary"!==e.slug&&t.parentElement.parentElement.remove()}function V(t){const e=o["a"].state.banners.current,n=o["a"].state.languages.current,s=i.find(t=>"cky_readmore"===t.key);if(t.querySelector('[data-cky-tag="readmore-button"]'))return;if(!1===e.properties.config.notice.elements.buttons.elements.readMore.status)return;const a=s.content,r=t.querySelector('[data-cky-tag="description"]');if(!r)return;const c=(new DOMParser).parseFromString(a,"text/html"),l=c.querySelector('[data-cky-tag="readmore-button"]');if(!l)return!1;if(l.innerHTML=e.contents[n].notice.elements.buttons.elements.readMore,r.childNodes.length>1){const e=t.querySelector('[data-cky-tag="description"] p:last-child');e&&e.insertAdjacentHTML("beforeend","&nbsp;"+c.body.innerHTML)}else r.insertAdjacentHTML("beforeend","&nbsp;"+c.body.innerHTML)}function E(){if(!Object(a["f"])())return;const t=["notice","detail","optout-popup","revisit-consent","video-placeholder"];t.forEach((function(t){r(t)&&r(t).classList.add("cky-rtl")}))}function N(){let t="flex-end";["detail-powered-by","optout-powered-by"].map(e=>{const n=document.querySelector(`[data-cky-tag="${e}"]`);n&&(n.style.display="flex",n.style.justifyContent=t,n.style.alignItems="center")})}function D(){const t=r("optout-option");if(!t)return;t.classList.remove("cky-disabled");const e=o["a"].state.banners.current,n=e.properties.config.optoutPopup.elements.optOption.elements.toggle,s=n.states.active.styles["background-color"],a=n.states.inactive.styles["background-color"],i=document.getElementById("ckyCCPAOptOut");i&&($("optout-option-title",()=>i.click()),i.style.backgroundColor=a,i.addEventListener("change",({currentTarget:t})=>{const e=t.checked;t.style.backgroundColor=e?s:a}))}const Z={shortCodes:window.ckyBannerConfig._shortCodes,generate:async function(t=!1,e=!0){const n=o["a"].state.banners.current;let s=await this.getTemplate(t);if(n){await this.renderBannerStyles(s.styles);const t=this.refreshBannerStyles(n.properties);await this.renderBannerHtml(t,e)}},startLoading(){const t=document.getElementById("cky-banner-preview-container");t&&t.classList.add("cky-preview-partial-refreshing")},stopLoading(){const t=document.getElementById("cky-banner-preview-container");t&&t.classList.remove("cky-preview-partial-refreshing")},getTemplate:async function(t=!1){const e=o["a"].state.banners.current;if(!e||!e.properties)return;const n=e.properties.settings.type,s=e.properties.settings.applicableLaw,a=o["a"].state.banners.template||[];if(Object.prototype.hasOwnProperty.call(a,s)&&Object.prototype.hasOwnProperty.call(a[s],n)&&!1===t)return a[s][n];const i=await this.getPreviewHtml(e);if(i.html){const t=a[s]||[];return t[n]=i,a[s]=t,await o["a"].dispatch("banners/setTemplate",a),a[s][n]}},showPreview:async function(t=!1){!0===o["a"].state.banners.preview?await this.generate(t):this.hidePreview()},forceShowPreview:async function(){await this.generate(!0)},forceRerenderPreview:async function(){!0===o["a"].state.banners.preview&&await this.generate(!0,!1)},hidePreview:function(){const t=document.getElementById("cky-banner-preview-container");null!==t&&document.body.removeChild(t)},closePreview:function(t=!0){o["a"].state.banners.preview=!1,!0===t&&this.clearTemplate(),this.hidePreview()},clearTemplate:function(){o["a"].state.banners.template=[],o["a"].state.banners.current=!1},resetTemplate(){o["a"].state.banners.template=[]},renderBannerHtml:async function(t,e=!0){if(!t||!e)return;let n=document.getElementById("cky-banner-preview-container");if(null===n)n=document.createElement("div"),n.id="cky-banner-preview-container",n.className="cky-banner-preview",document.body.insertBefore(n,document.body.firstChild);else{const t=document.querySelector(".cky-consent-container");t&&(n.innerHTML="")}const o=(new DOMParser).parseFromString(t,"text/html");this.updateCategoryPeview(o),S(o,"detail-close"),S(o,"close-button"),S(o,"detail-powered-by"),S(o,"revisit-consent"),S(o,"optout-powered-by"),M(o),n.insertAdjacentHTML("afterbegin",o.body.innerHTML),x(),this.attachPreviewCloseBtn(),v(),h(),P(),O(),V(document),E(),N(),D(),_()},renderBannerStyles:async function(t){const e=document.getElementById("cky-banner-style");null!==e&&document.getElementsByTagName("head")[0].removeChild(e);const n=document.createElement("style");n.id="cky-banner-style",n.innerHTML=t,document.getElementsByTagName("head")[0].appendChild(n)},refreshBannerStyles:function(t){const e=o["a"].state.banners.current;if(!e)return;const n=e.properties.settings.type,s=e.properties.settings.applicableLaw,a=o["a"].state.banners.template[s][n],i=a.html;if(!i)return;const r=(new DOMParser).parseFromString(i,"text/html");V(r);const c=t.config,l=t=>{Object.keys(t).forEach(e=>{if("object"===typeof t[e]&&null!==t[e]){if(Object.prototype.hasOwnProperty.call(t[e],"tag")){let n=t[e],o=n["tag"]?n["tag"]:"";""!=o&&r.querySelectorAll(`[data-cky-tag^="${o}"]`).forEach((function(t){let e=!(!n["status"]||!0!==n["status"]);!1===e&&"detail-category-toggle"!=o&&t.parentNode.removeChild(t),"detail-category-preview-save-button"==t.getAttribute("data-cky-tag")&&"detail"==o||Z.addStylesToElement(t,n)}))}l(t[e])}})};return l(c),this.updateCategoryPeview(r),this.resetTemplate(),r.body.innerHTML},updateCategoryPeview(t){const e=o["a"].state.cookies.items;e&&e.forEach((function(e){if("necessary"===e.slug)return;let n=t.getElementById("ckyDetailCategory"+e.slug);const o=t.getElementById("ckyCategoryDirect"+e.slug);!1===e.visibility&&(n&&n.parentNode.removeChild(n),o&&o.closest(".cky-category-direct-item").remove())}))},addStylesToElement:function(t,e){if(null!==e&&null!==t){let n="";for(const t in e.styles)n+=`${t}: ${e.styles[t]};`;t.style.cssText+=n}},getPreviewHtml:async function(t){try{return await s["a"].post({path:"/banners/preview",params:{language:o["a"].state.languages.current},data:t})}catch(e){console.log(e)}},attachPreviewCloseBtn(){const t=o["a"].state.banners.current;if(!t)return;const e=t.properties.settings.type,n=document.getElementById("cky-banner-preview-container");n&&"popup"===e&&n.insertAdjacentHTML("beforeend",'<button type="button" id="cky-preview-close-btn" data-cky-tag="preview-close" class="cky-button-close-alt cky-preview-close">Close Preview</button>')},parseDom(t){const e=new DOMParser,n=e.parseFromString(t,"text/html");return n.body}};e["a"]=Z},"83f4":function(t,e,n){n.p=window.ckyGlobals.app.url},"87e5":function(t,e,n){},"87ea":function(t,e,n){"use strict";n.d(e,"e",(function(){return a})),n.d(e,"d",(function(){return i})),n.d(e,"g",(function(){return r})),n.d(e,"m",(function(){return c})),n.d(e,"k",(function(){return l})),n.d(e,"j",(function(){return u})),n.d(e,"i",(function(){return d})),n.d(e,"c",(function(){return p})),n.d(e,"l",(function(){return h})),n.d(e,"b",(function(){return g})),n.d(e,"h",(function(){return y})),n.d(e,"f",(function(){return f})),n.d(e,"a",(function(){return m}));var o=n("07a4"),s=n("f9c4");function a(t,e=!1){let n=e;const s=o["a"].state.settings.options;return s[t]&&(n=s[t]),n}function i(t=!1,e=!1){let n=e;const s=o["a"].state.settings.info;return!1===t?s:(s[t]&&(n=s[t]),n)}async function r(t=!1){return await o["a"].dispatch("settings/loadInfo",t)}function c(t,e){o["a"].dispatch("settings/set",{key:t,value:e})}async function l(t=!0){return o["a"].dispatch("settings/save",{clear:t})}async function u(){return await o["a"].dispatch("settings/reInit")}function d(){if(Object.keys(o["a"].state.settings.errors).length){const t=o["a"].state.settings.errors;t.urlMismatch&&delete t.urlMismatch}}function p(){return o["a"].state.languages.default&&o["a"].state.languages.default||"en"}async function h(t="en"){await o["a"].dispatch("languages/setDefault",t)}function g(){return window.ckyGlobals.app.url}async function y(){await s["a"].post({path:"/settings/cache/purge",data:{}})}function f(t=!1){return!1===t&&(t=o["a"].state.languages.current),["ar","az","dv","he","ku","fa","ur"].includes(t)}function m(){try{const{hostname:t}=new URL(window.ckyGlobals.site.url),e=new URL(o["a"].state.settings.info.url),n=e.hostname,s=t.split(".");let a=!1;for(let o=0;o<s.length;o++){const t=s.slice(o).join(".");if(a=a||t===n||t.replace(/^www./,"")===n.replace(/^www./,""),a)break}return a}catch(t){return!1}}},"89d0":function(t,e,n){"use strict";n("01e9")},"8a08":function(t,e,n){"use strict";n("87e5")},"8a3e":function(t,e,n){},"8a80":function(t,e,n){"use strict";var o=function(){var t=this,e=t._self._c;return e("transition",{attrs:{name:"cky-app-modal-fade"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.isVisible,expression:"isVisible"}],staticClass:"cky-app-modal-backdrop"},[e("div",{staticClass:"cky-app-modal",attrs:{role:"dialog","aria-labelledby":t.id+"-title","aria-describedby":t.id+"-description"}},["default"===t.type?e("div",{staticClass:"cky-app-modal-header",attrs:{id:t.id+"-title"}},[t._t("header"),t.dismissable?e("button",{staticClass:"cky-button-close",attrs:{type:"button","aria-label":"Close cky-modal"},on:{click:t.close}},[e("cky-icon",{attrs:{icon:"close",width:"15px",height:"15px",color:"#8893a1"}})],1):t._e()],2):t._e(),e("div",{staticClass:"cky-app-modal-body",attrs:{id:t.id+"-description"}},[t.dismissable&&"info"===t.type?e("button",{staticClass:"cky-button-close",attrs:{type:"button","aria-label":"Close cky-modal"},on:{click:t.close}},[e("cky-icon",{attrs:{icon:"close",width:"15px",height:"15px",color:"#8893a1"}})],1):t._e(),t._t("body")],2),t.$slots.footer?e("div",{staticClass:"cky-app-modal-footer"},[t._t("footer")],2):t._e()])])])},s=[],a=n("1f3d"),i={name:"CkyModal",components:{CkyIcon:a["a"]},props:{id:{type:String,default:"cky-modal"},dismissable:{type:Boolean,default:!0},type:{type:String,default:"default"},visible:{type:Boolean,default:!1}},data(){return{isVisible:!1}},methods:{show(){this.isVisible=!0,document.body.classList.add("cky-app-modal-open")},close(){this.isVisible=!1,document.body.classList.remove("cky-app-modal-open"),this.$emit("close")}},computed:{hasFooter(){return!!this.$slots.footer}},created(){!0===this.visible&&(this.isVisible=!0)}},r=i,c=n("2877"),l=Object(c["a"])(r,o,s,!1,null,null,null);e["a"]=l.exports},"8a8c":function(t,e,n){},"8cf5":function(t,e,n){},"919d":function(t,e,n){"use strict";var o=function(){var t=this,e=t._self._c;return t.showConnectSuccess?e("div",{staticClass:"cky-connect-success",attrs:{id:"cky-connect-success"}},[t.syncing?e("div",{staticClass:"cky-connect-loader"},[e("cky-spinner"),e("h4",[t._v(" "+t._s(t.$i18n.__("Please wait while we connect your site to app.cookieyes.com","cookie-law-info"))+" ")])],1):e("div",{staticClass:"cky-connect-success-container"},[e("div",{staticClass:"cky-connect-success-icon"}),e("div",{staticClass:"cky-connect-success-message"},[t._t("message",(function(){return[e("h2",[t._v(" "+t._s(t.$i18n.__("Your website is connected to app.cookieyes.com","cookie-law-info"))+" ")]),e("p",[t._v(" "+t._s(t.$i18n.__("You can now continue to manage all your existing settings and access all free CookieYes features from your web app account","cookie-law-info"))+" ")])]}))],2),e("div",{staticClass:"cky-connect-success-actions"},[t._t("action",(function(){return[e("button",{staticClass:"cky-button cky-button-medium cky-external-link",on:{click:function(e){return t.redirectToApp()}}},[t._v(" "+t._s(t.$i18n.__("Go to CookieYes Web App","cookie-law-info"))+" ")])]}))],2)])]):t._e()},s=[],a=function(){var t=this,e=t._self._c;return e("span",{staticClass:"cky-spinner-loader"})},i=[],r={name:"CkySpinner",components:{}},c=r,l=(n("c9d1"),n("2877")),u=Object(l["a"])(c,a,i,!1,null,null,null),d=u.exports,p={name:"CkyConnectSuccess",components:{CkySpinner:d},props:{timeout:{type:Number,default:6e3}},data(){return{showConnectSuccess:!1,syncing:!1}},methods:{showMessage(){this.showConnectSuccess=!0},redirectToApp(){this.$router.redirectToApp(),this.showConnectSuccess=!1,this.$router.redirectToDashboard(this.$route.name)}},created(){this.$root.$on("afterConnection",()=>{this.syncing=!0,this.showMessage()}),this.$root.$on("afterSyncing",async()=>{this.syncing=!1})}},h=p,g=(n("045e"),Object(l["a"])(h,o,s,!1,null,null,null));e["a"]=g.exports},9373:function(t,e,n){"use strict";n("8a8c")},9610:function(t,e,n){"use strict";var o=function(){var t=this,e=t._self._c;return t.pluginStatus?e("div",{staticClass:"cky-card",class:t.getLoadingClass},[t.title?e("div",{staticClass:"cky-card-header"},[e("div",{staticClass:"cky-row"},[e("h5",{staticClass:"cky-card-title"},[t._v(" "+t._s(t.title)+" ")]),t.subtitle?e("p",{staticClass:"cky-card-subtitle"},[t._v(" "+t._s(t.subtitle)+" ")]):t._e()]),t.hasActions?e("div",{staticClass:"cky-card-actions"},[t._t("headerAction")],2):t._e()]):t._e(),t.hasBodySlot?e("div",{class:t.getBodyClass},[t.loading?e("cky-card-loader"):t._t("body")],2):t._e(),t._t("outside"),t.hasFooterSlot?e("div",{staticClass:"cky-card-footer"},[t._t("footer")],2):t._e()],2):t._e()},s=[],a=n("17aa"),i={components:{CkyCardLoader:a["a"]},name:"CkyCard",props:{title:{type:String,required:!1},subtitle:{type:String,required:!1,default:""},bodyClass:{type:String,default:""},loading:{type:Boolean,default:!1},fullWidth:{type:Boolean,default:!1}},computed:{hasActions(){return!!this.$slots.headerAction},hasBodySlot(){return!!this.$slots.body},hasFooterSlot(){return!!this.$slots.footer},getLoadingClass(){return this.loading?"cky-loading":""},getBodyClass(){return{"cky-card-body":!0,"cky-card-body--full":this.fullWidth,[this.bodyClass]:this.bodyClass}},pluginStatus(){return this.$store.state.settings.status}}},r=i,c=n("2877"),l=Object(c["a"])(r,o,s,!1,null,null,null);e["a"]=l.exports},9947:function(t,e,n){"use strict";var o=function(){var t=this,e=t._self._c;return e("cky-modal",{ref:"ckyConnectModal",staticClass:"cky-connect-modal cky-text-center",attrs:{type:"info",visible:t.visible,dismissable:t.dismissable},scopedSlots:t._u([{key:"body",fn:function(){return[t._t("title")]},proxy:!0},{key:"footer",fn:function(){return[t._t("message"),e("div",{staticClass:"cky-app-modal-actions cky-justify-center"},[e("cky-button",{ref:"ckyButtonConnect",nativeOn:{click:function(e){return t.handleNavigate()}}},[t._v(" "+t._s(t.$i18n.__("Connect to Web App to Access","cookie-law-info"))+" ")]),e("p",{staticClass:"cky-login-text"},["all"===t.availablePlan?e("a",{staticClass:"cky-login-and-connect cky-external-link",attrs:{href:""},on:{click:function(e){return e.preventDefault(),t.connectToApp(!0)}}},[t._v(" "+t._s(t.$i18n.__("Have an account? Log in and connect","cookie-law-info"))+" ")]):e("a",{staticClass:"cky-login-and-connect cky-external-link",attrs:{href:""},on:{click:function(e){return e.preventDefault(),t.connectToApp(!0,t.feature)}}},[t._v(" "+t._s(t.$i18n.__("Have an account? Log in and connect","cookie-law-info"))+" ")])])],1)]},proxy:!0}],null,!0)})},s=[],a=n("c068"),i=n("8a80"),r={name:"CkyConnectModal",mixins:[a["a"]],components:{CkyModal:i["a"]},props:{visible:{type:Boolean,default:!1},availablePlan:{type:String,default:"all"},feature:{type:String,default:""}},methods:{show(){this.$refs.ckyConnectModal&&this.$refs.ckyConnectModal.show()},close(){this.$refs.ckyConnectModal&&this.$refs.ckyConnectModal.close()},handleNavigate(){document.body.classList.remove("cky-app-modal-open"),this.$router.push({path:"dashboard/plans",query:{available:this.availablePlan}})}},computed:{dismissable(){return!this.visible}},mounted(){this.$root.$on("afterConnection",()=>{this.close()})}},c=r,l=(n("00b4"),n("2877")),u=Object(l["a"])(c,o,s,!1,null,null,null);e["a"]=u.exports},"9e47":function(t,e,n){"use strict";var o=function(){var t=this,e=t._self._c;return e("span",{staticClass:"cky-spinner"},[e("cky-icon",{attrs:{icon:"spinner",width:"20px",height:"20px"}})],1)},s=[],a=n("1f3d"),i={name:"CkyLoader",components:{CkyIcon:a["a"]}},r=i,c=(n("d235"),n("2877")),l=Object(c["a"])(r,o,s,!1,null,null,null);e["a"]=l.exports},a07a:function(t,e,n){"use strict";n.r(e);var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cky-app-input",class:{file:"file"===t.type}},[e("input",{staticClass:"cky-form-control",attrs:{disabled:t.disabled,type:t.type,placeholder:t.placeholder,autocomplete:t.autocomplete,readonly:t.readonly,spellcheck:t.spellcheck,max:"number"===t.type?t.max:null,min:"number"===t.type?t.min:null,step:"number"===t.type?t.step:null,id:t.inputId},domProps:{value:t.value},on:{input:function(e){return t.$emit("input",e.target.value)},blur:function(e){return t.$emit("blur",e.target.value)},change:function(e){return t.$emit("change",e.target.value)},focus:function(e){return t.$emit("focus",e.target.value)},keyup:function(e){return t.$emit("keyup",e)},keydown:function(e){return t.$emit("keydown",e)}}}),t._t("cky-input-error")],2)},s=[],a={props:{value:{type:[String,Number],default:""},placeholder:{type:String,default:""},autocomplete:{type:String,default:""},readonly:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},max:{type:Number,default:Number.MAX_SAFE_INTEGER},min:{type:Number,default:Number.MIN_SAFE_INTEGER},spellcheck:{type:Boolean,default(){return!0}},type:{type:String,default(){return"text"}},step:{type:Number,required:!1},inputId:String,error:{type:[String,Boolean],default(){return!1}}}},i=a,r=(n("1087"),n("2877")),c=Object(r["a"])(i,o,s,!1,null,"529f0665",null);e["default"]=c.exports},a2b6:function(t,e,n){"use strict";function o(t){return new Promise(e=>setTimeout(e,t))}function s(t){let e;return n=>{clearTimeout(e),e=setTimeout(()=>t(n),500)}}function a(t){const e=["Array","Object"],n=Object.prototype.toString.call(t);if(!new RegExp(e.join("|")).test(n)||t instanceof WeakMap||t instanceof WeakSet)return t;const o=new t.constructor;return Object.assign(o,...Object.keys(t).map(e=>({[e]:a(t[e])})))}function i(t,e=0){let n;const o=[];return(...s)=>new Promise((a,i)=>{clearTimeout(n),n=setTimeout(()=>{const e=[...o];o.length=0,Promise.resolve(t.apply(this,s)).then(t=>{e.forEach(({resolve:e})=>e(t))},t=>{e.forEach(({reject:e})=>e(t))})},e),o.push({resolve:a,reject:i})})}n.d(e,"d",(function(){return o})),n.d(e,"b",(function(){return s})),n.d(e,"a",(function(){return a})),n.d(e,"c",(function(){return i}))},a951:function(t,e,n){},a9e4:function(t,e,n){"use strict";var o=n("f9c4");e["a"]={async getCookies(t={}){try{const e=await o["a"].get({path:"cookies/",params:t});return e}catch(e){console.error(e)}},async getCookie(t){try{const e=await o["a"].get({path:"cookies/"+t});return e}catch(e){console.error(e)}},async createCookie(t){try{const e=await o["a"].post({path:"cookies/",data:t});return e}catch(e){console.error(e)}},async updateCookie(t,e){try{const n=await o["a"].put({path:"/cookies/"+t,data:e});return n}catch(n){console.error(n)}},async deleteCookie(t){try{const e=await o["a"].delete({path:"/cookies/"+t});return e}catch(e){console.error(e)}},async getCookieCategories(t={}){try{const e=await o["a"].get({path:"cookies/categories",params:t});if(e)return e}catch(e){console.error(e)}},async getCookieCategory(t){try{const e=await o["a"].get({path:"cookies/categories/"+t});return e}catch(e){console.error(e)}},async createCookieCategory(t){try{const e=await o["a"].post({path:"cookies/categories",data:t});return e}catch(e){console.error(e)}},async updateCookieCategory(t,e){try{const n=await o["a"].put({path:"/cookies/categories/"+t,data:e});return n}catch(n){console.error(n)}},async deleteCookieCategory(t){try{const e=await o["a"].delete({path:"/cookies/categories/"+t});return e}catch(e){console.error(e)}},async bulkUpdateCookieCategory(t){t.forEach(t=>{t.cookie_list&&delete t.cookie_list});try{const e=await o["a"].post({path:"/cookies/categories/bulk",data:{categories:t}});return e}catch(e){console.error(e)}}}},a9f4:function(t,e,n){"use strict";var o=function(){var t=this,e=t._self._c;return e("ul",{class:t.accordionClass},[t._t("default")],2)},s=[],a={props:{name:{type:String,default:"default"},type:{type:String,default:"default"}},data(){return{Accordion:{count:0,name:this.name},expandedIndex:null,activeIndex:null}},provide(){return{Accordion:this.Accordion,watchAccordion:this.watchAccordion}},computed:{accordionClass(){return{"cky-app-accordion":!0,"cky-app-accordion-boxed":"boxed"===this.type}}},methods:{watchAccordion(t){this.expandedIndex=t,this.isExpanded()?this.closeAccordion():(this.closeAccordion(),this.expandAccordion())},hideAccordion(t){const e=t.querySelector(".cky-app-accordion-content");e&&(e.style.height=e.scrollHeight+"px",window.setTimeout((function(){e.style.height="0",e.style.overflow="hidden"}),1),window.setTimeout((function(){t.classList.remove("cky-app-accordion-open")}),200))},expandAccordion(){const t=this.getActiveAccordions();t.forEach(t=>{if(t){const e=t.querySelector(".cky-app-accordion-content");if(!e)return;const n=this.getElementHeight(e);t.classList.add("cky-app-accordion-open"),e.style.height=n,window.setTimeout((function(){e.style.height="",e.style.overflow="visible"}),200)}}),this.activeIndex=this.expandedIndex},getElementHeight(t){t.style.display="block";const e=t.scrollHeight+"px";return t.style.display="",e},getActiveAccordions(){return document.querySelectorAll(`[data-cky-toggle="cky-${this.name}-accordion-${this.expandedIndex}"]`)},isExpanded(){return this.expandedIndex===this.activeIndex},closeAccordion(){null!==this.activeIndex&&(document.querySelectorAll(`.cky-app-accordion-open[data-cky-toggle="cky-${this.name}-accordion-${this.activeIndex}"]`).forEach(t=>{t&&this.hideAccordion(t)}),this.activeIndex=null)}}},i=a,r=n("2877"),c=Object(r["a"])(i,o,s,!1,null,null,null);e["a"]=c.exports},b02b:function(t,e,n){"use strict";var o=function(){var t=this,e=t._self._c;return e("li",{staticClass:"cky-app-accordion-item",attrs:{"data-cky-toggle":`cky-${t.Accordion.name}-accordion-${this.index}`}},[e("div",{staticClass:"cky-app-accordion-trigger",on:{click:t.open}},[t._t("cky-accordion-trigger")],2),e("transition",{attrs:{name:"cky-app-accordion"}},[e("div",{staticClass:"cky-app-accordion-content"},[e("div",{staticClass:"cky-app-accordion-content-inner"},[t._t("cky-accordion-content")],2)])])],1)},s=[],a={props:{visible:{type:Boolean,default:!1}},inject:["Accordion","watchAccordion"],data(){return{index:null}},methods:{open(){this.watchAccordion(this.index)}},created(){this.index=this.Accordion.count++},mounted(){this.visible&&this.watchAccordion(this.index)}},i=a,r=n("2877"),c=Object(r["a"])(i,o,s,!1,null,null,null);e["a"]=c.exports},b5ae:function(t,e,n){"use strict";n("17f4")},b920:function(t,e,n){},bc64:function(t,e,n){},be7c:function(t,e,n){},c068:function(t,e,n){"use strict";n.d(e,"a",(function(){return d}));var o=n("f9c4"),s=n("87ea");let a="",i=!1,r="hidden",c="visibilitychange";const l=!!window.ckyGlobals.multilingual,u=window.ckyGlobals.pluginVersion,d={components:{},computed:{connected(){let t=Object(s["e"])("account");return t.connected}},mounted(){window.addEventListener("message",this.listenMessage)},beforeDestroy(){window.removeEventListener("message",this.listenMessage),window.removeEventListener("message",this.listenForUpgrade)},methods:{reload(){window.location.reload()},async afterConnection(t=!1,e="plugin"){!0===t?(this.$root.$emit("connected",e),"plugin"===e&&await this.cloudSync(),await Object(s["j"])(),await Object(s["g"])(!0),!0===i&&(window.removeEventListener("message",this.listenMessage),window.addEventListener("message",this.listenForUpgrade),this.attachVisibilityAPI()),this.$root.$emit("afterSyncing")):console.log("Failed to sync data to the web app")},async listenMessage(t){if(!a||a!==this._uid)return;let e=t.origin&&t.origin.replace(/^https?:\/\//,"")||"",n=window.ckyGlobals.webApp.url.replace(/^https?:\/\//,"");if(e===n){if(!t.data)return;const e=JSON.parse(t.data);this.connected?this.upgradePlan(e):this.connect(e)}},async listenForUpgrade(t){if(!a||a!==this._uid)return;let e=t.origin&&t.origin.replace(/^https?:\/\//,"")||"",n=window.ckyGlobals.webApp.url.replace(/^https?:\/\//,"");if(e===n){if(!t.data)return;const e=JSON.parse(t.data);this.upgradePlan(e)}},async connect(t){this.$root.$emit("afterConnection");let e=!1;if(!t.email)return;let n=Object(s["e"])("account"),o=Object(s["e"])("api"),a=Object(s["e"])("onboarding");a.step=2,Object(s["m"])("onboarding",a),o.token=t.website_token,n.website_id=t.website_id,n.website_key=t.website_key,n.plan=t.slug,n.status=!0,n.connected=!0,n.email=t.email,Object(s["m"])("account",n),Object(s["m"])("api",o),e=await Object(s["k"])();let i=t.priority||"plugin";this.afterConnection(e,i)},async upgradePlan(t){let e=!(!t.upgrade||"success"!==t.upgrade);e&&(this.$router.redirectToDashboard(this.$route.name),await Object(s["g"])(!0))},connectToApp(t=!1,e=""){this.$root.$emit("beforeConnection"),a=this._uid;let n="";n=!0===t?this.getLoginUrl(!1,e):this.getSignupUrl();try{let t=window.open(n,"_blank");t.focus()}catch(o){alert("Your browser blocked the authorization window from opening. Please check your popup settings.")}},upgrade(){a=this._uid;try{let t=window.open(this.getUpgradeUrl(),"_blank");t.focus()}catch(t){alert("Your browser blocked the authorization window from opening. Please check your popup settings.")}},connectToPaidPlan(t,e){a=this._uid,i=!0;try{const n=window.open(this.getCheckoutUrl(t,e),"_blank");n.focus()}catch(n){alert("Your browser blocked the authorization window from opening. Please check your popup settings.")}},upgradeSignup(){a=this._uid,i=!0;try{let t=window.open(this.getUpgradeSignupUrl(),"_blank");t.focus()}catch(t){alert("Your browser blocked the authorization window from opening. Please check your popup settings.")}},async disconnect(){await this.resetAppToken();let t=Object(s["e"])("account");t.connected=!1,t.email="",t.plan="free",t.website_id="",t.website_key="",Object(s["m"])("account",t),await Object(s["k"])(),await Object(s["i"])(),this.$root.$emit("afterDisconnect")},getSignupUrl(){let t=window.ckyGlobals.webApp.signUpUrl,e=new URL(t);e.searchParams.append("platform","wordpress"),e.searchParams.append("mode","register"),e.searchParams.append("website",window.ckyGlobals.site.url),e.searchParams.append("multilingual",l);const n=`WPCY${u}a`;return e.searchParams.append("ref",n),e},getLoginUrl(t=!1,e=""){let n=t?"upgrade":"connect",o=window.ckyGlobals.webApp.loginUrl,s=new URL(o);return s.searchParams.append("platform","wordpress"),s.searchParams.append("mode",n),s.searchParams.append("website",window.ckyGlobals.site.url),s.searchParams.append("multilingual",l),t&&s.searchParams.append("upgrade_source","cypluginupgrade"),e&&s.searchParams.append("feature",e),s},getUpgradeUrl(){const t=Object(s["e"])("account");let e=window.ckyGlobals.webApp.loginUrl,n=new URL(e);return n.searchParams.append("platform","wordpress"),n.searchParams.append("mode","upgrade"),n.searchParams.append("website",t.website_id),n.searchParams.append("upgrade_source","cypluginupgrade"),n},getUpgradeSignupUrl(){let t=window.ckyGlobals.webApp.pricingUrl,e=new URL(t);e.searchParams.append("platform","wordpress"),e.searchParams.append("mode","upgrade"),e.searchParams.append("website",window.ckyGlobals.site.url);const n=`WPCY${u}e`;return e.searchParams.append("ref",n),e},getCheckoutUrl(t,e){const n=window.ckyGlobals.webApp.checkoutUrl;let o=new URL(n);o.searchParams.append("platform","wordpress"),o.searchParams.append("mode","upgrade"),o.searchParams.append("website",window.ckyGlobals.site.url),o.searchParams.append("plan",t),o.searchParams.append("currency",e);let s=t.split("-")[0];s="basic"===s?"b":"pro"===s?"c":"d";const a=`WPCY${u}${s}`;return o.searchParams.append("ref",a),o},async saveToken(){let t=Object(s["e"])("account");t.connected=!0,Object(s["m"])("account",t),await Object(s["k"])()},async cloudSync(t="plugin"){try{return await o["a"].post({path:"/settings/sync",data:{priority:t}})}catch(e){console.log(e)}},async resetAppToken(){try{return await o["a"].post({path:"/settings/disconnect"})}catch(t){console.log(t)}},attachVisibilityAPI(){"undefined"!==typeof document.hidden?(r="hidden",c="visibilitychange"):"undefined"!==typeof document.msHidden?(r="msHidden",c="msvisibilitychange"):"undefined"!==typeof document.webkitHidden&&(r="webkitHidden",c="webkitvisibilitychange"),document.addEventListener(c,this.handleVisibilityChange,!1)},async handleVisibilityChange(){document[r]||await Object(s["g"])()}}}},c4aa:function(t,e,n){"use strict";var o=n("07a4"),s=n("f9c4"),a=n("8259"),i=n("3840");let r=i["a"];const c={getBanners:async function(){let t=o["a"].state.banners.items||await o["a"].dispatch("banners/reInit");return t=JSON.parse(JSON.stringify(o["a"].state.banners.items)),t},getCurrentBanner:async function(){let t=o["a"].state.banners.current;if(t.id>0)return t;try{let e=await this.getBanners();return"object"===typeof e&&(t=e.find((function(t){return 1==t.default})),t||(t=e[0])),t?(await o["a"].dispatch("banners/setCurrentBanner",t),o["a"].state.banners.current):await this.getDefault()}catch(e){console.log(e)}return t},getActiveBanner:async function(){let t={},e=await this.getBanners();return"object"===typeof e&&(t=e.find((function(t){return 1==t.status})),t||(t=e[0])),t&&await o["a"].dispatch("banners/setCurrentBanner",t),t},getBannerById:async function(t){let e={},n=await this.getBanners();return"object"===typeof n&&t&&t>0&&(e=n.find((function(e){return e.id==t}))),e},getBannerByLaw:async function(t){let e={},n=await this.getBanners();return"object"===typeof n&&t&&(e=n.find((function(e){return e.properties.settings.applicableLaw==t}))),e},getBannerBySlug:async function(t){let e={},n=await this.getBanners();return"object"===typeof n&&t&&""!==t&&(e=n.find((function(e){return e.slug==t}))),e},setCurrent:async function(t){t&&await o["a"].dispatch("banners/setCurrentBanner",t)},getDefault:async function(){let t={name:"",properties:{settings:{},config:{}}};await this.loadConfigs();const e=o["a"].state.banners.configs;return t.properties.config=e.gdpr.config,t.properties.settings=e.gdpr.settings,t},save:async function(){let t=o["a"].state.languages.current,e=await this.getCurrentBanner();return e.id?await o["a"].dispatch("banners/saveBanner",{banner:e,params:{language:t}}):await this.createBanner(e)},createBanner:async function(t){let e=!1;return await s["a"].post({path:"/banners",data:t}).then(t=>{t.id&&(e=!0)}),!0===e&&await o["a"].dispatch("banners/reInit"),e},deleteBanner:async function(t){if(t){let e=!1;return await s["a"].delete({path:"/banners/"+t}).then(t=>{t&&(e=!0)}),!0===e&&await o["a"].dispatch("banners/reInit"),e}},updateBanner:async function(t){let e=t.id;if(e){let n=!1;return await s["a"].post({path:"/banners/"+e,data:t}).then(t=>{t&&(n=!0)}),!0===n&&await o["a"].dispatch("banners/reInit"),n}},generatePreview:async function(){await a["a"].generate()},clearTemplate:function(){a["a"].clearTemplate()},showPreview:async function(t=!1){await a["a"].showPreview(t)},forceShowPreview:async function(){a["a"].forceShowPreview()},forceRerenderPreview:async function(){await a["a"].forceRerenderPreview()},hidePreview:function(){a["a"].hidePreview()},closePreview:function(t=!0){a["a"].closePreview(t)},getLayouts:function(t=""){const e=[{type:"box",title:r.__("Box","cookie-law-info"),positions:["bottom-left","bottom-right","top-left","top-right"],default:"bottom-left"},{type:"classic",title:r.__("Classic","cookie-law-info"),positions:["bottom","top"],default:"bottom"},{type:"banner",title:r.__("Banner","cookie-law-info"),positions:["bottom","top"],default:"bottom"},{type:"popup",title:r.__("Popup","cookie-law-info"),positions:["center"],default:"center"}].filter(e=>(!["popup","classic"].includes(e.type)||"ccpa"!==t)&&("classic"!==e.type||"gdpr"!==t));return e},getPreferenceLayouts:function(t=""){const e=[{type:"popup",title:r.__("Center","cookie-law-info"),positions:[],default:""},{type:"sidebar",title:r.__("Sidebar","cookie-law-info"),positions:["left","right"],default:"left"},{type:"pushdown",title:r.__("Push down","cookie-law-info"),positions:[],default:""}].filter(e=>"pushdown"!==e.type||"ccpa"!==t);return e},getContentSections:function(t=""){let e=[];if(""!==t)switch(t){case"ccpa":e=[{id:"notice"},{id:"opt-out-center"},{id:"cookie-list"},{id:"revisit-consent"},{id:"blocked-content"}];break;default:e=[{id:"notice"},{id:"preference"},{id:"cookie-list"},{id:"revisit-consent"},{id:"blocked-content"}];break}return e},loadPresets:async function(){const t=o["a"].state.banners.current,e=t&&t.properties.settings.versionID||"6.0.0";o["a"].state.banners.presets.length<=0&&await s["a"].get({path:"/banners/presets/",params:{ver:e}}).then(t=>{t&&o["a"].dispatch("banners/setPresets",t)})},loadConfigs:async function(){let t=!1,e=[];return o["a"].state.banners.configs.length<=0&&(e=await s["a"].get({path:"/banners/configs/"}).then(e=>(e&&(t=!0),e)),!0===t&&await o["a"].dispatch("banners/setConfigs",e)),o["a"].state.banners.configs},resetConfigs:function(t="gdpr"){const e=o["a"].state.banners.current,n=o["a"].state.banners.configs,s=n[t]&&n[t]||n["gdpr"]&&n["gdpr"]||[],a=s.config,i=s.settings;e.properties.config=a,e.properties.settings.type=i.type},checkForChanges:function(t,e){if(t instanceof Object&&e instanceof Object){for(const n in e)if(t[n]&&this.checkForChanges(t[n],e[n]))return!0}else if(t.toUpperCase()!==e.toUpperCase())return!0;return!1},resetTheme:function(){const t=o["a"].state.banners.current;if(!t)return!1;const e=t.properties.settings.theme,n=o["a"].state.banners.presets.find(t=>t.name==e);if(!n)return!1;const s=this.checkForChanges(t.properties.config,n.settings);!0===s&&(o["a"].state.banners.current.properties.settings.theme="custom")},resetPreset(){const t=o["a"].state.banners.current;if(!t)return!1;const e=t.properties.settings.theme,n=o["a"].state.banners.presets.find(t=>t.name==e);if(!n)return!1;const s=n.settings;t.properties.config=this.arrayReplaceRecursive(t.properties.config,s)},arrayReplaceRecursive(t){let e={},n=0,o="",s=arguments.length;if(s<2)throw new Error("There should be at least 2 arguments passed to arrayReplaceRecursive()");for(o in t)e[o]=t[o];for(n=1;n<s;n++)for(o in arguments[n])e[o]&&"object"===typeof e[o]?e[o]=this.arrayReplaceRecursive(e[o],arguments[n][o]):e[o]=arguments[n][o];return e},loadTemplate:async function(){try{await a["a"].getTemplate(!0)}catch(t){console.error(t)}},updateContentPreview:async function(){o["a"].state.banners.preview?await c.showPreview(!0):(await a["a"].resetTemplate(),await c.loadTemplate())},async bulkUpdate({clear:t=!0}={}){let e=o["a"].state.banners.items;const n=o["a"].state.banners.current;return e.forEach((function(t,o){t.id===n.id&&(t=n,e[o]=t)})),await s["a"].post({path:"/banners/bulk",data:{banners:e},params:{clear:t}}).then(async t=>{t.length>0&&await o["a"].dispatch("banners/reInit")})},async toggleBanner(t=!1){const e=o["a"].state.banners.current;if(!t||t===e.id)return;let n=await this.getBanners();n.forEach((function(t,o){t.id===e.id&&(t=e),t.status=!t.status,n[o]=t})),await o["a"].dispatch("banners/setBanners",n);const s=await this.getActiveBanner();await o["a"].dispatch("banners/setCurrentBanner",s)},hasErrors(){const t=o["a"].state.banners.errors;return!!Object.keys(t).length},setErrors(t){o["a"].dispatch("banners/setErrors",t)},reset(){o["a"].state.banners.items=!1}};e["a"]=c},c702:function(t,e,n){"use strict";var o=function(){var t=this,e=t._self._c;return t.loading?t._e():e("div",{staticClass:"cky-tab-content-accordion",class:[{"cky-app-rtl":t.isRTLLanguage(t.language)}]},[e("cky-accordion",{attrs:{type:"boxed"}},t._l(t.contentSections,(function(n){return e(n.id,{key:n.id,tag:"component",attrs:{content:t.content,translate:t.translate,disabled:t.disabled,language:t.language}})})),1)],1)},s=[],a=n("9e47"),i=n("1f3d"),r=n("c4aa"),c=n("b02b"),l=n("a9f4"),u=function(){var t=this,e=t._self._c;return e("cky-accordion-item",{directives:[{name:"show",rawName:"v-show",value:t.banner.properties.config.notice.status,expression:"banner.properties.config.notice.status"}],attrs:{visible:t.translate}},[e("template",{slot:"cky-accordion-trigger"},[e("label",{staticClass:"cky-app-accordion-title"},[t._v(t._s(t.$i18n.__("Cookie Notice","cookie-law-info")))])]),e("template",{slot:"cky-accordion-content"},[e("div",{staticClass:"cky-form-section cky-form-section-group"},[e("div",{staticClass:"cky-form-header"}),e("div",{staticClass:"cky-form-content"},[e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[e("h6",[t._v(t._s(t.$i18n.__("Title","cookie-law-info")))])]),e("div",{staticClass:"cky-col-9"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.content.notice.elements.title,expression:"content.notice.elements.title"}],staticClass:"cky-form-control",attrs:{type:"text",disabled:t.disabled},domProps:{value:t.content.notice.elements.title},on:{input:function(e){e.target.composing||t.$set(t.content.notice.elements,"title",e.target.value)}}})])]),e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[e("h6",[t._v(t._s(t.$i18n.__("Message","cookie-law-info")))])]),e("div",{staticClass:"cky-col-9"},[e("wp-editor",{key:`${t.banner.id}-${t.key}`,attrs:{disabled:t.disabled,language:t.language},model:{value:t.content.notice.elements.description,callback:function(e){t.$set(t.content.notice.elements,"description",e)},expression:"content.notice.elements.description"}})],1)]),t.translate?t._e():e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("div",{staticClass:"cky-col-3"}),e("div",{staticClass:"cky-col-9"},[e("div",{staticClass:"cky-row cky-justify-between"},[e("div",{staticClass:"cky-col"},[e("label",[t._v(t._s(t.$i18n.__("Background","cookie-law-info")))]),e("cky-colors",{attrs:{color:t.banner.properties.config.notice.styles["background-color"],disabled:t.disabled},model:{value:t.banner.properties.config.notice.styles["background-color"],callback:function(e){t.$set(t.banner.properties.config.notice.styles,"background-color",e)},expression:"\n\t\t\t\t\t\t\t\t\t\tbanner.properties.config.notice.styles['background-color']\n\t\t\t\t\t\t\t\t\t"}})],1),e("div",{staticClass:"cky-col"},[e("label",[t._v(t._s(t.$i18n.__("Border","cookie-law-info")))]),e("cky-colors",{attrs:{color:t.banner.properties.config.notice.styles["border-color"],disabled:t.disabled},model:{value:t.banner.properties.config.notice.styles["border-color"],callback:function(e){t.$set(t.banner.properties.config.notice.styles,"border-color",e)},expression:"\n\t\t\t\t\t\t\t\t\t\tbanner.properties.config.notice.styles['border-color']\n\t\t\t\t\t\t\t\t\t"}})],1),e("div",{staticClass:"cky-col"},[e("label",[t._v(t._s(t.$i18n.__("Title","cookie-law-info")))]),e("cky-colors",{attrs:{color:t.banner.properties.config.notice.elements.title.styles.color,disabled:t.disabled},model:{value:t.banner.properties.config.notice.elements.title.styles.color,callback:function(e){t.$set(t.banner.properties.config.notice.elements.title.styles,"color",e)},expression:"\n\t\t\t\t\t\t\t\t\t\tbanner.properties.config.notice.elements.title.styles\n\t\t\t\t\t\t\t\t\t\t\t.color\n\t\t\t\t\t\t\t\t\t"}})],1),e("div",{staticClass:"cky-col"},[e("label",[t._v(t._s(t.$i18n.__("Message","cookie-law-info")))]),e("cky-colors",{attrs:{color:t.banner.properties.config.notice.elements.description.styles.color,disabled:t.disabled},model:{value:t.banner.properties.config.notice.elements.description.styles.color,callback:function(e){t.$set(t.banner.properties.config.notice.elements.description.styles,"color",e)},expression:"\n\t\t\t\t\t\t\t\t\t\tbanner.properties.config.notice.elements.description\n\t\t\t\t\t\t\t\t\t\t\t.styles.color\n\t\t\t\t\t\t\t\t\t"}})],1)])])])])]),e("cky-banner-btn",{attrs:{name:"accept-all",properties:t.banner.properties.config.notice.elements.buttons.elements.accept,content:t.content.notice.elements.buttons.elements.accept,title:t.$i18n.__("“Accept All” button","cookie-law-info"),toggle:!1,translate:t.translate,disabled:t.disabled},model:{value:t.content.notice.elements.buttons.elements.accept,callback:function(e){t.$set(t.content.notice.elements.buttons.elements,"accept",e)},expression:"content.notice.elements.buttons.elements.accept"}}),e("cky-banner-btn",{attrs:{name:"reject-all",properties:t.banner.properties.config.notice.elements.buttons.elements.reject,content:t.content.notice.elements.buttons.elements.reject,title:t.$i18n.__("“Reject All” button","cookie-law-info"),translate:t.translate,disabled:t.disabled},model:{value:t.content.notice.elements.buttons.elements.reject,callback:function(e){t.$set(t.content.notice.elements.buttons.elements,"reject",e)},expression:"content.notice.elements.buttons.elements.reject"}}),e("cky-banner-btn",{attrs:{name:"settings",properties:t.banner.properties.config.notice.elements.buttons.elements.settings,content:t.content.notice.elements.buttons.elements.settings,title:t.$i18n.__("“Customise” button","cookie-law-info"),translate:t.translate,disabled:t.disabled},model:{value:t.content.notice.elements.buttons.elements.settings,callback:function(e){t.$set(t.content.notice.elements.buttons.elements,"settings",e)},expression:"content.notice.elements.buttons.elements.settings"}}),e("cky-banner-btn",{attrs:{name:"readmore-link",properties:t.banner.properties.config.notice.elements.buttons.elements.readMore,content:t.content.notice.elements.buttons.elements.readMore,title:t.$i18n.__("“Cookie Policy” link","cookie-law-info"),translate:t.translate,disabled:t.disabled,supportedLaws:["gdpr","ccpa"],disabledFields:["background","border"]},model:{value:t.content.notice.elements.buttons.elements.readMore,callback:function(e){t.$set(t.content.notice.elements.buttons.elements,"readMore",e)},expression:"content.notice.elements.buttons.elements.readMore"}},[e("template",{slot:"cky-btn-meta"},[e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[t._v(t._s(t.$i18n.__("URL","cookie-law-info")))]),e("div",{staticClass:"cky-col-9"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.content.notice.elements.privacyLink,expression:"content.notice.elements.privacyLink"}],staticClass:"cky-form-control",attrs:{type:"text",placeholder:"Enter the URL to your cookie policy or privacy page",disabled:t.disabled},domProps:{value:t.content.notice.elements.privacyLink},on:{input:function(e){e.target.composing||t.$set(t.content.notice.elements,"privacyLink",e.target.value)}}})])])])],2),e("cky-banner-btn",{attrs:{name:"donotsell-link",properties:t.banner.properties.config.notice.elements.buttons.elements.donotSell,content:t.content.notice.elements.buttons.elements.donotSell,title:t.$i18n.__("“Do Not Sell” link","cookie-law-info"),toggle:!1,supportedLaws:["ccpa"],translate:t.translate,disabled:t.disabled,disabledFields:["background","border"]},model:{value:t.content.notice.elements.buttons.elements.donotSell,callback:function(e){t.$set(t.content.notice.elements.buttons.elements,"donotSell",e)},expression:"content.notice.elements.buttons.elements.donotSell"}}),t.translate?t._e():e("div",{staticClass:"cky-form-section cky-form-section-group"},[e("div",{staticClass:"cky-form-header"}),e("div",{staticClass:"cky-form-content"},[e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("div",{staticClass:"cky-col-3"},[e("label",{staticClass:"cky-zero-padding cky-col-label",attrs:{for:"cky-toggle-banner-close-btn"}},[t._v(t._s(t.$i18n.__("Close [X] button","cookie-law-info"))),"gdpr"===t.appliedLaw?e("cky-popper",{attrs:{content:t.$i18n.__("Enable the close button to let users close the banner and continue browsing the site without being tracked. A close button is required by the Italian DPA.","cookie-law-info")}}):t._e()],1)]),e("div",{staticClass:"cky-col-5"},[e("label",{staticClass:"cky-toggle",attrs:{for:"cky-toggle-banner-close-btn"}},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.banner.properties.config.notice.elements.closeButton.status,expression:"\n\t\t\t\t\t\t\t\t\tbanner.properties.config.notice.elements.closeButton.status\n\t\t\t\t\t\t\t\t"}],attrs:{type:"checkbox",id:"cky-toggle-banner-close-btn"},domProps:{checked:Array.isArray(t.banner.properties.config.notice.elements.closeButton.status)?t._i(t.banner.properties.config.notice.elements.closeButton.status,null)>-1:t.banner.properties.config.notice.elements.closeButton.status},on:{change:function(e){var n=t.banner.properties.config.notice.elements.closeButton.status,o=e.target,s=!!o.checked;if(Array.isArray(n)){var a=null,i=t._i(n,a);o.checked?i<0&&t.$set(t.banner.properties.config.notice.elements.closeButton,"status",n.concat([a])):i>-1&&t.$set(t.banner.properties.config.notice.elements.closeButton,"status",n.slice(0,i).concat(n.slice(i+1)))}else t.$set(t.banner.properties.config.notice.elements.closeButton,"status",s)}}}),e("span",{staticClass:"cky-toggle-slider",attrs:{id:"cky-toggle-banner-close-btn-text","aria-hidden":"true"}})])])])])]),t.translate?t._e():e("div",{staticClass:"cky-form-section"},[e("div",{staticClass:"cky-form-content"},[e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("div",{staticClass:"cky-col-3"},[e("label",{staticClass:"cky-zero-padding cky-col-label cky-action-link",attrs:{for:"cky-customize-branding"}},[t._v(t._s(t.$i18n.__("Custom logo","cookie-law-info"))+" "),e("img",{staticClass:"cky-crown-img",attrs:{src:n("7cd5"),alt:"crown-icon"}})])]),e("div",{staticClass:"cky-col-5"},[e("label",{staticClass:"cky-toggle",attrs:{for:"cky-customize-branding"}},[e("input",{attrs:{type:"checkbox",id:"cky-customize-branding"},domProps:{value:!1},on:{click:function(e){return e.preventDefault(),t.showCustomLogoModal()}}}),e("span",{staticClass:"cky-toggle-slider",attrs:{id:"cky-customize-branding-toggle","aria-hidden":"true"}})])])])])]),e("cky-connect-modal",{ref:"ckyCustomLogoModal",attrs:{availablePlan:"ultimate",feature:"custom_branding"},scopedSlots:t._u([{key:"title",fn:function(){return[e("img",{attrs:{src:n("f405"),alt:"customlog"}})]},proxy:!0},{key:"message",fn:function(){return[e("div",{staticClass:"cky-feature-text"},[t._v(" "+t._s(t.$i18n.__("Add a custom logo on the banner to match your brand’s unique identity","cookie-law-info"))+" ")]),e("div",{staticClass:"cky-available-wrapper"},[e("span",{staticClass:"cky-available-text",domProps:{innerHTML:t._s(t.$i18n.__("Available in: <b>Ultimate plan</b>","cookie-law-info"))}})])]},proxy:!0}])})],1)],2)},d=[],p=function(){var t=this,e=t._self._c;return e("div",{ref:"colorpicker",staticClass:"cky-color-picker cky-align-center"},[e("div",{staticClass:"cky-color-picker-container"},[e("div",{staticClass:"cky-current-color",style:"background-color: "+t.colorValue,on:{click:function(e){return t.togglePicker()}}}),t.displayPicker?e("chrome-picker",{attrs:{value:t.colors,disableAlpha:!0},on:{input:t.updateFromPicker}}):t._e()],1),e("div",{staticClass:"cky-color-picker-input cky-center",on:{click:function(e){return t.togglePicker()}}},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.colorValue,expression:"colorValue"}],staticClass:"cky-form-control cky-input-color",attrs:{type:"text"},domProps:{value:t.colorValue},on:{focus:function(e){return t.showPicker()},input:[function(e){e.target.composing||(t.colorValue=e.target.value)},t.updateFromInput]}})])])},h=[],g=n("c345"),y={name:"CkyColors",components:{"chrome-picker":g["Chrome"]},props:["color"],data(){return{colors:{hex:"#000000"},colorValue:"",displayPicker:!1}},mounted(){this.setColor(this.color||"#000000")},methods:{setColor(t){this.updateColors(t),this.colorValue=t},updateColors(t){if("#"==t.slice(0,1))this.colors=Object.assign({},this.colors,{hex:t});else if("rgba"==t.slice(0,4)){const e=t.replace(/^rgba?\(|\s+|\)$/g,"").split(","),n="#"+((1<<24)+(parseInt(e[0])<<16)+(parseInt(e[1])<<8)+parseInt(e[2])).toString(16).slice(1);this.colors=Object.assign({},this.colors,{hex:n,a:e[3]})}},showPicker(){document.addEventListener("click",this.documentClick),this.displayPicker=!0},hidePicker(){document.removeEventListener("click",this.documentClick),this.displayPicker=!1},togglePicker(){this.displayPicker?this.hidePicker():this.showPicker()},updateFromInput(){this.updateColors(this.colorValue)},updateFromPicker(t){this.colors=Object.assign({},this.colors,t),1==t.rgba.a?this.colorValue=t.hex:this.colorValue=`rgba(${t.rgba.r},${t.rgba.g},${t.rgba.b},${t.rgba.a})`},documentClick(t){var e=this.$refs.colorpicker,n=t.target;e===n||e.contains(n)||this.hidePicker()}},watch:{color(t){this.setColor(t),this.$emit("changeColor",t)},colorValue(t){t&&(this.updateColors(t),this.$emit("input",t))}}},f=y,m=n("2877"),k=Object(m["a"])(f,p,h,!1,null,null,null),b=k.exports,C=n("df44"),w=function(){var t=this,e=t._self._c;return t.supportedLaws.includes(t.appliedLaw)?e("div",{staticClass:"cky-form-section"},[e("div",{staticClass:"cky-form-header"},[t.toggle&&!t.translate?e("div",{staticClass:"cky-row"},[e("div",{staticClass:"cky-col-3"},[e("h5",{staticClass:"cky-form-heading"},[t._v(" "+t._s(t.title)+" ")])]),e("div",{staticClass:"cky-col-9"},[e("label",{staticClass:"cky-toggle",attrs:{for:"cky-toggle-"+t.name}},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.properties.status,expression:"properties.status"}],attrs:{type:"checkbox",id:"cky-toggle-"+t.name,disabled:t.disabled},domProps:{checked:Array.isArray(t.properties.status)?t._i(t.properties.status,null)>-1:t.properties.status},on:{change:function(e){var n=t.properties.status,o=e.target,s=!!o.checked;if(Array.isArray(n)){var a=null,i=t._i(n,a);o.checked?i<0&&t.$set(t.properties,"status",n.concat([a])):i>-1&&t.$set(t.properties,"status",n.slice(0,i).concat(n.slice(i+1)))}else t.$set(t.properties,"status",s)}}}),e("span",{staticClass:"cky-toggle-slider",attrs:{id:"cky-toggle-"+t.name,"aria-hidden":"true"}})])])]):e("div",{staticClass:"cky-row"},[e("div",{staticClass:"cky-col-12"},[e("h5",{staticClass:"cky-form-heading"},[t._v(" "+t._s(t.title)+" ")])])])]),e("div",{staticClass:"cky-form-content"},[e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[t._v(t._s(t.$i18n.__("Label","cookie-law-info")))]),e("div",{staticClass:"cky-col-9"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.bannerContent,expression:"bannerContent"}],staticClass:"cky-form-control",attrs:{type:"text",disabled:t.disabled},domProps:{value:t.bannerContent},on:{input:function(e){e.target.composing||(t.bannerContent=e.target.value)}}})])]),t.translate?t._e():e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("div",{staticClass:"cky-col-3"}),e("div",{staticClass:"cky-col-9"},[e("div",{staticClass:"cky-row"},[t.properties.styles["background-color"]&&!t.disabledFields.includes("background")?e("div",{staticClass:"cky-col-3"},[e("label",{},[t._v(t._s(t.$i18n.__("Background","cookie-law-info")))]),e("cky-colors",{attrs:{color:t.properties.styles["background-color"],disabled:t.disabled},model:{value:t.properties.styles["background-color"],callback:function(e){t.$set(t.properties.styles,"background-color",e)},expression:"properties.styles['background-color']"}})],1):t._e(),t.properties.styles["border-color"]&&!t.disabledFields.includes("border")?e("div",{staticClass:"cky-col-3"},[e("label",{},[t._v(t._s(t.$i18n.__("Border","cookie-law-info")))]),e("cky-colors",{attrs:{color:t.properties.styles["border-color"],disabled:t.disabled},model:{value:t.properties.styles["border-color"],callback:function(e){t.$set(t.properties.styles,"border-color",e)},expression:"properties.styles['border-color']"}})],1):t._e(),t.properties.styles.color&&!t.disabledFields.includes("color")?e("div",{staticClass:"cky-col-3"},[e("label",{},[t._v(t._s(t.$i18n.__("Text","cookie-law-info")))]),e("cky-colors",{attrs:{color:t.properties.styles.color,disabled:t.disabled},model:{value:t.properties.styles.color,callback:function(e){t.$set(t.properties.styles,"color",e)},expression:"properties.styles.color"}})],1):t._e()])])]),t._t("cky-btn-meta")],2)]):t._e()},v=[],_={name:"CkyBannerBtn",props:{properties:{type:Object,default(){return{}}},content:{type:String,default:""},title:{type:String,default:""},name:{type:String,default:""},toggle:{type:Boolean,default:!0},supportedLaws:{type:Array,default(){return["gdpr"]}},translate:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},disabledFields:{type:Array,default(){return[]}}},data(){return{bannerContent:""}},components:{CkyColors:b},computed:{appliedLaw(){return this.$store.state.banners.current.properties.settings.applicableLaw}},mounted(){this.bannerContent=this.content},watch:{bannerContent(t){this.$emit("input",t)},content(t){this.bannerContent=t}}},x=_,$=Object(m["a"])(x,w,v,!1,null,null,null),L=$.exports,P=n("2f62"),S=n("9947"),M={name:"Notice",components:{CkyAccordionItem:c["a"],CkyColors:b,WpEditor:C["a"],CkyBannerBtn:L,CkyConnectModal:S["a"]},props:{content:Object,translate:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},language:{type:String,default:"en"}},data(){return{key:Math.random().toString(),loaded:!1}},methods:{savePreferenceStyles(){const t=this.banner.properties.config;t.preferenceCenter.styles["background-color"]=t.notice.styles["background-color"],t.preferenceCenter.styles["border-color"]=t.notice.styles["border-color"],t.preferenceCenter.styles.color=t.preferenceCenter.elements.title.styles.color=t.preferenceCenter.elements.description.styles.color=t.preferenceCenter.elements.categories.elements.title.styles.color=t.preferenceCenter.elements.categories.elements.description.styles.color=t.categoryPreview.elements.title.styles.color=t.notice.elements.description.styles.color},showCustomLogoModal(){this.$refs.ckyCustomLogoModal.show()},saveOptoutStyles(){let t=this.banner.properties.config;t.optoutPopup.styles["background-color"]=t.notice.styles["background-color"],t.optoutPopup.styles.color=t.notice.elements.description.styles.color,t.optoutPopup.styles["border-color"]=t.notice.styles["border-color"],t.optoutPopup.styles.color=t.optoutPopup.elements.title.styles.color=t.optoutPopup.elements.description.styles.color=t.optoutPopup.elements.optOption.elements.title.styles.color=t.notice.elements.description.styles.color},toggleDetailSaveButton(){const t=this.banner.properties.config;let e=!0;this.showCategoriesOnFirstLayer&&t.categoryPreview.status&&(e=!1),t.preferenceCenter.elements.buttons.elements.save.status=e}},computed:{banner(){return this.$store.state.banners.current},showCategoriesOnFirstLayer(){return"classic"===this.banner.properties.settings.type},appliedLaw(){return this.banner.properties.settings.applicableLaw},...Object(P["d"])("settings",["info"])},watch:{"banner.properties.config.notice":{handler(){this.savePreferenceStyles(),this.saveOptoutStyles()},deep:!0},"banner.properties.config.categoryPreview":{handler(){this.toggleDetailSaveButton()},deep:!0},banner:{handler(){this.content.preferenceCenter.elements.buttons.elements.accept=this.content.notice.elements.buttons.elements.accept,this.content.preferenceCenter.elements.buttons.elements.reject=this.content.notice.elements.buttons.elements.reject,this.content.optoutPopup.elements.optOption.elements.title=this.content.notice.elements.buttons.elements.donotSell,this.banner.properties.config.preferenceCenter.elements.buttons.elements.accept.styles=this.banner.properties.config.notice.elements.buttons.elements.accept.styles,this.banner.properties.config.preferenceCenter.elements.buttons.elements.reject.styles=this.banner.properties.config.notice.elements.buttons.elements.reject.styles,this.banner.properties.config.preferenceCenter.elements.buttons.elements.reject.status=this.banner.properties.config.notice.elements.buttons.elements.reject.status},deep:!0},language:{handler(){this.key=Math.random().toString()}}},activated(){this.loaded&&(this.key=Math.random().toString()),this.loaded=!0}},O=M,j=Object(m["a"])(O,u,d,!1,null,null,null),A=j.exports,B=function(){var t=this,e=t._self._c;return e("cky-accordion-item",[e("template",{slot:"cky-accordion-trigger"},[e("label",{staticClass:"cky-app-accordion-title"},[t._v(t._s(t.$i18n.__("Preference Centre","cookie-law-info")))])]),e("template",{slot:"cky-accordion-content"},[e("div",{staticClass:"cky-form-section cky-form-section-group"},[e("div",{staticClass:"cky-form-header"}),e("div",{staticClass:"cky-form-content"},[e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[e("h6",[t._v(t._s(t.$i18n.__("Title","cookie-law-info")))])]),e("div",{staticClass:"cky-col-9"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.content.preferenceCenter.elements.title,expression:"content.preferenceCenter.elements.title"}],staticClass:"cky-form-control",attrs:{type:"text",disabled:t.disabled},domProps:{value:t.content.preferenceCenter.elements.title},on:{input:function(e){e.target.composing||t.$set(t.content.preferenceCenter.elements,"title",e.target.value)}}})])]),e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[e("h6",[t._v(" "+t._s(t.$i18n.__("Privacy overview","cookie-law-info"))+" ")])]),e("div",{staticClass:"cky-col-9"},[e("wp-editor",{key:t.key,attrs:{disabled:t.disabled,language:t.language,height:"250"},model:{value:t.content.preferenceCenter.elements.description,callback:function(e){t.$set(t.content.preferenceCenter.elements,"description",e)},expression:"content.preferenceCenter.elements.description"}})],1)]),e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[e("h6",[t._v(t._s(t.$i18n.__("“Show more” button","cookie-law-info")))])]),e("div",{staticClass:"cky-col-9"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.content.preferenceCenter.elements.showMore,expression:"content.preferenceCenter.elements.showMore"}],staticClass:"cky-form-control",attrs:{type:"text",disabled:t.disabled},domProps:{value:t.content.preferenceCenter.elements.showMore},on:{input:function(e){e.target.composing||t.$set(t.content.preferenceCenter.elements,"showMore",e.target.value)}}})])]),e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[e("h6",[t._v(" "+t._s(t.$i18n.__("“Show less” button","cookie-law-info"))+" ")])]),e("div",{staticClass:"cky-col-9"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.content.preferenceCenter.elements.showLess,expression:"content.preferenceCenter.elements.showLess"}],staticClass:"cky-form-control",attrs:{type:"text",disabled:t.disabled},domProps:{value:t.content.preferenceCenter.elements.showLess},on:{input:function(e){e.target.composing||t.$set(t.content.preferenceCenter.elements,"showLess",e.target.value)}}})])])])]),t.isCategoryPreviewDisabled?e("cky-banner-btn",{attrs:{name:"accept-all",properties:t.banner.properties.config.preferenceCenter.elements.buttons.elements.save,content:t.content.preferenceCenter.elements.buttons.elements.save,title:t.$i18n.__("“Save My Preferences” button","cookie-law-info"),toggle:!1,translate:t.translate,disabled:t.disabled},model:{value:t.content.preferenceCenter.elements.buttons.elements.save,callback:function(e){t.$set(t.content.preferenceCenter.elements.buttons.elements,"save",e)},expression:"content.preferenceCenter.elements.buttons.elements.save"}}):e("cky-banner-btn",{attrs:{name:"accept-all",properties:t.banner.properties.config.categoryPreview.elements.buttons.elements.save,content:t.content.categoryPreview.elements.buttons.elements.save,title:t.$i18n.__("“Save My Preferences” button","cookie-law-info"),toggle:!1,translate:t.translate,disabled:t.disabled},model:{value:t.content.categoryPreview.elements.buttons.elements.save,callback:function(e){t.$set(t.content.categoryPreview.elements.buttons.elements,"save",e)},expression:"content.categoryPreview.elements.buttons.elements.save"}})],1)],2)},T=[],H={name:"Preference",components:{CkyAccordionItem:c["a"],WpEditor:C["a"],CkyBannerBtn:L},props:{content:Object,translate:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},language:{type:String,default:"en"}},data(){return{key:Math.random().toString(),loaded:!1}},methods:{savePreferenceStyles(){const t=this.banner.properties.config;t.preferenceCenter.styles["background-color"]=t.notice.styles["background-color"],t.preferenceCenter.styles.color=t.preferenceCenter.elements.title.styles.color=t.preferenceCenter.elements.description.styles.color=t.preferenceCenter.elements.categories.elements.title.styles.color=t.preferenceCenter.elements.categories.elements.description.styles.color=t.notice.elements.description.styles.color}},computed:{banner(){return this.$store.state.banners.current},isCategoryPreviewDisabled(){return this.$store.state.banners.current.properties.config.preferenceCenter.elements.buttons.elements.save.status}},watch:{"banner.properties.config.notice":{handler(){this.savePreferenceStyles()},deep:!0},language:{handler(){this.key=Math.random().toString()}}},activated(){this.loaded&&(this.key=Math.random().toString()),this.loaded=!0}},I=H,V=Object(m["a"])(I,B,T,!1,null,null,null),E=V.exports,N=function(){var t=this,e=t._self._c;return e("cky-accordion-item",[e("template",{slot:"cky-accordion-trigger"},[e("label",{staticClass:"cky-app-accordion-title"},[t._v(t._s(t.$i18n.__("Cookie List","cookie-law-info")))])]),e("template",{slot:"cky-accordion-content"},[e("div",{staticClass:"cky-form-section"},[e("div",{staticClass:"cky-form-header"}),e("div",{staticClass:"cky-form-content"},[t.translate||"ccpa"===t.applicableLaw?t._e():e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("div",{staticClass:"cky-col-3"},[e("label",{staticClass:"cky-zero-padding cky-col-label",attrs:{for:"cky-show-cookie-declaration"}},[e("h6",[t._v(" "+t._s(t.$i18n.__("Show cookie list","cookie-law-info"))+" ")])])]),e("div",{staticClass:"cky-col-5"},[e("label",{staticClass:"cky-toggle",attrs:{for:"cky-show-cookie-declaration"}},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.banner.properties.config.auditTable.status,expression:"banner.properties.config.auditTable.status"}],attrs:{type:"checkbox",id:"cky-show-cookie-declaration",disabled:t.disabled},domProps:{checked:Array.isArray(t.banner.properties.config.auditTable.status)?t._i(t.banner.properties.config.auditTable.status,null)>-1:t.banner.properties.config.auditTable.status},on:{change:function(e){var n=t.banner.properties.config.auditTable.status,o=e.target,s=!!o.checked;if(Array.isArray(n)){var a=null,i=t._i(n,a);o.checked?i<0&&t.$set(t.banner.properties.config.auditTable,"status",n.concat([a])):i>-1&&t.$set(t.banner.properties.config.auditTable,"status",n.slice(0,i).concat(n.slice(i+1)))}else t.$set(t.banner.properties.config.auditTable,"status",s)}}}),e("span",{staticClass:"cky-toggle-slider",attrs:{"aria-hidden":"true"}})])])]),e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[t._v(t._s(t.$i18n.__('"Cookie" label',"cookie-law-info")))]),e("div",{staticClass:"cky-col-9"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.content.auditTable.elements.headers.elements.id,expression:"content.auditTable.elements.headers.elements.id"}],staticClass:"cky-form-control",attrs:{type:"text",disabled:t.disabled},domProps:{value:t.content.auditTable.elements.headers.elements.id},on:{input:function(e){e.target.composing||t.$set(t.content.auditTable.elements.headers.elements,"id",e.target.value)}}})])]),e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[t._v(t._s(t.$i18n.__('"Duration" label',"cookie-law-info")))]),e("div",{staticClass:"cky-col-9"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.content.auditTable.elements.headers.elements.duration,expression:"content.auditTable.elements.headers.elements.duration"}],staticClass:"cky-form-control",attrs:{type:"text",disabled:t.disabled},domProps:{value:t.content.auditTable.elements.headers.elements.duration},on:{input:function(e){e.target.composing||t.$set(t.content.auditTable.elements.headers.elements,"duration",e.target.value)}}})])]),e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[t._v(t._s(t.$i18n.__('"Description" label',"cookie-law-info")))]),e("div",{staticClass:"cky-col-9"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.content.auditTable.elements.headers.elements.description,expression:"\n\t\t\t\t\t\t\t\tcontent.auditTable.elements.headers.elements.description\n\t\t\t\t\t\t\t"}],staticClass:"cky-form-control",attrs:{type:"text",disabled:t.disabled},domProps:{value:t.content.auditTable.elements.headers.elements.description},on:{input:function(e){e.target.composing||t.$set(t.content.auditTable.elements.headers.elements,"description",e.target.value)}}})])]),"ccpa"!==t.applicableLaw?e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[t._v(t._s(t.$i18n.__('"Always Active" label',"cookie-law-info")))]),e("div",{staticClass:"cky-col-9"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.content.preferenceCenter.elements.category.elements.alwaysEnabled,expression:"\n\t\t\t\t\t\t\t\tcontent.preferenceCenter.elements.category.elements\n\t\t\t\t\t\t\t\t\t.alwaysEnabled\n\t\t\t\t\t\t\t"}],staticClass:"cky-form-control",attrs:{type:"text",disabled:t.disabled},domProps:{value:t.content.preferenceCenter.elements.category.elements.alwaysEnabled},on:{input:function(e){e.target.composing||t.$set(t.content.preferenceCenter.elements.category.elements,"alwaysEnabled",e.target.value)}}})])]):t._e(),"ccpa"!==t.applicableLaw?e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[t._v(t._s(t.$i18n.__('"No cookies to display" label',"cookie-law-info")))]),e("div",{staticClass:"cky-col-9"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.content.auditTable.elements.message,expression:"content.auditTable.elements.message"}],staticClass:"cky-form-control",attrs:{type:"text",disabled:t.disabled},domProps:{value:t.content.auditTable.elements.message},on:{input:function(e){e.target.composing||t.$set(t.content.auditTable.elements,"message",e.target.value)}}})])]):t._e()])])])],2)},D=[],Z={name:"PreferenceCenter",props:{content:Object,translate:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},components:{CkyAccordionItem:c["a"]},computed:{banner(){return this.$store.state.banners.current},applicableLaw(){return this.banner.properties.settings.applicableLaw}}},U=Z,R=Object(m["a"])(U,N,D,!1,null,null,null),F=R.exports,G=function(){var t=this,e=t._self._c;return e("cky-accordion-item",[e("template",{slot:"cky-accordion-trigger"},[e("label",{staticClass:"cky-app-accordion-title"},[t._v(t._s(t.$i18n.__("Revisit Consent Button","cookie-law-info")))])]),e("template",{slot:"cky-accordion-content"},[e("div",{staticClass:"cky-form-section"},[t.translate?t._e():e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("div",{staticClass:"cky-col-3"},[e("label",{staticClass:"cky-zero-padding cky-col-label",attrs:{for:"cky-revisit-consent-button"}},[e("h6",[t._v(t._s(t.$i18n.__("Revisit consent button","cookie-law-info")))])])]),e("div",{staticClass:"cky-col-9"},[e("label",{staticClass:"cky-toggle",attrs:{for:"cky-revisit-consent-button"}},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.banner.properties.config.revisitConsent.status,expression:"banner.properties.config.revisitConsent.status"}],attrs:{type:"checkbox",id:"cky-revisit-consent-button",disabled:t.disabled},domProps:{checked:Array.isArray(t.banner.properties.config.revisitConsent.status)?t._i(t.banner.properties.config.revisitConsent.status,null)>-1:t.banner.properties.config.revisitConsent.status},on:{change:function(e){var n=t.banner.properties.config.revisitConsent.status,o=e.target,s=!!o.checked;if(Array.isArray(n)){var a=null,i=t._i(n,a);o.checked?i<0&&t.$set(t.banner.properties.config.revisitConsent,"status",n.concat([a])):i>-1&&t.$set(t.banner.properties.config.revisitConsent,"status",n.slice(0,i).concat(n.slice(i+1)))}else t.$set(t.banner.properties.config.revisitConsent,"status",s)}}}),e("span",{staticClass:"cky-toggle-slider",attrs:{id:"cky-revisit-consent-button","aria-hidden":"true"}})])])]),e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[t._v(t._s(t.$i18n.__("Text on hover","cookie-law-info")))]),e("div",{staticClass:"cky-col-9"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.content.revisitConsent.elements.title,expression:"content.revisitConsent.elements.title"}],staticClass:"cky-form-control",attrs:{type:"text",disabled:t.disabled},domProps:{value:t.content.revisitConsent.elements.title},on:{input:function(e){e.target.composing||t.$set(t.content.revisitConsent.elements,"title",e.target.value)}}})])]),t.translate?t._e():e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[t._v(t._s(t.$i18n.__("Position","cookie-law-info")))]),e("div",{staticClass:"cky-col-9"},[e("div",{staticClass:"cky-form-check-inline"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.banner.properties.config.revisitConsent.position,expression:"banner.properties.config.revisitConsent.position"}],staticClass:"cky-form-check-input",attrs:{type:"radio",id:"cky-revisit-consent-button-position-bottom-left",value:"bottom-left"},domProps:{checked:t._q(t.banner.properties.config.revisitConsent.position,"bottom-left")},on:{change:function(e){return t.$set(t.banner.properties.config.revisitConsent,"position","bottom-left")}}}),e("label",{attrs:{for:"cky-revisit-consent-button-position-bottom-left"}},[t._v(t._s(t.$i18n.__("Left","cookie-law-info")))])]),e("div",{staticClass:"cky-form-check-inline"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.banner.properties.config.revisitConsent.position,expression:"banner.properties.config.revisitConsent.position"}],staticClass:"cky-form-check-input",attrs:{type:"radio",id:"cky-revisit-consent-button-position-bottom-right",value:"bottom-right"},domProps:{checked:t._q(t.banner.properties.config.revisitConsent.position,"bottom-right")},on:{change:function(e){return t.$set(t.banner.properties.config.revisitConsent,"position","bottom-right")}}}),e("label",{attrs:{for:"cky-revisit-consent-button-position-bottom-right"}},[t._v(t._s(t.$i18n.__("Right","cookie-law-info")))])])])]),t.translate?t._e():e("div",{staticClass:"cky-form-group cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[t._v(t._s(t.$i18n.__("Colours","cookie-law-info")))]),e("div",{staticClass:"cky-col-2"},[e("label",{},[t._v(t._s(t.$i18n.__("Background","cookie-law-info")))]),e("cky-colors",{attrs:{color:t.banner.properties.config.revisitConsent.styles["background-color"],disabled:t.disabled},model:{value:t.banner.properties.config.revisitConsent.styles["background-color"],callback:function(e){t.$set(t.banner.properties.config.revisitConsent.styles,"background-color",e)},expression:"banner.properties.config.revisitConsent.styles[\n\t\t'background-color'\n\t\t]\n\t\t"}})],1)]),t.translate?t._e():e("div",{staticClass:"cky-form-section"},[e("div",{staticClass:"cky-form-content"},[e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("div",{staticClass:"cky-col-3"},[e("label",{staticClass:"cky-zero-padding cky-col-label cky-action-link",attrs:{for:"cky-revisit-customize-branding"}},[t._v(t._s(t.$i18n.__("Custom icon","cookie-law-info"))+" "),e("img",{staticClass:"cky-crown-img",attrs:{src:n("7cd5"),alt:"crown-icon"}})])]),e("div",{staticClass:"cky-col-5"},[e("label",{staticClass:"cky-toggle",attrs:{for:"cky-revisit-customize-branding"}},[e("input",{attrs:{type:"checkbox",id:"cky-revisit-customize-branding"},domProps:{value:!1},on:{click:function(e){return e.preventDefault(),t.showRevisitIconModal()}}}),e("span",{staticClass:"cky-toggle-slider",attrs:{id:"cky-revisit-customize-branding-toggle","aria-hidden":"true"}})])])])])]),e("cky-connect-modal",{ref:"CkyRevisitIconModal",attrs:{feature:"revisit_custom_branding",availablePlan:"ultimate"},scopedSlots:t._u([{key:"title",fn:function(){return[e("img",{attrs:{src:n("1381"),alt:"revisiticon"}})]},proxy:!0},{key:"message",fn:function(){return[e("div",{staticClass:"cky-feature-text"},[t._v(" "+t._s(t.$i18n.__("Add a personal flair to your cookie revisit widget with a custom icon","cookie-law-info"))+" ")]),e("div",{staticClass:"cky-available-wrapper"},[e("span",{staticClass:"cky-available-text",domProps:{innerHTML:t._s(t.$i18n.__("Available in: <b>Ultimate plan</b>","cookie-law-info"))}})])]},proxy:!0}])})],1)])],2)},q=[],z={name:"RevisitConsent",props:{content:Object,translate:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},components:{CkyAccordionItem:c["a"],CkyColors:b,CkyConnectModal:S["a"]},methods:{showRevisitIconModal(){this.$refs.CkyRevisitIconModal.show()}},computed:{banner(){return this.$store.state.banners.current}}},Y=z,W=(n("c7b3"),Object(m["a"])(Y,G,q,!1,null,null,null)),J=W.exports,Q=function(){var t=this,e=t._self._c;return e("cky-accordion-item",[e("template",{slot:"cky-accordion-trigger"},[e("label",{staticClass:"cky-app-accordion-title"},[t._v(t._s(t.$i18n.__("Blocked Content","cookie-law-info")))])]),e("template",{slot:"cky-accordion-content"},[e("div",{staticClass:"cky-form-section"},[e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[t._v(t._s(t.$i18n.__("Label","cookie-law-info")))]),e("div",{staticClass:"cky-col-9"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.content.videoPlaceholder.elements.title,expression:"content.videoPlaceholder.elements.title"}],staticClass:"cky-form-control",attrs:{type:"text",disabled:t.disabled},domProps:{value:t.content.videoPlaceholder.elements.title},on:{input:function(e){e.target.composing||t.$set(t.content.videoPlaceholder.elements,"title",e.target.value)}}})])]),t.translate?t._e():e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[t._v(t._s(t.$i18n.__("Colours","cookie-law-info")))]),e("div",{staticClass:"cky-col-2"},[e("label",{},[t._v(t._s(t.$i18n.__("Background","cookie-law-info")))]),e("cky-colors",{attrs:{color:t.banner.properties.config.videoPlaceholder.styles["background-color"],disabled:t.disabled},model:{value:t.banner.properties.config.videoPlaceholder.styles["background-color"],callback:function(e){t.$set(t.banner.properties.config.videoPlaceholder.styles,"background-color",e)},expression:"\n\t\t\t\t\t\t\tbanner.properties.config.videoPlaceholder.styles[\n\t\t\t\t\t\t\t\t'background-color'\n\t\t\t\t\t\t\t]\n\t\t\t\t\t\t"}})],1),e("div",{staticClass:"cky-col-2"},[e("label",{},[t._v(t._s(t.$i18n.__("Border","cookie-law-info")))]),e("cky-colors",{attrs:{color:t.banner.properties.config.videoPlaceholder.styles["border-color"],disabled:t.disabled},model:{value:t.banner.properties.config.videoPlaceholder.styles["border-color"],callback:function(e){t.$set(t.banner.properties.config.videoPlaceholder.styles,"border-color",e)},expression:"\n\t\t\t\t\t\t\tbanner.properties.config.videoPlaceholder.styles['border-color']\n\t\t\t\t\t\t"}})],1),e("div",{staticClass:"cky-col-2"},[e("label",{},[t._v(t._s(t.$i18n.__("Text","cookie-law-info")))]),e("cky-colors",{attrs:{color:t.banner.properties.config.videoPlaceholder.elements.title.styles.color,disabled:t.disabled},model:{value:t.banner.properties.config.videoPlaceholder.elements.title.styles.color,callback:function(e){t.$set(t.banner.properties.config.videoPlaceholder.elements.title.styles,"color",e)},expression:"\n\t\t\t\t\t\t\tbanner.properties.config.videoPlaceholder.elements.title.styles\n\t\t\t\t\t\t\t\t.color\n\t\t\t\t\t\t"}})],1)])])])],2)},K=[],X={name:"BlockedContent",props:{content:Object,translate:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},components:{CkyAccordionItem:c["a"],CkyColors:b},computed:{banner(){return this.$store.state.banners.current}}},tt=X,et=Object(m["a"])(tt,Q,K,!1,null,null,null),nt=et.exports,ot=function(){var t=this,e=t._self._c;return e("cky-accordion-item",[e("template",{slot:"cky-accordion-trigger"},[e("label",{staticClass:"cky-app-accordion-title"},[t._v(t._s(t.$i18n.__("Opt-out center","cookie-law-info")))])]),e("template",{slot:"cky-accordion-content"},[e("div",{staticClass:"cky-form-section cky-form-section-group"},[e("div",{staticClass:"cky-form-content"},[e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[e("h6",[t._v(" "+t._s(t.$i18n.__("Title","cookie-law-info"))+" ")])]),e("div",{staticClass:"cky-col-9"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.content.optoutPopup.elements.title,expression:"content.optoutPopup.elements.title"}],staticClass:"cky-form-control",attrs:{type:"text",disabled:t.disabled},domProps:{value:t.content.optoutPopup.elements.title},on:{input:function(e){e.target.composing||t.$set(t.content.optoutPopup.elements,"title",e.target.value)}}})])]),e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[e("h6",[t._v(" "+t._s(t.$i18n.__("Privacy overview","cookie-law-info"))+" ")])]),e("div",{staticClass:"cky-col-9"},[e("wp-editor",{key:t.key,attrs:{disabled:t.disabled,language:t.language,height:"180"},model:{value:t.content.optoutPopup.elements.description,callback:function(e){t.$set(t.content.optoutPopup.elements,"description",e)},expression:"content.optoutPopup.elements.description"}})],1)]),e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[e("h6",[t._v(t._s(t.$i18n.__("“Show more” button","cookie-law-info")))])]),e("div",{staticClass:"cky-col-9"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.content.optoutPopup.elements.showMore,expression:"content.optoutPopup.elements.showMore"}],staticClass:"cky-form-control",attrs:{type:"text",disabled:t.disabled},domProps:{value:t.content.optoutPopup.elements.showMore},on:{input:function(e){e.target.composing||t.$set(t.content.optoutPopup.elements,"showMore",e.target.value)}}})])]),e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[e("h6",[t._v(" "+t._s(t.$i18n.__("“Show less” button","cookie-law-info"))+" ")])]),e("div",{staticClass:"cky-col-9"},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.content.optoutPopup.elements.showLess,expression:"content.optoutPopup.elements.showLess"}],staticClass:"cky-form-control",attrs:{type:"text",disabled:t.disabled},domProps:{value:t.content.optoutPopup.elements.showLess},on:{input:function(e){e.target.composing||t.$set(t.content.optoutPopup.elements,"showLess",e.target.value)}}})])])])]),e("div",{staticClass:"cky-form-section"},[e("div",{staticClass:"cky-form-content"},[t.translate?t._e():e("div",{staticClass:"cky-form-group cky-row"},[e("label",{staticClass:"cky-col-3 cky-col-label"},[e("h6",[t._v(" "+t._s(t.$i18n.__("Checkbox","cookie-law-info"))+" ")])]),e("div",{staticClass:"cky-col-9"},[e("div",{staticClass:"cky-row"},[e("div",{staticClass:"cky-col-3"},[e("label",{},[t._v(t._s(t.$i18n.__("Enabled state","cookie-law-info")))]),e("cky-colors",{attrs:{color:t.banner.properties.config.optoutPopup.elements.optOption.elements.toggle.states.active.styles["background-color"],disabled:t.disabled},model:{value:t.banner.properties.config.optoutPopup.elements.optOption.elements.toggle.states.active.styles["background-color"],callback:function(e){t.$set(t.banner.properties.config.optoutPopup.elements.optOption.elements.toggle.states.active.styles,"background-color",e)},expression:"\n\t\t\t\t\t\t\t\t\t\tbanner.properties.config.optoutPopup.elements.optOption\n\t\t\t\t\t\t\t\t\t\t\t.elements.toggle.states.active.styles[\n\t\t\t\t\t\t\t\t\t\t\t'background-color'\n\t\t\t\t\t\t\t\t\t\t]\n\t\t\t\t\t\t\t\t\t"}})],1),e("div",{staticClass:"cky-col-3"},[e("label",{},[t._v(t._s(t.$i18n.__("Disabled state","cookie-law-info")))]),e("cky-colors",{attrs:{color:t.banner.properties.config.optoutPopup.elements.optOption.elements.toggle.states.inactive.styles["background-color"],disabled:t.disabled},model:{value:t.banner.properties.config.optoutPopup.elements.optOption.elements.toggle.states.inactive.styles["background-color"],callback:function(e){t.$set(t.banner.properties.config.optoutPopup.elements.optOption.elements.toggle.states.inactive.styles,"background-color",e)},expression:"\n\t\t\t\t\t\t\t\t\t\tbanner.properties.config.optoutPopup.elements.optOption\n\t\t\t\t\t\t\t\t\t\t\t.elements.toggle.states.inactive.styles[\n\t\t\t\t\t\t\t\t\t\t\t'background-color'\n\t\t\t\t\t\t\t\t\t\t]\n\t\t\t\t\t\t\t\t\t"}})],1)])])])])]),e("cky-banner-btn",{attrs:{name:"optout-cancel",properties:t.banner.properties.config.optoutPopup.elements.buttons.elements.cancel,content:t.content.optoutPopup.elements.buttons.elements.cancel,title:t.$i18n.__("“Cancel“ button","cookie-law-info"),toggle:!1,supportedLaws:["ccpa"],translate:t.translate,disabled:t.disabled},model:{value:t.content.optoutPopup.elements.buttons.elements.cancel,callback:function(e){t.$set(t.content.optoutPopup.elements.buttons.elements,"cancel",e)},expression:"content.optoutPopup.elements.buttons.elements.cancel"}}),e("cky-banner-btn",{attrs:{name:"optout-confirm",properties:t.banner.properties.config.optoutPopup.elements.buttons.elements.confirm,content:t.content.optoutPopup.elements.buttons.elements.confirm,title:t.$i18n.__("“Save My Preferences” button","cookie-law-info"),toggle:!1,supportedLaws:["ccpa"],translate:t.translate,disabled:t.disabled},model:{value:t.content.optoutPopup.elements.buttons.elements.confirm,callback:function(e){t.$set(t.content.optoutPopup.elements.buttons.elements,"confirm",e)},expression:"content.optoutPopup.elements.buttons.elements.confirm"}}),t.translate?t._e():e("div",{staticClass:"cky-form-section"},[e("div",{staticClass:"cky-form-content"},[e("div",{staticClass:"cky-form-group cky-align-center cky-row"},[e("div",{staticClass:"cky-col-4"},[e("label",{staticClass:"cky-zero-padding cky-col-label cky-action-link",attrs:{for:"cky-toggle-gpc"}},[t._v(t._s(t.$i18n.__("Respect “Global Privacy control“","cookie-law-info"))+" "),e("span",[e("img",{staticClass:"cky-crown-img",attrs:{src:n("7cd5"),alt:"crown-icon"}})])])]),e("div",{staticClass:"cky-col-5"},[e("label",{staticClass:"cky-toggle",attrs:{for:"cky-toggle-gpc"}},[e("input",{attrs:{type:"checkbox",id:"cky-toggle-gpc"},domProps:{value:!1},on:{click:function(e){return e.preventDefault(),t.showConnectModal()}}}),e("span",{staticClass:"cky-toggle-slider",attrs:{id:"cky-toggle-gpc-toggle","aria-hidden":"true"}})])])])])]),e("cky-connect-modal",{ref:"ckyConnectModalGPC",attrs:{availablePlan:"pro",feature:"respect_gpc"},scopedSlots:t._u([{key:"title",fn:function(){return[e("img",{attrs:{src:n("d91f"),alt:"global-privacy"}})]},proxy:!0},{key:"message",fn:function(){return[e("div",{staticClass:"cky-feature-text"},[t._v(" "+t._s(t.$i18n.__("Respect Global Privacy Control (GPC) signals and honour users’ privacy settings","cookie-law-info"))+" ")]),e("div",{staticClass:"cky-available-wrapper"},[e("span",{staticClass:"cky-available-text",domProps:{innerHTML:t._s(t.$i18n.__("Available in: <b>Pro and Ultimate plans</b>","cookie-law-info"))}})])]},proxy:!0}])})],1)],2)},st=[],at={name:"OptOutCenter",props:{content:Object,translate:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},language:{type:String,default:"en"}},components:{CkyColors:b,WpEditor:C["a"],CkyAccordionItem:c["a"],CkyBannerBtn:L,CkyConnectModal:S["a"]},data(){return{key:Math.random().toString(),loaded:!1}},methods:{showConnectModal(){this.$refs.ckyConnectModalGPC.show()}},computed:{banner(){return this.$store.state.banners.current}}},it=at,rt=Object(m["a"])(it,ot,st,!1,null,null,null),ct=rt.exports,lt={name:"TabContentAccordion",components:{CkyLoader:a["a"],CkyIcon:i["a"],CkyAccordion:l["a"],CkyAccordionItem:c["a"],Notice:A,Preference:E,CookieList:F,RevisitConsent:J,BlockedContent:nt,OptOutCenter:ct},props:{language:{type:String,default:"en"},disabled:{type:Boolean,default:!1},translate:{type:Boolean,default:!1}},computed:{banner(){return this.$store.state.banners.current},contentSections:function(){let t=[];if(this.loading)return t;const e=this.banner.properties.settings.applicableLaw;return t=r["a"].getContentSections(e),t},loading(){return!this.$store.state.banners.current.id>0},content(){let t=this.banner.contents[this.language];return void 0!==t&&""!==t||(t=this.banner.contents["en"]),t}}},ut=lt,dt=(n("b5ae"),Object(m["a"])(ut,o,s,!1,null,null,null));e["a"]=dt.exports},c7b3:function(t,e,n){"use strict";n("22d4")},c9d1:function(t,e,n){"use strict";n("23fe")},cc73:function(t,e,n){},cfea:function(t,e,n){},d235:function(t,e,n){"use strict";n("cfea")},d91f:function(t,e,n){t.exports=n.p+"img/global-privacy.svg"},da4c:function(t,e,n){},dcb3:function(t,e,n){t.exports=n.p+"img/check-fill.svg"},df44:function(t,e,n){"use strict";var o=function(){var t=this,e=t._self._c;return e("div",{class:["cky-rich-text-editor-container",{"cky-disabled":this.disabled}]},[e("textarea",{staticClass:"wp-editor-area cky-rich-text-editor cky-form-control",attrs:{id:t.editorId,disabled:t.disabled,rows:"5"},domProps:{value:t.editorContent},on:{input:t.listener}})])},s=[],a=n("6821"),i=n.n(a),r={name:"WpEditor",props:{value:{type:String,default:""},id:{type:String,default:"cky-richtext-editor"},disabled:{type:Boolean,default:!1},language:{type:String,default:"en"},height:{type:String,default:"150"}},data(){return{editorId:`${this.id}-${i()(Math.random()+"-"+Math.random()+"-"+Math.random())}`,correctEditor:null}},computed:{editorContent:{get(){return this.value},set(t){this.$emit("input",t.replaceAll("<p><br></p>",""))}}},methods:{initialize(){this.correctEditor=()=>window.wp.oldEditor||window.wp.editor;const t=this.correctEditor().getDefaultSettings()||{};t.quicktags.buttons=" ",t.tinymce.toolbar1=" ",t.tinymce.toolbar2=" ",t.tinymce.height=this.height,t.tinymce.readonly=this.disabled,t.tinymce.body_class="cky-rich-text-editor",t.tinymce.content_style='body{font-size:14px; color: #2C3338;font-family: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif; -webkit-font-smoothing: subpixel-antialiased !important;}',t.tinymce.setup=t=>{t.on("init",()=>{t.setContent(this.value||"")}),t.on("change",()=>{this.listener()})},this.isRTLLanguage(this.language)&&(t.tinymce.directionality="rtl"),this.cleanup(),this.correctEditor().initialize(this.editorId,t)},listener(){if(!this.correctEditor)return;const t=this.correctEditor().getContent(this.editorId);this.$emit("input",t)},cleanup(){window.tinymce&&window.tinymce.editors[this.editorId]&&window.tinymce.editors[this.editorId].remove()},clearContent(){window.tinymce&&window.tinymce.editors[this.editorId]&&window.tinymce.editors[this.editorId].setContent("")}},mounted(){this.initialize()},beforeDestroy(){this.cleanup()},activated(){this.initialize()},deactivated(){this.cleanup()},watch:{value(t){if(window.tinymce&&window.tinymce.editors[this.editorId]){const e=window.tinymce.editors[this.editorId];e.getContent()!==t&&e.setContent(t||"")}}}},c=r,l=(n("7ad7"),n("2877")),u=Object(l["a"])(c,o,s,!1,null,null,null);e["a"]=u.exports},ebe0:function(t,e,n){},f405:function(t,e,n){t.exports=n.p+"img/custom-logo.svg"},f61e:function(t,e,n){"use strict";var o=n("f9c4"),s=n("07a4"),a=n("87ea");const i={get:async function(){try{const t=await o["a"].get({path:"languages/available"});return t}catch(t){return console.error(t),[]}},getLanguageDetails:function(t,e){let n=t.filter((function(t){if(-1!=e.indexOf(t.code))return t}));return n},getName(t){let e=s["a"].state.languages.available.find((function(e){return e.code==t}));return"undefined"!==typeof e?e.name:""},deleteLanguage:async function(t){if(t){let e=Object(a["e"])("languages"),n=e.selected.indexOf(t);return-1!==n&&e.selected.splice(n,1),s["a"].dispatch("languages/setSelected",e.selected),await s["a"].dispatch("languages/saveSelected",{remove:!0}),await Object(a["h"])(),await s["a"].dispatch("banners/reInit"),await s["a"].dispatch("cookies/reInit"),!0}},getTranslatedLanguages(){return["en","de","fr","it","es","nl","bg","da","ru","ar","pl","pt","ca","hu","sv","hr","zh","uk","sk","tr","lt","cs","fi","no","pt-br","sl","ro","th","et","lv","el","eu","bs","gl","ja","ko","mt","sr","tl","cy","sr-latn"]},deepEqual(t,e){const n=Object.keys(t),o=Object.keys(e);if(n.length!==o.length)return!1;for(const s of n){const n=t[s],o=e[s],a=this.isObject(n)&&this.isObject(o);if(a&&!this.deepEqual(n,o)||!a&&n!==o)return!1}return!0},isObject(t){return null!=t&&"object"===typeof t}};e["a"]=i},f9c4:function(t,e,n){"use strict";var o=n("8eaa");o["a"].use(o["a"].createNonceMiddleware(window.ckyGlobals.api.nonce)),o["a"].use(o["a"].createRootURLMiddleware(window.ckyGlobals.api.base));var s={post:function(t){if(t=t||{},t.method="POST",t.params){const e=new URLSearchParams(Object.entries(t.params));t.path=t.path+"?"+e}try{return Object(o["a"])(t)}catch(e){return e}},get:function(t){if(t=t||{},t.method="GET",t.params){const e=new URLSearchParams(Object.entries(t.params));t.path=t.path+"?"+e}try{return Object(o["a"])(t)}catch(e){return e}},put:function(t){if(t=t||{},t.method="PUT",t.params){const e=new URLSearchParams(Object.entries(t.params));t.path=t.path+"?"+e}try{return Object(o["a"])(t)}catch(e){return e}},delete:function(t){t=t||{},t.method="DELETE";try{return Object(o["a"])(t)}catch(e){return e}}};e["a"]=s},feb3:function(t,e,n){"use strict";n("bc64")}});