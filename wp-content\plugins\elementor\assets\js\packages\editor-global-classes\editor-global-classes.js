/*! For license information please see editor-global-classes.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React},"@elementor/editor":function(e){e.exports=window.elementorV2.editor},"@elementor/editor-current-user":function(e){e.exports=window.elementorV2.editorCurrentUser},"@elementor/editor-documents":function(e){e.exports=window.elementorV2.editorDocuments},"@elementor/editor-editing-panel":function(e){e.exports=window.elementorV2.editorEditingPanel},"@elementor/editor-panels":function(e){e.exports=window.elementorV2.editorPanels},"@elementor/editor-props":function(e){e.exports=window.elementorV2.editorProps},"@elementor/editor-styles":function(e){e.exports=window.elementorV2.editorStyles},"@elementor/editor-styles-repository":function(e){e.exports=window.elementorV2.editorStylesRepository},"@elementor/editor-ui":function(e){e.exports=window.elementorV2.editorUi},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/http-client":function(e){e.exports=window.elementorV2.httpClient},"@elementor/icons":function(e){e.exports=window.elementorV2.icons},"@elementor/query":function(e){e.exports=window.elementorV2.query},"@elementor/store":function(e){e.exports=window.elementorV2.store},"@elementor/ui":function(e){e.exports=window.elementorV2.ui},"@elementor/utils":function(e){e.exports=window.elementorV2.utils},"@wordpress/i18n":function(e){e.exports=window.wp.i18n}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};!function(){n.r(r),n.d(r,{init:function(){return Ae}});var e=n("@elementor/editor"),t=n("@elementor/editor-editing-panel"),a=n("@elementor/editor-panels"),o=n("@elementor/editor-styles-repository"),i=n("@elementor/editor-v1-adapters"),l=n("@elementor/store"),s=n("react"),c=n("@elementor/editor-documents"),d=n("@elementor/ui"),m=n("@wordpress/i18n"),u=n("@elementor/editor-styles"),p=n("@elementor/utils"),g=n("@elementor/editor-props"),h=n("@elementor/editor-ui"),y=n("@elementor/icons"),v=n("@elementor/query"),f=n("@elementor/http-client"),b=n("@elementor/editor-current-user"),E="elementor_global_classes_update_class",_=(0,p.createError)({code:"global_class_not_found",message:"Global class not found."}),x=(0,p.createError)({code:"global_class_label_already_exists",message:"Class with this name already exists."});function w({value:e,next:t,prev:n}){return{value:e,prev:n||null,next:t||null}}var S=class e{constructor(e){this.namespace=e}static registry={};static get(t){return e.registry[t]||(e.registry[t]=new e(t)),e.registry[t]}first=null;current=null;transform(e){return JSON.parse(JSON.stringify(e))}reset(){this.first=this.current=null}prev(){return this.current&&this.current!==this.first?(this.current=this.current.prev,this.current?.value||null):null}isLast(){return!this.current||!this.current.next}next(e){if(e){if(!this.current)return this.first=w({value:this.transform(e)}),this.current=this.first,this.current.value;const t=w({value:this.transform(e),prev:this.current});return this.current.next=t,this.current=t,this.current.value}return this.current&&this.current.next?(this.current=this.current.next,this.current.value):null}}.get("global-classes"),C="globalClasses",D=(0,l.__createSlice)({name:C,initialState:{data:{items:{},order:[]},initialData:{frontend:{items:{},order:[]},preview:{items:{},order:[]}},isDirty:!1},reducers:{load(e,{payload:{frontend:t,preview:n}}){e.initialData.frontend=t,e.initialData.preview=n,e.data=n,e.isDirty=!1},add(e,{payload:t}){S.next(e.data),e.data.items[t.id]=t,e.data.order.unshift(t.id),e.isDirty=!0},delete(e,{payload:t}){S.next(e.data),e.data.items=Object.fromEntries(Object.entries(e.data.items).filter((([e])=>e!==t))),e.data.order=e.data.order.filter((e=>e!==t)),e.isDirty=!0},setOrder(e,{payload:t}){S.next(e.data),e.data.order=t,e.isDirty=!0},update(e,{payload:t}){S.next(e.data);const n={...e.data.items[t.style.id],...t.style};e.data.items[t.style.id]=n,e.isDirty=!0},updateProps(e,{payload:t}){const n=e.data.items[t.id];if(!n)throw new _({context:{styleId:t.id}});S.next(e.data);const r=(0,u.getVariantByMeta)(n,t.meta);r?(r.props=(0,g.mergeProps)(r.props,t.props),0===Object.keys(r.props).length&&(n.variants=n.variants.filter((e=>e!==r)))):n.variants.push({meta:t.meta,props:t.props}),e.isDirty=!0},reset(e,{payload:{context:t}}){"frontend"===t&&(S.reset(),e.initialData.frontend=e.data,e.isDirty=!1),e.initialData.preview=e.data},undo(e){S.isLast()&&S.next(e.data);const t=S.prev();t?(e.data=t,e.isDirty=!0):e.data=e.initialData.preview},resetToInitialState(e,{payload:{context:t}}){S.reset(),e.data=e.initialData[t],e.isDirty=!1},redo(e){const t=S.next();S.isLast()&&S.prev(),t&&(e.data=t,e.isDirty=!0)}}}),T=e=>e[C].data,P=e=>e[C].initialData.frontend,I=e=>e[C].initialData.preview,A=(0,l.__createSelector)(T,(({order:e})=>e)),k=(0,l.__createSelector)(T,(({items:e})=>e)),V=e=>e[C].isDirty,M=(0,l.__createSelector)(k,A,((e,t)=>t.map((t=>e[t])))),O=(e,t)=>e[C].data.items[t]??null,L="global-classes",B=(0,o.createStylesProvider)({key:L,priority:30,limit:50,labels:{singular:(0,m.__)("class","elementor"),plural:(0,m.__)("classes","elementor")},subscribe:e=>(0,l.__subscribeWithSelector)((e=>e.globalClasses),e),capabilities:(()=>{if((0,i.isExperimentActive)("global_classes_should_enforce_capabilities"))return{update:E,create:E,delete:E,updateProps:E}})(),actions:{all:()=>M((0,l.__getState)()),get:e=>O((0,l.__getState)(),e),resolveCssName:e=>(0,i.isExperimentActive)("e_v_3_30")?O((0,l.__getState)(),e)?.label??e:e,create:e=>{const t=k((0,l.__getState)());if(Object.values(t).map((e=>e.label)).includes(e))throw new x({context:{label:e}});const n=Object.keys(t),r=(0,u.generateId)("g-",n);return(0,l.__dispatch)(D.actions.add({id:r,type:"class",label:e,variants:[]})),r},update:e=>{(0,l.__dispatch)(D.actions.update({style:e}))},delete:e=>{(0,l.__dispatch)(D.actions.delete(e))},updateProps:e=>{(0,l.__dispatch)(D.actions.updateProps({id:e.id,meta:e.meta,props:e.props}))}}}),j=()=>(0,l.__useSelector)(V),z="/global-classes",R={all:(e="preview")=>(0,f.httpService)().get("elementor/v1"+z,{params:{context:e}}),publish:e=>(0,f.httpService)().put("elementor/v1"+z,e,{params:{context:"frontend"}}),saveDraft:e=>(0,f.httpService)().put("elementor/v1"+z,e,{params:{context:"preview"}})};async function F({context:e}){const t=T((0,l.__getState)());"preview"===e?await R.saveDraft({items:t.items,order:t.order,changes:W(t,I((0,l.__getState)()))}):await R.publish({items:t.items,order:t.order,changes:W(t,P((0,l.__getState)()))}),(0,l.__dispatch)(D.actions.reset({context:e}))}function W(e,t){const n=Object.keys(e.items),r=Object.keys(t.items);return{added:n.filter((e=>!r.includes(e))),deleted:r.filter((e=>!n.includes(e))),modified:n.filter((n=>n in t.items&&N(e.items[n])!==N(t.items[n])))}}function N(e){return JSON.stringify(e,((e,t)=>function(e){return!!e&&"object"==typeof e&&!Array.isArray(e)}(t)?Object.keys(t).sort().reduce(((e,n)=>(e[n]=t[n],e)),{}):t))}var $=()=>{const[e,t]=(0,b.useSuppressedMessage)("global-class-manager"),[n,r]=(0,s.useState)(!e);return s.createElement(h.IntroductionModal,{open:n,title:(0,m.__)("Class Manager","elementor"),handleClose:e=>{e||t(),r(!1)}},s.createElement(d.Image,{sx:{width:"100%",aspectRatio:"16 / 9"},src:"https://assets.elementor.com/packages/v1/images/class-manager-intro.svg",alt:""}),s.createElement(G,null))},G=()=>s.createElement(d.Box,{p:3},s.createElement(d.Typography,{variant:"body2"},(0,m.__)("The Class Manager lets you see all the classes you've created, plus adjust their priority, rename them, and delete unused classes to keep your CSS structured.","elementor")),s.createElement("br",null),s.createElement(d.Typography,{variant:"body2"},(0,m.__)("Remember, when editing an item within a specific class, any changes you make will apply across all elements in that class.","elementor"))),U=({searchValue:e,onChange:t})=>s.createElement(d.Grid,{item:!0,xs:6,px:2,pb:1},s.createElement(d.Stack,{direction:"row",gap:.5,sx:{width:"100%"}},s.createElement(d.Box,{sx:{flexGrow:1}},s.createElement(d.TextField,{role:"search",fullWidth:!0,size:"tiny",value:e,placeholder:(0,m.__)("Search","elementor"),onChange:e=>t(e.target.value),InputProps:{startAdornment:s.createElement(d.InputAdornment,{position:"start"},s.createElement(y.SearchIcon,{fontSize:"tiny"}))}})))),H=!1,Y=({sx:e,...t})=>s.createElement(y.ColorSwatchIcon,{sx:{transform:"rotate(90deg)",...e},...t}),K=(0,s.createContext)(null),q=({children:e})=>{const[t,n]=(0,s.useState)(null);return s.createElement(K.Provider,{value:{openDialog:e=>{n(e)},closeDialog:()=>{n(null)},dialogProps:t}},e,!!t&&s.createElement(X,{...t}))},J="delete-class-dialog",X=({label:e,id:t})=>{const{closeDialog:n}=Q();return s.createElement(d.Dialog,{open:!0,onClose:n,"aria-labelledby":J,maxWidth:"xs"},s.createElement(d.DialogTitle,{id:J,display:"flex",alignItems:"center",gap:1,sx:{lineHeight:1}},s.createElement(y.AlertOctagonFilledIcon,{color:"error"}),(0,m.__)("Delete this class?","elementor")),s.createElement(d.DialogContent,null,s.createElement(d.DialogContentText,{variant:"body2",color:"textPrimary"},(0,m.__)("Deleting","elementor"),s.createElement(d.Typography,{variant:"subtitle2",component:"span"}," ",e," "),(0,m.__)("will permanently remove it from your project and may affect the design across all elements using it. This action cannot be undone.","elementor"))),s.createElement(d.DialogActions,null,s.createElement(d.Button,{color:"secondary",onClick:n},(0,m.__)("Not now","elementor")),s.createElement(d.Button,{variant:"contained",color:"error",onClick:()=>{(e=>{(0,l.__dispatch)(D.actions.delete(e)),H=!0})(t),n()}},(0,m.__)("Delete","elementor"))))},Q=()=>{const e=(0,s.useContext)(K);if(!e)throw new Error("useDeleteConfirmation must be used within a DeleteConfirmationProvider");return e},Z=e=>s.createElement(d.UnstableSortableProvider,{restrictAxis:!0,variant:"static",dragPlaceholderStyle:{opacity:"1"},...e}),ee=e=>s.createElement(ne,{...e,role:"button",className:"class-item-sortable-trigger"},s.createElement(y.GripVerticalIcon,{fontSize:"tiny"})),te=({children:e,id:t,...n})=>s.createElement(d.UnstableSortableItem,{...n,id:t,render:({itemProps:t,isDragged:n,triggerProps:r,itemStyle:a,triggerStyle:o,dropIndicationStyle:i,showDropIndication:l,isDragOverlay:c,isDragPlaceholder:m})=>s.createElement(d.Box,{...t,style:a,component:"li",role:"listitem",sx:{backgroundColor:c?"background.paper":void 0}},e({itemProps:t,isDragged:n,triggerProps:r,itemStyle:a,triggerStyle:o,isDragPlaceholder:m}),l&&s.createElement(re,{style:i}))}),ne=(0,d.styled)("div")((({theme:e})=>({position:"absolute",left:0,top:"50%",transform:`translate( -${e.spacing(1.5)}, -50% )`,color:e.palette.action.active}))),re=(0,d.styled)(d.Box)`
	width: 100%;
	height: 1px;
	background-color: ${({theme:e})=>e.palette.text.primary};
`,ae=(0,i.isExperimentActive)(t.EXPERIMENTAL_FEATURES.V_3_31),oe=({id:e,label:t,renameClass:n,selected:r,disabled:a,sortableTriggerProps:o,isSearchActive:i})=>{const l=(0,s.useRef)(null),{ref:c,openEditMode:u,isEditing:p,error:g,getProps:v}=(0,h.useEditable)({value:t,onSubmit:n,validation:me}),{openDialog:f}=Q(),b=(0,d.usePopupState)({variant:"popover",disableAutoFocus:!0}),E=(r||b.isOpen)&&!a;return s.createElement(s.Fragment,null,s.createElement(d.Stack,{p:0},s.createElement(h.WarningInfotip,{open:Boolean(g),text:g??"",placement:"bottom",width:l.current?.getBoundingClientRect().width,offset:[0,-15]},s.createElement(se,{ref:l,dense:!0,disableGutters:!0,showSortIndicator:i,showActions:E||p,shape:"rounded",onDoubleClick:u,selected:E,disabled:a,focusVisibleClassName:"visible-class-item"},s.createElement(ee,{...o}),s.createElement(ce,{isActive:p,isError:!!g},p?s.createElement(h.EditableField,{ref:c,as:d.Typography,variant:"caption",...v()}):s.createElement(h.EllipsisWithTooltip,{title:t,as:d.Typography,variant:"caption"})),s.createElement(d.Tooltip,{placement:"top",className:"class-item-more-actions",title:(0,m.__)("More actions","elementor")},s.createElement(d.IconButton,{size:"tiny",...(0,d.bindTrigger)(b),"aria-label":"More actions"},s.createElement(y.DotsVerticalIcon,{fontSize:"tiny"})))))),s.createElement(d.Menu,{...(0,d.bindMenu)(b),anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"}},s.createElement(h.MenuListItem,{sx:{minWidth:"160px"},onClick:()=>{b.close(),u()}},s.createElement(d.Typography,{variant:"caption",sx:{color:"text.primary"}},(0,m.__)("Rename","elementor"))),s.createElement(h.MenuListItem,{onClick:()=>{b.close(),f({id:e,label:t})}},s.createElement(d.Typography,{variant:"caption",sx:{color:"error.light"}},(0,m.__)("Delete","elementor")))))},ie=(0,d.styled)(d.ListItemButton,{shouldForwardProp:e=>!["showActions","showSortIndicator"].includes(e)})((({showActions:e,showSortIndicator:t})=>`\n\tmin-height: 36px;\n\n\t&.visible-class-item {\n\t\tbox-shadow: none !important;\n\t}\n\t.class-item-sortable-trigger {\n\t\tvisibility: ${t&&e?"visible":"hidden"};\n\t}\n\t&:hover&:not(:disabled) {\n\t\t.class-item-sortable-trigger  {\n\t\t\tvisibility: ${t?"visible":"hidden"};\n\t\t}\n\t}\n`)),le=(0,d.styled)(d.ListItemButton,{shouldForwardProp:e=>!["showActions","showSortIndicator"].includes(e)})((({showActions:e})=>`\n\tmin-height: 36px;\n\t&.visible-class-item {\n\t\tbox-shadow: none !important;\n\t}\n\t.class-item-more-actions, .class-item-sortable-trigger {\n\t\tvisibility: ${e?"visible":"hidden"};\n\t}\n\t.class-item-sortable-trigger {\n\t\tvisibility: ${e?"visible":"hidden"};\n\t}\n\t&:hover&:not(:disabled) {\n\t\t.class-item-more-actions, .class-item-sortable-trigger  {\n\t\t\tvisibility: visible;\n\t\t}\n\t}\n`)),se=ae?ie:le,ce=(0,d.styled)(d.Box,{shouldForwardProp:e=>!["isActive","isError"].includes(e)})((({theme:e,isActive:t,isError:n})=>({display:"flex",width:"100%",flexGrow:1,borderRadius:e.spacing(.5),border:de({isActive:t,isError:n,theme:e}),padding:`0 ${e.spacing(1)}`,marginLeft:t?e.spacing(1):0,minWidth:0}))),de=({isActive:e,isError:t,theme:n})=>t?`2px solid ${n.palette.error.main}`:e?`2px solid ${n.palette.secondary.main}`:"none",me=e=>{const t=(0,o.validateStyleLabel)(e,"rename");return t.isValid?null:t.errorMessage},ue=({onClear:e,searchValue:t})=>s.createElement(d.Stack,{color:"text.secondary",pt:5,alignItems:"center",gap:1,overflow:"hidden",maxWidth:"170px",justifySelf:"center"},s.createElement(Y,{color:"inherit",fontSize:"large"}),s.createElement(d.Box,null,s.createElement(d.Typography,{align:"center",variant:"subtitle2",color:"inherit"},(0,m.__)("Sorry, nothing matched","elementor")),s.createElement(d.Typography,{variant:"subtitle2",color:"inherit",sx:{display:"flex",width:"100%",justifyContent:"center"}},s.createElement("span",null,"“"),s.createElement("span",{style:{maxWidth:"80%",overflow:"hidden",textOverflow:"ellipsis"}},t),s.createElement("span",null,"”."))),s.createElement(d.Typography,{align:"center",variant:"caption",color:"inherit"},(0,m.__)("Try something else.","elementor"),s.createElement(d.Link,{color:"secondary",variant:"caption",component:"button",onClick:e},(0,m.__)("Clear & try again","elementor")))),pe=({disabled:e,searchValue:t,onSearch:n})=>{const r=(0,l.__useSelector)(M),a=(0,l.__useDispatch)(),[o,i]=ye(),c=(0,s.useMemo)((()=>r.map((e=>({...e,lowerLabel:e.label.toLowerCase()})))),[r]),m=(0,s.useMemo)((()=>t.length>1?c.filter((e=>e.lowerLabel.toLowerCase().includes(t.toLowerCase()))):r),[t,r,c]);return(0,s.useEffect)((()=>{const e=e=>{if("z"===e.key&&(e.ctrlKey||e.metaKey)){if(e.stopImmediatePropagation(),e.preventDefault(),e.shiftKey)return void a(D.actions.redo());a(D.actions.undo())}};return window.addEventListener("keydown",e,{capture:!0}),()=>window.removeEventListener("keydown",e)}),[a]),r?.length?s.createElement(q,null,m.length<=0&&t.length>1?s.createElement(ue,{onClear:()=>n(""),searchValue:t}):s.createElement(d.List,{sx:{display:"flex",flexDirection:"column",gap:.5}},s.createElement(Z,{value:o,onChange:i},m?.map((({id:n,label:r})=>s.createElement(te,{key:n,id:n},(({isDragged:o,isDragPlaceholder:i,triggerProps:l,triggerStyle:c})=>s.createElement(oe,{isSearchActive:t.length<2,id:n,label:r,renameClass:e=>{a(D.actions.update({style:{id:n,label:e}}))},selected:o,disabled:e||i,sortableTriggerProps:{...l,style:c}})))))))):s.createElement(ge,null)},ge=()=>s.createElement(d.Stack,{alignItems:"center",gap:1.5,pt:10,px:.5,maxWidth:"260px",margin:"auto"},s.createElement(Y,{fontSize:"large"}),s.createElement(he,{variant:"subtitle2",component:"h2",color:"text.secondary"},(0,m.__)("There are no global classes yet.","elementor")),s.createElement(d.Typography,{align:"center",variant:"caption",color:"text.secondary"},(0,m.__)("CSS classes created in the editor panel will appear here. Once they are available, you can arrange their hierarchy, rename them, or delete them as needed.","elementor"))),he=(0,d.styled)(d.Typography)((({theme:e,variant:t})=>({"&.MuiTypography-root":{...e.typography[t]}}))),ye=()=>{const e=(0,l.__useDispatch)();return[(0,l.__useSelector)(A),t=>{e(D.actions.setOrder(t))}]},ve="save-changes-dialog",fe=({children:e,onClose:t})=>s.createElement(d.Dialog,{open:!0,onClose:t,"aria-labelledby":ve,maxWidth:"xs"},e);fe.Title=({children:e})=>s.createElement(d.DialogTitle,{id:ve,display:"flex",alignItems:"center",gap:1,sx:{lineHeight:1}},s.createElement(y.AlertTriangleFilledIcon,{color:"secondary"}),e),fe.Content=({children:e})=>s.createElement(d.DialogContent,null,e),fe.ContentText=e=>s.createElement(d.DialogContentText,{variant:"body2",color:"textPrimary",display:"flex",flexDirection:"column",...e}),fe.Actions=({actions:e})=>{const[t,n]=(0,s.useState)(!1),{cancel:r,confirm:a,discard:o}=e;return s.createElement(d.DialogActions,null,r&&s.createElement(d.Button,{variant:"text",color:"secondary",onClick:r.action},r.label),o&&s.createElement(d.Button,{variant:"text",color:"secondary",onClick:o.action},o.label),s.createElement(d.Button,{variant:"contained",color:"secondary",onClick:async()=>{n(!0),await a.action(),n(!1)},loading:t},a.label))};var be=()=>{const[e,t]=(0,s.useState)(!1);return{isOpen:e,open:()=>t(!0),close:()=>t(!1)}},Ee=(0,i.isExperimentActive)(t.EXPERIMENTAL_FEATURES.V_3_31),_e="global-classes-manager",{panel:xe,usePanelActions:we}=(0,a.__createPanel)({id:_e,component:function(){const{debouncedValue:e,inputValue:t,handleChange:n}=(0,p.useDebounceState)({delay:300,initialValue:""}),r=j(),{close:o}=we(),{open:i,close:c,isOpen:u}=be(),{mutateAsync:g,isPending:y}=Te();return De(),s.createElement(h.ThemeProvider,null,s.createElement(d.ErrorBoundary,{fallback:s.createElement(Ce,null)},s.createElement(a.Panel,null,s.createElement(a.PanelHeader,null,s.createElement(d.Stack,{p:1,pl:2,width:"100%",direction:"row",alignItems:"center"},s.createElement(a.PanelHeaderTitle,{sx:{display:"flex",alignItems:"center",gap:.5}},s.createElement(Y,{fontSize:"inherit"}),(0,m.__)("Class Manager","elementor")),s.createElement(Se,{sx:{marginLeft:"auto"},disabled:y,onClose:()=>{r?i():o()}}))),s.createElement(a.PanelBody,{sx:{display:"flex",flexDirection:"column",height:"100%"}},Ee&&s.createElement(s.Fragment,null,s.createElement(U,{searchValue:t,onChange:n}),s.createElement(d.Divider,{sx:{borderWidth:"1px 0 0 0"}})),s.createElement(d.Box,{px:2,sx:{flexGrow:1,overflowY:"auto"}},s.createElement(pe,{disabled:y,searchValue:e,onSearch:n}))),s.createElement(a.PanelFooter,null,s.createElement(d.Button,{fullWidth:!0,size:"small",color:"global",variant:"contained",onClick:g,disabled:!r,loading:y},(0,m.__)("Save changes","elementor"))))),s.createElement($,null),u&&s.createElement(fe,null,s.createElement(d.DialogHeader,{onClose:c,logo:!1},s.createElement(fe.Title,null,(0,m.__)("You have unsaved changes","elementor"))),s.createElement(fe.Content,null,s.createElement(fe.ContentText,null,(0,m.__)("You have unsaved changes in the Class Manager.","elementor")),s.createElement(fe.ContentText,null,(0,m.__)("To avoid losing your updates, save your changes before leaving.","elementor"))),s.createElement(fe.Actions,{actions:{discard:{label:(0,m.__)("Discard","elementor"),action:()=>{(0,l.__dispatch)(D.actions.resetToInitialState({context:"frontend"})),c()}},confirm:{label:(0,m.__)("Save & Continue","elementor"),action:async()=>{await g(),c(),o()}}}})))},allowedEditModes:["edit",_e],onOpen:()=>{(0,i.changeEditMode)(_e),function(){const e=window;e.$e?.components?.get?.("panel")?.blockUserInteractions?.()}()},onClose:()=>{(0,i.changeEditMode)("edit"),function(){const e=window;e.$e?.components?.get?.("panel")?.unblockUserInteractions?.()}()}}),Se=({onClose:e,...t})=>s.createElement(d.IconButton,{size:"small",color:"secondary",onClick:e,"aria-label":"Close",...t},s.createElement(y.XIcon,{fontSize:"small"})),Ce=()=>s.createElement(d.Box,{role:"alert",sx:{minHeight:"100%",p:2}},s.createElement(d.Alert,{severity:"error",sx:{mb:2,maxWidth:400,textAlign:"center"}},s.createElement("strong",null,(0,m.__)("Something went wrong","elementor")))),De=()=>{const e=j();(0,s.useEffect)((()=>{const t=t=>{e&&t.preventDefault()};return window.addEventListener("beforeunload",t),()=>{window.removeEventListener("beforeunload",t)}}),[e])},Te=()=>(0,v.useMutation)({mutationFn:()=>F({context:"frontend"}),onSuccess:async()=>{(0,c.setDocumentModifiedStatus)(!1),H&&await(async()=>{await(()=>{const e=(0,c.getCurrentDocument)();return(0,c.getV1DocumentsManager)().invalidateCache(),(0,i.__privateRunCommand)("editor/documents/switch",{id:e?.id,shouldScroll:!1,shouldNavigateToDefaultRoute:!1})})(),H=!1})()}}),Pe=()=>{const e=(0,c.__useActiveDocument)(),{open:t}=we(),{save:n}=(0,c.__useActiveDocumentActions)(),{open:r,close:a,isOpen:i}=be(),{userCan:l}=(0,o.useUserStylesCapability)();return l(B.getKey()).update?s.createElement(s.Fragment,null,s.createElement(d.Tooltip,{title:(0,m.__)("Class Manager","elementor"),placement:"top"},s.createElement(d.IconButton,{size:"tiny",onClick:()=>{e?.isDirty?r():t()},sx:{marginInlineEnd:-.75}},s.createElement(Y,{fontSize:"tiny"}))),i&&s.createElement(fe,null,s.createElement(fe.Title,null,(0,m.__)("You have unsaved changes","elementor")),s.createElement(fe.Content,null,s.createElement(fe.ContentText,{sx:{mb:2}},(0,m.__)("To open the Class Manager, save your page first. You can't continue without saving.","elementor"))),s.createElement(fe.Actions,{actions:{cancel:{label:(0,m.__)("Stay here","elementor"),action:a},confirm:{label:(0,m.__)("Save & Continue","elementor"),action:async()=>{await n(),a(),t()}}}}))):null};function Ie(){const e=(0,l.__useDispatch)();return(0,s.useEffect)((()=>{Promise.all([R.all("preview"),R.all("frontend")]).then((([t,n])=>{const{data:r}=t,{data:a}=n;e(D.actions.load({preview:{items:r.data,order:r.meta.order},frontend:{items:a.data,order:a.meta.order}}))}))}),[e]),null}function Ae(){(0,l.__registerSlice)(D),(0,a.__registerPanel)(xe),o.stylesRepository.register(B),(0,e.injectIntoLogic)({id:"global-classes-populate-store",component:Ie}),(0,t.injectIntoClassSelectorActions)({id:"global-classes-manager-button",component:Pe}),(0,t.registerStyleProviderToColors)(L,{name:"global",getThemeColor:e=>e.palette.global.dark}),(0,i.__privateListenTo)((0,i.v1ReadyEvent)(),(()=>{!function(){const e=(0,l.__subscribeWithSelector)(V,(()=>{V((0,l.__getState)())&&(0,c.setDocumentModifiedStatus)(!0)}));(0,i.registerDataHook)("after","document/save/save",(e=>F({context:"publish"===e.status?"frontend":"preview"})))}()}))}}(),(window.elementorV2=window.elementorV2||{}).editorGlobalClasses=r}(),window.elementorV2.editorGlobalClasses?.init?.();