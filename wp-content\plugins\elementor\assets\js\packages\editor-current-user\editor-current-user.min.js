!function(){"use strict";var e={d:function(t,r){for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{ensureCurrentUser:function(){return m},getCurrentUser:function(){return d},useCurrentUserCapabilities:function(){return g},useSuppressedMessage:function(){return y}});var r=window.elementorV2.query,n=window.elementorV2.httpClient,s=window.elementorV2.editorV1Adapters,i="/users/me",u={params:{context:"edit"}},o={get:()=>(0,n.httpService)().get("wp/v2"+i,u).then((e=>a(e.data))),update:e=>(0,n.httpService)().patch("wp/v2"+i,c(e))},a=e=>({suppressedMessages:Object.entries(e.elementor_introduction).filter((([,e])=>e)).map((([e])=>e)),capabilities:Object.keys(e.capabilities)}),c=e=>({elementor_introduction:e.suppressedMessages?.reduce(((e,t)=>(e[t]=!0,e)),{})}),d=()=>o.get(),p="editor-current-user",l=()=>(0,r.useQuery)({queryKey:[p],queryFn:d}),y=e=>{const{data:t}=l(),{mutate:n}=(()=>{const e=(0,r.useQueryClient)();return(0,r.useMutation)({mutationFn:o.update,onSuccess:()=>e.invalidateQueries({queryKey:[p]})})})(),s=!!t?.suppressedMessages.includes(e);return[s,()=>{s||n({suppressedMessages:[...t?.suppressedMessages??[],e]})}]},g=()=>{const{data:e}=l();return{canUser:t=>Boolean(e?.capabilities.includes(t)),capabilities:e?.capabilities}};function m({queryClient:e}){return(0,s.registerDataHook)("after","editor/documents/attach-preview",(async()=>{await e.ensureQueryData({queryKey:[p],queryFn:o.get})})),e.getQueryData([p])}(window.elementorV2=window.elementorV2||{}).editorCurrentUser=t}(),window.elementorV2.editorCurrentUser?.init?.();