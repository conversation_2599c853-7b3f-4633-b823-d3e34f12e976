=== Elementor Website Builder - More Than Just a Page Builder ===
Contributors: elemntor
Tags: page builder, editor, landing page, drag-and-drop, elementor,
Requires at least: 6.6
Tested up to: 6.8
Requires PHP: 7.4
Stable tag: 3.30.0
Beta tag: 3.30.0-beta3
License: GPLv3
License URI: https://www.gnu.org/licenses/gpl-3.0.html

The Elementor Website Builder has it all: drag and drop page builder, pixel perfect design, mobile responsive editing, and more. Get started now!

== Description ==

https://www.youtube.com/watch?v=le72grP_Q6k

= THE #1 NO CODE DRAG & DROP WORDPRESS WEBSITE BUILDER POWERING OVER 18M WEBSITES WORLDWIDE, NOW WITH AI. =

Elementor, the leading WordPress website creation platform, empowers you to build professional, pixel-perfect websites seamlessly.

Unlock all features with [Elementor Pro](https://go.elementor.com/wp-repo-description-tab-elementor-pro-pro-features/).

Need fast and secure cloud hosting for your Elementor site? Try out **[Elementor Hosting](https://elemn.to/repo-hosting)** Powered by Google Cloud & Cloudflare. 4.9/5 TrustPilot score.

### 🌟 Create Professional Stunning Websites

- **[Intuitive Drag & Drop Builder](https://go.elementor.com/feature-page-editor/)**: Build any website with our no-code, drag-and-drop Editor. Achieve design precision with full control over layout and style.
- **[Pixel-Perfect Design Tools](https://go.elementor.com/wp-repo-description-tab-pro-features-feature-page/)**: Upload SVGs, apply masks, gradients, box shadows, headline effects, shape dividers, and use built-in CSS controls for advanced customization.
- **[Template Library](https://go.elementor.com/wp-repo-description-tab-library-full-website-kit/)**: Apply complete website kits for instant setups, or choose from a vast library of single pages, blocks, and pop-up templates.
- **[Advanced Widgets](https://go.elementor.com/feature-page-editor/)**: Access over 40 FREE widgets, including heading, image, text editor, video, button, gallery, carousels, and more.
- **[AI Capabilities](https://go.elementor.com/wp-repo-description-tab-elementor-ai/)**: Revolutionize your design and content creation process with native AI integration. Instantly create sections, text, code, and images.

= 🗝️ Key features: =

- **[Design System](https://go.elementor.com/feature-page-global-settings/)**: Use Elementor's Design System for consistent colors, typography, and design elements, ensuring a cohesive, professional look.
- **[Responsive Design](https://go.elementor.com/feature-page-responsive-design/)**: Optimize your design for every device with custom breakpoints, ensuring a seamless desktop, tablet, and mobile experience.
- **Mask Shapes**: Turning any element, like an image or video, into whatever shape you desire to create standout designs.
- **CSS Transform**: Use CSS Transform to rotate, scale, and skew elements, adding dynamic styling to your site.
- **Entrance Animations**: Add entrance animations to elements to create engaging and interactive user experiences.
- **[Revision History](https://elementor.com/features/#SaveBackup)**: Elementor's Revision History feature enables users to track and revert to previous versions of their designs, providing peace of mind and flexibility during the creative process.
- **[Developer-Friendly](https://go.elementor.com/wp-repo-description-tab-developers-developers-website/)**: Equipped with extensive documentation, API, developer tools, and custom code areas, Elementor offers a conducive environment for developers to extend its capabilities and create custom solutions.
- **Floating Buttons**: Enhance user interaction with customizable, floating action buttons that stay in view as users scroll.
- **[Theme Builder](https://go.elementor.com/wp-repo-description-tab-pro-features-industry-leading-theme-builder/) [Pro]**: Design every part of your site—headers, footers, posts, and archives—for complete control over appearance.
- **[Popup Builder](https://go.elementor.com/wp-repo-description-tab-pro-features-popup-builder/) [Pro]**: Create eye-catching popups with Elementor's Popup Builder, equipped with advanced targeting and triggering options to optimize user engagement and conversions.
- **[Forms](https://go.elementor.com/wp-repo-description-tab-pro-features-professional-form-builder-and-submission-log/) [Pro]**: Design and integrate custom forms, utilizing advanced features and integrations to capture and manage submissions effectively.
- **[WooCommerce Builder](https://go.elementor.com/wp-repo-description-tab-pro-features-woocommerce-builder/) [Pro]**: Integrate Elementor with WooCommerce to design custom product pages, shop layouts, archives, carts, checkout pages, my account, and more, enhancing your store's visual appeal and functionality.
- **[Dynamic Content](https://go.elementor.com/wp-repo-description-tab-pro-features-dynamic-content/) [Pro]**: Leverage dynamic content capabilities to create personalized and interactive web experiences by connecting your designs to various data sources.
- **[Notes](https://go.elementor.com/features-page-notes/) [Pro]**: Enhance team collaboration by using Elementor's Notes feature to leave feedback and comments directly on the design interface.
- **Custom Code [Pro]**: Insert custom code to extend the functionality of your site, offering flexibility for advanced customizations.
- **Custom CSS [Pro]**: Apply Custom CSS to fine-tune the styling of elements, ensuring precise control over the design aspects.
- **[Motion Effects](https://go.elementor.com/wp-repo-description-tab-pro-features-motion-effects/) [Pro]**: Add advanced motion effects to elements for a more dynamic and engaging user experience.
- **Custom Fonts & Icons [Pro]**: Upload and use custom fonts and icons to match your brand's identity.

= 😍 Elementor FREE widgets =

Unlock the potential of Elementor with our comprehensive suite of free widgets and tools, designed to empower your website creation process and elevate your design capabilities:

- **Heading**. Add eye-catching headlines.
- **Image**. Control the size, opacity and more.
- **Text Editor**. Just like the WordPress editor.
- **Video**. Add YouTube, Vimeo, Dailymotion or self-hosted videos.
- **Button**. Create interactive buttons.
- **Link in Bio**. Build link in bio components to promote your business / services.
- **Image Box**. A box with image, headline and text.
- **Testimonials**. Customer testimonials.
- **Icon**. Place one or more of 600+ icons available.
- **Icon Box**. An icon, headline, and text with one widget.
- **Social Icons**. Link to your social pages with the Facebook/X (formerly Twitter) icons.
- **Image Gallery**. Display your images in a grid.
- **Image Carousel**. Create rotating carousels or sliders for chosen images.
- **Icon List**. Use any icon to create a bullet list.
- **Counter**. Show numbers in an escalating manner.
- **Progress Bar**. Include an escalating progress bar.
- **Nested Tabs**. Display content in vertical or horizontal tabs.
- **Nested Accordion**. Display any type of content in collapsible sections.
- **Toggle**. Use the Toggle to hide or display content, like FAQ.
- **Rating**. Display how many stars (or another icon) other visitors gave.
- **Alert**. Include a colored alert box to draw visitor’s attention.
- **HTML**. Insert code into the page.
- **Shortcode**. Insert shortcodes from any plugin into the page.
- **Menu Anchor**. Link any menu to this anchor.
- **Read More**. Set the Read More cut-off for the excerpt in archive pages.
- **Sidebar**. Add sidebars onto the page.
- **Google Maps**. Embed maps into the page.
- **SoundCloud**. Add SoundCloud audio bits.
- **Divider**. Separate content with a designed divider.
- **Spacer**. Add space between elements.
- **Text Path**. Attach your text to a path.
- **And counting...**

### 🚀 Enhance Your Website

**[High-Performing Websites](https://go.elementor.com/wp-repo-description-tab-performance-performance-page/)**: Website performance impacts your visitor’s experience and search result ranking. Elementor, in partnership with Google Chrome, continuously enhances performance without compromising design.

**Key features include:**

- **Reduced DOM Output**: Streamlined HTML structure for faster rendering.
- **Improved Media File Loading**: Optimized loading of images, videos, and other media assets.
- **Reduced CSS and JS Files**: Minimized and concatenated stylesheets and scripts for quicker loading times.
- **Lazy Loading**: Deferred loading of non-critical resources to improve initial page load speed.
- **Faster Font Loading**: Efficient delivery of web fonts to enhance text rendering speed.
- **Optimized Front-End Asset Loading**: Efficient loading of assets like JavaScript and CSS to minimize render-blocking.
- **Element Caching**: Cache frequently accessed design elements to reduce server response time and enhance overall performance.

### 🔥 Elementor Pro Features

Create unparalleled websites while saving time, money and resources with [Elementor Pro](https://go.elementor.com/wp-repo-description-tab-elementor-pro-elementor-pro/)’s full website builder. Get access to 100+ professional widgets, features, and tools.

**Pro Design Widgets:**

1. **Posts**: Display your blog posts with customizable layouts and styles.
1. **Share Buttons**: Allow visitors to easily share your content on various social media platforms.
1. **Portfolio**: Showcase your work or projects with stunning portfolio layouts.
1. **Slides**: Create dynamic slideshows with custom animations and transitions.
1. **Form**: Design and customize advanced forms for user interaction and data collection.
1. **Login**: Add a login form or user registration module to your website.
1. **Nav Menu**: Customize and style your website's navigation menu for better user experience.
1. **Animated Headline**: Create attention-grabbing headlines with animated effects.
1. **Price Table**: Display pricing plans or packages in a structured and visually appealing format.
1. **Price List**: Showcase a list of prices or services with customizable styling options.
1. **Gallery**: Create beautiful image galleries with various layout options and lightbox support.
1. **Flip Box**: Add interactive flip animations to highlight content or features.
1. **Call to Action**: Encourage user interaction and conversions with compelling call-to-action sections.
1. **Media Carousel**: Showcase a carousel of media files such as images or videos.
1. **Testimonial Carousel**: Display client testimonials in a carousel format for social proof.
1. **Nested Carousel**: Create nested carousels for more complex content organization.
1. **Loop Carousel**: Display content in a looped carousel for continuous viewing.
1. **Table Of Content**: Generate a table of contents for longer articles or guides to improve navigation.
1. **Countdown**: Add countdown timers to create urgency for promotions or events.
1. **Facebook Page**: Embed your Facebook page feed or content onto your website.
1. **Blockquote**: Highlight quotes or testimonials with stylish formatting options.
1. **Template**: Save and reuse design templates for consistent branding and layout.
1. **Reviews**: Showcase customer reviews and ratings to build trust and credibility.
1. **Facebook Button**: Add buttons to promote interactions with your Facebook page or content.
1. **Facebook Embed**: Embed Facebook posts or content onto your website.
1. **Facebook Comments**: Enable Facebook comments on your website's pages or posts.
1. **PayPal Button**: Integrate PayPal buttons for easy online payments.
1. **Stripe Button**: Integrate Stripe payment buttons to facilitate secure online transactions.
1. **Lottie Widget**: Add Lottie animations to enhance visual appeal and engagement.
1. **Code Highlight**: Display code snippets with syntax highlighting for better readability.
1. **Video Playlist**: Create and customize playlists for video content on your website.
1. **Mega Menu**: Customize advanced menus for better navigation and displaying complex content.
1. **Off Canvas**: Create off-canvas areas that slide in to show extra info or menus without cluttering the main layout.

**Pro Theme Widgets:**

Build and customize all the key parts of your website including headers, footers, 404 page, global archives, and more

1. **Post Title**: Customize the title of individual blog posts or pages.
1. **Post Excerpt**: Display a brief summary or teaser of your blog posts.
1. **Post Content**: Customize the main content area of your blog posts or pages.
1. **Featured Image**: Set and customize featured images for blog posts or pages.
1. **Author Box**: Display author information and bios on blog posts.
1. **Post Comments**: Customize the appearance and functionality of comments sections on your website.
1. **Post Navigation**: Add navigation links to adjacent posts for easy browsing.
1. **Post Info**: Display additional information about blog posts, such as author and date.
1. **Site Logo**: Upload and customize your website's logo for branding purposes.
1. **Site Title**: Customize the title of your website.
1. **Page Title**: Customize the title of individual pages.
1. **Search Bar**: Add a search bar to allow users to search your website's content.
1. **Breadcrumbs**: Display hierarchical navigation paths for better user navigation.
1. **Sitemap**: Generate a sitemap for better search engine indexing and user navigation.
1. **Loop Grid**: Design and customize grid layouts for blog post archives or product listings.

**Pro WooCommerce Widgets:**

Design and customize a complete online shopping experience across your entire website.

1. **Product**: Display individual products with customizable layouts and styles.
1. **Breadcrumbs**: Display hierarchical navigation paths for better user navigation within your store.
1. **Product Title**: Customize the title of individual products.
1. **Product Images**: Set and customize images for product listings.
1. **Product Price**: Display the price of individual products.
1. **Add To Cart**: Add customizable add to cart buttons for easy purchasing.
1. **Product Rating**: Display ratings and reviews for products.
1. **Product Stock**: Display stock availability for products.
1. **Product Meta**: Display additional information about products, such as SKU and categories.
1. **Product Content**: Customize the main content area of product descriptions.
1. **Short Description**: Display brief summaries or teasers of products.
1. **Product Data Tabs**: Organize product information into tabbed sections for better organization.
1. **Additional Information**: Display additional details about products, such as dimensions and weight.
1. **Product Related**: Showcase related products to encourage additional purchases.
1. **Upsells**: Promote upsell products to increase average order value.
1. **Products**: Display a grid or list of products with customizable settings.
1. **Custom Add To Cart**: Customize the add to cart button for specific products.
1. **WooCommerce Pages**: Design and customize WooCommerce-specific pages, such as the cart and checkout pages.
1. **Product Categories**: Display product categories for easy navigation within your store.
1. **Menu Cart**: Display a cart icon in your navigation menu for easy access to the shopping cart.
1. **Cart**: Customize the appearance and functionality of the shopping cart page.
1. **Checkout**: Customize the appearance and functionality of the checkout page.
1. **My Account**: Customize the appearance and functionality of the customer account area.
1. **Purchase Summary**: Display a summary of purchases during the checkout process.
1. **WooCommerce Notices**: Customize the appearance and functionality of WooCommerce notices, such as order confirmation messages.

Build professional websites with **[Elementor Pro](https://go.elementor.com/wp-repo-description-tab-elementor-pro-elementor-pro/)**!

= 🔒 SECURITY AND COMPLIANCE =

Elementor places a paramount focus on security, evident through our acquisition of industry certifications such as ISO/IEC 27001, ISO/IEC 27017, ISO/IEC 27018, ISO/IEC 27701, and SOC 2 Type II. These certifications underscore our commitment to implementing robust security measures, and highlight our dedication to adhering to recognized industry standards.

We encourage ethical security research through our [Bug Bounty program](https://go.elementor.com/wp-repo-description-tab-bug-crowd-bug-bounty-program/). We collaborate with leading bug bounty services to provide opportunities for researchers to report vulnerabilities in our services. Our bounty programs include a triage team available 24/7/365.

For more information: [Trust Center](https://go.elementor.com/trust-center/).

= ♿ Accessibility Best Practices =

Elementor offers accessibility tools and enhancements to help you provide a better experience for all users. Including HTML 5 semantic, full keyboard navigation menu, ongoing improvement of features, widget, and more.

= 🌐 Translated to 63+ languages, includes RTL support =

Elementor supports multiple languages, typographies, and RTL, with editor translations in [over 63 languages](https://go.elementor.com/wp-repo-description-tab-wordpress-plugin-translate/).

It’s also compatible with WPML, Polylang, TranslatePress, Weglot, and more. To contribute, add a new language via translate.wordpress.org. See our guide on [how to translate and localize the plugin](https://go.elementor.com/wp-repo-description-tab-help-center-translate/).

= ⏩ Use of 3rd Party Services =

To improve the user experience, Elementor may use the following 3rd party services if the required feature is enabled:

- Google Fonts – are loaded to add additional fonts to your website. Google’s [TOS](https://policies.google.com/terms) and [Privacy Policy](https://policies.google.com/privacy)
- Some Elementor features require loading assets from Elementor.com. These assets are not used for tracking unless explicitly mentioned, requiring your approval and manual opt-in. Learn more in our [TOS](https://go.elementor.com/wp-repo-description-tab-elementor-plugin-terms/) and [Privacy Policy](https://go.elementor.com/wp-repo-description-tab-elementor-plugin-privacy/).

= 📧 Related Products by Elementor =

**[Image Optimizer](https://go.elementor.com/wp-repo-description-tab-elementor-io/)**: Superior image compression for faster, high-quality website performance.

**[Site Mailer](https://go.elementor.com/wp-repo-description-tab-elementor-sm/)**: Reliable email management without SMTP plugins, keeping your communications streamlined and efficient.

= 📣 See What Our Users Have to Say =

> “Elementor is hands down the best page builder out there” – ★★★★★ *[Graphicvision1](https://wordpress.org/support/topic/elementor-is-hands-down-the-best-page-builder-out-there/)*

> “An incredibly user-friendly plugin” – ★★★★★ *[Hyeyoga](https://wordpress.org/support/topic/a-wonderful-experience/)*

> “Easily, my most used WP plugin” – ★★★★★ *[Xander Venske](https://wordpress.org/support/topic/easily-my-most-used-wp-plugin/)*

> “I upgraded to the Pro version and just love this plugin!” – ★★★★★ *[Andybarn56](https://wordpress.org/support/topic/love-elementor-17/)*

> “Excellent product with great tech support” – ★★★★★ *[Martywilsonnj](https://wordpress.org/support/topic/excellent-product-with-great-tech-support/)*

*[More testimonials](https://wordpress.org/support/plugin/elementor/reviews/?filter=5)*

= 🌍 Join a Global Community =

Join a global community that helps each other achieve their goals.

- [Discord Community](https://elemn.to/discord) – Topic and language-specific channels, plus Ella, an amazing AI helper, ready to assist you.
- [Facebook Community](https://go.elementor.com/wp-repo-description-tab-facebook-group/) - Over 150K+ members, offering support, advice, feedback, and tutorials.
- [GitHub Community](https://go.elementor.com/wp-repo-description-tab-github-repo/) - Get information about releases, request features, or report a bug.
- [Elementor Addons, Themes, and Kits](https://go.elementor.com/wp-repo-description-tab-addons-dozens-of-elementor-addons/) Themes and Kits – created specifically for Elementor.
- Learn valuable insights and techniques from our [YouTube Channel](https://go.elementor.com/wp-repo-description-tab-youtube-channel/).
- Access our [Academy](https://go.elementor.com/wp-repo-description-tab-academy-elementor-academy/) and [Help Center](https://go.elementor.com/wp-repo-description-tab-help-center-help-center/) - Find guides, tutorials, and resources to answer your questions and boost creativity.
- Show your support by [rating us on WordPress](https://go.elementor.com/wp-repo-description-tab-wordpress-plugin-review/). Your feedback fuels our growth! 🤗
- If you have questions or need support, visit the [Plugin's Forum](https://go.elementor.com/wp-repo-description-tab-wordpress-plugin-forum/). Elementor Pro users can get 24/7 premium support, or visit [Elementor Website Builder](https://go.elementor.com/wp-repo-description-tab-homepage-elementor-website-builder/).

== Installation ==

= Minimum Requirements =

* WordPress 6.5 or greater
* PHP version 7.4 or greater
* MySQL version 5.0 or greater

= Recommended Requirements =

* PHP version 8.1 or greater
* MySQL version 5.6 or greater
* WordPress Memory limit of 64 MB or greater (128 MB or higher is preferred)

https://www.youtube.com/watch?v=9EZ159ryFNs

= Installation =

1. Install using the WordPress built-in Plugin installer, or Extract the zip file and drop the contents in the `wp-content/plugins/` directory of your WordPress installation.
2. Activate the plugin through the 'Plugins' menu in WordPress.
3. Go to Pages > Add New
4. Press the 'Edit with Elementor' button.
5. Now you can drag and drop widgets from the left panel onto the content area, as well as add new sections and columns that make up the page structure.

For documentation and tutorials visit our [Knowledge Base](https://elementor.com/help/?utm_source=wp-repo&utm_medium=link&utm_campaign=readme).

== Frequently Asked Questions ==

**How do I install Elementor?**

To install the free version of Elementor, follow the steps below:
From your WordPress dashboard -> Go to Plugins -> Click on 'Add new'-> In the Search field, enter Elementor and choose Elementor website builder.
Press install -> After installation, click Activate.

**Does Elementor work with all the themes?**

Elementor works all the themes that respect the coding standards of WordPress set by its Codex. It is recommended to use Elementor's [Hello Theme](https://go.elementor.com/wp-repo-description-tab-hello-theme-hello-theme/), a lightweight blank canvas, to enjoy full flexibility when using Elementor, and optimize your experience.

**Is Elementor compatible with Gutenberg?**

Elementor and Gutenberg work seamlessly together. As a user, you can easily decide which editor to use at every point while editing your site.

**Can I create an online store?**

Yes, with the Elementor Pro WooCommerce Builder you can customize every page of your store to create an amazing customer experience that drives sales.

**Does it work with other WordPress plugins? **

It works with almost all the plugins. If you experience an incompatibility issue, please report it to us and to the plugin that conflicts with Elementor.

**Do I need to know how to code?**

No! Elementor provides you with all the widgets and features that you need to build a professional website without using code.

**Do I need to know how to design?**

No, you can choose between professionally designed kits and templates that fit toevery industry and have all you need to create your own professional website.

**Will Elementor slow down my website?**

As Elementor prioritizes speed and performance, you enjoy better and faster performance with each new version of Elementor. When testing the same page layout on older versions you can see a significant performance improvement, from a score of 82 in Google PageSpeed Insight in version 3.1, to a score of 95 i in version 3.5.

**Is my site secure with Elementor?**

The security of your website is extremely important to us and we take proactive measures to assure that your websites are secure. Elementor is ISO 27001 certified, and has a dedicated team of security professionals that implements industry best-practices for maximum security and compliance, 24/7.

There is also a managed security Bug Bounty program, utilizing the community power by enabling 24/7/365 crowdsourced vulnerability detection.

**Can I buy templates separately?**

Of course, you can use any template that supports Elementor.

**Is Elementor compatible with Post and Custom Post Types?**

Of course! You can set which post types will enable Elementor in the settings page.

**What is the difference between Elementor's free Plugin and Elementor Pro**

Elementor’s Free version allows you to explore our revolutionary drag & drop live editor, basic widgets and templates. Elementor Pro (Essential, Advanced, Expert, Studio, and Agency) empowers you with more professional tools that speed up your workflow, give you access to human-powered support, help you build more advanced content, and convert visitors. See full comparison here.

**How can I become a Contributor**

If you want to contribute, go to our [Elementor GitHub Repository](https://github.com/elementor/elementor) and see where you can help.
You can also add a new language via [translate.wordpress.org](https://go.elementor.com/wp-repo-description-tab-wordpress-plugin-translate/). We’ve built a short guide explaining [how to translate and localize the plugin](https://go.elementor.com/wp-repo-description-tab-wordpress-plugin-translate-faq/).

== Screenshots ==

1. **Visual Drag and Drop Editor** - Design your website layouts and place any element anywhere on the page for pixel-perfect designs.
2. **Full Design System** - Enjoy a professional workflow and ensure consistency across your site. Define your settings, use them globally, and instantly adjust them any time.
3. **Responsive Design** Fully edit your website and customize the behavior on desktop, tablet, & mobile to optimize the visitor experience on every device.
4. **Kits and Templates** - Jumpstart your web creation process or get inspired with professionally-designed templates or full website kits available for your immediate customization.
5. **Nested Elements** Leverage Elementor's Nested widgets to place any widget inside the content area of another widget - like Tabs, and Accordion for complete design flexibility.
6. **Motion Effects** - Add entrance animations and transitions to any element in your website to captivate visitors.

== Changelog ==

= 3.30.0 - 2025-07-01 =

* New: Added support for setting custom units in size controls - Editor V4 ([#31287](https://github.com/elementor/elementor/issues/31287))
* New: Added reset control visibility via the floating action bar - Editor V4 ([#31356](https://github.com/elementor/elementor/issues/31356))
* New: Added JS handler infrastructure for the new elements system - Editor V4
* New: Introduced modular YouTube element built with new structure and JS handlers – Editor V4
* New: Added support for context-aware editing memory - Editor V4
* New: Enabled Smart Unit Typing - allows typing values with units directly - Editor V4
* New: Added ID control to Settings section under General tab - Editor V4
* New: Added Anchor Offset control to Position section under Style tab - Editor V4
* New: Added Display None control to Layout section under Style tab - Editor V4
* New: Added Object Fit control to Size section under Style tab - Editor V4
* New: Added Columns control to Typography section under Style tab - Editor V4
* New: Added Aspect Ratio control to Size section under Style tab - Editor V4
* New: Added group-level style indicators to show where class-based styles are applied – Editor V4
* New: Added Indications Popover for visualizing class-based style origins – Editor V4
* New: Introduced class permissions for non-admin users - Editor V4
* Tweak: Added title hover and focus color options to Icon Box and Image Box widgets ([#29948](https://github.com/elementor/elementor/issues/29948))
* Tweak: Added support for registering custom mask shapes ([#19396](https://github.com/elementor/elementor/issues/19396))
* Tweak: User-defined class names now appear as-is in the final code output in Class Management - Editor V4 ([#31055](https://github.com/elementor/elementor/issues/31055))
* Tweak: Renamed "Kits" to "Website Template" across the interface
* Tweak: Added Settings section inside the General tab - Editor V4
* Tweak: Merged "Editor Top Bar" feature into the core version
* Tweak: Merged "Load Google Fonts locally" feature into the core version
* Tweak: Activated "Optimized Markup" feature for new sites
* Tweak: Promoted "Element Caching" feature to Stable status
* Tweak: Added new mask shapes
* Tweak: Replaced select control with a visual choice control in Mask shapes
* Tweak: Add image `height`, `object-fit`, `object-position`, `box-shadow` in Image Box widget
* Tweak: Standardized naming convention for items in the editor `app-bar`
* Tweak: Consolidated control visibility and layout into a single Style tab in Progress Bar widget
* Tweak: Updated minimum required WordPress version to 6.6
* Fix: Global CSS transition with higher specificity prevents Container transitions from being applied ([#30460](https://github.com/elementor/elementor/issues/30460))
* Fix: Global CSS classes are lost when publishing from multiple tabs - Editor V4
* Fix: Redundant spacing appears below the image in the Image Box widget
* Fix: Scrolling to anchors no longer works inside the Editor

[See changelog for all versions.](https://go.elementor.com/full-changelog/)
