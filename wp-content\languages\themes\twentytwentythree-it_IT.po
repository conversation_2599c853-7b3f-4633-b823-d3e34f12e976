# Translation of Themes - Twenty Twenty-Three in Italian
# This file is distributed under the same license as the Themes - Twenty Twenty-Three package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-11-28 15:08:45+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: it\n"
"Project-Id-Version: Themes - Twenty Twenty-Three\n"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Three is designed to take advantage of the new design tools introduced in WordPress 6.1. With a clean, blank base as a starting point, this default theme includes ten diverse style variations created by members of the WordPress community. Whether you want to build a complex or incredibly simple website, you can do it quickly and intuitively through the bundled styles or dive into creation and full customization yourself."
msgstr "Twenty Twenty-Three è stato pensato per sfruttare tutti gli strumenti introdotti con WordPress 6.1. Questo tema predefinito è pulito e tutto da inventare, ma include dieci variazioni di stile diverse tra loro, fatte dai membri della community di WordPress. Che tu voglia creare un sito web complesso o incredibilmente semplice, lo potrai fare in modo veloce e intuitivo grazie agli stili inclusi. Oppure potrai immergerti in un tuo proprio flusso creativo e di completa personalizzazione del tema."

#. Theme Name of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Three"
msgstr "Twenty Twenty-Three"

#: patterns/hidden-heading.php:9
msgctxt "Main heading for homepage"
msgid "Mindblown: a blog about philosophy."
msgstr "Mindblown: un blog sulla filosofia."

#: patterns/hidden-heading.php
msgctxt "Pattern title"
msgid "Hidden Heading for Homepage"
msgstr "Titolo nascosto per l'homepage"

#: patterns/post-meta.php
msgctxt "Pattern description"
msgid "Post meta information with separator on the top."
msgstr "Meta dell'articolo con un separatore in alto."

#: patterns/footer-default.php
msgctxt "Pattern description"
msgid "Footer with site title and powered by WordPress."
msgstr "Footer con titolo del sito e frase powered by WordPress."

#: patterns/call-to-action.php
msgctxt "Pattern description"
msgid "Left-aligned text with a CTA button and a separator."
msgstr "Testo allineato a sinistra con un pulsante CTA e un separatore."

#: theme.json
msgctxt "Template part name"
msgid "Comments Template Part"
msgstr "Commenti parte dei template"

#: theme.json
msgctxt "Template part name"
msgid "Post Meta"
msgstr "Meta dell'articolo"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "Footer"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "Header"

#: theme.json
msgctxt "Font family name"
msgid "Source Serif Pro"
msgstr "Source Serif Pro"

#: theme.json
msgctxt "Font family name"
msgid "System Font"
msgstr "Font di sistema"

#: theme.json
msgctxt "Font family name"
msgid "Inter"
msgstr "Inter"

#: theme.json
msgctxt "Font family name"
msgid "IBM Plex Mono"
msgstr "IBM Plex Mono"

#: theme.json
msgctxt "Font family name"
msgid "DM Sans"
msgstr "DM Sans"

#: theme.json
msgctxt "Custom template name"
msgid "404"
msgstr "404"

#: theme.json
msgctxt "Custom template name"
msgid "Blog (Alternative)"
msgstr "Blog (alternativo)"

#: theme.json
msgctxt "Custom template name"
msgid "Blank"
msgstr "Vuoto"

#: styles/whisper.json
msgctxt "Style variation name"
msgid "Whisper"
msgstr "Sussurro"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Tertiary to Secondary to Primary Fixed"
msgstr "Da terziario a secondario a primario, non scorrevole"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Primary to Secondary to Tertiary Fixed"
msgstr "Da primario a secondario a terziario, non scorrevole"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Primary to Secondary to Tertiary"
msgstr "Da primario a secondario a terziario"

#: styles/sherbet.json
msgctxt "Style variation name"
msgid "Sherbet"
msgstr "Sherbet"

#: styles/pitch.json
msgctxt "Font size name"
msgid "2X Large"
msgstr "Grande 2x"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "Molto grande"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Large"
msgstr "Grande"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Medium"
msgstr "Medio"

#: styles/pitch.json
msgctxt "Font size name"
msgid "small"
msgstr "piccolo"

#: styles/pitch.json
msgctxt "Space size name"
msgid "7"
msgstr "7"

#: styles/pitch.json
msgctxt "Style variation name"
msgid "Pitch"
msgstr "Pece"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Dots"
msgstr "Puntinato"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Base to Primary"
msgstr "Da base a primario"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Tertiary to Secondary"
msgstr "Da terziario a secondario"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Secondary to Primary"
msgstr "Da secondario a primario"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Primary to Secondary"
msgstr "Da primario a secondario"

#: styles/pilgrimage.json
msgctxt "Style variation name"
msgid "Pilgrimage"
msgstr "Pellegrino"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Gigantic"
msgstr "Gigante"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Huge"
msgstr "Enorme"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Normal"
msgstr "Normale"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Tiny"
msgstr "Piccolissimo"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "6"
msgstr "6"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "5"
msgstr "5"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "4"
msgstr "4"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "3"
msgstr "3"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "2"
msgstr "2"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "1"
msgstr "1"

#: styles/marigold.json
msgctxt "Style variation name"
msgid "Marigold"
msgstr "Calendula"

#: styles/grapes.json
msgctxt "Style variation name"
msgid "Grapes"
msgstr "Uva"

#: styles/electric.json
msgctxt "Style variation name"
msgid "Electric"
msgstr "Elettrico"

#: styles/canary.json
msgctxt "Style variation name"
msgid "Canary"
msgstr "Canarino"

#: styles/block-out.json styles/canary.json styles/pilgrimage.json
#: styles/sherbet.json
msgctxt "Duotone name"
msgid "Default filter"
msgstr "Filtro predefinito"

#: styles/block-out.json
msgctxt "Style variation name"
msgid "Block out"
msgstr "Block out"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Tertiary"
msgstr "Terziario"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Secondary"
msgstr "Secondario"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Primary"
msgstr "Primario"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Contrast"
msgstr "Contrasto"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Base"
msgstr "Base"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Primary to Tertiary"
msgstr "Da primario a terziario"

#: styles/aubergine.json styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Tertiary to Primary"
msgstr "Da terziario a primario"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Base to Secondary to Base"
msgstr "Da base a secondario a base"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Secondary to Base"
msgstr "Da secondario a base"

#: styles/aubergine.json
msgctxt "Style variation name"
msgid "Aubergine"
msgstr "Melanzana"

#: patterns/post-meta.php:65
msgctxt "Label for a list of post tags"
msgid "Tags:"
msgstr "Tag:"

#: patterns/post-meta.php:49
msgctxt "Preposition to show the relationship between the post and its author"
msgid "by"
msgstr "da"

#: patterns/post-meta.php:37
msgctxt "Preposition to show the relationship between the post and its categories"
msgid "in"
msgstr "in"

#: patterns/post-meta.php:29
msgctxt "Verb to explain the publication status of a post"
msgid "Posted"
msgstr "Pubblicato"

#: patterns/post-meta.php
msgctxt "Pattern title"
msgid "Post Meta"
msgstr "Metadati dell'articolo"

#: patterns/hidden-no-results.php:10
msgctxt "Message explaining that there are no results returned from a search"
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "Non c’è nessuna corrispondenza con i termini di ricerca che hai indicato. Riprova con termini diversi."

#: patterns/hidden-no-results.php
msgctxt "Pattern title"
msgid "Hidden No Results Content"
msgstr "Contenuto nascosto per nessun risultato di ricerca"

#: patterns/hidden-comments.php:13
msgctxt "Title of comments section"
msgid "Comments"
msgstr "Commenti"

#: patterns/hidden-comments.php
msgctxt "Pattern title"
msgid "Hidden Comments"
msgstr "Commenti nascosti"

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgid "Search"
msgstr "Cerca"

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgctxt "placeholder for search field"
msgid "Search..."
msgstr "Cerca…"

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgctxt "label"
msgid "Search"
msgstr "Cerca"

#: patterns/hidden-404.php:19
msgctxt "Message to convey that a webpage could not be found"
msgid "This page could not be found."
msgstr "Non è stato possibile trovare questa pagina."

#: patterns/hidden-404.php:13
msgctxt "Error code for a webpage that is not found."
msgid "404"
msgstr "404"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "Hidden 404"
msgstr "404 nascosto"

#. Translators: WordPress link.
#: patterns/footer-default.php:20
msgid "Proudly powered by %s"
msgstr "Proudly powered by %s"

#: patterns/footer-default.php
msgctxt "Pattern title"
msgid "Default Footer"
msgstr "Footer predefinito"

#: patterns/call-to-action.php:25
msgctxt "sample content for call to action button"
msgid "Get In Touch"
msgstr "Contattaci"

#: patterns/call-to-action.php:16
msgctxt "sample content for call to action"
msgid "Got any book recommendations?"
msgstr "Vuoi raccomandare qualche libro?"

#: patterns/call-to-action.php
msgctxt "Pattern title"
msgid "Call to action"
msgstr "Invito all'azione"

#. Author URI of the theme
#: style.css patterns/footer-default.php:21
#, gp-priority: low
msgid "https://wordpress.org"
msgstr "https://wordpress.org"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "il team di WordPress"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentythree"
msgstr "https://it.wordpress.org/themes/twentytwentythree"
