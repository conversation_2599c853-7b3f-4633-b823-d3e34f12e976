!function(){"use strict";var e={d:function(t,n){for(var l in n)e.o(n,l)&&!e.o(t,l)&&Object.defineProperty(t,l,{enumerable:!0,get:n[l]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{EXPERIMENTAL_FEATURES:function(){return C},PopoverScrollableContent:function(){return bo},controlActionsMenu:function(){return xe},init:function(){return ua},injectIntoClassSelectorActions:function(){return ge},registerControlReplacement:function(){return x},registerStyleProviderToColors:function(){return z},useBoundProp:function(){return n.useBoundProp},useFontFamilies:function(){return hr},usePanelActions:function(){return Eo},usePanelStatus:function(){return fo},useSectionWidth:function(){return Ue}});var n=window.elementorV2.editorControls,l=window.React,r=window.elementorV2.editorElements,o=window.elementorV2.editorStylesRepository,a=window.elementorV2.editorUi,i=window.elementorV2.icons,s=window.elementorV2.locations,c=window.elementorV2.ui,m=window.wp.i18n,u=window.elementorV2.utils,p=window.elementorV2.editorDocuments,d=window.elementorV2.editorProps,E=window.elementorV2.editorV1Adapters,f=window.elementorV2.editorPanels,b=window.elementorV2.session,y=window.elementorV2.menus,v=window.elementorV2.editorResponsive,g=window.elementorV2.editorStyles,_=window.elementorV2.editorCanvas,h=window.elementorV2.editor,w=window.elementorV2.schema,S=window.elementorV2.wpMedia,C={V_3_30:"e_v_3_30",V_3_31:"e_v_3_31"},{registerControlReplacement:x,getControlReplacements:T}=(0,n.createControlReplacementsRegistry)(),I={name:"default",getThemeColor:null},k=new Map,z=(e,t)=>{k.set(e,t)},P=e=>k.get(e)??I,D=(0,l.createContext)(null);function R({children:e,prop:t}){return l.createElement(D.Provider,{value:{prop:t}},e)}function L(){const e=(0,l.useContext)(D);if(!e)throw new Error("useClassesProp must be used within a ClassesPropProvider");return e.prop}var V=(0,l.createContext)(null);function A({children:e,element:t,elementType:n}){return l.createElement(V.Provider,{value:{element:t,elementType:n}},e)}function N(){const e=(0,l.useContext)(V);if(!e)throw new Error("useElement must be used within a ElementProvider");return e}var B=(0,u.createError)({code:"control_type_not_found",message:"Control type not found."}),O=(0,u.createError)({code:"provider_not_found",message:"Styles provider not found."}),M=(0,u.createError)({code:"provider_cannot_update_props",message:"Styles provider doesn't support updating props."}),F=(0,u.createError)({code:"style_not_found_under_provider",message:"Style not found under the provider."}),$=(0,l.createContext)(null);function j({children:e,...t}){const n=null===t.id?null:W(t.id),{userCan:r}=(0,o.useUserStylesCapability)();if(t.id&&!n)throw new O({context:{styleId:t.id}});const a=r(n?.getKey()??"").updateProps;return l.createElement($.Provider,{value:{...t,provider:n,canEdit:a}},e)}function U(){const e=(0,l.useContext)($);if(!e)throw new Error("useStyle must be used within a StyleProvider");return e}function W(e){return o.stylesRepository.getProviders().find((t=>t.actions.all().find((t=>t.id===e))))??null}var G=e=>e&&e!==o.ELEMENTS_BASE_STYLES_PROVIDER_KEY?(0,o.isElementsStylesProvider)(e)?"accent":P(e).name:"default",K=e=>e&&e!==o.ELEMENTS_BASE_STYLES_PROVIDER_KEY?(0,o.isElementsStylesProvider)(e)?e=>e.palette.accent.main:P(e).getThemeColor:null;function H(e){const{_group:t,_action:n,...l}=e;return l}var J=l.forwardRef((function({selected:e,options:t,entityName:n,onSelect:r,placeholder:o,onCreate:a,validate:i,renderEmptyState:s,...m},u){const{inputValue:p,setInputValue:d,error:E,setError:f,inputHandlers:b}=function(e){const[t,n]=(0,l.useState)(""),[r,o]=(0,l.useState)(null);return{inputValue:t,setInputValue:n,error:r,setError:o,inputHandlers:{onChange:t=>{const{value:l}=t.target;if(n(l),!e)return;if(!l)return void o(null);const{isValid:r,errorMessage:a}=e(l,"inputChange");o(r?null:a)},onBlur:()=>{n(""),o(null)}}}}(i),{open:y,openDropdown:v,closeDropdown:g}=function(e=!1){const[t,n]=(0,l.useState)(e);return{open:t,openDropdown:()=>n(!0),closeDropdown:()=>n(!1)}}(m.open),{createOption:_,loading:h}=function(e){const{onCreate:t,validate:n,setInputValue:r,setError:o,closeDropdown:a}=e,[i,s]=(0,l.useState)(!1);return t?{createOption:async e=>{if(s(!0),n){const{isValid:t,errorMessage:l}=n(e,"create");if(!t)return o(l),void s(!1)}try{r(""),a(),await t(e)}catch{}finally{s(!1)}},loading:i}:{createOption:null,loading:!1}}({onCreate:a,validate:i,setInputValue:d,setError:f,closeDropdown:g}),[w,S]=(0,l.useMemo)((()=>[t,e].map((e=>function(e,t){return e.map((e=>({...e,_group:`Existing ${t??"options"}`})))}(e,n?.plural)))),[t,e,n?.plural]),C=function(e){const{options:t,onSelect:n,createOption:l,setInputValue:r,closeDropdown:o}=e;if(n||l)return async(e,n,i,s)=>{const c=s?.option;if(!c||"object"==typeof c&&c.fixed)return;const m=n.filter((e=>"string"!=typeof e));switch(i){case"removeOption":a(m,"removeOption",c);break;case"selectOption":{const e=c;if("create"===e._action){const t=e.value;return l?.(t)}a(m,"selectOption",e);break}case"createOption":{const e=c,n=t.find((t=>t.label.toLocaleLowerCase()===e.toLocaleLowerCase()));if(!n)return l?.(e);m.push(n),a(m,"selectOption",n);break}}r(""),o()};function a(e,t,l){n?.(e.map((e=>H(e))),t,H(l))}}({options:w,onSelect:r,createOption:_,setInputValue:d,closeDropdown:g}),x=function(e){const{options:t,selected:n,onCreate:l,entityName:r}=e,o=(0,c.createFilterOptions)();return(e,a)=>{const i=n.map((e=>e.value)),s=o(e.filter((e=>!i.includes(e.value))),a),c=t.some((e=>a.inputValue===e.label));return Boolean(l)&&""!==a.inputValue&&!i.includes(a.inputValue)&&!c&&s.unshift({label:`Create "${a.inputValue}"`,value:a.inputValue,_group:`Create a new ${r?.singular??"option"}`,key:`create-${a.inputValue}`,_action:"create"}),s}}({options:t,selected:e,onCreate:a,entityName:n}),T=Boolean(a)||p.length<2||void 0;return l.createElement(c.Autocomplete,{renderTags:(e,t)=>e.map(((e,n)=>l.createElement(c.Chip,{size:"tiny",...t({index:n}),key:e.key??e.value??e.label,label:e.label}))),...m,ref:u,freeSolo:T,forcePopupIcon:!1,multiple:!0,clearOnBlur:!0,selectOnFocus:!0,disableClearable:!0,handleHomeEndKeys:!0,disabled:h,open:y,onOpen:v,onClose:g,disableCloseOnSelect:!0,value:S,options:w,ListboxComponent:E?l.forwardRef(((e,t)=>l.createElement(q,{ref:t,error:E}))):void 0,renderGroup:e=>l.createElement(Y,{...e}),inputValue:p,renderInput:e=>l.createElement(c.TextField,{...e,error:Boolean(E),placeholder:o,...b,sx:e=>({".MuiAutocomplete-inputRoot.MuiInputBase-adornedStart":{paddingLeft:e.spacing(.25),paddingRight:e.spacing(.25)}})}),onChange:C,getOptionLabel:e=>"string"==typeof e?e:e.label,getOptionKey:e=>"string"==typeof e?e:e.key??e.value??e.label,filterOptions:x,groupBy:e=>e._group??"",renderOption:(e,t)=>{const{_group:n,label:r}=t;return l.createElement("li",{...e,style:{display:"block",textOverflow:"ellipsis"},"data-group":n},r)},noOptionsText:s?.({searchValue:p,onClear:()=>{d(""),g()}}),isOptionEqualToValue:(e,t)=>"string"==typeof e?e===t:e.value===t.value})})),Y=e=>{const t=`combobox-group-${(0,l.useId)().replace(/:/g,"_")}`;return l.createElement(X,{role:"group","aria-labelledby":t},l.createElement(Z,{id:t}," ",e.group),l.createElement(Q,{role:"listbox"},e.children))},q=l.forwardRef((({error:e="error"},t)=>l.createElement(c.Box,{ref:t,sx:e=>({padding:e.spacing(2)})},l.createElement(c.Typography,{variant:"caption",sx:{color:"error.main",display:"inline-block"}},e)))),X=(0,c.styled)("li")`
	&:not( :last-of-type ) {
		border-bottom: 1px solid ${({theme:e})=>e.palette.divider};
	}
`,Z=(0,c.styled)(c.Box)((({theme:e})=>({position:"sticky",top:"-8px",padding:e.spacing(1,2),color:e.palette.text.tertiary,backgroundColor:e.palette.primary.contrastText}))),Q=(0,c.styled)("ul")`
	padding: 0;
`,ee=(0,l.createContext)(null),te=()=>{const e=(0,l.useContext)(ee);if(!e)throw new Error("useCssClass must be used within a CssClassProvider");return e};function ne({children:e,...t}){return l.createElement(ee.Provider,{value:t},e)}var le=(0,c.styled)("div",{shouldForwardProp:e=>!["isOverridden","getColor"].includes(e)})`
	width: 5px;
	height: 5px;
	border-radius: 50%;
	background-color: ${({theme:e,isOverridden:t,getColor:n})=>{if(t)return e.palette.warning.light;const l=n?.(e);return l??e.palette.text.disabled}};
`;function re(){const{id:e,setId:t}=U(),{element:n}=N(),o=(0,E.isExperimentActive)(C.V_3_30),a=oe(),i=ae(),s=(0,l.useMemo)((()=>(0,E.undoable)({do:({classId:t})=>{const n=e;return i(t),n},undo:({classId:e},n)=>{a(e),t(n)}},{title:(0,r.getElementLabel)(n.id),subtitle:({classLabel:e})=>(0,m.__)("class %s removed","elementor").replace("%s",e)})),[e,a,n.id,i,t]),c=(0,l.useCallback)((({classId:e})=>{i(e)}),[i]);return o?s:c}function oe(){const{element:e}=N(),{setId:t}=U(),{setClasses:n,getAppliedClasses:r}=ie();return(0,l.useCallback)((l=>{const o=r();if(o.includes(l))throw new Error(`Class ${l} is already applied to element ${e.id}, cannot re-apply.`);const a=[...o,l];n(a),t(l)}),[e.id,r,t,n])}function ae(){const{element:e}=N(),{id:t,setId:n}=U(),{setClasses:r,getAppliedClasses:o}=ie();return(0,l.useCallback)((l=>{const a=o();if(!a.includes(l))throw new Error(`Class ${l} is not applied to element ${e.id}, cannot unapply it.`);const i=a.filter((e=>e!==l));r(i),t===l&&n(i[0]??null)}),[t,e.id,o,n,r])}function ie(){const{element:e}=N(),t=L(),n=(0,E.isExperimentActive)(C.V_3_30);return(0,l.useMemo)((()=>({setClasses:l=>{(0,r.updateElementSettings)({id:e.id,props:{[t]:d.classesPropTypeUtil.create(l)},withHistory:!n}),n&&(0,p.setDocumentModifiedStatus)(!0)},getAppliedClasses:()=>(0,r.getElementSetting)(e.id,t)?.value||[]})),[t,e.id,n])}var se=[{key:"normal",value:null},{key:"hover",value:"hover"},{key:"focus",value:"focus"},{key:"active",value:"active"}];function ce({popupState:e,anchorEl:t,fixed:n}){const{provider:r}=te();return l.createElement(c.Menu,{MenuListProps:{dense:!0,sx:{minWidth:"160px"}},...(0,c.bindMenu)(e),anchorEl:t,anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{horizontal:"left",vertical:-4},onKeyDown:e=>{e.stopPropagation()},disableAutoFocusItem:!0},function({provider:e,closeMenu:t,fixed:n}){if(!e)return[];const r=o.stylesRepository.getProviderByKey(e),a=r?.actions,i=a?.update,s=!n,m=[i&&l.createElement(pe,{key:"rename-class",closeMenu:t}),s&&l.createElement(ue,{key:"unapply-class",closeMenu:t})].filter(Boolean);return m.length&&(m.unshift(l.createElement(c.MenuSubheader,{key:"provider-label",sx:{typography:"caption",color:"text.secondary",pb:.5,pt:1,textTransform:"capitalize"}},r?.labels?.singular)),m.push(l.createElement(c.Divider,{key:"provider-actions-divider"}))),m}({provider:r,closeMenu:e.close,fixed:n}),l.createElement(c.MenuSubheader,{sx:{typography:"caption",color:"text.secondary",pb:.5,pt:1}},(0,m.__)("States","elementor")),se.map((t=>l.createElement(me,{key:t.key,state:t.value,closeMenu:e.close}))))}function me({state:e,closeMenu:t,...n}){const{id:r,provider:i}=te(),{id:s,setId:u,setMetaState:p,meta:d}=U(),{state:E}=d,{userCan:f}=(0,o.useUserStylesCapability)(),b=function(e){const{meta:t}=U(),n=o.stylesRepository.all().find((t=>t.id===e));return Object.fromEntries(n?.variants.filter((e=>t.breakpoint===e.meta.breakpoint)).map((e=>[e.meta.state??"normal",!0]))??[])}(r),y=!e||f(i??"").updateProps,v=b[e??"normal"]??!1,g=!y&&!v,_=r===s,h=e===E&&_;return l.createElement(a.MenuListItem,{...n,selected:h,disabled:g,sx:{textTransform:"capitalize"},onClick:()=>{_||u(r),p(e),t()}},l.createElement(a.MenuItemInfotip,{showInfoTip:g,content:(0,m.__)("With your current role, you can only use existing states.","elementor")},l.createElement(c.Stack,{gap:.75,direction:"row",alignItems:"center"},v&&l.createElement(le,{"aria-label":(0,m.__)("Has style","elementor"),getColor:K(i??"")}),e??"normal")))}function ue({closeMenu:e,...t}){const{id:n,label:r}=te(),o=re();return n?l.createElement(a.MenuListItem,{...t,onClick:()=>{o({classId:n,classLabel:r}),e()}},(0,m.__)("Remove","elementor")):null}function pe({closeMenu:e}){const{handleRename:t,provider:n}=te(),{userCan:r}=(0,o.useUserStylesCapability)();if(!n)return null;const i=r(n).update;return l.createElement(a.MenuListItem,{disabled:!i,onClick:()=>{e(),t()}},l.createElement(a.MenuItemInfotip,{showInfoTip:!i,content:(0,m.__)("With your current role, you can use existing classes but can’t modify them.","elementor")},(0,m.__)("Rename","elementor")))}var de="tiny";function Ee(e){const{chipProps:t,icon:n,color:r,fixed:s,...u}=e,{id:p,provider:d,label:E,isActive:f,onClickActive:b,renameLabel:y,setError:v}=u,{meta:g,setMetaState:_}=U(),h=(0,c.usePopupState)({variant:"popover"}),[w,S]=(0,l.useState)(null),{onDelete:C,...x}=t,{userCan:T}=(0,o.useUserStylesCapability)(),{ref:I,isEditing:k,openEditMode:z,error:P,getProps:D}=(0,a.useEditable)({value:E,onSubmit:y,validation:fe,onError:v}),R=P?"error":r,L=d?o.stylesRepository.getProviderByKey(d)?.actions:null,V=Boolean(L?.update)&&T(d??"")?.update,A=f&&g.state;return l.createElement(l.Fragment,null,l.createElement(c.UnstableChipGroup,{ref:S,...x,"aria-label":`Edit ${E}`,role:"group",sx:e=>({"&.MuiChipGroup-root.MuiAutocomplete-tag":{margin:e.spacing(.125)}})},l.createElement(c.Chip,{size:de,label:k?l.createElement(a.EditableField,{ref:I,...D()}):l.createElement(a.EllipsisWithTooltip,{maxWidth:"10ch",title:E,as:"div"}),variant:!f||g.state||k?"standard":"filled",shape:"rounded",icon:n,color:R,onClick:()=>{A?_(null):V&&f?z():b(p)},"aria-pressed":f,sx:e=>({lineHeight:1,cursor:f&&V&&!A?"text":"pointer",borderRadius:.75*e.shape.borderRadius+"px","&.Mui-focusVisible":{boxShadow:"none !important"}})}),!k&&l.createElement(c.Chip,{icon:A?void 0:l.createElement(i.DotsVerticalIcon,{fontSize:"tiny"}),size:de,label:A?l.createElement(c.Stack,{direction:"row",gap:.5,alignItems:"center"},l.createElement(c.Typography,{variant:"inherit"},g.state),l.createElement(i.DotsVerticalIcon,{fontSize:"tiny"})):void 0,variant:"filled",shape:"rounded",color:R,...(0,c.bindTrigger)(h),"aria-label":(0,m.__)("Open CSS Class Menu","elementor"),sx:e=>({borderRadius:.75*e.shape.borderRadius+"px",paddingRight:0,...A?{}:{paddingLeft:0},".MuiChip-label":A?{paddingRight:0}:{padding:0}})})),l.createElement(ne,{...u,handleRename:z},l.createElement(ce,{popupState:h,anchorEl:w,fixed:s})))}var fe=e=>{const t=(0,o.validateStyleLabel)(e,"rename");return t.isValid?null:t.errorMessage},be="elementor-css-class-selector",ye={label:(0,m.__)("local","elementor"),value:null,fixed:!0,color:"accent",icon:l.createElement(i.MapPinIcon,null),provider:null},{Slot:ve,inject:ge}=(0,s.createLocation)();function _e(){const e=function(){const{element:e}=N();return(0,o.useProviders)().filter((e=>!!e.actions.updateProps)).flatMap((t=>{const n=(0,o.isElementsStylesProvider)(t.getKey()),r=t.actions.all({elementId:e.id});return n&&0===r.length?[ye]:r.map((e=>({label:e.label,value:e.id,fixed:n,color:G(t.getKey()),icon:n?l.createElement(i.MapPinIcon,null):null,provider:t.getKey()})))}))}(),{id:t,setId:n}=U(),s=(0,l.useRef)(null),[u,p]=(0,l.useState)(null),d=function(){const e=function(){const{id:e,setId:t}=U(),{element:n}=N(),o=(0,E.isExperimentActive)(C.V_3_30),a=oe(),i=ae(),s=(0,l.useMemo)((()=>(0,E.undoable)({do:({classId:t})=>{const n=e;return a(t),n},undo:({classId:e},n)=>{i(e),t(n)}},{title:(0,r.getElementLabel)(n.id),subtitle:({classLabel:e})=>(0,m.__)("class %s applied","elementor").replace("%s",e)})),[e,a,n.id,i,t]),c=(0,l.useCallback)((({classId:e})=>{a(e)}),[a]);return o?s:c}(),t=re();return(n,l,r)=>{if(r.value)switch(l){case"selectOption":e({classId:r.value,classLabel:r.label});break;case"removeOption":t({classId:r.value,classLabel:r.label})}}}(),{create:f,validate:b,entityName:y}=function(){const[e,t]=function(){const{id:e,setId:t}=U(),n=(0,E.isExperimentActive)(C.V_3_30),[r,a]=(0,o.useGetStylesRepositoryCreateAction)()??[null,null],i=r?.actions.delete,s=oe(),c=ae(),u=(0,l.useMemo)((()=>{if(r&&a)return(0,E.undoable)({do:({classLabel:t})=>{const n=e,l=a(t);return s(l),{prevActiveId:n,createdId:l}},undo:(e,{prevActiveId:n,createdId:l})=>{c(l),i?.(l),t(n)}},{title:(0,m.__)("Class","elementor"),subtitle:({classLabel:e})=>(0,m.__)("%s created","elementor").replace("%s",e)})}),[e,s,a,i,r,t,c]),p=(0,l.useCallback)((({classLabel:e})=>{if(!a)return;const t=a(e);s(t)}),[s,a]);return r&&u?n?[r,u]:[r,p]:[null,null]}();if(!e||!t)return{};return{create:e=>{t({classLabel:e})},validate:(t,n)=>function(e){return e.actions.all().length>=e.limit}(e)?{isValid:!1,errorMessage:(0,m.__)("You’ve reached the limit of 50 classes. Please remove an existing one to create a new class.","elementor")}:(0,o.validateStyleLabel)(t,n),entityName:e.labels.singular&&e.labels.plural?e.labels:void 0}}(),v=function(e){const{element:t}=N(),n=L(),l=(0,r.useElementSetting)(t.id,n)?.value||[],a=e.filter((e=>e.value&&l.includes(e.value)));return a.some((e=>e.provider&&(0,o.isElementsStylesProvider)(e.provider)))||a.unshift(ye),a}(e),g=v.find((e=>e.value===t))??ye,_=v.every((({fixed:e})=>e)),{userCan:h}=(0,o.useUserStylesCapability)(),w=!g.provider||h(g.provider).updateProps;return l.createElement(c.Stack,{p:2},l.createElement(c.Stack,{direction:"row",gap:1,alignItems:"center",justifyContent:"space-between"},l.createElement(c.FormLabel,{htmlFor:be,size:"small"},(0,m.__)("Classes","elementor")),l.createElement(c.Stack,{direction:"row",gap:1},l.createElement(ve,null))),l.createElement(a.WarningInfotip,{open:Boolean(u),text:u??"",placement:"bottom",width:s.current?.getBoundingClientRect().width,offset:[0,-15]},l.createElement(J,{id:be,ref:s,size:"tiny",placeholder:_?(0,m.__)("Type class name","elementor"):void 0,options:e,selected:v,entityName:y,onSelect:d,onCreate:f??void 0,validate:b??void 0,limitTags:50,renderEmptyState:he,getLimitTagsText:e=>l.createElement(c.Chip,{size:"tiny",variant:"standard",label:`+${e}`,clickable:!0}),renderTags:(e,t)=>e.map(((e,r)=>{const o=t({index:r}),a=e.value===g?.value;return l.createElement(Ee,{key:o.key,fixed:e.fixed,label:e.label,provider:e.provider,id:e.value,isActive:a,color:a&&e.color?e.color:"default",icon:e.icon,chipProps:o,onClickActive:()=>n(e.value),renameLabel:t=>{if(!e.value)throw new Error("Cannot rename a class without style id");return we(e.provider,{label:t,id:e.value})},setError:p})}))})),!w&&l.createElement(a.InfoAlert,{sx:{mt:1}},(0,m.__)("With your current role, you can use existing classes but can’t modify them.","elementor")))}var he=({searchValue:e,onClear:t})=>l.createElement(c.Box,{sx:{py:4}},l.createElement(c.Stack,{gap:1,alignItems:"center",color:"text.secondary",justifyContent:"center",sx:{px:2,m:"auto",maxWidth:"236px"}},l.createElement(i.ColorSwatchIcon,{sx:{transform:"rotate(90deg)"},fontSize:"large"}),l.createElement(c.Typography,{align:"center",variant:"subtitle2"},(0,m.__)("Sorry, nothing matched","elementor"),l.createElement("br",null),"“",e,"”."),l.createElement(c.Typography,{align:"center",variant:"caption",sx:{mb:2}},(0,m.__)("With your current role,","elementor"),l.createElement("br",null),(0,m.__)("you can only use existing classes.","elementor")),l.createElement(c.Link,{color:"text.secondary",variant:"caption",component:"button",onClick:t},(0,m.__)("Clear & try again","elementor")))),we=(e,t)=>{if(!e)return;const n=o.stylesRepository.getProviderByKey(e);return n?n.actions.update?.(t):void 0},Se="tiny",Ce="tiny",xe=(0,y.createMenu)({components:{Action:function({title:e,visible:t=!0,icon:n,onClick:r}){return t?l.createElement(c.Tooltip,{placement:"top",title:e,arrow:!0},l.createElement(c.IconButton,{"aria-label":e,size:Se,onClick:r},l.createElement(n,{fontSize:Se}))):null},PopoverAction:function({title:e,visible:t=!0,icon:n,content:r}){const o=(0,l.useId)(),a=(0,c.usePopupState)({variant:"popover",popupId:`elementor-popover-action-${o}`});return t?l.createElement(l.Fragment,null,l.createElement(c.Tooltip,{placement:"top",title:e},l.createElement(c.IconButton,{"aria-label":e,key:o,size:Ce,...(0,c.bindToggle)(a)},l.createElement(n,{fontSize:Ce}))),l.createElement(c.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},PaperProps:{sx:{my:2.5}},...(0,c.bindPopover)(a)},l.createElement(r,{close:a.close}))):null}}});function Te(){return l.createElement(c.Box,{role:"alert",sx:{minHeight:"100%",p:2}},l.createElement(c.Alert,{severity:"error",sx:{mb:2,maxWidth:400,textAlign:"center"}},l.createElement("strong",null,"Something went wrong")))}var Ie=(0,l.createContext)(void 0),ke=(0,c.styled)("div")`
	height: 100%;
	overflow-y: auto;
`;function ze({children:e}){const[t,n]=(0,l.useState)("up"),r=(0,l.useRef)(null),o=(0,l.useRef)(0);return(0,l.useEffect)((()=>{const e=r.current;if(!e)return;const t=()=>{const{scrollTop:t}=e;t>o.current?n("down"):t<o.current&&n("up"),o.current=t};return e.addEventListener("scroll",t),()=>{e.removeEventListener("scroll",t)}})),l.createElement(Ie.Provider,{value:{direction:t}},l.createElement(ke,{ref:r},e))}var Pe={defaultSectionsExpanded:{settings:["Content","Settings"],style:[]},defaultTab:"settings"},De=(0,l.createContext)({"e-div-block":{defaultSectionsExpanded:Pe.defaultSectionsExpanded,defaultTab:"style"},"e-flexbox":{defaultSectionsExpanded:Pe.defaultSectionsExpanded,defaultTab:"style"},"e-divider":{defaultSectionsExpanded:Pe.defaultSectionsExpanded,defaultTab:"style"}}),Re=()=>{const{element:e}=N();return(0,l.useContext)(De)[e.type]||Pe},Le=(e,t)=>{const{element:n}=N(),r=(0,E.isExperimentActive)(C.V_3_30),o=`elementor/editor-state/${n.id}/${e}`,a=r?(0,b.getSessionStorageItem)(o):t,[i,s]=(0,l.useState)(a??t);return[i,e=>{(0,b.setSessionStorageItem)(o,e),s(e)}]},Ve={image:{component:n.ImageControl,layout:"full",propTypeUtil:d.imagePropTypeUtil},"svg-media":{component:n.SvgMediaControl,layout:"full",propTypeUtil:d.imageSrcPropTypeUtil},text:{component:n.TextControl,layout:"full",propTypeUtil:d.stringPropTypeUtil},textarea:{component:n.TextAreaControl,layout:"full",propTypeUtil:d.stringPropTypeUtil},size:{component:n.SizeControl,layout:"two-columns",propTypeUtil:d.sizePropTypeUtil},select:{component:n.SelectControl,layout:"two-columns",propTypeUtil:d.stringPropTypeUtil},link:{component:n.LinkControl,layout:"custom",propTypeUtil:d.linkPropTypeUtil},url:{component:n.UrlControl,layout:"full",propTypeUtil:d.stringPropTypeUtil},switch:{component:n.SwitchControl,layout:"two-columns",propTypeUtil:d.booleanPropTypeUtil},repeatable:{component:n.RepeatableControl,layout:"full",propTypeUtil:void 0},"key-value":{component:n.KeyValueControl,layout:"full",propTypeUtil:d.keyValuePropTypeUtil}},Ae=e=>Ve[e]?.component,Ne=({props:e,type:t})=>{const n=Ae(t),{element:r}=N();if(!n)throw new B({context:{controlType:t}});return l.createElement(n,{...e,context:{elementId:r.id}})},Be=({children:e,layout:t})=>"custom"===t?e:l.createElement(Oe,{layout:t},e),Oe=(0,c.styled)(c.Box,{shouldForwardProp:e=>!["layout"].includes(e)})((({layout:e,theme:t})=>({display:"grid",gridGap:t.spacing(1),...Me(e)}))),Me=e=>({justifyContent:"space-between",gridTemplateColumns:{full:"minmax(0, 1fr)","two-columns":"repeat(2, minmax(0, 1fr))"}[e]}),Fe=({schema:e})=>({key:"",kind:"object",meta:{},settings:{},default:null,shape:e}),$e=({bind:e,children:t,propDisplayName:o})=>{const{element:a,elementType:i}=N(),s=(0,r.useElementSettings)(a.id,Object.keys(i.propsSchema)),c={[e]:s?.[e]},u=Fe({schema:i.propsSchema}),f=function({propKey:e,elementId:t,propDisplayName:n}){return(0,l.useMemo)((()=>(0,E.undoable)({do:({newValue:n})=>{const l=(0,r.getElementSetting)(t,e);return(0,r.updateElementSettings)({id:t,props:{...n},withHistory:!1}),(0,p.setDocumentModifiedStatus)(!0),{[e]:l}},undo:({},e)=>{(0,r.updateElementSettings)({id:t,props:e,withHistory:!1})}},{title:(0,r.getElementLabel)(t),subtitle:(0,m.__)("%s edited","elementor").replace("%s",n)})),[e,t,n])}({propKey:e,elementId:a.id,propDisplayName:o});return l.createElement(n.PropProvider,{propType:u,value:c,setValue:e=>{(0,E.isExperimentActive)(C.V_3_31)?f({newValue:e}):(0,r.updateElementSettings)({id:a.id,props:e})},isDisabled:e=>function(e,t){const n=e.dependencies?.filter((({effect:e})=>"disable"===e))||[];if(!n.length)return!1;if(n.length>1)throw new Error("Multiple disabling dependencies are not supported.");return(0,d.shouldApplyEffect)(n[0],t)}(e,s)},l.createElement(n.PropKeyProvider,{bind:e},t))},je=(0,l.createContext)(null),Ue=()=>{const e=(0,l.useContext)(je);return e?.current?.offsetWidth??320},We=(0,c.styled)(i.ChevronDownIcon,{shouldForwardProp:e=>"open"!==e})((({theme:e,open:t})=>({transform:t?"rotate(180deg)":"rotate(0deg)",transition:e.transitions.create("transform",{duration:e.transitions.duration.standard})}))),Ge=(0,c.styled)("div")`
	position: absolute;
	top: 0;
	right: ${({theme:e})=>e.spacing(3)};
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
`,Ke=({children:e,defaultOpen:t=!1,titleEnd:n=null})=>{const[r,o]=(0,l.useState)(t);return l.createElement(c.Stack,null,l.createElement(c.Stack,{sx:{position:"relative"}},l.createElement(c.Button,{fullWidth:!0,size:"small",color:"secondary",variant:"outlined",onClick:()=>{o((e=>!e))},endIcon:l.createElement(We,{open:r}),sx:{my:.5}},r?(0,m.__)("Show less","elementor"):(0,m.__)("Show more","elementor")),n&&l.createElement(Ge,null,He(n,r))),l.createElement(c.Collapse,{in:r,timeout:"auto",unmountOnExit:!0},e))};function He(e,t){return"function"==typeof e?e(t):e}function Je({title:e,children:t,defaultExpanded:n=!1,titleEnd:r}){const[o,a]=Le(e,!!n),i=(0,l.useRef)(null),s=(0,l.useId)(),m=`label-${s}`,u=`content-${s}`,p=(0,E.isExperimentActive)(C.V_3_30);return l.createElement(l.Fragment,null,l.createElement(c.ListItemButton,{id:m,"aria-controls":u,onClick:()=>{a(!o)},sx:{"&:hover":{backgroundColor:"transparent"}}},l.createElement(c.Stack,{direction:"row",alignItems:"center",justifyItems:"start",flexGrow:1,gap:.5},l.createElement(c.ListItemText,{secondary:e,secondaryTypographyProps:{color:"text.primary",variant:"caption",fontWeight:"bold"},sx:{flexGrow:0,flexShrink:1,marginInlineEnd:1}}),p?He(r,o):null),l.createElement(We,{open:o,color:"secondary",fontSize:"tiny"})),l.createElement(c.Collapse,{id:u,"aria-labelledby":m,in:o,timeout:"auto",unmountOnExit:!0},l.createElement(je.Provider,{value:i},l.createElement(c.Stack,{ref:i,gap:2.5,p:2},t))),l.createElement(c.Divider,null))}function Ye(e){return l.createElement(c.List,{disablePadding:!0,component:"div",...e})}var qe=()=>{const{elementType:e,element:t}=N(),n=Re();return l.createElement(b.SessionStorageProvider,{prefix:t.id},l.createElement(Ye,null,e.controls.map((({type:e,value:t},r)=>{return"control"===e?l.createElement(Xe,{key:t.bind,control:t}):"section"===e?l.createElement(Je,{title:t.label,key:e+"."+r,defaultExpanded:(o=t.label,!(0,E.isExperimentActive)(C.V_3_30)||n.defaultSectionsExpanded.settings?.includes(o))},t.items?.map((e=>"control"===e.type?l.createElement(Xe,{key:e.value.bind,control:e.value}):null))):null;var o}))))},Xe=({control:e})=>{if(!Ae(e.type))return null;const t=e.meta?.layout||(r=e.type,Ve[r].layout);var r;const o=function(e){if(e.childControlType){const t=Ae(e.childControlType),n=(e=>Ve[e]?.propTypeUtil)(e.childControlType);e={...e,childControlConfig:{component:t,props:e.childControlProps||{},propTypeUtil:n}}}return e}(e.props);return"custom"===t&&(o.label=e.label),l.createElement($e,{bind:e.bind,propDisplayName:e.label||e.bind},e.meta?.topDivider&&l.createElement(c.Divider,null),l.createElement(Be,{layout:t},e.label&&"custom"!==t?l.createElement(n.ControlFormLabel,null,e.label):null,l.createElement(Ne,{type:e.type,props:o})))},Ze=()=>{const{provider:e}=U(),[,t]=(0,l.useReducer)((e=>!e),!1);(0,l.useEffect)((()=>e?.subscribe(t)),[e])},Qe="normal",et=e=>e??Qe,tt=e=>e??"desktop";function nt(e,t){const n=function(e){const t={},n=(e,l)=>{const{id:r,children:o}=e;t[r]=l?[...l]:[],o?.forEach((e=>{n(e,[...t[r]??[],r])}))};return n(e),t}(t),l={};return t=>{const{breakpoint:r,state:o}=t,a=et(o),i=tt(r);if(l[i]?.[a])return l[i][a].snapshot;const s=[...n[i],r];return s.forEach(((t,n)=>{const r=n>0?s[n-1]:null;((t,n,r)=>{const o=tt(t),a=et(r);l[o]||(l[o]={[Qe]:lt(e({breakpoint:t,state:null}),n,{},null)}),r&&!l[o][a]&&(l[o][a]=lt(e({breakpoint:t,state:r}),n,l[o],r))})(t,r?l[r]:void 0,o)})),l[i]?.[a]?.snapshot}}function lt(e,t,n,l){const r=function(e){const t={};return e.forEach((e=>{const{variant:{props:n}}=e;Object.entries(n).forEach((([n,l])=>{const r=(0,d.filterEmptyValues)(l);if(null===r)return;t[n]||(t[n]=[]);const o={...e,value:r};t[n].push(o)}))})),{snapshot:t,stateSpecificSnapshot:t}}(e);return l?{snapshot:rt([r.snapshot,t?.[l]?.stateSpecificSnapshot,n[Qe]?.snapshot]),stateSpecificSnapshot:rt([r.stateSpecificSnapshot,t?.[l]?.stateSpecificSnapshot])}:{snapshot:rt([r.snapshot,t?.[Qe]?.snapshot]),stateSpecificSnapshot:void 0}}function rt(e){const t={};return e.filter(Boolean).forEach((e=>Object.entries(e).forEach((([e,n])=>{t[e]||(t[e]=[]),t[e]=t[e].concat(n)})))),t}function ot(e,t,n){return e&&"object"==typeof e?function(e,t){return!!e&&(0,d.isTransformable)(t)&&e.key!==t.$$type}(n,e)?e:t.reduce(((e,t)=>e?(0,d.isTransformable)(e)?e.value?.[t]??null:"object"==typeof e?e[t]??null:null:null),e):null}var at=(e,t)=>e&&"union"===e.kind?Object.values(e.prop_types).find((e=>!!t.reduce(((e,t)=>{if("object"!==e?.kind)return null;const{shape:n}=e;return n[t]?n[t]:null}),e)))??null:null,it=(0,l.createContext)(null);function st({children:e}){const t=ut(),n=(0,v.getBreakpointsTree)(),{getSnapshot:r,getInheritanceChain:o}=function(e,t){const n=function(e){const t={};return e.forEach((e=>{const n=W(e.id)?.getKey()??null;e.variants.forEach((l=>{const{meta:r}=l,{state:o,breakpoint:a}=r,i=tt(a),s=et(o);t[i]||(t[i]={});const c=t[i];c[s]||(c[s]=[]),c[s].push({style:e,variant:l,provider:n})}))})),t}(e);return{getSnapshot:nt((({breakpoint:e,state:t})=>n?.[tt(e)]?.[et(t)]??[]),t),getInheritanceChain:(e,t,n)=>{const[l,...r]=t;let o=e[l]??[];if(r.length>0){const e=at(n,r);o=o.map((({value:t,...n})=>({...n,value:ot(t,r,e)}))).filter((({value:e})=>!(0,d.isEmpty)(e)))}return o}}}(t,n);return l.createElement(it.Provider,{value:{getSnapshot:r,getInheritanceChain:o}},e)}function ct(){const e=(0,l.useContext)(it),{meta:t}=U();if(!e)throw new Error("useStylesInheritanceSnapshot must be used within a StyleInheritanceProvider");return t?e.getSnapshot(t)??null:null}function mt(e){const t=(0,l.useContext)(it);if(!t)throw new Error("useStylesInheritanceChain must be used within a StyleInheritanceProvider");const n=(0,g.getStylesSchema)(),r=n?.[e[0]],o=ct();return o?t.getInheritanceChain(o,e,r):[]}var ut=()=>{const{element:e}=N(),t=L(),n=pt();Ze();const l=(0,r.useElementSetting)(e.id,t),a=d.classesPropTypeUtil.extract(l)??[];return o.stylesRepository.all().filter((e=>[...n,...a].includes(e.id)))},pt=()=>{const{elementType:e}=N(),t=(0,r.getWidgetsCache)(),n=t?.[e.key];return Object.keys(n?.base_styles??{})};function dt(e){const{element:{id:t}}=N(),{id:n,meta:a,provider:i,canEdit:s}=U(),c=function({elementId:e,meta:t}){return(0,l.useMemo)((()=>{const n=(0,E.isExperimentActive)(C.V_3_31);return(0,E.undoable)({do:({provider:n,styleId:l,props:r})=>{if(!n.actions.updateProps)throw new M({context:{providerKey:n.getKey()}});const o=function(e,t){if(!e)return{};const n=(0,g.getVariantByMeta)(e,t);return structuredClone(n?.props??{})}(n.actions.get(l,{elementId:e}),t);return n.actions.updateProps({id:l,meta:t,props:r},{elementId:e}),o},undo:({provider:n,styleId:l},r)=>{n.actions.updateProps?.({id:l,meta:t,props:r},{elementId:e})}},{title:({provider:t})=>n?(0,o.isElementsStylesProvider)(t.getKey())?bt.title({elementId:e}):ft.title({provider:t}):Et.title({elementId:e}),subtitle:({provider:t,styleId:l,propDisplayName:r})=>n?(0,o.isElementsStylesProvider)(t.getKey())?bt.subtitle({propDisplayName:r}):ft.subtitle({provider:t,styleId:l,elementId:e,propDisplayName:r}):Et.subtitle})}),[e,t])}({elementId:t,meta:a}),m=function({elementId:e,meta:t}){const n=L();return(0,l.useMemo)((()=>{const l=(0,E.isExperimentActive)(C.V_3_31),a={elementId:e,classesProp:n,meta:t,label:o.ELEMENTS_STYLES_RESERVED_LABEL};return(0,E.undoable)({do:({props:e})=>(0,r.createElementStyle)({...a,props:e}),undo:(t,n)=>{(0,r.deleteElementStyle)(e,n)},redo:({props:e},t)=>(0,r.createElementStyle)({...a,props:e,styleId:t})},{title:()=>l?bt.title({elementId:e}):Et.title({elementId:e}),subtitle:({propDisplayName:e})=>l?bt.subtitle({propDisplayName:e}):Et.subtitle})}),[n,e,t])}({elementId:t,meta:a});Ze();const u=function({styleId:e,elementId:t,provider:n,meta:l,propNames:r}){if(!n||!e)return null;const o=n.actions.get(e,{elementId:t});if(!o)throw new F({context:{styleId:e,providerKey:n.getKey()}});const a=(0,g.getVariantByMeta)(o,l);return Object.fromEntries(r.map((e=>[e,a?.props[e]??null])))}({elementId:t,styleId:n,provider:i,meta:a,propNames:e});return{values:u,setValues:(e,{history:{propDisplayName:t}})=>{null===n?m({props:e,propDisplayName:t}):c({provider:i,styleId:n,props:e,propDisplayName:t})},canEdit:s}}var Et={title:({elementId:e})=>(0,r.getElementLabel)(e),subtitle:(0,m.__)("Style edited","elementor")},ft={title:({provider:e})=>{const t=e.labels?.singular;return t?(n=t).charAt(0).toUpperCase()+n.slice(1):(0,m.__)("Style","elementor");var n},subtitle:({provider:e,styleId:t,elementId:n,propDisplayName:l})=>{const r=e.actions.get(t,{elementId:n})?.label;if(!r)throw new Error(`Style ${t} not found`);return(0,m.__)("%s$1 %s$2 edited","elementor").replace("%s$1",r).replace("%s$2",l)}},bt={title:({elementId:e})=>(0,r.getElementLabel)(e),subtitle:({propDisplayName:e})=>(0,m.__)("%s edited","elementor").replace("%s",e)};function yt(e,t){const{values:n,setValues:l,canEdit:r}=dt([e]);return{value:n?.[e]??null,setValue:n=>{l({[e]:n},t)},canEdit:r}}var vt=new Set(["background-color-overlay","background-image-overlay","background-gradient-overlay","gradient-color-stop","color-stop","background-image-position-offset","background-image-size-scale","image-src","image","background-overlay"]),gt=()=>(0,E.isExperimentActive)("e_v_3_30");function _t(){const e="rtl"===(0,c.useTheme)().direction;return{isSiteRtl:!!(()=>{const e=window;return e.elementorFrontend?.config??{}})()?.is_rtl,isUiRtl:e}}var ht=async(e,t,n)=>{try{const r=await n({props:{[t]:e.value}}),o=r?.[t]??r;return(0,l.isValidElement)(o)?o:"object"==typeof o?JSON.stringify(o):String(o)}catch{return""}},wt=(0,_.createTransformersRegistry)(),St={widescreen:i.WidescreenIcon,desktop:i.DesktopIcon,laptop:i.LaptopIcon,tablet_extra:i.TabletLandscapeIcon,tablet:i.TabletPortraitIcon,mobile_extra:i.MobileLandscapeIcon,mobile:i.MobilePortraitIcon},Ct=({breakpoint:e})=>{const t=(0,v.useBreakpoints)(),n=e||"desktop",r=St[n];if(!r)return null;const o=t.find((e=>e.id===n))?.label;return l.createElement(c.Tooltip,{title:o,placement:"top"},l.createElement(r,{fontSize:"tiny",sx:{mt:"2px"}}))},xt="tiny",Tt=({displayLabel:e,provider:t})=>{const n=t===o.ELEMENTS_BASE_STYLES_PROVIDER_KEY?l.createElement(c.Tooltip,{title:(0,m.__)("Inherited from base styles","elementor"),placement:"top"},l.createElement(i.InfoCircleIcon,{fontSize:xt})):void 0;return l.createElement(c.Chip,{label:e,size:xt,color:G(t),variant:"standard",state:"enabled",icon:n,sx:e=>({lineHeight:1,flexWrap:"nowrap",alignItems:"center",borderRadius:.75*e.shape.borderRadius+"px",flexDirection:"row-reverse",".MuiChip-label":{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}})})},It=({index:e,value:t})=>l.createElement(c.Typography,{variant:"caption",color:"text.tertiary",sx:{mt:"1px",textDecoration:0===e?"none":"line-through",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},t),kt=()=>l.createElement(c.Box,{display:"flex",gap:.5,alignItems:"center"}),zt=({inheritanceChain:e,propType:t,path:n,label:r,children:i})=>{const[s,u]=(0,l.useState)(!1),p=()=>u(!1),d=n.join("."),E=Ue()+32,f=(0,l.useMemo)((()=>(0,_.createPropsResolver)({transformers:wt,schema:{[d]:t}})),[d,t]),b=((e,t,n)=>{const[r,a]=(0,l.useState)([]);return(0,l.useEffect)((()=>{(async()=>{const l=(await Promise.all(e.filter((({style:e})=>e)).map(((e,l)=>(async(e,t,n,l)=>{const{variant:{meta:{state:r,breakpoint:o}},style:{label:a,id:i}}=e,s=`${a}${r?":"+r:""}`;return{id:i?i+(r??""):t,provider:e.provider||"",breakpoint:o??"desktop",displayLabel:s,value:await ht(e,n,l)}})(e,l,t,n))))).map((e=>({...e,displayLabel:o.ELEMENTS_BASE_STYLES_PROVIDER_KEY!==e.provider?e.displayLabel:(0,m.__)("Base","elementor")}))).filter((e=>!e.value||""!==e.displayLabel)).slice(0,2);a(l)})()}),[e,t,n]),r})(e,d,f),y=l.createElement(c.ClickAwayListener,{onClickAway:p},l.createElement(c.Card,{elevation:0,sx:{width:E-32+"px",maxWidth:496,overflowX:"hidden"}},l.createElement(c.CardContent,{sx:{display:"flex",gap:.5,flexDirection:"column",p:0,"&:last-child":{pb:0}}},l.createElement(a.PopoverHeader,{title:(0,m.__)("Style origin","elementor"),onClose:p}),l.createElement(c.Stack,{gap:1.5,sx:{pl:2,pr:1,pb:2,overflowX:"hidden",overflowY:"auto"},role:"list"},b.map(((e,t)=>l.createElement(c.Box,{key:e.id,display:"flex",gap:.5,role:"listitem","aria-label":(0,m.__)("Inheritance item: %s","elementor").replace("%s",e.displayLabel)},l.createElement(c.Box,{display:"flex",gap:.5,sx:{flexWrap:"wrap",width:"100%"}},l.createElement(Ct,{breakpoint:e.breakpoint}),l.createElement(Tt,{displayLabel:e.displayLabel,provider:e.provider}),l.createElement(It,{index:t,value:e.value})),l.createElement(kt,null))))))));return l.createElement(Pt,{showInfotip:s,onClose:p,infotipContent:y},l.createElement(c.IconButton,{onClick:()=>u((e=>!e)),"aria-label":r,sx:{my:"-1px"}},i))};function Pt({children:e,showInfotip:t,onClose:n,infotipContent:r}){const{isSiteRtl:o}=_t(),a=o?9999999:-9999999;return t?l.createElement(l.Fragment,null,l.createElement(c.Backdrop,{open:t,onClick:n,sx:{backgroundColor:"transparent",zIndex:e=>e.zIndex.modal-1}}),l.createElement(c.Infotip,{placement:"top",content:r,open:t,onClose:n,disableHoverListener:!0,componentsProps:{tooltip:{sx:{mx:2}}},slotProps:{popper:{modifiers:[{name:"offset",options:{offset:[a,0]}}]}}},e)):l.createElement(c.Tooltip,{title:(0,m.__)("Style origin","elementor"),placement:"top"},e)}var Dt=()=>{const{path:e,propType:t}=(0,n.useBoundProp)(),r=(0,E.isExperimentActive)(C.V_3_30)?e:e.slice(0,1),o=mt(r);return o.length?l.createElement(Rt,{inheritanceChain:o,path:r,propType:t}):null},Rt=({inheritanceChain:e,path:t,propType:n})=>{const{id:r,provider:a,meta:i}=U(),s=r?((e,t,n)=>e.find((({style:e,variant:{meta:{breakpoint:l,state:r}}})=>e.id===t&&l===n.breakpoint&&r===n.state)))(e,r,i):null,u=!(0,d.isEmpty)(s?.value),[p]=e;if(!(0,E.isExperimentActive)(C.V_3_31)&&p.provider===o.ELEMENTS_BASE_STYLES_PROVIDER_KEY)return null;const f=s===p,b=Lt({isFinalValue:f,hasValue:u}),y={getColor:f&&a?K(a.getKey()):void 0,isOverridden:!(!u||f)||void 0};return gt()?l.createElement(zt,{inheritanceChain:e,path:t,propType:n,label:b},l.createElement(le,{...y})):l.createElement(c.Tooltip,{title:(0,m.__)("Style origin","elementor"),placement:"top"},l.createElement(le,{...y,"aria-label":b}))},Lt=({isFinalValue:e,hasValue:t})=>e?(0,m.__)("This is the final value","elementor"):t?(0,m.__)("This value is overridden by another style","elementor"):(0,m.__)("This has value from another style","elementor"),Vt=({bind:e,placeholder:t,propDisplayName:r,children:o})=>{const{value:a,setValue:i,canEdit:s}=yt(e,{history:{propDisplayName:r}}),c=(0,E.isExperimentActive)("e_v_3_31"),m=mt([e]),u=(0,g.getStylesSchema)(),p=Fe({schema:u}),d={[e]:a},[f]=m,b={[e]:c?f?.value:t};return l.createElement(n.ControlAdornmentsProvider,{items:[{id:"styles-inheritance",Adornment:Dt}]},l.createElement(n.PropProvider,{propType:p,value:d,setValue:t=>{i(t[e])},placeholder:b,isDisabled:()=>!s},l.createElement(n.PropKeyProvider,{bind:e},o)))},At=({gap:e=2,sx:t,children:n})=>l.createElement(c.Stack,{gap:e,sx:{...t}},n),Nt=(0,m.__)("Background","elementor"),Bt=()=>l.createElement(At,null,l.createElement(Vt,{bind:"background",propDisplayName:Nt},l.createElement(n.BackgroundControl,null))),Ot=()=>l.createElement(c.Divider,{sx:{my:.5}}),Mt="tiny",Ft=({isAdded:e,onAdd:t,onRemove:n,children:r,disabled:o,renderLabel:a})=>l.createElement(At,null,l.createElement(c.Stack,{direction:"row",sx:{justifyContent:"space-between",alignItems:"center",marginInlineEnd:-.75}},a(),e?l.createElement(c.IconButton,{size:Mt,onClick:n,"aria-label":"Remove",disabled:o},l.createElement(i.MinusIcon,{fontSize:Mt})):l.createElement(c.IconButton,{size:Mt,onClick:t,"aria-label":"Add",disabled:o},l.createElement(i.PlusIcon,{fontSize:Mt}))),l.createElement(c.Collapse,{in:e,unmountOnExit:!0},l.createElement(At,null,r))),$t=({children:e})=>l.createElement(c.Stack,{direction:"row",alignItems:"center",justifyItems:"start",gap:.25},l.createElement(n.ControlFormLabel,null,e),l.createElement(n.ControlAdornments,null)),jt=l.forwardRef(((e,t)=>{const{direction:n="row",children:r,label:o}=e,a="row"===n?Ut:Wt;return l.createElement(a,{label:o,ref:t,children:r})})),Ut=l.forwardRef((({label:e,children:t},n)=>l.createElement(c.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap",ref:n},l.createElement(c.Grid,{item:!0,xs:6},l.createElement($t,null,e)),l.createElement(c.Grid,{item:!0,xs:6,sx:e=>({width:`calc(50% - ${e.spacing(2)})`})},t)))),Wt=l.forwardRef((({label:e,children:t},n)=>l.createElement(c.Stack,{gap:.75,ref:n},l.createElement($t,null,e),t))),Gt=(0,m.__)("Border color","elementor"),Kt=()=>l.createElement(Vt,{bind:"border-color",propDisplayName:Gt},l.createElement(jt,{label:Gt},l.createElement(n.ColorControl,null))),Ht=(0,m.__)("Border type","elementor"),Jt=[{value:"none",label:(0,m.__)("None","elementor")},{value:"solid",label:(0,m.__)("Solid","elementor")},{value:"dashed",label:(0,m.__)("Dashed","elementor")},{value:"dotted",label:(0,m.__)("Dotted","elementor")},{value:"double",label:(0,m.__)("Double","elementor")},{value:"groove",label:(0,m.__)("Groove","elementor")},{value:"ridge",label:(0,m.__)("Ridge","elementor")},{value:"inset",label:(0,m.__)("Inset","elementor")},{value:"outset",label:(0,m.__)("Outset","elementor")}],Yt=()=>l.createElement(Vt,{bind:"border-style",propDisplayName:Ht},l.createElement(jt,{label:Ht},l.createElement(n.SelectControl,{options:Jt}))),qt=(0,m.__)("Border width","elementor"),Xt=(0,c.withDirection)(i.SideRightIcon),Zt=(0,c.withDirection)(i.SideLeftIcon),Qt=e=>[{label:(0,m.__)("Top","elementor"),icon:l.createElement(i.SideTopIcon,{fontSize:"tiny"}),bind:"block-start"},{label:e?(0,m.__)("Left","elementor"):(0,m.__)("Right","elementor"),icon:l.createElement(Xt,{fontSize:"tiny"}),bind:"inline-end"},{label:(0,m.__)("Bottom","elementor"),icon:l.createElement(i.SideBottomIcon,{fontSize:"tiny"}),bind:"block-end"},{label:e?(0,m.__)("Right","elementor"):(0,m.__)("Left","elementor"),icon:l.createElement(Zt,{fontSize:"tiny"}),bind:"inline-start"}],en=()=>{const{isSiteRtl:e}=_t();return l.createElement(Vt,{bind:"border-width",propDisplayName:qt},l.createElement(n.EqualUnequalSizesControl,{items:Qt(e),label:qt,icon:l.createElement(i.SideAllIcon,{fontSize:"tiny"}),tooltipLabel:(0,m.__)("Adjust borders","elementor"),multiSizePropTypeUtil:d.borderWidthPropTypeUtil}))},tn=(0,m.__)("Border","elementor"),nn={"border-width":{$$type:"size",value:{size:1,unit:"px"}},"border-color":{$$type:"color",value:"#000000"},"border-style":{$$type:"string",value:"solid"}},ln=()=>{const{values:e,setValues:t,canEdit:r}=dt(Object.keys(nn)),o={history:{propDisplayName:tn}},a=Object.values(e??{}).some(Boolean);return l.createElement(Ft,{isAdded:a,onAdd:()=>{t(nn,o)},onRemove:()=>{t({"border-width":null,"border-color":null,"border-style":null},o)},disabled:!r,renderLabel:()=>l.createElement(n.ControlFormLabel,null,tn)},l.createElement(en,null),l.createElement(Kt,null),l.createElement(Yt,null))},rn=({children:e})=>{const{isSiteRtl:t}=_t();return l.createElement(c.DirectionProvider,{rtl:t},l.createElement(c.ThemeProvider,null,e))},on=(0,m.__)("Border radius","elementor"),an=(0,c.withDirection)(i.RadiusTopLeftIcon),sn=(0,c.withDirection)(i.RadiusTopRightIcon),cn=(0,c.withDirection)(i.RadiusBottomLeftIcon),mn=(0,c.withDirection)(i.RadiusBottomRightIcon),un=e=>e?(0,m.__)("Top right","elementor"):(0,m.__)("Top left","elementor"),pn=e=>e?(0,m.__)("Top left","elementor"):(0,m.__)("Top right","elementor"),dn=e=>e?(0,m.__)("Bottom right","elementor"):(0,m.__)("Bottom left","elementor"),En=e=>e?(0,m.__)("Bottom left","elementor"):(0,m.__)("Bottom right","elementor"),fn=e=>[{label:un(e),icon:l.createElement(an,{fontSize:"tiny"}),bind:"start-start"},{label:pn(e),icon:l.createElement(sn,{fontSize:"tiny"}),bind:"start-end"},{label:dn(e),icon:l.createElement(cn,{fontSize:"tiny"}),bind:"end-start"},{label:En(e),icon:l.createElement(mn,{fontSize:"tiny"}),bind:"end-end"}],bn=()=>{const{isSiteRtl:e}=_t();return l.createElement(rn,null,l.createElement(Vt,{bind:"border-radius",propDisplayName:on},l.createElement(n.EqualUnequalSizesControl,{items:fn(e),label:on,icon:l.createElement(i.BorderCornersIcon,{fontSize:"tiny"}),tooltipLabel:(0,m.__)("Adjust corners","elementor"),multiSizePropTypeUtil:d.borderRadiusPropTypeUtil})))},yn=()=>l.createElement(At,null,l.createElement(bn,null),l.createElement(Ot,null),l.createElement(ln,null)),vn=(0,m.__)("Opacity","elementor"),gn=()=>{const e=(0,l.useRef)(null);return l.createElement(Vt,{bind:"opacity",propDisplayName:vn},l.createElement(jt,{ref:e,label:vn},l.createElement(n.SizeControl,{units:["%"],anchorRef:e,defaultUnit:"%"})))},_n=(0,m.__)("Box shadow","elementor"),hn=(0,m.__)("Filter","elementor"),wn=(0,m.__)("Transform","elementor"),Sn=()=>{const e=(0,E.isExperimentActive)(C.V_3_31);return l.createElement(At,null,e&&l.createElement(l.Fragment,null,l.createElement(gn,null),l.createElement(Ot,null)),l.createElement(Vt,{bind:"box-shadow",propDisplayName:_n},l.createElement(n.BoxShadowRepeaterControl,null)),e&&l.createElement(l.Fragment,null,l.createElement(Ot,null),l.createElement(Vt,{bind:"transform",propDisplayName:wn},l.createElement(n.TransformRepeaterControl,null)),l.createElement(Ot,null),l.createElement(Vt,{bind:"filter",propDisplayName:hn},l.createElement(n.FilterRepeaterControl,null))))},Cn=(0,m.__)("Flex direction","elementor"),xn={row:0,column:90,"row-reverse":180,"column-reverse":270},Tn={row:0,column:-90,"row-reverse":-180,"column-reverse":-270},In=({icon:e,size:t,isClockwise:n=!0,offset:r=0,disableRotationForReversed:o=!1})=>{const a=(0,l.useRef)(kn(n,r,o));return a.current=kn(n,r,o,a),l.createElement(e,{fontSize:t,sx:{transition:".3s",rotate:`${a.current}deg`}})},kn=(e,t,n,l)=>{const{value:r}=yt("flex-direction",{history:{propDisplayName:Cn}}),o="rtl"===(0,c.useTheme)().direction?-1:1,a=e?xn:Tn,i=r?.value||"row",s=l?l.current*o:a[i]+t,m=((a[i]+t-s+360)%360+180)%360-180;return n&&["row-reverse","column-reverse"].includes(i)?0:(s+m)*o},zn=(0,m.__)("Align content","elementor"),Pn=(0,c.withDirection)(i.JustifyTopIcon),Dn=(0,c.withDirection)(i.JustifyBottomIcon),Rn={isClockwise:!1,offset:0,disableRotationForReversed:!0},Ln=[{value:"start",label:(0,m.__)("Start","elementor"),renderContent:({size:e})=>l.createElement(In,{icon:Pn,size:e,...Rn}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:e})=>l.createElement(In,{icon:i.JustifyCenterIcon,size:e,...Rn}),showTooltip:!0},{value:"end",label:(0,m.__)("End","elementor"),renderContent:({size:e})=>l.createElement(In,{icon:Dn,size:e,...Rn}),showTooltip:!0},{value:"space-between",label:(0,m.__)("Space between","elementor"),renderContent:({size:e})=>l.createElement(In,{icon:i.JustifySpaceBetweenVerticalIcon,size:e,...Rn}),showTooltip:!0},{value:"space-around",label:(0,m.__)("Space around","elementor"),renderContent:({size:e})=>l.createElement(In,{icon:i.JustifySpaceAroundVerticalIcon,size:e,...Rn}),showTooltip:!0},{value:"space-evenly",label:(0,m.__)("Space evenly","elementor"),renderContent:({size:e})=>l.createElement(In,{icon:i.JustifyDistributeVerticalIcon,size:e,...Rn}),showTooltip:!0}],Vn=()=>l.createElement(Vt,{bind:"align-content",propDisplayName:zn},l.createElement(rn,null,l.createElement(jt,{label:zn,direction:"column"},l.createElement(n.ToggleControl,{options:Ln,fullWidth:!0})))),An=(0,m.__)("Align items","elementor"),Nn=(0,c.withDirection)(i.LayoutAlignLeftIcon),Bn=(0,c.withDirection)(i.LayoutAlignRightIcon),On={isClockwise:!1,offset:90},Mn=[{value:"start",label:(0,m.__)("Start","elementor"),renderContent:({size:e})=>l.createElement(In,{icon:Nn,size:e,...On}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:e})=>l.createElement(In,{icon:i.LayoutAlignCenterIcon,size:e,...On}),showTooltip:!0},{value:"end",label:(0,m.__)("End","elementor"),renderContent:({size:e})=>l.createElement(In,{icon:Bn,size:e,...On}),showTooltip:!0},{value:"stretch",label:(0,m.__)("Stretch","elementor"),renderContent:({size:e})=>l.createElement(In,{icon:i.LayoutDistributeVerticalIcon,size:e,...On}),showTooltip:!0}],Fn=()=>l.createElement(rn,null,l.createElement(Vt,{bind:"align-items",propDisplayName:An},l.createElement(jt,{label:An},l.createElement(n.ToggleControl,{options:Mn})))),$n=(0,m.__)("Align self","elementor"),jn={row:90,"row-reverse":90,column:0,"column-reverse":0},Un=(0,c.withDirection)(i.LayoutAlignLeftIcon),Wn=(0,c.withDirection)(i.LayoutAlignRightIcon),Gn={isClockwise:!1},Kn=e=>[{value:"start",label:(0,m.__)("Start","elementor"),renderContent:({size:t})=>l.createElement(In,{icon:Un,size:t,offset:jn[e],...Gn}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:t})=>l.createElement(In,{icon:i.LayoutAlignCenterIcon,size:t,offset:jn[e],...Gn}),showTooltip:!0},{value:"end",label:(0,m.__)("End","elementor"),renderContent:({size:t})=>l.createElement(In,{icon:Wn,size:t,offset:jn[e],...Gn}),showTooltip:!0},{value:"stretch",label:(0,m.__)("Stretch","elementor"),renderContent:({size:t})=>l.createElement(In,{icon:i.LayoutDistributeVerticalIcon,size:t,offset:jn[e],...Gn}),showTooltip:!0}],Hn=({parentStyleDirection:e})=>l.createElement(Vt,{bind:"align-self",propDisplayName:$n},l.createElement(rn,null,l.createElement(jt,{label:$n},l.createElement(n.ToggleControl,{options:Kn(e)})))),Jn=(0,m.__)("Display","elementor"),Yn=[{value:"block",renderContent:()=>(0,m.__)("Block","elementor"),label:(0,m.__)("Block","elementor"),showTooltip:!0},{value:"flex",renderContent:()=>(0,m.__)("Flex","elementor"),label:(0,m.__)("Flex","elementor"),showTooltip:!0},{value:"inline-block",renderContent:()=>(0,m.__)("In-blk","elementor"),label:(0,m.__)("Inline-block","elementor"),showTooltip:!0}],qn=()=>{const e=(0,E.isExperimentActive)(C.V_3_30),t=[...Yn];e&&t.push({value:"none",renderContent:()=>(0,m.__)("None","elementor"),label:(0,m.__)("None","elementor"),showTooltip:!0}),t.push({value:"inline-flex",renderContent:()=>(0,m.__)("In-flx","elementor"),label:(0,m.__)("Inline-flex","elementor"),showTooltip:!0});const r=Xn();return l.createElement(Vt,{bind:"display",propDisplayName:Jn,placeholder:r},l.createElement(jt,{label:Jn,direction:"column"},l.createElement(n.ToggleControl,{options:t,maxItems:4,fullWidth:!0})))},Xn=()=>mt(["display"])[0]?.value??void 0,Zn=(0,m.__)("Direction","elementor"),Qn=[{value:"row",label:(0,m.__)("Row","elementor"),renderContent:({size:e})=>{const t=(0,c.withDirection)(i.ArrowRightIcon);return l.createElement(t,{fontSize:e})},showTooltip:!0},{value:"column",label:(0,m.__)("Column","elementor"),renderContent:({size:e})=>l.createElement(i.ArrowDownSmallIcon,{fontSize:e}),showTooltip:!0},{value:"row-reverse",label:(0,m.__)("Reversed row","elementor"),renderContent:({size:e})=>{const t=(0,c.withDirection)(i.ArrowLeftIcon);return l.createElement(t,{fontSize:e})},showTooltip:!0},{value:"column-reverse",label:(0,m.__)("Reversed column","elementor"),renderContent:({size:e})=>l.createElement(i.ArrowUpSmallIcon,{fontSize:e}),showTooltip:!0}],el=()=>l.createElement(Vt,{bind:"flex-direction",propDisplayName:Zn},l.createElement(rn,null,l.createElement(jt,{label:Zn},l.createElement(n.ToggleControl,{options:Qn})))),tl=(0,m.__)("Order","elementor"),nl=-99999,ll="first",rl="last",ol="custom",al={[ll]:nl,[rl]:99999},il=[{value:ll,label:(0,m.__)("First","elementor"),renderContent:({size:e})=>l.createElement(i.ArrowUpSmallIcon,{fontSize:e}),showTooltip:!0},{value:rl,label:(0,m.__)("Last","elementor"),renderContent:({size:e})=>l.createElement(i.ArrowDownSmallIcon,{fontSize:e}),showTooltip:!0},{value:ol,label:(0,m.__)("Custom","elementor"),renderContent:({size:e})=>l.createElement(i.PencilIcon,{fontSize:e}),showTooltip:!0}],sl=()=>{const{value:e,setValue:t,canEdit:r}=yt("order",{history:{propDisplayName:tl}}),[o,a]=(0,l.useState)(cl(e?.value||null));return l.createElement(Vt,{bind:"order",propDisplayName:tl},l.createElement(rn,null,l.createElement(At,null,l.createElement(jt,{label:tl},l.createElement(n.ControlToggleButtonGroup,{items:il,value:o,onChange:e=>{a(e),t(e&&e!==ol?{$$type:"number",value:al[e]}:null)},exclusive:!0,disabled:!r})),ol===o&&l.createElement(c.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(c.Grid,{item:!0,xs:6},l.createElement($t,null,(0,m.__)("Custom order","elementor"))),l.createElement(c.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},l.createElement(n.NumberControl,{min:-99998,max:99998,shouldForceInt:!0}))))))},cl=e=>99999===e?rl:nl===e?ll:0===e||e?ol:null,ml=(0,m.__)("Flex Size","elementor"),ul=[{value:"flex-grow",label:(0,m.__)("Grow","elementor"),renderContent:({size:e})=>l.createElement(i.ExpandIcon,{fontSize:e}),showTooltip:!0},{value:"flex-shrink",label:(0,m.__)("Shrink","elementor"),renderContent:({size:e})=>l.createElement(i.ShrinkIcon,{fontSize:e}),showTooltip:!0},{value:"custom",label:(0,m.__)("Custom","elementor"),renderContent:({size:e})=>l.createElement(i.PencilIcon,{fontSize:e}),showTooltip:!0}],pl=()=>{const{values:e,setValues:t,canEdit:r}=dt(["flex-grow","flex-shrink","flex-basis"]),o=e?.["flex-grow"]?.value||null,a=e?.["flex-shrink"]?.value||null,i=e?.["flex-basis"]?.value||null,s=(0,l.useMemo)((()=>El({grow:o,shrink:a,basis:i})),[o,a,i]),[c,m]=(0,l.useState)(s);return l.createElement(rn,null,l.createElement(At,null,l.createElement(Vt,{bind:c??"",propDisplayName:ml},l.createElement(jt,{label:ml},l.createElement(n.ControlToggleButtonGroup,{value:c,onChange:(e=null)=>{let n;m(e),n=e&&"custom"!==e?"flex-grow"===e?{"flex-basis":null,"flex-grow":d.numberPropTypeUtil.create(1),"flex-shrink":null}:{"flex-basis":null,"flex-grow":null,"flex-shrink":d.numberPropTypeUtil.create(1)}:{"flex-basis":null,"flex-grow":null,"flex-shrink":null},t(n,{history:{propDisplayName:ml}})},disabled:!r,items:ul,exclusive:!0}))),"custom"===c&&l.createElement(dl,null)))},dl=()=>{const e=(0,l.useRef)(null);return l.createElement(l.Fragment,null,l.createElement(Vt,{bind:"flex-grow",propDisplayName:ml},l.createElement(jt,{label:(0,m.__)("Grow","elementor")},l.createElement(n.NumberControl,{min:0,shouldForceInt:!0}))),l.createElement(Vt,{bind:"flex-shrink",propDisplayName:ml},l.createElement(jt,{label:(0,m.__)("Shrink","elementor")},l.createElement(n.NumberControl,{min:0,shouldForceInt:!0}))),l.createElement(Vt,{bind:"flex-basis",propDisplayName:ml},l.createElement(jt,{label:(0,m.__)("Basis","elementor"),ref:e},l.createElement(n.SizeControl,{extendedOptions:["auto"],anchorRef:e}))))},El=({grow:e,shrink:t,basis:n})=>null!==e||null!==t||n?t&&e||n?"custom":1===e?"flex-grow":1===t?"flex-shrink":"custom":null,fl=(0,m.__)("Gaps","elementor"),bl=()=>l.createElement(Vt,{bind:"gap",propDisplayName:fl},l.createElement(n.GapControl,{label:fl})),yl=(0,m.__)("Justify content","elementor"),vl=(0,c.withDirection)(i.JustifyTopIcon),gl=(0,c.withDirection)(i.JustifyBottomIcon),_l={isClockwise:!0,offset:-90},hl=[{value:"flex-start",label:(0,m.__)("Start","elementor"),renderContent:({size:e})=>l.createElement(In,{icon:vl,size:e,..._l}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:e})=>l.createElement(In,{icon:i.JustifyCenterIcon,size:e,..._l}),showTooltip:!0},{value:"flex-end",label:(0,m.__)("End","elementor"),renderContent:({size:e})=>l.createElement(In,{icon:gl,size:e,..._l}),showTooltip:!0},{value:"space-between",label:(0,m.__)("Space between","elementor"),renderContent:({size:e})=>l.createElement(In,{icon:i.JustifySpaceBetweenVerticalIcon,size:e,..._l}),showTooltip:!0},{value:"space-around",label:(0,m.__)("Space around","elementor"),renderContent:({size:e})=>l.createElement(In,{icon:i.JustifySpaceAroundVerticalIcon,size:e,..._l}),showTooltip:!0},{value:"space-evenly",label:(0,m.__)("Space evenly","elementor"),renderContent:({size:e})=>l.createElement(In,{icon:i.JustifyDistributeVerticalIcon,size:e,..._l}),showTooltip:!0}],wl=()=>l.createElement(Vt,{bind:"justify-content",propDisplayName:yl},l.createElement(rn,null,l.createElement(jt,{label:yl,direction:"column"},l.createElement(n.ToggleControl,{options:hl,fullWidth:!0})))),Sl=(0,m.__)("Wrap","elementor"),Cl=[{value:"nowrap",label:(0,m.__)("No wrap","elementor"),renderContent:({size:e})=>l.createElement(i.ArrowRightIcon,{fontSize:e}),showTooltip:!0},{value:"wrap",label:(0,m.__)("Wrap","elementor"),renderContent:({size:e})=>l.createElement(i.ArrowBackIcon,{fontSize:e}),showTooltip:!0},{value:"wrap-reverse",label:(0,m.__)("Reversed wrap","elementor"),renderContent:({size:e})=>l.createElement(i.ArrowForwardIcon,{fontSize:e}),showTooltip:!0}],xl=()=>l.createElement(Vt,{bind:"flex-wrap",propDisplayName:Sl},l.createElement(rn,null,l.createElement(jt,{label:Sl},l.createElement(n.ToggleControl,{options:Cl})))),Tl=(0,m.__)("Display","elementor"),Il=(0,m.__)("Flex wrap","elementor"),kl=()=>{const{value:e}=yt("display",{history:{propDisplayName:Tl}}),t=Xn(),n=Dl(e,t),{element:o}=N(),a=(0,r.useParentElement)(o.id),i=(s=a?.id||null,(0,E.__privateUseListenTo)([(0,E.windowEvent)("elementor/device-mode/change"),(0,E.commandEndEvent)("document/elements/reset-style"),(0,E.commandEndEvent)("document/elements/settings"),(0,E.commandEndEvent)("document/elements/paste-style")],(()=>{if(!s)return null;const e=window,t=e.elementor?.getContainer?.(s);return t?.view?.el?window.getComputedStyle(t.view.el):null})));var s;const c=i?.flexDirection??"row";return l.createElement(At,null,l.createElement(qn,null),n&&l.createElement(zl,null),"flex"===i?.display&&l.createElement(Pl,{parentStyleDirection:c}))},zl=()=>{const{value:e}=yt("flex-wrap",{history:{propDisplayName:Il}});return l.createElement(l.Fragment,null,l.createElement(el,null),l.createElement(wl,null),l.createElement(Fn,null),l.createElement(Ot,null),l.createElement(bl,null),l.createElement(xl,null),["wrap","wrap-reverse"].includes(e?.value)&&l.createElement(Vn,null))},Pl=({parentStyleDirection:e})=>l.createElement(l.Fragment,null,l.createElement(Ot,null),l.createElement(n.ControlFormLabel,null,(0,m.__)("Flex child","elementor")),l.createElement(Hn,{parentStyleDirection:e}),l.createElement(sl,null),l.createElement(pl,null)),Dl=(e,t)=>{const n=e?.value??t?.value;return!!n&&("flex"===n||"inline-flex"===n)},Rl=(0,c.withDirection)(i.SideLeftIcon),Ll=(0,c.withDirection)(i.SideRightIcon),Vl={"inset-block-start":l.createElement(i.SideTopIcon,{fontSize:"tiny"}),"inset-block-end":l.createElement(i.SideBottomIcon,{fontSize:"tiny"}),"inset-inline-start":l.createElement(In,{icon:Rl,size:"tiny"}),"inset-inline-end":l.createElement(In,{icon:Ll,size:"tiny"})},Al=e=>e?(0,m.__)("Right","elementor"):(0,m.__)("Left","elementor"),Nl=e=>e?(0,m.__)("Left","elementor"):(0,m.__)("Right","elementor"),Bl=()=>{const{isSiteRtl:e}=_t(),t=[(0,l.useRef)(null),(0,l.useRef)(null)];return l.createElement(rn,null,l.createElement(c.Stack,{direction:"row",gap:2,flexWrap:"nowrap",ref:t[0]},l.createElement(Ol,{side:"inset-block-start",label:(0,m.__)("Top","elementor"),rowRef:t[0]}),l.createElement(Ol,{side:"inset-inline-end",label:Nl(e),rowRef:t[0]})),l.createElement(c.Stack,{direction:"row",gap:2,flexWrap:"nowrap",ref:t[1]},l.createElement(Ol,{side:"inset-block-end",label:(0,m.__)("Bottom","elementor"),rowRef:t[1]}),l.createElement(Ol,{side:"inset-inline-start",label:Al(e),rowRef:t[1]})))},Ol=({side:e,label:t,rowRef:r})=>l.createElement(Vt,{bind:e,propDisplayName:t},l.createElement(c.Grid,{container:!0,gap:.75,alignItems:"center"},l.createElement(c.Grid,{item:!0,xs:12},l.createElement($t,null,t)),l.createElement(c.Grid,{item:!0,xs:12},l.createElement(n.SizeControl,{startIcon:Vl[e],extendedOptions:["auto"],anchorRef:r})))),Ml=(0,m.__)("Anchor offset","elementor"),Fl=["px","em","rem","vw","vh"],$l=()=>{const e=(0,l.useRef)(null);return l.createElement(Vt,{bind:"scroll-margin-top",propDisplayName:Ml},l.createElement(jt,{label:Ml,ref:e},l.createElement(n.SizeControl,{units:Fl,anchorRef:e})))},jl=(0,m.__)("Position","elementor"),Ul=[{label:(0,m.__)("Static","elementor"),value:"static"},{label:(0,m.__)("Relative","elementor"),value:"relative"},{label:(0,m.__)("Absolute","elementor"),value:"absolute"},{label:(0,m.__)("Fixed","elementor"),value:"fixed"},{label:(0,m.__)("Sticky","elementor"),value:"sticky"}],Wl=({onChange:e})=>l.createElement(Vt,{bind:"position",propDisplayName:jl},l.createElement(jt,{label:jl},l.createElement(n.SelectControl,{options:Ul,onChange:e}))),Gl=(0,m.__)("Z-index","elementor"),Kl=()=>l.createElement(Vt,{bind:"z-index",propDisplayName:Gl},l.createElement(jt,{label:Gl},l.createElement(n.NumberControl,null))),Hl=(0,m.__)("Position","elementor"),Jl=(0,m.__)("Dimensions","elementor"),Yl=()=>{const{value:e}=yt("position",{history:{propDisplayName:Hl}}),{values:t,setValues:n}=dt(["inset-block-start","inset-block-end","inset-inline-start","inset-inline-end"]),[r,o,a]=ql(),i=(0,E.isExperimentActive)("e_v_3_30"),s=e&&"static"!==e?.value;return l.createElement(At,null,l.createElement(Wl,{onChange:(e,l)=>{const i={history:{propDisplayName:Jl}};"static"===e?t&&(o(t),n({"inset-block-start":void 0,"inset-block-end":void 0,"inset-inline-start":void 0,"inset-inline-end":void 0},i)):"static"===l&&r&&(n(r,i),a())}}),s?l.createElement(l.Fragment,null,l.createElement(Bl,null),l.createElement(Kl,null)):null,i&&l.createElement(l.Fragment,null,l.createElement(Ot,null),l.createElement($l,null)))},ql=()=>{const{id:e,meta:t}=U(),n=`styles/${e}/${t.breakpoint||"desktop"}/${t.state||"null"}/dimensions`;return(0,b.useSessionStorage)(n)},Xl=({fields:e})=>{const{id:t,meta:n,provider:r}=U(),a=ct(),i=Object.fromEntries(Object.entries(a??{}).filter((([t])=>e.includes(t)))),{hasValues:s,hasOverrides:u}=function(e,t,n){let l=!1,r=!1;return Object.values(e).forEach((e=>{const o=function(e,t,n){return e.find((({style:{id:e},variant:{meta:{breakpoint:l,state:r}}})=>e===t&&l===n.breakpoint&&r===n.state))}(e,t,n);if(!o)return;const[a]=e;o===a?l=!0:r=!0})),{hasValues:l,hasOverrides:r}}(i,t??"",n);if(!s&&!u)return null;const p=(0,m.__)("Has effective styles","elementor"),d=(0,m.__)("Has overridden styles","elementor");return l.createElement(c.Tooltip,{title:(0,m.__)("Has styles","elementor"),placement:"top"},l.createElement(c.Stack,{direction:"row",sx:{"& > *":{marginInlineStart:-.25}},role:"list"},s&&r&&l.createElement(le,{getColor:K(r.getKey()),"data-variant":(0,o.isElementsStylesProvider)(r.getKey())?"local":"global",role:"listitem","aria-label":p}),u&&l.createElement(le,{isOverridden:!0,"data-variant":"overridden",role:"listitem","aria-label":d})))},Zl=({fields:e=[],children:t})=>l.createElement(Ke,{titleEnd:Ql(e)},t);function Ql(e){const t=(0,E.isExperimentActive)(C.V_3_30);return 0!==e.length&&t?t=>t?null:l.createElement(Xl,{fields:e}):null}var er=(0,m.__)("Object fit","elementor"),tr=[{label:(0,m.__)("Fill","elementor"),value:"fill"},{label:(0,m.__)("Cover","elementor"),value:"cover"},{label:(0,m.__)("Contain","elementor"),value:"contain"},{label:(0,m.__)("None","elementor"),value:"none"},{label:(0,m.__)("Scale down","elementor"),value:"scale-down"}],nr=()=>l.createElement(Vt,{bind:"object-fit",propDisplayName:er},l.createElement(jt,{label:er},l.createElement(n.SelectControl,{options:tr}))),lr=(0,m.__)("Object position","elementor"),rr=()=>l.createElement(Vt,{bind:"object-position",propDisplayName:lr},l.createElement(n.PositionControl,null)),or=(0,m.__)("Overflow","elementor"),ar=[{value:"visible",label:(0,m.__)("Visible","elementor"),renderContent:({size:e})=>l.createElement(i.EyeIcon,{fontSize:e}),showTooltip:!0},{value:"hidden",label:(0,m.__)("Hidden","elementor"),renderContent:({size:e})=>l.createElement(i.EyeOffIcon,{fontSize:e}),showTooltip:!0},{value:"auto",label:(0,m.__)("Auto","elementor"),renderContent:({size:e})=>l.createElement(i.LetterAIcon,{fontSize:e}),showTooltip:!0}],ir=()=>l.createElement(Vt,{bind:"overflow",propDisplayName:or},l.createElement(jt,{label:or},l.createElement(n.ToggleControl,{options:ar}))),sr=[[{bind:"width",label:(0,m.__)("Width","elementor")},{bind:"height",label:(0,m.__)("Height","elementor")}],[{bind:"min-width",label:(0,m.__)("Min width","elementor")},{bind:"min-height",label:(0,m.__)("Min height","elementor")}],[{bind:"max-width",label:(0,m.__)("Max width","elementor")},{bind:"max-height",label:(0,m.__)("Max height","elementor")}]],cr=(0,m.__)("Aspect Ratio","elementor"),mr=(0,m.__)("Object fit","elementor"),ur=()=>{const{value:e}=yt("object-fit",{history:{propDisplayName:mr}}),t=e&&"fill"!==e?.value,r=[(0,l.useRef)(null),(0,l.useRef)(null),(0,l.useRef)(null)],o=(0,E.isExperimentActive)("e_v_3_30");return l.createElement(At,null,sr.map(((e,t)=>l.createElement(c.Grid,{key:t,container:!0,gap:2,flexWrap:"nowrap",ref:r[t]},e.map((e=>l.createElement(c.Grid,{item:!0,xs:6,key:e.bind},l.createElement(pr,{...e,rowRef:r[t],extendedOptions:["auto"]}))))))),l.createElement(Ot,null),l.createElement(c.Stack,null,l.createElement(ir,null)),o&&l.createElement(Zl,{fields:["aspect-ratio","object-fit"]},l.createElement(c.Stack,{gap:2,pt:2},l.createElement(Vt,{bind:"aspect-ratio",propDisplayName:cr},l.createElement(n.AspectRatioControl,{label:cr})),l.createElement(Ot,null),l.createElement(nr,null),t&&l.createElement(c.Grid,{item:!0,xs:6},l.createElement(rr,null)))))},pr=({label:e,bind:t,rowRef:r,extendedOptions:o})=>l.createElement(Vt,{bind:t,propDisplayName:e},l.createElement(c.Grid,{container:!0,gap:.75,alignItems:"center"},l.createElement(c.Grid,{item:!0,xs:12},l.createElement($t,null,e)),l.createElement(c.Grid,{item:!0,xs:12},l.createElement(n.SizeControl,{extendedOptions:o,anchorRef:r})))),dr=(0,m.__)("Margin","elementor"),Er=(0,m.__)("Padding","elementor"),fr=()=>{const{isSiteRtl:e}=_t();return l.createElement(At,null,l.createElement(Vt,{bind:"margin",propDisplayName:dr},l.createElement(n.LinkedDimensionsControl,{label:dr,isSiteRtl:e,extendedOptions:["auto"]})),l.createElement(Ot,null),l.createElement(Vt,{bind:"padding",propDisplayName:Er},l.createElement(n.LinkedDimensionsControl,{label:Er,isSiteRtl:e})))},br=(0,m.__)("Columns","elementor"),yr=()=>l.createElement(Vt,{bind:"column-count",propDisplayName:br},l.createElement(jt,{label:br},l.createElement(n.NumberControl,{shouldForceInt:!0,min:0,step:1}))),vr=(0,m.__)("Column gap","elementor"),gr=()=>{const e=(0,l.useRef)(null);return l.createElement(Vt,{bind:"column-gap",propDisplayName:vr},l.createElement(jt,{label:vr,ref:e},l.createElement(n.SizeControl,{anchorRef:e})))},_r={system:(0,m.__)("System","elementor"),custom:(0,m.__)("Custom Fonts","elementor"),googlefonts:(0,m.__)("Google Fonts","elementor")},hr=()=>{const e=(()=>{const{controls:e}=(()=>{const e=window;return e.elementor?.config??{}})(),t=e?.font?.options;return t||null})();return(0,l.useMemo)((()=>{const t=["system","custom","googlefonts"];return Object.entries(e||{}).reduce(((e,[n,l])=>{if(!_r[l])return e;const r=t.indexOf(l);return e[r]||(e[r]={label:_r[l],fonts:[]}),e[r].fonts.push(n),e}),[]).filter(Boolean)}),[e])},wr=(0,m.__)("Font family","elementor"),Sr=()=>{const e=hr(),t=Ue();return 0===e.length?null:l.createElement(Vt,{bind:"font-family",propDisplayName:wr},l.createElement(jt,{label:wr},l.createElement(n.FontFamilyControl,{fontFamilies:e,sectionWidth:t})))},Cr=(0,m.__)("Font size","elementor"),xr=()=>{const e=(0,l.useRef)(null);return l.createElement(Vt,{bind:"font-size",propDisplayName:Cr},l.createElement(jt,{label:Cr,ref:e},l.createElement(n.SizeControl,{anchorRef:e})))},Tr=(0,m.__)("Font style","elementor"),Ir=[{value:"normal",label:(0,m.__)("Normal","elementor"),renderContent:({size:e})=>l.createElement(i.MinusIcon,{fontSize:e}),showTooltip:!0},{value:"italic",label:(0,m.__)("Italic","elementor"),renderContent:({size:e})=>l.createElement(i.ItalicIcon,{fontSize:e}),showTooltip:!0}],kr=()=>l.createElement(Vt,{bind:"font-style",propDisplayName:Tr},l.createElement(jt,{label:Tr},l.createElement(n.ToggleControl,{options:Ir}))),zr=(0,m.__)("Font weight","elementor"),Pr=[{value:"100",label:(0,m.__)("100 - Thin","elementor")},{value:"200",label:(0,m.__)("200 - Extra light","elementor")},{value:"300",label:(0,m.__)("300 - Light","elementor")},{value:"400",label:(0,m.__)("400 - Normal","elementor")},{value:"500",label:(0,m.__)("500 - Medium","elementor")},{value:"600",label:(0,m.__)("600 - Semi bold","elementor")},{value:"700",label:(0,m.__)("700 - Bold","elementor")},{value:"800",label:(0,m.__)("800 - Extra bold","elementor")},{value:"900",label:(0,m.__)("900 - Black","elementor")}],Dr=()=>l.createElement(Vt,{bind:"font-weight",propDisplayName:zr},l.createElement(jt,{label:zr},l.createElement(n.SelectControl,{options:Pr}))),Rr=(0,m.__)("Letter spacing","elementor"),Lr=()=>{const e=(0,l.useRef)(null);return l.createElement(Vt,{bind:"letter-spacing",propDisplayName:Rr},l.createElement(jt,{label:Rr,ref:e},l.createElement(n.SizeControl,{anchorRef:e})))},Vr=(0,m.__)("Line height","elementor"),Ar=()=>{const e=(0,l.useRef)(null);return l.createElement(Vt,{bind:"line-height",propDisplayName:Vr},l.createElement(jt,{label:Vr,ref:e},l.createElement(n.SizeControl,{anchorRef:e})))},Nr=(0,m.__)("Text align","elementor"),Br=(0,c.withDirection)(i.AlignLeftIcon),Or=(0,c.withDirection)(i.AlignRightIcon),Mr=[{value:"start",label:(0,m.__)("Start","elementor"),renderContent:({size:e})=>l.createElement(Br,{fontSize:e}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:e})=>l.createElement(i.AlignCenterIcon,{fontSize:e}),showTooltip:!0},{value:"end",label:(0,m.__)("End","elementor"),renderContent:({size:e})=>l.createElement(Or,{fontSize:e}),showTooltip:!0},{value:"justify",label:(0,m.__)("Justify","elementor"),renderContent:({size:e})=>l.createElement(i.AlignJustifiedIcon,{fontSize:e}),showTooltip:!0}],Fr=()=>l.createElement(Vt,{bind:"text-align",propDisplayName:Nr},l.createElement(rn,null,l.createElement(jt,{label:Nr},l.createElement(n.ToggleControl,{options:Mr})))),$r=(0,m.__)("Text color","elementor"),jr=()=>l.createElement(Vt,{bind:"color",propDisplayName:$r},l.createElement(jt,{label:$r},l.createElement(n.ColorControl,null))),Ur=(0,m.__)("Line decoration","elementor"),Wr=[{value:"none",label:(0,m.__)("None","elementor"),renderContent:({size:e})=>l.createElement(i.MinusIcon,{fontSize:e}),showTooltip:!0,exclusive:!0},{value:"underline",label:(0,m.__)("Underline","elementor"),renderContent:({size:e})=>l.createElement(i.UnderlineIcon,{fontSize:e}),showTooltip:!0},{value:"line-through",label:(0,m.__)("Line-through","elementor"),renderContent:({size:e})=>l.createElement(i.StrikethroughIcon,{fontSize:e}),showTooltip:!0},{value:"overline",label:(0,m.__)("Overline","elementor"),renderContent:({size:e})=>l.createElement(i.OverlineIcon,{fontSize:e}),showTooltip:!0}],Gr=()=>l.createElement(Vt,{bind:"text-decoration",propDisplayName:Ur},l.createElement(jt,{label:Ur},l.createElement(n.ToggleControl,{options:Wr,exclusive:!1}))),Kr=(0,m.__)("Direction","elementor"),Hr=[{value:"ltr",label:(0,m.__)("Left to right","elementor"),renderContent:({size:e})=>l.createElement(i.TextDirectionLtrIcon,{fontSize:e}),showTooltip:!0},{value:"rtl",label:(0,m.__)("Right to left","elementor"),renderContent:({size:e})=>l.createElement(i.TextDirectionRtlIcon,{fontSize:e}),showTooltip:!0}],Jr=()=>l.createElement(Vt,{bind:"direction",propDisplayName:Kr},l.createElement(jt,{label:Kr},l.createElement(n.ToggleControl,{options:Hr}))),Yr={$$type:"stroke",value:{color:{$$type:"color",value:"#000000"},width:{$$type:"size",value:{unit:"px",size:1}}}},qr=(0,m.__)("Text stroke","elementor"),Xr=()=>{const{value:e,setValue:t,canEdit:r}=yt("stroke",{history:{propDisplayName:qr}}),o=Boolean(e);return l.createElement(Vt,{bind:"stroke",propDisplayName:qr},l.createElement(Ft,{isAdded:o,onAdd:()=>{t(Yr)},onRemove:()=>{t(null)},disabled:!r,renderLabel:()=>l.createElement($t,null,qr)},l.createElement(n.StrokeControl,null)))},Zr=(0,m.__)("Text transform","elementor"),Qr=[{value:"none",label:(0,m.__)("None","elementor"),renderContent:({size:e})=>l.createElement(i.MinusIcon,{fontSize:e}),showTooltip:!0},{value:"capitalize",label:(0,m.__)("Capitalize","elementor"),renderContent:({size:e})=>l.createElement(i.LetterCaseIcon,{fontSize:e}),showTooltip:!0},{value:"uppercase",label:(0,m.__)("Uppercase","elementor"),renderContent:({size:e})=>l.createElement(i.LetterCaseUpperIcon,{fontSize:e}),showTooltip:!0},{value:"lowercase",label:(0,m.__)("Lowercase","elementor"),renderContent:({size:e})=>l.createElement(i.LetterCaseLowerIcon,{fontSize:e}),showTooltip:!0}],eo=()=>l.createElement(Vt,{bind:"text-transform",propDisplayName:Zr},l.createElement(jt,{label:Zr},l.createElement(n.ToggleControl,{options:Qr}))),to=(0,m.__)("Word spacing","elementor"),no=()=>{const e=(0,l.useRef)(null);return l.createElement(Vt,{bind:"word-spacing",propDisplayName:to},l.createElement(jt,{label:to,ref:e},l.createElement(n.SizeControl,{anchorRef:e})))},lo=(0,m.__)("Column count","elementor"),ro=()=>{const{value:e}=yt("column-count",{history:{propDisplayName:lo}}),t=!!(e?.value&&e?.value>1),n=(0,E.isExperimentActive)("e_v_3_30");return l.createElement(At,null,l.createElement(Sr,null),l.createElement(Dr,null),l.createElement(xr,null),l.createElement(Ot,null),l.createElement(Fr,null),l.createElement(jr,null),l.createElement(Zl,{fields:["line-height","letter-spacing","word-spacing","column-count","text-decoration","text-transform","direction","font-style","stroke"]},l.createElement(At,{sx:{pt:2}},l.createElement(Ar,null),l.createElement(Lr,null),l.createElement(no,null),n&&l.createElement(l.Fragment,null,l.createElement(yr,null),t&&l.createElement(gr,null)),l.createElement(Ot,null),l.createElement(Gr,null),l.createElement(eo,null),l.createElement(Jr,null),l.createElement(kr,null),l.createElement(Xr,null))))},oo=({section:e,fields:t=[]})=>{const{component:n,name:r,title:o}=e,a=Re(),i=n,s=!!(0,E.isExperimentActive)(C.V_3_30)&&a.defaultSectionsExpanded.style?.includes(r);return l.createElement(Je,{title:o,defaultExpanded:s,titleEnd:Ql(t)},l.createElement(i,null))},ao={position:"sticky",zIndex:1100,opacity:1,backgroundColor:"background.default",transition:"top 300ms ease"},io=()=>{const e=function(){const{elementType:e}=N(),t=Object.entries(e.propsSchema).find((([,e])=>"plain"===e.kind&&e.key===d.CLASSES_PROP_KEY));if(!t)throw new Error("Element does not have a classes prop");return t[0]}(),[t,n]=function(e){const[t,n]=Le("active-style-id",null),l=function(e){const{element:t}=N();return(0,r.useElementSetting)(t.id,e)}(e)?.value||[],o=function(e){const{element:t}=N(),n=(0,r.getElementStyles)(t.id)??{};return Object.values(n).find((t=>e.includes(t.id)))}(l);return[function(e,t){return e&&t.includes(e)?e:null}(t,l)||o?.id||null,n]}(e),[o,a]=(0,l.useState)(null),i=(0,v.useActiveBreakpoint)();return l.createElement(R,{prop:e},l.createElement(j,{meta:{breakpoint:i,state:o},id:t,setId:e=>{n(e),a(null)},setMetaState:a},l.createElement(b.SessionStorageProvider,{prefix:t??""},l.createElement(st,null,l.createElement(so,null,l.createElement(_e,null),l.createElement(c.Divider,null)),l.createElement(Ye,null,l.createElement(oo,{section:{component:kl,name:"Layout",title:(0,m.__)("Layout","elementor")},fields:["display","flex-direction","flex-wrap","justify-content","align-items","align-content","align-self","gap"]}),l.createElement(oo,{section:{component:fr,name:"Spacing",title:(0,m.__)("Spacing","elementor")},fields:["margin","padding"]}),l.createElement(oo,{section:{component:ur,name:"Size",title:(0,m.__)("Size","elementor")},fields:["width","min-width","max-width","height","min-height","max-height","overflow","aspect-ratio","object-fit"]}),l.createElement(oo,{section:{component:Yl,name:"Position",title:(0,m.__)("Position","elementor")},fields:["position","z-index","scroll-margin-top"]}),l.createElement(oo,{section:{component:ro,name:"Typography",title:(0,m.__)("Typography","elementor")},fields:["font-family","font-weight","font-size","text-align","color","line-height","letter-spacing","word-spacing","column-count","text-decoration","text-transform","direction","font-style","stroke"]}),l.createElement(oo,{section:{component:Bt,name:"Background",title:(0,m.__)("Background","elementor")},fields:["background"]}),l.createElement(oo,{section:{component:yn,name:"Border",title:(0,m.__)("Border","elementor")},fields:["border-radius","border-width","border-color","border-style"]}),l.createElement(oo,{section:{component:Sn,name:"Effects",title:(0,m.__)("Effects","elementor")},fields:["box-shadow"]}))))))};function so({children:e}){const t=(0,l.useContext)(Ie)?.direction??"up";return l.createElement(c.Stack,{sx:{...ao,top:"up"===t?"37px":0}},e)}var co=()=>{const{element:e}=N();return l.createElement(l.Fragment,{key:e.id},l.createElement(mo,null))},mo=()=>{const e=Re(),t=(0,E.isExperimentActive)(C.V_3_30)?e.defaultTab:"settings",[n,r]=Le("tab",t),{getTabProps:o,getTabPanelProps:a,getTabsProps:i}=(0,c.useTabs)(n);return l.createElement(ze,null,l.createElement(c.Stack,{direction:"column",sx:{width:"100%"}},l.createElement(c.Stack,{sx:{...ao,top:0}},l.createElement(c.Tabs,{variant:"fullWidth",size:"small",sx:{mt:.5},...i(),onChange:(e,t)=>{i().onChange(e,t),r(t)}},l.createElement(c.Tab,{label:(0,m.__)("General","elementor"),...o("settings")}),l.createElement(c.Tab,{label:(0,m.__)("Style","elementor"),...o("style")})),l.createElement(c.Divider,null)),l.createElement(c.TabPanel,{...a("settings"),disablePadding:!0},l.createElement(qe,null)),l.createElement(c.TabPanel,{...a("style"),disablePadding:!0},l.createElement(io,null))))},{useMenuItems:uo}=xe,{panel:po,usePanelActions:Eo,usePanelStatus:fo}=(0,f.__createPanel)({id:"editing-panel",component:()=>{const{element:e,elementType:t}=(0,r.useSelectedElement)(),o=T(),s=uo().default;if(!e||!t)return null;const u=(0,m.__)("Edit %s","elementor").replace("%s",t.title);return l.createElement(c.ErrorBoundary,{fallback:l.createElement(Te,null)},l.createElement(b.SessionStorageProvider,{prefix:"elementor"},l.createElement(a.ThemeProvider,null,l.createElement(f.Panel,null,l.createElement(f.PanelHeader,null,l.createElement(f.PanelHeaderTitle,null,u),l.createElement(i.AtomIcon,{fontSize:"small",sx:{color:"text.tertiary"}})),l.createElement(f.PanelBody,null,l.createElement(n.ControlActionsProvider,{items:s},l.createElement(n.ControlReplacementsProvider,{replacements:o},l.createElement(A,{element:e,elementType:t},l.createElement(co,null)))))))))}}),bo=e=>{const t=Ue();return l.createElement(a.PopoverScrollableContent,{...e,width:t})},yo=()=>{const e=(0,r.getSelectedElements)(),t=(0,r.getWidgetsCache)();return 1===e.length&&!!t?.[e[0].type]?.atomic_controls},vo=()=>((()=>{const{open:e}=Eo();(0,l.useEffect)((()=>(0,E.__privateListenTo)((0,E.commandStartEvent)("panel/editor/open"),(()=>{yo()&&e()}))),[])})(),null),go=()=>{const{atomicDynamicTags:e}=(()=>{const e=window;return e.elementor?.config??{}})();return e?{tags:e.tags,groups:e.groups}:null},_o="dynamic",ho=e=>{const t="union"===e.kind&&e.prop_types[_o];return t&&t.key===_o?t:null},wo=e=>(0,d.isTransformable)(e)&&e.$$type===_o,So=(0,d.createPropUtils)(_o,w.z.strictObject({name:w.z.string(),settings:w.z.any().optional()})),Co=()=>{let e=[];const{propType:t}=(0,n.useBoundProp)();if(t){const n=ho(t);e=n?.settings.categories||[]}return(0,l.useMemo)((()=>xo(e)),[e.join()])},xo=e=>{const t=go();if(!e.length||!t?.tags)return[];const n=new Set(e);return Object.values(t.tags).filter((e=>e.categories.some((e=>n.has(e)))))},To=e=>{const t=Co();return(0,l.useMemo)((()=>t.find((t=>t.name===e))??null),[t,e])},Io=()=>l.createElement(i.DatabaseIcon,{fontSize:"tiny"}),ko=({value:e})=>{const t=(0,n.useBoundProp)(d.backgroundImageOverlayPropTypeUtil);return l.createElement(n.PropProvider,{...t,value:e.value},l.createElement(n.PropKeyProvider,{bind:"image"},l.createElement(zo,{rawValue:e.value})))},zo=({rawValue:e})=>{const{propType:t}=(0,n.useBoundProp)(),r=t.prop_types["background-image-overlay"];return l.createElement(n.PropProvider,{propType:r.shape.image,value:e,setValue:()=>{}},l.createElement(n.PropKeyProvider,{bind:"src"},l.createElement(Po,{rawValue:e.image})))},Po=({rawValue:e})=>{const t=e.value.src,n=To(t.value.name||"");return l.createElement(l.Fragment,null,n?.label)},Do=e=>{const{element:t}=N(),n=`dynamic/non-dynamic-values-history/${t.id}/${e}`;return(0,b.useSessionStorage)(n)},Ro=({bind:e,children:t})=>{const{value:r,setValue:o}=(0,n.useBoundProp)(So),{name:a="",settings:i}=r??{},s=To(a);if(!s)throw new Error(`Dynamic tag ${a} not found`);const c=s.props_schema[e],m=c?.default,u=i?.[e]??m,p=Fe({schema:s.props_schema});return l.createElement(n.PropProvider,{propType:p,setValue:e=>{o({name:a,settings:{...i,...e}})},value:{[e]:u}},l.createElement(n.PropKeyProvider,{bind:e},t))},Lo=({close:e})=>{const[t,r]=(0,l.useState)(""),{groups:o}=go()||{},s=(0,c.useTheme)(),{value:u}=(0,n.useBoundProp)(),{bind:p,value:d,setValue:E}=(0,n.useBoundProp)(So),[,f]=Do(p),b=!!d,y=No(t),v=!y.length&&!t.trim(),g=y.flatMap((([e,t])=>[{type:"category",value:e,label:o?.[e]?.title||e},...t.map((e=>({type:"item",value:e.value,label:e.label})))]));return l.createElement(l.Fragment,null,l.createElement(a.PopoverHeader,{title:(0,m.__)("Dynamic tags","elementor"),onClose:e,icon:l.createElement(i.DatabaseIcon,{fontSize:"tiny"})}),l.createElement(c.Stack,null,v?l.createElement(Ao,null):l.createElement(l.Fragment,null,l.createElement(a.PopoverSearch,{value:t,onSearch:e=>{r(e)},placeholder:(0,m.__)("Search dynamic tags…","elementor")}),l.createElement(c.Divider,null),l.createElement(bo,null,l.createElement(a.PopoverMenuList,{items:g,onSelect:t=>{b||f(u);const n=y.flatMap((([,e])=>e)).find((e=>e.value===t));E({name:t,settings:{label:n?.label}}),e()},onClose:e,selectedValue:d?.name,itemStyle:e=>"item"===e.type?{paddingInlineStart:s.spacing(3.5)}:{},noResultsComponent:l.createElement(Vo,{searchValue:t,onClear:()=>r("")})})))))},Vo=({searchValue:e,onClear:t})=>l.createElement(c.Stack,{gap:1,alignItems:"center",justifyContent:"center",height:"100%",p:2.5,color:"text.secondary",sx:{pb:3.5}},l.createElement(i.DatabaseIcon,{fontSize:"large"}),l.createElement(c.Typography,{align:"center",variant:"subtitle2"},(0,m.__)("Sorry, nothing matched","elementor"),l.createElement("br",null),"“",e,"”."),l.createElement(c.Typography,{align:"center",variant:"caption",sx:{display:"flex",flexDirection:"column"}},(0,m.__)("Try something else.","elementor"),l.createElement(c.Link,{color:"text.secondary",variant:"caption",component:"button",onClick:t},(0,m.__)("Clear & try again","elementor")))),Ao=()=>l.createElement(c.Box,{sx:{overflowY:"hidden",height:297,width:220}},l.createElement(c.Divider,null),l.createElement(c.Stack,{gap:1,alignItems:"center",justifyContent:"center",height:"100%",p:2.5,color:"text.secondary",sx:{pb:3.5}},l.createElement(i.DatabaseIcon,{fontSize:"large"}),l.createElement(c.Typography,{align:"center",variant:"subtitle2"},(0,m.__)("Streamline your workflow with dynamic tags","elementor")),l.createElement(c.Typography,{align:"center",variant:"caption"},(0,m.__)("You'll need Elementor Pro to use this feature.","elementor")))),No=e=>[...Co().reduce(((t,{name:n,label:l,group:r})=>l.toLowerCase().includes(e.trim().toLowerCase())?(t.has(r)||t.set(r,[]),t.get(r)?.push({label:l,value:n}),t):t),new Map)],Bo="tiny",Oo=()=>{const{setValue:e}=(0,n.useBoundProp)(),{bind:t,value:r}=(0,n.useBoundProp)(So),[o]=Do(t),a=(0,c.usePopupState)({variant:"popover"}),{name:s=""}=r,u=To(s);if(!u)throw new Error(`Dynamic tag ${s} not found`);return l.createElement(c.Box,null,l.createElement(c.UnstableTag,{fullWidth:!0,showActionsOnHover:!0,label:u.label,startIcon:l.createElement(i.DatabaseIcon,{fontSize:Bo}),...(0,c.bindTrigger)(a),actions:l.createElement(l.Fragment,null,l.createElement(Mo,{dynamicTag:u}),l.createElement(c.IconButton,{size:Bo,onClick:()=>{e(o??null)},"aria-label":(0,m.__)("Remove dynamic value","elementor")},l.createElement(i.XIcon,{fontSize:Bo})))}),l.createElement(c.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},PaperProps:{sx:{my:1}},...(0,c.bindPopover)(a)},l.createElement(c.Stack,null,l.createElement(Lo,{close:a.close}))))},Mo=({dynamicTag:e})=>{const t=(0,c.usePopupState)({variant:"popover"});return e.atomic_controls.length?l.createElement(l.Fragment,null,l.createElement(c.IconButton,{size:Bo,...(0,c.bindTrigger)(t),"aria-label":(0,m.__)("Settings","elementor")},l.createElement(i.SettingsIcon,{fontSize:Bo})),l.createElement(c.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"center"},PaperProps:{sx:{my:.5}},...(0,c.bindPopover)(t)},l.createElement(a.PopoverHeader,{title:e.label,onClose:t.close,icon:l.createElement(i.DatabaseIcon,{fontSize:Bo})}),l.createElement(Fo,{controls:e.atomic_controls}))):null},Fo=({controls:e})=>{const t=e.filter((({type:e})=>"section"===e)),{getTabsProps:n,getTabProps:r,getTabPanelProps:o}=(0,c.useTabs)(0);return t.length?l.createElement(a.PopoverScrollableContent,null,l.createElement(c.Tabs,{size:"small",variant:"fullWidth",...n()},t.map((({value:e},t)=>l.createElement(c.Tab,{key:t,label:e.label,sx:{px:1,py:.5},...r(t)})))),l.createElement(c.Divider,null),t.map((({value:e},t)=>l.createElement(c.TabPanel,{key:t,sx:{flexGrow:1,py:0},...o(t)},l.createElement(c.Stack,{p:2,gap:2},e.items.map((e=>"control"===e.type?l.createElement($o,{key:e.value.bind,control:e.value}):null))))))):null},$o=({control:e})=>Ae(e.type)?l.createElement(Ro,{bind:e.bind},l.createElement(c.Grid,{container:!0,gap:.75},e.label?l.createElement(c.Grid,{item:!0,xs:12},l.createElement(n.ControlFormLabel,null,e.label)):null,l.createElement(c.Grid,{item:!0,xs:12},l.createElement(Ne,{type:e.type,props:e.props})))):null,jo=(0,u.createError)({code:"dynamic_tags_manager_not_found",message:"Dynamic tags manager not found"}),Uo=(0,_.createTransformer)((e=>e.name?function(e,t){const n=window,{dynamicTags:l}=n.elementor??{};if(!l)throw new jo;const r=()=>{const n=l.createTag("v4-dynamic-tag",e,t);return n?l.loadTagDataFromCache(n)??null:null},o=r();return null!==o?o:new Promise((e=>{l.refreshCacheFromServer((()=>{e(r())}))}))}(e.name,function(e){const t=Object.entries(e).map((([e,t])=>[e,(0,d.isTransformable)(t)?t.value:t]));return Object.fromEntries(t)}(e.settings??{})):null)),Wo=()=>{const{propType:e}=(0,n.useBoundProp)(),t=!!e&&(e=>!!ho(e))(e);return{visible:t,icon:i.DatabaseIcon,title:(0,m.__)("Dynamic tags","elementor"),content:({close:e})=>l.createElement(Lo,{close:e})}},{registerPopoverAction:Go}=xe,Ko=()=>{x({component:Oo,condition:({value:e})=>wo(e)}),(0,n.injectIntoRepeaterItemLabel)({id:"dynamic-background-image",condition:({value:e})=>wo(e.value?.image?.value?.src),component:ko}),(0,n.injectIntoRepeaterItemIcon)({id:"dynamic-background-image",condition:({value:e})=>wo(e.value?.image?.value?.src),component:Io}),Go({id:"dynamic-tags",useProps:Wo}),_.styleTransformersRegistry.register("dynamic",Uo),_.settingsTransformersRegistry.register("dynamic",Uo)},{registerAction:Ho}=xe,Jo=["order","flex-grow","flex-shrink","flex-basis"];function Yo(){const e=!!(0,l.useContext)($),{value:t,setValue:r,path:o,bind:a}=(0,n.useBoundProp)();return{visible:e&&null!=t&&o.length<=2&&!Jo.includes(a),title:(0,m.__)("Clear","elementor"),icon:i.BrushBigIcon,onClick:()=>r(null)}}var qo=(0,_.createTransformer)((e=>l.createElement(c.Stack,{direction:"row",gap:10},l.createElement(Xo,{value:e}),l.createElement(Zo,{value:e})))),Xo=({value:e})=>{const{color:t}=e;return l.createElement(Qo,{size:"inherit",component:"span",value:t})},Zo=({value:{color:e}})=>l.createElement("span",null,e),Qo=(0,c.styled)(c.UnstableColorIndicator)((({theme:e})=>({borderRadius:e.shape.borderRadius/2+"px"}))),ea=(0,_.createTransformer)((e=>l.createElement(c.Stack,{direction:"row",gap:10},l.createElement(ta,{value:e}),l.createElement(na,{value:e})))),ta=({value:e})=>{const t=la(e);return l.createElement(Qo,{size:"inherit",component:"span",value:t})},na=({value:e})=>"linear"===e.type?l.createElement("span",null,(0,m.__)("Linear Gradient","elementor")):l.createElement("span",null,(0,m.__)("Radial Gradient","elementor")),la=e=>{const t=e.stops?.map((({color:e,offset:t})=>`${e} ${t??0}%`))?.join(",");return"linear"===e.type?`linear-gradient(${e.angle}deg, ${t})`:`radial-gradient(circle at ${e.positions}, ${t})`},ra=(0,_.createTransformer)((e=>l.createElement(c.Stack,{direction:"row",gap:10},l.createElement(oa,{value:e}),l.createElement(aa,{value:e})))),oa=({value:e})=>{const{imageUrl:t}=ia(e);return l.createElement(c.CardMedia,{image:t,sx:e=>({height:"1em",width:"1em",borderRadius:e.shape.borderRadius/2+"px",outline:`1px solid ${e.palette.action.disabled}`})})},aa=({value:e})=>{const{imageTitle:t}=ia(e);return l.createElement(a.EllipsisWithTooltip,{title:t},l.createElement("span",null,t))},ia=e=>{let t,n=null;const l=e?.image.src,{data:r}=(0,S.useWpMediaAttachment)(l.id||null);if(l.id){const e=sa(r?.filename);t=`${r?.title}${e}`||null,n=r?.url||null}else l.url&&(n=l.url,t=n?.substring(n.lastIndexOf("/")+1)||null);return{imageTitle:t,imageUrl:n}},sa=e=>e?`.${e.substring(e.lastIndexOf(".")+1)}`:"",ca=(0,_.createTransformer)((e=>e&&0!==e.length?l.createElement(c.Stack,{direction:"column"},e.map(((e,t)=>l.createElement(c.Stack,{key:t},e)))):null));var ma=()=>{gt()&&function(){const e=_.styleTransformersRegistry.all();Object.entries(e).forEach((([e,t])=>{vt.has(e)||wt.register(e,t)})),wt.registerFallback((0,_.createTransformer)((e=>e))),wt.register("background-color-overlay",qo),wt.register("background-gradient-overlay",ea),wt.register("background-image-overlay",ra),wt.register("background-overlay",ca)}()};function ua(){(0,f.__registerPanel)(po),pa(),(0,h.injectIntoLogic)({id:"editing-panel-hooks",component:vo}),Ko(),ma(),(0,E.isExperimentActive)(C.V_3_30)&&Ho({id:"reset-style-value",useProps:Yo})}var pa=()=>{(0,E.blockCommand)({command:"panel/editor/open",condition:yo})};(window.elementorV2=window.elementorV2||{}).editorEditingPanel=t}(),window.elementorV2.editorEditingPanel?.init?.();