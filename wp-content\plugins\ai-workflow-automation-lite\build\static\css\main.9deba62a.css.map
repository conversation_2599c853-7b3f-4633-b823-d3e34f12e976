{"version": 3, "file": "static/css/main.9deba62a.css", "mappings": "6GAEA,8BACE,+BAAkC,CAClC,uBACF,CAEA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CCjBA,YAGE,gCAAiC,CACjC,gCAAiC,CACjC,sCAAuC,CAEvC,0CAA2C,CAC3C,0CAA2C,CAE3C,mDAAmE,CAEnE,0CAA2C,CAC3C,oDAAmE,CACnE,4CAAmD,CACnD,wCAAyC,CACzC,kDAAmD,CACnD,4CAAmD,CACnD,wCAAyC,CAEzC,mCAA0C,CAC1C,kDAAmD,CACnD,gDAAiD,CACjD,mDAAoD,CAEpD,+BAAgC,CAChC,0CAA2C,CAC3C,uCAAwC,CACxC,kDAAmE,CACnE,yDAAoE,CACpE,wDAAyD,CACzD,mCAAoC,CAEpC,4CAA6C,CAC7C,qCAAsC,CAEtC,iDAA+D,CAC/D,kDAA+D,CAE/D,qDAAsD,CACtD,2DAA4D,CAC5D,0CAA2C,CAC3C,gDAAiD,CACjD,8CAA+C,CAC/C,sDAAiE,CAEjE,6CAAiD,CACjD,qCAAsC,CACtC,4CAA6C,CAxB7C,mDAAgF,CAAhF,8EAAgF,CAvBhF,aAgDF,CACA,iBACE,gCAAiC,CACjC,gCAAiC,CACjC,yCAA0C,CAE1C,4CAA6C,CAC7C,0CAA2C,CAE3C,mDAAoE,CAEpE,6CAA8C,CAC9C,oDAAgE,CAChE,4CAAmD,CACnD,wCAAyC,CACzC,kDAAmD,CACnD,4CAAmD,CACnD,wCAAyC,CAEzC,qCAAsC,CACtC,+CAAgD,CAChD,gDAAiD,CACjD,gDAAiD,CACjD,+BAAgC,CAChC,0CAA2C,CAC3C,0CAA2C,CAC3C,kDAAmE,CACnE,yDAA0E,CAC1E,qDAAsD,CAEtD,4CAA6C,CAC7C,wCAAyC,CAEzC,iDAAkE,CAClE,kDAAkE,CAElE,qDAAsD,CACtD,2DAA4D,CAC5D,0CAA2C,CAC3C,6CAA8C,CAC9C,iDAAkD,CAClD,sDAAiE,CAEjE,gDAAiD,CACjD,qCACF,CACA,wBACE,mDAAkH,CAAlH,+GAAkH,CAClH,mBAAoB,CACpB,UACF,CACA,uBAGE,WAAY,CAEZ,MAAO,CAJP,iBAAkB,CAGlB,KAAM,CAFN,UAIF,CACA,kBACE,SACF,CACA,4BACI,WACF,CACF,2BACI,eACF,CACF,4BACI,cACF,CACF,sBAGE,mBAAoB,CAFpB,oBAAqB,CACrB,SAEF,CACA,sBACE,SACF,CACA,uBACE,SACF,CACA,sFAEE,YACF,CACA,uBACE,oCAA4D,CAA5D,0DAA4D,CAC5D,gDAA8E,CAA9E,4EAA8E,CAC9E,SACF,CACA,6BACE,8CAAgF,CAAhF,8EAAgF,CAChF,0DAAkG,CAAlG,gGAAkG,CAClG,SACF,CACA,+BACE,iBACF,CACA,mCACI,gBAAiB,CAEjB,mBAAoB,CADpB,iBAEF,CACF,kBACE,4BACF,CACA,6BACI,cACF,CACF,gCACI,kBAAmB,CACnB,sCACF,CACF,6DACI,qBAAsB,CACtB,cACF,CACF,2BACI,mBACF,CACF,mFAGI,YACF,CACF,8KAGI,6CAA8E,CAA9E,4EACF,CACF,8BACI,kBACF,CACF,yCACI,mBAAoB,CACpB,wBAAyB,CAEjB,gBACV,CACF,wBACE,mBACF,CACA,kCACI,kBAAmB,CACnB,sCACF,CACF,+BAEE,gBAAiB,CACjB,iBAAkB,CAFlB,YAGF,CACA,mBACE,mBAAoB,CACpB,oBACF,CACA,kBAOE,qBAAsB,CACtB,cAAe,CAHf,kBAAmB,CAJnB,iBAAkB,CAKlB,oBAAqB,CAJrB,wBAAyB,CAEjB,gBAKV,CACA,6BACI,cACF,CACF,4BACI,WAAY,CACZ,kBACF,CACF,qCACM,eACF,CACJ,4BAGE,mBAAoB,CADpB,yBAA0B,CAD1B,SAGF,CACA,iCAGI,WAAY,CADZ,kBAAmB,CADnB,iBAGF,CACF,oBAOE,0DAA8F,CAA9F,4FAA8F,CAC9F,oFAAsF,CACtF,kBAAmB,CAHnB,UAAW,CAFX,cAAe,CADf,aAAc,CADd,mBAAoB,CADpB,iBAAkB,CAIlB,SAKF,CACA,mCACI,kBACF,CACF,wCAEI,gBAAiB,CADjB,kBAEF,CACF,2BAGI,QAAS,CADT,QAAS,CADT,QAAS,CAGT,6BACF,CACF,wBAEI,QAAS,CADT,KAAM,CAEN,8BACF,CACF,yBAEI,MAAO,CADP,OAAQ,CAER,8BACF,CACF,0BAEI,OAAQ,CADR,OAAQ,CAER,6BACF,CACF,yBACE,WAAY,CACZ,kBACF,CACA,mBAGE,WAAY,CAFZ,iBAAkB,CAClB,SAEF,CACA,uBACI,KACF,CACF,0BACI,QACF,CACF,wBACI,MACF,CACF,yBACI,OACF,CACF,0BACI,QAAS,CACT,0BACF,CACF,yBAEE,yDAAkG,CAAlG,gGAAkG,CADlG,cAAe,CAGf,QAAS,CADT,eAEF,CACA,2BAEI,UAAW,CADX,oBAEF,CACF,oBACE,GACE,oBACF,CACF,CACA,gCAIE,mBAMF,CACA,6DARE,WAAY,CAKZ,MAAO,CAPP,iBAAkB,CAQlB,KAAM,CAJN,wBAAyB,CAEjB,gBAAiB,CALzB,UAkBF,CACA,qBACE,qDAGC,CAHD;;GAIF,CACA,yBACI,aACF,CACF,0BACI,oDAGC,CAHD;;KAGC,CACD,kDAGC,CAHD;;KAGC,CACD,wDAGC,CAHD;;KAIF,CACF,0BACI,oDAGC,CAHD;;KAGC,CACD,kDAGC,CAHD;;KAGC,CACD,wDAGC,CAHD;;KAIF,CACF,qCACI,oDAGC,CAHD;;KAIF,CACF,sCACI,uDAGC,CAHD;;KAIF,CACF,sCACI,uDAGC,CAHD;;KAIF,CACF,sBAGE,gDAAgF,CAAhF,8EAAgF,CAFhF,YAAa,CACb,qBAEF,CACA,iCACI,kBACF,CACF,6BAGI,kBAAmB,CAKnB,6DAA0G,CAA1G,wGAA0G,CAD1G,WAAY,CAEZ;;OAIG,CACH,6CAGC,CAHD;;KAGC,CACD,cAAe,CAjBf,YAAa,CAGb,WAAY,CAFZ,sBAAuB,CAIvB,WAAY,CAaZ,wBAAyB,CAEjB,gBAAiB,CAhBzB,UAiBF,CACF,iCAIM,iBAAkB,CADlB,eAAgB,CADhB,cAAe,CADf,UAIF,CACJ,kDACM,WACF,CACJ,uBACI,cACF,CACF,8EAEI,YACF,CAeF,uKACM,iDAAkF,CAAlF,gFACF,CACJ,iiBAYM,oDAAwF,CAAxF,sFACF,CACJ,wBACE,8DAAsG,CAAtG,oGACF,CACA,wDAEE,uDAA8F,CAA9F,4FAA8F,CAC9F,oEACF,CACA,wJAII,YACF,CACF,mCACM,mEAGC,CAHD;;OAGC,CACD,mDAGC,CAHD;;OAIF,CACJ,sCACM,mBACF,CACJ,0CACQ,eACF,CACN,wCACI,kBACF,CACF,4BACE,iBACF,CACA,mEAEE,gBACF,CACA,mEAEE,gBACF,CACA,8EAEE,kBACF,CACA,8EAEE,kBACF,CAEA,mCAKE,0DAA8F,CAA9F,4FAA8F,CAF9F,qBAAsB,CACtB,iBAAkB,CAFlB,UAAW,CAIX,8BAAgC,CALhC,SAMF,CACA,wCACE,MAAO,CACP,OACF,CACA,yCACE,SAAU,CACV,OACF,CACA,uCACE,QAAS,CACT,KACF,CACA,0CACE,QAAS,CACT,QACF,CAIA,2FACE,MACF,CAIA,6FACE,SACF,CAEA,iCACE,wFAA0F,CAE1F,kBAAmB,CADnB,cAEF,CACA,6EAKE,WAAY,CADZ,KAAM,CADN,yBAA6B,CAD7B,SAIF,CACA,sCAEE,qBAAsB,CADtB,MAEF,CACA,uCAEE,sBAAuB,CADvB,SAEF,CACA,6EAEE,UAAW,CAEX,MAAO,CADP,0BAA6B,CAE7B,UACF,CACA,qCAEE,oBAAqB,CADrB,KAEF,CACA,wCACE,uBAAwB,CACxB,QACF,CACA,yBACE,kDAA0F,CAA1F,wFACF,CACA,uBACE,uCAAoE,CAApE,kEACF;;ACvlBA;;;;;EAKE,CACF,cACE,qBAAsB,CACtB,sCAAyC,CACzC,cAAe,CACf,WAAY,CACZ,QAAW,CACX,iBACF,CACA,sCACE,iBACF,CACA,gEACE,mBACF,CACA,cAEE,UAAW,CADX,cAAe,CAEf,iBAAkB,CAClB,iBAAkB,CAClB,OACF,CACA,gBACE,QAAS,CACT,SACF,CACA,WAWE,oBAAqB,CAVrB,qBAAsB,CAEtB,WAAY,CADZ,gBAAiB,CAEjB,YAAa,CACb,eAAgB,CAChB,iBAAkB,CAClB,UAAW,CACX,eAAgB,CAChB,eAAgB,CAChB,oBAEF,CACA,aACE,WACF,CACA,kKAaE,4EAA6E,CAF7E,QAAS,CACT,SAEF,CACA,4BAEE,kBACF,CACA,kCAEE,oBACF,CACA,wBACE,eACF,CACA,mEAEE,mBACF,CACA,6EAEE,kBACF,CACA,uFAEE,UAAW,CACX,cAAe,CACf,kBACF,CACA,2CACE,eACF,CACA,4CACE,eACF,CACA,qBACE,oBAAqB,CACrB,kBAAmB,CACnB,WACF,CACA,4CACE,kBAAmB,CACnB,iBAAmB,CACnB,gBACF,CACA,sCACE,gBAAkB,CAClB,mBACF,CACA,gFAEE,kBACF,CACA,oEAEE,mBACF,CACA,iBAEE,wBAAyB,CADzB,4EAEF,CACA,wBACE,oCACF,CACA,6BACE,wBACF,CACA,oCACE,wCACF,CACA,6BACE,qEACF,CACA,6BACE,wBACF,CACA,oCACE,wCACF,CACA,6BACE,8DACF,CACA,6BACE,wBACF,CACA,oCACE,oCACF,CACA,6BACE,uDACF,CACA,6BACE,wBACF,CACA,oCACE,wCACF,CACA,6BACE,gDACF,CACA,6BACE,wBACF,CACA,oCACE,wCACF,CACA,6BACE,yCACF,CACA,6BACE,wBACF,CACA,oCACE,oCACF,CACA,6BACE,kCACF,CACA,6BACE,wBACF,CACA,oCACE,wCACF,CACA,6BACE,2BACF,CACA,6BACE,wBACF,CACA,oCACE,wCACF,CACA,6BACE,oBACF,CACA,6BACE,wBACF,CACA,oCACE,oCACF,CACA,+CACE,gBACF,CACA,iDACE,kBACF,CACA,wDACE,iBACF,CACA,0DACE,mBACF,CACA,+CACE,gBACF,CACA,iDACE,kBACF,CACA,wDACE,iBACF,CACA,0DACE,mBACF,CACA,+CACE,gBACF,CACA,iDACE,mBACF,CACA,wDACE,iBACF,CACA,0DACE,oBACF,CACA,+CACE,iBACF,CACA,iDACE,mBACF,CACA,wDACE,kBACF,CACA,0DACE,oBACF,CACA,+CACE,iBACF,CACA,iDACE,mBACF,CACA,wDACE,kBACF,CACA,0DACE,oBACF,CACA,+CACE,iBACF,CACA,iDACE,mBACF,CACA,wDACE,kBACF,CACA,0DACE,oBACF,CACA,+CACE,iBACF,CACA,iDACE,mBACF,CACA,wDACE,kBACF,CACA,0DACE,oBACF,CACA,+CACE,iBACF,CACA,iDACE,mBACF,CACA,wDACE,kBACF,CACA,0DACE,oBACF,CACA,+CACE,iBACF,CACA,iDACE,mBACF,CACA,wDACE,kBACF,CACA,0DACE,oBACF,CACA,qBACE,aAAc,CACd,cACF,CACA,qCACE,aACF,CACA,oCACE,iBACF,CACA,wBACE,qBACF,CACA,sBACE,wBACF,CACA,yBACE,qBACF,CACA,yBACE,qBACF,CACA,wBACE,wBACF,CACA,uBACE,qBACF,CACA,yBACE,qBACF,CACA,2BACE,UACF,CACA,yBACE,aACF,CACA,4BACE,UACF,CACA,4BACE,UACF,CACA,2BACE,aACF,CACA,0BACE,UACF,CACA,4BACE,UACF,CACA,0BACE,yCACF,CACA,8BACE,wCACF,CACA,0BACE,eACF,CACA,0BACE,eACF,CACA,yBACE,eACF,CACA,6BACE,aAAc,CACd,kBACF,CACA,4BACE,iBACF,CACA,6BACE,kBACF,CACA,2BACE,gBACF,CACA,2BACE,WAAsB,CACtB,8BAA+B,CAC/B,iBAAkB,CAClB,SAAU,CACV,mBAAoB,CACpB,iBAAkB,CAClB,UACF,CACA,qDAEE,UAAW,CACX,UAAW,CACX,aACF,CACA,uDAEE,eAAgB,CAChB,WAAY,CACZ,cAAe,CACf,oBAAqB,CACrB,UAAW,CACX,WAAY,CACZ,eAAgB,CAChB,UACF,CACA,+DAEE,UAAW,CACX,WACF,CACA,iFAEE,YACF,CACA,6FAEE,YACF,CACA,6jBAcE,UACF,CACA,kgDA4BE,SACF,CACA,kgDA4BE,WACF,CACA,wBACE,mGAEE,UACF,CACA,8PAIE,SACF,CACA,8PAIE,WACF,CACF,CAIA,oBACE,qBACF,CACA,oBACE,YACF,CACA,6CAEE,iBACF,CACA,qBACE,iBAAkB,CAClB,0BACF,CACA,uBACE,cAAe,CACf,oBACF,CACA,6BACE,2BACF,CACA,qBACE,oBAAqB,CACrB,qBACF,CACA,2BACE,UAAW,CACX,UAAW,CACX,aACF,CACA,oBACE,SAAU,CACV,WAAY,CACZ,oBAAqB,CACrB,qBAAsB,CACtB,cACF,CACA,0BACE,SAAU,CACV,WAAY,CACZ,oBAAqB,CACrB,cACF,CACA,8CAEE,SACF,CACA,mBACE,SACF,CACA,kBACE,iBACF,CACA,8CAEE,cACF,CACA,yBACE,UACF,CACA,sCACE,YACF,CACA,gDACE,cACF,CACA,iDACE,YACF,CACA,uBACE,aACF,CACA,uBACE,eACF,CACA,uBACE,gBACF,CACA,uBACE,aACF,CACA,uBACE,eACF,CACA,uBACE,eACF,CACA,sBACE,yBACF,CACA,+BACE,0BAA2B,CAC3B,iBAAkB,CAClB,cAAe,CACf,iBACF,CACA,iDAEE,wBAAyB,CACzB,iBACF,CACA,wBAEE,iBAAkB,CAClB,cAAe,CACf,gBAAiB,CAHjB,oBAIF,CACA,yBACE,aAAc,CACd,eACF,CACA,kCACE,wBAAyB,CACzB,aAAc,CACd,gBACF,CACA,wBACE,cACF,CACA,oBACE,UAAW,CACX,oBAAqB,CACrB,UAAW,CACX,cAAe,CACf,eAAgB,CAChB,WAAY,CACZ,iBAAkB,CAClB,qBACF,CACA,0BACE,cAAe,CACf,oBAAqB,CACrB,WAAY,CACZ,gBAAiB,CACjB,iBAAkB,CAClB,iBAAkB,CAClB,UACF,CACA,iCACE,oBAAqB,CACrB,gBACF,CACA,4BACE,qBAAsB,CACtB,YAAa,CACb,cAAe,CACf,eAAgB,CAChB,iBAAkB,CAClB,kBACF,CACA,4CACE,cAAe,CACf,aAAc,CACd,kBAAmB,CACnB,eACF,CACA,iDACE,UAAW,CACX,SACF,CACA,0DACE,SACF,CACA,4DACE,WACF,CACA,mDACE,aAAc,CACd,eAAgB,CAChB,QAAS,CACT,SACF,CACA,mDAEE,UACF,CACA,qFAEE,eACF,CACA,6FAEE,SACF,CACA,4CACE,aACF,CACA,yCACE,WAAY,CAEZ,eAAgB,CADhB,UAEF,CACA,6CACE,eAAgB,CAChB,WACF,CACA,0CACE,sBAA6B,CAC7B,UAAW,CACX,WAAY,CACZ,UAAW,CACX,SAAY,CACZ,UACF,CACA,mEAEE,eAAgB,CADhB,iBAAkB,CAElB,OAAQ,CACR,OAAQ,CACR,UACF,CACA,+fAME,wBACF,CACA,8BACE,UACF,CACA,2GAEE,gBACF,CACA,2IAEE,mBACF,CACA,2IAEE,mBACF,CACA,2IAEE,mBACF,CACA,2IAEE,mBACF,CACA,2IAEE,mBACF,CACA,2IAEE,mBACF,CACA,qEACE,aACF,CACA,qEACE,eACF,CACA,qEACE,gBACF,CACA,qEACE,aACF,CACA,qEACE,eACF,CACA,qEACE,eACF,CACA,4BACE,WACF,CACA,uGAEE,oBACF,CACA,2IAEE,eACF,CACA,mJAEE,mBACF,CACA,qEACE,yCACF,CACA,yEACE,wCACF,CACA,4BACE,UACF,CACA,uGAEE,gBACF,CACA,2IAEE,eACF,CACA,2IAEE,eACF,CACA,yIAEE,cACF,CACA,qEACE,cACF,CACA,qEACE,cACF,CACA,oEACE,cACF,CACA,wDACE,qBACF,CACA,mDACE,qBACF,CACA,oBACE,qBAAsB,CACtB,qBAAsB,CACtB,qDAA+D,CAC/D,WACF,CACA,gCACE,iBACF,CACA,qCACE,sBACF,CACA,uCACE,sBAA6B,CAC7B,0BACF,CAIA,0HACE,iBACF,CACA,4HAEE,iBACF,CACA,0CACE,YACF,CACA,qBACE,qBAAsB,CACtB,qBAAsB,CACtB,uBAA4B,CAC5B,UAAW,CACX,gBAAiB,CACjB,kBACF,CACA,4BACE,oBAAqB,CACrB,gBAAiB,CACjB,gBACF,CACA,sCAEE,qBAAsB,CADtB,YAAa,CAEb,cAAe,CACf,WAAY,CACZ,QAAW,CACX,eAAgB,CAChB,WACF,CACA,kCACE,oBAAqB,CACrB,eAAgB,CAChB,iBAAkB,CAClB,sBAAuB,CACvB,kBACF,CACA,uCACE,2BAA4B,CAC5B,cAAe,CACf,gBAAiB,CACjB,iBACF,CACA,wCACE,gBAAiB,CACjB,eACF,CACA,uBACE,gBACF,CACA,yFAEE,YACF,CACA,iDACE,oBACF,CACA,kDACE,cAAiB,CACjB,cAAe,CACf,eACF,CACA,4CACE,qBACF,CACA,+CACE,wBACF,CACA,6CACE,sBACF,CACA,WACE,UACF,CACA,sBACE,qBACF,CC/6BA,MACE,0BAA2B,CAC3B,8BACF,CAGA,yBACE,wBACF,CAEA,gBACE,gBACF,CAEA,MACE,QAAS,CACT,SACF,CAGA,sBAEE,yBAAgD,CAAhD,+CAAgD,CADhD,QAAS,CAET,eACF,CAGA,YACE,uCAA+D,CAA/D,6DACF,CAGA,mBAGE,kBAAmB,CADnB,YAAa,CAIb,YAAe,CAFf,wBAGF,CAEA,8BACE,WAAY,CACZ,iBACF,CAEA,6BACE,QACF,CAGA,oBACE,gBACF,CAEA,oBAGE,wBAAyB,CACzB,qBAAuB,CACvB,iBAAkB,CAHlB,WAAY,CADZ,UAKF,CAEA,yBACE,SACF,CAEA,0BACE,UACF,CAEA,kBACE,SACF,CAEA,kBACE,SACF,CAEA,oBACE,SACF,CAEA,UACE,SACF,CAEA,WACE,YACF,CAGA,uBACE,qBAAsB,CACtB,iBAAkB,CAClB,kDACF,CAEA,4BACE,kBACF,CAEA,4BACE,YACF,CAGA,oCACE,MACE,0BACF,CAEA,mBACE,gBACF,CACF,CAEA,oCACE,MACE,uBACF,CAEA,mBACE,YACF,CACF,CAEA,sCACE,gBAAiB,CACjB,eACF,CAEA,yCAEE,eAAgB,CADhB,eAEF,CAEA,OACE,eAAiB,CACjB,iBACF,CAEA,qBACE,gBACF,CAGA,6BASE,kBAAmB,CAEnB,iCAA0B,CAA1B,yBAA0B,CAL1B,0BAAqC,CADrC,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,6BACE,0BAA2C,CAE3C,kBAAmB,CACnB,+BAAyC,CACzC,eAAgB,CAHhB,YAAa,CAKb,iBAAkB,CADlB,SAEF,CAEA,gBACE,oCACF,CAEA,gBACE,YACF,CAEA,4BACE,YACF,CAEA,uBAEE,wBAAyB,CADzB,iBAAkB,CAElB,kBACF,CAEA,6BACE,oBACF,CAEA,6BACE,oBAAqB,CACrB,8BAA6C,CAC7C,YACF,CAEA,mBACE,kBAAmB,CACnB,eACF,CAEA,kBACE,2BACF,CAEA,oBACE,iDAAoD,CACpD,WAAY,CACZ,UAAY,CACZ,kBACF,CAEA,0BAGE,+BAA0C,CAF1C,UAAY,CACZ,0BAEF,CAGA,6BAEE,UACI,8BACJ,CAEA,gBACI,8BACJ,CAGA,wBASI,kBAAmB,CARnB,kBAAmB,CAInB,wBAAyB,CAHzB,iBAAkB,CAKlB,YAAa,CACb,sBAAuB,CAJvB,YAAa,CAEb,gBAAiB,CAHjB,YAOJ,CAGA,kBACI,UACJ,CAGA,qBAEI,kBAAmB,CADnB,YAAa,CAEb,OAAQ,CAER,YACI,cACJ,CAEA,YACI,QACJ,CACJ,CAGA,cACI,kBACJ,CAGA,oBACI,gBACJ,CAGA,OACI,eAAiB,CACjB,wBAAyB,CACzB,iBAAkB,CAClB,iBAAkB,CAElB,YACI,+BAAgC,CAChC,yBACJ,CAEA,cACI,yBACJ,CACJ,CAGA,gBACI,kBAAmB,CAEnB,iBAAkB,CAClB,qBAAsB,CACtB,YAAa,CAHb,WAIJ,CAGA,oBACI,YACJ,CAGA,mBACI,kBAAmB,CAEnB,iBAEI,kBAAmB,CADnB,YAAa,CAEb,6BAA8B,CAC9B,iBACJ,CAEA,iBACI,UACJ,CAEA,gBACI,aAAc,CACd,cAAe,CACf,cACJ,CACJ,CAGA,iBAEI,kBAAmB,CACnB,iBAAkB,CAClB,kBAAmB,CAHnB,WAIJ,CAGA,gBACI,wBAAyB,CACzB,iBAAkB,CAElB,aAAc,CADd,eAAgB,CAGhB,OACI,kBAAmB,CACnB,UACJ,CAEA,gBAEI,kBAAyC,CAAzC,uCAAyC,CACzC,UAAY,CAFZ,WAGJ,CAEA,iBAEI,gBAAiB,CADjB,WAEJ,CAEA,eAEI,4BAA6B,CAD7B,WAEJ,CACJ,CACF,CAGA,mBAEE,cAAe,CADf,eAEF,CAGA,iBACE,gBACI,eAAgB,CAChB,eACJ,CAEA,qBACI,eACJ,CACF,CAEA,oBACE,SAAU,CAEV,kBAAmB,CADnB,sBAEF,CAOA,mGACE,SACF,CAEA,kBACE,cACF,CAEA,2BACE,YACF,CAEA,oBACE,YACF,CAEA,uBACE,sCACF,CC9ZA,gCACI,sBAAuB,CACvB,cAAkB,CAClB,gBAAoB,CACpB,wBAAyB,CACzB,kBAAmB,CACnB,kCACJ,CAEA,qCACI,sBAAuB,CACvB,iBAAkB,CAClB,gBAAoB,CACpB,wBACJ,CAEA,6BAWI,yBAA0B,CAN1B,yBAA0B,CAC1B,gCAAiC,CAMjC,uCAAwC,CALxC,6BAA8B,CAC9B,YAAa,CACb,qBAAsB,CANtB,eAAgB,CAFhB,cAAe,CASf,uBAAyB,CANzB,UAAW,CAFX,YAWJ,CAGA,0CACI,WAAY,CACZ,UACJ,CAEA,yCACI,WAAY,CACZ,SACJ,CAEA,uCAEI,UAAW,CADX,QAEJ,CAEA,sCAEI,SAAU,CADV,QAEJ,CAEA,aAGI,kBAAmB,CAEnB,8BAA+B,CAE/B,gDAAiD,CACjD,iDAAkD,CAFlD,UAAc,CALd,YAAa,CACb,6BAA8B,CAE9B,iBAKJ,CAEA,uDACI,cACJ,CAGA,gBAEI,kBAAmB,CADnB,YAAa,CAEb,OACJ,CAEA,UACI,eACJ,CAEA,gBAEI,kBAAmB,CADnB,YAAa,CAEb,OACJ,CAEA,eAII,YAAa,CAHb,QAAO,CAIP,qBAAsB,CACtB,QAAS,CAJT,eAAgB,CAChB,YAIJ,CAEA,cACI,YAAa,CACb,iBACJ,CAEA,+BACI,+CACJ,CAEA,mBACI,wBACJ,CAEA,iBAII,oBAAqB,CAFrB,kBAAmB,CACnB,aAAc,CAFd,gBAIJ,CAEA,oCACI,8BAA+B,CAC/B,UACJ,CAEA,yCACI,gCAAiC,CACjC,sBACJ,CAEA,YAEI,0CAA2C,CAC3C,YAAa,CACb,OAAQ,CAHR,YAIJ,CAEA,kBAMI,yBAA0B,CAH1B,sCAAuC,CACvC,iBAAkB,CAGlB,sBAAuB,CANvB,QAAO,CAIP,YAAa,CAHb,gBAMJ,CAEA,mBAEI,8BAA+B,CAE/B,WAAY,CACZ,iBAAkB,CAFlB,UAAc,CAGd,cAAe,CALf,gBAAiB,CAMjB,sBACJ,CAEA,yBACI,UACJ,CAGA,eAUI,kBAAmB,CAJnB,8BAA+B,CAD/B,kBAAmB,CAOnB,6BAA8B,CAL9B,UAAc,CACd,cAAe,CACf,YAAa,CALb,WAAY,CAOZ,sBAAuB,CAVvB,cAAe,CAYf,6BAA+B,CAV/B,UAAW,CADX,YAYJ,CAEA,qBACI,qBACJ,CAEA,4BACI,WAAY,CACZ,UACJ,CAEA,2BACI,WAAY,CACZ,SACJ,CAEA,yBAEI,UAAW,CADX,QAEJ,CAEA,wBAEI,SAAU,CADV,QAEJ,CAGA,yBACI,0CAII,WAAY,CADZ,WAAY,CAGZ,6BAA8B,CAJ9B,cAAe,CAGf,gBAAiB,CAJjB,uBAMJ,CAGA,0CACI,QAAS,CACT,OACJ,CAEA,yCACI,QAAS,CACT,MACJ,CAEA,uCAEI,OAAQ,CADR,KAEJ,CAEA,sCAEI,MAAO,CADP,KAEJ,CAEA,aACI,gDAAiD,CACjD,iDACJ,CACJ,CAEA,yBACI,0CAKI,eAAgB,CAHhB,WAAY,CACZ,QAAS,CACT,gBAAiB,CAHjB,UAKJ,CAEA,uDACI,eACJ,CACJ,CAGA,mBACI,SAAU,CACV,0BACJ,CAEA,0BAGI,8CACJ,CAEA,4CALI,SAAU,CACV,uBAOJ,CAEA,yBACI,SAAU,CACV,0BAA2B,CAC3B,8CACJ,CAGA,kBAII,gCAAiC,CACjC,kBAAmB,CAJnB,YAAa,CACb,OAAQ,CACR,gBAAiB,CAGjB,yBAAkB,CAAlB,sBAAkB,CAAlB,iBACJ,CAEA,YAMI,uCAAwC,CAHxC,2BAA4B,CAC5B,iBAAkB,CAFlB,UAAW,CAGX,UAAY,CAJZ,SAMJ,CAEA,yBACI,mBACJ,CAEA,yBACI,mBACJ,CAEA,2BACI,MACI,uBACJ,CACA,IACI,0BACJ,CACJ,CAGA,4BAQE,kBAAmB,CANnB,gBAAuB,CACvB,WAAY,CACZ,WAA+B,CAC/B,cAAe,CAEf,YAAa,CAEb,sBAAuB,CAHvB,WAAY,CAIZ,yBACF,CAEA,wCAEE,UACF,CAGA,eAEI,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAClB,aAAc,CACd,YAAa,CALb,gBAMJ,CAGA,kCACI,SACJ,CAEA,wCACI,gBACJ,CAEA,wCACI,gBAA8B,CAC9B,iBACJ,CAEA,8CACI,gBACJ,CAGA,qBACI,oBAA+B,CAE/B,iBAAkB,CAClB,eAAgB,CAFhB,WAGJ,CAEA,2BACI,oBACJ,CAGA,mBACI,cACJ,CAEA,8BACI,eACJ,CAEA,wCAEI,YAAa,CACb,iBACJ,CAEA,sBACI,oBAA+B,CAE/B,iBAAkB,CAClB,qBAAsB,CAFtB,eAGJ,CAEA,4BACI,oBACJ,CAGA,kCACI,iBAAkB,CAClB,gBAAoB,CACpB,wBAAyB,CACzB,2BACJ,CAGA,6BACI,mEACJ,CAEA,+BACI,mDACJ,CAGA,kBACI,uCAAwC,CACxC,iBAAkB,CAIlB,cAAe,CADf,YAAa,CAFb,YAAa,CACb,iBAAkB,CAGlB,2BACJ,CAMA,mDAHI,gCAMJ,CAHA,2BACI,2CAEJ,CAGA,SAOI,kBAAsB,CACtB,QAAS,CALT,UAAW,CAEX,WAAY,CACZ,eAAgB,CAFhB,SAAU,CAHV,iBAAkB,CAClB,SAOJ,CAGA,qEAGI,qCAAsC,CACtC,kBACJ,CAGA,uCACI,WAAY,CACZ,YAAiB,CAAjB,eACJ,CAEA,yGAEI,YACJ,CAEA,yCAMI,4BAAoD,CAApD,kDAAoD,CADpD,aAAgC,CAAhC,8BAAgC,CAFhC,aAAc,CACd,UAAY,CAFZ,WAAY,CADZ,iBAAkB,CAMlB,2BACJ,CAEA,2CAII,kBAAmB,CAHnB,aAAc,CAEd,mBAAoB,CAEpB,OAAQ,CAHR,oBAIJ,CAEA,iDACI,SAAU,CACV,yBACJ,CAGA,8CACI,UACJ,CAEA,gDACI,WACJ,CAGA,8FAEI,eAAgB,CAChB,eACJ,CAKA,oCASI,qBAAyC,CAAzC,oCAAyC,CAFzC,wBAAgD,CAAhD,8CAAgD,CAChD,kBAA8C,CAA9C,4CAA8C,CAE9C,yBAA2B,CAC3B,mCAAoC,CACpC,+BAAgC,CARhC,qBAAuB,CAEvB,aAAc,CAHd,cAAe,CAEf,gBAAiB,CAJjB,iBAAkB,CAClB,UAWJ,CAGA,iDAII,wBAA8C,CAA9C,4CAA8C,CAF9C,2BAAuD,CAAvD,qDAAuD,CACvD,4BAAwD,CAAxD,sDAAwD,CAExD,iBAAkB,CAJlB,iBAAkB,CAKlB,SACJ,CAGA,mDAII,qBAAyC,CAAzC,oCAAyC,CAHzC,YAAa,CACb,8BAA+B,CAC/B,eAAgB,CAEhB,YACJ,CAGA,gDAKI,qBAAyC,CAAzC,oCAAyC,CACzC,8BAA0D,CAA1D,wDAA0D,CAC1D,+BAA2D,CAA3D,yDAA2D,CAC3D,4BAAoD,CAApD,kDAAoD,CANpD,QAAS,CACT,MAAO,CAMP,YAAa,CARb,iBAAkB,CAGlB,OAMJ,CAGA,yCACI,wBAAyB,CACzB,iBACJ,CAEA,6GAEI,wBACJ,CAGA,2CACI,+BAAgC,CAChC,kCACJ,CAEA,iHAEI,+BACJ,CAEA,gDACI,kBAAmB,CAMnB,mDAAoD,CACpD,oDAAqD,CANrD,8BAAwC,CAGxC,aAAc,CAFd,QAAS,CAGT,UAAY,CAFZ,WAKJ,CAGA,qDACI,0BACJ,CAGA,uDACI,sCACJ,CAGA,kDACI,aAAc,CACd,UACJ,CAGA,4JAGI,yBAA2B,CAC3B,uBACJ,CAIA,oCACI,oCACI,aAAc,CACd,gBACJ,CAEA,mDACI,YAAa,CACb,8BACJ,CAEA,iDACI,gBACJ,CAEA,gDACI,YACJ,CACJ,CAEA,oCACI,oCACI,YAAa,CACb,gBACJ,CAEA,mDACI,YAAa,CACb,YACJ,CAEA,8CACI,cACJ,CAEA,qDACI,aAAc,CACd,gBACJ,CACJ,CAGA,sCACI,iBACJ,CAEA,iDACI,sCACJ,CAEA,yBACI,0CACI,cACJ,CACJ", "sources": ["index.css", "../node_modules/@xyflow/react/dist/style.css", "../node_modules/react-quill/dist/quill.snow.css", "styles/CustomNodeStyles.css", "styles/chat.css"], "sourcesContent": ["@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:ital,wght@0,400;0,700;1,400&display=swap');\n\n.sidebar-upgrade-button:hover {\n  background-color: white !important;\n  color: #764ba2 !important;\n}\n\nbody {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n", "/* this gets exported as style.css and can be used for the default theming */\n/* these are the necessary styles for React/Svelte Flow, they get used by base.css and style.css */\n.react-flow {\n  direction: ltr;\n\n  --xy-edge-stroke-default: #b1b1b7;\n  --xy-edge-stroke-width-default: 2;\n  --xy-edge-stroke-selected-default: #555;\n\n  --xy-connectionline-stroke-default: #b1b1b7;\n  --xy-connectionline-stroke-width-default: 2;\n\n  --xy-attribution-background-color-default: rgba(255, 255, 255, 0.5);\n\n  --xy-minimap-background-color-default: #fff;\n  --xy-minimap-mask-background-color-default: rgb(240, 240, 240, 0.6);\n  --xy-minimap-mask-stroke-color-default: transparent;\n  --xy-minimap-mask-stroke-width-default: 1;\n  --xy-minimap-node-background-color-default: #e2e2e2;\n  --xy-minimap-node-stroke-color-default: transparent;\n  --xy-minimap-node-stroke-width-default: 2;\n\n  --xy-background-color-default: transparent;\n  --xy-background-pattern-dots-color-default: #91919a;\n  --xy-background-pattern-lines-color-default: #eee;\n  --xy-background-pattern-cross-color-default: #e2e2e2;\n  background-color: var(--xy-background-color, var(--xy-background-color-default));\n  --xy-node-color-default: inherit;\n  --xy-node-border-default: 1px solid #1a192b;\n  --xy-node-background-color-default: #fff;\n  --xy-node-group-background-color-default: rgba(240, 240, 240, 0.25);\n  --xy-node-boxshadow-hover-default: 0 1px 4px 1px rgba(0, 0, 0, 0.08);\n  --xy-node-boxshadow-selected-default: 0 0 0 0.5px #1a192b;\n  --xy-node-border-radius-default: 3px;\n\n  --xy-handle-background-color-default: #1a192b;\n  --xy-handle-border-color-default: #fff;\n\n  --xy-selection-background-color-default: rgba(0, 89, 220, 0.08);\n  --xy-selection-border-default: 1px dotted rgba(0, 89, 220, 0.8);\n\n  --xy-controls-button-background-color-default: #fefefe;\n  --xy-controls-button-background-color-hover-default: #f4f4f4;\n  --xy-controls-button-color-default: inherit;\n  --xy-controls-button-color-hover-default: inherit;\n  --xy-controls-button-border-color-default: #eee;\n  --xy-controls-box-shadow-default: 0 0 2px 1px rgba(0, 0, 0, 0.08);\n\n  --xy-edge-label-background-color-default: #ffffff;\n  --xy-edge-label-color-default: inherit;\n  --xy-resize-background-color-default: #3367d9;\n}\n.react-flow.dark {\n  --xy-edge-stroke-default: #c083f3;\n  --xy-edge-stroke-width-default: 2;\n  --xy-edge-stroke-selected-default: #ab54f3;\n\n  --xy-connectionline-stroke-default: #c183f3d0;\n  --xy-connectionline-stroke-width-default: 2;\n\n  --xy-attribution-background-color-default: rgba(150, 150, 150, 0.25);\n\n  --xy-minimap-background-color-default: #141414;\n  --xy-minimap-mask-background-color-default: rgb(60, 60, 60, 0.6);\n  --xy-minimap-mask-stroke-color-default: transparent;\n  --xy-minimap-mask-stroke-width-default: 1;\n  --xy-minimap-node-background-color-default: #2b2b2b;\n  --xy-minimap-node-stroke-color-default: transparent;\n  --xy-minimap-node-stroke-width-default: 2;\n\n  --xy-background-color-default: #141414;\n  --xy-background-pattern-dots-color-default: #777;\n  --xy-background-pattern-lines-color-default: #777;\n  --xy-background-pattern-cross-color-default: #777;\n  --xy-node-color-default: #f8f8f8;\n  --xy-node-border-default: 1px solid #3c3c3c;\n  --xy-node-background-color-default: #1e1e1e;\n  --xy-node-group-background-color-default: rgba(240, 240, 240, 0.25);\n  --xy-node-boxshadow-hover-default: 0 1px 4px 1px rgba(255, 255, 255, 0.08);\n  --xy-node-boxshadow-selected-default: 0 0 0 0.5px #999;\n\n  --xy-handle-background-color-default: #bebebe;\n  --xy-handle-border-color-default: #1e1e1e;\n\n  --xy-selection-background-color-default: rgba(200, 200, 220, 0.08);\n  --xy-selection-border-default: 1px dotted rgba(200, 200, 220, 0.8);\n\n  --xy-controls-button-background-color-default: #2b2b2b;\n  --xy-controls-button-background-color-hover-default: #3e3e3e;\n  --xy-controls-button-color-default: #f8f8f8;\n  --xy-controls-button-color-hover-default: #fff;\n  --xy-controls-button-border-color-default: #5b5b5b;\n  --xy-controls-box-shadow-default: 0 0 2px 1px rgba(0, 0, 0, 0.08);\n\n  --xy-edge-label-background-color-default: #141414;\n  --xy-edge-label-color-default: #f8f8f8;\n}\n.react-flow__background {\n  background-color: var(--xy-background-color, var(--xy-background-color-props, var(--xy-background-color-default)));\n  pointer-events: none;\n  z-index: -1;\n}\n.react-flow__container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n}\n.react-flow__pane {\n  z-index: 1;\n}\n.react-flow__pane.draggable {\n    cursor: grab;\n  }\n.react-flow__pane.dragging {\n    cursor: grabbing;\n  }\n.react-flow__pane.selection {\n    cursor: pointer;\n  }\n.react-flow__viewport {\n  transform-origin: 0 0;\n  z-index: 2;\n  pointer-events: none;\n}\n.react-flow__renderer {\n  z-index: 4;\n}\n.react-flow__selection {\n  z-index: 6;\n}\n.react-flow__nodesselection-rect:focus,\n.react-flow__nodesselection-rect:focus-visible {\n  outline: none;\n}\n.react-flow__edge-path {\n  stroke: var(--xy-edge-stroke, var(--xy-edge-stroke-default));\n  stroke-width: var(--xy-edge-stroke-width, var(--xy-edge-stroke-width-default));\n  fill: none;\n}\n.react-flow__connection-path {\n  stroke: var(--xy-connectionline-stroke, var(--xy-connectionline-stroke-default));\n  stroke-width: var(--xy-connectionline-stroke-width, var(--xy-connectionline-stroke-width-default));\n  fill: none;\n}\n.react-flow .react-flow__edges {\n  position: absolute;\n}\n.react-flow .react-flow__edges svg {\n    overflow: visible;\n    position: absolute;\n    pointer-events: none;\n  }\n.react-flow__edge {\n  pointer-events: visibleStroke;\n}\n.react-flow__edge.selectable {\n    cursor: pointer;\n  }\n.react-flow__edge.animated path {\n    stroke-dasharray: 5;\n    animation: dashdraw 0.5s linear infinite;\n  }\n.react-flow__edge.animated path.react-flow__edge-interaction {\n    stroke-dasharray: none;\n    animation: none;\n  }\n.react-flow__edge.inactive {\n    pointer-events: none;\n  }\n.react-flow__edge.selected,\n  .react-flow__edge:focus,\n  .react-flow__edge:focus-visible {\n    outline: none;\n  }\n.react-flow__edge.selected .react-flow__edge-path,\n  .react-flow__edge.selectable:focus .react-flow__edge-path,\n  .react-flow__edge.selectable:focus-visible .react-flow__edge-path {\n    stroke: var(--xy-edge-stroke-selected, var(--xy-edge-stroke-selected-default));\n  }\n.react-flow__edge-textwrapper {\n    pointer-events: all;\n  }\n.react-flow__edge .react-flow__edge-text {\n    pointer-events: none;\n    -webkit-user-select: none;\n       -moz-user-select: none;\n            user-select: none;\n  }\n.react-flow__connection {\n  pointer-events: none;\n}\n.react-flow__connection .animated {\n    stroke-dasharray: 5;\n    animation: dashdraw 0.5s linear infinite;\n  }\nsvg.react-flow__connectionline {\n  z-index: 1001;\n  overflow: visible;\n  position: absolute;\n}\n.react-flow__nodes {\n  pointer-events: none;\n  transform-origin: 0 0;\n}\n.react-flow__node {\n  position: absolute;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  pointer-events: all;\n  transform-origin: 0 0;\n  box-sizing: border-box;\n  cursor: default;\n}\n.react-flow__node.selectable {\n    cursor: pointer;\n  }\n.react-flow__node.draggable {\n    cursor: grab;\n    pointer-events: all;\n  }\n.react-flow__node.draggable.dragging {\n      cursor: grabbing;\n    }\n.react-flow__nodesselection {\n  z-index: 3;\n  transform-origin: left top;\n  pointer-events: none;\n}\n.react-flow__nodesselection-rect {\n    position: absolute;\n    pointer-events: all;\n    cursor: grab;\n  }\n.react-flow__handle {\n  position: absolute;\n  pointer-events: none;\n  min-width: 5px;\n  min-height: 5px;\n  width: 6px;\n  height: 6px;\n  background-color: var(--xy-handle-background-color, var(--xy-handle-background-color-default));\n  border: 1px solid var(--xy-handle-border-color, var(--xy-handle-border-color-default));\n  border-radius: 100%;\n}\n.react-flow__handle.connectingfrom {\n    pointer-events: all;\n  }\n.react-flow__handle.connectionindicator {\n    pointer-events: all;\n    cursor: crosshair;\n  }\n.react-flow__handle-bottom {\n    top: auto;\n    left: 50%;\n    bottom: 0;\n    transform: translate(-50%, 50%);\n  }\n.react-flow__handle-top {\n    top: 0;\n    left: 50%;\n    transform: translate(-50%, -50%);\n  }\n.react-flow__handle-left {\n    top: 50%;\n    left: 0;\n    transform: translate(-50%, -50%);\n  }\n.react-flow__handle-right {\n    top: 50%;\n    right: 0;\n    transform: translate(50%, -50%);\n  }\n.react-flow__edgeupdater {\n  cursor: move;\n  pointer-events: all;\n}\n.react-flow__panel {\n  position: absolute;\n  z-index: 5;\n  margin: 15px;\n}\n.react-flow__panel.top {\n    top: 0;\n  }\n.react-flow__panel.bottom {\n    bottom: 0;\n  }\n.react-flow__panel.left {\n    left: 0;\n  }\n.react-flow__panel.right {\n    right: 0;\n  }\n.react-flow__panel.center {\n    left: 50%;\n    transform: translateX(-50%);\n  }\n.react-flow__attribution {\n  font-size: 10px;\n  background: var(--xy-attribution-background-color, var(--xy-attribution-background-color-default));\n  padding: 2px 3px;\n  margin: 0;\n}\n.react-flow__attribution a {\n    text-decoration: none;\n    color: #999;\n  }\n@keyframes dashdraw {\n  from {\n    stroke-dashoffset: 10;\n  }\n}\n.react-flow__edgelabel-renderer {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  left: 0;\n  top: 0;\n}\n.react-flow__viewport-portal {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  left: 0;\n  top: 0;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\n.react-flow__minimap {\n  background: var(\n    --xy-minimap-background-color-props,\n    var(--xy-minimap-background-color, var(--xy-minimap-background-color-default))\n  );\n}\n.react-flow__minimap-svg {\n    display: block;\n  }\n.react-flow__minimap-mask {\n    fill: var(\n      --xy-minimap-mask-background-color-props,\n      var(--xy-minimap-mask-background-color, var(--xy-minimap-mask-background-color-default))\n    );\n    stroke: var(\n      --xy-minimap-mask-stroke-color-props,\n      var(--xy-minimap-mask-stroke-color, var(--xy-minimap-mask-stroke-color-default))\n    );\n    stroke-width: var(\n      --xy-minimap-mask-stroke-width-props,\n      var(--xy-minimap-mask-stroke-width, var(--xy-minimap-mask-stroke-width-default))\n    );\n  }\n.react-flow__minimap-node {\n    fill: var(\n      --xy-minimap-node-background-color-props,\n      var(--xy-minimap-node-background-color, var(--xy-minimap-node-background-color-default))\n    );\n    stroke: var(\n      --xy-minimap-node-stroke-color-props,\n      var(--xy-minimap-node-stroke-color, var(--xy-minimap-node-stroke-color-default))\n    );\n    stroke-width: var(\n      --xy-minimap-node-stroke-width-props,\n      var(--xy-minimap-node-stroke-width, var(--xy-minimap-node-stroke-width-default))\n    );\n  }\n.react-flow__background-pattern.dots {\n    fill: var(\n      --xy-background-pattern-color-props,\n      var(--xy-background-pattern-color, var(--xy-background-pattern-dots-color-default))\n    );\n  }\n.react-flow__background-pattern.lines {\n    stroke: var(\n      --xy-background-pattern-color-props,\n      var(--xy-background-pattern-color, var(--xy-background-pattern-lines-color-default))\n    );\n  }\n.react-flow__background-pattern.cross {\n    stroke: var(\n      --xy-background-pattern-color-props,\n      var(--xy-background-pattern-color, var(--xy-background-pattern-cross-color-default))\n    );\n  }\n.react-flow__controls {\n  display: flex;\n  flex-direction: column;\n  box-shadow: var(--xy-controls-box-shadow, var(--xy-controls-box-shadow-default));\n}\n.react-flow__controls.horizontal {\n    flex-direction: row;\n  }\n.react-flow__controls-button {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    height: 26px;\n    width: 26px;\n    padding: 4px;\n    border: none;\n    background: var(--xy-controls-button-background-color, var(--xy-controls-button-background-color-default));\n    border-bottom: 1px solid\n      var(\n        --xy-controls-button-border-color-props,\n        var(--xy-controls-button-border-color, var(--xy-controls-button-border-color-default))\n      );\n    color: var(\n      --xy-controls-button-color-props,\n      var(--xy-controls-button-color, var(--xy-controls-button-color-default))\n    );\n    cursor: pointer;\n    -webkit-user-select: none;\n       -moz-user-select: none;\n            user-select: none;\n  }\n.react-flow__controls-button svg {\n      width: 100%;\n      max-width: 12px;\n      max-height: 12px;\n      fill: currentColor;\n    }\n.react-flow__edge.updating .react-flow__edge-path {\n      stroke: #777;\n    }\n.react-flow__edge-text {\n    font-size: 10px;\n  }\n.react-flow__node.selectable:focus,\n  .react-flow__node.selectable:focus-visible {\n    outline: none;\n  }\n/*\n.react-flow__node-input,\n.react-flow__node-default,\n/*.react-flow__node-output,\n.react-flow__node-group {\n  padding: 10px;\n  border-radius: var(--xy-node-border-radius, var(--xy-node-border-radius-default));\n  width: 150px;\n  font-size: 12px;\n  color: var(--xy-node-color, var(--xy-node-color-default));\n  text-align: center;\n  border: var(--xy-node-border, var(--xy-node-border-default));\n  background-color: var(--xy-node-background-color, var(--xy-node-background-color-default));\n}*/\n.react-flow__node-input.selectable:hover, .react-flow__node-default.selectable:hover, .react-flow__node-output.selectable:hover, .react-flow__node-group.selectable:hover {\n      box-shadow: var(--xy-node-boxshadow-hover, var(--xy-node-boxshadow-hover-default));\n    }\n.react-flow__node-input.selectable.selected,\n    .react-flow__node-input.selectable:focus,\n    .react-flow__node-input.selectable:focus-visible,\n    .react-flow__node-default.selectable.selected,\n    .react-flow__node-default.selectable:focus,\n    .react-flow__node-default.selectable:focus-visible,\n    .react-flow__node-output.selectable.selected,\n    .react-flow__node-output.selectable:focus,\n    .react-flow__node-output.selectable:focus-visible,\n    .react-flow__node-group.selectable.selected,\n    .react-flow__node-group.selectable:focus,\n    .react-flow__node-group.selectable:focus-visible {\n      box-shadow: var(--xy-node-boxshadow-selected, var(--xy-node-boxshadow-selected-default));\n    }\n.react-flow__node-group {\n  background-color: var(--xy-node-group-background-color, var(--xy-node-group-background-color-default));\n}\n.react-flow__nodesselection-rect,\n.react-flow__selection {\n  background: var(--xy-selection-background-color, var(--xy-selection-background-color-default));\n  border: var(--xy-selection-border, var(--xy-selection-border-default));\n}\n.react-flow__nodesselection-rect:focus,\n  .react-flow__nodesselection-rect:focus-visible,\n  .react-flow__selection:focus,\n  .react-flow__selection:focus-visible {\n    outline: none;\n  }\n.react-flow__controls-button:hover {\n      background: var(\n        --xy-controls-button-background-color-hover-props,\n        var(--xy-controls-button-background-color-hover, var(--xy-controls-button-background-color-hover-default))\n      );\n      color: var(\n        --xy-controls-button-color-hover-props,\n        var(--xy-controls-button-color-hover, var(--xy-controls-button-color-hover-default))\n      );\n    }\n.react-flow__controls-button:disabled {\n      pointer-events: none;\n    }\n.react-flow__controls-button:disabled svg {\n        fill-opacity: 0.4;\n      }\n.react-flow__controls-button:last-child {\n    border-bottom: none;\n  }\n.react-flow__resize-control {\n  position: absolute;\n}\n.react-flow__resize-control.left,\n.react-flow__resize-control.right {\n  cursor: ew-resize;\n}\n.react-flow__resize-control.top,\n.react-flow__resize-control.bottom {\n  cursor: ns-resize;\n}\n.react-flow__resize-control.top.left,\n.react-flow__resize-control.bottom.right {\n  cursor: nwse-resize;\n}\n.react-flow__resize-control.bottom.left,\n.react-flow__resize-control.top.right {\n  cursor: nesw-resize;\n}\n/* handle styles */\n.react-flow__resize-control.handle {\n  width: 4px;\n  height: 4px;\n  border: 1px solid #fff;\n  border-radius: 1px;\n  background-color: var(--xy-resize-background-color, var(--xy-resize-background-color-default));\n  transform: translate(-50%, -50%);\n}\n.react-flow__resize-control.handle.left {\n  left: 0;\n  top: 50%;\n}\n.react-flow__resize-control.handle.right {\n  left: 100%;\n  top: 50%;\n}\n.react-flow__resize-control.handle.top {\n  left: 50%;\n  top: 0;\n}\n.react-flow__resize-control.handle.bottom {\n  left: 50%;\n  top: 100%;\n}\n.react-flow__resize-control.handle.top.left {\n  left: 0;\n}\n.react-flow__resize-control.handle.bottom.left {\n  left: 0;\n}\n.react-flow__resize-control.handle.top.right {\n  left: 100%;\n}\n.react-flow__resize-control.handle.bottom.right {\n  left: 100%;\n}\n/* line styles */\n.react-flow__resize-control.line {\n  border-color: var(--xy-resize-background-color, var(--xy-resize-background-color-default));\n  border-width: 0;\n  border-style: solid;\n}\n.react-flow__resize-control.line.left,\n.react-flow__resize-control.line.right {\n  width: 1px;\n  transform: translate(-50%, 0);\n  top: 0;\n  height: 100%;\n}\n.react-flow__resize-control.line.left {\n  left: 0;\n  border-left-width: 1px;\n}\n.react-flow__resize-control.line.right {\n  left: 100%;\n  border-right-width: 1px;\n}\n.react-flow__resize-control.line.top,\n.react-flow__resize-control.line.bottom {\n  height: 1px;\n  transform: translate(0, -50%);\n  left: 0;\n  width: 100%;\n}\n.react-flow__resize-control.line.top {\n  top: 0;\n  border-top-width: 1px;\n}\n.react-flow__resize-control.line.bottom {\n  border-bottom-width: 1px;\n  top: 100%;\n}\n.react-flow__edge-textbg {\n  fill: var(--xy-edge-label-background-color, var(--xy-edge-label-background-color-default));\n}\n.react-flow__edge-text {\n  fill: var(--xy-edge-label-color, var(--xy-edge-label-color-default));\n}\n", "/*!\n * Quill Editor v1.3.7\n * https://quilljs.com/\n * Copyright (c) 2014, <PERSON>\n * Copyright (c) 2013, salesforce.com\n */\n.ql-container {\n  box-sizing: border-box;\n  font-family: Helvetica, Arial, sans-serif;\n  font-size: 13px;\n  height: 100%;\n  margin: 0px;\n  position: relative;\n}\n.ql-container.ql-disabled .ql-tooltip {\n  visibility: hidden;\n}\n.ql-container.ql-disabled .ql-editor ul[data-checked] > li::before {\n  pointer-events: none;\n}\n.ql-clipboard {\n  left: -100000px;\n  height: 1px;\n  overflow-y: hidden;\n  position: absolute;\n  top: 50%;\n}\n.ql-clipboard p {\n  margin: 0;\n  padding: 0;\n}\n.ql-editor {\n  box-sizing: border-box;\n  line-height: 1.42;\n  height: 100%;\n  outline: none;\n  overflow-y: auto;\n  padding: 12px 15px;\n  tab-size: 4;\n  -moz-tab-size: 4;\n  text-align: left;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n}\n.ql-editor > * {\n  cursor: text;\n}\n.ql-editor p,\n.ql-editor ol,\n.ql-editor ul,\n.ql-editor pre,\n.ql-editor blockquote,\n.ql-editor h1,\n.ql-editor h2,\n.ql-editor h3,\n.ql-editor h4,\n.ql-editor h5,\n.ql-editor h6 {\n  margin: 0;\n  padding: 0;\n  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n}\n.ql-editor ol,\n.ql-editor ul {\n  padding-left: 1.5em;\n}\n.ql-editor ol > li,\n.ql-editor ul > li {\n  list-style-type: none;\n}\n.ql-editor ul > li::before {\n  content: '\\2022';\n}\n.ql-editor ul[data-checked=true],\n.ql-editor ul[data-checked=false] {\n  pointer-events: none;\n}\n.ql-editor ul[data-checked=true] > li *,\n.ql-editor ul[data-checked=false] > li * {\n  pointer-events: all;\n}\n.ql-editor ul[data-checked=true] > li::before,\n.ql-editor ul[data-checked=false] > li::before {\n  color: #777;\n  cursor: pointer;\n  pointer-events: all;\n}\n.ql-editor ul[data-checked=true] > li::before {\n  content: '\\2611';\n}\n.ql-editor ul[data-checked=false] > li::before {\n  content: '\\2610';\n}\n.ql-editor li::before {\n  display: inline-block;\n  white-space: nowrap;\n  width: 1.2em;\n}\n.ql-editor li:not(.ql-direction-rtl)::before {\n  margin-left: -1.5em;\n  margin-right: 0.3em;\n  text-align: right;\n}\n.ql-editor li.ql-direction-rtl::before {\n  margin-left: 0.3em;\n  margin-right: -1.5em;\n}\n.ql-editor ol li:not(.ql-direction-rtl),\n.ql-editor ul li:not(.ql-direction-rtl) {\n  padding-left: 1.5em;\n}\n.ql-editor ol li.ql-direction-rtl,\n.ql-editor ul li.ql-direction-rtl {\n  padding-right: 1.5em;\n}\n.ql-editor ol li {\n  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n  counter-increment: list-0;\n}\n.ql-editor ol li:before {\n  content: counter(list-0, decimal) '. ';\n}\n.ql-editor ol li.ql-indent-1 {\n  counter-increment: list-1;\n}\n.ql-editor ol li.ql-indent-1:before {\n  content: counter(list-1, lower-alpha) '. ';\n}\n.ql-editor ol li.ql-indent-1 {\n  counter-reset: list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n}\n.ql-editor ol li.ql-indent-2 {\n  counter-increment: list-2;\n}\n.ql-editor ol li.ql-indent-2:before {\n  content: counter(list-2, lower-roman) '. ';\n}\n.ql-editor ol li.ql-indent-2 {\n  counter-reset: list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n}\n.ql-editor ol li.ql-indent-3 {\n  counter-increment: list-3;\n}\n.ql-editor ol li.ql-indent-3:before {\n  content: counter(list-3, decimal) '. ';\n}\n.ql-editor ol li.ql-indent-3 {\n  counter-reset: list-4 list-5 list-6 list-7 list-8 list-9;\n}\n.ql-editor ol li.ql-indent-4 {\n  counter-increment: list-4;\n}\n.ql-editor ol li.ql-indent-4:before {\n  content: counter(list-4, lower-alpha) '. ';\n}\n.ql-editor ol li.ql-indent-4 {\n  counter-reset: list-5 list-6 list-7 list-8 list-9;\n}\n.ql-editor ol li.ql-indent-5 {\n  counter-increment: list-5;\n}\n.ql-editor ol li.ql-indent-5:before {\n  content: counter(list-5, lower-roman) '. ';\n}\n.ql-editor ol li.ql-indent-5 {\n  counter-reset: list-6 list-7 list-8 list-9;\n}\n.ql-editor ol li.ql-indent-6 {\n  counter-increment: list-6;\n}\n.ql-editor ol li.ql-indent-6:before {\n  content: counter(list-6, decimal) '. ';\n}\n.ql-editor ol li.ql-indent-6 {\n  counter-reset: list-7 list-8 list-9;\n}\n.ql-editor ol li.ql-indent-7 {\n  counter-increment: list-7;\n}\n.ql-editor ol li.ql-indent-7:before {\n  content: counter(list-7, lower-alpha) '. ';\n}\n.ql-editor ol li.ql-indent-7 {\n  counter-reset: list-8 list-9;\n}\n.ql-editor ol li.ql-indent-8 {\n  counter-increment: list-8;\n}\n.ql-editor ol li.ql-indent-8:before {\n  content: counter(list-8, lower-roman) '. ';\n}\n.ql-editor ol li.ql-indent-8 {\n  counter-reset: list-9;\n}\n.ql-editor ol li.ql-indent-9 {\n  counter-increment: list-9;\n}\n.ql-editor ol li.ql-indent-9:before {\n  content: counter(list-9, decimal) '. ';\n}\n.ql-editor .ql-indent-1:not(.ql-direction-rtl) {\n  padding-left: 3em;\n}\n.ql-editor li.ql-indent-1:not(.ql-direction-rtl) {\n  padding-left: 4.5em;\n}\n.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right {\n  padding-right: 3em;\n}\n.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right {\n  padding-right: 4.5em;\n}\n.ql-editor .ql-indent-2:not(.ql-direction-rtl) {\n  padding-left: 6em;\n}\n.ql-editor li.ql-indent-2:not(.ql-direction-rtl) {\n  padding-left: 7.5em;\n}\n.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right {\n  padding-right: 6em;\n}\n.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right {\n  padding-right: 7.5em;\n}\n.ql-editor .ql-indent-3:not(.ql-direction-rtl) {\n  padding-left: 9em;\n}\n.ql-editor li.ql-indent-3:not(.ql-direction-rtl) {\n  padding-left: 10.5em;\n}\n.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right {\n  padding-right: 9em;\n}\n.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right {\n  padding-right: 10.5em;\n}\n.ql-editor .ql-indent-4:not(.ql-direction-rtl) {\n  padding-left: 12em;\n}\n.ql-editor li.ql-indent-4:not(.ql-direction-rtl) {\n  padding-left: 13.5em;\n}\n.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right {\n  padding-right: 12em;\n}\n.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right {\n  padding-right: 13.5em;\n}\n.ql-editor .ql-indent-5:not(.ql-direction-rtl) {\n  padding-left: 15em;\n}\n.ql-editor li.ql-indent-5:not(.ql-direction-rtl) {\n  padding-left: 16.5em;\n}\n.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right {\n  padding-right: 15em;\n}\n.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right {\n  padding-right: 16.5em;\n}\n.ql-editor .ql-indent-6:not(.ql-direction-rtl) {\n  padding-left: 18em;\n}\n.ql-editor li.ql-indent-6:not(.ql-direction-rtl) {\n  padding-left: 19.5em;\n}\n.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right {\n  padding-right: 18em;\n}\n.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right {\n  padding-right: 19.5em;\n}\n.ql-editor .ql-indent-7:not(.ql-direction-rtl) {\n  padding-left: 21em;\n}\n.ql-editor li.ql-indent-7:not(.ql-direction-rtl) {\n  padding-left: 22.5em;\n}\n.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right {\n  padding-right: 21em;\n}\n.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right {\n  padding-right: 22.5em;\n}\n.ql-editor .ql-indent-8:not(.ql-direction-rtl) {\n  padding-left: 24em;\n}\n.ql-editor li.ql-indent-8:not(.ql-direction-rtl) {\n  padding-left: 25.5em;\n}\n.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right {\n  padding-right: 24em;\n}\n.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right {\n  padding-right: 25.5em;\n}\n.ql-editor .ql-indent-9:not(.ql-direction-rtl) {\n  padding-left: 27em;\n}\n.ql-editor li.ql-indent-9:not(.ql-direction-rtl) {\n  padding-left: 28.5em;\n}\n.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right {\n  padding-right: 27em;\n}\n.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right {\n  padding-right: 28.5em;\n}\n.ql-editor .ql-video {\n  display: block;\n  max-width: 100%;\n}\n.ql-editor .ql-video.ql-align-center {\n  margin: 0 auto;\n}\n.ql-editor .ql-video.ql-align-right {\n  margin: 0 0 0 auto;\n}\n.ql-editor .ql-bg-black {\n  background-color: #000;\n}\n.ql-editor .ql-bg-red {\n  background-color: #e60000;\n}\n.ql-editor .ql-bg-orange {\n  background-color: #f90;\n}\n.ql-editor .ql-bg-yellow {\n  background-color: #ff0;\n}\n.ql-editor .ql-bg-green {\n  background-color: #008a00;\n}\n.ql-editor .ql-bg-blue {\n  background-color: #06c;\n}\n.ql-editor .ql-bg-purple {\n  background-color: #93f;\n}\n.ql-editor .ql-color-white {\n  color: #fff;\n}\n.ql-editor .ql-color-red {\n  color: #e60000;\n}\n.ql-editor .ql-color-orange {\n  color: #f90;\n}\n.ql-editor .ql-color-yellow {\n  color: #ff0;\n}\n.ql-editor .ql-color-green {\n  color: #008a00;\n}\n.ql-editor .ql-color-blue {\n  color: #06c;\n}\n.ql-editor .ql-color-purple {\n  color: #93f;\n}\n.ql-editor .ql-font-serif {\n  font-family: Georgia, Times New Roman, serif;\n}\n.ql-editor .ql-font-monospace {\n  font-family: Monaco, Courier New, monospace;\n}\n.ql-editor .ql-size-small {\n  font-size: 0.75em;\n}\n.ql-editor .ql-size-large {\n  font-size: 1.5em;\n}\n.ql-editor .ql-size-huge {\n  font-size: 2.5em;\n}\n.ql-editor .ql-direction-rtl {\n  direction: rtl;\n  text-align: inherit;\n}\n.ql-editor .ql-align-center {\n  text-align: center;\n}\n.ql-editor .ql-align-justify {\n  text-align: justify;\n}\n.ql-editor .ql-align-right {\n  text-align: right;\n}\n.ql-editor.ql-blank::before {\n  color: rgba(0,0,0,0.6);\n  content: attr(data-placeholder);\n  font-style: italic;\n  left: 15px;\n  pointer-events: none;\n  position: absolute;\n  right: 15px;\n}\n.ql-snow.ql-toolbar:after,\n.ql-snow .ql-toolbar:after {\n  clear: both;\n  content: '';\n  display: table;\n}\n.ql-snow.ql-toolbar button,\n.ql-snow .ql-toolbar button {\n  background: none;\n  border: none;\n  cursor: pointer;\n  display: inline-block;\n  float: left;\n  height: 24px;\n  padding: 3px 5px;\n  width: 28px;\n}\n.ql-snow.ql-toolbar button svg,\n.ql-snow .ql-toolbar button svg {\n  float: left;\n  height: 100%;\n}\n.ql-snow.ql-toolbar button:active:hover,\n.ql-snow .ql-toolbar button:active:hover {\n  outline: none;\n}\n.ql-snow.ql-toolbar input.ql-image[type=file],\n.ql-snow .ql-toolbar input.ql-image[type=file] {\n  display: none;\n}\n.ql-snow.ql-toolbar button:hover,\n.ql-snow .ql-toolbar button:hover,\n.ql-snow.ql-toolbar button:focus,\n.ql-snow .ql-toolbar button:focus,\n.ql-snow.ql-toolbar button.ql-active,\n.ql-snow .ql-toolbar button.ql-active,\n.ql-snow.ql-toolbar .ql-picker-label:hover,\n.ql-snow .ql-toolbar .ql-picker-label:hover,\n.ql-snow.ql-toolbar .ql-picker-label.ql-active,\n.ql-snow .ql-toolbar .ql-picker-label.ql-active,\n.ql-snow.ql-toolbar .ql-picker-item:hover,\n.ql-snow .ql-toolbar .ql-picker-item:hover,\n.ql-snow.ql-toolbar .ql-picker-item.ql-selected,\n.ql-snow .ql-toolbar .ql-picker-item.ql-selected {\n  color: #06c;\n}\n.ql-snow.ql-toolbar button:hover .ql-fill,\n.ql-snow .ql-toolbar button:hover .ql-fill,\n.ql-snow.ql-toolbar button:focus .ql-fill,\n.ql-snow .ql-toolbar button:focus .ql-fill,\n.ql-snow.ql-toolbar button.ql-active .ql-fill,\n.ql-snow .ql-toolbar button.ql-active .ql-fill,\n.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,\n.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,\n.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,\n.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,\n.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,\n.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,\n.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,\n.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,\n.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,\n.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill,\n.ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,\n.ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,\n.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,\n.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,\n.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,\n.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,\n.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,\n.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,\n.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,\n.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,\n.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,\n.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {\n  fill: #06c;\n}\n.ql-snow.ql-toolbar button:hover .ql-stroke,\n.ql-snow .ql-toolbar button:hover .ql-stroke,\n.ql-snow.ql-toolbar button:focus .ql-stroke,\n.ql-snow .ql-toolbar button:focus .ql-stroke,\n.ql-snow.ql-toolbar button.ql-active .ql-stroke,\n.ql-snow .ql-toolbar button.ql-active .ql-stroke,\n.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,\n.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,\n.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,\n.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,\n.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,\n.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,\n.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,\n.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,\n.ql-snow.ql-toolbar button:hover .ql-stroke-miter,\n.ql-snow .ql-toolbar button:hover .ql-stroke-miter,\n.ql-snow.ql-toolbar button:focus .ql-stroke-miter,\n.ql-snow .ql-toolbar button:focus .ql-stroke-miter,\n.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,\n.ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,\n.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,\n.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,\n.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,\n.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,\n.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,\n.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,\n.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,\n.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter {\n  stroke: #06c;\n}\n@media (pointer: coarse) {\n  .ql-snow.ql-toolbar button:hover:not(.ql-active),\n  .ql-snow .ql-toolbar button:hover:not(.ql-active) {\n    color: #444;\n  }\n  .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-fill,\n  .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-fill,\n  .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,\n  .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill {\n    fill: #444;\n  }\n  .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke,\n  .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke,\n  .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,\n  .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter {\n    stroke: #444;\n  }\n}\n.ql-snow {\n  box-sizing: border-box;\n}\n.ql-snow * {\n  box-sizing: border-box;\n}\n.ql-snow .ql-hidden {\n  display: none;\n}\n.ql-snow .ql-out-bottom,\n.ql-snow .ql-out-top {\n  visibility: hidden;\n}\n.ql-snow .ql-tooltip {\n  position: absolute;\n  transform: translateY(10px);\n}\n.ql-snow .ql-tooltip a {\n  cursor: pointer;\n  text-decoration: none;\n}\n.ql-snow .ql-tooltip.ql-flip {\n  transform: translateY(-10px);\n}\n.ql-snow .ql-formats {\n  display: inline-block;\n  vertical-align: middle;\n}\n.ql-snow .ql-formats:after {\n  clear: both;\n  content: '';\n  display: table;\n}\n.ql-snow .ql-stroke {\n  fill: none;\n  stroke: #444;\n  stroke-linecap: round;\n  stroke-linejoin: round;\n  stroke-width: 2;\n}\n.ql-snow .ql-stroke-miter {\n  fill: none;\n  stroke: #444;\n  stroke-miterlimit: 10;\n  stroke-width: 2;\n}\n.ql-snow .ql-fill,\n.ql-snow .ql-stroke.ql-fill {\n  fill: #444;\n}\n.ql-snow .ql-empty {\n  fill: none;\n}\n.ql-snow .ql-even {\n  fill-rule: evenodd;\n}\n.ql-snow .ql-thin,\n.ql-snow .ql-stroke.ql-thin {\n  stroke-width: 1;\n}\n.ql-snow .ql-transparent {\n  opacity: 0.4;\n}\n.ql-snow .ql-direction svg:last-child {\n  display: none;\n}\n.ql-snow .ql-direction.ql-active svg:last-child {\n  display: inline;\n}\n.ql-snow .ql-direction.ql-active svg:first-child {\n  display: none;\n}\n.ql-snow .ql-editor h1 {\n  font-size: 2em;\n}\n.ql-snow .ql-editor h2 {\n  font-size: 1.5em;\n}\n.ql-snow .ql-editor h3 {\n  font-size: 1.17em;\n}\n.ql-snow .ql-editor h4 {\n  font-size: 1em;\n}\n.ql-snow .ql-editor h5 {\n  font-size: 0.83em;\n}\n.ql-snow .ql-editor h6 {\n  font-size: 0.67em;\n}\n.ql-snow .ql-editor a {\n  text-decoration: underline;\n}\n.ql-snow .ql-editor blockquote {\n  border-left: 4px solid #ccc;\n  margin-bottom: 5px;\n  margin-top: 5px;\n  padding-left: 16px;\n}\n.ql-snow .ql-editor code,\n.ql-snow .ql-editor pre {\n  background-color: #f0f0f0;\n  border-radius: 3px;\n}\n.ql-snow .ql-editor pre {\n  white-space: pre-wrap;\n  margin-bottom: 5px;\n  margin-top: 5px;\n  padding: 5px 10px;\n}\n.ql-snow .ql-editor code {\n  font-size: 85%;\n  padding: 2px 4px;\n}\n.ql-snow .ql-editor pre.ql-syntax {\n  background-color: #23241f;\n  color: #f8f8f2;\n  overflow: visible;\n}\n.ql-snow .ql-editor img {\n  max-width: 100%;\n}\n.ql-snow .ql-picker {\n  color: #444;\n  display: inline-block;\n  float: left;\n  font-size: 14px;\n  font-weight: 500;\n  height: 24px;\n  position: relative;\n  vertical-align: middle;\n}\n.ql-snow .ql-picker-label {\n  cursor: pointer;\n  display: inline-block;\n  height: 100%;\n  padding-left: 8px;\n  padding-right: 2px;\n  position: relative;\n  width: 100%;\n}\n.ql-snow .ql-picker-label::before {\n  display: inline-block;\n  line-height: 22px;\n}\n.ql-snow .ql-picker-options {\n  background-color: #fff;\n  display: none;\n  min-width: 100%;\n  padding: 4px 8px;\n  position: absolute;\n  white-space: nowrap;\n}\n.ql-snow .ql-picker-options .ql-picker-item {\n  cursor: pointer;\n  display: block;\n  padding-bottom: 5px;\n  padding-top: 5px;\n}\n.ql-snow .ql-picker.ql-expanded .ql-picker-label {\n  color: #ccc;\n  z-index: 2;\n}\n.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill {\n  fill: #ccc;\n}\n.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke {\n  stroke: #ccc;\n}\n.ql-snow .ql-picker.ql-expanded .ql-picker-options {\n  display: block;\n  margin-top: -1px;\n  top: 100%;\n  z-index: 1;\n}\n.ql-snow .ql-color-picker,\n.ql-snow .ql-icon-picker {\n  width: 28px;\n}\n.ql-snow .ql-color-picker .ql-picker-label,\n.ql-snow .ql-icon-picker .ql-picker-label {\n  padding: 2px 4px;\n}\n.ql-snow .ql-color-picker .ql-picker-label svg,\n.ql-snow .ql-icon-picker .ql-picker-label svg {\n  right: 4px;\n}\n.ql-snow .ql-icon-picker .ql-picker-options {\n  padding: 4px 0px;\n}\n.ql-snow .ql-icon-picker .ql-picker-item {\n  height: 24px;\n  width: 24px;\n  padding: 2px 4px;\n}\n.ql-snow .ql-color-picker .ql-picker-options {\n  padding: 3px 5px;\n  width: 152px;\n}\n.ql-snow .ql-color-picker .ql-picker-item {\n  border: 1px solid transparent;\n  float: left;\n  height: 16px;\n  margin: 2px;\n  padding: 0px;\n  width: 16px;\n}\n.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {\n  position: absolute;\n  margin-top: -9px;\n  right: 0;\n  top: 50%;\n  width: 18px;\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=''])::before,\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=''])::before,\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=''])::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=''])::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=''])::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=''])::before {\n  content: attr(data-label);\n}\n.ql-snow .ql-picker.ql-header {\n  width: 98px;\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item::before {\n  content: 'Normal';\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"1\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\n  content: 'Heading 1';\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"2\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\n  content: 'Heading 2';\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"3\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\n  content: 'Heading 3';\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"4\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\n  content: 'Heading 4';\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"5\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\n  content: 'Heading 5';\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=\"6\"]::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\n  content: 'Heading 6';\n}\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"1\"]::before {\n  font-size: 2em;\n}\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"2\"]::before {\n  font-size: 1.5em;\n}\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"3\"]::before {\n  font-size: 1.17em;\n}\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"4\"]::before {\n  font-size: 1em;\n}\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"5\"]::before {\n  font-size: 0.83em;\n}\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=\"6\"]::before {\n  font-size: 0.67em;\n}\n.ql-snow .ql-picker.ql-font {\n  width: 108px;\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item::before {\n  content: 'Sans Serif';\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=serif]::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]::before {\n  content: 'Serif';\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=monospace]::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {\n  content: 'Monospace';\n}\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]::before {\n  font-family: Georgia, Times New Roman, serif;\n}\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {\n  font-family: Monaco, Courier New, monospace;\n}\n.ql-snow .ql-picker.ql-size {\n  width: 98px;\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item::before {\n  content: 'Normal';\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=small]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]::before {\n  content: 'Small';\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=large]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]::before {\n  content: 'Large';\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=huge]::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]::before {\n  content: 'Huge';\n}\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]::before {\n  font-size: 10px;\n}\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]::before {\n  font-size: 18px;\n}\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]::before {\n  font-size: 32px;\n}\n.ql-snow .ql-color-picker.ql-background .ql-picker-item {\n  background-color: #fff;\n}\n.ql-snow .ql-color-picker.ql-color .ql-picker-item {\n  background-color: #000;\n}\n.ql-toolbar.ql-snow {\n  border: 1px solid #ccc;\n  box-sizing: border-box;\n  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;\n  padding: 8px;\n}\n.ql-toolbar.ql-snow .ql-formats {\n  margin-right: 15px;\n}\n.ql-toolbar.ql-snow .ql-picker-label {\n  border: 1px solid transparent;\n}\n.ql-toolbar.ql-snow .ql-picker-options {\n  border: 1px solid transparent;\n  box-shadow: rgba(0,0,0,0.2) 0 2px 8px;\n}\n.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {\n  border-color: #ccc;\n}\n.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {\n  border-color: #ccc;\n}\n.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item.ql-selected,\n.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item:hover {\n  border-color: #000;\n}\n.ql-toolbar.ql-snow + .ql-container.ql-snow {\n  border-top: 0px;\n}\n.ql-snow .ql-tooltip {\n  background-color: #fff;\n  border: 1px solid #ccc;\n  box-shadow: 0px 0px 5px #ddd;\n  color: #444;\n  padding: 5px 12px;\n  white-space: nowrap;\n}\n.ql-snow .ql-tooltip::before {\n  content: \"Visit URL:\";\n  line-height: 26px;\n  margin-right: 8px;\n}\n.ql-snow .ql-tooltip input[type=text] {\n  display: none;\n  border: 1px solid #ccc;\n  font-size: 13px;\n  height: 26px;\n  margin: 0px;\n  padding: 3px 5px;\n  width: 170px;\n}\n.ql-snow .ql-tooltip a.ql-preview {\n  display: inline-block;\n  max-width: 200px;\n  overflow-x: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n}\n.ql-snow .ql-tooltip a.ql-action::after {\n  border-right: 1px solid #ccc;\n  content: 'Edit';\n  margin-left: 16px;\n  padding-right: 8px;\n}\n.ql-snow .ql-tooltip a.ql-remove::before {\n  content: 'Remove';\n  margin-left: 8px;\n}\n.ql-snow .ql-tooltip a {\n  line-height: 26px;\n}\n.ql-snow .ql-tooltip.ql-editing a.ql-preview,\n.ql-snow .ql-tooltip.ql-editing a.ql-remove {\n  display: none;\n}\n.ql-snow .ql-tooltip.ql-editing input[type=text] {\n  display: inline-block;\n}\n.ql-snow .ql-tooltip.ql-editing a.ql-action::after {\n  border-right: 0px;\n  content: 'Save';\n  padding-right: 0px;\n}\n.ql-snow .ql-tooltip[data-mode=link]::before {\n  content: \"Enter link:\";\n}\n.ql-snow .ql-tooltip[data-mode=formula]::before {\n  content: \"Enter formula:\";\n}\n.ql-snow .ql-tooltip[data-mode=video]::before {\n  content: \"Enter video:\";\n}\n.ql-snow a {\n  color: #06c;\n}\n.ql-container.ql-snow {\n  border: 1px solid #ccc;\n}\n", "/* Variables for WordPress admin dimensions */\n:root {\n  --wp-admin-bar-height: 32px;\n  --wp-admin-sidebar-width: 160px;\n}\n\n/* WordPress admin overrides */\nbody.wp-admin #wpcontent {\n  padding-left: 0 !important;\n}\n\n#wpbody-content {\n  padding-bottom: 0;\n}\n\n.wrap {\n  margin: 0;\n  padding: 0;\n}\n\n/* Plugin root container */\n#wp-ai-workflows-root {\n  margin: 0;\n  height: calc(100vh - var(--wp-admin-bar-height));\n  overflow-y: auto;\n}\n\n/* Ant Design layout adjustments */\n.ant-layout {\n  min-height: calc(100vh - var(--wp-admin-bar-height)) !important;\n}\n\n/* Header styles */\n.ant-layout-header {\n  /* Remove position: sticky; */\n  display: flex;\n  align-items: center;\n  padding: 0 16px !important;\n  /* Remove top gap */\n  margin-top: 0px;\n}\n\n.ant-layout-header .ant-image {\n  height: 32px;\n  margin-right: 24px;\n}\n\n.ant-layout-header .ant-menu {\n  flex: 1;\n}\n\n/* Content area adjustments */\n.ant-layout-content {\n  padding-top: 16px;\n}\n\n.react-flow__handle {\n  width: 12px;\n  height: 12px;\n  background-color: #1890ff;\n  border: 2px solid white;\n  border-radius: 50%;\n}\n\n.react-flow__handle-left {\n  left: -4px;\n}\n\n.react-flow__handle-right {\n  right: -4px;\n}\n\n.react-flow__node {\n  z-index: 1;\n}\n\n.react-flow__edge {\n  z-index: 2;\n}\n\n.react-flow__handle {\n  z-index: 3;\n}\n\n.ant-card {\n  z-index: 4;\n}\n\n.ant-modal {\n  z-index: 1000;\n}\n\n/* Card styles for nodes */\n.custom-node .ant-card {\n  background-color: #fff;\n  border-radius: 5px;\n  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);\n}\n\n.custom-node .ant-card-head {\n  border-bottom: none;\n}\n\n.custom-node .ant-card-body {\n  padding: 12px;\n}\n\n/* Responsive adjustments */\n@media screen and (max-width: 782px) {\n  :root {\n    --wp-admin-bar-height: 46px;\n  }\n  \n  .ant-layout-header {\n    margin-top: -46px;\n  }\n}\n\n@media screen and (max-width: 600px) {\n  :root {\n    --wp-admin-bar-height: 0;\n  }\n  \n  .ant-layout-header {\n    margin-top: 0;\n  }\n}\n\n.field-collapse .ant-collapse-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.ant-input.ant-input-textarea-show-count {\n  resize: vertical;\n  min-height: 32px;\n}\n\n.quill {\n  background: white;\n  margin-bottom: 8px;\n}\n\n.quill .ql-container {\n  min-height: 100px;\n}\n\n/* Add to your CSS file */\n.workflow-generation-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.75);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 2000;\n  backdrop-filter: blur(4px);\n}\n\n.workflow-generation-content {\n  background-color: rgba(255, 255, 255, 0.95);\n  padding: 40px;\n  border-radius: 16px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  max-width: 500px;\n  width: 90%;\n  text-align: center;\n}\n\n.ant-modal-mask {\n  background-color: rgba(0, 0, 0, 0.65) !important;\n}\n\n.ant-modal-wrap {\n  z-index: 1050;\n}\n\n.workflow-generation-loader {\n  z-index: 2000;\n}\n\n.workflow-prompt-input {\n  border-radius: 8px;\n  border: 1px solid #d9d9d9;\n  transition: all 0.3s;\n}\n\n.workflow-prompt-input:hover {\n  border-color: #40a9ff;\n}\n\n.workflow-prompt-input:focus {\n  border-color: #40a9ff;\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n  outline: none;\n}\n\n.ant-modal-content {\n  border-radius: 12px;\n  overflow: hidden;\n}\n\n.ant-modal-header {\n  border-radius: 12px 12px 0 0;\n}\n\n.generate-ai-button {\n  background: linear-gradient(45deg, #FF6B6B, #4ECDC4);\n  border: none;\n  color: white;\n  transition: all 0.3s;\n}\n\n.generate-ai-button:hover {\n  opacity: 0.9;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n/* Chat Node specific styles */\n.chat-node.ai-workflows-node {\n  /* Base node styling */\n  .ant-card {\n      transition: box-shadow 0.3s ease;\n  }\n\n  .ant-card:hover {\n      box-shadow: 0 2px 8px rgba(0,0,0,0.15);\n  }\n\n  /* Design preview container */\n  .chat-preview-container {\n      background: #f5f5f5;\n      border-radius: 8px;\n      padding: 12px;\n      margin: 8px 0;\n      border: 1px solid #d9d9d9;\n      min-height: 200px;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n  }\n\n  /* Color picker customization */\n  .ant-color-picker {\n      width: 100%;\n  }\n\n  /* Model selection dropdown customization */\n  .model-select-option {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      .model-icon {\n          font-size: 16px;\n      }\n\n      .model-info {\n          flex: 1;\n      }\n  }\n\n  /* Settings tabs customization */\n  .ant-tabs-nav {\n      margin-bottom: 12px;\n  }\n\n  /* Collapse panel customization */\n  .ant-collapse-ghost {\n      background: transparent;\n  }\n\n  /* ReactQuill editor customization */\n  .quill {\n      background: white;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      margin-bottom: 8px;\n\n      .ql-toolbar {\n          border-bottom: 1px solid #d9d9d9;\n          border-radius: 4px 4px 0 0;\n      }\n\n      .ql-container {\n          border-radius: 0 0 4px 4px;\n      }\n  }\n\n  /* Embedding instructions styling */\n  .embedding-code {\n      background: #f5f5f5;\n      padding: 8px;\n      border-radius: 4px;\n      font-family: monospace;\n      margin: 8px 0;\n  }\n\n  /* Node input handler customization */\n  .node-input-handler {\n      margin: 8px 0;\n  }\n\n  /* Parameter settings styling */\n  .parameter-setting {\n      margin-bottom: 12px;\n      \n      .parameter-label {\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n          margin-bottom: 4px;\n      }\n\n      .parameter-input {\n          width: 100%;\n      }\n\n      .parameter-info {\n          color: #8c8c8c;\n          font-size: 12px;\n          margin-top: 2px;\n      }\n  }\n\n  /* Memory settings styling */\n  .memory-settings {\n      padding: 8px;\n      background: #fafafa;\n      border-radius: 4px;\n      margin-bottom: 12px;\n  }\n\n  /* Design settings preview */\n  .design-preview {\n      border: 1px solid #d9d9d9;\n      border-radius: 8px;\n      overflow: hidden;\n      margin: 12px 0;\n      \n      &.dark {\n          background: #141414;\n          color: white;\n      }\n\n      .preview-header {\n          padding: 8px;\n          background: var(--primary-color, #1677ff);\n          color: white;\n      }\n\n      .preview-content {\n          padding: 8px;\n          min-height: 100px;\n      }\n\n      .preview-input {\n          padding: 8px;\n          border-top: 1px solid #d9d9d9;\n      }\n  }\n}\n\n/* Contextual help tooltips */\n.chat-node-tooltip {\n  max-width: 300px;\n  font-size: 12px;\n}\n\n/* Modal customization for chat node */\n.chat-node-modal {\n  .ant-modal-body {\n      max-height: 70vh;\n      overflow-y: auto;\n  }\n\n  .ant-form-item-label {\n      font-weight: 500;\n  }\n}\n\n.edge-delete-button {\n  opacity: 0;\n  transition: opacity 0.2s;\n  pointer-events: all;\n}\n\n.react-flow__edge:hover .edge-delete-button,\n.edge-delete-button.selected {\n  opacity: 1;\n}\n\n.edge-delete-button:hover {\n  opacity: 1;\n}\n\n.react-flow__edge {\n  cursor: pointer;\n}\n\n.react-flow__edge.selected {\n  z-index: 1000;\n}\n\n.edge-delete-button {\n  z-index: 1001;\n}\n\n.react-flow__edge-path {\n  transition: stroke 0.2s, stroke-width 0.2s;\n}\n/* Additional specific styles can be added here */", ".wp-ai-workflows-chat-container {\n    --chat-primary: #1677ff;\n    --chat-bg: #ffffff;\n    --chat-text: #000000;\n    --chat-secondary: #f5f5f5;\n    --chat-radius: 12px;\n    --chat-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.wp-ai-workflows-chat-container.dark {\n    --chat-primary: #177ddc;\n    --chat-bg: #1f1f1f;\n    --chat-text: #ffffff;\n    --chat-secondary: #2f2f2f;\n}\n\n.wp-ai-workflows-chat-widget {\n    position: fixed;\n    z-index: 9999;\n    max-width: 400px;\n    width: 100%;\n    background: var(--chat-bg);\n    border-radius: var(--chat-radius);\n    box-shadow: var(--chat-shadow);\n    display: flex;\n    flex-direction: column;\n    transition: all 0.3s ease;\n    --chat-border-radius: 12px;\n    border-radius: var(--chat-border-radius);\n}\n\n/* Position variants */\n.wp-ai-workflows-chat-widget.bottom-right {\n    bottom: 20px;\n    right: 20px;\n}\n\n.wp-ai-workflows-chat-widget.bottom-left {\n    bottom: 20px;\n    left: 20px;\n}\n\n.wp-ai-workflows-chat-widget.top-right {\n    top: 20px;\n    right: 20px;\n}\n\n.wp-ai-workflows-chat-widget.top-left {\n    top: 20px;\n    left: 20px;\n}\n\n.chat-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 12px 16px;\n    background: var(--chat-primary);\n    color: #ffffff;\n    border-top-left-radius: var(--chat-border-radius);\n    border-top-right-radius: var(--chat-border-radius);\n}\n\n.wp-ai-workflows-chat-widget:not(.inline) .chat-header {\n    cursor: pointer;\n}\n\n\n.header-content {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n}\n  \n.bot-name {\n    font-weight: 500;\n}\n\n.header-actions {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n}\n\n.chat-messages {\n    flex: 1;\n    overflow-y: auto;\n    padding: 16px;\n    display: flex;\n    flex-direction: column;\n    gap: 12px;\n}\n\n.chat-message {\n    display: flex;\n    margin-bottom: 8px;\n}\n\n.chat-message .message-content {\n    border-radius: calc(var(--chat-border-radius) / 2);\n}\n\n.chat-message.user {\n    justify-content: flex-end;\n}\n\n.message-content {\n    padding: 8px 12px;\n    border-radius: 12px;\n    max-width: 80%;\n    word-wrap: break-word;\n}\n\n.chat-message.user .message-content {\n    background: var(--chat-primary);\n    color: #ffffff;\n}\n\n.chat-message.assistant .message-content {\n    background: var(--chat-secondary);\n    color: var(--chat-text);\n}\n\n.chat-input {\n    padding: 16px;\n    border-top: 1px solid var(--chat-secondary);\n    display: flex;\n    gap: 8px;\n}\n\n.chat-input input {\n    flex: 1;\n    padding: 8px 12px;\n    border: 1px solid var(--chat-secondary);\n    border-radius: 6px;\n    outline: none;\n    background: var(--chat-bg);\n    color: var(--chat-text);\n}\n\n.chat-input button {\n    padding: 8px 16px;\n    background: var(--chat-primary);\n    color: #ffffff;\n    border: none;\n    border-radius: 6px;\n    cursor: pointer;\n    transition: opacity 0.2s;\n}\n\n.chat-input button:hover {\n    opacity: 0.9;\n}\n\n/* Launcher button */\n.chat-launcher {\n    position: fixed;\n    z-index: 9998;\n    width: 56px;\n    height: 56px;\n    border-radius: 28px;\n    background: var(--chat-primary);\n    color: #ffffff;\n    cursor: pointer;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    box-shadow: var(--chat-shadow);\n    transition: transform 0.3s ease;\n}\n\n.chat-launcher:hover {\n    transform: scale(1.05);\n}\n\n.chat-launcher.bottom-right {\n    bottom: 20px;\n    right: 20px;\n}\n\n.chat-launcher.bottom-left {\n    bottom: 20px;\n    left: 20px;\n}\n\n.chat-launcher.top-right {\n    top: 20px;\n    right: 20px;\n}\n\n.chat-launcher.top-left {\n    top: 20px;\n    left: 20px;\n}\n\n/* Mobile responsiveness */\n@media (max-width: 480px) {\n    .wp-ai-workflows-chat-widget:not(.inline) {\n        width: calc(100% - 32px); /* Full width minus margins */\n        max-width: none;\n        margin: 16px;\n        height: auto;\n        min-height: 400px;\n        max-height: calc(100vh - 32px); /* Full height minus margins */\n    }\n\n    /* Maintain position classes */\n    .wp-ai-workflows-chat-widget.bottom-right {\n        bottom: 0;\n        right: 0;\n    }\n\n    .wp-ai-workflows-chat-widget.bottom-left {\n        bottom: 0;\n        left: 0;\n    }\n\n    .wp-ai-workflows-chat-widget.top-right {\n        top: 0;\n        right: 0;\n    }\n\n    .wp-ai-workflows-chat-widget.top-left {\n        top: 0;\n        left: 0;\n    }\n\n    .chat-header {\n        border-top-left-radius: var(--chat-border-radius);\n        border-top-right-radius: var(--chat-border-radius);\n    }\n}\n\n@media (max-width: 360px) {\n    .wp-ai-workflows-chat-widget:not(.inline) {\n        width: 100%;\n        height: 100%;\n        margin: 0;\n        max-height: 100vh;\n        border-radius: 0;\n    }\n\n    .wp-ai-workflows-chat-widget:not(.inline) .chat-header {\n        border-radius: 0;\n    }\n}\n\n/* Animations */\n.chat-widget-enter {\n    opacity: 0;\n    transform: translateY(20px);\n}\n\n.chat-widget-enter-active {\n    opacity: 1;\n    transform: translateY(0);\n    transition: opacity 0.3s ease, transform 0.3s ease;\n}\n\n.chat-widget-exit {\n    opacity: 1;\n    transform: translateY(0);\n}\n\n.chat-widget-exit-active {\n    opacity: 0;\n    transform: translateY(20px);\n    transition: opacity 0.3s ease, transform 0.3s ease;\n}\n\n/* Typing indicator */\n.typing-indicator {\n    display: flex;\n    gap: 4px;\n    padding: 8px 12px;\n    background: var(--chat-secondary);\n    border-radius: 12px;\n    width: fit-content;\n}\n\n.typing-dot {\n    width: 6px;\n    height: 6px;\n    background: var(--chat-text);\n    border-radius: 50%;\n    opacity: 0.6;\n    animation: typingAnimation 1.4s infinite;\n}\n\n.typing-dot:nth-child(2) {\n    animation-delay: 0.2s;\n}\n\n.typing-dot:nth-child(3) {\n    animation-delay: 0.4s;\n}\n\n@keyframes typingAnimation {\n    0%, 100% {\n        transform: translateY(0);\n    }\n    50% {\n        transform: translateY(-4px);\n    }\n}\n\n/* Sound toggle button */\n.sound-toggle,\n.close-button {\n  background: transparent;\n  border: none;\n  color: rgba(255, 255, 255, 0.8);\n  cursor: pointer;\n  padding: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: color 0.2s ease;\n}\n\n.sound-toggle:hover,\n.close-button:hover {\n  color: #ffffff;\n}\n\n/* Error message styles */\n.error-message {\n    padding: 8px 12px;\n    background: #fff2f0;\n    border: 1px solid #ffccc7;\n    border-radius: 6px;\n    color: #ff4d4f;\n    margin: 8px 0;\n}\n\n/* Custom scrollbar */\n.chat-messages::-webkit-scrollbar {\n    width: 6px;\n}\n\n.chat-messages::-webkit-scrollbar-track {\n    background: transparent;\n}\n\n.chat-messages::-webkit-scrollbar-thumb {\n    background: rgba(0, 0, 0, 0.2);\n    border-radius: 3px;\n}\n\n.dark .chat-messages::-webkit-scrollbar-thumb {\n    background: rgba(255, 255, 255, 0.2);\n}\n\n/* Code block styling */\n.message-content pre {\n    background: rgba(0, 0, 0, 0.05);\n    padding: 8px;\n    border-radius: 4px;\n    overflow-x: auto;\n}\n\n.dark .message-content pre {\n    background: rgba(255, 255, 255, 0.1);\n}\n\n/* Markdown styling */\n.message-content p {\n    margin: 0 0 8px 0;\n}\n\n.message-content p:last-child {\n    margin-bottom: 0;\n}\n\n.message-content ul, \n.message-content ol {\n    margin: 8px 0;\n    padding-left: 20px;\n}\n\n.message-content code {\n    background: rgba(0, 0, 0, 0.05);\n    padding: 2px 4px;\n    border-radius: 4px;\n    font-family: monospace;\n}\n\n.dark .message-content code {\n    background: rgba(255, 255, 255, 0.1);\n}\n\n/* Dark theme specific adjustments */\n.wp-ai-workflows-chat-widget.dark {\n    --chat-bg: #1f1f1f;\n    --chat-text: #ffffff;\n    --chat-secondary: #2f2f2f;\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n}\n  \n/* Add smooth transitions for theme changes */\n.wp-ai-workflows-chat-widget {\n    transition: background-color 0.3s ease, width 0.3s ease, height 0.3s ease;\n}\n  \n.wp-ai-workflows-chat-widget * {\n    transition: background-color 0.3s ease, color 0.3s ease;\n}\n\n/* File upload area */\n.file-upload-area {\n    border: 2px dashed var(--chat-secondary);\n    border-radius: 8px;\n    padding: 16px;\n    text-align: center;\n    margin: 8px 0;\n    cursor: pointer;\n    transition: border-color 0.2s;\n}\n\n.file-upload-area:hover {\n    border-color: var(--chat-primary);\n}\n\n.file-upload-area.dragging {\n    background: rgba(var(--chat-primary-rgb), 0.1);\n    border-color: var(--chat-primary);\n}\n\n/* Accessibility */\n.sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    border: 0;\n}\n\n/* Focus styles */\n.chat-input input:focus,\n.chat-input button:focus,\n.sound-toggle:focus {\n    outline: 2px solid var(--chat-primary);\n    outline-offset: 2px;\n}\n\n/* CSS for minimized state */\n.wp-ai-workflows-chat-widget.minimized {\n    height: auto;\n    min-height: unset;\n}\n\n.wp-ai-workflows-chat-widget.minimized .chat-messages,\n.wp-ai-workflows-chat-widget.minimized .chat-input {\n    display: none;\n}\n\n.wp-ai-workflows-chat-widget .powered-by {\n    text-align: center;\n    padding: 6px;\n    font-size: 9px;\n    opacity: 0.7;\n    color: var(--chat-text, inherit);\n    border-top: 1px solid var(--chat-secondary, #f0f0f0);\n    transition: opacity 0.2s ease;\n}\n\n.wp-ai-workflows-chat-widget .powered-by a {\n    color: inherit;\n    text-decoration: none;\n    display: inline-flex;\n    align-items: center;\n    gap: 4px;\n}\n\n.wp-ai-workflows-chat-widget .powered-by a:hover {\n    opacity: 1;\n    text-decoration: underline;\n}\n\n/* Dark theme specific styles */\n.wp-ai-workflows-chat-widget.dark .powered-by {\n    opacity: 0.5;\n}\n\n.wp-ai-workflows-chat-widget.dark .powered-by a {\n    color: rgba(255, 255, 255, 0.8);\n}\n\n/* Remove any background/border-radius from all themes */\n.wp-ai-workflows-chat-widget.dark .powered-by,\n.wp-ai-workflows-chat-widget.custom .powered-by {\n    background: none;\n    border-radius: 0;\n}\n\n\n\n/* Base styles for inline widget */\n.wp-ai-workflows-chat-widget.inline {\n    position: relative;\n    width: 100%;\n    max-width: 100%;\n    height: auto !important; /* Override fixed height */\n    min-height: 500px;\n    margin: 20px 0;\n    border: 1px solid var(--chat-secondary, #f0f0f0);\n    border-radius: var(--chat-border-radius, 12px);\n    background-color: var(--chat-bg, #ffffff);\n    box-shadow: none !important; /* Remove floating widget shadow */\n    font-family: var(--chat-font-family);\n    font-size: var(--chat-font-size);\n}\n\n/* Header styling for inline widget */\n.wp-ai-workflows-chat-widget.inline .chat-header {\n    position: relative;\n    border-top-left-radius: var(--chat-border-radius, 12px);\n    border-top-right-radius: var(--chat-border-radius, 12px);\n    background-color: var(--chat-primary, #1677ff);\n    padding: 12px 16px;\n    z-index: 2;\n}\n\n/* Messages container for inline widget */\n.wp-ai-workflows-chat-widget.inline .chat-messages {\n    height: 400px; /* Default height */\n    max-height: calc(100vh - 300px); /* Responsive max height */\n    overflow-y: auto;\n    background-color: var(--chat-bg, #ffffff);\n    padding: 16px;\n}\n\n/* Input area for inline widget */\n.wp-ai-workflows-chat-widget.inline .chat-input {\n    position: relative;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    background-color: var(--chat-bg, #ffffff);\n    border-bottom-left-radius: var(--chat-border-radius, 12px);\n    border-bottom-right-radius: var(--chat-border-radius, 12px);\n    border-top: 1px solid var(--chat-secondary, #f0f0f0);\n    padding: 16px;\n}\n\n/* Theme-specific styles */\n.wp-ai-workflows-chat-widget.inline.dark {\n    background-color: #1f1f1f;\n    border-color: #333333;\n}\n\n.wp-ai-workflows-chat-widget.inline.dark .chat-messages,\n.wp-ai-workflows-chat-widget.inline.dark .chat-input {\n    background-color: #1f1f1f;\n}\n\n/* Custom theme support */\n.wp-ai-workflows-chat-widget.inline.custom {\n    background-color: var(--chat-bg);\n    border-color: var(--chat-secondary);\n}\n\n.wp-ai-workflows-chat-widget.inline.custom .chat-messages,\n.wp-ai-workflows-chat-widget.inline.custom .chat-input {\n    background-color: var(--chat-bg);\n}\n\n.wp-ai-workflows-chat-widget.inline .powered-by {\n    background: inherit;\n    border-top: 1px solid rgba(0, 0, 0, 0.1);\n    margin: 0;\n    padding: 6px;\n    font-size: 9px;\n    opacity: 0.7;\n    border-bottom-left-radius: var(--chat-border-radius);\n    border-bottom-right-radius: var(--chat-border-radius);\n}\n\n/* Dark theme adjustments */\n.wp-ai-workflows-chat-widget.inline.dark .powered-by {\n    border-top-color: rgba(255, 255, 255, 0.1);\n}\n\n/* Custom theme adjustments */\n.wp-ai-workflows-chat-widget.inline.custom .powered-by {\n    border-top-color: var(--chat-secondary);\n}\n\n/* Fix link color inheritance */\n.wp-ai-workflows-chat-widget.inline .powered-by a {\n    color: inherit;\n    opacity: 0.7;\n}\n\n/* Remove any conflicting styles */\n.wp-ai-workflows-chat-widget.inline .powered-by,\n.wp-ai-workflows-chat-widget.inline.dark .powered-by,\n.wp-ai-workflows-chat-widget.inline.custom .powered-by {\n    background: none !important;\n    color: inherit !important;\n}\n\n\n/* Responsive styles */\n@media screen and (max-width: 768px) {\n    .wp-ai-workflows-chat-widget.inline {\n        margin: 10px 0;\n        min-height: 400px;\n    }\n\n    .wp-ai-workflows-chat-widget.inline .chat-messages {\n        height: 300px;\n        max-height: calc(100vh - 200px);\n    }\n\n    .wp-ai-workflows-chat-widget.inline .chat-header {\n        padding: 8px 12px;\n    }\n\n    .wp-ai-workflows-chat-widget.inline .chat-input {\n        padding: 12px;\n    }\n}\n\n@media screen and (max-width: 480px) {\n    .wp-ai-workflows-chat-widget.inline {\n        margin: 5px 0;\n        min-height: 350px;\n    }\n\n    .wp-ai-workflows-chat-widget.inline .chat-messages {\n        height: 250px;\n        padding: 12px;\n    }\n\n    .wp-ai-workflows-chat-widget.inline .bot-name {\n        font-size: 14px;\n    }\n\n    .wp-ai-workflows-chat-widget.inline .message-content {\n        max-width: 90%;\n        padding: 6px 10px;\n    }\n}\n\n/* Font size inheritance */\n.wp-ai-workflows-chat-widget.inline * {\n    font-size: inherit;\n}\n\n.wp-ai-workflows-chat-widget.inline .chat-header {\n    font-size: var(--chat-header-font-size);\n}\n\n@media (min-width: 768px) {\n    .wp-ai-workflows-chat-widget:not(.inline) {\n        max-width: 90vw;\n    }\n}\n"], "names": [], "sourceRoot": ""}