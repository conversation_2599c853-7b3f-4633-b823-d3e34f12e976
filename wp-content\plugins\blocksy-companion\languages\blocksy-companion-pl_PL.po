# Translation of Blocksy Companion Pro in Polish
# This file is distributed under the same license as the Blocksy Companion Pro package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-02-13 13:42:54+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n == 1) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: pl\n"
"Project-Id-Version: Blocksy Companion Pro\n"

#: static/js/dashboard/helpers/useUpsellModal.js:88
msgid "Business or Agency"
msgstr "Firma lub agencja"

#: static/js/options/ConditionsManager/SingleCondition.js:482
msgid "Select value"
msgstr "Podaj wartość"

#: framework/helpers/exts-configs.php:385
msgid "Product Waitlist"
msgstr "Lista produktów oczekujących"

#: framework/helpers/exts-configs.php:386
msgid "Allow your customers to sign up for a waitlist for products that are out of stock and get notified when they are back in stock."
msgstr "Pozwól swoim klientom zapisać się na listę oczekujących na produkty, których nie ma w magazynie. Klienci otrzymają powiadomienie, gdy produkt ponownie będzie dostępny."

#: framework/features/blocks/share-box/options.php:103
msgid "Clipboard"
msgstr "Schowek"

#: framework/premium/extensions/shortcuts/customizer.php:609,
#: framework/premium/extensions/shortcuts/customizer.php:635,
#: framework/premium/extensions/shortcuts/views/bar.php:53,
#: framework/features/header/items/account/views/login.php:554,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:4,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/feature.php:189,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/feature.php:193,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-account.php:67
msgid "Waitlist"
msgstr "Lista oczekujących"

#: framework/premium/extensions/shortcuts/customizer.php:895
msgid "Tooltip Visibility"
msgstr "Widoczność podpowiedzi"

#: framework/premium/extensions/shortcuts/customizer.php:1351
msgid "Container Backdrop Blur"
msgstr "Rozmycie tła kontenera"

#: framework/premium/features/content-blocks/hooks-manager.php:957
msgid "Added to Cart: Before product"
msgstr "Dodano do koszyka: przed produktami"

#: framework/premium/features/content-blocks/hooks-manager.php:961
msgid "Added to Cart: Before actions"
msgstr "Dodano do koszyka: Przed działaniami"

#: framework/premium/features/content-blocks/hooks-manager.php:965
msgid "Added to Cart: Before suggested products"
msgstr "Dodano do koszyka: Przed produktami sugerowanymi"

#: framework/premium/features/content-blocks/hooks-manager.php:969
msgid "Added to Cart: After suggested products"
msgstr "Dodano do koszyka: po produktach sugerowanych"

#: framework/premium/features/content-blocks/hooks-manager.php:971
msgid "WooCommerce: Added to Cart"
msgstr "WooCommerce: Dodano do koszyka"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:294
msgid "Upsell Products"
msgstr "Produkty upsell"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:295
msgid "Cross-sell Products"
msgstr "Produkty cross-sell"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:506
msgid "Auto Close Panel"
msgstr "Automatyczne zamykanie panelu"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:507
msgid "Automatically close the panel when a filter option is selected."
msgstr "Automatyczne zamykanie panelu, gdy opcja filtra została wybrana"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:15
msgid "Form Type"
msgstr "Typ formularza"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:29
msgid "Form Max Width"
msgstr "Maksymalna szerokość formularza"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:41
msgid "Enable For Backorders"
msgstr "Włączone dla zamówień oczekujących"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:42
msgid "Allow users to join the waitlist even if the product is on backorder."
msgstr "Umożliwienie użytkownikom dołączenia do listy oczekujących, nawet jeśli produkt znajduje się na liście oczekujących."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:55
msgid "Show Users Count"
msgstr "Pokaż liczbę użytkowników"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:56
msgid "Display a counter that reflects the current number of users on the waitlist."
msgstr "Wyświetla licznik użytkowników na liście oczekujących."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:69
msgid "Logged In Users Only"
msgstr "Tylko dla zalogowanych użytkowników"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:70
msgid "Display the waitlist feature exclusively to users who are logged in."
msgstr "Wyświetlaj listę oczekujących wyłącznie dla zalogowanych użytkowników"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:83
msgid "Subscription Confirmation"
msgstr "Potwierdzenie subskrypcji"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:84
msgid "Specify which users should verify their waitlist subscription through email confirmation."
msgstr "Określ, jacy użytkownicy powinni zweryfikować swoją subskrypcję na liście oczekujących, za pomocą potwierdzenia e-mail."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:107
msgid "Waitlist Form Display Conditions"
msgstr "Warunki wyświetlania listy oczekujących"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:108
msgid "Choose where you want this Waitlist Form to be displayed."
msgstr "Wybierz miejsce wyświetlania Formularza Oczekujących"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:160
msgid "Message Font"
msgstr "Czcionka wiadomości"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:168
msgid "Message Color"
msgstr "Kolor wiadomości"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:225
msgid "Container Padding"
msgstr "Wypełnienie kontenera"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/table.php:40,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:46
msgid "Actions"
msgstr "Działania"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:96,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:200,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:221,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:282
msgid "Invalid request"
msgstr "Nieprawidłowe żądanie"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:106
msgid "No waitlist found"
msgstr "Nie odnaleziono listy"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:127,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:128,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:162
msgid "Waitlists"
msgstr "Lista oczekujących"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:153
msgid "Number of items per page"
msgstr "Liczba elementów na stronę"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:171
msgid "Waitlist for %s"
msgstr "Lista oczekujących na %s"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:213
msgid "Invalid email"
msgstr "Nieprawidłowy adres e-mail"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:229
msgid "You are already on the waitlist"
msgstr "Już jesteś na liście oczekujących"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:267
msgid "You have been added to the waitlist"
msgstr "Zostałeś dodany na listę oczekujących"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:291
msgid "You have been removed from the waitlist"
msgstr "Zostałeś usunięty z listy oczekujących"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-layer.php:33
msgid "%s %s joined the waitlist for this item."
msgstr "%s %s dołączył do listy oczekujących na ten produkt."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-layer.php:243
msgid "Waitlist Form"
msgstr "Formularz oczekujących"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-mailer.php:78
msgid "Your waitlist subscription has been successfully canceled."
msgstr "Subskrypcja listy oczekujących została anulowana"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-mailer.php:117
msgid "Your waitlist subscription has been successfully confirmed."
msgstr "Twoja subskrypcja listy oczekujących jest potwierdzona"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-table.php:11
msgid "Search Products"
msgstr "Wyszukiwanie produktów"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:14
msgid "Export Subscribers"
msgstr "Eksportuj subskrybentów"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:63
msgid "Guest"
msgstr "Gość"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:75
msgid "Edit this customer"
msgstr "Edycja tego klienta"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:76
msgid "Edit"
msgstr "Edytuj"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:115,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:182
msgid "Delete"
msgstr "Usuń"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:123
msgid "View"
msgstr "Widok"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:153
msgid "%s ago"
msgstr "%s temu"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:169
msgid "Is registered"
msgstr "Jest zarejestrowany"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:170
msgid "Date created"
msgstr "Data utworzenia"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:8
msgid "Waitlist - Back in Stock Notification"
msgstr "Lista oczekujących- powiadomienie o ponownej dostępności"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:9
msgid "This email is sent when a product is back in stock"
msgstr "Ten e-mail jest wysyłany, gdy produkt jest ponownie dostępny"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:10
msgid "A product you are waiting for is back in stock"
msgstr "Produkt na który czekasz jest ponownie dostępny"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:11
msgid "Good news! The product you have been waiting for is now back in stock!"
msgstr "Dobra wiadomość! Produkt, na który czekaliście, jest już dostępny!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:8
msgid "Waitlist - Confirm Subscription"
msgstr "Lista oczekujących - Potwierdzenie subskrypcji"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:9
msgid "This email is sent when a user subscribes to a product stock alert and should confirm their subscription"
msgstr "Ten e-mail jest wysyłany, gdy użytkownik subskrybuje powiadomienie o stanie magazynowym produktu i powinien potwierdzić swoją subskrypcję"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:10
msgid "Confirm waitlist subscription"
msgstr "Potwierdź subskrypcję listy oczekujących"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:11
msgid "Get notified when {product_title} is back in stock"
msgstr "Otrzymaj powiadomienie gdy {product_title} będzie ponownie dostępny"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:8
msgid "Waitlist - Subscription Confirmed"
msgstr "Lista oczekujących - subskrypcja potwierdzona"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:9
msgid "This email is sent after a user confirmed the subscription to a product stock alert"
msgstr "Ta wiadomość e-mail jest wysyłana po potwierdzeniu przez użytkownika subskrypcji alertu o stanie magazynowym produktu"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:10
msgid "Waitlist subscription confirmed"
msgstr "Subskrypcja listy oczekujących została potwierdzona"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:11
msgid "You will be notified when {product_title} is back in stock"
msgstr "Otrzymasz powiadomienie gdy {product_title} będzie znowu dostępny"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/waitlist-email.php:35,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/waitlist-email.php:139
msgid "Customer"
msgstr "Klient"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:27
msgid "Enter your email"
msgstr "Wpisz swój adres email"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:40
msgid "Join Waitlist"
msgstr "Dołączenie do listy oczekujących"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:53
msgid "This product is currently sold out!"
msgstr "Ten produkt został wyprzedany!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:61
msgid "No worries! Please enter your e-mail address and we will promptly notify you as soon as the item is back in stock."
msgstr "Bez obaw! Podaj swój adres e-mail, a niezwłocznie powiadomimy cię, gdy tylko produkt będzie ponownie dostępny."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:69
msgid "Great! You have been added to the waitlist for this product. Please check your inbox and confirm the subscription to this waitlist."
msgstr "Super! Zostałeś dodany do listy oczekujących na ten produkt. Sprawdź swoją skrzynkę odbiorczą i potwierdź subskrypcję tej listy oczekujących."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:97
msgid "Great! You have been added to the waitlist for this product. You will receive an email as soon as the item is back in stock."
msgstr "Udało się! Zostałeś dodany do listy oczekujących na ten produkt. Otrzymasz wiadomość e-mail, gdy tylko produkt będzie dostępny."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:151,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:86
msgid "Unsubscribe"
msgstr "Anuluj subskrypcję"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:65
msgid "Yes"
msgstr "Tak"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:65
msgid "No"
msgstr "Nie"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:21
msgid "You don't have any products in your waitlist yet."
msgstr "Nie masz produktów na liście oczekujących"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:42
msgid "Confirmed"
msgstr "Potwierdzono"

#. translators: %s User name.
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:19,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:17,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:17,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:12,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:10,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-subscription-confirmed.php:10
msgid "Hi, %s!"
msgstr "Cześć %s!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:31,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:19
msgid "Great news! The %s from your waitlist is now back in stock!"
msgstr "Dobra wiadomość! Produkt %s z Twojej listy oczekujących jest już dostępny!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:42,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:24
msgid "Click the link below to secure your purchase before it is gone!"
msgstr "Kliknij poniższy link, aby potwierdzić swój zakup, zanim zniknie!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:26,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:15
msgid "You have requested to join the waitlist for this item:"
msgstr "Poprosiłeś o wpisanie się na listę oczekujących na ten produkt:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:62
msgid "Click the button below to confirm your subscription. Once confirmed, we will notify you when the item is back in stock."
msgstr "Kliknij poniższy przycisk, aby potwierdzić subskrypcję. Po potwierdzeniu powiadomimy cię, gdy produkt będzie ponownie dostępny w magazynie."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:66
msgid "Confirm Subscription"
msgstr "Potwierdź subskrypcję"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:70,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:26
msgid "Please note, the confirmation period is 2 days."
msgstr "Okres potwierdzenia to 2 dni"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:77,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:65
msgid "If you don't want to receive any further notifications, please %s"
msgstr "Jeśli nie chcesz już otrzymywać powiadomień, prosimy %s"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:78,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:66
msgid "unsubscribe"
msgstr "anuluj subskrypcję"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:26,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-subscription-confirmed.php:15
msgid "You have been successfully added to the waitlist for the following item:"
msgstr "Zostałeś dodany do listy oczekujących na produkt:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:28,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:17
msgid "Product:"
msgstr "Produkt:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:29,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:18
msgid "Price:"
msgstr "Cena:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:30
msgid "Add to cart:"
msgstr "Dodaj do koszyka:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:19
msgid "Product link:"
msgstr "Link do produktu:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:23
msgid "Click the link below to confirm your subscription. Once confirmed, we will notify you when the item is back in stock."
msgstr "Kliknij poniższy link, aby potwierdzić subskrypcję. Po potwierdzeniu powiadomimy Cię, gdy produkt będzie ponownie dostępny."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:32,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-subscription-confirmed.php:25
msgid "If you don't want to receive any further notifications, please unsubscribe by clicking on this link - %s"
msgstr "Jeśli nie chcesz otrzymywać kolejnych powiadomień, zrezygnuj z subskrypcji, klikając ten link - %s"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:127
msgid "API URL"
msgstr "URL API"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:304
msgid "More information on how to generate an API key for ActiveCampaign can be found %shere%s."
msgstr "Więcej informacji o generowaniu klucza API znajdziesz %s tu%s"

#: static/js/dashboard/helpers/useUpsellModal.js:18
#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:111
msgid "Business"
msgstr "Biznes"

#: framework/helpers/exts-configs.php:377,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:4
msgid "Added to Cart Popup"
msgstr "Wyskakujące okienko Dodano do koszyka"

#: framework/helpers/exts-configs.php:378
msgid "Show a dynamic confirmation popup with product recommendations whenever items are added to the cart."
msgstr "Wyświetlaj dynamiczne wyskakujące okienko potwierdzenia z rekomendacjami produktów za każdym razem, gdy produkty są dodawane do koszyka."

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:15
msgid "Trigger Popup On"
msgstr "Wyzwalacz wyskakującego okienka"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:26
msgid "Product Page"
msgstr "Strona produktu"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:91
msgid "Description Length"
msgstr "Długość Opisu"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:102
msgid "Cart Button"
msgstr "Przycisk koszyka"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:112
msgid "Checkout Button"
msgstr "Przycisk kasy"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:121
msgid "Continue Shopping Button"
msgstr "Przycisk Kontynuowania zakupów"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:140
msgid "Shipping Info"
msgstr "Informacje o wysyłce"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:149
msgid "Tax Info"
msgstr "Informacje podatkowe"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:158
msgid "Total Info"
msgstr "Informacje całkowite"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:264
msgid "Suggested Products"
msgstr "Sugerowane produkty"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:292,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/suggested-products.php:51
msgid "Related Products"
msgstr "Powiązane produkty"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:293
msgid "Recently Viewed Products"
msgstr "Ostatnio oglądane produkty"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:368
msgid "Products Card Type"
msgstr "Produkty Typ karty"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:382
msgid "Products Visibility"
msgstr "Widoczność produktów"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:516
msgid "Popup Options"
msgstr "Opcje wyskakujących okienek"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:736,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:752,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:315,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:96
msgid "Popup Backdrop"
msgstr "Wyskakujące tło"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:768
msgid "Close Icon Size"
msgstr "Rozmiar ikony zamknięcia"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/view.php:95
msgid "Product succesfully added to your cart!"
msgstr "Produkt został pomyślnie dodany do koszyka!"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/view.php:102
msgid "Close Modal"
msgstr "Zamknij okno dialogowe"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:331,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:124
msgid "Popup Shadow"
msgstr "Wyskakujący cień"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:350,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:143
msgid "Popup Border Radius"
msgstr "Promień obramowania wyskakującego okienka"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/added-product.php:227
msgid "Shipping Cost"
msgstr "Koszt wysyłki"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/added-product.php:236
msgid "Tax Amount"
msgstr "Kwota podatku"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/added-product.php:245
msgid "Cart Total"
msgstr "Całkowita suma koszyka"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/product-actions.php:24
msgid "View Cart"
msgstr "Wyświetl koszyk"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/product-actions.php:34
msgid "Checkout"
msgstr "Kasa"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/product-actions.php:44
msgid "Continue Shopping"
msgstr "Kontynuuj zakupy"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/suggested-products.php:53
msgid "Recently Viewed"
msgstr "Ostatnio oglądane"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:340
msgid "More information on how to create a list in MailPoet can be found %shere%s."
msgstr "Więcej informacji na temat tworzenia list w MailPoet można znaleźć %stutaj%s."

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:9
msgid "Color Mode"
msgstr "Tryb koloru"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:17
msgid "One Color"
msgstr "Jeden kolor"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:18
msgid "Dual Color"
msgstr "Podwójny kolor"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:28
msgid "Colors"
msgstr "Zabarwienie"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:50
msgid "Color 1"
msgstr "Kolor 1"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:58
msgid "Color 2"
msgstr "Kolor 2"

#: framework/features/blocks/share-box/options.php:110,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:88
msgid "Tooltip"
msgstr "Etykieta (Podpowiedź)"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:108
msgid "Tooltip Text"
msgstr "Tekst podpowiedzi"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:111
msgid "{term_name}"
msgstr "{term_name}"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:122
msgid "Tooltip Image"
msgstr "Obraz podpowiedzi"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:135
msgid "Subtype"
msgstr "Podtyp"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:174
msgid "here"
msgstr "tutaj"

#: framework/premium/features/premium-header/items/contacts/options.php:21
msgid "Item Visibility"
msgstr "Widoczność pozycji"

#: framework/premium/features/premium-header/items/language-switcher/options.php:36
msgid "Hide Missing Language"
msgstr "Ukryj brakujący język"

#: framework/premium/extensions/woocommerce-extra/features/filters/filter-types/taxonomies.php:52
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Preview.js:94
msgid "Please wait until the lookup table is generated."
msgstr "Poczekaj na wygenerowanie tabeli odnośników."

#: framework/premium/extensions/woocommerce-extra/features/filters/filter-types/taxonomies.php:472
msgid "Collapse"
msgstr "Zwiń"

#: framework/premium/extensions/woocommerce-extra/features/filters/filter-types/taxonomies.php:473
msgid "Expand"
msgstr "Rozwiń"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/filter-presenter.php:64
msgid "category"
msgstr "kategoria"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/filter-presenter.php:108
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Preview.js:32
msgid "Find by %s"
msgstr "Znajdź według %s"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:434
msgid "Regenerate the product taxonomies lookup table"
msgstr "Regeneracja tabeli odnośników taksonomii produktów"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:435
msgid "This tool will regenerate the product taxonomies lookup table data from existing product(s) data. This process may take a while."
msgstr "To narzędzie zregeneruje dane tabeli wyszukiwania taksonomii produktów z istniejących danych produktów. Proces ten może chwilę potrwać."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:446
msgid "Product taxonomies lookup table data is regenerating"
msgstr "Dane tabeli odnośników taksonomii produktów są regenerowane"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:459
msgid "Regenerate"
msgstr "Regeneracja"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:453
msgid "Filling in progress (%d)"
msgstr "Napełnianie w toku (%d)"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:494
msgid "Resume the product taxonomies lookup table regeneration"
msgstr "Wznowienie regeneracji tabeli wyszukiwania taksonomii produktów"

#. translators: %1$s = count of products already processed.
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:497
msgid "This tool will resume the product taxonomies lookup table regeneration at the point in which it was aborted (%1$s products were already processed)."
msgstr "To narzędzie wznowi regenerację tabeli wyszukiwania taksonomii produktów w punkcie, w którym została ona przerwana (%1$s produktów zostało już przetworzonych)."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:516
msgid "Product taxonomies lookup table regeneration process has been resumed."
msgstr "Proces regeneracji tabeli wyszukiwania taksonomii produktów został wznowiony."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:518
msgid "Resume"
msgstr "Wznowienie"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:466
msgid "Abort the product taxonomies lookup table regeneration"
msgstr "Przerwanie regeneracji tabeli wyszukiwania taksonomii produktów"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:486
msgid "Product taxonomies lookup table regeneration process has been aborted."
msgstr "Proces regeneracji tabeli wyszukiwania taksonomii produktów został przerwany."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:488
msgid "Abort"
msgstr "Przerwać"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:158
msgid "Container Border Color"
msgstr "Kolor obramowania kontenera"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:189
msgid "Container Background Color"
msgstr "Kolor tła kontenera"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:143
msgid "API Version"
msgstr "Wersja API"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:268
msgid "More information on how to generate an API key for Brevo can be found %shere%s."
msgstr "Więcej informacji na temat generowania klucza API dla Brevo znajdziesz %stutaj%s."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:834
msgid "Hide Documentation Links"
msgstr "Ukryj linki do Dokumentacji"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:842
msgid "Hide Video Links"
msgstr "Ukryj linki do Video"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:104
msgid "Display the currently active filters."
msgstr "Wyświetla aktualnie aktywne filtry."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:47
msgid "Category 1"
msgstr "Kategoria 1"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:53
msgid "Category 2"
msgstr "Kategoria 2"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:66
msgid "Attribute 1"
msgstr "Atrybut 1"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:72
msgid "Attribute 2"
msgstr "Atrybut 2"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:12
msgid "Date"
msgstr "Data"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:10
msgid "Filter by Price Controls"
msgstr "Filtruj według kontroli cen"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:106
msgid "Filter by Price"
msgstr "Filtruj według ceny"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:11
msgid "Widget for filtering the WooCommerce products by price."
msgstr "Widżet do filtrowania produktów WooCommerce według ceny."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:58
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:473
msgid "Show Tooltip"
msgstr "Pokaż podpowiedź"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:70
msgid "Show Prices"
msgstr "Pokaż ceny"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Disabled.js:6
msgid "Please select a valid taxonomy."
msgstr "Wybierz prawidłową taksonomię."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:174
msgid "Filter Settings"
msgstr "Ustawienia filtra"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:253
msgid "Show Search Box"
msgstr "Pokaż pole wyszukiwania"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:496
msgid "Container Maximum Height"
msgstr "Maksymalna wysokość kontenera"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:55
msgid "Exclude Speciffic Items"
msgstr "Nie obejmuje określonych przedmiotów"

#: static/js/dashboard/VersionMismatch.js:62
#: static/js/notifications/VersionMismatchNotice.js:74
msgid "Update Blocksy Theme Now"
msgstr "Zaktualizuj motyw Blocksy już teraz"

#: static/js/dashboard/screens/DemoInstall.js:183
msgid "Your site is misconfigured and AJAX requests are not reaching your backend. Please click %shere%s to find the common causes and possible solutions to this.<br> Error code - %s"
msgstr "Twoja witryna jest źle skonfigurowana i żądania AJAX nie docierają do zaplecza. Kliknij %s tutaj %s , aby znaleźć typowe przyczyny i możliwe rozwiązania tego problemu.<br> Kod błędu - %s"

#: static/js/dashboard/screens/DemoInstall.js:201
msgid "Failed to retrieve starter sites list.<br> Error code - %s"
msgstr "Nie udało się pobrać listy witryn startowych.<br> Kod błędu - %s"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:129
msgid "Installing %s"
msgstr "Instalowanie %s"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:345
msgid "Preparing data..."
msgstr "Przygotowywanie danych..."

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:89
msgid "Required plan"
msgstr "Wymagany plan"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:13
msgid "All Plans"
msgstr "Wszystkie plany"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:15
msgid "Pro"
msgstr "Pro"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:182
msgid "All Builders"
msgstr "Wszystkie Kreatory"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:206
msgid "Search for a starter site..."
msgstr "Wyszukaj strony startowej..."

#: static/js/dashboard/screens/SiteExport.js:176
msgid "Export Site"
msgstr "Eksport Strony"

#: static/js/dashboard/screens/SiteExport.js:191
msgid "Starter site"
msgstr "Strona startowa"

#: static/js/dashboard/screens/SiteExport.js:203
msgid "Select a starter site"
msgstr "Wybierz witrynę startową"

#: static/js/editor/blocks/about-me/index.js:15
msgid "About Me Controls"
msgstr "Elementy sterujące O mnie"

#: static/js/editor/blocks/about-me/index.js:43
msgid "Showcase your personal information across your website."
msgstr "Zaprezentuj swoje dane osobowe na swojej stronie internetowej."

#: static/js/editor/blocks/breadcrumbs/Preview.js:63
msgid "Subpage"
msgstr "Podstrona"

#: static/js/editor/blocks/breadcrumbs/index.js:11
msgid "Breadcrumbs"
msgstr "Okruszki"

#: static/js/editor/blocks/breadcrumbs/index.js:12
msgid "Display navigational links, showing users their path within the site."
msgstr "Wyświetlaj linki nawigacyjne, pokazując użytkownikom ich ścieżkę w obrębie witryny."

#: static/js/editor/blocks/contact-info/index.js:15
msgid "Contact Info Controls"
msgstr "Elementy sterujące Informacje kontaktowe"

#: static/js/editor/blocks/contact-info/index.js:52
msgid "Display essential contact details to your visitors."
msgstr "Wyświetlaj istotne dane kontaktowe dla odwiedzających."

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:102
msgid "9:16"
msgstr "9:16"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:106
msgid "3:4"
msgstr "3:4"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:110
msgid "2:3"
msgstr "2:3"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:133
msgid "Width"
msgstr "Szerokość"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:157
msgid "Height"
msgstr "Wysokość"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:178
msgid "Scale"
msgstr "Skala"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:209
msgid "Resolution"
msgstr "Rozdzielczość"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:215
msgid "Select the size of the source image."
msgstr "Wybierz rozmiar obrazka źródłowego."

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:57
msgid "Image Settings"
msgstr "Ustawienia obrazka"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:77
msgid "Aspect Ratio"
msgstr "Proporcje"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:82
msgid "Original"
msgstr "Oryginał"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:90
msgid "16:9"
msgstr "16:9"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:94
msgid "4:3"
msgstr "4:3"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:98
msgid "3:2"
msgstr "3:2"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:100
msgid "Icon/Logo"
msgstr "Ikonka/Logo"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:138
msgid "Expand on click"
msgstr "Rozwiń po kliknięciu"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:151
msgid "Video thumbnail"
msgstr "Miniatura filmu"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:161
msgid "Image Hover Effect"
msgstr "Efekt obrazka po najechaniu"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:174
msgid "Zoom In"
msgstr "Powiększ"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:178
msgid "Zoom Out"
msgstr "Pomniejsz"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:202
msgid "Alternative Text"
msgstr "Tekst alternatywny"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:215
msgid "Describe the purpose of the image."
msgstr "Opisz cel tego obrazka."

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:221
msgid "Leave empty if decorative."
msgstr "Pozostaw puste, jeśli to dekoracja."

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:238
msgid "Image size"
msgstr "Rozmiar obrazu"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:271
msgid "Logo Gap"
msgstr "Logo Gap"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:323
msgid "Custom field fallback"
msgstr "Pole niestandardowe w trybie awaryjnym"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:343
msgid "Term additional class"
msgstr "Termin zajęć dodatkowych"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:350
msgid "Additional class for term items. Useful for styling."
msgstr "Dodatkowa klasa dla elementów terminowych. Przydatna do stylizacji."

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:45
msgid "Content Source"
msgstr "Źródło treści"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:51
msgid "Search for field"
msgstr "Wyszukaj pole"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:87
msgid "Image Source"
msgstr "Źródło obrazka"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:14
msgid "Change heading level"
msgstr "Zmień poziom nagłówka"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:38
msgid "Heading 1"
msgstr "Nagłówek 1"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:39
msgid "Heading 2"
msgstr "Nagłówek 2"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:40
msgid "Heading 3"
msgstr "Nagłówek 3"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:41
msgid "Heading 4"
msgstr "Nagłówek 4"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:42
msgid "Heading 5"
msgstr "Nagłówek 5"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:43
msgid "Heading 6"
msgstr "Nagłówek 6"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:44
msgid "Paragraph"
msgstr "Akapit"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:45
msgid "Span"
msgstr "Zakres"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:46
msgid "Div"
msgstr "Sekcja"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:101
msgid "Archive Image"
msgstr "Obrazek archiwalny"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:38
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:136
msgid "Stock Status"
msgstr "Stan magazynowy"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:24
#: static/js/editor/blocks/dynamic-data/preview-parts/wp/TermTextPreview.js:8
msgid "Term Title"
msgstr "Tytuł taksonomii"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:28
#: static/js/editor/blocks/dynamic-data/preview-parts/wp/TermTextPreview.js:9
msgid "Term Description"
msgstr "Opis taksonomii"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:32
msgid "Term Image"
msgstr "Obrazek taksonomii"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:36
#: static/js/editor/blocks/dynamic-data/preview-parts/wp/TermTextPreview.js:10
msgid "Term Count"
msgstr "Licznik taksonomii"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:50
msgid "Excerpt"
msgstr "Zajawka"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:55
msgid "Post Date"
msgstr "Data publikacji"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:65
msgid "Terms"
msgstr "Terminy"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:91
#: static/js/editor/blocks/dynamic-data/preview-parts/WpFieldPreview.js:46
msgid "Archive Title"
msgstr "Tytuł archiwum"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:96
#: static/js/editor/blocks/dynamic-data/preview-parts/WpFieldPreview.js:50
msgid "Archive Description"
msgstr "Opis archiwum"

#: static/js/editor/blocks/dynamic-data/index.js:17
msgid "Fetch and display content from various sources."
msgstr "Pobieranie i wyświetlanie treści z różnych źródeł."

#: static/js/editor/blocks/dynamic-data/index.js:33
msgid "Dynamic Title"
msgstr "Tytuł dynamiczny"

#: static/js/editor/blocks/dynamic-data/index.js:37
msgid "Dynamic Excerpt"
msgstr "Dynamiczny fragment"

#: static/js/editor/blocks/dynamic-data/index.js:41
msgid "Dynamic Post Date"
msgstr "Dynamiczna data wysłania"

#: static/js/editor/blocks/dynamic-data/index.js:45
msgid "Dynamic Comments"
msgstr "Dynamiczne komentarze"

#: static/js/editor/blocks/dynamic-data/index.js:49
msgid "Dynamic Terms"
msgstr "Warunki dynamiczne"

#: static/js/editor/blocks/dynamic-data/index.js:53
msgid "Dynamic Author"
msgstr "Dynamiczny autor"

#: static/js/editor/blocks/dynamic-data/index.js:57
msgid "Dynamic Featured Image"
msgstr "Dynamiczny obraz wyróżniony"

#: static/js/editor/blocks/dynamic-data/index.js:61
msgid "Dynamic Author Avatar"
msgstr "Dynamiczny awatar autora"

#: static/js/editor/blocks/dynamic-data/index.js:65
msgid "Dynamic Price"
msgstr "Dynamiczna cena"

#: static/js/editor/blocks/dynamic-data/index.js:69
msgid "Dynamic Stock Status"
msgstr "Dynamiczny stan zapasów"

#: static/js/editor/blocks/dynamic-data/index.js:73
msgid "Dynamic Brands"
msgstr "Marki dynamiczne"

#: static/js/editor/blocks/dynamic-data/index.js:77
msgid "Dynamic SKU"
msgstr "Dynamiczne SKU"

#: static/js/editor/blocks/dynamic-data/index.js:81
msgid "Dynamic Rating"
msgstr "Ocena dynamiczna"

#: static/js/editor/blocks/dynamic-data/index.js:85
msgid "Dynamic Term Title"
msgstr "Dynamiczny tytuł taksonomii"

#: static/js/editor/blocks/dynamic-data/index.js:89
msgid "Dynamic Term Description"
msgstr "Dynamiczny opis taksonomii"

#: static/js/editor/blocks/dynamic-data/index.js:93
msgid "Dynamic Term Count"
msgstr "Dynamiczna liczba taksonomii"

#: static/js/editor/blocks/dynamic-data/index.js:97
msgid "Dynamic Term Image"
msgstr "Dynamiczny obrazek taksonomii"

#: static/js/editor/blocks/dynamic-data/preview-parts/woo/RatingPreview.js:13
msgid "Rated %s out of 5"
msgstr "Oceniono %s na 5"

#: static/js/editor/blocks/dynamic-data/utils.js:15
msgid "Unknown"
msgstr "Nieznany"

#: static/js/editor/blocks/post-template/Edit.js:170
#: static/js/editor/blocks/tax-template/Edit.js:152
msgid "List view"
msgstr "Widok listy"

#: static/js/editor/blocks/post-template/Edit.js:176
#: static/js/editor/blocks/tax-template/Edit.js:158
msgid "Grid view"
msgstr "Widok siatki"

#: static/js/editor/blocks/post-template/Edit.js:209
#: static/js/editor/blocks/tax-template/Edit.js:190
msgid "Tablet Columns"
msgstr "Kolumny na tablecie"

#: static/js/editor/blocks/post-template/Edit.js:225
#: static/js/editor/blocks/tax-template/Edit.js:206
msgid "Mobile Columns"
msgstr "Kolumny na urządzeniu mobilnym"

#: static/js/editor/blocks/post-template/index.js:13
msgid "Post Template"
msgstr "Szablon wpisu"

#: static/js/editor/blocks/query/Edit.js:120
#: static/js/editor/blocks/tax-query/Edit.js:123
msgid "Reset layout"
msgstr "Resetuj układ"

#: static/js/editor/blocks/query/Edit.js:186
msgid "Pagination"
msgstr "Stronicowanie"

#: static/js/editor/blocks/query/Edit.js:208
#: static/js/editor/blocks/tax-query/Edit.js:189
msgid "Block ID"
msgstr "Identyfikator bloku"

#: static/js/editor/blocks/query/Edit.js:214
#: static/js/editor/blocks/tax-query/Edit.js:195
msgid "Please look at the documentation for more information on why this is useful."
msgstr "Zapoznaj się z dokumentacją, aby uzyskać więcej informacji na temat tego, dlaczego jest to przydatne."

#: static/js/editor/blocks/query/edit/PatternSelectionModal.js:82
#: static/js/editor/blocks/tax-query/edit/PatternSelectionModal.js:68
msgid "Choose a pattern"
msgstr "Wybierz wzorzec"

#: static/js/editor/blocks/query/edit/PatternSelectionModal.js:91
#: static/js/editor/blocks/tax-query/edit/PatternSelectionModal.js:77
msgid "Search for patterns"
msgstr "Szukaj wzorów"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:133
msgid "Publish Date"
msgstr "Data publikacji"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:173
msgid "Menu Order"
msgstr "Kolejność w menu"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:216
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:187
msgid "Order"
msgstr "Kolejność"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:224
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:195
msgid "Descending"
msgstr "Malejąco"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:232
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:203
msgid "Ascending"
msgstr "Rosnąco"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:275
msgid "Sticky Posts"
msgstr "Przypięte wpisy"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:299
msgid "Only"
msgstr "Tylko"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:318
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:222
msgid "Parameters"
msgstr "Parametry"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:105
#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:91
msgid "Create Custom Layout"
msgstr "Utwórz układ niestandardowy"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:37
#: static/js/editor/blocks/query/index.js:12
msgid "Advanced Posts"
msgstr "Zaawansowane posty"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:41
msgid "Inherit the Customizer layout, start with a pattern or create a custom layout"
msgstr "Odziedzicz układ Konfiguratora, zacznij od wzorca lub utwórz niestandardowy układ."

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:56
msgid "Inherit From Customizer"
msgstr "Dziedziczone z Konfiguratora"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:66
#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:57
msgid "Choose Pattern"
msgstr "Wybierz wzór"

#: static/js/editor/blocks/query/edit/TaxonomyControls.js:167
msgid "Search for a term"
msgstr "Wyszukaj termin"

#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:47
msgid "Include %s"
msgstr "Uwzględnij %s"

#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:75
msgid "Related"
msgstr "Podobne"

#: static/js/editor/blocks/query/index.js:13
#: static/js/editor/blocks/tax-query/index.js:13
msgid "Create advanced queries based on your specified criterias."
msgstr "Twórz zaawansowane zapytania na podstawie określonych kryteriów."

#: static/js/editor/blocks/search/Edit.js:163
msgid "Button Outside"
msgstr "Przycisk na zewnątrz"

#: static/js/editor/blocks/search/Edit.js:180
msgid "Use button with text"
msgstr "Użyj przycisku z tekstem"

#: static/js/editor/blocks/search/Edit.js:282
msgid "Button Icon Color"
msgstr "Kolor ikony przycisku"

#: static/js/editor/blocks/search/Edit.js:379
msgid "Dropdown Background Color"
msgstr "Kolor tła listy rozwijanej"

#: static/js/editor/blocks/search/Edit.js:400
msgid "Dropdown Shadow Color"
msgstr "Kolor cienia listy rozwijanej"

#: static/js/editor/blocks/search/Preview.js:24
msgid "Select category"
msgstr "Wybierz kategorię"

#: static/js/editor/blocks/search/index.js:16
msgid "Advanced Search"
msgstr "Wyszukiwanie zaawansowane"

#: static/js/editor/blocks/search/index.js:17
msgid "Quickly find specific content on your site."
msgstr "Szybkie wyszukiwanie określonych treści w witrynie."

#: static/js/editor/blocks/share-box/Edit.js:110
#: static/js/editor/blocks/socials/Edit.js:110
msgid "Icons Background Colors"
msgstr "Kolory tła ikon"

#: static/js/editor/blocks/share-box/Edit.js:142
#: static/js/editor/blocks/socials/Edit.js:142
msgid "Icons Border Colors"
msgstr "Kolory obramowania ikon"

#: static/js/editor/blocks/share-box/index.js:15
msgid "Share Box Controls"
msgstr "Elementy sterujące Share Box"

#: static/js/editor/blocks/share-box/index.js:45
msgid "Share content on social media, boosting visibility & engagement."
msgstr "Udostępniaj treści w mediach społecznościowych, zwiększając widoczność i zaangażowanie."

#: static/js/editor/blocks/socials/index.js:15
msgid "Socials Controls"
msgstr "Elementy sterujące Społeczności"

#: static/js/editor/blocks/socials/index.js:45
msgid "Display your social media profiles and boost the site engagement."
msgstr "Wyświetlaj swoje profile w mediach społecznościowych i zwiększaj zaangażowanie na stronie."

#: static/js/editor/blocks/socials/index.js:47
#: static/js/editor/blocks/widgets-wrapper/index.js:58
msgid "Socials"
msgstr "Społeczności"

#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:120
msgid "ID"
msgstr "ID"

#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:136
msgid "Count"
msgstr "Licznik"

#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:40
#: static/js/editor/blocks/tax-query/index.js:12
msgid "Advanced Taxonomies"
msgstr "Zaawansowane taksonomie"

#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:44
msgid "Start with a pattern or create a custom layout"
msgstr "Zacznij od wzorca lub utwórz niestandardowy układ"

#: static/js/editor/blocks/tax-template/index.js:13
msgid "Taxonomy Template"
msgstr "Szablon taksonomii"

#: static/js/editor/blocks/widgets-wrapper/Edit.js:81
msgid "Expandable Container"
msgstr "Kontener z możliwością rozbudowy"

#: static/js/editor/blocks/widgets-wrapper/index.js:40
msgid "Widgets Wrapper"
msgstr "Widgety Wrapper"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:41
msgid "Parameters options"
msgstr "Opcje parametrów"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:46
msgid "All options are currently hidden"
msgstr "Wszystkie opcje są aktualnie ukryte"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:82
msgid "All options reset"
msgstr "Zresetuj wszystkie opcje"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:87
msgid "Reset all"
msgstr "Zresetuj wszystko"

#: static/js/options/ConditionsManager/ExpireCondition.js:119
msgid "The expiration date cannot be set earlier than the start date."
msgstr "Data wygaśnięcia nie może być wcześniejsza niż data rozpoczęcia."

#: framework/features/demo-install.php:155,
#: framework/features/demo-install/content-installer.php:164,
#: framework/features/demo-install/content-installer.php:159,
#: framework/features/demo-install/demo-register.php:9
msgid "No demo name provided."
msgstr "Nie podano nazwy wersji demonstracyjnej."

#: framework/helpers/exts-configs.php:369,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:248,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:271,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:4
msgid "Stock Scarcity"
msgstr "Niedobór zapasów"

#: framework/helpers/exts-configs.php:370
msgid "Show the remaining stock of a product to create a sense of urgency and encourage your visitors to make a purchase."
msgstr "Pokaż pozostałe zapasy produktu, aby stworzyć poczucie pilności i zachęcić odwiedzających do dokonania zakupu."

#: framework/views/theme-mismatch.php:36
#: static/js/dashboard/VersionMismatch.js:19
#: static/js/notifications/VersionMismatchNotice.js:27
msgid "Action required - please update Blocksy theme to the latest version!"
msgstr "Wymagane działanie - zaktualizuj motyw Blocksy do najnowszej wersji!"

#: framework/views/theme-mismatch.php:41
#: static/js/dashboard/VersionMismatch.js:25
#: static/js/notifications/VersionMismatchNotice.js:35
msgid "We detected that you are using an outdated version of Blocksy theme."
msgstr "Wykryliśmy, że używasz nieaktualnej wersji motywu Blocksy."

#: framework/views/theme-mismatch.php:45
#: static/js/dashboard/VersionMismatch.js:32
#: static/js/notifications/VersionMismatchNotice.js:44
msgid "In order to take full advantage of all features the core has to offer - please install and activate the latest version of Blocksy theme."
msgstr "Aby w pełni wykorzystać wszystkie funkcje oferowane przez rdzeń, zainstaluj i aktywuj najnowszą wersję motywu Blocksy."

#: framework/extensions/newsletter-subscribe/customizer.php:94,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:127
msgid "Make Name Field Required"
msgstr "Ustaw pole Nazwa jako wymagane"

#: framework/extensions/trending/customizer.php:121
msgid "Tag"
msgstr "Etykieta"

#: framework/extensions/trending/customizer.php:142
msgid "Taxonomy Source"
msgstr "Źródło taksonomii"

#: framework/extensions/trending/customizer.php:216
msgid "Module Title Icon Source"
msgstr "Tytuł modułu Ikona Źródło"

#: framework/extensions/trending/customizer.php:303
msgid "Products Status"
msgstr "Status produktów"

#: framework/extensions/trending/customizer.php:307
msgid "On Sale"
msgstr "W promocji"

#: framework/extensions/trending/customizer.php:308
msgid "Top Rated"
msgstr "Najwyżej oceniane"

#: framework/extensions/trending/customizer.php:309
msgid "Best Sellers"
msgstr "Bestsellery"

#: framework/extensions/trending/customizer.php:409
msgid "Show Product Price"
msgstr "Pokaż cenę produktu"

#: framework/extensions/trending/customizer.php:423
msgid "Show Taxonomy"
msgstr "Pokaż taksonomię"

#: framework/extensions/trending/customizer.php:441
msgid "Taxonomy Style"
msgstr "Styl taksonomii"

#: framework/extensions/trending/customizer.php:450
msgid "Underline"
msgstr "Podkreślenie"

#: framework/extensions/trending/customizer.php:465,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:55
msgid "Image Width"
msgstr "Szerokość obrazku"

#: framework/extensions/trending/customizer.php:693,
#: framework/extensions/trending/customizer.php:825
msgid "Taxonomy Font"
msgstr "Czcionka taksonomii"

#: framework/extensions/trending/customizer.php:708,
#: framework/extensions/trending/customizer.php:748
msgid "Taxonomies Font Color"
msgstr "Kolor czcionki taksonomii"

#: framework/extensions/trending/customizer.php:780
msgid "Taxonomies Button Color"
msgstr "Kolor przycisku taksonomii"

#: framework/extensions/trending/customizer.php:835
msgid "Taxonomy Font Color"
msgstr "Kolor czcionki taksonomii"

#: framework/extensions/trending/customizer.php:860,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:248,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:476,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:494
msgid "Image Border Radius"
msgstr "Promień obramowania obrazku"

#: framework/features/blocks/blocks.php:19
msgctxt "Block pattern category"
msgid "Blocksy"
msgstr "Blocksy"

#: framework/features/blocks/blocks.php:24
msgid "Patterns that contain buttons and call to actions."
msgstr "Wzorce zawierające przyciski i wezwanie do działania."

#: framework/features/blocks/blocks.php:71
msgid "Home Page Text"
msgstr "Tekst na stronie głównej"

#: framework/features/demo-install/child-theme.php:9
msgid "Sorry, you don't have permission to install child themes."
msgstr "Przepraszamy, nie masz uprawnień do instalowania motywów potomnych."

#: framework/features/demo-install/content-eraser.php:23
msgid "Sorry, you don't have permission to erase content."
msgstr "Niestety, nie masz uprawnień do usuwania zawartości."

#: framework/features/demo-install/content-installer.php:69
msgid "Sorry, you don't have permission to install content."
msgstr "Przepraszamy, nie masz uprawnień do instalowania zawartości."

#: framework/features/demo-install/content-installer.php:195,
#: framework/features/demo-install/content-installer.php:189
msgid "No demo data found."
msgstr "Nie znaleziono danych demonstracyjnych."

#: framework/features/demo-install/content-installer.php:351,
#: framework/features/demo-install/content-installer.php:346
msgid "No pages to assign."
msgstr "Brak stron do przypisania."

#: framework/features/demo-install/install-finish.php:23
msgid "Sorry, you don't have permission to finish the installation."
msgstr "Przepraszamy, nie masz uprawnień do zakończenia instalacji."

#: framework/features/demo-install/options-import.php:38
msgid "Sorry, you don't have permission to install options."
msgstr "Przepraszamy, nie masz uprawnień do instalowania opcji."

#: framework/features/demo-install/options-import.php:50,
#: framework/features/demo-install/options-import.php:45,
#: framework/features/demo-install/options-import.php:80,
#: framework/features/demo-install/options-import.php:75,
#: framework/features/demo-install/widgets-import.php:48,
#: framework/features/demo-install/widgets-import.php:43
msgid "No demo to install"
msgstr "Brak wersji demonstracyjnej do zainstalowania"

#: framework/features/demo-install/plugins-uninstaller.php:9
msgid "Sorry, you don't have permission to uninstall plugins."
msgstr "Przepraszamy, nie masz uprawnień do odinstalowywania wtyczek."

#: framework/features/demo-install/required-plugins.php:37
msgid "Sorry, you don't have permission to install plugins."
msgstr "Przepraszamy, nie masz uprawnień do instalowania wtyczek."

#: framework/features/demo-install/required-plugins.php:49,
#: framework/features/demo-install/required-plugins.php:44
msgid "No plugins to install."
msgstr "Nie musisz instalować żadnych wtyczek."

#: framework/features/demo-install/widgets-import.php:36
msgid "Sorry, you don't have permission to install widgets."
msgstr "Przepraszamy, nie masz uprawnień do instalowania widżetów."

#: framework/features/demo-install/widgets-import.php:79,
#: framework/features/demo-install/widgets-import.php:73
msgid "No widgets to install."
msgstr "Nie musisz instalować żadnych widżetów."

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:98
msgid "Fields Gap"
msgstr "Pola Gap"

#: framework/features/blocks/about-me/options.php:21
msgid "User Source"
msgstr "Źródło użytkownika"

#: framework/features/blocks/about-me/options.php:26
msgid "Dynamic"
msgstr "Dynamicznie"

#: framework/features/blocks/about-me/options.php:37
msgid "User"
msgstr "Użytkownik"

#: framework/features/blocks/about-me/options.php:92
msgid "Image Shape"
msgstr "Kształt obrazu"

#: framework/features/blocks/about-me/options.php:104
msgid "Alignment"
msgstr "Wyrównanie"

#: framework/features/blocks/about-me/options.php:119,
#: framework/features/blocks/socials/options.php:19
msgid "Social Channels"
msgstr "Kanały społecznościowe"

#: framework/features/blocks/about-me/options.php:195,
#: framework/features/blocks/share-box/options.php:149,
#: framework/features/blocks/socials/options.php:101
msgid "Official"
msgstr "Oficjalne"

#: framework/features/blocks/about-me/view.php:197
msgid "View Profile"
msgstr "Zobacz profil"

#: framework/features/blocks/contact-info/options.php:44
msgid "Contact Information"
msgstr "Dane kontaktowe"

#: framework/features/blocks/dynamic-data/options.php:24,
#: framework/features/blocks/dynamic-data/options.php:49
msgid "Date type"
msgstr "Typ daty"

#: framework/features/blocks/dynamic-data/options.php:31
msgid "Published Date"
msgstr "Data publikacji"

#: framework/features/blocks/dynamic-data/options.php:32
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:141
msgid "Modified Date"
msgstr "Data modyfikacji"

#: framework/features/blocks/dynamic-data/options.php:39
msgid "Default format"
msgstr "Domyślny format"

#: framework/features/blocks/dynamic-data/options.php:41
msgid "Example: January 24, 2022"
msgstr "Przykład: 24 stycznia 2022 r."

#: framework/features/blocks/dynamic-data/options.php:75
msgid "Custom date format"
msgstr "Niestandardowy format daty"

#: framework/features/blocks/dynamic-data/options.php:79
msgid "Enter a date or time"
msgstr "Wprowadź datę lub godzinę"

#: framework/features/blocks/dynamic-data/options.php:96,
#: framework/features/blocks/dynamic-data/options.php:97,
#: framework/features/blocks/dynamic-data/views/wp-field.php:194
msgid "No comments"
msgstr "Brak komentarzy"

#: framework/features/blocks/dynamic-data/options.php:102,
#: framework/features/blocks/dynamic-data/options.php:103,
#: framework/features/blocks/dynamic-data/views/wp-field.php:195
msgid "One comment"
msgstr "Jeden komentarz"

#: framework/features/blocks/dynamic-data/options.php:108
msgid "Multiple comments"
msgstr "Wiele komentarzy"

#: framework/features/blocks/dynamic-data/options.php:120
msgid "Separator"
msgstr "Separator"

#: framework/features/blocks/dynamic-data/options.php:132
msgid "Author Field"
msgstr "Pole autora"

#: framework/features/blocks/dynamic-data/options.php:139
msgid "Nickname"
msgstr "Pseudonim"

#: framework/features/blocks/dynamic-data/options.php:138
msgid "Display Name"
msgstr "Wyświetlana nazwa"

#: framework/features/blocks/dynamic-data/options.php:140
msgid "First Name"
msgstr "Imię"

#: framework/features/blocks/dynamic-data/options.php:141
msgid "Last Name"
msgstr "Nazwisko"

#: framework/features/blocks/dynamic-data/options.php:172
msgid "Link to post"
msgstr "Link do postu"

#: framework/features/blocks/dynamic-data/options.php:176
msgid "Link to author page"
msgstr "Link do strony autora"

#: framework/features/blocks/dynamic-data/options.php:180
msgid "Link to term page"
msgstr "Link do strony terminu"

#: framework/features/blocks/dynamic-data/options.php:184
msgid "Link to archive page"
msgstr "Link do strony archiwum"

#: framework/features/blocks/dynamic-data/options.php:197
msgid "Open in new tab"
msgstr "Otwórz w nowej karcie"

#: framework/features/blocks/dynamic-data/options.php:203
msgid "Link Rel"
msgstr "Odsyłacz do odnośnika"

#: framework/features/blocks/dynamic-data/options.php:220
msgid "Terms accent color"
msgstr "Kolor akcentujący warunki"

#: framework/features/blocks/search/options.php:147,
#: framework/features/blocks/search/view.php:265
msgid "Select Category"
msgstr "Wybierz kategorię"

#: framework/features/blocks/search/options.php:175,
#: framework/premium/features/premium-header/items/search-input/options.php:164
msgid "Taxonomy Children"
msgstr "Taksonomia Dzieci"

#: framework/features/blocks/search/options.php:205,
#: framework/premium/features/premium-header/items/search-input/options.php:192
msgid "Search Through Taxonomies"
msgstr "Wyszukiwanie poprzez taksonomie"

#: framework/features/blocks/search/options.php:209,
#: framework/premium/features/premium-header/items/search-input/options.php:196
msgid "Search through taxonomies from selected custom post types."
msgstr "Przeszukuj taksonomie z wybranych własnych typów treści."

#: framework/features/blocks/share-box/options.php:15
msgid "Share Icons"
msgstr "Udostępnij ikony"

#: framework/features/blocks/share-box/options.php:43
msgid "Reddit"
msgstr "Reddit"

#: framework/features/blocks/share-box/options.php:49
msgid "Hacker News"
msgstr "Hacker News"

#: framework/features/blocks/share-box/options.php:67
msgid "Telegram"
msgstr "Telegram"

#: framework/features/blocks/share-box/options.php:73
msgid "Viber"
msgstr "Viber"

#: framework/features/blocks/share-box/options.php:79
msgid "WhatsApp"
msgstr "WhatsApp"

#: framework/features/blocks/share-box/options.php:85
msgid "Flipboard"
msgstr "Flipboard"

#: framework/features/blocks/share-box/options.php:91
msgid "Line"
msgstr "Linia"

#: framework/features/conditions/rules/archive-loop.php:6
msgid "Archive Item with Taxonomy ID"
msgstr "Element archiwum z identyfikatorem taksonomii"

#: framework/features/conditions/rules/archive-loop.php:13
msgid "WooCommerce Archive Item with Taxonomy ID"
msgstr "Pozycja archiwum WooCommerce z identyfikatorem taksonomii"

#: framework/features/conditions/rules/archive-loop.php:19
msgid "Archive Loop Speciffic"
msgstr "Specyfika pętli archiwum"

#: framework/features/conditions/rules/posts.php:10
msgid "Post Archives"
msgstr "Archiwum wpisów"

#: framework/features/conditions/rules/woo.php:54
msgid "Single Product ID"
msgstr "Identyfikator pojedynczego produktu"

#: framework/features/conditions/rules/woo.php:59
msgid "Single Product with Taxonomy ID"
msgstr "Pojedynczy produkt z identyfikatorem taksonomii"

#: framework/premium/extensions/woocommerce-extra/utils.php:141
msgid "Private: %s"
msgstr "Prywatne: %s"

#: framework/premium/extensions/woocommerce-extra/utils.php:131
msgid "Protected: %s"
msgstr "Chronione: %s"

#: framework/premium/features/content-blocks/hooks-manager.php:733
msgid "WooCommerce Single Product"
msgstr "Pojedynczy produkt WooCommerce"

#: framework/premium/features/media-video/options.php:119
msgid "Video Size"
msgstr "Rozmiar Video"

#: framework/premium/features/media-video/options.php:127
#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:192
msgid "Contain"
msgstr "Zawiera"

#: framework/premium/features/media-video/options.php:128
#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:187
msgid "Cover"
msgstr "Okładka"

#. translators: placeholder here means the actual URL.
#: framework/premium/features/media-video/options.php:132
msgid "Choose how the video will fill its container. More info about this can be found %shere%s."
msgstr "Wybierz sposób, w jaki wideo wypełni swój kontener. Więcej informacji na ten temat można znaleźć %stutaj%s."

#: framework/features/blocks/dynamic-data/views/avatar-field.php:30
msgid "%s Avatar"
msgstr "Awatar %s"

#: framework/features/blocks/query/block-patterns/posts-layout-1.php:4
msgid "Posts - Layout 1"
msgstr "Posty - Układ 1"

#: framework/features/blocks/query/block-patterns/posts-layout-2.php:4
msgid "Posts - Layout 2"
msgstr "Posty - Układ 2"

#: framework/features/blocks/query/block-patterns/posts-layout-3.php:4
msgid "Posts - Layout 3"
msgstr "Posty - Układ 3"

#: framework/features/blocks/query/block-patterns/posts-layout-4.php:4
msgid "Posts - Layout 4"
msgstr "Posty - Układ 4"

#: framework/features/blocks/query/block-patterns/posts-layout-5.php:4
msgid "Posts - Layout 5"
msgstr "Wpisy - Układ 5"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-1.php:4
msgid "Taxonomies - Layout 1"
msgstr "Taksonomie - Układ 1"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-2.php:4
msgid "Taxonomies - Layout 2"
msgstr "Taksonomie - Układ 2"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-3.php:4
msgid "Taxonomies - Layout 3"
msgstr "Taksonomie - Układ 3"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-4.php:4
msgid "Taxonomies - Layout 4"
msgstr "Taksonomie - Układ 4"

#: framework/features/header/items/account/options.php:15
msgid "Action Link"
msgstr "Odnośnik akcji"

#: framework/features/header/items/account/options.php:49
msgid "Additional User Info"
msgstr "Dodatkowe informacje o użytkowniku"

#: framework/features/header/items/account/options.php:53
msgid "Available fields: {user_email}, {user_name}, {user_role}"
msgstr "Dostępne pola: {user_email}, {user_name}, {user_role}."

#: framework/features/header/items/account/options.php:142
msgid "Menu"
msgstr "Menu"

#: framework/features/header/items/account/options.php:717
msgid "Items Hover Effect"
msgstr "Efekt najechania na elementy"

#: framework/features/header/items/account/options.php:727
msgid "Boxed Color"
msgstr "Kolor pudełka"

#: framework/features/header/items/account/options.php:1945
msgid "Link Active"
msgstr "Aktywny odnośnik"

#: framework/premium/features/content-blocks/options/popup.php:262
msgid "Close Trigger Delay"
msgstr "Opóźnienie wyzwalania zamknięcia"

#: framework/premium/features/content-blocks/options/popup.php:269
msgid "Set the close delay time (in seconds) after the form submit action is detected."
msgstr "Ustawia czas opóźnienia zamknięcia (w sekundach) po wykryciu akcji przesłania formularza."

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:100,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:813
msgid "Featured Icon/Logo"
msgstr "Wyróżniona ikona/logo"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:570
msgid "Upvote"
msgstr "Głos w górę"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:584
msgid "Downvote"
msgstr "Głos w dół"

#: framework/premium/extensions/woocommerce-extra/features/brands/brands-import-export.php:17,
#: framework/premium/extensions/woocommerce-extra/features/brands/brands-import-export.php:69
msgid "Blocksy Brands"
msgstr "Marki Blocksy"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/select.php:18
msgid "Choose page"
msgstr "Wybierz stronę"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/select.php:33
msgid "Choose a custom thank you page for this product."
msgstr "Wybierz niestandardową stronę z podziękowaniem dla tego produktu."

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:49
msgid "Product Image Visibility"
msgstr "Widoczność obrazu produktu"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:87
msgid "Product Price & Stock Visibility"
msgstr "Cena produktu i widoczność zapasów"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:42
msgid "Trigger Icon Type"
msgstr "Typ ikony wyzwalacza"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:77
msgid "Trigger Visibility"
msgstr "Widoczność wyzwalacza"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:107
msgid "Trigger Label"
msgstr "Etykieta wyzwalacza"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:233
msgid "Panel Default State"
msgstr "Domyślny stan panelu"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:240
msgid "Closed"
msgstr "Zamknięte"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:241
msgid "Opened"
msgstr "Otwarty"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:488
msgid "Panel AJAX Reveal"
msgstr "Panel AJAX Reveal"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:140
msgid "Autoplay Gallery"
msgstr "Galeria autoodtwarzania"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:152
msgid "Delay (in seconds)"
msgstr "Opóźnienie (w sekundach)"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:219
msgid "Columns Spacing"
msgstr "Rozstaw kolumn"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:247,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:275
msgid "Arrows Visibility"
msgstr "Widoczność strzałek"

#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:11
msgid "Prev/Next Arrow"
msgstr "Strzałka przed/następna"

#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:41
msgid "Prev/Next Background"
msgstr "Poprzednie/Następne tło"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:372,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:160
msgid "Add {items} more items to get free shipping!"
msgstr "Dodaj {items} więcej przedmiotów, aby otrzymać darmową wysyłkę!"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:52
msgid "Count Criteria"
msgstr "Kryteria liczenia"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:69,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:59,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:246,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/price-filter.php:299,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:53,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:34,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:34
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:128
msgid "Price"
msgstr "Cena"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:60
msgid "Items"
msgstr "Towary"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:86
msgid "Goal Items"
msgstr "Liczba docelowa"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:90
msgid "Amount of items the client has to buy in order to get free shipping."
msgstr "Ilość przedmiotów, które klient musi kupić, aby otrzymać darmową wysyłkę."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:168
msgid "You can use dynamic code tags such as {items} inside this option."
msgstr "Wewnątrz tej opcji można używać dynamicznych znaczników kodu, takich jak {items}."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:195,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:62
msgid "Bar Color"
msgstr "Kolor paska informacyjnego"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:187,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:29
msgid "🚨 Hurry up! Only {items} units left in stock!"
msgstr "🚨 Pospiesz się! W magazynie pozostały tylko {items} sztuk!"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:15
msgid "Stock Threshold"
msgstr "Próg zapasów"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:23
msgid "Show the stock scarcity module when product stock is below this number."
msgstr "Pokaż moduł niedoboru zapasów, gdy zapasy produktu są poniżej tej liczby."

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:27
msgid "Message"
msgstr "Wiadomość"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:43
msgid "Bar Height"
msgstr "Wysokość Paska"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:686
msgid "Swatches removed"
msgstr "Próbki usunięte"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:693
msgid "Swatches saved"
msgstr "Zapisane próbki"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:755
msgid "Custom Attributes"
msgstr "Atrybuty niestandardowe"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:761,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:800,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:873
msgid "Terms Limit"
msgstr "Limit warunków"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:764,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:803
msgid "Set how many terms you want to display in this attribute."
msgstr "Określ, ile terminów ma być wyświetlanych w tym atrybucie."

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:774,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:813,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:887
#: static/js/editor/blocks/query/Edit.js:178
#: static/js/editor/blocks/tax-query/Edit.js:165
msgid "Limit"
msgstr "Limit"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:876
msgid "Set how many terms you want to display in each attribute."
msgstr "Określ, ile terminów ma być wyświetlanych w każdym atrybucie."

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:179,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:462
msgid "Mixed Swatches"
msgstr "Mieszane próbki"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:233
msgid "Generate Variation URL"
msgstr "Generowanie linku dla wariacji"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:239
msgid "Generate a shareable single product page URL with pre-selected variation attributes."
msgstr "Wygeneruj udostępniany adres URL pojedynczej strony produktu z wybranymi wcześniej atrybutami odmiany."

#: static/js/options/ConditionsManager/SingleCondition.js:446
msgid "Display if query string is present in URL"
msgstr "Wyświetlaj, jeśli ciąg zapytania jest obecny w adresie URL"

#: static/js/options/DisplayCondition.js:62
msgid "Add Conditions"
msgstr "Dodaj warunki"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:68
msgid "Upgrade to the agency plan and get instant access to this starter site and many other features."
msgstr "Przejdź na plan agencyjny i uzyskaj natychmiastowy dostęp do tej strony startowej i wielu innych funkcji."

#: static/js/dashboard/screens/Extensions/CurrentExtension.js:316
msgid "Documentation"
msgstr "Dokumentacja"

#: static/js/dashboard/screens/Extensions/CurrentExtension.js:346
msgid "Manage"
msgstr "Zarządzanie"

#: static/js/dashboard/screens/Extensions/CurrentExtension.js:86
msgid "Video Tutorial"
msgstr "Samouczek wideo"

#: static/js/options/ConditionsManager.js:207
msgid "Advanced Mode"
msgstr "Tryb zaawansowany"

#: static/js/options/ConditionsManager/PostIdPicker.js:78
msgid "Select product"
msgstr "Wybierz produkt"

#: static/js/options/ConditionsManager/ScheduleDate.js:149
msgid "Monday"
msgstr "Poniedziałek"

#: static/js/options/ConditionsManager/ScheduleDate.js:154
msgid "Tuesday"
msgstr "Wtorek"

#: static/js/options/ConditionsManager/ScheduleDate.js:159
msgid "Wednesday"
msgstr "Środa"

#: static/js/options/ConditionsManager/ScheduleDate.js:164
msgid "Thursday"
msgstr "Czwartek"

#: static/js/options/ConditionsManager/ScheduleDate.js:169
msgid "Friday"
msgstr "Piątek"

#: static/js/options/ConditionsManager/ScheduleDate.js:174
msgid "Saturday"
msgstr "Sobota"

#: static/js/options/ConditionsManager/ScheduleDate.js:179
msgid "Sunday"
msgstr "Niedziela"

#: static/js/options/ConditionsManager/ScheduleDate.js:21
msgid "Mon"
msgstr "Pon."

#: static/js/options/ConditionsManager/ScheduleDate.js:211
msgid "Start Time"
msgstr "Czas rozpoczęcia"

#: static/js/options/ConditionsManager/ScheduleDate.js:22
msgid "Tue"
msgstr "Wt."

#: static/js/options/ConditionsManager/ScheduleDate.js:23
msgid "Wed"
msgstr "Śr."

#: static/js/options/ConditionsManager/ScheduleDate.js:234
msgid "Stop Time"
msgstr "Czas zatrzymania"

#: static/js/options/ConditionsManager/ScheduleDate.js:24
msgid "Thu"
msgstr "Czw."

#: static/js/options/ConditionsManager/ScheduleDate.js:25
msgid "Fri"
msgstr "Pt."

#: static/js/options/ConditionsManager/ScheduleDate.js:26
msgid "Sat"
msgstr "Sob."

#: static/js/options/ConditionsManager/ScheduleDate.js:27
msgid "Sun"
msgstr "Niedz."

#: static/js/options/ConditionsManager/ScheduleDate.js:58
msgid "Every day"
msgstr "Codziennie"

#: static/js/options/ConditionsManager/ScheduleDate.js:66
msgid "Only weekends"
msgstr "Tylko weekendy"

#: static/js/options/ConditionsManager/ScheduleDate.js:74
msgid "Only weekdays"
msgstr "Tylko dni powszednie"

#: static/js/options/ConditionsManager/SingleCondition.js:325
msgid "Select sub field"
msgstr "Wybierz pole podrzędne"

#: static/js/options/ConditionsManager/SingleCondition.js:378
msgid "Display based on referer domain"
msgstr "Wyświetlanie na podstawie domeny odsyłającej"

#: static/js/options/ConditionsManager/SingleCondition.js:412
msgid "Display if cookie is present"
msgstr "Wyświetl, jeśli plik cookie jest obecny"

#: static/js/dashboard/helpers/useUpsellModal.js:42
msgid "Upgrade to the agency plan and get instant access to this and many other features."
msgstr "Przejdź na plan agencyjny i uzyskaj natychmiastowy dostęp do tych i wielu innych funkcji."

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:53
msgid "This is a Pro starter site"
msgstr "To jest strona startowa dla wersji Pro"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:54
msgid "Upgrade to any pro plan and get instant access to this starter site and many other features."
msgstr "Uaktualnij do dowolnego planu pro i uzyskaj natychmiastowy dostęp do tej strony startowej i wielu innych funkcji."

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:61
msgid "Upgrade to the business or agency plan and get instant access to this starter site and many other features."
msgstr "Uaktualnij do planu biznesowego lub agencyjnego i uzyskaj natychmiastowy dostęp do tej witryny startowej i wielu innych funkcji."

#: static/js/dashboard/helpers/useProExtensionInFree.js:14
msgid "This is a Pro extension"
msgstr "To jest rozszerzenie Pro"

#: static/js/dashboard/helpers/useUpsellModal.js:103
#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:115
msgid "Agency"
msgstr "Agencja"

#: static/js/dashboard/helpers/useUpsellModal.js:11
#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:14
msgid "Free"
msgstr "Darmowe"

#: static/js/dashboard/helpers/useUpsellModal.js:122
msgid "Compare Plans"
msgstr "Porównaj plany subskrypcji"

#: static/js/dashboard/helpers/useUpsellModal.js:17
#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:107
msgid "Personal"
msgstr "Osobiste"

#: static/js/dashboard/helpers/useUpsellModal.js:27
msgid "This is a Pro feature"
msgstr "Jest to funkcja Pro"

#: static/js/dashboard/helpers/useUpsellModal.js:28
msgid "Upgrade to any pro plan and get instant access to this and many other feature."
msgstr "Uaktualnij do dowolnego planu pro i uzyskaj natychmiastowy dostęp do tej i wielu innych funkcji."

#: static/js/dashboard/helpers/useUpsellModal.js:35
msgid "Upgrade to the business or agency plan and get instant access to this and many other features."
msgstr "Uaktualnij do planu biznesowego lub agencyjnego i uzyskaj natychmiastowy dostęp do tych i wielu innych funkcji."

#: static/js/dashboard/NoTheme.js:31
msgid "In order to take full advantage of all features it has to offer - please install and activate the Blocksy theme also."
msgstr "Aby w pełni wykorzystać wszystkie funkcje, które ma do zaoferowania - zainstaluj i aktywuj motyw Blocksy."

#: static/js/dashboard/NoTheme.js:65
msgid "Install and activate the Blocksy theme"
msgstr "Zainstaluj i aktywuj motyw Blocksy"

#: static/js/dashboard/NoTheme.js:18
msgid "Action Required - Install Blocksy Theme"
msgstr "Wymagane działanie - zainstaluj motyw Blocksy"

#: static/js/dashboard/NoTheme.js:24
msgid "Blocksy Companion is the complementary plugin to Blocksy theme. It adds a bunch of great features to the theme and acts as an unlocker for the Blocksy Pro package."
msgstr "Blocksy Companion to dodatkowa wtyczka do motywu Blocksy. Dodaje wiele wspaniałych funkcji i odblokowuje pakiet Blocksy Pro."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:423
msgid "Show Image Frame"
msgstr "Pokaż ramkę obrazu"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:444
msgid "Show Label"
msgstr "Pokaż etykietę"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:457
msgid "Show Counter"
msgstr "Pokaż licznik"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:164
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:82
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:526
msgid "Show Reset Button"
msgstr "Pokaż przycisk resetowania"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:168
msgid "Show or hide reset filter button."
msgstr "Pokaż lub ukryj przycisk resetowania filtra."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:9
msgid "Shop Filters Controls"
msgstr "Elementy kontrolne Filtry sklepowe"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:135
msgid "Shop Filters"
msgstr "Filtry sklepu"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:10
msgid "Widget for filtering the WooCommerce products loop by category, attribute or brand."
msgstr "Widżet do filtrowania pętli produktów WooCommerce według kategorii, atrybutu lub marki."

#: framework/premium/static/js/blocks/ContentBlock.js:26
msgid "Insert a specific Content Block anywhere on the site."
msgstr "Wstaw określony blok treści w dowolnym miejscu na stronie."

#: framework/premium/static/js/hooks/CreateHook.js:156
msgid "Nothing Found Template"
msgstr "Szablon Nic nie znaleziono"

#: framework/premium/static/js/hooks/CreateHook.js:164
msgid "Maintenance Template"
msgstr "Szablon konserwacji"

#: framework/premium/static/js/media-video/components/EditVideoMeta.js:44
msgid "Video Options"
msgstr "Opcje wideo"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:75
msgid "Display order overhiew section."
msgstr "Wyświetla sekcję przeglądu zamówień."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:90
msgid "Order Details"
msgstr "Szczegóły zamówienia"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:94
msgid "Display order details section."
msgstr "Wyświetla sekcję szczegółów zamówienia."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:177
msgid "Filter By"
msgstr "Filtruj według"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:92
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:221
msgid "Attribute"
msgstr "Atrybut"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:11
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:145
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:192
msgid "Display Type"
msgstr "Typ wyświetlacza"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:154
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:200
msgid "List"
msgstr "Lista"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:387
msgid "Image Aspect Ratio"
msgstr "Proporcje obrazu"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:408
msgid "Image Max width"
msgstr "Maksymalna szerokość obrazu"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:233
msgid "Multiple Selections"
msgstr "Wielokrotny wybór"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:237
msgid "Allow selecting multiple items in a filter."
msgstr "Zezwalaj na zaznaczanie wielu elementów w filtrze."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:118
msgid "Select attribute"
msgstr "Wybierz atrybut"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:305
msgid "Show Hierarchy"
msgstr "Pokaż hierarchię"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:321
msgid "Expandable"
msgstr "Rozszerzalny"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:336
#: static/js/editor/blocks/widgets-wrapper/Edit.js:95
msgid "Expanded by Default"
msgstr "Rozszerzony domyślnie"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:283
msgid "Show Checkboxes"
msgstr "Pokaż pola wyboru"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:364
msgid "Show Brands Images"
msgstr "Pokaż obrazy marek"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:368
msgid "Show Swatches"
msgstr "Pokaż próbki"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/VariableTab.js:30
msgid "Upload only the %s.woff2%s or %s.ttf%s font file formats. Don't convert variable fonts by yourself. Ask the font provider to hand a correct file or the %svariable%s font won't work."
msgstr "Prześlij tylko pliki w formacie plików czcionek %s.woff2%s lub %s.ttf%s. Nie konwertuj zmiennych czcionek samodzielnie. Poproś dostawcę czcionek o przesłanie poprawnego pliku, w przeciwnym razie czcionka %szmienna%s nie będzie działać."

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/AdvancedTab.js:54
msgid "Preload Subsets"
msgstr "Preload Subsets"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/GeneralTab.js:10
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:65
msgid "Select Variations"
msgstr "Wybierz warianty"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:255
msgid "Local Google Fonts Settings"
msgstr "Ustawienia lokalnych czcionek Google"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:267
msgid "Select font"
msgstr "Wybierz czcionkę"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:335
msgid "Download Font"
msgstr "Pobierz czcionkę"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:217
msgid "Save Font"
msgstr "Zapisz czcionkę"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:608
msgid "Companion Plugin Details"
msgstr "Szczegóły dotyczące wtyczki Companion"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Disabled.js:7
msgid "Please select a valid attribute."
msgstr "Wybierz prawidłowy atrybut."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:41
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:130
msgid "Exclude %s"
msgstr "Wyklucz %s"

#: framework/features/conditions/rules/woo.php:35
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:31
msgid "Product Attributes"
msgstr "Atrybuty produktu"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/CustomerDetails.js:10
msgid "Billing address"
msgstr "Adres rozliczeniowy"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/CustomerDetails.js:34
msgid "Shipping address"
msgstr "Adres wysyłki"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:64
msgid "Subtotal"
msgstr "Kwota"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:77
msgid "Shipping"
msgstr "Wysyłka"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:79
msgid "Free shipping"
msgstr "Bezpłatna wysyłka"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:83
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:42
msgid "Payment method"
msgstr "Metoda płatności"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:85
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:43
msgid "Cash on delivery"
msgstr "Za pobraniem"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:88
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:28
msgid "Total"
msgstr "Łącznie"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:8
msgid "Order number"
msgstr "Numer zamówienia"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:108
msgid "Customer Details"
msgstr "Szczegóły Klienta"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:112
msgid "Display customer details section."
msgstr "Wyświetla sekcję szczegółów klienta."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:71
msgid "Order Overview"
msgstr "Przegląd zamówień"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:89
msgid "Once you insert your %sProject ID%s and click the \"Fetch Fonts\" button, your fonts will become available in all theme’s typography options."
msgstr "Po wstawieniu %sidentyfikatora projektu%s i kliknięciu przycisku \"Pobierz czcionki\", czcionki staną się dostępne we wszystkich opcjach typografii motywu."

#: framework/premium/extensions/custom-fonts/dashboard-static/js/CustomFonts.js:135
#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:320
msgid "Upload Font"
msgstr "Prześlij czcionkę"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/PreloadTab.js:14
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/AdvancedTab.js:19
msgid "Preload Variations"
msgstr "Wstępnie załaduj wariacje"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:356
msgid "Simple Font"
msgstr "Prosta czcionka"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:360
msgid "Variable Font"
msgstr "Zmienna czcionka"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:364
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:116
msgid "Preload"
msgstr "Wstępnie załaduj"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:174
#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:20
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:342
msgid "Available Fonts"
msgstr "Dostępne czcionki"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:322
msgid "More information on how to generate an API key for Campaign Monitor can be found %shere%s."
msgstr "Więcej informacji na temat generowania klucza API dla Campaign Monitor znajdziesz %stutaj%s."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:83
msgid "Connect Newsletter Provider"
msgstr "Połącz się z dostawcą Newslettera"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:96
msgid "Provider"
msgstr "Dostawca"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/ListPicker.js:91
msgid "Fetching..."
msgstr "Pobieranie..."

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:164
msgid "Please enter a valid Project ID to get all fonts."
msgstr "Wprowadź prawidłowy identyfikator projektu, aby uzyskać wszystkie czcionki."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:254
msgid "This provider is used only for testing purposes. It doesnt register any real subscribers."
msgstr "Ten dostawca jest używany wyłącznie do celów testowych. Nie rejestruje on żadnych prawdziwych subskrybentów."

#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:486,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:520,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:555,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:589,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:428,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:463,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:494,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:524
msgid "Badge Color"
msgstr "Kolor znaczka"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:221
#: static/js/editor/blocks/search/Edit.js:281
msgid "Button Text Color"
msgstr "Kolor tekstu przycisku"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/index.js:17
msgid "Newsletter Controls"
msgstr "Kontrola Newslettera"

#: framework/features/blocks/search/options.php:126,
#: framework/premium/features/premium-header/items/search-input/options.php:119
msgid "Live Results Product Status"
msgstr "Status produktu w wynikach na żywo"

#: framework/features/blocks/search/options.php:138,
#: framework/premium/features/premium-header/items/search-input/options.php:132
msgid "Taxonomy Filter"
msgstr "Filtr taksonomii"

#: framework/features/blocks/search/options.php:156,
#: framework/premium/features/premium-header/items/search-input/options.php:144
msgid "Filter Visibility"
msgstr "Widoczność filtra"

#: framework/premium/features/premium-header/items/search-input/options.php:795
msgid "Input Border Radius"
msgstr "Promień granicy wejścia"

#: framework/premium/features/premium-header/items/search-input/options.php:823
msgid "Dropdown Font"
msgstr "Czcionka rozwijanego menu"

#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:604
msgid "You don't have any products in your compare list yet."
msgstr "Na liście porównawczej nie ma jeszcze żadnych produktów."

#: framework/features/blocks/dynamic-data/views/woo-field.php:25,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:481
#: static/js/editor/blocks/dynamic-data/preview-parts/woo/StockPreview.js:6
msgid "In Stock"
msgstr "W magazynie"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:12,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:37,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/inline-filter.php:78,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/list-filter.php:109
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:198
msgid "Active Filters"
msgstr "Aktywne filtry"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:22
msgid "Active Filters Label"
msgstr "Etykieta aktywnych filtrów"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/inline-filter.php:26,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/list-filter.php:31
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:32
msgid "Reset Filters"
msgstr "Zresetuj filtry"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/filter-presenter.php:149,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/price-filter.php:188
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/Preview.js:59
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Preview.js:183
msgid "Reset Filter"
msgstr "Zresetuj filtr"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:728,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:24,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:46,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:142
msgid "Color"
msgstr "Kolor"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:78
msgid "Short Name"
msgstr "Nazwa skrócona"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:245
msgid "Sorry, this product is unavailable. Please choose a different combination."
msgstr "Przepraszamy, ten produkt jest niedostępny. Wybierz inną kombinację."

#: framework/features/blocks/dynamic-data/views/woo-field.php:26,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:246,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:481,
#: framework/premium/extensions/woocommerce-extra/features/swatches/includes/swatch-element-render.php:47,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:57
#: static/js/editor/blocks/dynamic-data/preview-parts/woo/StockPreview.js:7
msgid "Out of Stock"
msgstr "Brak w magazynie"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:540
msgid "Display Variations Inline"
msgstr "Warianty wyświetlania Inline"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:636,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:736,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:833
msgid "Swatches"
msgstr "Próbki"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:16,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:250
msgid "Color Swatches"
msgstr "Próbki kolorów"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:20,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:74,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:129,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:183
msgid "Swatch Shape"
msgstr "Kształt próbki"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:26,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:80,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:135,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:189
msgid "Round"
msgstr "Okrągły"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:87,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:142,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:196
msgid "Single Page Swatch Size"
msgstr "Rozmiar próbki pojedynczej strony"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:42,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:96,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:151,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:205
msgid "Widget Swatch Size"
msgstr "Rozmiar próbki widżetu"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:111,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:166,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:220
msgid "Archive Cards Swatch Size"
msgstr "Rozmiar próbki kart archiwalnych"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:70,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:294
msgid "Image Swatches"
msgstr "Próbki obrazu"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:125,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:338
msgid "Button Swatches"
msgstr "Próbki przycisków"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:395
msgid "Wishlist Button"
msgstr "Przycisk listy życzeń"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:58
msgid "Specific Product Variation "
msgstr "Specyficzne warianty produktu "

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:61
msgid "This option will allow you to add a speciffic product variation to wishlist."
msgstr "Ta opcja umożliwia dodanie określonego wariantu produktu do listy życzeń."

#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:601,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/views/table-no-results.php:3,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:19
msgid "Browse products"
msgstr "Przeglądaj produkty"

#: framework/features/blocks/contact-info/options.php:533,
#: framework/premium/features/premium-header/items/contacts/options.php:417
msgid "Link Icons"
msgstr "Ikony linków"

#: framework/premium/features/premium-header/items/contacts/view.php:59
msgid "Download"
msgstr "Pobierz"

#: framework/premium/features/premium-header/items/divider/options.php:22,
#: framework/premium/features/premium-header/items/divider/options.php:51,
#: framework/premium/features/premium-header/items/divider/options.php:66,
#: framework/premium/features/premium-header/items/divider/options.php:81
msgid "Style & Color"
msgstr "Styl i kolor"

#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:87
msgid "Dropdown Arrow"
msgstr "Strzałka rozwijana"

#: framework/premium/features/premium-header/items/menu-tertiary/config.php:4
msgid "Menu 3"
msgstr "Menu 3"

#: framework/features/blocks/search/options.php:119,
#: framework/premium/features/premium-header/items/search-input/options.php:111
msgid "Live Results Product Price"
msgstr "Cena produktu w wynikach na żywo"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:274
msgid "Add New Size Guide"
msgstr "Dodaj nowy przewodnik po rozmiarach"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:275
msgid "Edit Size Guide"
msgstr "Edytuj przewodnik po rozmiarach"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:276
msgid "New Size Guide"
msgstr "Nowy przewodnik po rozmiarach"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:278
msgid "View Size Guide"
msgstr "Zobacz przewodnik po rozmiarach"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:279
msgid "Search Size Guides"
msgstr "Szukaj przewodników po rozmiarach"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/modal-view.php:25,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/side-view.php:34
msgid "Close Sizes Modal"
msgstr "Zamknięte rozmiary Modal"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:29
msgid "Size Guide Display Conditions"
msgstr "Warunki wyświetlania Przewodnika po rozmiarach"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:30
msgid "Choose where you want this size guide to be displayed."
msgstr "Wybierz miejsce wyświetlania przewodnika po rozmiarach."

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:243
msgid "Sorry, no products matched your selection. Please choose a different combination."
msgstr "Przepraszamy, żaden produkt nie pasuje do Twojego wyboru. Wybierz inną kombinację."

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:244
msgid "Please select some product options before adding this product to your cart."
msgstr "Wybierz niektóre opcje produktu przed dodaniem go do koszyka."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:76
msgid "Amount the client has to reach in order to get free shipping."
msgstr "Kwota, którą klient musi osiągnąć, aby otrzymać darmową wysyłkę."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:104
msgid "The calculation method will be based on WooCommerce zones."
msgstr "Metoda obliczania będzie oparta na strefach WooCommerce."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:119
msgid "Discount Calculation"
msgstr "Obliczanie rabatu"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:129
msgid "Include or exclude the discount code when calculating the shipping progress."
msgstr "Uwzględnij lub wyłącz kod rabatowy podczas obliczania postępu wysyłki."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:133,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:158
msgid "Default Message"
msgstr "Komunikat domyślny"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:143
msgid "You can use dynamic code tags such as {price} inside this option."
msgstr "Wewnątrz tej opcji można używać dynamicznych znaczników kodu, takich jak {price}."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:174
msgid "Success Message"
msgstr "Wiadomość o powodzeniu"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:5,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:271,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:277
msgid "Size Guides"
msgstr "Przewodniki po rozmiarach"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:16
msgid "Size Guide Placement"
msgstr "Rozmieszczenie przewodnika po rozmiarach"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:23
msgid "Side Panel"
msgstr "Panel boczny"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:33
msgid "Reveal From"
msgstr "Ujawnij z"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:303,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:30
msgid "Columns & Products"
msgstr "Kolumny i produkty"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:330,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:57
msgid "Number of products"
msgstr "Liczba produktów"

#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:32
msgid "Share Box Icons Color"
msgstr "Kolor ikon Share Box"

#: framework/helpers/exts-configs.php:330,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:180,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:4
msgid "Free Shipping Bar"
msgstr "Bezpłatny pasek wysyłkowy"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:183
msgid "Show if cart is empty"
msgstr "Pokaż, czy koszyk jest pusty"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:382,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:176
msgid "Congratulations! You got free shipping 🎉"
msgstr "Gratulacje! Otrzymałeś darmową wysyłkę 🎉"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:359,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:135
msgid "Add {price} more to get free shipping!"
msgstr "Dodaj {price} więcej, aby otrzymać darmową wysyłkę!"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:15
msgid "Show In Cart Page"
msgstr "Pokaż na stronie koszyka"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:21
msgid "Show In Checkout Page"
msgstr "Pokaż na stronie realizacji zakupu"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:27
msgid "Show In Mini Cart"
msgstr "Pokaż w mini koszyku"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:34
msgid "Calculation Method"
msgstr "Metoda kalkulacji"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:69
msgid "Goal Amount"
msgstr "Kwota docelowa"

#: framework/premium/extensions/woocommerce-extra/features/filters/feature.php:126
msgid "Automatically scroll page to top after user interaction."
msgstr "Automatyczne przewijanie strony do góry po interakcji użytkownika."

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-cart/feature.php:167
msgid "Shopping Cart"
msgstr "Koszyk zakupów"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-cart/feature.php:169
msgid "Close cart drawer"
msgstr "Zamknij szufladę wózka"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/feature.php:152,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:525
msgid "WooCommerce Filters Canvas"
msgstr "Filtry WooCommerce Canvas"

#: framework/premium/extensions/shortcuts/customizer.php:477,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:4
msgid "Filters Canvas"
msgstr "Filtry płócienne"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:177
msgid "Panel Height"
msgstr "Wysokość panelu"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:187
msgid "Auto"
msgstr "Automatycznie"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:198
msgid "Custom Height"
msgstr "Własna wysokość"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:216
msgid "Panel Columns"
msgstr "Kolumny panelu"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:279,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:100
msgid "Panel Backdrop"
msgstr "Tło panelu"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:516
msgid "Widget Area Source"
msgstr "Źródło obszaru widgetu"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:524
msgid "WooCommerce Sidebar"
msgstr "Panel boczny WooCommerce"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:110,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:242
msgid "Days"
msgstr "Dni"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:111,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:256
msgid "Hours"
msgstr "Godziny"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:115,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:270
msgid "Min"
msgstr "Min"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:116,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:284
msgid "Sec"
msgstr "Sek."

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:298,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:330
msgid "Hurry up! This sale ends in"
msgstr "Pospiesz się! Wyprzedaż kończy się w"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:324
msgid "Countdown Box"
msgstr "Box Odliczania"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/helpers.php:36
msgid "Quick view"
msgstr "Szybki podgląd"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:19
msgid "Additional Actions"
msgstr "Dodatkowe działania"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:25
msgid "Buttons Type"
msgstr "Typ przycisków"

#: framework/extensions/trending/customizer.php:449,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:80,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:727
msgid "Button"
msgstr "Przycisk"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:72
msgid "Modal Trigger"
msgstr "Wyzwalacz modalny"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:82
msgid "Card"
msgstr "Karta"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:91
msgid "Modal Width"
msgstr "Szerokość modalna"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:102
msgid "Product Navigation"
msgstr "Nawigacja produktu"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:108
msgid "Display next/previous buttons that will help to easily navigate through products."
msgstr "Wyświetlaj przyciski następny/poprzedni, które pomogą w łatwej nawigacji po produktach."

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:175,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:412,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:130,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:138
msgid "Title Font"
msgstr "Czcionka Tytułu"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:210,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:442,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:168
msgid "Price Font"
msgstr "Czcionka Ceny"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:219,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:450,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:176
msgid "Price Color"
msgstr "Kolor ceny"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:219
msgid "Add To Cart Button"
msgstr "Przycisk dodaj do koszyka"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:291
msgid "View Cart Button"
msgstr "Przycisk zobacz koszyk"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:361
msgid "Product Page Button"
msgstr "Przycisk strony produktu"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:46
msgid "How many days the products will be marked as \"New\" after creation."
msgstr "Ile dni po utworzeniu produkty będą oznaczone jako \"Nowe\"."

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:136,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:142
msgid "Product Tabs"
msgstr "Zakładki produktu"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:137
msgid "Product Tab"
msgstr "Zakładka produktu"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:139
msgid "Add New Product Tab"
msgstr "Zakładka dodaj nowy produkt"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:140
msgid "Edit Product Tab"
msgstr "Edytuj kartę produktu"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:141
msgid "New Product Tab"
msgstr "Nowa zakładka produktu"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:143
msgid "View Product Tab"
msgstr "Wyświetl zakładkę produktu"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:144
msgid "Search Product Tabs"
msgstr "Zakładki wyszukiwania produktów"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:10
msgid "Product Tab Display Conditions"
msgstr "Warunki wyświetlania zakładki produktu"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:11
msgid "Choose where you want this product tab to be displayed."
msgstr "Wybierz, gdzie chcesz wyświetlać tę zakładkę produktu."

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:27
msgid "Tab Order"
msgstr "Kolejność zakładek"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:32
msgid "Default tabs order: Description - 10, Additional Information - 20, Reviews - 30."
msgstr "Domyślna kolejność zakładek: Opis - 10, Dodatkowe informacje - 20, Recenzje - 30."

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:106
msgid "Payment Gateways"
msgstr "Bramki płatności"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:112,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:65
msgid "Shipping Methods"
msgstr "Metody wysyłki"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:265
msgid "Thank you Page"
msgstr "Strona z podziękowaniem"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:480,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:486
msgid "Thank You Pages"
msgstr "Strony z podziękowaniami"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:481
msgid "Thank You Page"
msgstr "Strona z podziękowaniami"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:483
msgid "Add New Thank You Page"
msgstr "Dodaj nową stronę z podziękowaniem"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:484
msgid "Edit Thank You Page"
msgstr "Edytuj stronę z podziękowaniem"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:485
msgid "New Thank You Page"
msgstr "Nowa strona z podziękowaniem"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:487
msgid "View Thank You Page"
msgstr "Wyświetl stronę z podziękowaniem"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:488
msgid "Search Thank You Pages"
msgstr "Wyszukaj strony z podziękowaniami"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:78
msgid "Payment Gateway"
msgstr "Bramka płatności"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:106
msgid "Priority"
msgstr "Priorytet"

#: framework/premium/extensions/woocommerce-extra/features/cart-page/options.php:5
msgid "Coupon Form"
msgstr "Formularz kuponu"

#: framework/premium/extensions/woocommerce-extra/features/filters/feature.php:119
msgid "AJAX Filtering"
msgstr "Filtrowanie AJAX"

#: framework/premium/extensions/woocommerce-extra/features/filters/feature.php:125
msgid "Scroll to Top"
msgstr "Przewiń na górę"

#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:276
msgid "Compare Products"
msgstr "Porównaj produkty"

#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:282
msgid "Close Compare Modal"
msgstr "Zamknij porównanie modalne"

#: framework/premium/extensions/woocommerce-extra/features/compare/helpers.php:93,
#: framework/premium/extensions/woocommerce-extra/features/compare/helpers.php:152
msgid "Add to compare"
msgstr "Dodaj do porównania"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:46
msgid "Compare Placement"
msgstr "Porównaj rozmieszczenie"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:53
#: static/js/editor/blocks/breadcrumbs/Preview.js:46
msgid "Page"
msgstr "Strona"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:62
msgid "Select Page"
msgstr "Wybierz stronę"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:69
msgid "Select a page where the compare table will be outputted."
msgstr "Wybierz stronę, na której zostanie wyświetlona tabela porównawcza."

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:75
msgid "Compare Table Fields"
msgstr "Porównaj pola tabeli"

#: framework/features/blocks/dynamic-data/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:162
msgid "Length"
msgstr "Długość"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:130,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:186
msgid "Attributes"
msgstr "Atrybuty"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:266,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:470
msgid "Availability"
msgstr "Dostępność"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:469
msgid "Modal Border Radius"
msgstr "Modalny promień graniczny"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:366
msgid "Compare Bar"
msgstr "Pasek porównawczy"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:394
msgid "Button Icon"
msgstr "Ikona przycisku"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:449
msgid "Compare Bar Display Conditions"
msgstr "Warunki wyświetlania paska porównania"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:450
msgid "Add one or more conditions to display the Compare bar."
msgstr "Dodaj jeden lub więcej warunków, aby wyświetlić pasek porównania."

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:463,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:223,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:295,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:365
msgid "Button Font Color"
msgstr "Kolor tekstu przycisku"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:494,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:253,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:325,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:393
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:250
#: static/js/editor/blocks/search/Edit.js:311
msgid "Button Background Color"
msgstr "Kolor tła przycisku"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:15,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:5
msgid "New Badge"
msgstr "Nowa odznaka"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:55,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:53
msgid "Featured Badge"
msgstr "Wyróżniona odznaka"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/feature.php:168,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:82
msgid "HOT"
msgstr "HOT"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/feature.php:202,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:35
msgid "NEW"
msgstr "NOWOŚĆ"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:32,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:79
msgid "Badge Label"
msgstr "Etykieta odznaki"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:40
msgid "Label Duration"
msgstr "Etykieta Czas trwania"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:57
msgid "Allow users to upload images when leaving a review."
msgstr "Zezwalaj użytkownikom na przesyłanie zdjęć podczas pozostawiania recenzji."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:73
msgid "Image Lightbox"
msgstr "Lightbox obrazu"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:74
msgid "Allow users to open attached review images in lightbox."
msgstr "Umożliwienie użytkownikom otwierania załączonych obrazów recenzji w lightboxie."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:88
msgid "Review Voting"
msgstr "Recenzja Głosowanie"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:89
msgid "Allow users to upvote reviews."
msgstr "Zezwalaj użytkownikom na głosowanie na recenzje."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:105
msgid "Allowed Users"
msgstr "Dozwoleni użytkownicy"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:106
msgid "Set which users are allowed to vote."
msgstr "Określ, którzy użytkownicy mogą głosować."

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:5
msgid "Affiliate Products"
msgstr "Produkty partnerskie"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:12
msgid "Product Archive"
msgstr "Archiwum produktów"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:16,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:70
msgid "Image Affiliate Link"
msgstr "Link partnerski do obrazu"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:27,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:48,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:81
msgid "Open In New Tab"
msgstr "Otwórz w nowej karcie"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:36
msgid "Title Affiliate Link"
msgstr "Tytuł Link partnerski"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:90
msgid "Open Button Link In New Tab"
msgstr "Przycisk Otwórz łącze w nowej karcie"

#: framework/premium/extensions/woocommerce-extra/features/cart-page/options.php:14
msgid "Quantity Auto Update"
msgstr "Automatyczna aktualizacja ilości"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:37
msgid "Product brands base"
msgstr "Baza marek produktów"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:49
msgid "brand"
msgstr "marka"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:500,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:532
msgid "About Brands"
msgstr "Informacje o markach"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:501,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:534
msgid "About %s"
msgstr "O %s"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:558,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:578,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:640,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:707,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:718,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:493
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:146
msgid "Brands"
msgstr "Marki"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:562,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:150,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:177,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:221,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:239,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:257,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:275
msgid "Sticky Row"
msgstr "Przyklejony rząd"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:593,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:644
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:257
msgid "Logo Size"
msgstr "Rozmiar Logo"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:605,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:656
msgid "Logos Gap"
msgstr "Logos Gap"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:708
msgid "Brand"
msgstr "Marka"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:709
msgid "Search Brands"
msgstr "Szukaj marek"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:710
msgid "All Brands"
msgstr "Wszystkie marki"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:711
msgid "Parent Brand"
msgstr "Marka macierzysta"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:712
msgid "Parent Brand:"
msgstr "Marka macierzysta:"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:713
msgid "View Brand"
msgstr "Zobacz markę"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:714
msgid "Edit Brand"
msgstr "Edytuj markę"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:715
msgid "Update Brand"
msgstr "Aktualizuj markę"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:716
msgid "Add New Brand"
msgstr "Dodaj nową markę"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:717
msgid "New Brand Name"
msgstr "Nowa nazwa marki"

#: framework/premium/extensions/woocommerce-extra/features/brands/options.php:5
msgid "Product Brand Tab"
msgstr "Zakładka marki produktu"

#: framework/premium/extensions/woocommerce-extra/features/brands/options.php:20
msgid "Brand Name In Tab Title"
msgstr "Nazwa marki w tytule zakładki"

#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:5
msgid "Product Image"
msgstr "Zdjęcie produktu"

#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:54
msgid "Quantity Input"
msgstr "Wprowadzanie ilości"

#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:228
msgid "Compare Button"
msgstr "Przycisk porównania"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:139
msgid "Text Hover"
msgstr "Najechanie tekstem"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:144
msgid "Background Initial"
msgstr "Tło początkowe"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:376
msgid "Star"
msgstr "Gwiazda"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:376
msgid "Stars"
msgstr "Gwiazdy"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:442
msgid "%s%% of customers recommend this product."
msgstr "%s%% klientów poleca ten produkt"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:604
msgid "%s of %s found this review helpful"
msgstr "%s z %s uznało tę recenzję za pomocną"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:666,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:44
msgid "Review Title"
msgstr "Tytuł recenzji"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:694
msgid "Upload Image (Optional)"
msgstr "Prześlij obraz (opcjonalnie)"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:11
msgid "Reviews Order"
msgstr "Kolejność Recenzji"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:18
msgid "Oldest First"
msgstr "Najstarszy pierwszy"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:19
msgid "Newest First"
msgstr "Najpierw najnowsze"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:20
msgid "Low Rating First"
msgstr "Najpierw niska ocena"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:21
msgid "High Rating First"
msgstr "Najpierw wysoka ocena"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:22
msgid "Most Relevant"
msgstr "Najbardziej istotne"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:32
msgid "Average Score"
msgstr "Średni wynik"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:33
msgid "Display an average score for all reviews."
msgstr "Wyświetl średnią ocenę dla wszystkich recenzji."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:45
msgid "Allow users to add a title when leaving a review."
msgstr "Zezwalaj użytkownikom na dodawanie tytułu podczas pozostawiania recenzji."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:56
msgid "Image Upload"
msgstr "Przesyłanie obrazu"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/view.php:78
msgid "Color mode switch"
msgstr "Przełącznik trybu koloru"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:92
msgid "Items Counter"
msgstr "Licznik przedmiotów"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:20,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:13
msgid "Slider"
msgstr "Suwak"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:41
msgid "Columns & Posts"
msgstr "Kolumny i wpisy"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:49,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:311,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:38
msgid "Number of columns"
msgstr "Liczba kolumn"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:68
msgid "Number of posts"
msgstr "Liczba wpisów"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:91,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:344,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:75
msgid "Autoplay"
msgstr "Autoodtwarzanie"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:112,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:356,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:91
msgid "Delay (Seconds)"
msgstr "Opó��nienie (w sekundach)"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:113,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:357,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:92
msgid "Specify the amount of time (in seconds) to delay between automatically cycling an item."
msgstr "Określa czas (w sekundach) opóźnienia między automatycznymi cyklami elementu."

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:140
msgid "Choose the default color mode that a user will see when it visits your site."
msgstr "Wybierz domyślny tryb kolorów, który zobaczy użytkownik odwiedzający twoją witrynę."

#: framework/premium/features/content-blocks/options/popup.php:577
msgid "Enable this option if you want to lock the page scroll while the popup is triggered."
msgstr "Włącz tę opcję, jeśli chcesz zablokować przewijanie strony podczas uruchamiania wyskakującego okienka."

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/config.php:4
msgid "Color Switch"
msgstr "Przełącznik kolorów"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:39
msgid "Reverse Icon State"
msgstr "Stan odwróconej ikony"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:102
msgid "Dark Mode Label"
msgstr "Etykieta trybu ciemnego"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:114
msgid "Light Mode Label"
msgstr "Etykieta trybu oświetlenia"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:118,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/view.php:54
msgid "Light Mode"
msgstr "Tryb oświetlenia"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:128
msgid "Default Color Mode"
msgstr "Domyślny tryb kolorów"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:136
msgid "Light"
msgstr "Jasny"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:137
msgid "Dark"
msgstr "Ciemny"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:138
msgid "OS Aware"
msgstr "OS Aware"

#: framework/premium/features/content-blocks/options/popup.php:326
msgid "Set after how many days the popup will relaunch if the additional close trigger is met."
msgstr "Ustawia, po ilu dniach wyskakujące okienko zostanie ponownie uruchomione, jeśli zostanie spełniony dodatkowy wyzwalacz zamknięcia."

#: framework/premium/features/content-blocks/options/popup.php:446
msgid "Load Content With AJAX"
msgstr "Wczytywanie zawartości za pomocą AJAX"

#: framework/premium/features/content-blocks/options/popup.php:450
msgid "Enable this option if you want to load the popup content using AJAX."
msgstr "Włącz tę opcję, jeśli chcesz załadować zawartość wyskakującego okienka za pomocą AJAX."

#: framework/premium/features/content-blocks/options/popup.php:459
msgid "Reload Content"
msgstr "Przeładuj zawartość"

#: framework/premium/features/content-blocks/options/popup.php:466
#: static/js/options/ConditionsManager/ScheduleDate.js:78
msgid "Never"
msgstr "Nigdy"

#: framework/premium/features/content-blocks/options/popup.php:467
msgid "Always"
msgstr "Zawsze"

#: framework/premium/features/content-blocks/options/popup.php:469
msgid "Set this option to always if you have dynamic content inside the popup in order to keep everything up to date."
msgstr "Ustaw tę opcję na zawsze, jeśli masz dynamiczną zawartość w wyskakującym okienku, aby wszystko było aktualne."

#: framework/premium/features/content-blocks/options/popup.php:495,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:637
msgid "Popup Visibility"
msgstr "Widoczność wyskakującego okienka"

#: framework/premium/features/content-blocks/options/popup.php:573
msgid "Scroll Lock"
msgstr "Blokada przewijania"

#: framework/premium/features/content-blocks/options/popup.php:250
msgid "Set the button class selector that will trigger popup to close."
msgstr "Ustaw selektor klasy przycisku, który spowoduje zamknięcie wyskakującego okienka."

#: framework/premium/features/content-blocks/options/popup.php:280
msgid "Relaunch Trigger"
msgstr "Wyzwalacz ponownego uruchomienia"

#: framework/premium/features/content-blocks/options/popup.php:286
msgid "Never relaunch"
msgstr "Nigdy nie uruchamiaj ponownie"

#: framework/premium/features/content-blocks/options/popup.php:297
msgid "Days After Close"
msgstr "Dni po zamknięciu"

#: framework/premium/features/content-blocks/options/popup.php:303
msgid "Set after how many days the popup will relaunch."
msgstr "Określa, po ilu dniach wyskakujące okienko zostanie ponownie uruchomione."

#: framework/premium/features/content-blocks/options/popup.php:313
msgid "Days After Form Submit"
msgstr "Dni po przesłaniu formularza"

#: framework/premium/features/content-blocks/options/popup.php:317
msgid "Days After Button Click"
msgstr "Dni po kliknięciu przycisku"

#: framework/premium/features/content-blocks/options/maintenance.php:16
msgid "Add one or more conditions to display the Maintenance block."
msgstr "Dodaj jeden lub więcej warunków, aby wyświetlić blok Konserwacja."

#: framework/premium/features/content-blocks/options/popup.php:53
msgid "Popup Display Conditions"
msgstr "Warunki wyświetlania wyskakującego okienka"

#: framework/premium/features/content-blocks/options/popup.php:54
msgid "Choose where you want this popup to be displayed."
msgstr "Wybierz miejsce wyświetlania tego wyskakującego okienka."

#: framework/premium/features/content-blocks/options/popup.php:78
msgid "On element click"
msgstr "Po kliknięciu elementu"

#: framework/premium/features/content-blocks/options/popup.php:153
msgid "Close Popup On Scroll Back"
msgstr "Zamknij wyskakujące okienko przy przewijaniu do tyłu"

#: framework/premium/features/content-blocks/options/popup.php:216
msgid "Additional Close Trigger"
msgstr "Dodatkowy wyzwalacz zamknięcia"

#: framework/premium/features/content-blocks/options/popup.php:223
msgid "On form submit"
msgstr "Po przesłaniu formularza"

#: framework/premium/features/content-blocks/options/popup.php:224
msgid "On button click"
msgstr "Po kliknięciu przycisku"

#: framework/premium/features/content-blocks/options/popup.php:235
msgid "The popup will auto-close if a form submit action is detected inside of it."
msgstr "Wyskakujące okienko zostanie automatycznie zamknięte po wykryciu w nim akcji przesłania formularza."

#: framework/premium/features/content-blocks/options/popup.php:247
msgid "Button Class Selector"
msgstr "Selektor klasy przycisku"

#: framework/premium/features/content-blocks/options/hook.php:41,
#: framework/premium/features/content-blocks/options/nothing_found.php:35
msgid "Choose where you want this content block to be displayed."
msgstr "Wybierz miejsce wyświetlania tego bloku zawartości."

#: framework/premium/features/content-blocks/options/hook.php:283
msgid "Select a post/page to preview it's content inside the editor while building the hook."
msgstr "Wybierz post / stronę, aby wyświetlić podgląd jego zawartości w edytorze podczas tworzenia haka."

#: framework/premium/features/content-blocks/options/maintenance.php:15
msgid "Maintenance Block Display Conditions"
msgstr "Warunki wyświetlania bloku konserwacji"

#: framework/premium/features/media-video/options.php:110
msgid "Display a minimalistic view of the video player."
msgstr "Wyświetla minimalistyczny widok odtwarzacza wideo."

#: framework/premium/features/performance-typography/feature.php:116
msgid "Preconnect Google Fonts"
msgstr "Wstępne podłączenie czcionek Google"

#: framework/premium/features/performance-typography/feature.php:127
msgid "Preconnect Adobe Typekit Fonts"
msgstr "Wstępnie podłącz czcionki Adobe Typekit"

#: framework/features/header/items/account/config.php:4
msgid "Account"
msgstr "Konto"

#: framework/features/header/items/account/options.php:12
msgid "User Info"
msgstr "Informacja o użytkowniku"

#: framework/features/header/items/account/options.php:60
msgid "User Avatar"
msgstr "Awatar użytkownika"

#: framework/features/header/items/account/options.php:91,
#: framework/features/header/items/account/options.php:658,
#: framework/features/header/items/account/views/login.php:164,
#: framework/features/header/items/account/views/login.php:364
msgid "Edit Profile"
msgstr "Edytuj profil"

#: framework/features/header/items/account/options.php:101,
#: framework/features/header/items/account/options.php:105,
#: framework/features/header/items/account/options.php:667,
#: framework/features/header/items/account/views/login.php:170,
#: framework/features/header/items/account/views/login.php:385
msgid "Log Out"
msgstr "Wyloguj się"

#: framework/features/header/items/account/options.php:185,
#: framework/features/header/items/account/options.php:189,
#: framework/features/header/items/account/views/login.php:591
msgid "Dokan Dashboard"
msgstr "Pulpit Dokan"

#: framework/features/header/items/account/options.php:199,
#: framework/features/header/items/account/options.php:203,
#: framework/features/header/items/account/views/login.php:625
msgid "Dokan Shop"
msgstr "Sklep Dokan"

#: framework/features/header/items/account/options.php:215,
#: framework/features/header/items/account/options.php:219,
#: framework/features/header/items/account/views/login.php:657
msgid "Tutor LMS Dashboard"
msgstr "Pulpit nawigacyjny Tutor LMS"

#: framework/features/header/items/account/options.php:235,
#: framework/features/header/items/account/views/login.php:684
msgid "bbPress Dashboard"
msgstr "pulpit nawigacyjny bbPress"

#: framework/features/header/items/account/options.php:619,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:32
msgid "Link"
msgstr "Odnośnik"

#: framework/features/header/items/account/options.php:630
msgid "Dropdown Items"
msgstr "Elementy listy rozwijanej"

#: framework/features/header/items/account/options.php:687
msgid "Link To"
msgstr "Odnośnik do"

#: framework/premium/extensions/color-mode-switch/includes/logo-enhancements.php:34
msgid "Dark Mode Logo"
msgstr "Logo trybu ciemnego"

#: framework/premium/extensions/woocommerce-extra/features/sku-layer.php:60,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:248,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:434
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:140
msgid "SKU"
msgstr "SKU"

#: framework/premium/features/content-blocks/options/404.php:84,
#: framework/premium/features/content-blocks/options/archive.php:161,
#: framework/premium/features/content-blocks/options/header.php:102,
#: framework/premium/features/content-blocks/options/hook.php:211,
#: framework/premium/features/content-blocks/options/maintenance.php:81,
#: framework/premium/features/content-blocks/options/nothing_found.php:102,
#: framework/premium/features/content-blocks/options/single.php:93
msgid "Wide"
msgstr "Szeroki"

#: framework/premium/features/content-blocks/options/archive.php:10
msgid "Replace Conditions"
msgstr "Warunki wymiany"

#: framework/premium/features/content-blocks/options/archive.php:15
msgid "Template Replace Conditions"
msgstr "Warunki zastąpienia szablonu"

#: framework/premium/features/content-blocks/options/archive.php:16,
#: framework/premium/features/content-blocks/options/header.php:36,
#: framework/premium/features/content-blocks/options/single.php:16
msgid "Choose where you want this template to be displayed."
msgstr "Wybierz miejsce wyświetlania tego szablonu."

#: framework/premium/features/content-blocks/options/archive.php:17
msgid "Add Replace Condition"
msgstr "Dodaj warunek zastąpienia"

#: framework/premium/features/content-blocks/options/archive.php:142,
#: framework/premium/features/content-blocks/options/single.php:74
msgid "Left Sidebar"
msgstr "Panel boczny po lewej"

#: framework/premium/features/content-blocks/options/archive.php:147,
#: framework/premium/features/content-blocks/options/single.php:79
msgid "Right Sidebar"
msgstr "Panel boczny po prawej"

#: framework/premium/features/content-blocks/options/header.php:35,
#: framework/premium/features/content-blocks/options/single.php:15
msgid "Template Display Conditions"
msgstr "Warunki wyświetlania szablonu"

#: framework/premium/features/content-blocks/options/hook.php:40
msgid "Content Block Display Conditions"
msgstr "Warunki wyświetlania bloku zawartości"

#. translators: placeholder here means the actual URL.
#: framework/premium/features/media-video/options.php:77
msgid "YouTube won't store information about visitors on your website unless they play the video. More info about this can be found %shere%s."
msgstr "YouTube nie będzie przechowywać informacji o odwiedzających Twoją witrynę, dopóki nie odtworzą oni filmu. Więcej informacji na ten temat można znaleźć %stutaj%s."

#: framework/premium/features/media-video/options.php:91
msgid "Autoplay Video"
msgstr "Automatycznie odtwarzane wideo"

#: framework/premium/features/media-video/options.php:94
msgid "Automatically start video playback after the gallery is loaded."
msgstr "Automatyczne uruchamianie odtwarzania wideo po załadowaniu galerii."

#: framework/premium/features/media-video/options.php:99
msgid "Loop Video"
msgstr "Wideo w pętli"

#: framework/premium/features/media-video/options.php:102
msgid "Start video again after it ends."
msgstr "Uruchom wideo ponownie po jego zakończeniu."

#: framework/premium/features/media-video/options.php:107
msgid "Simplified Player"
msgstr "Odtwarzacz uproszczony"

#: framework/premium/features/content-blocks/hooks-manager.php:720
msgid "After single product \"Add to cart\" button"
msgstr "Po pojedynczym produkcie przycisk \"Dodaj do koszyka\""

#: framework/premium/features/content-blocks/hooks-manager.php:725
msgid "Before single product meta"
msgstr "Przed metą pojedynczego produktu"

#: framework/premium/features/content-blocks/hooks-manager.php:730
msgid "After single product meta"
msgstr "Po meta pojedynczego produktu"

#: framework/premium/features/media-video/options.php:6
msgid "Video Source"
msgstr "Źródło wideo"

#: framework/premium/features/media-video/options.php:25
msgid "Upload Video"
msgstr "Prześlij wideo"

#: framework/premium/features/media-video/options.php:29
msgid "Upload an MP4 file into the media library."
msgstr "Prześlij plik MP4 do biblioteki multimediów."

#: framework/premium/features/media-video/options.php:42
msgid "YouTube Url"
msgstr "Adres URL YouTube"

#: framework/premium/features/media-video/options.php:44
msgid "Enter a valid YouTube media URL."
msgstr "Wprowadź prawidłowy adres URL multimediów YouTube."

#: framework/premium/features/media-video/options.php:57
msgid "Vimeo Url"
msgstr "Adres URL Vimeo"

#: framework/premium/features/media-video/options.php:59
msgid "Enter a valid Vimeo media URL."
msgstr "Wprowadź prawidłowy adres URL multimediów Vimeo."

#: framework/premium/features/media-video/options.php:72
msgid "YouTube Privacy Enhanced Mode"
msgstr "Tryb rozszerzonej prywatności YouTube"

#: framework/premium/features/content-blocks/hooks-manager.php:234
msgid "After first post meta"
msgstr "Po meta pierwszym poście"

#: framework/premium/features/content-blocks/hooks-manager.php:242
msgid "After second post meta"
msgstr "Po meta drugim poście"

#: framework/premium/features/content-blocks/hooks-manager.php:580
msgid "Offcanvas Cart - Empty State"
msgstr "Koszyk offcanvas - stan pusty"

#: framework/premium/features/content-blocks/hooks-manager.php:705
msgid "Before single product gallery"
msgstr "Przed galerią pojedynczego produktu"

#: framework/premium/features/content-blocks/hooks-manager.php:710
msgid "After single product gallery"
msgstr "Po galerii pojedynczego produktu"

#: framework/premium/features/content-blocks/hooks-manager.php:715
msgid "Before single product \"Add to cart\" button"
msgstr "Przed przyciskiem \"Dodaj do koszyka\" pojedynczego produktu"

#: framework/premium/extensions/mega-menu/options.php:532,
#: framework/premium/extensions/mega-menu/options.php:875
msgid "Badge Settings"
msgstr "Ustawienia odznaki"

#: framework/premium/extensions/mega-menu/options.php:764
msgid "Column Background"
msgstr "Tło kolumny"

#: framework/premium/extensions/shortcuts/customizer.php:673,
#: framework/premium/extensions/shortcuts/customizer.php:699,
#: framework/premium/extensions/shortcuts/views/bar.php:55,
#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:241,
#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:245,
#: framework/premium/extensions/woocommerce-extra/features/compare/helpers.php:110,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:36,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:418,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/bar.php:53,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/config.php:4,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:140,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/view.php:97
msgid "Compare"
msgstr "Porównaj"

#: framework/premium/extensions/shortcuts/customizer.php:1124
msgid "Item Background Color"
msgstr "Kolor tła elementu"

#: framework/premium/extensions/shortcuts/customizer.php:1205
msgid "Wishlist Badge Color"
msgstr "Kolor odznaki z listy życzeń"

#: framework/premium/extensions/shortcuts/customizer.php:1247
msgid "Compare Badge Color"
msgstr "Porównaj kolor odznaki"

#: framework/premium/extensions/sidebars/extension.php:185
msgid "Remove Widget Area"
msgstr "Usuń obszar widgetu"

#: framework/premium/extensions/woocommerce-extra/config.php:21
msgid "This extension requires the WooCommerce plugin to be installed and activated."
msgstr "To rozszerzenie wymaga zainstalowania i aktywowania wtyczki WooCommerce."

#: framework/premium/extensions/woocommerce-extra/extension.php:359
msgid "Cart Page"
msgstr "Strona koszyka"

#: framework/premium/features/content-blocks/admin-ui.php:269
msgid "Nothing Found"
msgstr "Nic nie znaleziono"

#: framework/premium/features/content-blocks/admin-ui.php:270
msgid "Maintenance"
msgstr "Konserwacja"

#: framework/premium/features/content-blocks/admin-ui.php:374
msgid "On click to element"
msgstr "Po kliknięciu na element"

#: framework/extensions/product-reviews/extension.php:321,
#: framework/premium/features/content-blocks/content-block-layer.php:194,
#: framework/premium/features/content-blocks/content-block-layer.php:244,
#: framework/premium/extensions/woocommerce-extra/features/sku-layer.php:64,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:739,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:617,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:668,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:339,
#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:203,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:190,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:251,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:274,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:903,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-layer.php:246
msgid "Bottom Spacing"
msgstr "Odstęp u dołu"

#: framework/premium/features/content-blocks/hooks-manager.php:194
msgid "Before first post meta"
msgstr "Przed meta pierwszym postem"

#: framework/premium/features/content-blocks/hooks-manager.php:202
msgid "Before second post meta"
msgstr "Przed meta drugim postem"

#: framework/features/conditions/rules/woo.php:80,
#: framework/features/conditions/rules/woo.php:95
msgid "Product with Taxonomy ID"
msgstr "Produkt z identyfikatorem taksonomii"

#: framework/premium/extensions/color-mode-switch/extension.php:96
msgid "Dark Mode Color Palette"
msgstr "Paleta kolorów trybu ciemnego"

#: framework/premium/extensions/mega-menu/options.php:72
msgid "Dropdown Custom Width"
msgstr "Niestandardowa szerokość listy rozwijanej"

#: framework/premium/extensions/mega-menu/options.php:309
msgid "AJAX Content Loading"
msgstr "Ładowanie zawartości AJAX"

#: framework/premium/extensions/mega-menu/options.php:312
msgid "If you have complex data inside your mega menu you can enable this option in order to load the dropdown content with AJAX and improve the website loading time."
msgstr "Jeśli masz złożone dane w swoim mega menu, możesz włączyć tę opcję, aby załadować rozwijaną zawartość za pomocą AJAX i poprawić czas ładowania strony."

#: framework/premium/extensions/mega-menu/options.php:403
msgid "Content Visibility"
msgstr "Widoczność treści"

#: framework/premium/features/clone-cpt.php:127
msgid "Post creation failed, could not find original post: "
msgstr "Tworzenie postu nie powiodło się, nie można znaleźć oryginalnego postu: "

#: framework/premium/features/clone-cpt.php:184
msgid "Post copy created."
msgstr "Utworzono kopię postu."

#: framework/premium/features/local-gravatars.php:34
msgid "Store Gravatars Locally"
msgstr "Przechowuj Gravatary lokalnie"

#: framework/premium/features/local-gravatars.php:39
msgid "Store and load Gravatars locally for increased privacy and performance."
msgstr "Przechowuj i ładuj Gravatary lokalnie, aby zwiększyć prywatność i wydajność."

#: framework/premium/features/premium-header.php:314,
#: framework/premium/features/socials.php:13,
#: framework/features/blocks/contact-info/options.php:103,
#: framework/features/blocks/contact-info/options.php:168,
#: framework/features/blocks/contact-info/options.php:231,
#: framework/features/blocks/contact-info/options.php:294,
#: framework/features/blocks/contact-info/options.php:357,
#: framework/features/blocks/contact-info/options.php:420,
#: framework/features/blocks/contact-info/options.php:483,
#: framework/features/header/items/account/options.php:375,
#: framework/features/header/items/account/options.php:756,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:9
msgid "Icon Source"
msgstr "Źródło ikonki"

#: framework/premium/features/socials.php:41
msgid "URL Source"
msgstr "Źródło URL"

#: framework/premium/features/socials.php:59
msgid "Custom URL"
msgstr "Niestandardowy adres URL"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:60
msgid "Form Style"
msgstr "Styl formularza"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:68
msgid "Stacked"
msgstr "Ułożone"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:137,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:152
msgid "Your email *"
msgstr "Twój adres e-mail *"

#: framework/extensions/newsletter-subscribe/providers/demo.php:16
msgid "Demo List"
msgstr "Lista demonstracyjna"

#: framework/features/conditions/rules/basic.php:10,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:12
msgid "All Products"
msgstr "Wszystkie produkty"

#: framework/features/conditions/rules/basic.php:40
msgid "All Singulars"
msgstr "Wszystkie liczby pojedyncze"

#: framework/features/conditions/rules/basic.php:51
msgid "All Archives"
msgstr "Wszystkie archiwa"

#: framework/features/conditions/rules/date-time.php:5
msgid "Date & Time"
msgstr "Data i czas"

#: framework/features/conditions/rules/date-time.php:9
msgid "Range Date/Time"
msgstr "Zakres Data/godzina"

#: framework/features/conditions/rules/date-time.php:14
#: static/js/options/ConditionsManager/ScheduleDate.js:143
msgid "Recurring Days"
msgstr "Powtarzające się dni"

#: framework/features/conditions/rules/requests.php:5
msgid "Requests"
msgstr "Żądania"

#: framework/features/conditions/rules/requests.php:9
msgid "Request Referer"
msgstr "Odsyłacz żądania"

#: framework/features/conditions/rules/requests.php:14
msgid "Request Cookie"
msgstr "Poproś o plik cookie"

#: framework/features/conditions/rules/requests.php:19
msgid "Request URL"
msgstr "Adres URL żądania"

#: framework/features/conditions/rules/specific.php:31
msgid "Post with Author ID"
msgstr "Post z identyfikatorem autora"

#: framework/features/conditions/rules/woo.php:75,
#: framework/features/conditions/rules/woo.php:90
msgid "Product ID"
msgstr "Identyfikator produktu."

#: framework/extensions/cookies-consent/customizer.php:199
msgid "Accept Button"
msgstr "Przycisk akceptacji"

#: framework/extensions/cookies-consent/customizer.php:266
msgid "Decline Button"
msgstr "Przycisk odrzucenia"

#: framework/extensions/cookies-consent/extension.php:105
msgid "Please accept the Privacy Policy in order to comment."
msgstr "Aby móc komentować, zaakceptuj Politykę prywatności."

#: framework/extensions/cookies-consent/extension.php:106
msgid "Comment Submission Failure"
msgstr "Nie udało się przesłać komentarza"

#: framework/extensions/newsletter-subscribe/customizer.php:28,
#: framework/extensions/newsletter-subscribe/helpers.php:24,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:15
msgid "Enter your email address below and subscribe to our newsletter"
msgstr "Wpisz swój adres e-mail poniżej i zapisz się do naszego newslettera"

#: framework/extensions/newsletter-subscribe/customizer.php:389
msgid "Container Shadow"
msgstr "Cień kontenera"

#: framework/extensions/trending/customizer.php:588
msgid "Module Title Font"
msgstr "Czcionka tytułu modułu"

#: framework/extensions/trending/customizer.php:596
msgid "Module Title Color"
msgstr "Kolor tytułu modułu"

#: framework/extensions/trending/customizer.php:647
msgid "Posts Title Font"
msgstr "Krój pisma tytułu wpisów"

#: framework/extensions/trending/customizer.php:656
msgid "Posts Title Font Color"
msgstr "Kolor tekstu tytułu wpisów"

#: framework/extensions/trending/customizer.php:871
msgid "Arrows Color"
msgstr "Kolor strzałek"

#: framework/premium/features/clone-cpt.php:42,
#: framework/premium/features/clone-cpt.php:45
msgid "Duplicate"
msgstr "Duplikat"

#: framework/premium/features/clone-cpt.php:55
msgid "No post to duplicate"
msgstr "Brak postów do powielenia"

#: framework/helpers/exts-configs.php:314
msgid "Create a customized order “Thank You” page for your customers, giving them a personalized experience."
msgstr "Stwórz spersonalizowaną stronę z podziękowaniem za zamówienie dla swoich klientów, zapewniając im spersonalizowane doświadczenie."

#: framework/helpers/exts-configs.php:322,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:5
msgid "Advanced Reviews"
msgstr "Zaawansowane recenzje"

#: framework/helpers/exts-configs.php:323
msgid "Enhance your WooCommerce reviews with rich content, images and a thumbs up system that help your shoppers find the perfect product."
msgstr "Wzbogać swoje recenzje WooCommerce o bogate treści, obrazy i system kciuków w górę, które pomogą kupującym znaleźć idealny produkt."

#: framework/extensions/cookies-consent/customizer.php:112
msgid "Forms Cookies Content"
msgstr "Zgoda na pliki cookie przy formularzu"

#: framework/helpers/exts-configs.php:362
msgid "Better management for affiliate products with a few simple options that strengthen the external integration with these."
msgstr "Lepsze zarządzanie produktami partnerskimi dzięki kilku prostym opcjom, które wzmacniają zewnętrzną integrację z nimi."

#: framework/helpers/exts-configs.php:295
msgid "Custom Tabs"
msgstr "Zakładki niestandardowe"

#: framework/helpers/exts-configs.php:296
msgid "Present additional information about your products by adding new custom tabs to the product information section."
msgstr "Prezentuj dodatkowe informacje o swoich produktach, dodając nowe niestandardowe karty do sekcji informacji o produkcie."

#: framework/helpers/exts-configs.php:304,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:58,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:62,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:272,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/helpers.php:47,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/modal-view.php:18,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/side-view.php:27
msgid "Size Guide"
msgstr "Przewodnik po rozmiarach"

#: framework/helpers/exts-configs.php:305
msgid "Show a size chart guide so that your visitors can pick the right size for them when ordering a product."
msgstr "Pokaż tabelę rozmiarów, aby odwiedzający mogli wybrać odpowiedni rozmiar podczas zamawiania produktu."

#: framework/helpers/exts-configs.php:313
msgid "Custom Thank You Pages"
msgstr "Własne strony z podziękowaniami"

#: framework/helpers/exts-configs.php:279
msgid "Catch the attention of your clients by showcasing your product variations as colour, image or button swatches."
msgstr "Przyciągnij uwagę klientów, prezentując warianty produktów w postaci próbek kolorów, obrazów lub przycisków."

#: framework/helpers/exts-configs.php:286,
#: framework/features/conditions/rules/woo.php:42
msgid "Product Brands"
msgstr "Marki produktów"

#: framework/helpers/exts-configs.php:287
msgid "Categorise products by brands and show their logo in archive or single page so users could discover more about their makers."
msgstr "Kategoryzuj produkty według marek i wyświetlaj ich logo w archiwum lub na pojedynczej stronie, aby użytkownicy mogli dowiedzieć się więcej o ich producentach."

#: framework/helpers/exts-configs.php:361
msgid "Affiliate Product Links"
msgstr "Linki do produktów partnerskich"

#: framework/helpers/exts-configs.php:339
msgid "Replace the standard product gallery with additional layouts which can showcase the photos as a grid or even a slider."
msgstr "Zastąp standardową galerię produktów dodatkowymi układami, które mogą prezentować zdjęcia w formie siatki lub nawet suwaka."

#: framework/helpers/exts-configs.php:354
msgid "Search by SKU"
msgstr "Szukaj SKU"

#: framework/helpers/exts-configs.php:355
msgid "Advanced searching for products by their SKU classification can be useful in cases of vast product catalogues."
msgstr "Zaawansowane wyszukiwanie produktów według ich klasyfikacji SKU może być przydatne w przypadku obszernych katalogów produktów."

#: framework/helpers/exts-configs.php:331
msgid "Add a visual cue that tells your visitors how much the cart total must be to be able to benefit of free shipping."
msgstr "Dodaj wizualną wskazówkę, która informuje odwiedzających, ile musi wynosić suma koszyka, aby móc skorzystać z bezpłatnej wysyłki."

#: framework/helpers/exts-configs.php:278,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:4
msgid "Variation Swatches"
msgstr "Próbki wariantów"

#: framework/helpers/exts-configs.php:271
msgid "Compare products with a clear and concise table system that gives your users a way to make a quick decision."
msgstr "Porównuj produkty za pomocą przejrzystego i zwięzłego systemu tabel, który daje użytkownikom możliwość podjęcia szybkiej decyzji."

#: framework/helpers/exts-configs.php:346
msgid "Product Share Box"
msgstr "Skrzynka udostępniania produktu"

#: framework/helpers/exts-configs.php:347
msgid "Enable social sharing abilities for products available on the site, letting even more users discover your great shop selection."
msgstr "Włącz funkcje udostępniania w serwisach społecznościowych produktów dostępnych w witrynie, dzięki czemu jeszcze więcej użytkowników będzie mogło zapoznać się z ofertą Twojego sklepu."

#: framework/helpers/exts-configs.php:338
msgid "Advanced Gallery"
msgstr "Galeria zaawansowana"

#: framework/helpers/exts-configs.php:247
msgid "Preview the available products and let your users make quick and informative decisions about their purchase."
msgstr "Wyświetlaj podgląd dostępnych produktów i pozwól użytkownikom podejmować szybkie decyzje dotyczące zakupu."

#: framework/helpers/exts-configs.php:254,
#: framework/premium/extensions/shortcuts/views/bar.php:54
msgid "Filters"
msgstr "Filtry"

#: framework/helpers/exts-configs.php:255
msgid "Drill down the product list with new filtering widgets, an off canvas area for them and showing the active filters on the page."
msgstr "Przejdź w dół listy produktów za pomocą nowych widżetów filtrowania, obszaru poza obszarem roboczym dla nich i wyświetlania aktywnych filtrów na stronie."

#: framework/helpers/exts-configs.php:263
msgid "A set of features that lets you create easily your dream products wishlists and share them with friends and family."
msgstr "Zestaw funkcji, które pozwolą Ci łatwo tworzyć listy życzeń wymarzonych produktów i dzielić się nimi z przyjaciółmi i rodziną."

#: framework/helpers/exts-configs.php:270
msgid "Compare View"
msgstr "Widok porównania"

#: framework/helpers/exts-configs.php:239
msgid "Adds the “add to cart” actions to the product page as a floating bar if the product summary has disappeared from view."
msgstr "Dodaje akcje \"dodaj do koszyka\" do strony produktu jako pływający pasek, jeśli podsumowanie produktu zniknęło z widoku."

#: framework/helpers/exts-configs.php:246,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:58
msgid "Quick View"
msgstr "Szybki podgląd"

#: framework/helpers/exts-configs.php:157
msgid "Let your guests easily filter the posts by their category or tags taxonomy terms, instantly drilling down the listings."
msgstr "Pozwól swoim gościom łatwo filtrować posty według ich kategorii lub terminów taksonomii, tagów, natychmiast przechodząc w dół listy."

#: framework/helpers/exts-configs.php:163
msgid "Taxonomy Customisations"
msgstr "Dostosowywanie taksonomii"

#: framework/helpers/exts-configs.php:164
msgid "Additional customisation options for your taxonomies such as hero backgrounds and custom colour labels."
msgstr "Dodatkowe opcje dostosowywania taksonomii, takie jak tła bohaterów i niestandardowe etykiety kolorów."

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:227
msgctxt "Extension Brand Name"
msgid "Shop Extra"
msgstr "Sklep Extra"

#: framework/helpers/exts-configs.php:143
msgid "Display the approximate reading time of an article, so that visitors know what to expect when starting to read the content."
msgstr "Wyświetlaj przybliżony czas czytania artykułu, aby odwiedzający wiedzieli, czego się spodziewać po rozpoczęciu czytania treści."

#: framework/helpers/exts-configs.php:149
#: static/js/editor/blocks/dynamic-data/index.js:100
msgid "Dynamic Data"
msgstr "Dane dynamiczne"

#: framework/helpers/exts-configs.php:150
msgid "Integrates custom fields solutions in the meta layers of a post and presents additional information."
msgstr "Integruje niestandardowe rozwiązania pól w meta warstwach postu i prezentuje dodatkowe informacje."

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:83
msgctxt "Extension Brand Name"
msgid "Color Mode Switch"
msgstr "Przełącznik trybu koloru"

#: framework/helpers/exts-configs.php:84
msgid "Add a dark colour scheme and switch to your website, which will make it pleasant to look at in low light environments."
msgstr "Dodaj ciemny schemat kolorów i przełącznik do swojej witryny, co sprawi, że będzie ona przyjemna dla oka w warunkach słabego oświetlenia."

#. translators: 1: link open 2: link close
#: framework/features/account-auth.php:253
msgid "Registration complete. Please check your email, then visit the %1$slogin page%2$s."
msgstr "Rejestracja zakończona. Sprawdź pocztę e-mail, a następnie odwiedź %1$sstronę logowania%2$s."

#: framework/dashboard.php:34,
#: framework/features/header/items/account/options.php:23,
#: framework/features/header/items/account/options.php:73,
#: framework/features/header/items/account/options.php:77,
#: framework/features/header/items/account/options.php:649,
#: framework/features/header/items/account/views/login.php:158,
#: framework/features/header/items/account/views/login.php:335
msgid "Dashboard"
msgstr "Kokpit"

#: framework/dashboard.php:521
msgid "You do not have sufficient permissions to access this page."
msgstr "Brak uprawnień, aby uzyskać dostęp do tej strony."

#: framework/theme-integration.php:221,
#: framework/features/blocks/share-box/options.php:25
msgid "X (Twitter)"
msgstr "X (Twitter)"

#: framework/theme-integration.php:234
msgid "Mastodon"
msgstr "Mastodon"

#: framework/features/conditions/rules/pages.php:39
msgid "Privacy Policy Page"
msgstr "Strona polityki prywatności"

#: framework/features/conditions/rules/pages.php:46
msgid "Author Archives"
msgstr "Archiwa autora"

#: framework/features/conditions/rules/woo.php:104,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:42
msgid "WooCommerce"
msgstr "WooCommerce"

#: framework/features/conditions/rules/woo.php:15
msgid "Shop Home"
msgstr "Strona główna sklepu"

#: framework/features/conditions/rules/woo.php:20,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:66
msgid "Single Product"
msgstr "Pojedynczy produkt"

#: framework/features/conditions/rules/woo.php:25
msgid "Product Archives"
msgstr "Archiwa produktów"

#: framework/features/conditions/rules/woo.php:30,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:91
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:27
msgid "Product Categories"
msgstr "Kategorie produktów"

#: framework/features/conditions/rules/woo.php:49
msgid "Product Tags"
msgstr "Tagi produktu"

#: framework/features/conditions/rules/cpt.php:53
msgid "Custom Post Types"
msgstr "Własne typy treści"

#: framework/features/conditions/rules/localization.php:15
msgid "Current Language"
msgstr "Aktualny język"

#: framework/features/conditions/rules/bbPress.php:11,
#: framework/features/header/items/account/options.php:231
msgid "bbPress"
msgstr "bbPress"

#: framework/features/conditions/rules/bbPress.php:15,
#: framework/features/header/items/account/options.php:24,
#: framework/features/header/items/account/options.php:87
msgid "Profile"
msgstr "Profil"

#: framework/extensions/newsletter-subscribe/helpers.php:142
msgid "First name"
msgstr "Imię"

#: framework/extensions/trending/customizer.php:537
msgid "Display Location"
msgstr "Wyświetl położenie"

#: framework/extensions/trending/customizer.php:545
msgid "Before Footer"
msgstr "Przed stopką"

#: framework/extensions/trending/customizer.php:550
msgid "After Footer"
msgstr "Po stopce"

#: framework/extensions/trending/customizer.php:555
msgid "After Header"
msgstr "Po nagłówku"

#: framework/extensions/trending/customizer.php:572
msgid "Trending Block Display Conditions"
msgstr "Trendy w warunkach wyświetlania bloków"

#: framework/extensions/trending/customizer.php:573
msgid "Add one or more conditions to display the trending block."
msgstr "Dodaj jeden lub więcej warunków, aby wyświetlić blok trendu."

#: framework/premium/features/content-blocks/admin-ui.php:641
msgid "Hide Hooks"
msgstr "Ukryj haki"

#: framework/premium/features/content-blocks/admin-ui.php:642
msgid "Show Hooks"
msgstr "Pokaż haki"

#: framework/premium/features/content-blocks/admin-ui.php:698
msgid "Hide Theme Hooks"
msgstr "Ukryj haki motywu"

#: framework/premium/features/content-blocks/admin-ui.php:699
msgid "Show Theme Hooks"
msgstr "Pokaż haki motywu"

#: framework/premium/features/content-blocks/admin-ui.php:707
msgid "Hide WooCommerce Hooks"
msgstr "Ukryj haki WooCommerce"

#: framework/premium/features/content-blocks/admin-ui.php:708
msgid "Show WooCommerce Hooks"
msgstr "Pokaż haki WooCommerce"

#: framework/features/header/modal/register.php:76
msgid "A link to set a new password will be sent to your email address."
msgstr "Link do ustawienia nowego hasła zostanie wysłany na Twój adres e-mail."

#: framework/premium/extensions/code-snippets/extension.php:31,
#: framework/premium/extensions/code-snippets/extension.php:86,
#: framework/premium/extensions/code-snippets/extension.php:131
msgid "Header scripts"
msgstr "Skrypty nagłówka"

#: framework/premium/extensions/code-snippets/extension.php:53,
#: framework/premium/extensions/code-snippets/extension.php:108,
#: framework/premium/extensions/code-snippets/extension.php:153
msgid "Footer scripts"
msgstr "Skrypty stopki"

#: framework/premium/extensions/mega-menu/options.php:377,
#: framework/premium/extensions/mega-menu/options.php:385,
#: framework/premium/features/content-blocks/content-block-layer.php:170,
#: framework/premium/features/content-blocks/content-block-layer.php:178,
#: framework/premium/features/content-blocks/content-block-layer.php:221,
#: framework/premium/features/content-blocks/content-block-layer.php:229,
#: framework/features/header/items/account/options.php:254,
#: framework/features/header/items/account/options.php:262,
#: framework/premium/features/premium-header/items/content-block/options.php:13,
#: framework/premium/features/premium-header/items/content-block/options.php:21
#: framework/premium/static/js/blocks/ContentBlock.js:110
msgid "Select Content Block"
msgstr "Wybierz blok zawartości"

#: framework/premium/extensions/mega-menu/options.php:380,
#: framework/premium/features/content-blocks/content-block-layer.php:173,
#: framework/premium/features/content-blocks/content-block-layer.php:224,
#: framework/features/header/items/account/options.php:257,
#: framework/premium/features/premium-header/items/content-block/options.php:16
msgid "Create a new content Block/Hook"
msgstr "Utwórz nowy blok zawartości/hak"

#: framework/premium/extensions/mega-menu/options.php:637
msgid "Heading Font"
msgstr "Czcionka nagłówka"

#: framework/premium/features/content-blocks/admin-ui.php:155
msgid "Enable"
msgstr "Włącz"

#: framework/premium/features/content-blocks/admin-ui.php:156
msgid "Disable"
msgstr "Wyłącz"

#: framework/premium/features/content-blocks/admin-ui.php:211
msgid "Enabled %s content block."
msgid_plural "Enabled %s content blocks."
msgstr[0] "Włączony %s blok treści."
msgstr[1] "Włączone %s bloki treści."
msgstr[2] "Włączone %s bloków treści."

#: framework/premium/features/content-blocks/admin-ui.php:236
msgid "Disabled %s content block."
msgid_plural "Disabled %s content blocks."
msgstr[0] "Wyłączony %s blok treści."
msgstr[1] "Wyłączone %s bloki treści."
msgstr[2] "Wyłączone %s bloków treści."

#: framework/premium/features/content-blocks/admin-ui.php:264
msgid "404 Page"
msgstr "Strona 404"

#: framework/premium/features/content-blocks/admin-ui.php:267,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:16,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:64
msgid "Archive"
msgstr "Archiwum"

#: framework/premium/features/content-blocks/admin-ui.php:268,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:17,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:65
msgid "Single"
msgstr "Pojedyncze"

#: framework/premium/features/content-blocks/hooks-manager.php:39
msgid "WP body open"
msgstr "WP body open"

#: framework/premium/features/content-blocks/hooks-manager.php:340
msgid "Before related posts"
msgstr "Przed powiązanymi wpisami"

#: framework/premium/features/content-blocks/hooks-manager.php:341,
#: framework/premium/features/content-blocks/hooks-manager.php:348,
#: framework/premium/features/content-blocks/hooks-manager.php:355,
#: framework/premium/features/content-blocks/hooks-manager.php:362,
#: framework/premium/features/content-blocks/hooks-manager.php:369,
#: framework/premium/features/content-blocks/hooks-manager.php:376,
#: framework/premium/features/content-blocks/hooks-manager.php:383,
#: framework/premium/features/content-blocks/hooks-manager.php:390,
#: framework/premium/features/content-blocks/hooks-manager.php:398,
#: framework/premium/features/content-blocks/hooks-manager.php:405
msgid "Related posts"
msgstr "Powiązane wpisy"

#: framework/premium/features/content-blocks/hooks-manager.php:347
msgid "Related posts top"
msgstr "Powiązane wpisy u góry"

#: framework/premium/features/content-blocks/hooks-manager.php:368
msgid "Card top"
msgstr "Card top"

#: framework/premium/features/content-blocks/hooks-manager.php:375
msgid "Before featured image"
msgstr "Przed wyróżnionym obrazkiem"

#: framework/premium/features/content-blocks/hooks-manager.php:382
msgid "After featured image"
msgstr "Po wyróżnionym obrazku"

#: framework/premium/features/content-blocks/hooks-manager.php:389
msgid "Card bottom"
msgstr "Card bottom"

#: framework/premium/features/content-blocks/hooks-manager.php:397
msgid "Related posts bottom"
msgstr "Related posts bottom"

#: framework/premium/features/content-blocks/hooks-manager.php:404
msgid "After related posts"
msgstr "Po powiązanych wpisach"

#: framework/premium/features/content-blocks/hooks-manager.php:568
msgid "Offcanvas Filters - Top"
msgstr "Offcanvas Filters Top"

#: framework/premium/features/content-blocks/hooks-manager.php:574
msgid "Offcanvas Filters - Bottom"
msgstr "Offcanvas Filters Bottom"

#: framework/premium/features/content-blocks/options/archive.php:31,
#: framework/premium/features/content-blocks/options/single.php:30
msgid "Replacement Behavior"
msgstr "Zachowanie zastępcze"

#: framework/premium/features/content-blocks/options/archive.php:38
msgid "Only Card"
msgstr "Tylko karta"

#: framework/premium/features/content-blocks/options/archive.php:39,
#: framework/premium/features/content-blocks/options/single.php:38
msgid "Full Page"
msgstr "Pełna strona"

#: framework/premium/features/content-blocks/options/404.php:51,
#: framework/premium/features/content-blocks/options/archive.php:118,
#: framework/premium/features/content-blocks/options/maintenance.php:48,
#: framework/premium/features/content-blocks/options/nothing_found.php:69,
#: framework/premium/features/content-blocks/options/single.php:50
msgid "Page Structure"
msgstr "Struktura strony"

#: framework/premium/features/content-blocks/options/single.php:37
msgid "Content Area"
msgstr "Obszar zawartości"

#: framework/premium/features/content-blocks/options/single.php:99
msgid "Content Area Vel Spacing"
msgstr "Obszar zawartości Vel Odstępy"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:81
msgid "Filter Source"
msgstr "Żródło filtrów"

#: framework/features/blocks/contact-info/options.php:590,
#: framework/premium/features/premium-header/items/contacts/options.php:6
msgid "Items Direction"
msgstr "Kierunek elementów"

#: framework/features/blocks/contact-info/options.php:597,
#: framework/premium/features/premium-header/items/contacts/options.php:14
msgid "Horizontal"
msgstr "Poziomo"

#: framework/features/blocks/contact-info/options.php:596,
#: framework/premium/features/premium-header/items/contacts/options.php:13
msgid "Vertical"
msgstr "Pionowo"

#: framework/premium/features/premium-header/items/contacts/options.php:889,
#: framework/premium/features/premium-header/items/contacts/options.php:931,
#: framework/premium/features/premium-header/items/contacts/options.php:969,
#: framework/premium/features/premium-header/items/contacts/options.php:1007
#: static/js/editor/blocks/about-me/Edit.js:148
#: static/js/editor/blocks/contact-info/Edit.js:162
msgid "Icons Background Color"
msgstr "Kolor tła ikon"

#: framework/premium/features/premium-header/items/contacts/options.php:893,
#: framework/premium/features/premium-header/items/contacts/options.php:935,
#: framework/premium/features/premium-header/items/contacts/options.php:973,
#: framework/premium/features/premium-header/items/contacts/options.php:1011
#: static/js/editor/blocks/about-me/Edit.js:179
#: static/js/editor/blocks/contact-info/Edit.js:193
msgid "Icons Border Color"
msgstr "Kolor obramowania ikonek"

#: framework/premium/static/js/options/IconPicker/CustomIcon.js:22
msgid "Click to upload"
msgstr "Kliknij, aby przesłać"

#: framework/premium/static/js/options/IconPicker/Modal.js:135
msgid "All Icons"
msgstr "Wszystkie ikony"

#: static/js/options/ConditionsManager/SingleCondition.js:296
msgid "All authors"
msgstr "Wszyscy autorzy"

#: static/js/dashboard/screens/DemoInstall/components/Error.js:25
msgid "Can't Import Starter Site"
msgstr "Nie można zaimportować Strony Startowej"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:31
msgid "Unfortunately, your hosting configuration doesn't meet the minimum requirements for importing a starter site."
msgstr "Niestety, konfiguracja Twojego hostingu nie spełnia minimalnych wymagań dotyczących importowania strony startowej."

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/views/panel.php:62
msgid "Close filters modal"
msgstr "Zamknij okno modalne z filtrami"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/view.php:45
msgid "Close quick view"
msgstr "Zamknij szybki podgląd"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/helpers.php:45
msgid "Quick view toggle"
msgstr "Przełącznik szybkiego podglądu"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/helpers.php:32
msgid "Quick view icon"
msgstr "Ikona szybkiego podglądu"

#: framework/features/header/items/account/options.php:1724,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:314,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:185
msgid "Close Button Type"
msgstr "Typ przycisku zamykania"

#: framework/features/header/items/account/options.php:726,
#: framework/features/header/items/account/options.php:1731,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:323,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:23,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:194
msgid "Simple"
msgstr "Proste"

#: framework/features/header/items/account/options.php:1732,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:324,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:195
msgid "Border"
msgstr "Obramowanie"

#: framework/features/header/items/account/options.php:1778,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:366,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:248,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:254,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:298,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:382,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:466
msgid "Border Color"
msgstr "Kolor obramowania"

#: framework/premium/features/content-blocks/hooks-manager.php:170
msgid "Before description"
msgstr "Przed opisem"

#: framework/premium/features/content-blocks/hooks-manager.php:178
msgid "Before breadcrumbs"
msgstr "Przed okruszkami"

#: framework/premium/features/content-blocks/hooks-manager.php:218
msgid "After description"
msgstr "Po opisie"

#: framework/premium/features/content-blocks/hooks-manager.php:226
msgid "After breadcrumbs"
msgstr "Po okruszkach"

#: framework/premium/features/content-blocks/hooks-manager.php:630
msgid "Before shop loop item actions"
msgstr "Before shop loop item actions"

#: framework/premium/features/content-blocks/hooks-manager.php:635
msgid "After shop loop item actions"
msgstr "After shop loop item actions"

#. translators: placeholder here means the actual URL.
#: framework/features/blocks/socials/options.php:24
msgid "Configure the social links in Customizer ➝ General ➝ %sSocial Network Accounts%s."
msgstr "Skonfiguruj odnośniki społecznościowe w Wygląd ➝ Dostosuj ➝ Ogólne ➝ %sKonta sieci społecznościowych%s."

#: framework/features/header/items/account/options.php:306
msgid "Customizing: Logged out State"
msgstr "Dostosowywanie: Stan wylogowania"

#: framework/features/header/items/account/options.php:1088
msgid "User Visibility"
msgstr "Widoczność użytkownika"

#: framework/features/header/items/account/options.php:1099,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:116,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:95
msgid "Logged In"
msgstr "Zalogowano"

#: framework/features/header/items/account/options.php:1100,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:117,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:96
msgid "Logged Out"
msgstr "Wylogowano"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1385
msgid "Custom Field"
msgstr "Niestandardowe pola"

#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:114
msgid "%s min"
msgid_plural "%s mins"
msgstr[0] "%s min."
msgstr[1] "%s min."
msgstr[2] "%s min."

#: framework/premium/features/content-blocks/options/archive.php:98
msgid "Default Card Layout"
msgstr "Domyślny układ kart"

#: framework/premium/features/content-blocks/options/archive.php:103
msgid "Inherit card wrapper settings from Customizer (background color, spacing, shadow)."
msgstr "Dziedzicz ustawienia opakowania karty z narzędzia Dostosuj (kolor tła, odstępy, cień)."

#: framework/premium/features/content-blocks/options/archive.php:53,
#: framework/premium/features/content-blocks/options/hook.php:277,
#: framework/premium/features/content-blocks/options/popup.php:483,
#: framework/premium/features/content-blocks/options/single.php:151
msgid "Dynamic Content Preview"
msgstr "Dynamiczny podgląd treści"

#: framework/premium/features/content-blocks/options/archive.php:60
msgid "Select a post/page to preview it's content inside the editor while building the archive."
msgstr "Wybierz wpis / stronę, aby wyświetlić podgląd jej zawartości w edytorze podczas tworzenia archiwum."

#: framework/premium/features/content-blocks/options/archive.php:66
msgid "Editor/Card Width"
msgstr "Szerokość edytora / karty"

#: framework/premium/features/content-blocks/options/archive.php:77
msgid "Set the editor width for better understanging the layout you are building (just for preview purpose, this option won't apply in frontend)."
msgstr "Ustaw szerokość edytora, aby lepiej zrozumieć budowany układ (tylko w celu podglądu, ta opcja nie będzie miała zastosowania w interfejsie użytkownika)."

#: framework/premium/features/content-blocks/options/popup.php:203
msgid "After X Pages"
msgstr "Po X stronach"

#: framework/premium/features/content-blocks/options/popup.php:209
msgid "Set after how many visited pages the popup block will appear."
msgstr "Ustaw po ilu odwiedzonych stronach pojawi się wyskakujący blok."

#: framework/premium/features/content-blocks/options/popup.php:489,
#: framework/premium/features/content-blocks/options/single.php:157
msgid "Select a post/page to preview it's content inside the editor while building the post/page."
msgstr "Wybierz wpis / stronę, aby wyświetlić podgląd jej zawartości w edytorze podczas tworzenia wpisu / strony."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:236
msgid "More information on how to generate an API key for Mailerlite can be found %shere%s. Please note that it is required at least one group to be created in your account for the integration to work. More info on how to create a group %shere%s."
msgstr "Więcej informacji na temat generowania klucza API dla Mailerlite można znaleźć %stutaj%s. Proszę pamiętać, że aby integracja działała, wymagamy utworzenia co najmniej jednej grupy na swoim koncie. Więcej informacji na temat %stworzenia grupy%s."

#: framework/premium/static/js/hooks/CodeEditor.js:59
msgid "Code Editor"
msgstr "Edytor kodu"

#: framework/premium/static/js/hooks/CreateHook.js:101
msgid "Template Type"
msgstr "Typ szablonu"

#: framework/premium/static/js/hooks/CreateHook.js:116
msgid "Archive Template"
msgstr "Szablon archiwum"

#: framework/premium/static/js/hooks/CreateHook.js:124
msgid "Single Template"
msgstr "Pojedynczy szablon"

#: framework/premium/static/js/hooks/CreateHook.js:178
msgid "Hook Name"
msgstr "Nazwa haka"

#: framework/premium/static/js/hooks/CreateHook.js:182
msgid "Popup Name"
msgstr "Nazwa okienka popup"

#: framework/premium/static/js/hooks/CreateHook.js:186
msgid "Template Name"
msgstr "Nazwa szablonu"

#: framework/premium/features/content-blocks/admin-ui.php:263,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:52,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:22
#: framework/premium/static/js/hooks/CreateHook.js:77
msgid "Popup"
msgstr "Wyskakujące okienko"

#: framework/premium/static/js/hooks/CreateHook.js:86
msgid "Custom Template"
msgstr "Niestandardowy szablon"

#: framework/premium/static/js/options/IconPicker.js:20
msgid "Theme Icons"
msgstr "Ikony tematyczne"

#: framework/premium/static/js/options/IconPicker.js:26
msgid "FontAwesome Brands"
msgstr "FontAwesome Brands"

#: framework/premium/static/js/options/IconPicker.js:32
msgid "FontAwesome Solid"
msgstr "FontAwesome Solid"

#: framework/premium/static/js/options/IconPicker.js:38
msgid "FontAwesome Regular"
msgstr "FontAwesome Regular"

#: framework/premium/static/js/typography/providers/kadence.js:21
#: framework/premium/static/js/typography/providers/plus-addons.js:23
#: framework/premium/static/js/typography/providers/stackable.js:23
msgid "%s Local Google Fonts"
msgstr "%s Local Google Fonts"

#: framework/premium/static/js/typography/providers/kadence.js:26
#: framework/premium/static/js/typography/providers/plus-addons.js:27
#: framework/premium/static/js/typography/providers/stackable.js:27
msgid "%s Typekit"
msgstr "%s Typekit"

#: framework/premium/static/js/typography/providers/kadence.js:31
#: framework/premium/static/js/typography/providers/stackable.js:31
msgid "%s Custom Fonts"
msgstr "%s niestandardowe czcionki"

#: framework/premium/static/js/typography/providers/kadence.js:59
msgid "Normal"
msgstr "Normalny"

#: framework/premium/static/js/typography/providers/kadence.js:83
msgid "Inherit"
msgstr "Dziedziczone"

#: framework/premium/static/js/typography/providers/plus-addons.js:31
msgid "%s Custom"
msgstr "%s niestandardowy"

#: framework/premium/static/js/typography/providers/plus-addons.js:35
msgid "%s System"
msgstr "%s systemowy"

#: framework/premium/features/premium-header.php:22,
#: framework/premium/features/premium-header.php:58
msgid "Mobile Menu 1"
msgstr "Menu mobilne 1"

#: framework/premium/features/premium-header.php:59,
#: framework/premium/features/premium-header/items/mobile-menu-secondary/config.php:4
msgid "Mobile Menu 2"
msgstr "Menu mobilne 2"

#: framework/extensions/newsletter-subscribe/providers/active-campaign.php:154,
#: framework/extensions/newsletter-subscribe/providers/brevo.php:116,
#: framework/extensions/newsletter-subscribe/providers/campaign-monitor.php:136,
#: framework/extensions/newsletter-subscribe/providers/convertkit.php:97,
#: framework/extensions/newsletter-subscribe/providers/demo.php:40,
#: framework/extensions/newsletter-subscribe/providers/mailerlite-classic.php:104,
#: framework/extensions/newsletter-subscribe/providers/mailerlite-new.php:123,
#: framework/extensions/newsletter-subscribe/providers/mailpoet.php:93
msgid "Thank you for subscribing to our newsletter!"
msgstr "Dziękujemy za zapisanie się do naszego newslettera!"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:59
msgctxt "Extension Brand Name"
msgid "Adobe Fonts"
msgstr "Kroje pisma Adobe"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:70,
#: framework/premium/extensions/code-snippets/extension.php:24,
#: framework/premium/extensions/code-snippets/extension.php:79,
#: framework/premium/extensions/code-snippets/extension.php:122
msgctxt "Extension Brand Name"
msgid "Custom Code Snippets"
msgstr "Własne fragmenty kodu"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:96
msgctxt "Extension Brand Name"
msgid "Custom Fonts"
msgstr "Własne czcionki"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:108
msgctxt "Extension Brand Name"
msgid "Local Google Fonts"
msgstr "Lokalne czcionki Google"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:123
msgctxt "Extension Brand Name"
msgid "Advanced Menu"
msgstr "Menu zaawansowane"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:136
msgctxt "Extension Brand Name"
msgid "Post Types Extra"
msgstr "Dodatkowe typy postów"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:179,
#: framework/premium/extensions/shortcuts/config.php:5,
#: framework/premium/extensions/shortcuts/customizer.php:751
msgctxt "Extension Brand Name"
msgid "Shortcuts Bar"
msgstr "Pasek skrótów"

#: framework/premium/extensions/shortcuts/customizer.php:314
msgid "Set link to nofollow"
msgstr "Ustaw odnośnik na nofollow"

#: framework/premium/extensions/shortcuts/customizer.php:320
msgid "Custom class"
msgstr "Klasa niestandardowa"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:194
msgctxt "Extension Brand Name"
msgid "Multiple Sidebars"
msgstr "Wiele pasków bocznych"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:214
msgctxt "Extension Brand Name"
msgid "White Label"
msgstr "Biała etykieta"

#: framework/features/blocks/share-box/options.php:132,
#: framework/features/blocks/socials/options.php:84
msgid "Icons Spacing"
msgstr "Odstępy między ikonami"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/feature.php:157
msgid "Add widgets here."
msgstr "Tutaj można dodawać widżety."

#. translators: 1: link open 2: link close
#: framework/features/account-auth.php:106
msgid "Check your email for the confirmation link, then visit the %slogin page%s."
msgstr "Sprawdź email z linkiem potwierdzającym, a następnie odwiedź %sstronę logowania%s."

#: framework/features/account-auth.php:226
msgid "Your account was created successfully. Your login details have been sent to your email address. Please visit the %1$slogin page%2$s."
msgstr "Twoje konto zostało pomyślnie utworzone. Dane logowania zostały wysłane na podany adres e-mail. Odwiedź %1$sstronę logowania%2$s."

#: framework/features/account-auth.php:238
msgid "Your account was created successfully and a password has been sent to your email address. Please visit the %1$slogin page%2$s."
msgstr "Twoje konto zostało pomyślnie utworzone, a hasło zostało wysłane na podany adres e-mail. Odwiedź %1$sstronę logowania%2$s."

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/cookies-consent/config.php:5,
#: framework/extensions/cookies-consent/customizer.php:5
msgctxt "Extension Brand Name"
msgid "Cookies Consent"
msgstr "Zgoda na pliki cookie"

#: framework/extensions/cookies-consent/customizer.php:76
msgid "Accept Button text"
msgstr "Tekst przycisku akceptacji"

#: framework/extensions/cookies-consent/customizer.php:85
msgid "Decline Button text"
msgstr "Tekst przycisku odrzucenia"

#: framework/extensions/cookies-consent/customizer.php:88,
#: framework/extensions/cookies-consent/helpers.php:11
msgid "Decline"
msgstr "Odrzuć"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/newsletter-subscribe/config.php:5
msgctxt "Extension Brand Name"
msgid "Newsletter Subscribe"
msgstr "Zapisz się do newslettera"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/product-reviews/config.php:5
msgctxt "Extension Brand Name"
msgid "Product Reviews"
msgstr "Recenzje produktów"

#: framework/extensions/product-reviews/metabox.php:60
msgid "Please note that some of this information (price, sku, brand) won't be displayed on the front-end. It is solely used for Google's Schema.org markup."
msgstr "Należy pamiętać, że niektóre z tych informacji (cena, sku, marka) nie będą wyświetlane na stronie głównej. Są używane wyłącznie do znaczników Google Schema.org."

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/trending/config.php:5,
#: framework/extensions/trending/customizer.php:160
msgctxt "Extension Brand Name"
msgid "Trending Posts"
msgstr "Najpopularniejsze wpisy"

#: framework/extensions/trending/customizer.php:477,
#: framework/features/blocks/about-me/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:32,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:133,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:73
msgid "Image Size"
msgstr "Rozmiar obrazka"

#: framework/features/header/account-modal.php:25
msgid "Close account modal"
msgstr "Zamknij konto modalne"

#: framework/features/header/header-options.php:68
msgid "Effect"
msgstr "Efekt"

#: framework/features/header/header-options.php:84
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:73
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:68
msgid "Offset"
msgstr "Przesunięcie"

#: framework/premium/features/content-blocks/admin-ui.php:275
msgid "All types"
msgstr "Wszystkie typy"

#: framework/premium/features/content-blocks/admin-ui.php:378,
#: framework/premium/features/content-blocks/options/popup.php:82
msgid "After x pages"
msgstr "Po x stronach"

#: framework/premium/features/content-blocks/popups.php:376
msgid "Close popup"
msgstr "Zamknij wyskakujące okienko"

#: framework/premium/features/media-video/options.php:13
msgid "Upload"
msgstr "Prześlij"

#: framework/premium/static/js/header/CreateHeader.js:117
msgid "Picker header"
msgstr "Nagłówek selektora"

#: framework/premium/static/js/header/CreateHeader.js:134
#: static/js/header/PanelsManager.js:58
msgid "Global Header"
msgstr "Globalny nagłówek"

#: framework/premium/static/js/header/CreateHeader.js:173
msgid "Create New Header"
msgstr "Utwórz nowy nagłówek"

#: framework/premium/static/js/header/CreateHeader.js:50
msgid "Create new header"
msgstr "Utwórz nowy nagłówek"

#: framework/premium/static/js/header/CreateHeader.js:53
msgid "Create a new header and assign it to different pages or posts based on your conditions."
msgstr "Utwórz nowy nagłówek i przypisz go do różnych stron lub postów w zależności od warunków."

#: framework/premium/static/js/header/CreateHeader.js:71
msgid "Header name"
msgstr "Nazwa nagłówka"

#: framework/premium/static/js/hooks/CodeEditor.js:238
msgid "Yes, continue"
msgstr "Tak, kontynuuj"

#: framework/premium/static/js/hooks/CodeEditor.js:150
msgid "Use code editor"
msgstr "Użyj edytora kodu"

#: framework/premium/static/js/hooks/CodeEditor.js:153
msgid "Exit code editor"
msgstr "Wyjdź z edytora kodu"

#: framework/premium/static/js/hooks/CodeEditor.js:165
msgid "Heads up!"
msgstr "Uwaga!"

#: framework/premium/static/js/hooks/CodeEditor.js:167
msgid "Enabling & disabling the code editor will erase everything from your post editor and this action is irreversible."
msgstr "Włączanie i wyłączanie edytora kodu spowoduje usunięcie wszystkiego z edytora wpisów. Czynności tej nie można cofnąć."

#: framework/premium/static/js/hooks/CodeEditor.js:174
msgid "Are you sure you want to continue?"
msgstr "Jesteś pewien, że chcesz kontynuować?"

#: framework/premium/static/js/hooks/CreateHook.js:223
msgid "Create Content Block"
msgstr "Utwórz blok treści"

#: framework/premium/static/js/hooks/CreateHook.js:36
msgid "Please select the type of your content block and place it in the location you want based on your display and user conditions."
msgstr "Wybierz typ bloku treści i umieść go w wybranej lokalizacji na podstawie wyświetlania i warunków użytkownika."

#: framework/premium/static/js/hooks/CreateHook.js:108
msgid "Select template type..."
msgstr "Wybierz typ szablonu..."

#: framework/premium/features/content-blocks/admin-ui.php:262
#: framework/premium/static/js/hooks/CreateHook.js:68
msgid "Custom Content/Hooks"
msgstr "Niestandardowa treść / hak"

#: framework/premium/static/js/hooks/CreateHook.js:148
msgid "404 Page Template"
msgstr "Szablon strony 404"

#: framework/premium/static/js/hooks/CreateHook.js:132
msgid "Header Template"
msgstr "Szablon nagłówka"

#: framework/premium/static/js/hooks/CreateHook.js:140
msgid "Footer Template"
msgstr "Szablon stopki"

#: framework/premium/static/js/options/IconPicker.js:137
msgid "Change Icon"
msgstr "Zmień ikonę"

#: framework/premium/static/js/options/IconPicker.js:155
msgid "Remove Icon"
msgstr "Usuń ikonę"

#: framework/premium/static/js/options/IconPicker.js:161
msgid "Select"
msgstr "Wybierz"

#: framework/premium/static/js/options/IconPicker/Modal.js:148
msgid "Upload Icon"
msgstr "Prześlij ikonę"

#: framework/premium/static/js/options/IconPicker/CustomIcon.js:34
msgid "For performance and customization reasons, only SVG files are allowed."
msgstr "Ze względu na wydajność i dostosowanie, dozwolone są tylko pliki SVG."

#: framework/premium/static/js/options/IconPicker/IconsList.js:24
msgid "Search icon"
msgstr "Ikona wyszukiwania"

#: framework/premium/static/js/options/MultipleLocationsSelect.js:151
msgid "Add New Location"
msgstr "Dodaj nową lokalizację"

#: framework/premium/static/js/options/MultipleLocationsSelect.js:53
msgid "Select location"
msgstr "Wybierz lokalizację"

#: static/js/dashboard.js:64
msgid "Starter Sites"
msgstr "Witryny startowe"

#: static/js/dashboard.js:76
msgid "Extensions"
msgstr "Rozszerzenia"

#: static/js/header/EditConditions.js:151
#: static/js/options/DisplayCondition.js:98
msgid "Save Conditions"
msgstr "Zapisz warunki"

#: static/js/header/EditConditions.js:107
msgid "Add one or more conditions in order to display your header."
msgstr "Dodaj jeden lub więcej warunków, aby wyświetlić nagłówek."

#: static/js/header/PanelsManager.js:174
msgid "Remove header"
msgstr "Usuń nagłówek"

#: static/js/header/PanelsManager.js:198
msgid "Remove Header"
msgstr "Usuń nagłówek"

#: static/js/header/PanelsManager.js:201
msgid "You are about to remove a custom header, are you sure you want to continue?"
msgstr "Zamierzasz usunąć własny nagłówek, czy na pewno chcesz kontynuować?"

#: static/js/dashboard/helpers/SubmitSupport.js:18
msgid "Need help or advice?"
msgstr "Potrzebujesz pomocy lub porady?"

#: static/js/dashboard/helpers/SubmitSupport.js:21
msgid "Got a question or need help with the theme? You can always submit a support ticket or ask for help in our friendly Facebook community."
msgstr "Masz pytanie lub potrzebujesz pomocy z motywem? Zawsze możesz przesłać zgłoszenie do pomocy technicznej lub poprosić o pomoc w naszej przyjaznej społeczności na Facebooku."

#: static/js/dashboard/helpers/SubmitSupport.js:33
msgid "Submit a Support Ticket"
msgstr "Prześlij zgłoszenie do pomocy technicznej"

#: static/js/dashboard/helpers/SubmitSupport.js:41
msgid "Join Facebook Community"
msgstr "Dołącz do społeczności na Facebooku"

#: static/js/dashboard/helpers/useUpsellModal.js:134
msgid "Upgrade Now"
msgstr "Ulepsz teraz"

#: static/js/options/ConditionsManager/SingleCondition.js:114
msgid "Select rule"
msgstr "Wybierz regułę"

#: static/js/options/ConditionsManager/SingleCondition.js:204
msgid "Select taxonomy"
msgstr "Wybierz taksonomię"

#: static/js/options/ConditionsManager/SingleCondition.js:236
msgid "Select language"
msgstr "Wybierz język"

#: static/js/options/ConditionsManager/SingleCondition.js:292
msgid "Select user"
msgstr "Wybierz użytkownika"

#: static/js/options/ConditionsManager/SingleCondition.js:265
msgid "Current user"
msgstr "Bieżący użytkownik"

#: static/js/options/DisplayCondition.js:28
msgid "Add Display Condition"
msgstr "Dodaj warunek wyświetlania"

#: framework/features/conditions-manager.php:242,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:126
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:283
#: static/js/options/ConditionsManager/SingleCondition.js:97
msgid "Include"
msgstr "Zawiera"

#: framework/features/conditions-manager.php:242,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:127
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:291
#: static/js/options/ConditionsManager/SingleCondition.js:98
msgid "Exclude"
msgstr "Wyklucz"

#: framework/premium/static/js/options/PreviewedPostsSelect.js:170
#: static/js/options/ConditionsManager/PostIdPicker.js:68
msgid "Type to search by ID or title..."
msgstr "Wpisz, aby wyszukać według identyfikatora lub tytułu..."

#: framework/premium/static/js/options/PreviewedPostsSelect.js:174
#: static/js/options/ConditionsManager/PostIdPicker.js:74
msgid "Select post"
msgstr "Wybierz wpis"

#: static/js/options/ConditionsManager/PostIdPicker.js:76
msgid "Select page"
msgstr "Wybierz stronę"

#: static/js/options/CustomizerOptionsManager.js:113
msgid "Import Options"
msgstr "Opcje importu"

#: static/js/options/CustomizerOptionsManager.js:116
msgid "Easily import the theme customizer settings."
msgstr "Łatwo importuj opcje dostosowywania motywu."

#: static/js/options/CustomizerOptionsManager.js:140
msgid "Click or drop to upload a file..."
msgstr "Kliknij lub upuść, aby przesłać plik..."

#: static/js/options/CustomizerOptionsManager.js:239
msgid "Import Customizations"
msgstr "Importuj ustawienia własne"

#: static/js/options/CustomizerOptionsManager.js:249
msgid "Copy Options"
msgstr "Opcje kopiowania"

#: static/js/options/CustomizerOptionsManager.js:252
msgid "Copy and import your customizations from parent or child theme."
msgstr "Kopiuj i importuj ustawienia z motywu nadrzędnego lub potomnego."

#: static/js/options/CustomizerOptionsManager.js:308
msgid "Copy From Parent Theme"
msgstr "Kopiuj z motywu nadrzędnego"

#: static/js/options/CustomizerOptionsManager.js:314
msgid "Copy From Child Theme"
msgstr "Kopiuj z motywu potomnego"

#: static/js/options/CustomizerOptionsManager.js:321
msgid "You are about to copy all the settings from your parent theme into the child theme. Are you sure you want to continue?"
msgstr "Zamierzasz skopiować wszystkie ustawienia z motywu nadrzędnego do motywu potomnego. Jesteś pewien, że chcesz kontynuować?"

#: static/js/options/CustomizerOptionsManager.js:327
msgid "You are about to copy all the settings from your child theme into the parent theme. Are you sure you want to continue?"
msgstr "Zamierzasz skopiować wszystkie ustawienia z motywu potomnego do motywu nadrzędnego. Czy na pewno chcesz kontynuować?"

#: static/js/options/CustomizerOptionsManager.js:376
msgid "Yes, I am sure"
msgstr "Tak, na pewno"

#: static/js/options/CustomizerOptionsManager.js:390
msgid "Export Settings"
msgstr "Ustawienia eksportu"

#: static/js/options/CustomizerOptionsManager.js:394
msgid "Choose what set of settings you want to export."
msgstr "Wybierz zestaw ustawień, które chcesz wyeksportować."

#: static/js/options/CustomizerOptionsManager.js:439
msgid "Customizer settings"
msgstr "Ustawienia konfiguratora"

#: static/js/options/CustomizerOptionsManager.js:443
msgid "Widgets settings"
msgstr "Ustawienia długości"

#: static/js/options/CustomizerOptionsManager.js:536
msgid "Export"
msgstr "Eksport"

#: static/js/options/CustomizerOptionsManager.js:87
msgid "Export Options"
msgstr "Eksportuj opcje"

#: static/js/options/CustomizerOptionsManager.js:90
msgid "Easily export the theme customizer settings."
msgstr "Łatwo wyeksportuj opcje dostosowywania motywu."

#: static/js/options/CustomizerOptionsManager.js:107
msgid "Export Customizations"
msgstr "Eksportuj ustawienia własne"

#: static/js/options/DisplayCondition.js:19
msgid "Transparent Header Display Conditions"
msgstr "Warunki wyświetlania przezroczystego nagłówka"

#: static/js/options/DisplayCondition.js:23
msgid "Add one or more conditions to display the transparent header."
msgstr "Dodaj co najmniej jeden warunek, aby wyświetlić przezroczysty nagłówek."

#: static/js/dashboard/screens/DemoInstall.js:166
msgid "Loading Starter Sites..."
msgstr "Ładowanie witryn startowych..."

#: static/js/dashboard/screens/DemoInstall/components/Progress.js:29
msgid "Installing"
msgstr "Instalowanie"

#: static/js/dashboard/screens/DemoInstall/components/Progress.js:34
msgid "Please be patient and don't refresh this page, the import process may take a while, this also depends on your server."
msgstr "Prosimy o cierpliwość i nie odświeżanie strony, proces importowania może chwilę potrwać, zależy to również od możliwości serwera."

#: static/js/dashboard/screens/DemoInstall/DemoToInstall.js:306
msgid "Back"
msgstr "Powrót"

#: static/js/dashboard/screens/DemoInstall/DemoToInstall.js:346
msgid "Install"
msgstr "Zainstaluj"

#: static/js/dashboard/screens/DemoInstall/DemoToInstall.js:347
msgid "Next"
msgstr "Następny"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:182
msgid "Import"
msgstr "Import"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:127
msgid "Available for"
msgstr "Dostępne dla"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:166
msgid "Preview"
msgstr "Podgląd"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:181
msgid "Modify"
msgstr "Zmień"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:22
msgid "Starter Site Imported Successfully"
msgstr "Witryna startowa została pomyślnie zaimportowana"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:25
msgid "Now you can view your website or start customizing it"
msgstr "Teraz możesz wyświetlić swoją witrynę lub rozpocząć jej dostosowywanie"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:33
#: static/js/dashboard/screens/Extensions/CurrentExtension.js:342
msgid "Customize"
msgstr "Dostosuj"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:40
msgid "View site"
msgstr "Zobacz witrynę"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:12
msgid "copying child theme sources"
msgstr "kopiowanie źródeł motywu potomnego"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:16
msgid "activating child theme"
msgstr "włącz motyw potomny"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:21
msgid "installing plugin %s"
msgstr "instalowanie wtyczki %s"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:28
msgid "activating plugin %s"
msgstr "włącz wtyczkę %s"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:34
msgid "downloading demo widgets"
msgstr "pobieranie widżetów demonstracyjnych"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:38
msgid "installing demo widgets"
msgstr "instalowanie widżetów demonstracyjnych"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:42
msgid "downloading demo options"
msgstr "pobieranie opcji demo"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:46
msgid "importing images from customizer"
msgstr "importowanie obrazków z konfiguratora"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:50
msgid "import customizer options"
msgstr "importuj opcje dostosowywania"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:54
msgid "activating required extensions"
msgstr "włącz wymagane rozszeżenia"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:58
msgid "removing previously installed posts"
msgstr "usuwanie wcześniej zainstalowanych wpisów"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:62
msgid "removing previously installed taxonomies"
msgstr "usuwanie wcześniej zainstalowanych taksonomii"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:66
msgid "removing default WordPress pages"
msgstr "usuwanie domyślnych stron WordPress"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:70
msgid "resetting customizer options"
msgstr "resetowanie opcji dostosowywania"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:74
msgid "resetting widgets"
msgstr "resetowanie widżetów"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:89
msgid "users"
msgstr "użytkownicy"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:90
msgid "terms"
msgstr "warunki"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:91
msgid "images"
msgstr "obrazki"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:92
msgid "posts"
msgstr "wpisy"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:93
msgid "comments"
msgstr "komentarze"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:118
msgid "Child theme"
msgstr "Motyw potomny"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:143
#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:70
msgid "Erase content"
msgstr "Usuń treść"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:185
msgid "Final touches"
msgstr "Ostatnie poprawki"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:155
msgid "Import options"
msgstr "Opcje importu"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:168
msgid "Import widgets"
msgstr "Import widżetów"

#: static/js/dashboard/screens/DemoInstall/Installer/contentCalculation.js:9
msgid "Import content"
msgstr "Import treści"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:108
msgid "You already have a child theme properly installed and activated. Move on."
msgstr "Masz już poprawnie zainstalowany i włączony motyw potomny. Przejdź dalej."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:116
msgid "Learn more about child themes"
msgstr "Dowiedz się więcej o motywach potomnych"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:70
msgid "We strongly recommend to install the child theme, this way you will have freedom to make changes without breaking the parent theme."
msgstr "Zdecydowanie zalecamy zainstalowanie motywu potomnego, dzięki czemu będziesz mieć swobodę wprowadzania zmian bez nadpisywania motywu nadrzędnego."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:77
msgid "We strongly recommend to activate the child theme, this way you will have freedom to make changes without breaking the parent theme."
msgstr "Zdecydowanie zalecamy użycie motywu potomnego, dzięki czemu zyskamy swobodę wprowadzania zmian bez nadpisywania motywu nadrzędnego."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:97
msgid "Install Child Theme"
msgstr "Zainstaluj motyw potomny"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:98
msgid "Activate Child Theme"
msgstr "Włącz motyw potomny"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:56
msgid "Import Content"
msgstr "Import treści"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:59
msgid "This will import posts, pages, comments, navigation menus, custom fields, terms and custom posts."
msgstr "Spowoduje to zaimportowanie wpisów, stron, komentarzy, menu nawigacyjnych, pól niestandardowych, warunków i własnych wpisów"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:104
msgid "Clean Install"
msgstr "Czysta instalacja"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:106
msgid "This option will remove the previous imported content and will perform a fresh and clean install."
msgstr "Opcja usunie poprzednio zaimportowaną zawartość i wykona nową i czystą instalację."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:147
msgid "This starter site is already installed"
msgstr "Ta witryna startowa jest już zainstalowana"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:156
msgid "Starter Site Removed"
msgstr "Usunięto witrynę startową"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:165
msgid "Dismiss"
msgstr "Odrzuć"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:174
msgid "What steps do you want to perform next?"
msgstr "Jakie kroki chcesz wykonać dalej?"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:191
msgid "Remove"
msgstr "Usuń"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:200
msgid "Reinstall"
msgstr "Zainstaluj ponownie"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:55
msgid "Deactivate demo plugins"
msgstr "Wyłącz wtyczki demo"

#: static/js/dashboard/screens/DemoInstall/Wizzard/PickBuilder.js:62
msgid "Choose Page Builder"
msgstr "Wybierz konstruktora strony"

#: static/js/dashboard/screens/DemoInstall/Wizzard/PickBuilder.js:65
msgid "This starter site can be imported and used with one of these page builders. Please select one in order to continue."
msgstr "Witrynę startową można zaimportować i używać z jednym z tych narzędzi do tworzenia stron. Wybierz preferowane dane, aby kontynuować."

#: static/js/dashboard/screens/DemoInstall/Wizzard/Plugins.js:71
msgid "Install & Activate Plugins"
msgstr "Zainstaluj i włącz wtyczki"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Plugins.js:73
msgid "The following plugins are required for this starter site in order to work properly."
msgstr "Aby witryna startowa działała poprawnie, wymagane są następujące wtyczki."

#: static/js/dashboard/screens/Extension.js:95
#: static/js/dashboard/screens/Extensions.js:86
msgid "Loading Extensions Status..."
msgstr "Wczytywanie stanu rozszerzeń..."

#: static/js/dashboard/screens/Extensions/Sidebar.js:60
msgid "Free Extensions"
msgstr "Bezpłatne rozszerzenia"

#: static/js/dashboard/screens/Extensions/Sidebar.js:68
msgid "Pro Extensions"
msgstr "Rozszerzenia Pro"

#: static/js/dashboard/screens/SiteExport.js:239
msgid "Builder"
msgstr "Konstruktor"

#: static/js/dashboard/screens/SiteExport.js:311
msgid "Export site"
msgstr "Eksport witryny"

#: framework/premium/extensions/mega-menu/extension.php:325
msgid "New"
msgstr "Nowości"

#: framework/premium/extensions/mega-menu/options.php:16,
#: framework/premium/extensions/mega-menu/options.php:581
msgid "Mega Menu Settings"
msgstr "Ustawienia Mega Menu"

#: framework/premium/extensions/mega-menu/options.php:28
msgid "Dropdown Width"
msgstr "Szerokość listy rozwijanej"

#: framework/premium/extensions/mega-menu/options.php:36,
#: framework/premium/extensions/mega-menu/options.php:49
msgid "Content Width"
msgstr "Szerokość treści"

#: framework/premium/extensions/mega-menu/options.php:37,
#: framework/premium/extensions/mega-menu/options.php:58
msgid "Full Width"
msgstr "Pełna szerokość"

#: framework/premium/extensions/mega-menu/options.php:38,
#: framework/premium/features/content-blocks/options/archive.php:86
msgid "Custom Width"
msgstr "Niestandardowa szerokość"

#: framework/premium/extensions/mega-menu/options.php:57
msgid "Default Width"
msgstr "Domyślna szerokość"

#: framework/premium/extensions/mega-menu/options.php:86,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:178,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:197
msgid "Columns"
msgstr "Kolumny"

#: framework/premium/extensions/mega-menu/options.php:333
msgid "Custom Content"
msgstr "Treść niestandardowa"

#: framework/premium/extensions/mega-menu/options.php:337
msgid "Content Type"
msgstr "Typ treści"

#: framework/premium/extensions/mega-menu/options.php:344
msgid "Default (Menu Item)"
msgstr "Domyślne (pozycja menu)"

#: framework/premium/extensions/mega-menu/options.php:345
msgid "Custom Text"
msgstr "Niestandardowy tekst"

#: framework/premium/extensions/mega-menu/options.php:432,
#: framework/premium/extensions/mega-menu/options.php:791
msgid "Item Label Settings"
msgstr "Ustawienia etykiety przedmiotu"

#: framework/premium/extensions/mega-menu/options.php:437
msgid "Item Label"
msgstr "Etykieta elementu"

#: framework/premium/extensions/mega-menu/options.php:448
msgid "Enabled"
msgstr "Włączono"

#: framework/premium/extensions/mega-menu/options.php:449,
#: framework/premium/features/content-blocks/options/404.php:111,
#: framework/premium/features/content-blocks/options/archive.php:188,
#: framework/premium/features/content-blocks/options/header.php:129,
#: framework/premium/features/content-blocks/options/hook.php:238,
#: framework/premium/features/content-blocks/options/maintenance.php:108,
#: framework/premium/features/content-blocks/options/nothing_found.php:129,
#: framework/premium/features/content-blocks/options/single.php:120
msgid "Disabled"
msgstr "Wyłączono"

#: framework/premium/extensions/mega-menu/options.php:450
msgid "Heading"
msgstr "Nagłówek"

#: framework/premium/extensions/mega-menu/options.php:456
msgid "Label Link"
msgstr "Link do etykiety"

#: framework/extensions/trending/customizer.php:496,
#: framework/premium/extensions/mega-menu/options.php:550,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:154,
#: framework/premium/features/premium-header/items/contacts/options.php:526,
#: framework/premium/features/premium-header/items/language-switcher/options.php:105,
#: framework/premium/features/premium-header/items/search-input/options.php:223
msgid "Vertical Alignment"
msgstr "Wyrównanie w pionie"

#: framework/features/header/header-options.php:209,
#: framework/features/header/header-options.php:236,
#: framework/features/header/header-options.php:251,
#: framework/features/header/header-options.php:266,
#: framework/premium/extensions/mega-menu/options.php:585,
#: framework/premium/extensions/shortcuts/customizer.php:1181,
#: framework/premium/extensions/shortcuts/customizer.php:1223,
#: framework/premium/extensions/shortcuts/customizer.php:1265,
#: framework/features/header/items/account/options.php:1733,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:37,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:77,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:325,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:196,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:541,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:577,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:611,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:480,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:512,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:542
msgid "Background"
msgstr "Tło"

#: framework/premium/extensions/mega-menu/options.php:599
msgid "Link Color"
msgstr "Kolor linku"

#: framework/premium/extensions/mega-menu/options.php:619,
#: framework/features/header/items/account/options.php:1933,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:634,
#: framework/premium/features/premium-header/items/contacts/options.php:641,
#: framework/premium/features/premium-header/items/contacts/options.php:681,
#: framework/premium/features/premium-header/items/contacts/options.php:720
#: static/js/editor/blocks/breadcrumbs/Edit.js:60
#: static/js/editor/blocks/contact-info/Edit.js:114
msgid "Link Initial"
msgstr "Odnośnik początkowy"

#: framework/premium/extensions/mega-menu/options.php:624
msgid "Link Hover/Active"
msgstr "Link najechany / aktywny"

#: framework/premium/extensions/mega-menu/options.php:629,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:149
msgid "Background Hover"
msgstr "Tło po najechaniu kursorem"

#: framework/premium/extensions/mega-menu/options.php:646,
#: framework/premium/extensions/mega-menu/options.php:803
msgid "Heading Color"
msgstr "Kolor nagłówka"

#: framework/premium/extensions/mega-menu/options.php:658,
#: framework/premium/extensions/mega-menu/options.php:677,
#: framework/premium/extensions/mega-menu/options.php:815
msgid "Initial Color"
msgstr "Kolor początkowy"

#: framework/extensions/cookies-consent/customizer.php:147,
#: framework/extensions/newsletter-subscribe/customizer.php:196,
#: framework/premium/extensions/mega-menu/options.php:665,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:342
#: static/js/editor/blocks/about-me/Edit.js:89
#: static/js/editor/blocks/breadcrumbs/Edit.js:43
#: static/js/editor/blocks/contact-info/Edit.js:96
msgid "Text Color"
msgstr "Kolor tekstu"

#: framework/premium/extensions/mega-menu/options.php:684,
#: framework/premium/extensions/shortcuts/customizer.php:1282,
#: framework/premium/features/premium-header/items/search-input/options.php:904,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:161
msgid "Items Divider"
msgstr "Separator elementów"

#: framework/premium/extensions/mega-menu/options.php:699
msgid "Columns Divider"
msgstr "Rozdzielacz kolumn"

#: framework/premium/extensions/mega-menu/options.php:714,
#: framework/premium/features/premium-header/items/search-input/options.php:886
msgid "Dropdown Shadow"
msgstr "Cień listy rozwijanej"

#: framework/premium/extensions/mega-menu/options.php:746
msgid "Column Settings"
msgstr "Ustawienia kolumny"

#: framework/premium/extensions/mega-menu/options.php:750
msgid "Column Spacing"
msgstr "Odstępy między kolumnami"

#: framework/premium/extensions/mega-menu/options.php:825,
#: framework/features/header/items/account/options.php:1327,
#: framework/features/header/items/account/options.php:1366,
#: framework/features/header/items/account/options.php:1409,
#: framework/features/header/items/account/options.php:1450,
#: framework/features/header/items/account/options.php:1738,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:300,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:328,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:359,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:388,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:331,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:213,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:353,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:382,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:413,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:442,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:354,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:382,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:417,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:450,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:308,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:336,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:367,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:396
#: static/js/editor/blocks/share-box/Edit.js:81
#: static/js/editor/blocks/socials/Edit.js:81
msgid "Icon Color"
msgstr "Kolor ikony"

#: framework/helpers/exts-configs.php:137
msgid "Enables support for Custom Fields inside archive cards and single page post title, adds a reading progress bar for your posts and lets you set featured images and colors for your categories archives."
msgstr "Włącza obsługę pól niestandardowych w kartach archiwum i tytułach postów na pojedynczej stronie, dodaje pasek postępu czytania dla twoich postów i pozwala ustawić wyróżnione obrazy i kolory dla archiwów kategorii."

#: framework/helpers/exts-configs.php:180,
#: framework/premium/extensions/shortcuts/config.php:6
msgid "Easily turn your websites into mobile first experiences. You can easily add the most important actions at the bottom of the screen for easy access."
msgstr "Z łatwością przekształć swoje strony internetowe w mobilne doświadczenia. Możesz łatwo dodać najważniejsze działania na dole ekranu, aby uzyskać łatwy dostęp."

#: framework/premium/extensions/shortcuts/customizer.php:12,
#: framework/premium/extensions/shortcuts/customizer.php:38,
#: framework/premium/extensions/shortcuts/customizer.php:790,
#: framework/premium/extensions/shortcuts/extension.php:173,
#: framework/premium/extensions/shortcuts/views/bar.php:13,
#: framework/premium/extensions/shortcuts/views/bar.php:46
msgid "Home"
msgstr "Strona główna"

#: framework/features/blocks/contact-info/options.php:139,
#: framework/premium/extensions/shortcuts/customizer.php:71,
#: framework/premium/extensions/shortcuts/customizer.php:97,
#: framework/premium/extensions/shortcuts/customizer.php:799,
#: framework/premium/extensions/shortcuts/extension.php:182,
#: framework/premium/extensions/shortcuts/views/bar.php:22,
#: framework/premium/extensions/shortcuts/views/bar.php:47,
#: framework/premium/features/premium-header/items/contacts/options.php:127
msgid "Phone"
msgstr "Telefon"

#: framework/premium/extensions/shortcuts/customizer.php:201,
#: framework/premium/extensions/shortcuts/customizer.php:227,
#: framework/premium/extensions/shortcuts/views/bar.php:49
msgid "Scroll Top"
msgstr "Przewiń do góry"

#: framework/premium/extensions/shortcuts/customizer.php:352,
#: framework/premium/extensions/shortcuts/customizer.php:378,
#: framework/premium/extensions/shortcuts/views/bar.php:50
msgid "Cart"
msgstr "Koszyk"

#: framework/premium/extensions/shortcuts/customizer.php:411,
#: framework/premium/extensions/shortcuts/customizer.php:437
msgid "Shop"
msgstr "Sklep"

#: framework/helpers/exts-configs.php:262,
#: framework/premium/extensions/shortcuts/customizer.php:545,
#: framework/premium/extensions/shortcuts/customizer.php:571,
#: framework/premium/extensions/shortcuts/views/bar.php:52,
#: framework/features/header/items/account/views/login.php:520,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:309,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:406,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:410,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:438,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:442,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/helpers.php:168,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:17,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/config.php:4,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:141,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/view.php:100
msgid "Wishlist"
msgstr "Lista życzeń"

#: framework/premium/extensions/shortcuts/customizer.php:784
msgid "Shortcuts"
msgstr "Skróty"

#: framework/premium/extensions/shortcuts/customizer.php:839,
#: framework/features/header/items/account/options.php:520,
#: framework/features/header/items/account/options.php:983,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:38,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:91,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:92
msgid "Label Visibility"
msgstr "Widoczność etykiety"

#: framework/premium/extensions/shortcuts/customizer.php:879,
#: framework/features/header/items/account/options.php:554,
#: framework/features/header/items/account/options.php:1017,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:88,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:122,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:123
msgid "Label Position"
msgstr "Położenie etykiety"

#: framework/premium/extensions/shortcuts/customizer.php:888,
#: framework/premium/features/content-blocks/hooks-manager.php:477,
#: framework/features/header/items/account/options.php:563,
#: framework/features/header/items/account/options.php:1035,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:97,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:21,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:131,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:132
msgid "Bottom"
msgstr "Na dole"

#: framework/premium/extensions/mega-menu/options.php:482,
#: framework/premium/extensions/shortcuts/customizer.php:925,
#: framework/features/header/items/account/options.php:508,
#: framework/features/header/items/account/options.php:882,
#: framework/features/header/items/account/options.php:968,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:46,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:439,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:202,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:42,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:73,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:74
msgid "Icon Size"
msgstr "Rozmiar ikony"

#: framework/premium/extensions/shortcuts/customizer.php:936,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:383
msgid "Container Height"
msgstr "Wysokość kontenera"

#: framework/premium/extensions/shortcuts/customizer.php:951
msgid "Container Max Width"
msgstr "Maksymalna szerokość kontenera"

#: framework/premium/extensions/shortcuts/customizer.php:975
msgid "Scroll Interaction"
msgstr "Przewiń interakcję"

#: framework/premium/extensions/shortcuts/customizer.php:981
msgid "Hide"
msgstr "Ukryj"

#: framework/premium/extensions/shortcuts/customizer.php:1023
msgid "Shortcuts Bar Display Conditions"
msgstr "Warunki wyświetlania paska skrótów"

#: framework/premium/extensions/shortcuts/customizer.php:1024
msgid "Add one or more conditions to display the shortcuts bar."
msgstr "Dodaj jeden lub więcej warunków, aby wyświetlić pasek skrótów."

#: framework/premium/extensions/shortcuts/customizer.php:1048,
#: framework/features/header/items/account/options.php:1895,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:184,
#: framework/premium/features/premium-header/items/contacts/options.php:577,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:105,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:212,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:42
msgid "Font"
msgstr "Krój pisma"

#: framework/features/blocks/about-me/options.php:187,
#: framework/features/blocks/share-box/options.php:141,
#: framework/features/blocks/socials/options.php:93,
#: framework/premium/extensions/shortcuts/customizer.php:1092,
#: framework/premium/features/premium-header/items/contacts/options.php:751,
#: framework/premium/features/premium-header/items/contacts/options.php:780,
#: framework/premium/features/premium-header/items/contacts/options.php:811,
#: framework/premium/features/premium-header/items/contacts/options.php:840
#: static/js/editor/blocks/about-me/Edit.js:117
#: static/js/editor/blocks/contact-info/Edit.js:133
msgid "Icons Color"
msgstr "Kolor ikonek"

#: framework/premium/extensions/shortcuts/customizer.php:1163
msgid "Cart Badge Color"
msgstr "Kolor odznaki koszyka"

#: framework/premium/extensions/shortcuts/customizer.php:1302
msgid "Items Divider Height"
msgstr "Wysokość rozdzielacza elementów"

#: framework/premium/extensions/shortcuts/customizer.php:1316,
#: framework/features/header/items/account/options.php:2016,
#: framework/premium/features/content-blocks/options/popup.php:545,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:701,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:150,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:175
msgid "Shadow"
msgstr "Cień"

#: framework/helpers/exts-configs.php:195
msgid "Create unlimited personalized sets of widget areas and display them on any page or post using our conditional logic functionality."
msgstr "Twórz nieograniczone spersonalizowane zestawy obszarów widżetów i wyświetlaj je na dowolnej stronie lub w dowolnym poście, korzystając z naszej logiki warunkowej."

#: framework/helpers/exts-configs.php:199
msgid "Create New Sidebar"
msgstr "Utwórz nowy pasek boczny"

#: framework/premium/extensions/sidebars/form.php:3
#: framework/premium/extensions/sidebars/static/js/main.js:50
msgid "Create Sidebar/Widget Area"
msgstr "Utwórz pasek boczny / obszar widżetu"

#: framework/premium/extensions/sidebars/form.php:6
msgid "In order to create a new sidebar/widget area simply enter a name in the input below and click the Create Sidebar button."
msgstr "Aby utworzyć nowy pasek boczny / obszar widżetów, po prostu wprowadź nazwę w polu poniżej i kliknij przycisk Utwórz pasek boczny."

#: framework/premium/extensions/sidebars/form.php:16
#: framework/premium/extensions/sidebars/static/js/main.js:66
msgid "Create Sidebar"
msgstr "Utwórz pasek boczny"

#: framework/premium/extensions/sidebars/form.php:20
msgid "Available Sidebars/Widget Areas"
msgstr "Dostępne paski boczne / obszary widżetów"

#: framework/helpers/exts-configs.php:215
msgid "Replace Blocksy's branding with your own. Easily hide licensing info and other sections of the theme and companion plugin from your clients and make your final product look more professional."
msgstr "Zastąp branding Blocksy swoim własnym. Z łatwością ukryj informacje licencyjne i inne sekcje motywu i wtyczki towarzyszącej przed klientami i spraw, aby Twój produkt końcowy wyglądał bardziej profesjonalnie."

#: framework/helpers/exts-configs.php:228
msgid "Make the shopping experience better for your visitors! Add features such as Product Quick View, Wishlist functionality and a Floating Add to Cart button. Customize the single product gallery/slider and the layout."
msgstr "Usprawnij zakupy dla odwiedzających! Dodaj funkcje takie jak Szybki podgląd produktu, funkcjonalność Listy życzeń i pływający przycisk Dodaj do Koszyka. Dostosuj galerię pojedynczego produktu / suwak i układ."

#: framework/premium/extensions/woocommerce-extra/features/quick-view/feature.php:14
msgid "Quick View Button"
msgstr "Przycisk szybkiego podglądu"

#: framework/features/header/items/account/options.php:467,
#: framework/features/header/items/account/options.php:841,
#: framework/features/header/items/account/options.php:929,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/archive-card/feature.php:13,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:66,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:111,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:65,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:66
msgid "Type 3"
msgstr "Typ 3"

#: framework/features/header/items/account/options.php:477,
#: framework/features/header/items/account/options.php:851,
#: framework/features/header/items/account/options.php:939,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:71,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:116
msgid "Type 4"
msgstr "Typ 4"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/views/panel.php:61
msgid "Available Filters"
msgstr "Dostępne filtry"

#: framework/premium/extensions/woocommerce-extra/extension.php:128
msgid "Quick view title before"
msgstr "Szybki podgląd tytułu przed"

#: framework/premium/extensions/woocommerce-extra/extension.php:133
msgid "Quick view title after"
msgstr "Szybki podgląd tytułu po"

#: framework/premium/extensions/woocommerce-extra/extension.php:138
msgid "Quick view price before"
msgstr "Szybki podgląd ceny przed"

#: framework/premium/extensions/woocommerce-extra/extension.php:143
msgid "Quick view price after"
msgstr "Szybki podgląd ceny po"

#: framework/premium/extensions/woocommerce-extra/extension.php:148
msgid "Quick view summary before"
msgstr "Szybki podgląd podsumowania przed"

#: framework/premium/extensions/woocommerce-extra/extension.php:153
msgid "Quick view summary after"
msgstr "Szybki podgląd podsumowania po"

#: framework/helpers/exts-configs.php:238,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:4
msgid "Floating Cart"
msgstr "Pływający koszyk"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:15
msgid "Position"
msgstr "Pozycja"

#: framework/premium/features/content-blocks/hooks-manager.php:439,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:20
msgid "Top"
msgstr "Na górze"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:68
msgid "Product Title Visibility"
msgstr "Widoczność tytułu produktu"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:30
msgid "Floating Cart Visibility"
msgstr "Widoczność pływającego koszyka"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/view.php:77
msgid "Go to product page"
msgstr "Przejdź do strony produktu"

#: framework/premium/extensions/shortcuts/customizer.php:507,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/helpers.php:40,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:111
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:108
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:137
msgid "Filter"
msgstr "Filtr"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:532
msgid "Filter Widgets"
msgstr "Filtruj widżety"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:536
msgid "Widgets"
msgstr "Widżety"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:580
msgid "Widgets Vertical Spacing"
msgstr "Odstępy pionowe widżetów"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:598
msgid "Widgets Font"
msgstr "Krój pisma widżetów"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:606
msgid "Widgets Font Color"
msgstr "Kolor tekstu widżetów"

#: framework/features/header/items/account/options.php:1927,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:134,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:628,
#: framework/premium/features/premium-header/items/contacts/options.php:635,
#: framework/premium/features/premium-header/items/contacts/options.php:676,
#: framework/premium/features/premium-header/items/contacts/options.php:715
msgid "Text Initial"
msgstr "Tekst Początkowy"

#: framework/features/header/items/account/options.php:1939,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:639,
#: framework/premium/features/premium-header/items/contacts/options.php:647,
#: framework/premium/features/premium-header/items/contacts/options.php:687,
#: framework/premium/features/premium-header/items/contacts/options.php:726
#: static/js/editor/blocks/breadcrumbs/Edit.js:66
#: static/js/editor/blocks/contact-info/Edit.js:123
msgid "Link Hover"
msgstr "Odnośnik po najechaniu"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:123
msgid "Panel Reveal"
msgstr "Odsłonięcie panelu"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:131,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:39
msgid "Left Side"
msgstr "Lewa strona"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:132,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:40
msgid "Right Side"
msgstr "Prawa strona"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:137,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:45
msgid "Panel Width"
msgstr "Szerokość panelu"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:295,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:163
msgid "Panel Shadow"
msgstr "Cień panelu"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:263,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:76
msgid "Panel Background"
msgstr "Tło panelu"

#: framework/premium/features/content-blocks/options/popup.php:615,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:779
msgid "Close Icon Color"
msgstr "Kolor ikony zamknięcia"

#: framework/premium/features/content-blocks/options/popup.php:646
msgid "Close Icon Background"
msgstr "Tło ikony zamknięcia"

#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:175
#: static/js/editor/blocks/share-box/index.js:47
msgid "Share Box"
msgstr "Pole udostępniania"

#: framework/premium/features/content-blocks/hooks-manager.php:11
msgid "WP head"
msgstr "WP head"

#: framework/premium/features/content-blocks/hooks-manager.php:13,
#: framework/premium/features/content-blocks/hooks-manager.php:22,
#: framework/premium/features/content-blocks/hooks-manager.php:32,
#: framework/premium/features/content-blocks/hooks-manager.php:41
msgid "Head"
msgstr "Head"

#: framework/premium/features/content-blocks/hooks-manager.php:20
msgid "WP head start"
msgstr "WP head start"

#: framework/premium/features/content-blocks/hooks-manager.php:30
msgid "WP head end"
msgstr "WP head end"

#: framework/premium/features/content-blocks/hooks-manager.php:48
msgid "Header before"
msgstr "Header before"

#: framework/premium/features/content-blocks/admin-ui.php:265,
#: framework/premium/features/content-blocks/hooks-manager.php:50,
#: framework/premium/features/content-blocks/hooks-manager.php:59
msgid "Header"
msgstr "Nagłówek"

#: framework/premium/features/content-blocks/hooks-manager.php:57
msgid "Header after"
msgstr "Header after"

#: framework/premium/features/content-blocks/hooks-manager.php:66
msgid "Desktop top"
msgstr "Desktop top"

#: framework/premium/features/content-blocks/hooks-manager.php:68,
#: framework/premium/features/content-blocks/hooks-manager.php:77,
#: framework/premium/features/content-blocks/hooks-manager.php:86,
#: framework/premium/features/content-blocks/hooks-manager.php:95
msgid "Header offcanvas"
msgstr "Header offcanvas"

#: framework/premium/features/content-blocks/hooks-manager.php:75
msgid "Desktop bottom"
msgstr "Desktop bottom"

#: framework/premium/features/content-blocks/hooks-manager.php:84
msgid "Mobile top"
msgstr "Mobile top"

#: framework/premium/features/content-blocks/hooks-manager.php:93
msgid "Mobile bottom"
msgstr "Mobile bottom"

#: framework/premium/features/content-blocks/hooks-manager.php:102
msgid "Sidebar before"
msgstr "Sidebar before"

#: framework/premium/features/content-blocks/hooks-manager.php:103,
#: framework/premium/features/content-blocks/hooks-manager.php:110,
#: framework/premium/features/content-blocks/hooks-manager.php:117,
#: framework/premium/features/content-blocks/hooks-manager.php:124
msgid "Left/Right sidebar"
msgstr "Lewy / Prawy pasek boczny"

#: framework/premium/features/content-blocks/hooks-manager.php:109
msgid "Sidebar start"
msgstr "Sidebar start"

#: framework/premium/features/content-blocks/hooks-manager.php:116
msgid "Sidebar end"
msgstr "Sidebar end"

#: framework/premium/features/content-blocks/hooks-manager.php:123
msgid "Sidebar after"
msgstr "Sidebar after"

#: framework/premium/features/content-blocks/hooks-manager.php:130
msgid "Dynamic sidebar before"
msgstr "Dynamic sidebar before"

#: framework/premium/features/content-blocks/hooks-manager.php:131,
#: framework/premium/features/content-blocks/hooks-manager.php:138,
#: framework/premium/features/content-blocks/hooks-manager.php:146
msgid "All widget areas"
msgstr "Wszystkie obszary widżetów"

#: framework/premium/features/content-blocks/hooks-manager.php:137
msgid "Dynamic sidebar"
msgstr "Dynamic sidebar"

#: framework/premium/features/content-blocks/hooks-manager.php:145
msgid "Dynamic sidebar after"
msgstr "Dynamic sidebar after"

#: framework/premium/features/content-blocks/hooks-manager.php:154
msgid "Before section"
msgstr "Before section"

#: framework/premium/features/content-blocks/hooks-manager.php:155,
#: framework/premium/features/content-blocks/hooks-manager.php:163,
#: framework/premium/features/content-blocks/hooks-manager.php:171,
#: framework/premium/features/content-blocks/hooks-manager.php:179,
#: framework/premium/features/content-blocks/hooks-manager.php:187,
#: framework/premium/features/content-blocks/hooks-manager.php:195,
#: framework/premium/features/content-blocks/hooks-manager.php:203,
#: framework/premium/features/content-blocks/hooks-manager.php:211,
#: framework/premium/features/content-blocks/hooks-manager.php:219,
#: framework/premium/features/content-blocks/hooks-manager.php:227,
#: framework/premium/features/content-blocks/hooks-manager.php:235,
#: framework/premium/features/content-blocks/hooks-manager.php:243,
#: framework/premium/features/content-blocks/hooks-manager.php:251,
#: framework/premium/features/content-blocks/hooks-manager.php:259
msgid "Page/post title"
msgstr "Tytuł strony / wpisu"

#: framework/premium/features/content-blocks/hooks-manager.php:162,
#: framework/premium/features/content-blocks/hooks-manager.php:312,
#: framework/premium/features/content-blocks/hooks-manager.php:354
msgid "Before title"
msgstr "Przed tytulem"

#: framework/premium/features/content-blocks/hooks-manager.php:186
msgid "Before post meta"
msgstr "Before post meta"

#: framework/premium/features/content-blocks/hooks-manager.php:210,
#: framework/premium/features/content-blocks/hooks-manager.php:319,
#: framework/premium/features/content-blocks/hooks-manager.php:361
msgid "After title"
msgstr "Po tytule"

#: framework/premium/features/content-blocks/hooks-manager.php:250
msgid "After post meta"
msgstr "After post meta"

#: framework/premium/features/content-blocks/hooks-manager.php:258
msgid "After section"
msgstr "Po sekcji"

#: framework/premium/features/content-blocks/hooks-manager.php:266
msgid "Before content"
msgstr "Przed treścią"

#: framework/premium/features/content-blocks/hooks-manager.php:274,
#: framework/premium/features/content-blocks/hooks-manager.php:447
msgid "Top content"
msgstr "Top content"

#: framework/premium/features/content-blocks/hooks-manager.php:282,
#: framework/premium/features/content-blocks/hooks-manager.php:469
msgid "Bottom content"
msgstr "Bottom content"

#: framework/premium/features/content-blocks/hooks-manager.php:290
msgid "After content"
msgstr "Po treści"

#: framework/premium/features/content-blocks/hooks-manager.php:298
msgid "Before comments"
msgstr "Przed komentarzami"

#: framework/premium/features/content-blocks/hooks-manager.php:299,
#: framework/premium/features/content-blocks/hooks-manager.php:306,
#: framework/premium/features/content-blocks/hooks-manager.php:313,
#: framework/premium/features/content-blocks/hooks-manager.php:320,
#: framework/premium/features/content-blocks/hooks-manager.php:327,
#: framework/premium/features/content-blocks/hooks-manager.php:334
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:60
msgid "Comments"
msgstr "Komentarze"

#: framework/premium/features/content-blocks/hooks-manager.php:305
msgid "Top comments"
msgstr "Top comments"

#: framework/premium/features/content-blocks/hooks-manager.php:326
msgid "Bottom comments"
msgstr "Bottom comments"

#: framework/premium/features/content-blocks/hooks-manager.php:333
msgid "After comments"
msgstr "Po komentarzach"

#: framework/premium/features/content-blocks/hooks-manager.php:411,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:705
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:298
msgid "Before"
msgstr "Przed"

#: framework/premium/features/content-blocks/hooks-manager.php:412,
#: framework/premium/features/content-blocks/hooks-manager.php:419
msgid "Loop"
msgstr "Pętla"

#: framework/premium/features/content-blocks/hooks-manager.php:418,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:716
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:307
msgid "After"
msgstr "Po"

#: framework/premium/features/content-blocks/hooks-manager.php:425
msgid "Start"
msgstr "Początek"

#: framework/premium/features/content-blocks/hooks-manager.php:426,
#: framework/premium/features/content-blocks/hooks-manager.php:433
msgid "Loop card"
msgstr "Loop card"

#: framework/premium/features/content-blocks/hooks-manager.php:432
msgid "End"
msgstr "Koniec"

#: framework/premium/features/content-blocks/hooks-manager.php:455
msgid "After certain number of blocks"
msgstr "After certain number of blocks"

#: framework/premium/features/content-blocks/hooks-manager.php:462
msgid "Before certain number of headings"
msgstr "Before certain number of headings"

#: framework/premium/features/content-blocks/hooks-manager.php:485
msgid "Login form start"
msgstr "Login form start"

#: framework/premium/features/content-blocks/hooks-manager.php:486,
#: framework/premium/features/content-blocks/hooks-manager.php:493,
#: framework/premium/features/content-blocks/hooks-manager.php:500,
#: framework/premium/features/content-blocks/hooks-manager.php:507,
#: framework/premium/features/content-blocks/hooks-manager.php:514,
#: framework/premium/features/content-blocks/hooks-manager.php:521,
#: framework/premium/features/content-blocks/hooks-manager.php:528,
#: framework/premium/features/content-blocks/hooks-manager.php:535,
#: framework/premium/features/content-blocks/hooks-manager.php:542,
#: framework/premium/features/content-blocks/hooks-manager.php:549
msgid "Auth forms"
msgstr "Auth forms"

#: framework/premium/features/content-blocks/hooks-manager.php:492
msgid "Login form end"
msgstr "Login form end"

#: framework/premium/features/content-blocks/hooks-manager.php:499
msgid "Login form modal start"
msgstr "Login form modal start"

#: framework/premium/features/content-blocks/hooks-manager.php:506
msgid "Login form modal end"
msgstr "Login form modal end"

#: framework/premium/features/content-blocks/hooks-manager.php:513
msgid "Register form start"
msgstr "Początek formularza rejestracyjnego"

#: framework/premium/features/content-blocks/hooks-manager.php:520
msgid "Register form end"
msgstr "Koniec formularza rejestracyjnego"

#: framework/premium/features/content-blocks/hooks-manager.php:527
msgid "Register form modal start"
msgstr "Register form modal start"

#: framework/premium/features/content-blocks/hooks-manager.php:534
msgid "Register form modal end"
msgstr "Register form modal end"

#: framework/premium/features/content-blocks/hooks-manager.php:541
msgid "Lost password form modal start"
msgstr "Lost password form modal start"

#: framework/premium/features/content-blocks/hooks-manager.php:548
msgid "Lost password form modal end"
msgstr "Lost password form modal end"

#: framework/premium/features/content-blocks/hooks-manager.php:556
msgid "Before main content"
msgstr "Przed główną treścią"

#: framework/premium/features/content-blocks/hooks-manager.php:562
msgid "After main content"
msgstr "Po głównej treści"

#: framework/premium/features/content-blocks/hooks-manager.php:583
msgid "WooCommerce Global"
msgstr "WooCommerce Global"

#: framework/premium/features/content-blocks/hooks-manager.php:588
msgid "Archive description"
msgstr "Opis archiwum"

#: framework/premium/features/content-blocks/hooks-manager.php:593
msgid "Before shop loop"
msgstr "Before shop loop"

#: framework/premium/features/content-blocks/hooks-manager.php:610
msgid "Before shop loop item title"
msgstr "Before shop loop item title"

#: framework/premium/features/content-blocks/hooks-manager.php:615
msgid "After shop loop item title"
msgstr "After shop loop item title"

#: framework/premium/features/content-blocks/hooks-manager.php:620
msgid "Before shop loop item price"
msgstr "Before shop loop item price"

#: framework/premium/features/content-blocks/hooks-manager.php:625
msgid "After shop loop item price"
msgstr "After shop loop item price"

#: framework/premium/features/content-blocks/hooks-manager.php:640
msgid "After shop loop"
msgstr "After shop loop"

#: framework/premium/features/content-blocks/hooks-manager.php:642
msgid "WooCommerce Archive"
msgstr "WooCommerce Archive"

#: framework/premium/features/content-blocks/hooks-manager.php:647
msgid "Before single product"
msgstr "Before single product"

#: framework/premium/features/content-blocks/hooks-manager.php:665
msgid "Product meta start"
msgstr "Product meta start"

#: framework/premium/features/content-blocks/hooks-manager.php:669
msgid "Product meta end"
msgstr "Product meta end"

#: framework/premium/features/content-blocks/hooks-manager.php:673
msgid "Share"
msgstr "Udostępnij"

#: framework/premium/features/content-blocks/hooks-manager.php:677
msgid "After single product"
msgstr "After single product"

#: framework/premium/features/content-blocks/hooks-manager.php:683
msgid "Before single product excerpt"
msgstr "Before single product excerpt"

#: framework/premium/features/content-blocks/hooks-manager.php:688
msgid "After single product excerpt"
msgstr "Po fragmencie pojedynczego produktu"

#: framework/premium/features/content-blocks/hooks-manager.php:693
msgid "Before single product tabs"
msgstr "Before single product tabs"

#: framework/premium/features/content-blocks/hooks-manager.php:699
msgid "After single product tabs"
msgstr "After single product tabs"

#: framework/premium/features/content-blocks/hooks-manager.php:738
msgid "Cart is empty"
msgstr "Koszyk jest pusty"

#: framework/premium/features/content-blocks/hooks-manager.php:742
msgid "Before cart"
msgstr "Before cart"

#: framework/premium/features/content-blocks/hooks-manager.php:746
msgid "Before cart table"
msgstr "Before cart table"

#: framework/premium/features/content-blocks/hooks-manager.php:750
msgid "Before cart contents"
msgstr "Before cart contents"

#: framework/premium/features/content-blocks/hooks-manager.php:754
msgid "Cart contents"
msgstr "Cart contents"

#: framework/premium/features/content-blocks/hooks-manager.php:758
msgid "After cart contents"
msgstr "After cart contents"

#: framework/premium/features/content-blocks/hooks-manager.php:762
msgid "Cart coupon"
msgstr "Kupon koszyka"

#: framework/premium/features/content-blocks/hooks-manager.php:766
msgid "Cart actions"
msgstr "Akcje koszyka"

#: framework/premium/features/content-blocks/hooks-manager.php:770
msgid "After cart table"
msgstr "After cart table"

#: framework/premium/features/content-blocks/hooks-manager.php:774
msgid "Cart collaterals"
msgstr "Cart collaterals"

#: framework/premium/features/content-blocks/hooks-manager.php:778
msgid "Before cart totals"
msgstr "Before cart totals"

#: framework/premium/features/content-blocks/hooks-manager.php:782
msgid "Cart totals before order total"
msgstr "Cart totals before order total"

#: framework/premium/features/content-blocks/hooks-manager.php:786
msgid "Cart totals after order total"
msgstr "Cart totals after order total"

#: framework/premium/features/content-blocks/hooks-manager.php:790
msgid "Proceed to checkout"
msgstr "Proceed to checkout"

#: framework/premium/features/content-blocks/hooks-manager.php:794
msgid "After cart totals"
msgstr "After cart totals"

#: framework/premium/features/content-blocks/hooks-manager.php:798
msgid "After cart"
msgstr "After cart"

#: framework/premium/features/content-blocks/hooks-manager.php:803
msgid "Before Mini Cart"
msgstr "Before Mini Cart"

#: framework/premium/features/content-blocks/hooks-manager.php:808
msgid "Before Mini Cart Contents"
msgstr "Before Mini Cart Contents"

#: framework/premium/features/content-blocks/hooks-manager.php:813
msgid "Mini Cart Contents"
msgstr "Mini Cart Contents"

#: framework/premium/features/content-blocks/hooks-manager.php:818
msgid "Widget Shopping Cart Before Buttons"
msgstr "Widget Shopping Cart Before Buttons"

#: framework/premium/features/content-blocks/hooks-manager.php:823
msgid "Widget Shopping Cart After Buttons"
msgstr "Widget Shopping Cart After Buttons"

#: framework/premium/features/content-blocks/hooks-manager.php:828
msgid "After Mini Cart"
msgstr "After Mini Cart"

#: framework/premium/features/content-blocks/hooks-manager.php:830
msgid "WooCommerce Cart"
msgstr "WooCommerce Cart"

#: framework/premium/features/content-blocks/hooks-manager.php:836
msgid "Before checkout form"
msgstr "Before checkout form"

#: framework/premium/features/content-blocks/hooks-manager.php:840
msgid "Before customer details"
msgstr "Before customer details"

#: framework/premium/features/content-blocks/hooks-manager.php:844
msgid "After customer details"
msgstr "After customer details"

#: framework/premium/features/content-blocks/hooks-manager.php:848
msgid "Checkout billing"
msgstr "Checkout billing"

#: framework/premium/features/content-blocks/hooks-manager.php:852
msgid "Before checkout billing form"
msgstr "Before checkout billing form"

#: framework/premium/features/content-blocks/hooks-manager.php:856
msgid "After checkout billing form"
msgstr "After checkout billing form"

#: framework/premium/features/content-blocks/hooks-manager.php:860
msgid "Before order notes"
msgstr "Before order notes"

#: framework/premium/features/content-blocks/hooks-manager.php:864
msgid "After order notes"
msgstr "After order notes"

#: framework/premium/features/content-blocks/hooks-manager.php:868
msgid "Checkout shipping"
msgstr "Checkout shipping"

#: framework/premium/features/content-blocks/hooks-manager.php:872
msgid "Checkout before order review"
msgstr "Checkout before order review"

#: framework/premium/features/content-blocks/hooks-manager.php:876
msgid "Checkout order review"
msgstr "Checkout order review"

#: framework/premium/features/content-blocks/hooks-manager.php:880
msgid "Review order before cart contents"
msgstr "Review order before cart contents"

#: framework/premium/features/content-blocks/hooks-manager.php:884
msgid "Review order after cart contents"
msgstr "Review order after cart contents"

#: framework/premium/features/content-blocks/hooks-manager.php:888
msgid "Review order before order total"
msgstr "Review order before order total"

#: framework/premium/features/content-blocks/hooks-manager.php:892
msgid "Review order after order total"
msgstr "Review order after order total"

#: framework/premium/features/content-blocks/hooks-manager.php:896
msgid "Review order before payment"
msgstr "Review order before payment"

#: framework/premium/features/content-blocks/hooks-manager.php:900
msgid "Review order before submit"
msgstr "Review order before submit"

#: framework/premium/features/content-blocks/hooks-manager.php:904
msgid "Review order after submit"
msgstr "Review order after submit"

#: framework/premium/features/content-blocks/hooks-manager.php:908
msgid "Review order after payment"
msgstr "Review order after payment"

#: framework/premium/features/content-blocks/hooks-manager.php:912
msgid "Checkout after order review"
msgstr "Checkout after order review"

#: framework/premium/features/content-blocks/hooks-manager.php:916
msgid "After checkout form"
msgstr "After checkout form"

#: framework/premium/features/content-blocks/hooks-manager.php:919
msgid "WooCommerce Checkout"
msgstr "WooCommerce Checkout"

#: framework/premium/features/content-blocks/hooks-manager.php:925
msgid "Before my account"
msgstr "Before my account"

#: framework/premium/features/content-blocks/hooks-manager.php:929
msgid "Before account navigation"
msgstr "Before account navigation"

#: framework/premium/features/content-blocks/hooks-manager.php:933
msgid "Account navigation"
msgstr "Account navigation"

#: framework/premium/features/content-blocks/hooks-manager.php:937
msgid "After account navigation"
msgstr "After account navigation"

#: framework/premium/features/content-blocks/hooks-manager.php:941
msgid "Account content"
msgstr "Account content"

#: framework/premium/features/content-blocks/hooks-manager.php:945
msgid "Account dashboard"
msgstr "Account dashboard"

#: framework/premium/features/content-blocks/hooks-manager.php:949
msgid "After my account"
msgstr "After my account"

#: framework/premium/features/content-blocks/hooks-manager.php:951,
#: framework/features/header/items/account/options.php:169,
#: framework/features/header/items/account/options.php:284,
#: framework/features/header/items/account/options.php:285
msgid "WooCommerce Account"
msgstr "Konto WooCommerce"

#: framework/premium/features/content-blocks/hooks-manager.php:977
msgid "WP footer"
msgstr "WP footer"

#: framework/premium/features/content-blocks/admin-ui.php:266,
#: framework/premium/features/content-blocks/hooks-manager.php:978,
#: framework/premium/features/content-blocks/hooks-manager.php:986,
#: framework/premium/features/content-blocks/hooks-manager.php:994
msgid "Footer"
msgstr "Stopka"

#: framework/premium/features/content-blocks/hooks-manager.php:985
msgid "Footer before"
msgstr "Footer before"

#: framework/premium/features/content-blocks/hooks-manager.php:993
msgid "Footer after"
msgstr "Footer after"

#: framework/premium/features/content-blocks/hooks-manager.php:1009
msgid "Custom Hook (%s)"
msgstr "Custom Hook (%s)"

#: framework/premium/features/content-blocks/hooks-manager.php:1015,
#: framework/premium/features/content-blocks/options/hook.php:116
#: framework/premium/static/js/options/MultipleLocationsSelect.js:94
msgid "After Block Number"
msgstr "After Block Number"

#: framework/premium/features/content-blocks/hooks-manager.php:1021,
#: framework/premium/features/content-blocks/options/hook.php:133
#: framework/premium/static/js/options/MultipleLocationsSelect.js:116
msgid "Before Heading Number"
msgstr "Before Heading Number"

#: static/js/editor/blocks/about-me/index.js:45
msgid "About Me"
msgstr "O mnie"

#: framework/features/blocks/about-me/options.php:16
msgid "About me"
msgstr "O mnie"

#: framework/features/blocks/about-me/options.php:49,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:31,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:729,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:68,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:99,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:143,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:166,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:166
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:96
msgid "Image"
msgstr "Obrazek"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:126
msgid "Upload Image"
msgstr "Prześlij obrazek"

#: framework/features/blocks/about-me/options.php:53,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:96,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:106,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:809,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:819,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:72
msgid "Select Image"
msgstr "Wybierz obrazek"

#: framework/features/blocks/about-me/options.php:54
msgid "Change Image"
msgstr "Zmień obrazek"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:46,
#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:20,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:123,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:112,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:65
msgid "Image Ratio"
msgstr "Współczynnik proporcji obrazka"

#: framework/features/blocks/contact-info/options.php:520,
#: framework/premium/extensions/shortcuts/customizer.php:307
msgid "Open link in new tab"
msgstr "Otwórz odnośnik w nowej karcie"

#: framework/features/blocks/contact-info/options.php:35
#: static/js/editor/blocks/contact-info/index.js:54
msgid "Contact Info"
msgstr "Dane kontaktowe"

#: framework/features/blocks/contact-info/options.php:51,
#: framework/features/blocks/contact-info/options.php:81,
#: framework/features/blocks/contact-info/view.php:15,
#: framework/premium/features/premium-header/items/contacts/options.php:57,
#: framework/premium/features/premium-header/items/contacts/options.php:88,
#: framework/premium/features/premium-header/items/contacts/view.php:35
msgid "Address:"
msgstr "Adres:"

#: framework/features/blocks/contact-info/options.php:59,
#: framework/features/blocks/contact-info/options.php:146,
#: framework/features/blocks/contact-info/view.php:23,
#: framework/premium/features/premium-header/items/contacts/options.php:65,
#: framework/premium/features/premium-header/items/contacts/options.php:133,
#: framework/premium/features/premium-header/items/contacts/view.php:43
msgid "Phone:"
msgstr "Telefon:"

#: framework/features/blocks/contact-info/options.php:67,
#: framework/features/blocks/contact-info/options.php:209,
#: framework/features/blocks/contact-info/view.php:31,
#: framework/premium/features/premium-header/items/contacts/options.php:73,
#: framework/premium/features/premium-header/items/contacts/options.php:178,
#: framework/premium/features/premium-header/items/contacts/view.php:51
msgid "Mobile:"
msgstr "Telefon komórkowy:"

#: framework/features/blocks/contact-info/options.php:75,
#: framework/premium/features/premium-header/items/contacts/options.php:82
msgid "Address"
msgstr "Adres"

#: framework/features/blocks/contact-info/options.php:94,
#: framework/features/blocks/contact-info/options.php:159,
#: framework/features/blocks/contact-info/options.php:222,
#: framework/features/blocks/contact-info/options.php:285,
#: framework/features/blocks/contact-info/options.php:348,
#: framework/features/blocks/contact-info/options.php:411,
#: framework/features/blocks/contact-info/options.php:474,
#: framework/premium/features/premium-header/items/contacts/options.php:107,
#: framework/premium/features/premium-header/items/contacts/options.php:152,
#: framework/premium/features/premium-header/items/contacts/options.php:197,
#: framework/premium/features/premium-header/items/contacts/options.php:242,
#: framework/premium/features/premium-header/items/contacts/options.php:288,
#: framework/premium/features/premium-header/items/contacts/options.php:333,
#: framework/premium/features/premium-header/items/contacts/options.php:378
msgid "Link (optional)"
msgstr "Odnośnik (opcjonalnie)"

#: framework/features/blocks/contact-info/options.php:265,
#: framework/premium/features/premium-header/items/contacts/options.php:217
msgid "Work Hours"
msgstr "Godziny pracy"

#: framework/features/blocks/contact-info/options.php:272,
#: framework/premium/features/premium-header/items/contacts/options.php:223
msgid "Opening hours"
msgstr "Godziny otwarcia"

#: framework/features/blocks/contact-info/options.php:328,
#: framework/premium/features/premium-header/items/contacts/options.php:263
msgid "Fax"
msgstr "Faks"

#: framework/features/blocks/contact-info/options.php:335,
#: framework/premium/features/premium-header/items/contacts/options.php:269
msgid "Fax:"
msgstr "Faks:"

#: framework/features/blocks/contact-info/options.php:398,
#: framework/premium/features/premium-header/items/contacts/options.php:314
msgid "Email:"
msgstr "Adres e-mail:"

#: framework/features/blocks/contact-info/options.php:454,
#: framework/premium/features/premium-header/items/contacts/options.php:353
msgid "Website"
msgstr "Witryna internetowa"

#: framework/features/blocks/contact-info/options.php:461,
#: framework/premium/features/premium-header/items/contacts/options.php:359
msgid "Website:"
msgstr "Witryna internetowa:"

#: framework/features/blocks/about-me/options.php:85,
#: framework/premium/features/content-blocks/options/archive.php:73
msgid "Small"
msgstr "Mały"

#: framework/features/blocks/about-me/options.php:87
msgid "Large"
msgstr "Duży"

#: framework/features/blocks/about-me/options.php:200,
#: framework/features/blocks/contact-info/options.php:557,
#: framework/features/blocks/share-box/options.php:154,
#: framework/features/blocks/socials/options.php:106,
#: framework/premium/features/premium-header/items/contacts/options.php:445
msgid "Icons Shape Type"
msgstr "Rodzaj kształtu ikonki"

#: framework/features/blocks/about-me/options.php:97,
#: framework/features/blocks/about-me/options.php:205,
#: framework/features/blocks/contact-info/options.php:565,
#: framework/features/blocks/share-box/options.php:161,
#: framework/features/blocks/socials/options.php:113,
#: framework/premium/features/premium-header/items/contacts/options.php:452
msgid "Rounded"
msgstr "Zaokrąglone"

#: framework/features/blocks/about-me/options.php:98,
#: framework/features/blocks/about-me/options.php:206,
#: framework/features/blocks/contact-info/options.php:566,
#: framework/features/blocks/share-box/options.php:162,
#: framework/features/blocks/socials/options.php:114,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:27,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:136,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:190,
#: framework/premium/features/premium-header/items/contacts/options.php:453
#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:86
msgid "Square"
msgstr "Kwadrat"

#: framework/features/blocks/about-me/options.php:215,
#: framework/features/blocks/contact-info/options.php:575,
#: framework/features/blocks/share-box/options.php:171,
#: framework/features/blocks/socials/options.php:123,
#: framework/premium/features/premium-header/items/contacts/options.php:464
msgid "Shape Fill Type"
msgstr "Rodzaj wypełnienia kształtu"

#: framework/features/blocks/about-me/options.php:220,
#: framework/features/blocks/contact-info/options.php:582,
#: framework/features/blocks/share-box/options.php:178,
#: framework/features/blocks/socials/options.php:130,
#: framework/premium/features/premium-header/items/contacts/options.php:472
msgid "Solid"
msgstr "Jednolite"

#: framework/features/blocks/about-me/options.php:219,
#: framework/features/blocks/contact-info/options.php:581,
#: framework/features/blocks/share-box/options.php:177,
#: framework/features/blocks/socials/options.php:129,
#: framework/premium/features/premium-header/items/contacts/options.php:471
msgid "Outline"
msgstr "Kontur"

#: framework/extensions/trending/customizer.php:273,
#: framework/extensions/trending/customizer.php:287
#: static/js/editor/blocks/query/Edit.js:169
msgid "Post Type"
msgstr "Typ treści"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:149
msgid "Most commented"
msgstr "Najczęściej komentowane"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:165
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:144
msgid "Random"
msgstr "Losowo"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:117
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:112
msgid "Order by"
msgstr "Kolejność sortowania"

#: framework/features/blocks/dynamic-data/options.php:109,
#: framework/features/blocks/dynamic-data/views/wp-field.php:196
msgid "% comments"
msgstr "% komentarzy"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:80
msgid "Author Avatar"
msgstr "Awatar autora"

#: framework/features/blocks/socials/options.php:15
msgid "Social Icons"
msgstr "Ikony mediów społecznościowych"

#: framework/features/blocks/about-me/options.php:124
msgid "You can configure social URLs in %s."
msgstr "Możesz skonfigurować adresy URL do mediów społecznościowych w %s."

#: framework/features/blocks/about-me/options.php:156,
#: framework/features/blocks/socials/options.php:62,
#: framework/premium/features/premium-header/items/contacts/options.php:405
msgid "Open links in new tab"
msgstr "Otwórz odnośniki w nowej zakładce"

#: framework/features/blocks/about-me/options.php:163,
#: framework/features/blocks/contact-info/options.php:527,
#: framework/features/blocks/share-box/options.php:117,
#: framework/features/blocks/socials/options.php:69,
#: framework/premium/features/premium-header/items/contacts/options.php:411
msgid "Set links to nofollow"
msgstr "Ustaw odnośniki na nofollow"

#: framework/features/header/items/account/options.php:4
msgid "Profile Page"
msgstr "Strona profilowa"

#: framework/features/header/items/account/options.php:5
msgid "Dashboard Page"
msgstr "Strona kokpitu"

#: framework/features/header/items/account/options.php:7,
#: framework/features/header/items/account/options.php:36,
#: framework/features/header/items/account/options.php:117,
#: framework/features/header/items/account/options.php:280,
#: framework/features/header/items/account/views/login.php:396
msgid "Custom Link"
msgstr "Własny odnośnik"

#: framework/features/header/items/account/options.php:6
msgid "Logout"
msgstr "Wyloguj się"

#: framework/features/header/items/account/options.php:279
msgid "Modal"
msgstr "Okno modalne"

#: framework/features/header/items/account/options.php:295
msgid "Customizing: Logged in State"
msgstr "Dostosowywanie: Stan zalogowania"

#: framework/features/header/items/account/options.php:319
msgid "Logged In Options"
msgstr "Opcje logowania się"

#: framework/features/header/items/account/options.php:324
msgid "Logged Out Options"
msgstr "Opcje wylogowania się"

#: framework/features/header/items/account/options.php:611,
#: framework/features/header/items/account/options.php:1059
msgid "Account Action"
msgstr "Działanie na koncie"

#: framework/features/header/items/account/options.php:145
msgid "Select Menu"
msgstr "Wybierz menu"

#: framework/features/header/items/account/options.php:151
msgid "Select menu..."
msgstr "Wybierz menu..."

#. translators: placeholder here means the actual URL.
#: framework/features/header/items/account/options.php:155
msgid "Manage your menus in the %sMenus screen%s."
msgstr "Zarządzaj pozycjami menu na %sekranie menu%s."

#: framework/features/header/items/account/options.php:702,
#: framework/features/header/items/account/options.php:1073
msgid "Custom Page Link"
msgstr "Własny odnośnik do strony"

#: framework/features/header/items/account/options.php:340,
#: framework/features/header/items/account/options.php:739
msgid "Account Image"
msgstr "Obrazek konta"

#: framework/features/header/items/account/options.php:346
msgid "Avatar"
msgstr "Awatar"

#: framework/features/header/items/account/options.php:357
msgid "Avatar Size"
msgstr "Rozmiar awatara"

#: framework/features/header/items/account/options.php:487,
#: framework/features/header/items/account/options.php:861,
#: framework/features/header/items/account/options.php:949
msgid "Type 5"
msgstr "Typ 5"

#: framework/features/header/items/account/options.php:497,
#: framework/features/header/items/account/options.php:871,
#: framework/features/header/items/account/options.php:959
msgid "Type 6"
msgstr "Typ 6"

#: framework/features/header/items/account/options.php:570
msgid "Label Type"
msgstr "Rodzaj etykiety"

#: framework/features/header/items/account/options.php:587,
#: framework/features/header/items/account/options.php:1045,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:136,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:137
msgid "Label Text"
msgstr "Treść etykiety"

#: framework/features/header/items/account/options.php:173,
#: framework/features/header/items/account/options.php:597,
#: framework/features/header/items/account/views/login.php:100,
#: framework/features/header/items/account/views/login.php:472
msgid "My Account"
msgstr "Moje konto"

#: framework/features/header/items/account/options.php:1125,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:163,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:167,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:170
msgid "Label Font"
msgstr "Krój pisma etykiety"

#: framework/features/header/items/account/options.php:1135,
#: framework/features/header/items/account/options.php:1174,
#: framework/features/header/items/account/options.php:1218,
#: framework/features/header/items/account/options.php:1260
msgid "Label Color"
msgstr "Kolor etykiety"

#: framework/features/header/header-options.php:214,
#: framework/features/header/items/account/options.php:1140,
#: framework/features/header/items/account/options.php:1332,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:179,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:305,
#: framework/premium/features/premium-header/items/contacts/options.php:590,
#: framework/premium/features/premium-header/items/contacts/options.php:756,
#: framework/premium/features/premium-header/items/contacts/options.php:901,
#: framework/premium/features/premium-header/items/divider/options.php:27,
#: framework/premium/features/premium-header/items/search-input/options.php:271,
#: framework/premium/features/premium-header/items/search-input/options.php:401,
#: framework/premium/features/premium-header/items/search-input/options.php:531,
#: framework/premium/features/premium-header/items/search-input/options.php:667,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:227,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:358,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:57,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:183,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:359,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:492,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:186,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:313,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:434
msgid "Default State"
msgstr "Stan domyślny"

#: framework/features/header/header-options.php:219,
#: framework/features/header/items/account/options.php:1148,
#: framework/features/header/items/account/options.php:1340,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:184,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:310,
#: framework/premium/features/premium-header/items/contacts/options.php:595,
#: framework/premium/features/premium-header/items/contacts/options.php:761,
#: framework/premium/features/premium-header/items/contacts/options.php:909,
#: framework/premium/features/premium-header/items/divider/options.php:32,
#: framework/premium/features/premium-header/items/search-input/options.php:276,
#: framework/premium/features/premium-header/items/search-input/options.php:406,
#: framework/premium/features/premium-header/items/search-input/options.php:536,
#: framework/premium/features/premium-header/items/search-input/options.php:672,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:232,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:363,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:62,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:191,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:364,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:500,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:191,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:318,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:442
msgid "Transparent State"
msgstr "Stan przezroczysty"

#: framework/features/header/header-options.php:227,
#: framework/features/header/items/account/options.php:1161,
#: framework/features/header/items/account/options.php:1353,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:193,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:319,
#: framework/premium/features/premium-header/items/contacts/options.php:604,
#: framework/premium/features/premium-header/items/contacts/options.php:770,
#: framework/premium/features/premium-header/items/contacts/options.php:919,
#: framework/premium/features/premium-header/items/divider/options.php:41,
#: framework/premium/features/premium-header/items/search-input/options.php:285,
#: framework/premium/features/premium-header/items/search-input/options.php:415,
#: framework/premium/features/premium-header/items/search-input/options.php:545,
#: framework/premium/features/premium-header/items/search-input/options.php:681,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:241,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:372,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:71,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:204,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:373,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:510,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:200,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:327,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:452
msgid "Sticky State"
msgstr "Stan \"Przypięty\""

#: framework/features/header/items/account/options.php:1499
msgid "Item Margin"
msgstr "Margines elementu"

#: framework/features/header/items/account/options.php:1514
msgid "Modal Options"
msgstr "Opcje okna modalnego"

#: framework/features/header/items/account/options.php:1705,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:451
msgid "Modal Shadow"
msgstr "Cień okna modalnego"

#: framework/features/header/items/account/options.php:1677,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:422
msgid "Modal Background"
msgstr "Tło okna modalnego"

#: framework/features/header/items/account/options.php:1691,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:437
msgid "Modal Backdrop"
msgstr "Tło okna modalnego"

#: framework/features/blocks/contact-info/options.php:13,
#: framework/features/header/items/account/options.php:2059,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:441,
#: framework/premium/features/premium-header/items/contacts/options.php:486,
#: framework/premium/features/premium-header/items/contacts/options.php:541,
#: framework/premium/features/premium-header/items/language-switcher/options.php:120,
#: framework/premium/features/premium-header/items/language-switcher/options.php:251,
#: framework/premium/features/premium-header/items/search-input/options.php:239,
#: framework/premium/features/premium-header/items/search-input/options.php:936,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:644,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:578
msgid "Element Visibility"
msgstr "Widoczność elementu"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:655
msgid "You have no %s fields declared for this custom post type."
msgstr "Nie masz zadeklarowanych %s pól dla tego niestandardowego typu postu."

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:667,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1401,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1412
msgid "Field"
msgstr "Pole"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:679,
#: framework/premium/features/premium-header/items/language-switcher/options/common.php:24
msgid "Label"
msgstr "Etykieta"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:728
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:319
msgid "Fallback"
msgstr "Plan awaryjny"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:755
msgid "%s Field"
msgstr "%s Pole"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1399
msgid "%s %s Font"
msgstr "%s %s Czcionka"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1410
msgid "%s %s Color"
msgstr "%s %s Kolor"

#: framework/helpers/exts-configs.php:142,
#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:11,
#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:68
msgid "Read Time"
msgstr "Czas czytania"

#: framework/premium/extensions/post-types-extra/features/filtering/helpers.php:191,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:195,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:842
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:65
msgid "All"
msgstr "Wszystkie"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:92,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:803
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:75
msgid "Featured Image"
msgstr "Wyróżniony obraz"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:110
msgid "Accent Color"
msgstr "Kolor akcentu"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/helpers.php:150,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/helpers.php:209
msgid "Add to wishlist"
msgstr "Dodaj do listy życzeń"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:9
msgid "Select a page"
msgstr "Wybierz stronę"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:23
msgid "Show Wishlist Page To"
msgstr "Pokaż stronę listy życzeń dla"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:29
msgid "Logged Users"
msgstr "Zalogowani Użytkownicy"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:30
msgid "All Users"
msgstr "Wszyscy użytkownicy"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:40
msgid "Wishlist Page"
msgstr "Strona listy życzeń"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:46
msgid "The page you select here will display the wish list for your logged out users."
msgstr "Wybrana strona wyświetli listę życzeń dla wylogowanych użytkowników."

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:25
msgid "Archive Page"
msgstr "Archiwum stron"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:227,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:240,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:276,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:273,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:303,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:332,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:405,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:435,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:464,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:103,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:133,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:162
msgid "Hover/Active"
msgstr "Podświetlenie/Aktywny"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/views/table-no-results.php:6
msgid "You don't have any products in your wish list yet."
msgstr "Nie masz jeszcze żadnych produktów na swojej liście życzeń."

#: framework/premium/extensions/woocommerce-extra/features/archive-card/feature.php:56,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:54,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:82
msgid "Add to cart"
msgstr "Dodaj do koszyka"

#: framework/premium/extensions/woocommerce-extra/features/compare/views/bar.php:101,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:142,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:145,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/views/columns/product-remove-button.php:12
msgid "Remove Product"
msgstr "Usuń produkt"

#: framework/premium/extensions/woocommerce-extra/includes/woo-import-export.php:36
msgid "Blocksy Variation Images"
msgstr "Obrazy wariantów Blocksy"

#: framework/premium/features/content-blocks/options/404.php:33,
#: framework/premium/features/content-blocks/options/header.php:55,
#: framework/premium/features/content-blocks/options/hook.php:159,
#: framework/premium/features/content-blocks/options/maintenance.php:30,
#: framework/premium/features/content-blocks/options/nothing_found.php:51,
#: framework/premium/features/content-blocks/options/popup.php:41
msgid "Container Structure"
msgstr "Struktura kontenera"

#: framework/premium/features/content-blocks/options/404.php:65,
#: framework/premium/features/content-blocks/options/archive.php:132,
#: framework/premium/features/content-blocks/options/header.php:83,
#: framework/premium/features/content-blocks/options/hook.php:192,
#: framework/premium/features/content-blocks/options/maintenance.php:62,
#: framework/premium/features/content-blocks/options/nothing_found.php:83,
#: framework/premium/features/content-blocks/options/single.php:64
msgid "Narrow Width"
msgstr "Wąska szerokość"

#: framework/premium/features/content-blocks/options/404.php:70,
#: framework/premium/features/content-blocks/options/archive.php:137,
#: framework/premium/features/content-blocks/options/header.php:88,
#: framework/premium/features/content-blocks/options/hook.php:197,
#: framework/premium/features/content-blocks/options/maintenance.php:67,
#: framework/premium/features/content-blocks/options/nothing_found.php:88,
#: framework/premium/features/content-blocks/options/single.php:69
msgid "Normal Width"
msgstr "Normalna szerokość"

#: framework/premium/features/content-blocks/options/404.php:76,
#: framework/premium/features/content-blocks/options/archive.php:153,
#: framework/premium/features/content-blocks/options/header.php:94,
#: framework/premium/features/content-blocks/options/hook.php:203,
#: framework/premium/features/content-blocks/options/maintenance.php:73,
#: framework/premium/features/content-blocks/options/nothing_found.php:94,
#: framework/premium/features/content-blocks/options/single.php:85
msgid "Content Area Style"
msgstr "Styl obszaru treści"

#: framework/premium/features/content-blocks/options/404.php:90,
#: framework/premium/features/content-blocks/options/archive.php:167,
#: framework/premium/features/content-blocks/options/header.php:108,
#: framework/premium/features/content-blocks/options/hook.php:217,
#: framework/premium/features/content-blocks/options/maintenance.php:87,
#: framework/premium/features/content-blocks/options/nothing_found.php:108
msgid "Content Area Vertical Spacing"
msgstr "Odstępy pionowe w obszarze zawartości"

#: framework/premium/features/content-blocks/options/404.php:102,
#: framework/premium/features/content-blocks/options/archive.php:179,
#: framework/premium/features/content-blocks/options/header.php:120,
#: framework/premium/features/content-blocks/options/hook.php:229,
#: framework/premium/features/content-blocks/options/maintenance.php:99,
#: framework/premium/features/content-blocks/options/nothing_found.php:120,
#: framework/premium/features/content-blocks/options/single.php:111
msgid "Top & Bottom"
msgstr "Góra i dół"

#: framework/premium/features/content-blocks/options/404.php:105,
#: framework/premium/features/content-blocks/options/archive.php:182,
#: framework/premium/features/content-blocks/options/header.php:123,
#: framework/premium/features/content-blocks/options/hook.php:232,
#: framework/premium/features/content-blocks/options/maintenance.php:102,
#: framework/premium/features/content-blocks/options/nothing_found.php:123,
#: framework/premium/features/content-blocks/options/single.php:114
msgid "Only Top"
msgstr "Tylko góra"

#: framework/premium/features/content-blocks/options/404.php:108,
#: framework/premium/features/content-blocks/options/archive.php:185,
#: framework/premium/features/content-blocks/options/header.php:126,
#: framework/premium/features/content-blocks/options/hook.php:235,
#: framework/premium/features/content-blocks/options/maintenance.php:105,
#: framework/premium/features/content-blocks/options/nothing_found.php:126,
#: framework/premium/features/content-blocks/options/single.php:117
msgid "Only Bottom"
msgstr "Tylko dół"

#: framework/premium/features/content-blocks/options/hook.php:60
msgid "Location & Priority"
msgstr "Lokalizacja i priorytet"

#: framework/premium/features/content-blocks/options/hook.php:23,
#: framework/premium/features/content-blocks/options/hook.php:100
#: framework/premium/static/js/options/MultipleLocationsSelect.js:76
msgid "Custom Hook"
msgstr "Custom Hook"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:730
msgid "Mixed"
msgstr "Mieszany"

#: framework/premium/features/content-blocks/options/popup.php:438,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:628
msgid "Popup Position"
msgstr "Pozycja wyskakującego okienka"

#: framework/premium/features/content-blocks/options/popup.php:384,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:573
msgid "Popup Size"
msgstr "Rozmiar wyskakującego okienka"

#: framework/premium/features/content-blocks/options/popup.php:390,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:580
msgid "Small Size"
msgstr "Mały rozmiar"

#: framework/premium/features/content-blocks/options/popup.php:391,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:581
msgid "Medium Size"
msgstr "Średni rozmiar"

#: framework/premium/features/content-blocks/options/popup.php:392,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:582
msgid "Large Size"
msgstr "Duży rozmiar"

#: framework/premium/features/content-blocks/options/popup.php:393,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:583
msgid "Custom Size"
msgstr "Niestandardowy rozmiar"

#: framework/premium/features/content-blocks/options/popup.php:403,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:593
msgid "Max Width"
msgstr "Maksymalna szerokość"

#: framework/premium/features/content-blocks/options/popup.php:419,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:609
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:510
msgid "Max Height"
msgstr "Maksymalna wysokość"

#: framework/premium/features/content-blocks/options/popup.php:339,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:525
msgid "Popup Animation"
msgstr "Animacja wyskakujących okienek"

#: framework/premium/features/content-blocks/options/popup.php:345,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:531
msgid "Fade in fade out"
msgstr "Pojawianie / Znikanie"

#: framework/premium/features/content-blocks/options/popup.php:346,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:532
msgid "Zoom in zoom out"
msgstr "Przybli��enie / Oddalenie"

#: framework/premium/features/content-blocks/options/popup.php:347,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:533
msgid "Slide in from left"
msgstr "Wślizg od lewej"

#: framework/premium/features/content-blocks/options/popup.php:348,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:534
msgid "Slide in from right"
msgstr "Wślizg od prawej"

#: framework/premium/features/content-blocks/options/popup.php:349,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:535
msgid "Slide in from top"
msgstr "Wślizg z góry"

#: framework/premium/features/content-blocks/options/popup.php:350,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:536
msgid "Slide in from bottom"
msgstr "Wślizg z dołu"

#: framework/premium/features/content-blocks/options/popup.php:355,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:542
msgid "Animation Speed"
msgstr "Prędkość animacji"

#: framework/premium/features/content-blocks/options/popup.php:372,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:560
msgid "Entrance Value"
msgstr "Wartość wejściowa"

#: framework/premium/features/content-blocks/options/popup.php:69
msgid "Trigger Condition"
msgstr "Warunki wyzwalacza"

#: framework/premium/features/content-blocks/options/popup.php:93,
#: framework/premium/features/content-blocks/options/popup.php:111
msgid "Element Class"
msgstr "Klasa elementu"

#: framework/premium/features/content-blocks/options/popup.php:99,
#: framework/premium/features/content-blocks/options/popup.php:117
msgid "Separate each class by comma if you have multiple elements."
msgstr "W przypadku wielu elementów, oddziel każdą klasę przecinkiem."

#: framework/premium/features/content-blocks/options/popup.php:129
msgid "Scroll Direction"
msgstr "Kierunek przewijania"

#: framework/premium/features/content-blocks/options/popup.php:134
msgid "Scroll Down"
msgstr "Przewiń w dół"

#: framework/premium/features/content-blocks/options/popup.php:135
msgid "Scroll Up"
msgstr "Przewiń w górę"

#: framework/premium/features/content-blocks/options/popup.php:140
msgid "Scroll Distance"
msgstr "Odległość przewijania"

#: framework/premium/features/content-blocks/options/popup.php:149
msgid "Set the scroll distance till the popup block will appear."
msgstr "Ustaw odległość przewijania, aż pojawi się wyskakujący blok."

#: framework/premium/features/content-blocks/options/popup.php:167
msgid "Inactivity Time"
msgstr "Czas bezczynności"

#: framework/premium/features/content-blocks/options/popup.php:173
msgid "Set the inactivity time (in seconds) till the popup block will appear."
msgstr "Ustaw czas bezczynności (w sekundach) do pojawienia się wyskakującego bloku."

#: framework/premium/features/content-blocks/options/popup.php:185
msgid "After X Time"
msgstr "Po X czasie"

#: framework/premium/features/content-blocks/options/popup.php:191
msgid "Set after how much time (in seconds) the popup block will appear."
msgstr "Ustaw po jakim czasie (w sekundach) pojawi się wyskakujący blok."

#: framework/premium/features/content-blocks/options/popup.php:522,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:676
msgid "Padding"
msgstr "Dopełnienie"

#: framework/features/header/items/account/options.php:2034,
#: framework/premium/features/content-blocks/options/popup.php:533,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:298,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:688,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:455,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:330,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:194
msgid "Border Radius"
msgstr "Promień obramowania"

#: framework/premium/features/content-blocks/options/popup.php:563,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:666
msgid "Popup Offset"
msgstr "Przesunięcie wyskakującego okienka"

#: framework/premium/features/content-blocks/options/popup.php:581
msgid "Container Overflow"
msgstr "Przepełnienie kontenera"

#: framework/premium/features/content-blocks/options/popup.php:588
msgid "Hidden"
msgstr "Ukryty"

#: framework/premium/features/content-blocks/options/popup.php:589
msgid "Visible"
msgstr "Widoczny"

#: framework/premium/features/content-blocks/options/popup.php:590
msgid "Scroll"
msgstr "Przewijaj"

#: framework/premium/features/content-blocks/options/popup.php:592
msgid "Control what happens to the content that is too big to fit into the popup."
msgstr "Kontrola nad treścią, która jest zbyt duża, aby zmieściła się w wyskakującym okienku."

#: framework/premium/features/content-blocks/options/popup.php:596
msgid "Close Button"
msgstr "Przycisk zamykania"

#: framework/premium/features/content-blocks/options/popup.php:604
msgid "Inside"
msgstr "Wewnątrz"

#: framework/premium/features/content-blocks/options/popup.php:605
msgid "Outside"
msgstr "Poza"

#: framework/premium/features/content-blocks/options/popup.php:679,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:720,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:299,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:72
msgid "Popup Background"
msgstr "Tło wyskakującego okienka"

#: framework/premium/features/content-blocks/options/popup.php:693
msgid "Popup Backdrop Background"
msgstr "Przesłonięcie tła wyskakującego okienka"

#: framework/helpers/exts-configs.php:156,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:25
msgid "Posts Filter"
msgstr "Filtr postów"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:59
msgid "Filtering Behavior"
msgstr "Zachowanie podczas filtrowania"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:67
msgid "Instant Reload"
msgstr "Natychmiastowe przeładowanie"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:68
msgid "Page Reload"
msgstr "Przeładowanie strony"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:103
msgid "Items Horizontal Spacing"
msgstr "Odstępy poziome elementów"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:115,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:79
msgid "Items Vertical Spacing"
msgstr "Odstępy między blokami w pionie"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:127
msgid "Container Bottom Spacing"
msgstr "Dolny odstęp kontenera"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:139,
#: framework/premium/features/premium-header/items/contacts/options.php:510,
#: framework/premium/features/premium-header/items/language-switcher/options.php:90,
#: framework/premium/features/premium-header/items/search-input/options.php:207
msgid "Horizontal Alignment"
msgstr "Wyrównanie w poziomie"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:284
msgid "Button Padding"
msgstr "Wypełnienie przycisku"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:4
msgid "Read Progress"
msgstr "Postęp czytania"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:24
msgid "Indicator Height"
msgstr "Wysokość wskaźnika"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:34
msgid "Auto Hide"
msgstr "Automatyczne ukrywanie"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:38
msgid "Automatically hide the read progress bar once you arrive at the bottom of the article."
msgstr "Automatycznie ukrywaj pasek postępu czytania po dotarciu na dół artykułu."

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:71
msgid "Main Color"
msgstr "Główny kolor"

#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:83,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:84
msgid "Icon Badge"
msgstr "Ikonka odznaki"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:174,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:178,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:181
msgid "Label Font Color"
msgstr "Kolor tekstu etykiety"

#: framework/premium/features/premium-header/items/contacts/config.php:4
msgid "Contacts"
msgstr "Kontakty"

#: framework/features/blocks/about-me/options.php:178,
#: framework/features/blocks/contact-info/options.php:548,
#: framework/premium/features/premium-header/items/contacts/options.php:435,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:16
msgid "Items Spacing"
msgstr "Odstępy elementów"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:420,
#: framework/premium/features/premium-header/items/contacts/options.php:1051,
#: framework/premium/features/premium-header/items/content-block/options.php:40,
#: framework/premium/features/premium-header/items/divider/options.php:107,
#: framework/premium/features/premium-header/items/divider/options.php:125,
#: framework/premium/features/premium-header/items/search-input/options.php:804,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:478,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:172,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:625,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:557
msgid "Margin"
msgstr "Margines"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:106,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/view.php:45
msgid "Dark Mode"
msgstr "Tryb ciemny"

#: framework/features/header/items/account/options.php:68,
#: framework/features/header/items/account/options.php:1999,
#: framework/premium/features/premium-header/items/divider/config.php:4
msgid "Divider"
msgstr "Separator"

#: framework/premium/features/premium-header/items/divider/options.php:6
msgid "Size"
msgstr "Rozmiar"

#: framework/features/conditions/rules/localization.php:11,
#: framework/premium/features/premium-header/items/language-switcher/config.php:14
msgid "Languages"
msgstr "Języki"

#: framework/premium/features/premium-header/items/language-switcher/options.php:274
msgid "Top Level Options"
msgstr "Opcje najwyższego poziomu"

#: framework/features/header/items/account/options.php:618,
#: framework/premium/features/premium-header/items/language-switcher/options.php:170
msgid "Dropdown"
msgstr "Lista rozwijana"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:23
msgid "Flag"
msgstr "Flaga"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:39
msgid "Label Style"
msgstr "Styl etykiety"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:46
msgid "Long"
msgstr "Długi"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:47
msgid "Short"
msgstr "Krótki"

#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:26
msgid "Hide Current Language"
msgstr "Ukryj bieżący język"

#: framework/features/header/items/account/options.php:1890,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:59
msgid "Dropdown Options"
msgstr "Opcje listy rozwijanej"

#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:70
msgid "Dropdown Top Offset"
msgstr "Górne przesunięcie listy rozwijanej"

#: framework/premium/features/premium-header/items/search-input/config.php:4
msgid "Search Box"
msgstr "Pole wyszukiwania"

#: framework/features/blocks/search/options.php:46,
#: framework/features/blocks/search/options.php:52,
#: framework/features/blocks/search/options.php:64,
#: framework/features/blocks/search/options.php:145,
#: framework/premium/features/premium-header/items/search-input/options.php:45
msgid "Placeholder Text"
msgstr "Tekst zastępczy"

#: framework/premium/features/premium-header/items/search-input/options.php:55
msgid "Input Maximum Width"
msgstr "Wpisz maksymalną szerokość"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:87,
#: framework/features/blocks/search/options.php:70,
#: framework/premium/features/premium-header/items/search-input/options.php:67
msgid "Input Height"
msgstr "Wysokość wejściowa"

#: framework/features/blocks/search/options.php:97,
#: framework/premium/features/premium-header/items/search-input/options.php:88
msgid "Live Results"
msgstr "Wyniki na żywo"

#: framework/features/blocks/search/options.php:109,
#: framework/premium/features/premium-header/items/search-input/options.php:100
msgid "Live Results Images"
msgstr "Obrazki w wynikach na żywo"

#: framework/features/blocks/search/options.php:185,
#: framework/premium/features/premium-header/items/search-input/options.php:175
msgid "Search Through Criteria"
msgstr "Wyszukiwanie według kryteriów"

#: framework/features/blocks/search/options.php:187,
#: framework/premium/features/premium-header/items/search-input/options.php:176
msgid "Chose in which post types do you want to perform searches."
msgstr "Wybierz, w których typach wpisów chcesz wyszukiwać."

#: framework/premium/features/premium-header/items/search-input/options.php:396,
#: framework/premium/features/premium-header/items/search-input/options.php:425,
#: framework/premium/features/premium-header/items/search-input/options.php:457,
#: framework/premium/features/premium-header/items/search-input/options.php:487
msgid "Input Icon Color"
msgstr "Kolor ikony pola tekstowego"

#: framework/premium/features/premium-header/items/search-input/options.php:833
#: static/js/editor/blocks/search/Edit.js:346
msgid "Dropdown Text Color"
msgstr "Kolor rozwijanego tekstu"

#: framework/premium/features/premium-header/items/search-input/options.php:864
msgid "Dropdown Background"
msgstr "Tło listy rozwijanej"

#: framework/premium/features/premium-header/items/widget-area-1/config.php:4
msgid "Widget Area"
msgstr "Obszar widżetów"

#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker.js:14
#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker/Implementation.js:72
#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:362
#: static/js/dashboard/NoTheme.js:64 static/js/dashboard/VersionMismatch.js:61
#: static/js/dashboard/screens/SiteExport.js:310
#: static/js/notifications/VersionMismatchNotice.js:73
msgid "Loading..."
msgstr "Wczytywanie…"

#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker/Implementation.js:73
msgid "Invalid API Key..."
msgstr "Nieprawidłowy klucz API..."

#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker/Implementation.js:101
#: framework/extensions/newsletter-subscribe/dashboard-static/js/ListPicker.js:119
msgid "Select list..."
msgstr "Wybierz listę..."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:218
msgid "More information on how to generate an API key for Mailchimp can be found %shere%s."
msgstr "Więcej informacji na temat generowania klucza API dla Mailchimp można znaleźć %stutaj%s."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:286
msgid "More information on how to generate an API key for ConvertKit can be found %shere%s."
msgstr "Więcej informacji na temat generowania klucza API dla Mailerlite można znaleźć %stutaj%s."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:184
msgid "API Key"
msgstr "Klucz API"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:363
#: framework/extensions/product-reviews/static/js/ProductReviews.js:94
#: framework/premium/extensions/mega-menu/static/js/SettingsManager.js:280
#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:111
#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:910
#: framework/premium/static/js/footer/EditConditions.js:143
#: framework/premium/static/js/media-video/components/EditVideoMeta.js:106
msgid "Save Settings"
msgstr "Zapisz ustawienia"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:153
msgid "Pick Mailing Service"
msgstr "Wybierz usługę pocztową"

#: framework/extensions/product-reviews/static/js/ProductReviews.js:22
msgid "Product Reviews Settings"
msgstr "Ustawienia opinii produktów"

#: framework/extensions/product-reviews/static/js/ProductReviews.js:25
msgid "Configure the slugs for single and category pages of the product review custom post type."
msgstr "Skonfiguruj uproszczone nazwy pojedynczych treści i stron kategorii własnego typu wpisu dla opinii o produktach."

#: framework/extensions/product-reviews/static/js/ProductReviews.js:43
msgid "Single Slug"
msgstr "Uproszczona nazwa pojedynczej treści"

#: framework/extensions/product-reviews/static/js/ProductReviews.js:49
msgid "Category Slug"
msgstr "Uproszczona nazwa kategorii"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:84
msgid "Adobe Fonts Settings"
msgstr "Ustawienia Adobe Fonts"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:116
msgid "Project ID"
msgstr "Identyfikator projektu"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:158
msgid "Fetch Fonts"
msgstr "Pobierz czcionki"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:182
#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:54
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:39
msgid "Variations"
msgstr "Warianty"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/CustomFonts.js:74
msgid "Custom Fonts Settings"
msgstr "Niestandardowe ustawienia czcionek"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/CustomFonts.js:76
msgid "Here you can see all your custom fonts that can be used in all typography options across the theme."
msgstr "Tutaj możesz zobaczyć wszystkie niestandardowe czcionki, które mogą być używane we wszystkich opcjach typografii w motywie."

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:28
msgid "Dynamic Font"
msgstr "Czcionka dynamiczna"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:37
msgid "Variable font"
msgstr "Czcionka zmienna"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:78
#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:322
msgid "Edit Font"
msgstr "Edytuj czcionkę"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:100
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:78
msgid "Remove Font"
msgstr "Usuń czcionkę"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:216
msgid "Change"
msgstr "Zmień"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:217
msgid "Choose"
msgstr "Wybierz"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:127
#: static/js/options/ConditionsManager/SingleCondition.js:95
msgid "Select variation"
msgstr "Wybierz wariant"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:147
msgid "Regular"
msgstr "Regular"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:154
#: framework/premium/static/js/typography/providers/kadence.js:71
msgid "Italic"
msgstr "Italic"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/RegularTab.js:30
msgid "Upload only the %s.woff2%s or %s.ttf%s font file formats (see browser coverage %shere%s). Use %sthis converter tool%s if you don't have these font formats."
msgstr "Prześlij tylko pliki czcionek w formacie %s.woff2%s lub %s.ttf%s (zobacz zasięg przeglądarki %shere%s). Użyj %sthis converter tool%s, jeśli nie masz tych formatów czcionek."

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:90
msgid "Font Name"
msgstr "Nazwa czcionki"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:459
msgid "Save Custom Font"
msgstr "Zapisz niestandardową czcionkę"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:79
msgid "Add Variation"
msgstr "Dodaj wariant"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:258
msgid "Download a font and serve it directly from your server, this is handy for those who want to comply with GDPR regulations or serve the font via CDN."
msgstr "Pobierz czcionkę i udostępnij ją bezpośrednio ze swojego serwera, jest to przydatne dla tych, którzy chcą przestrzegać przepisów RODO lub udostępniać czcionkę przez CDN."

#: framework/premium/extensions/mega-menu/static/js/SettingsManager.js:189
msgid "Item Settings"
msgstr "Ustawienia elementu"

#: framework/premium/extensions/sidebars/static/js/BlockWidgetControls.js:60
msgid "Remove Sidebar"
msgstr "Usuń pasek boczny"

#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:65
msgid "%s - Sidebar Display Conditions"
msgstr "%s - Warunki wyświetlania paska bocznego"

#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:73
msgid "Add one or more conditions in order to display your sidebar."
msgstr "Dodaj jeden lub więcej warunków, aby wyświetlić pasek boczny."

#: framework/premium/extensions/sidebars/static/js/main.js:53
msgid "Enter a name in the input below and hit the Create Sidebar button."
msgstr "Wprowadź nazwę w polu poniżej i naciśnij przycisk \"Utwórz pasek boczny\"."

#: framework/premium/extensions/sidebars/static/js/main.js:60
msgid "Sidebar name"
msgstr "Nazwa paska bocznego"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:108
msgid "Advanced"
msgstr "Zaawansowane"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:123
msgid "Agency Details"
msgstr "Szczegóły agencji"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:131
msgid "Agency Name"
msgstr "Nazwa agencji"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:159
msgid "Agency URL"
msgstr "URL agencji"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:187
msgid "Agency Support/Contact Form URL"
msgstr "URL do wsparcia / formularza kontaktowego agencji"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:220
msgid "Theme Details"
msgstr "Szczegóły motywu"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:228
msgid "Theme Name"
msgstr "Nazwa motywu"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:256
msgid "Theme Description"
msgstr "Opis motywu"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:286
msgid "Theme Screenshot URL"
msgstr "URL do zrzutu ekranu motywu"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:384
msgid "You can insert the link to a self hosted image or upload one. The recommended image size is 1200px wide by 900px tall."
msgstr "Możesz wstawić link do obrazu hostowanego samodzielnie lub przesłać go. Zalecany rozmiar obrazu to 1200x900 pikseli."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:393
msgid "Theme Icon URL"
msgstr "URL do ikony motywu"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:489
msgid "You can insert the link to a self hosted image or upload one. The recommended image size is 18px wide by 18px tall."
msgstr "Możesz wstawić link do obrazu hostowanego samodzielnie lub przesłać go. Zalecany rozmiar obrazu to 18x18 pikseli."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:498
msgid "Gutenberg Options Panel Icon URL"
msgstr "Adres URL ikony panelu opcji Gutenberga"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:596
msgid "You can insert the link to a self hosted image or upload one. Please note that only icons in SVG format are allowed here to not break the editor interactiveness."
msgstr "Możesz wstawić link do obrazu hostowanego samodzielnie lub przesłać go. Pamiętaj, że dozwolone są tutaj tylko ikony w formacie SVG, aby nie zakłócać interaktywności edytora."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:616
msgid "Plugin Name"
msgstr "Nazwa wtyczki"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:644
msgid "Plugin Description"
msgstr "Opis wtyczki"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:674
msgid "Plugin Thumbnail URL"
msgstr "URL miniatury wtyczki"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:764
msgid "Choose File"
msgstr "Wybierz plik"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:772
msgid "You can insert the link to a self hosted image or upload one. The recommended image size is 256px wide by 256px tall."
msgstr "Możesz wstawić link do obrazu hostowanego samodzielnie lub przesłać go. Zalecany rozmiar obrazu to 256x256 pikseli."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:795
msgid "Hide Account Menu Item"
msgstr "Ukryj elementy menu konta"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:803
msgid "Hide Starter Sites Tab"
msgstr "Ukryj zakładkę Strony startowe"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:810
msgid "Hide Useful Plugins Tab"
msgstr "Ukryj zakładkę Przydatne wtyczki"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:818
msgid "Hide Changelog Tab"
msgstr "Ukryj zakładkę Dziennik zmian"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:826
msgid "Hide Support Section"
msgstr "Ukryj sekcję Wsparcie"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:876
msgid "Hide White Label Extension"
msgstr "Ukryj zakładkę White Label"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:891
msgid "Please note that the white label extension will be hidden if this option is enabled. In order to bring it back you have to hit the SHIFT key and click on the dashboard logo."
msgstr "Pamiętaj, że rozszerzenie z białą etykietą zostanie ukryte, jeśli ta opcja jest włączona. Aby go przywrócić, musisz nacisnąć klawisz SHIFT i kliknąć logo pulpitu."

#: framework/premium/static/js/footer/CloneItem.js:66
#: framework/premium/static/js/header/CloneItem.js:66
msgid "Clone Item"
msgstr "Duplikuj element"

#: framework/premium/static/js/footer/CloneItem.js:90
#: framework/premium/static/js/header/CloneItem.js:90
msgid "Remove Item"
msgstr "Usuń element"

#: framework/premium/static/js/footer/CreateFooter.js:107
#: framework/premium/static/js/header/CreateHeader.js:106
msgid "Copy elements & styles from"
msgstr "Kopiuj elementy i styl od"

#: framework/premium/static/js/footer/CreateFooter.js:118
msgid "Picker Footer"
msgstr "Stopka selektora"

#: framework/premium/static/js/footer/CreateFooter.js:133
#: framework/premium/static/js/footer/PanelsManager.js:52
msgid "Global Footer"
msgstr "Globalna stopka"

#: framework/premium/static/js/footer/CreateFooter.js:137
#: framework/premium/static/js/header/CreateHeader.js:138
msgid "Secondary"
msgstr "Drugorzędny"

#: framework/premium/static/js/footer/CreateFooter.js:141
#: framework/premium/static/js/header/CreateHeader.js:142
msgid "Centered"
msgstr "Wyśrodkowany"

#: framework/premium/static/js/footer/CreateFooter.js:172
msgid "Create New Footer"
msgstr "Utwórz nową stopkę"

#: framework/premium/static/js/footer/CreateFooter.js:50
msgid "Create new footer"
msgstr "Utwórz nową stopkę"

#: framework/premium/static/js/footer/CreateFooter.js:53
msgid "Create a new footer and assign it to different pages or posts based on your conditions."
msgstr "Utwórz nową stopkę i przypisz ją do różnych stron lub postów w zależności od warunków."

#: framework/premium/static/js/footer/CreateFooter.js:72
msgid "Footer name"
msgstr "Nazwa stopki"

#: framework/premium/static/js/footer/EditConditions.js:100
msgid "Add one or more conditions in order to display your footer."
msgstr "Dodaj jeden lub więcej warunków, aby wyświetlić stopkę."

#: framework/premium/static/js/footer/EditConditions.js:84
#: static/js/header/EditConditions.js:88
msgid "Add/Edit Conditions"
msgstr "Dodaj/edytuj warunki"

#: framework/premium/static/js/footer/PanelsManager.js:169
msgid "Remove footer"
msgstr "Usuń stopkę"

#: framework/premium/static/js/footer/PanelsManager.js:193
msgid "Remove Footer"
msgstr "Usuń stopkę"

#: framework/premium/static/js/footer/PanelsManager.js:196
msgid "You are about to remove a custom footer, are you sure you want to continue?"
msgstr "Zamierzasz usunąć niestandardową stopkę, czy na pewno chcesz kontynuować?"

#: framework/premium/static/js/footer/PanelsManager.js:212
#: framework/premium/static/js/hooks/CodeEditor.js:189
#: static/js/header/PanelsManager.js:217
#: static/js/options/CustomizerOptionsManager.js:463
msgid "Cancel"
msgstr "Anuluj"

#: framework/premium/static/js/footer/PanelsManager.js:228
#: static/js/header/PanelsManager.js:233
msgid "Confirm"
msgstr "Potwierdź"

#: framework/premium/static/js/footer/PanelsManager.js:68
#: static/js/header/PanelsManager.js:74
#: static/js/options/DisplayCondition.js:61
msgid "Edit Conditions"
msgstr "Edytuj warunki"

#. translators: %s: PHP version
#: blocksy-companion.php:182
msgid "Blocksy requires PHP version %s+, plugin is currently NOT RUNNING."
msgstr "Blocksy wymaga wersji PHP %s +, wtyczka obecnie NIE DZIAŁA."

#. translators: %s: WordPress version
#: blocksy-companion.php:193
msgid "Blocksy requires WordPress version %s+. Because you are using an earlier version, the plugin is currently NOT RUNNING."
msgstr "Blocksy wymaga wersji WordPress %s +. Ponieważ używasz wcześniejszej wersji, wtyczka obecnie NIE DZIAŁA."

#: framework/premium/extensions/adobe-typekit/extension.php:46
msgid "Adobe Typekit"
msgstr "Adobe Typekit"

#: framework/premium/extensions/custom-fonts/extension.php:154
msgid "Custom Fonts"
msgstr "Własne kroje pisma"

#: framework/premium/extensions/local-google-fonts/extension.php:122
msgid "Local Google Fonts"
msgstr "Lokalne czcionki Google"

#: framework/theme-integration.php:220,
#: framework/features/blocks/share-box/options.php:19
msgid "Facebook"
msgstr "Facebook"

#: framework/theme-integration.php:222,
#: framework/features/blocks/share-box/options.php:37
msgid "LinkedIn"
msgstr "LinkedIn"

#: framework/theme-integration.php:223
msgid "Dribbble"
msgstr "Dribbble"

#: framework/theme-integration.php:224
msgid "Instagram"
msgstr "Instagram"

#: framework/theme-integration.php:225,
#: framework/features/blocks/share-box/options.php:31
msgid "Pinterest"
msgstr "Pinterest"

#: framework/theme-integration.php:226
msgid "WordPress"
msgstr "WordPress"

#: framework/theme-integration.php:227
msgid "GitHub"
msgstr "GitHub"

#: framework/theme-integration.php:228,
#: framework/features/blocks/about-me/options.php:86,
#: framework/premium/features/content-blocks/options/archive.php:74
msgid "Medium"
msgstr "Średni"

#: framework/theme-integration.php:229,
#: framework/premium/features/media-video/options.php:14
msgid "YouTube"
msgstr "YouTube"

#: framework/theme-integration.php:230,
#: framework/premium/features/media-video/options.php:15
msgid "Vimeo"
msgstr "Vimeo"

#: framework/theme-integration.php:231,
#: framework/features/blocks/share-box/options.php:55
msgid "VKontakte"
msgstr "VKontakte"

#: framework/theme-integration.php:232,
#: framework/features/blocks/share-box/options.php:61
msgid "Odnoklassniki"
msgstr "Odnoklassniki"

#: framework/theme-integration.php:233
msgid "TikTok"
msgstr "TikTok"

#: framework/theme-integration.php:278
msgid "Companion"
msgstr "Towarzysz"

#: framework/theme-integration.php:296
msgid "PRO"
msgstr "PRO"

#: framework/features/account-auth.php:119,
#: framework/features/account-auth.php:267
msgid "Check your email"
msgstr "Sprawdź skrzynkę pocztową"

#: framework/features/account-auth.php:273
msgid "Registration Form"
msgstr "Formularz rejestracyjny"

#: framework/features/account-auth.php:274
msgid "Register For This Site"
msgstr "Zarejestruj się w witrynie"

#: framework/features/conditions/rules/cpt.php:17
msgid "%s Single"
msgstr "Pojedyncza %s"

#: framework/features/conditions/rules/cpt.php:27
msgid "%s Archive"
msgstr "Archiwum %s"

#: framework/features/conditions/rules/cpt.php:40
msgid "%s %s Taxonomy"
msgstr "%s %s taksonomia"

#: framework/features/conditions/rules/basic.php:3
msgid "Entire Website"
msgstr "Cała witryna"

#: framework/features/conditions/rules/basic.php:57
msgid "Basic"
msgstr "Podstawowe"

#: framework/extensions/trending/customizer.php:4,
#: framework/features/blocks/query/block.php:19,
#: framework/features/blocks/search/options.php:6,
#: framework/features/conditions/rules/posts.php:33,
#: framework/premium/features/premium-header/items/search-input/options.php:4
msgid "Posts"
msgstr "Wpisy"

#: framework/features/conditions/rules/posts.php:27,
#: framework/premium/features/content-blocks/hooks-manager.php:440,
#: framework/premium/features/content-blocks/hooks-manager.php:448,
#: framework/premium/features/content-blocks/hooks-manager.php:456,
#: framework/premium/features/content-blocks/hooks-manager.php:463,
#: framework/premium/features/content-blocks/hooks-manager.php:470,
#: framework/premium/features/content-blocks/hooks-manager.php:478
msgid "Single Post"
msgstr "Pojedynczy wpis"

#: framework/features/conditions/rules/posts.php:15
msgid "Post Categories"
msgstr "Kategorie wpisu"

#: framework/features/conditions/rules/posts.php:20
msgid "Post Tags"
msgstr "Tagi wpisu"

#: framework/features/blocks/search/options.php:7,
#: framework/features/conditions/rules/pages.php:52,
#: framework/premium/features/premium-header/items/search-input/options.php:5
msgid "Pages"
msgstr "Strony"

#: framework/features/conditions/rules/pages.php:8
msgid "Single Page"
msgstr "Pojedyncza strona"

#: framework/features/conditions/rules/specific.php:48
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:69
msgid "Specific"
msgstr "Szczegółowy"

#: framework/features/conditions/rules/specific.php:10
msgid "Post ID"
msgstr "Identyfikator wpisu"

#: framework/features/conditions/rules/specific.php:15
msgid "Page ID"
msgstr "Identyfikator strony"

#: framework/features/conditions/rules/specific.php:20
#: static/js/options/ConditionsManager/PostIdPicker.js:79
msgid "Custom Post Type ID"
msgstr "Identyfikator własnego typy treści"

#: framework/features/conditions/rules/specific.php:38,
#: framework/features/conditions/rules/woo.php:65
msgid "Taxonomy ID"
msgstr "Identyfikator taksonomii"

#: framework/features/conditions/rules/specific.php:25
msgid "Post with Taxonomy ID"
msgstr "W[is z identyfikatorem taksonomii"

#: framework/features/conditions/rules/pages.php:15
msgid "404"
msgstr "404"

#: framework/features/blocks/search/options.php:60,
#: framework/features/blocks/search/options.php:66,
#: framework/features/blocks/search/view.php:80,
#: framework/features/blocks/search/view.php:261,
#: framework/features/conditions/rules/pages.php:22,
#: framework/premium/features/premium-header/items/search-input/options.php:48,
#: framework/premium/features/premium-header/items/search-input/view.php:126
#: static/js/editor/blocks/query/edit/PatternSelectionModal.js:92
#: static/js/editor/blocks/search/Preview.js:23
#: static/js/editor/blocks/tax-query/edit/PatternSelectionModal.js:78
msgid "Search"
msgstr "Szukaj"

#: framework/features/conditions/rules/pages.php:27
msgid "Blog"
msgstr "Blog"

#: framework/features/conditions/rules/pages.php:33
msgid "Front Page"
msgstr "Strona główna"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:70
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:157
msgid "Author"
msgstr "Autor"

#: framework/features/conditions/rules/user-auth.php:20
msgid "User Auth"
msgstr "Autoryzacja użytkownika"

#: framework/features/conditions/rules/user-auth.php:24
msgid "User Logged In"
msgstr "Użytkownik zalogował się"

#: framework/features/conditions/rules/user-auth.php:29
msgid "User Logged Out"
msgstr "Użytkownik wylogował się"

#: framework/features/conditions/rules/user-auth.php:34
msgid "User Role"
msgstr "Rola użytkownika"

#: framework/premium/features/content-blocks/options/404.php:19,
#: framework/premium/features/content-blocks/options/header.php:19,
#: framework/premium/features/content-blocks/options/hook.php:24,
#: framework/premium/features/content-blocks/options/nothing_found.php:19,
#: framework/premium/features/content-blocks/options/popup.php:25
msgid "Other"
msgstr "Inne"

#: framework/features/conditions-manager.php:307
msgid "Language"
msgstr "Język"

#: framework/features/demo-install.php:98
msgid "Your PHP installation doesn't have support for XML. Please install the <i>xml</i> or <i>simplexml</i> PHP extension in order to be able to install starter sites. You might need to contact your hosting provider to assist you in doing so."
msgstr "Twoja instalacja PHP nie obsługuje XML. Zainstaluj rozszerzenie PHP <i>xml</i> lub <i>simplexml</i>, aby móc instalować witryny startowe. Może być konieczne skontaktowanie się z dostawcą usług hostingowych, aby uzyskać pomoc."

#: framework/features/dynamic-css.php:48
msgid "Dynamic CSS Output"
msgstr "Dynamiczne dane wyjściowe CSS"

#: framework/features/dynamic-css.php:52
msgid "The strategy of outputting the dynamic CSS. File - all the CSS code will be placed in a static file, otherwise it will be placed inline in head."
msgstr "Strategia tworzenia dynamicznego CSS. Plik - cały kod CSS zostanie umieszczony w pliku statycznym, w przeciwnym razie zostanie umieszczony w nagłówku."

#: framework/features/dynamic-css.php:54
msgid "File"
msgstr "Plik"

#: framework/features/dynamic-css.php:55,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:67,
#: framework/premium/features/premium-header/items/language-switcher/options.php:165
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:159
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:204
msgid "Inline"
msgstr "W treści"

#: framework/features/google-analytics.php:69
msgid "Google Analytics v4"
msgstr "Google Analytics v4"

#: framework/features/google-analytics.php:74
msgid "Link your Google Analytics 4 tracking ID. More info and instructions can be found %shere%s."
msgstr "Połącz swój identyfikator śledzenia Google Analytics 4. Więcej informacji i instrukcji można znaleźć %stutaj%s."

#: framework/features/opengraph-meta-data.php:13
msgid "OpenGraph Meta Data"
msgstr "Dane meta OpenGraph"

#: framework/features/opengraph-meta-data.php:17
msgid "Enable the OpenGraph rich meta data features for your website."
msgstr "Włącz funkcje zaawansowanych metadanych OpenGraph dla swojej witryny."

#: framework/features/opengraph-meta-data.php:26
msgid "Facebook Page URL"
msgstr "Adres URL strony na Facebooku"

#: framework/features/opengraph-meta-data.php:33
msgid "Facebook App ID"
msgstr "Identyfikator aplikacji Facebooka"

#: framework/features/opengraph-meta-data.php:40
msgid "Twitter Username"
msgstr "Nazwa użytkownika na Twitterze"

#: framework/extensions/cookies-consent/config.php:6
msgid "Display a cookie acceptance box in order to comply with the privacy regulations in your country."
msgstr "Włącz rozszerzenie, aby zachować zgodność z przepisami RODO."

#: framework/extensions/cookies-consent/customizer.php:15,
#: framework/extensions/newsletter-subscribe/customizer.php:12,
#: framework/extensions/product-reviews/extension.php:382,
#: framework/extensions/product-reviews/metabox.php:6,
#: framework/extensions/trending/customizer.php:173,
#: framework/features/header/header-options.php:6,
#: framework/premium/extensions/mega-menu/options.php:6,
#: framework/premium/extensions/shortcuts/customizer.php:761,
#: framework/features/header/items/account/options.php:330,
#: framework/premium/features/content-blocks/options/404.php:55,
#: framework/premium/features/content-blocks/options/archive.php:122,
#: framework/premium/features/content-blocks/options/header.php:73,
#: framework/premium/features/content-blocks/options/hook.php:182,
#: framework/premium/features/content-blocks/options/maintenance.php:52,
#: framework/premium/features/content-blocks/options/nothing_found.php:73,
#: framework/premium/features/content-blocks/options/popup.php:30,
#: framework/premium/features/content-blocks/options/single.php:54,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:6,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:35,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:279,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:520,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:41,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:120,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:378,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:20,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:66,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:11,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:10,
#: framework/premium/features/premium-header/items/contacts/options.php:45,
#: framework/premium/features/premium-header/items/content-block/options.php:5,
#: framework/premium/features/premium-header/items/language-switcher/options.php:278,
#: framework/premium/features/premium-header/items/search-input/options.php:40,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:19,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:163,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:63,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:5,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:5
#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:352
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:111
#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:104
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:43
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:38
msgid "General"
msgstr "Ogólne"

#: framework/extensions/cookies-consent/customizer.php:28,
#: framework/premium/extensions/shortcuts/customizer.php:773,
#: framework/features/header/items/account/options.php:447,
#: framework/features/header/items/account/options.php:821,
#: framework/features/header/items/account/options.php:909,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:23,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:48,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:375,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:31,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:56,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:101,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:55,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:56
msgid "Type 1"
msgstr "Typ 1"

#: framework/extensions/cookies-consent/customizer.php:33,
#: framework/premium/extensions/shortcuts/customizer.php:778,
#: framework/features/header/items/account/options.php:457,
#: framework/features/header/items/account/options.php:831,
#: framework/features/header/items/account/options.php:919,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:28,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:53,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:376,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:36,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:61,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:106,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:60,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:61
msgid "Type 2"
msgstr "Typ 2"

#: framework/extensions/cookies-consent/customizer.php:40
msgid "Cookie period"
msgstr "Okres ważności plików cookie"

#: framework/extensions/cookies-consent/customizer.php:48
msgid "One hour"
msgstr "Godzina"

#: framework/extensions/cookies-consent/customizer.php:49
msgid "One day"
msgstr "Dzień"

#: framework/extensions/cookies-consent/customizer.php:50
msgid "One week"
msgstr "Tydzień"

#: framework/extensions/cookies-consent/customizer.php:51
msgid "One month"
msgstr "Miesiąc"

#: framework/extensions/cookies-consent/customizer.php:52
msgid "Three months"
msgstr "Trzy miesiące"

#: framework/extensions/cookies-consent/customizer.php:53
msgid "Six months"
msgstr "Sześć miesięcy"

#: framework/extensions/cookies-consent/customizer.php:54
msgid "One year"
msgstr "Rok"

#: framework/extensions/cookies-consent/customizer.php:55
msgid "Forever"
msgstr "Na zawsze"

#: framework/extensions/cookies-consent/customizer.php:62,
#: framework/features/blocks/contact-info/options.php:87,
#: framework/features/blocks/contact-info/options.php:152,
#: framework/features/blocks/contact-info/options.php:215,
#: framework/features/blocks/contact-info/options.php:278,
#: framework/features/blocks/contact-info/options.php:341,
#: framework/features/blocks/contact-info/options.php:404,
#: framework/features/blocks/contact-info/options.php:467,
#: framework/premium/features/content-blocks/hooks-manager.php:267,
#: framework/premium/features/content-blocks/hooks-manager.php:275,
#: framework/premium/features/content-blocks/hooks-manager.php:283,
#: framework/premium/features/content-blocks/hooks-manager.php:291,
#: framework/premium/features/premium-header/items/contacts/options.php:97,
#: framework/premium/features/premium-header/items/contacts/options.php:142,
#: framework/premium/features/premium-header/items/contacts/options.php:187,
#: framework/premium/features/premium-header/items/contacts/options.php:232,
#: framework/premium/features/premium-header/items/contacts/options.php:278,
#: framework/premium/features/premium-header/items/contacts/options.php:323,
#: framework/premium/features/premium-header/items/contacts/options.php:368
msgid "Content"
msgstr "Treść"

#: framework/extensions/cookies-consent/customizer.php:64,
#: framework/extensions/cookies-consent/helpers.php:7
msgid "We use cookies to ensure that we give you the best experience on our website."
msgstr "Używamy plików cookie, aby zapewnić najlepszą jakość korzystania z naszej strony."

#: framework/features/blocks/search/options.php:58
msgid "Button Text"
msgstr "Tekst przycisku"

#: framework/extensions/cookies-consent/customizer.php:80,
#: framework/extensions/cookies-consent/helpers.php:10
msgid "Accept"
msgstr "Akceptuję"

#: framework/extensions/cookies-consent/customizer.php:142,
#: framework/extensions/newsletter-subscribe/customizer.php:170,
#: framework/extensions/product-reviews/extension.php:422,
#: framework/extensions/trending/customizer.php:582,
#: framework/features/header/header-options.php:203,
#: framework/premium/extensions/mega-menu/options.php:567,
#: framework/premium/extensions/shortcuts/customizer.php:1032,
#: framework/features/header/items/account/options.php:1107,
#: framework/premium/features/content-blocks/options/404.php:119,
#: framework/premium/features/content-blocks/options/archive.php:196,
#: framework/premium/features/content-blocks/options/header.php:137,
#: framework/premium/features/content-blocks/options/hook.php:246,
#: framework/premium/features/content-blocks/options/maintenance.php:116,
#: framework/premium/features/content-blocks/options/nothing_found.php:137,
#: framework/premium/features/content-blocks/options/popup.php:517,
#: framework/premium/features/content-blocks/options/single.php:128,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:147,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:178,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:169,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:406,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:661,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:289,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:458,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:108,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:253,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:570,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:124,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:132,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:190,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:66,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:245,
#: framework/premium/features/premium-header/items/contacts/options.php:571,
#: framework/premium/features/premium-header/items/content-block/options.php:36,
#: framework/premium/features/premium-header/items/language-switcher/options.php:284,
#: framework/premium/features/premium-header/items/search-input/options.php:260,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:66,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:100,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:152,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:154
msgid "Design"
msgstr "Wygląd"

#: framework/extensions/cookies-consent/customizer.php:203,
#: framework/extensions/cookies-consent/customizer.php:270,
#: framework/premium/extensions/mega-menu/options.php:879,
#: framework/premium/extensions/shortcuts/customizer.php:1058,
#: framework/features/header/items/account/options.php:1518,
#: framework/features/header/items/account/options.php:1902,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:202,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:233,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:262,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:195,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:113,
#: framework/premium/features/premium-header/items/contacts/options.php:585,
#: framework/premium/features/premium-header/items/contacts/options.php:614,
#: framework/premium/features/premium-header/items/contacts/options.php:655,
#: framework/premium/features/premium-header/items/contacts/options.php:694,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:111,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:222,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:251,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:281,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:310,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:52,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:81,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:111,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:140,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:217,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:261,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:303,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:209,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:240,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:269
msgid "Font Color"
msgstr "Kolor tekstu"

#: framework/extensions/cookies-consent/customizer.php:164,
#: framework/extensions/cookies-consent/customizer.php:191,
#: framework/extensions/cookies-consent/customizer.php:220,
#: framework/extensions/cookies-consent/customizer.php:251,
#: framework/extensions/cookies-consent/customizer.php:287,
#: framework/extensions/cookies-consent/customizer.php:316,
#: framework/extensions/newsletter-subscribe/customizer.php:188,
#: framework/extensions/newsletter-subscribe/customizer.php:213,
#: framework/extensions/newsletter-subscribe/customizer.php:245,
#: framework/extensions/newsletter-subscribe/customizer.php:276,
#: framework/extensions/newsletter-subscribe/customizer.php:313,
#: framework/extensions/newsletter-subscribe/customizer.php:345,
#: framework/extensions/trending/customizer.php:610,
#: framework/extensions/trending/customizer.php:673,
#: framework/extensions/trending/customizer.php:726,
#: framework/extensions/trending/customizer.php:766,
#: framework/extensions/trending/customizer.php:798,
#: framework/extensions/trending/customizer.php:848,
#: framework/extensions/trending/customizer.php:889,
#: framework/premium/extensions/mega-menu/options.php:845,
#: framework/premium/extensions/mega-menu/options.php:892,
#: framework/premium/extensions/mega-menu/options.php:911,
#: framework/premium/extensions/shortcuts/customizer.php:1076,
#: framework/premium/extensions/shortcuts/customizer.php:1110,
#: framework/premium/extensions/shortcuts/customizer.php:1142,
#: framework/features/header/items/account/options.php:1198,
#: framework/features/header/items/account/options.php:1242,
#: framework/features/header/items/account/options.php:1284,
#: framework/features/header/items/account/options.php:1389,
#: framework/features/header/items/account/options.php:1432,
#: framework/features/header/items/account/options.php:1473,
#: framework/features/header/items/account/options.php:1539,
#: framework/features/header/items/account/options.php:1572,
#: framework/features/header/items/account/options.php:1610,
#: framework/features/header/items/account/options.php:1653,
#: framework/features/header/items/account/options.php:1758,
#: framework/features/header/items/account/options.php:1801,
#: framework/features/header/items/account/options.php:1852,
#: framework/features/header/items/account/options.php:1974,
#: framework/premium/features/content-blocks/options/popup.php:632,
#: framework/premium/features/content-blocks/options/popup.php:663,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:219,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:250,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:279,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:345,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:376,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:405,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1430,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:220,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:234,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:270,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:196,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:232,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:433,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:463,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:797,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:480,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:511,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:126,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:349,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:383,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:422,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:151,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:180,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:159,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:189,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:209,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:239,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:269,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:311,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:341,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:381,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:409,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:27,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:50,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:215,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:231,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:267,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:308,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:82,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:275,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:319,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:363,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:403,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:442,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:487,
#: framework/premium/features/premium-header/items/contacts/options.php:797,
#: framework/premium/features/premium-header/items/contacts/options.php:828,
#: framework/premium/features/premium-header/items/contacts/options.php:857,
#: framework/premium/features/premium-header/items/contacts/options.php:956,
#: framework/premium/features/premium-header/items/contacts/options.php:994,
#: framework/premium/features/premium-header/items/contacts/options.php:1032,
#: framework/premium/features/premium-header/items/search-input/options.php:313,
#: framework/premium/features/premium-header/items/search-input/options.php:345,
#: framework/premium/features/premium-header/items/search-input/options.php:375,
#: framework/premium/features/premium-header/items/search-input/options.php:443,
#: framework/premium/features/premium-header/items/search-input/options.php:475,
#: framework/premium/features/premium-header/items/search-input/options.php:505,
#: framework/premium/features/premium-header/items/search-input/options.php:573,
#: framework/premium/features/premium-header/items/search-input/options.php:605,
#: framework/premium/features/premium-header/items/search-input/options.php:635,
#: framework/premium/features/premium-header/items/search-input/options.php:708,
#: framework/premium/features/premium-header/items/search-input/options.php:738,
#: framework/premium/features/premium-header/items/search-input/options.php:768,
#: framework/premium/features/premium-header/items/search-input/options.php:851,
#: framework/premium/features/premium-header/items/search-input/options.php:879,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:84,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:105,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:128,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:154,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:268,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:298,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:327,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:399,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:430,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:459,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:98,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:128,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:157,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:241,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:285,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:327,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:403,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:438,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:471,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:226,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:257,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:286,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:353,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:384,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:413
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:261
#: static/js/editor/blocks/about-me/Edit.js:189
#: static/js/editor/blocks/contact-info/Edit.js:203
#: static/js/editor/blocks/search/Edit.js:409
#: static/js/editor/blocks/share-box/Edit.js:152
#: static/js/editor/blocks/socials/Edit.js:152
msgid "Initial"
msgstr "Początkowo"

#: framework/extensions/cookies-consent/customizer.php:170,
#: framework/extensions/cookies-consent/customizer.php:226,
#: framework/extensions/cookies-consent/customizer.php:257,
#: framework/extensions/cookies-consent/customizer.php:292,
#: framework/extensions/cookies-consent/customizer.php:321,
#: framework/extensions/newsletter-subscribe/customizer.php:219,
#: framework/extensions/newsletter-subscribe/customizer.php:350,
#: framework/extensions/trending/customizer.php:678,
#: framework/extensions/trending/customizer.php:731,
#: framework/extensions/trending/customizer.php:772,
#: framework/extensions/trending/customizer.php:804,
#: framework/extensions/trending/customizer.php:895,
#: framework/premium/extensions/mega-menu/options.php:850,
#: framework/premium/extensions/shortcuts/customizer.php:1082,
#: framework/premium/extensions/shortcuts/customizer.php:1116,
#: framework/premium/extensions/shortcuts/customizer.php:1148,
#: framework/features/header/items/account/options.php:1207,
#: framework/features/header/items/account/options.php:1250,
#: framework/features/header/items/account/options.php:1292,
#: framework/features/header/items/account/options.php:1398,
#: framework/features/header/items/account/options.php:1440,
#: framework/features/header/items/account/options.php:1481,
#: framework/features/header/items/account/options.php:1545,
#: framework/features/header/items/account/options.php:1764,
#: framework/features/header/items/account/options.php:1810,
#: framework/features/header/items/account/options.php:1861,
#: framework/features/header/items/account/options.php:1979,
#: framework/premium/features/content-blocks/options/popup.php:638,
#: framework/premium/features/content-blocks/options/popup.php:669,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:225,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:255,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:284,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:351,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:381,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:410,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1436,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:802,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:486,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:517,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:354,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:389,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:428,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:245,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:275,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:317,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:347,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:386,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:414,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:63,
#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:56,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:236,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:273,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:314,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:281,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:325,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:369,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:409,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:448,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:493,
#: framework/premium/features/premium-header/items/contacts/options.php:803,
#: framework/premium/features/premium-header/items/contacts/options.php:833,
#: framework/premium/features/premium-header/items/contacts/options.php:862,
#: framework/premium/features/premium-header/items/contacts/options.php:961,
#: framework/premium/features/premium-header/items/contacts/options.php:999,
#: framework/premium/features/premium-header/items/contacts/options.php:1037,
#: framework/premium/features/premium-header/items/search-input/options.php:856,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:133,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:250,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:293,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:335,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:409,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:443,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:476,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:232,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:262,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:291,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:359,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:389,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:418
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:270
#: static/js/editor/blocks/about-me/Edit.js:198
#: static/js/editor/blocks/contact-info/Edit.js:212
#: static/js/editor/blocks/search/Edit.js:368
#: static/js/editor/blocks/share-box/Edit.js:161
#: static/js/editor/blocks/socials/Edit.js:161
msgid "Hover"
msgstr "Po najechaniu"

#: framework/extensions/newsletter-subscribe/customizer.php:328,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:253
msgid "Button Color"
msgstr "Kolor przycisku"

#: framework/extensions/cookies-consent/customizer.php:178,
#: framework/extensions/cookies-consent/customizer.php:234,
#: framework/extensions/cookies-consent/customizer.php:299,
#: framework/premium/extensions/mega-menu/options.php:899,
#: framework/features/header/items/account/options.php:1829,
#: framework/features/header/items/account/options.php:1953,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:134,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:405,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:289,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:421,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:92,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:141
msgid "Background Color"
msgstr "Kolor tła"

#: framework/extensions/cookies-consent/customizer.php:98
msgid "Maximum Width"
msgstr "Maksymalna szerokość"

#: framework/extensions/cookies-consent/customizer.php:119
msgid "I accept the %sPrivacy Policy%s*"
msgstr "Akceptuję %sPolitykę prywatności%s*"

#: framework/extensions/cookies-consent/customizer.php:123
msgid "This text will appear under each comment form and subscribe form."
msgstr "Ten tekst pojawi się pod każdym formularzem komentarza i formularzem subskrypcji."

#: framework/extensions/cookies-consent/helpers.php:55
msgid "I accept the %sPrivacy Policy%s"
msgstr "Akceptuję %sPolitykę prywatności%s"

#: framework/features/blocks/about-me/options.php:128
msgid "Customizer"
msgstr "Personalizacja"

#: framework/extensions/newsletter-subscribe/config.php:6
msgid "Easily capture new leads for your newsletter with the help of a widget, shortcode or even a block inserted on your pages or posts."
msgstr "Wyświetl formularz subskrypcji za pomocą widżetu, krótkiego kodu lub bloku."

#: framework/extensions/newsletter-subscribe/customizer.php:4
msgid "Subscribe Form"
msgstr "Formularz subskrypcji"

#: framework/extensions/newsletter-subscribe/customizer.php:18,
#: framework/features/blocks/about-me/options.php:15,
#: framework/features/blocks/contact-info/options.php:34,
#: framework/features/blocks/contact-info/options.php:80,
#: framework/features/blocks/contact-info/options.php:145,
#: framework/features/blocks/contact-info/options.php:208,
#: framework/features/blocks/contact-info/options.php:271,
#: framework/features/blocks/contact-info/options.php:334,
#: framework/features/blocks/contact-info/options.php:397,
#: framework/features/blocks/contact-info/options.php:460,
#: framework/features/blocks/share-box/options.php:14,
#: framework/features/blocks/socials/options.php:14,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:582,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:327,
#: framework/premium/features/premium-header/items/contacts/options.php:87,
#: framework/premium/features/premium-header/items/contacts/options.php:132,
#: framework/premium/features/premium-header/items/contacts/options.php:177,
#: framework/premium/features/premium-header/items/contacts/options.php:222,
#: framework/premium/features/premium-header/items/contacts/options.php:268,
#: framework/premium/features/premium-header/items/contacts/options.php:313,
#: framework/premium/features/premium-header/items/contacts/options.php:358
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:45
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:125
msgid "Title"
msgstr "Tytuł"

#: framework/extensions/newsletter-subscribe/customizer.php:20,
#: framework/extensions/newsletter-subscribe/helpers.php:21,
#: framework/extensions/newsletter-subscribe/helpers.php:69
msgid "Newsletter Updates"
msgstr "Aktualizacje newslettera"

#: framework/extensions/newsletter-subscribe/customizer.php:26,
#: framework/features/blocks/about-me/options.php:64,
#: framework/features/blocks/dynamic-data/options.php:142,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:78,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:159,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:248
msgid "Description"
msgstr "Opis"

#: framework/extensions/newsletter-subscribe/helpers.php:72
msgid "Enter your email address below to subscribe to our newsletter"
msgstr "Wprowadź swój adres e-mail poniżej, aby zapisać się do naszego newslettera"

#: framework/extensions/newsletter-subscribe/customizer.php:41,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:34
msgid "List Source"
msgstr "Źródło listy"

#: framework/extensions/newsletter-subscribe/customizer.php:48,
#: framework/extensions/product-reviews/metabox.php:17,
#: framework/extensions/trending/customizer.php:232,
#: framework/extensions/trending/customizer.php:306,
#: framework/extensions/trending/customizer.php:448,
#: framework/features/header/header-options.php:76,
#: framework/premium/features/premium-header.php:322,
#: framework/premium/features/socials.php:20,
#: framework/premium/features/socials.php:48,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:40,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:81,
#: framework/features/blocks/contact-info/options.php:110,
#: framework/features/blocks/contact-info/options.php:175,
#: framework/features/blocks/contact-info/options.php:238,
#: framework/features/blocks/contact-info/options.php:301,
#: framework/features/blocks/contact-info/options.php:364,
#: framework/features/blocks/contact-info/options.php:427,
#: framework/features/blocks/contact-info/options.php:490,
#: framework/features/header/items/account/options.php:385,
#: framework/features/header/items/account/options.php:766,
#: framework/premium/features/content-blocks/options/hook.php:166,
#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:19,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:12,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:17,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:17
msgid "Default"
msgstr "Domyślnie"

#: framework/extensions/newsletter-subscribe/customizer.php:49,
#: framework/extensions/trending/customizer.php:236,
#: framework/premium/features/premium-header.php:323,
#: framework/premium/features/socials.php:21,
#: framework/premium/features/socials.php:49,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:41,
#: framework/features/blocks/about-me/options.php:27,
#: framework/features/blocks/about-me/options.php:194,
#: framework/features/blocks/contact-info/options.php:111,
#: framework/features/blocks/contact-info/options.php:176,
#: framework/features/blocks/contact-info/options.php:239,
#: framework/features/blocks/contact-info/options.php:302,
#: framework/features/blocks/contact-info/options.php:365,
#: framework/features/blocks/contact-info/options.php:428,
#: framework/features/blocks/contact-info/options.php:491,
#: framework/features/blocks/dynamic-data/options.php:64,
#: framework/features/blocks/share-box/options.php:148,
#: framework/features/blocks/socials/options.php:100,
#: framework/features/conditions/rules/custom.php:33,
#: framework/premium/extensions/shortcuts/customizer.php:260,
#: framework/premium/extensions/shortcuts/customizer.php:287,
#: framework/features/header/items/account/options.php:25,
#: framework/features/header/items/account/options.php:389,
#: framework/features/header/items/account/options.php:770,
#: framework/premium/features/content-blocks/options/archive.php:75,
#: framework/premium/features/content-blocks/options/hook.php:168,
#: framework/premium/features/content-blocks/options/popup.php:287,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:616,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:196,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:188,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:41,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:843,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:18,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:18
#: static/js/editor/blocks/dynamic-data/utils.js:10
msgid "Custom"
msgstr "Własne"

#: framework/extensions/newsletter-subscribe/customizer.php:61,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:50
#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:199
msgid "List ID"
msgstr "Identyfikator listy"

#: framework/extensions/newsletter-subscribe/customizer.php:79,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:109
msgid "Name Field"
msgstr "pole nazwy"

#: framework/extensions/newsletter-subscribe/customizer.php:117,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:121
msgid "Name Label"
msgstr "Etykieta nazwy"

#: framework/extensions/newsletter-subscribe/customizer.php:119,
#: framework/extensions/newsletter-subscribe/extension.php:208,
#: framework/extensions/newsletter-subscribe/helpers.php:37,
#: framework/extensions/newsletter-subscribe/helpers.php:81,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:122,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:147
msgid "Your name"
msgstr "Twoje imię i nazwisko"

#: framework/extensions/newsletter-subscribe/customizer.php:128,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:136
msgid "Mail Label"
msgstr "Etykieta e-maila"

#: framework/extensions/newsletter-subscribe/customizer.php:130,
#: framework/extensions/newsletter-subscribe/extension.php:209,
#: framework/extensions/newsletter-subscribe/helpers.php:41,
#: framework/extensions/newsletter-subscribe/helpers.php:82
msgid "Your email"
msgstr "Twój adres e-mail"

#: framework/extensions/newsletter-subscribe/customizer.php:137,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:142,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:414
msgid "Button Label"
msgstr "Etykieta przycisku"

#: framework/extensions/newsletter-subscribe/customizer.php:139,
#: framework/extensions/newsletter-subscribe/extension.php:203,
#: framework/extensions/newsletter-subscribe/helpers.php:31,
#: framework/extensions/newsletter-subscribe/helpers.php:76,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:143,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:21
msgid "Subscribe"
msgstr "Subskrybuj"

#: framework/extensions/newsletter-subscribe/customizer.php:149,
#: framework/extensions/trending/customizer.php:516,
#: framework/premium/extensions/shortcuts/customizer.php:48,
#: framework/premium/extensions/shortcuts/customizer.php:113,
#: framework/premium/extensions/shortcuts/customizer.php:178,
#: framework/premium/extensions/shortcuts/customizer.php:237,
#: framework/premium/extensions/shortcuts/customizer.php:326,
#: framework/premium/extensions/shortcuts/customizer.php:388,
#: framework/premium/extensions/shortcuts/customizer.php:447,
#: framework/premium/extensions/shortcuts/customizer.php:517,
#: framework/premium/extensions/shortcuts/customizer.php:581,
#: framework/premium/extensions/shortcuts/customizer.php:645,
#: framework/premium/extensions/shortcuts/customizer.php:709,
#: framework/premium/extensions/shortcuts/customizer.php:996,
#: framework/premium/features/content-blocks/options/header.php:167,
#: framework/premium/features/content-blocks/options/hook.php:300,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:155,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:423,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:43
msgid "Visibility"
msgstr "Widoczność"

#: framework/extensions/newsletter-subscribe/customizer.php:160,
#: framework/extensions/trending/customizer.php:528,
#: framework/features/header/header-options.php:108,
#: framework/features/header/header-options.php:190,
#: framework/features/blocks/contact-info/options.php:24,
#: framework/features/blocks/search/options.php:168,
#: framework/premium/extensions/mega-menu/options.php:413,
#: framework/premium/extensions/shortcuts/customizer.php:62,
#: framework/premium/extensions/shortcuts/customizer.php:127,
#: framework/premium/extensions/shortcuts/customizer.php:192,
#: framework/premium/extensions/shortcuts/customizer.php:251,
#: framework/premium/extensions/shortcuts/customizer.php:341,
#: framework/premium/extensions/shortcuts/customizer.php:402,
#: framework/premium/extensions/shortcuts/customizer.php:461,
#: framework/premium/extensions/shortcuts/customizer.php:531,
#: framework/premium/extensions/shortcuts/customizer.php:595,
#: framework/premium/extensions/shortcuts/customizer.php:659,
#: framework/premium/extensions/shortcuts/customizer.php:723,
#: framework/premium/extensions/shortcuts/customizer.php:861,
#: framework/premium/extensions/shortcuts/customizer.php:918,
#: framework/premium/extensions/shortcuts/customizer.php:1010,
#: framework/features/header/items/account/options.php:532,
#: framework/features/header/items/account/options.php:995,
#: framework/premium/features/content-blocks/options/header.php:178,
#: framework/premium/features/content-blocks/options/hook.php:311,
#: framework/premium/features/content-blocks/options/popup.php:506,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:70,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:168,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:396,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:651,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:436,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:42,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:61,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:80,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:98,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:90,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:258,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:286,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:49,
#: framework/premium/features/premium-header/items/contacts/options.php:32,
#: framework/premium/features/premium-header/items/contacts/options.php:553,
#: framework/premium/features/premium-header/items/language-switcher/options.php:131,
#: framework/premium/features/premium-header/items/search-input/options.php:157,
#: framework/premium/features/premium-header/items/search-input/options.php:250,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:56,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:104,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:105
msgid "Desktop"
msgstr "Komputer stacjonarny"

#: framework/extensions/newsletter-subscribe/customizer.php:161,
#: framework/extensions/trending/customizer.php:529,
#: framework/features/blocks/contact-info/options.php:25,
#: framework/features/blocks/search/options.php:169,
#: framework/premium/extensions/shortcuts/customizer.php:63,
#: framework/premium/extensions/shortcuts/customizer.php:128,
#: framework/premium/extensions/shortcuts/customizer.php:193,
#: framework/premium/extensions/shortcuts/customizer.php:252,
#: framework/premium/extensions/shortcuts/customizer.php:342,
#: framework/premium/extensions/shortcuts/customizer.php:403,
#: framework/premium/extensions/shortcuts/customizer.php:462,
#: framework/premium/extensions/shortcuts/customizer.php:532,
#: framework/premium/extensions/shortcuts/customizer.php:596,
#: framework/premium/extensions/shortcuts/customizer.php:660,
#: framework/premium/extensions/shortcuts/customizer.php:724,
#: framework/premium/extensions/shortcuts/customizer.php:862,
#: framework/premium/extensions/shortcuts/customizer.php:919,
#: framework/premium/extensions/shortcuts/customizer.php:1011,
#: framework/features/header/items/account/options.php:533,
#: framework/features/header/items/account/options.php:996,
#: framework/features/header/items/account/options.php:2069,
#: framework/premium/features/content-blocks/options/header.php:179,
#: framework/premium/features/content-blocks/options/hook.php:312,
#: framework/premium/features/content-blocks/options/popup.php:507,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:71,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:452,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:169,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:397,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:652,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:437,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:43,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:62,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:99,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:91,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:259,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:287,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:50,
#: framework/premium/features/premium-header/items/contacts/options.php:33,
#: framework/premium/features/premium-header/items/contacts/options.php:497,
#: framework/premium/features/premium-header/items/contacts/options.php:554,
#: framework/premium/features/premium-header/items/language-switcher/options.php:132,
#: framework/premium/features/premium-header/items/language-switcher/options.php:261,
#: framework/premium/features/premium-header/items/search-input/options.php:158,
#: framework/premium/features/premium-header/items/search-input/options.php:251,
#: framework/premium/features/premium-header/items/search-input/options.php:947,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:57,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:105,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:655,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:106,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:589
msgid "Tablet"
msgstr "Tablet"

#: framework/extensions/newsletter-subscribe/customizer.php:162,
#: framework/extensions/trending/customizer.php:530,
#: framework/features/header/header-options.php:110,
#: framework/features/header/header-options.php:192,
#: framework/features/blocks/contact-info/options.php:26,
#: framework/features/blocks/contact-info/options.php:202,
#: framework/features/blocks/search/options.php:170,
#: framework/premium/extensions/mega-menu/options.php:414,
#: framework/premium/extensions/shortcuts/customizer.php:64,
#: framework/premium/extensions/shortcuts/customizer.php:129,
#: framework/premium/extensions/shortcuts/customizer.php:194,
#: framework/premium/extensions/shortcuts/customizer.php:253,
#: framework/premium/extensions/shortcuts/customizer.php:343,
#: framework/premium/extensions/shortcuts/customizer.php:404,
#: framework/premium/extensions/shortcuts/customizer.php:463,
#: framework/premium/extensions/shortcuts/customizer.php:533,
#: framework/premium/extensions/shortcuts/customizer.php:597,
#: framework/premium/extensions/shortcuts/customizer.php:661,
#: framework/premium/extensions/shortcuts/customizer.php:725,
#: framework/premium/extensions/shortcuts/customizer.php:863,
#: framework/premium/extensions/shortcuts/customizer.php:920,
#: framework/premium/extensions/shortcuts/customizer.php:1012,
#: framework/features/header/items/account/options.php:534,
#: framework/features/header/items/account/options.php:997,
#: framework/features/header/items/account/options.php:2070,
#: framework/premium/features/content-blocks/options/header.php:180,
#: framework/premium/features/content-blocks/options/hook.php:313,
#: framework/premium/features/content-blocks/options/popup.php:508,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:72,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:453,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:170,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:398,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:653,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:438,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:44,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:63,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:82,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:100,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:92,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:260,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:288,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:51,
#: framework/premium/features/premium-header/items/contacts/options.php:34,
#: framework/premium/features/premium-header/items/contacts/options.php:172,
#: framework/premium/features/premium-header/items/contacts/options.php:498,
#: framework/premium/features/premium-header/items/contacts/options.php:555,
#: framework/premium/features/premium-header/items/language-switcher/options.php:133,
#: framework/premium/features/premium-header/items/language-switcher/options.php:262,
#: framework/premium/features/premium-header/items/search-input/options.php:159,
#: framework/premium/features/premium-header/items/search-input/options.php:252,
#: framework/premium/features/premium-header/items/search-input/options.php:948,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:58,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:106,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:656,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:107,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:590
msgid "Mobile"
msgstr "Urządzenie przenośne"

#: framework/extensions/newsletter-subscribe/customizer.php:175,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:183,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:420,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:138,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:146
msgid "Title Color"
msgstr "Kolor tytułu"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:197
msgid "Description Color"
msgstr "Kolor opisu"

#: framework/extensions/newsletter-subscribe/customizer.php:227,
#: framework/features/header/items/account/options.php:1553,
#: framework/premium/features/premium-header/items/search-input/options.php:266,
#: framework/premium/features/premium-header/items/search-input/options.php:295,
#: framework/premium/features/premium-header/items/search-input/options.php:327,
#: framework/premium/features/premium-header/items/search-input/options.php:357
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:128
#: static/js/editor/blocks/search/Edit.js:196
msgid "Input Font Color"
msgstr "Kolor tekstu pola formularza"

#: framework/extensions/newsletter-subscribe/customizer.php:251,
#: framework/extensions/newsletter-subscribe/customizer.php:282,
#: framework/extensions/newsletter-subscribe/customizer.php:318,
#: framework/features/header/items/account/options.php:1579,
#: framework/features/header/items/account/options.php:1617,
#: framework/features/header/items/account/options.php:1663,
#: framework/premium/features/premium-header/items/search-input/options.php:319,
#: framework/premium/features/premium-header/items/search-input/options.php:350,
#: framework/premium/features/premium-header/items/search-input/options.php:380,
#: framework/premium/features/premium-header/items/search-input/options.php:449,
#: framework/premium/features/premium-header/items/search-input/options.php:480,
#: framework/premium/features/premium-header/items/search-input/options.php:510,
#: framework/premium/features/premium-header/items/search-input/options.php:579,
#: framework/premium/features/premium-header/items/search-input/options.php:610,
#: framework/premium/features/premium-header/items/search-input/options.php:640,
#: framework/premium/features/premium-header/items/search-input/options.php:713,
#: framework/premium/features/premium-header/items/search-input/options.php:743,
#: framework/premium/features/premium-header/items/search-input/options.php:773
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:210
#: static/js/editor/blocks/search/Edit.js:269
msgid "Focus"
msgstr "Skupienie"

#: framework/extensions/newsletter-subscribe/customizer.php:259,
#: framework/features/header/items/account/options.php:1588,
#: framework/premium/features/premium-header/items/search-input/options.php:526,
#: framework/premium/features/premium-header/items/search-input/options.php:555,
#: framework/premium/features/premium-header/items/search-input/options.php:587,
#: framework/premium/features/premium-header/items/search-input/options.php:617
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:159
#: static/js/editor/blocks/search/Edit.js:223
msgid "Input Border Color"
msgstr "Kolor obramowania pola"

#: framework/extensions/newsletter-subscribe/customizer.php:296,
#: framework/features/header/items/account/options.php:1631,
#: framework/premium/features/premium-header/items/search-input/options.php:662,
#: framework/premium/features/premium-header/items/search-input/options.php:690,
#: framework/premium/features/premium-header/items/search-input/options.php:720,
#: framework/premium/features/premium-header/items/search-input/options.php:750
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:190
#: static/js/editor/blocks/search/Edit.js:250
msgid "Input Background Color"
msgstr "Kolor tła pola"

#: framework/extensions/newsletter-subscribe/customizer.php:357,
#: framework/extensions/trending/customizer.php:903,
#: framework/premium/extensions/shortcuts/customizer.php:1335,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:525,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:209
msgid "Container Background"
msgstr "Kontener tła"

#: framework/extensions/newsletter-subscribe/customizer.php:373,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:193
msgid "Container Border"
msgstr "Obramowanie kontenera"

#: framework/extensions/newsletter-subscribe/customizer.php:408,
#: framework/extensions/trending/customizer.php:919
msgid "Container Inner Spacing"
msgstr "Wewnętrzne odstępy kontenera"

#: framework/extensions/newsletter-subscribe/customizer.php:422,
#: framework/premium/extensions/shortcuts/customizer.php:1368,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:237
msgid "Container Border Radius"
msgstr "Zaokrąglenie kontenera"

#: framework/extensions/newsletter-subscribe/extension.php:155
msgid "Disable Subscribe Form"
msgstr "Wyłącz formularz subskrypcji"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:253,
#: framework/features/blocks/about-me/options.php:58,
#: framework/features/header/items/account/options.php:578,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:167
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:128
msgid "Name"
msgstr "Nazwa"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:262,
#: framework/features/blocks/contact-info/options.php:391,
#: framework/features/blocks/dynamic-data/options.php:143,
#: framework/features/blocks/share-box/options.php:97,
#: framework/features/header/modal/register.php:47,
#: framework/premium/extensions/shortcuts/customizer.php:136,
#: framework/premium/extensions/shortcuts/customizer.php:162,
#: framework/premium/extensions/shortcuts/views/bar.php:48,
#: framework/premium/features/premium-header/items/contacts/options.php:308,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:168
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:23
msgid "Email"
msgstr "E-mail"

#: framework/extensions/product-reviews/extension.php:540,
#: framework/extensions/product-reviews/extension.php:541,
#: framework/extensions/product-reviews/extension.php:544,
#: framework/extensions/product-reviews/extension.php:546
msgid "Product Reviews"
msgstr "Recenzje produktów"

#: framework/extensions/product-reviews/config.php:6
msgid "This extension lets you easily create an affiliate marketing type of website by giving you options to create a personalized product review and use your affiliate links to direct your readers to the purchase page."
msgstr "Rozszerzenie umożliwia łatwe tworzenie witryny typu marketing afiliacyjny, oferując opcje tworzenia spersonalizowanej recenzji produktu i korzystania z linków afiliacyjnych, aby skierować czytelników na stronę zakupu."

#: framework/extensions/product-reviews/extension.php:318,
#: framework/extensions/product-reviews/views/single-top.php:139
msgid "Overall Score"
msgstr "Ogólny wynik"

#: framework/extensions/product-reviews/extension.php:376
msgid "Review Summary"
msgstr "Podsumowanie opinii"

#: framework/extensions/product-reviews/extension.php:387
msgid "Scores Box Width"
msgstr "Szerokość pola wyników"

#: framework/extensions/product-reviews/extension.php:397
msgid "Read More Button"
msgstr "Przycisk czytaj więcej"

#: framework/extensions/product-reviews/extension.php:408
msgid "Buy Now Button"
msgstr "Przycisk kup teraz"

#: framework/extensions/product-reviews/extension.php:256,
#: framework/extensions/product-reviews/extension.php:427
msgid "Star Rating Color"
msgstr "Kolor oceny gwiazdek"

#: framework/extensions/product-reviews/extension.php:274,
#: framework/extensions/product-reviews/extension.php:444,
#: framework/extensions/product-reviews/extension.php:472,
#: framework/extensions/product-reviews/extension.php:493,
#: framework/premium/extensions/mega-menu/options.php:855,
#: framework/features/header/items/account/options.php:1985,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:221,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:227,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:88,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:94,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:286,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:330,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:375,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:414,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:454,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:498
#: static/js/dashboard/screens/Extensions/Sidebar.js:120
msgid "Active"
msgstr "Włączono"

#: framework/extensions/product-reviews/extension.php:280,
#: framework/extensions/product-reviews/extension.php:450
msgid "Inactive"
msgstr "Nieaktywne"

#: framework/extensions/product-reviews/extension.php:458
msgid "Overll Score Text"
msgstr "Tekst wyniku ogólnego"

#: framework/extensions/product-reviews/extension.php:480
msgid "Overll Score Background"
msgstr "Tło wyniku ogólnego"

#: framework/extensions/product-reviews/extension.php:545
msgid "Product Review"
msgstr "Opinia o produkcie"

#: framework/extensions/product-reviews/extension.php:547
msgid "Parent Product Review"
msgstr "Opinia o produkcie nadrzędnym"

#: framework/extensions/product-reviews/extension.php:548
msgid "All Reviews"
msgstr "Wszystkie opinie"

#: framework/extensions/product-reviews/extension.php:549
msgid "View Product Review"
msgstr "Wyświetl recenzję produktu"

#: framework/extensions/product-reviews/extension.php:550
msgid "Add New Product Review"
msgstr "Dodaj opinię produktu"

#: framework/extensions/product-reviews/extension.php:551
msgid "Add New Review"
msgstr "Dodaj nową opinię"

#: framework/extensions/product-reviews/extension.php:552
msgid "Edit Product Review"
msgstr "Edytuj opinię o produkcie"

#: framework/extensions/product-reviews/extension.php:553
msgid "Update Product Review"
msgstr "Zaktualizuj recenzję produktu"

#: framework/extensions/product-reviews/extension.php:554
msgid "Search Product Review"
msgstr "Wyszukaj recenzję produktu"

#: framework/extensions/product-reviews/extension.php:555
msgid "Not Found"
msgstr "Nie znaleziono"

#: framework/extensions/product-reviews/extension.php:556
msgid "Not found in Trash"
msgstr "Niczego nie znaleziono w koszu"

#: framework/extensions/product-reviews/extension.php:590,
#: framework/extensions/product-reviews/extension.php:600,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:100,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:190
msgid "Categories"
msgstr "Kategorie"

#: framework/extensions/product-reviews/extension.php:591,
#: framework/extensions/trending/customizer.php:36,
#: framework/extensions/trending/customizer.php:117
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:88
msgid "Category"
msgstr "Kategoria"

#: framework/extensions/product-reviews/extension.php:592
msgid "Search Category"
msgstr "Szukaj kategorii"

#: framework/extensions/product-reviews/extension.php:593
#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:88
msgid "All Categories"
msgstr "Wszystkie kategorie"

#: framework/extensions/product-reviews/extension.php:594
msgid "Parent Category"
msgstr "Kategoria nadrzędna"

#: framework/extensions/product-reviews/extension.php:595
msgid "Parent Category:"
msgstr "Kategoria nadrzędna:"

#: framework/extensions/product-reviews/extension.php:596
msgid "Edit Category"
msgstr "Edytuj kategorię"

#: framework/extensions/product-reviews/extension.php:597
msgid "Update Category"
msgstr "Zaktualizuj kategorię"

#: framework/extensions/product-reviews/extension.php:598
msgid "Add New Category"
msgstr "Utwórz kategorię"

#: framework/extensions/product-reviews/extension.php:599
msgid "New Category Name"
msgstr "Nazwa nowej kategorii"

#. translators: %s is the theme name.
#: framework/extensions/product-reviews/extension.php:618
msgid "%s Settings"
msgstr "Ustawienia: %s"

#: framework/dashboard.php:476, framework/dashboard.php:477,
#: framework/extensions/product-reviews/extension.php:619
msgid "Blocksy"
msgstr "Blocksy"

#: framework/extensions/product-reviews/helpers.php:30,
#: framework/extensions/product-reviews/metabox.php:181,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:230,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:394
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:132
msgid "Rating"
msgstr "Ocena"

#: framework/extensions/product-reviews/metabox.php:10
msgid "Review Entity"
msgstr "Przejrzyj opinię"

#: framework/extensions/product-reviews/metabox.php:18,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/table.php:39,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:34,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:52,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:33,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:33
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:11
msgid "Product"
msgstr "Produkt"

#: framework/extensions/product-reviews/metabox.php:19
msgid "Book"
msgstr "Książka"

#: framework/extensions/product-reviews/metabox.php:21
msgid "Creative Work Season"
msgstr "Sezon kreatywnej pracy"

#: framework/extensions/product-reviews/metabox.php:22
msgid "Creative Work Series"
msgstr "Seria prac twórczych"

#: framework/extensions/product-reviews/metabox.php:23
msgid "Episode"
msgstr "Odcinek"

#: framework/extensions/product-reviews/metabox.php:25
msgid "Game"
msgstr "Gra"

#: framework/extensions/product-reviews/metabox.php:27
msgid "Local Business"
msgstr "Lokalna firma"

#: framework/extensions/product-reviews/metabox.php:28
msgid "Media Object"
msgstr "Obiekt medialny"

#: framework/extensions/product-reviews/metabox.php:29
msgid "Movie"
msgstr "Film"

#: framework/extensions/product-reviews/metabox.php:30
msgid "Music Playlist"
msgstr "Lista odtwarzania muzyki"

#: framework/extensions/product-reviews/metabox.php:31
msgid "Music Recording"
msgstr "Nagrywanie muzyki"

#: framework/extensions/product-reviews/metabox.php:32
msgid "Organization"
msgstr "Organizacja"

#: framework/extensions/product-reviews/metabox.php:38
msgid "More info about review entity and how to choose the right one can be found %shere%s."
msgstr "Więcej informacji na temat opinii i jak wybrać właściwą można znaleźć %stutaj%s."

#: framework/extensions/product-reviews/metabox.php:69
msgid "Product Price"
msgstr "Cena produktu"

#: framework/extensions/product-reviews/metabox.php:76
msgid "Product SKU"
msgstr "SKU produktu"

#: framework/extensions/product-reviews/metabox.php:83
msgid "Product Brand"
msgstr "Marka produktu"

#: framework/extensions/product-reviews/metabox.php:96
msgid "Gallery"
msgstr "Galeria"

#: framework/extensions/product-reviews/metabox.php:107
msgid "Affiliate Button Label"
msgstr "Etykieta przycisku partnerskiego"

#: framework/extensions/product-reviews/metabox.php:109,
#: framework/extensions/product-reviews/views/single-top.php:156
msgid "Buy Now"
msgstr "Kup teraz"

#: framework/extensions/product-reviews/metabox.php:114
msgid "Affiliate Link"
msgstr "Odnośnik partnerski"

#: framework/extensions/product-reviews/metabox.php:120
msgid "Open Link In New Tab"
msgstr "Otwórz odnośniki w nowej zakładce"

#: framework/extensions/product-reviews/metabox.php:127
msgid "Sponsored Attribute"
msgstr "Atrybut sponsorowany"

#: framework/extensions/product-reviews/metabox.php:150
msgid "Read More Button Label"
msgstr "Etykieta przycisku czytaj więcej"

#: framework/extensions/product-reviews/metabox.php:152,
#: framework/extensions/product-reviews/views/single-top.php:162
msgid "Read More"
msgstr "Dowiedz się więcej"

#: framework/extensions/product-reviews/metabox.php:172
msgid "Short Description"
msgstr "Krótki opis"

#: framework/extensions/product-reviews/metabox.php:187
msgid "Scores"
msgstr "Wyniki"

#: framework/extensions/product-reviews/metabox.php:227
msgid "Product specs"
msgstr "Specyfikacje produktu"

#: framework/extensions/product-reviews/metabox.php:252,
#: framework/extensions/product-reviews/views/single-top.php:262
msgid "Pros"
msgstr "Plusy"

#: framework/extensions/product-reviews/metabox.php:272,
#: framework/extensions/product-reviews/views/single-top.php:285
msgid "Cons"
msgstr "Minusy"

#: framework/extensions/trending/customizer.php:169
msgid "Trending Posts"
msgstr "Najpopularniejsze wpisy"

#: framework/extensions/trending/config.php:6
msgid "Highlight your most popular posts or products based on the number of comments or reviews they have gotten in the specified period of time."
msgstr "Wyróżnij swoje najpopularniejsze wpisy lub produkty na podstawie liczby komentarzy lub recenzji, które pojawiły się w określonym czasie."

#: framework/extensions/trending/customizer.php:8,
#: framework/features/blocks/search/options.php:16,
#: framework/premium/extensions/shortcuts/views/bar.php:51,
#: framework/premium/features/premium-header/items/search-input/options.php:14
msgid "Products"
msgstr "Produkty"

#: framework/extensions/trending/customizer.php:37
msgid "All categories"
msgstr "Wszystkie kategorie"

#: framework/extensions/trending/customizer.php:42
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:210
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:34
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:66
#: static/js/editor/blocks/tax-query/Edit.js:156
msgid "Taxonomy"
msgstr "Taksonomia"

#: framework/extensions/trending/customizer.php:43
msgid "All taxonomies"
msgstr "Wszystkie taksonomie"

#: framework/extensions/trending/customizer.php:179
msgid "Module Title"
msgstr "Tytuł modułu"

#: framework/extensions/trending/customizer.php:182,
#: framework/extensions/trending/helpers.php:352
msgid "Trending now"
msgstr "Popularne"

#: framework/extensions/trending/customizer.php:187
msgid "Module Title Tag"
msgstr "Tag tytułu modułu"

#: framework/extensions/trending/customizer.php:324,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:284
msgid "Source"
msgstr "Źródło"

#: framework/extensions/trending/customizer.php:329
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:229
msgid "Taxonomies"
msgstr "Taksonomie"

#: framework/extensions/trending/customizer.php:330
msgid "Custom Query"
msgstr "Własne zapytanie"

#: framework/extensions/trending/customizer.php:354
msgid "Posts ID"
msgstr "Identyfikator wpisu"

#: framework/extensions/trending/customizer.php:358
msgid "Separate posts ID by comma. How to find the %spost ID%s."
msgstr "Oddziel identyfikatory wpisów przecinkami. Jak znaleźć identyfikator %swpisu%s."

#: framework/extensions/trending/customizer.php:375
msgid "Trending From"
msgstr "Trendy z"

#: framework/extensions/trending/customizer.php:383
msgid "All Time"
msgstr "Cały okres"

#: framework/extensions/trending/customizer.php:384
msgid "Last 24 Hours"
msgstr "Ostatnie 24 godziny"

#: framework/extensions/trending/customizer.php:385
msgid "Last 7 Days"
msgstr "Ostatnie 7 dni"

#: framework/extensions/trending/customizer.php:386
msgid "Last Month"
msgstr "Ostatni miesiąc"

#: framework/features/header/account-modal.php:37,
#: framework/features/header/items/account/options.php:1053,
#: framework/features/header/items/account/views/logout.php:8
msgid "Login"
msgstr "Logowanie"

#: framework/features/header/account-modal.php:41
msgid "Sign Up"
msgstr "Zarejestruj się"

#: framework/features/header/account-modal.php:61
msgid "Back to login"
msgstr "Powrót do logowania"

#: framework/features/header/header-options.php:11
msgid "Sticky Functionality"
msgstr "Funkcjonalność \"Przypięty\""

#: framework/features/header/header-options.php:37
msgid "Only Main Row"
msgstr "Tylko główny rząd"

#: framework/features/header/header-options.php:42
msgid "Top & Main Row"
msgstr "Górny i główny rząd"

#: framework/features/header/header-options.php:47
msgid "All Rows"
msgstr "Wszystkie rzędy"

#: framework/features/header/header-options.php:52
msgid "Main & Bottom Row"
msgstr "Główny i dolny rząd"

#: framework/features/header/header-options.php:57
msgid "Only Top Row"
msgstr "Tylko górny rząd"

#: framework/features/header/header-options.php:62
msgid "Only Bottom Row"
msgstr "Tylko dolny rząd"

#: framework/features/header/header-options.php:77
msgid "Slide Down"
msgstr "Wjazd na dół"

#: framework/features/header/header-options.php:78
msgid "Fade"
msgstr "Przenikanie"

#: framework/features/header/header-options.php:79
msgid "Auto Hide/Show"
msgstr "Automatyczne ukrywanie/pokazywanie"

#: framework/features/header/header-options.php:97,
#: framework/features/header/header-options.php:179
msgid "Enable on"
msgstr "Włącz na"

#: framework/features/header/header-options.php:125
msgid "Transparent Functionality"
msgstr "Funkcjonalność przezroczystości"

#: framework/extensions/trending/customizer.php:561,
#: framework/features/header/header-options.php:163,
#: framework/premium/extensions/shortcuts/customizer.php:1017,
#: framework/premium/features/content-blocks/options/header.php:31,
#: framework/premium/features/content-blocks/options/hook.php:36,
#: framework/premium/features/content-blocks/options/maintenance.php:5,
#: framework/premium/features/content-blocks/options/nothing_found.php:29,
#: framework/premium/features/content-blocks/options/nothing_found.php:34,
#: framework/premium/features/content-blocks/options/popup.php:49,
#: framework/premium/features/content-blocks/options/single.php:10,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:443,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:5,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:102,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:24
#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:50
#: framework/premium/static/js/footer/EditConditions.js:97
#: static/js/header/EditConditions.js:103
msgid "Display Conditions"
msgstr "Warunki wyświetlania"

#: framework/premium/features/content-blocks/admin-ui.php:653
msgid "Hooks Locations"
msgstr "Lokalizacja haków"

#: framework/premium/extensions/shortcuts/customizer.php:765,
#: framework/premium/features/content-blocks/admin-ui.php:342,
#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:12,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:5
msgid "Type"
msgstr "Typ"

#: framework/premium/features/content-blocks/admin-ui.php:343
msgid "Location/Trigger"
msgstr "Lokalizacja / Wyzwalacz"

#: framework/premium/features/content-blocks/admin-ui.php:344,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:80,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:143
msgid "Conditions"
msgstr "Warunki"

#: framework/premium/features/content-blocks/admin-ui.php:345
msgid "Output"
msgstr "Wyjście"

#: framework/premium/features/content-blocks/admin-ui.php:346
msgid "Enable/Disable"
msgstr "Włączyć/wyłączyć"

#: framework/features/blocks/about-me/options.php:204,
#: framework/features/blocks/contact-info/options.php:564,
#: framework/features/blocks/share-box/options.php:160,
#: framework/features/blocks/socials/options.php:112,
#: framework/premium/extensions/shortcuts/customizer.php:980,
#: framework/premium/features/content-blocks/admin-ui.php:371,
#: framework/features/header/items/account/options.php:22,
#: framework/features/header/items/account/options.php:348,
#: framework/features/header/items/account/options.php:746,
#: framework/premium/features/content-blocks/options/hook.php:9,
#: framework/premium/features/content-blocks/options/hook.php:77,
#: framework/premium/features/content-blocks/options/hook.php:167,
#: framework/premium/features/content-blocks/options/popup.php:75,
#: framework/premium/features/content-blocks/options/popup.php:222,
#: framework/premium/features/content-blocks/options/popup.php:603,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/select.php:23,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:97,
#: framework/premium/features/premium-header/items/contacts/options.php:451
#: framework/premium/static/js/blocks/ContentBlock.js:56
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:170
msgid "None"
msgstr "Brak"

#: framework/premium/features/content-blocks/admin-ui.php:372,
#: framework/premium/features/content-blocks/options/popup.php:76
msgid "On scroll"
msgstr "Na przewijaniu"

#: framework/premium/features/content-blocks/admin-ui.php:373,
#: framework/premium/features/content-blocks/options/popup.php:77
msgid "On scroll to element"
msgstr "Po przewinięciu do elementu"

#: framework/premium/features/content-blocks/admin-ui.php:375,
#: framework/premium/features/content-blocks/options/popup.php:79
msgid "On page load"
msgstr "Podczas ładowania strony"

#: framework/premium/features/content-blocks/admin-ui.php:376,
#: framework/premium/features/content-blocks/options/popup.php:80
msgid "After inactivity"
msgstr "Po bezczynności"

#: framework/premium/features/content-blocks/admin-ui.php:377,
#: framework/premium/features/content-blocks/options/popup.php:81
msgid "After x time"
msgstr "Po x czasie"

#: framework/premium/features/content-blocks/admin-ui.php:379,
#: framework/premium/features/content-blocks/options/popup.php:83
msgid "On page exit intent"
msgstr "Podczas wychodzenia ze strony"

#: framework/premium/features/content-blocks/admin-ui.php:383
msgid "Down"
msgstr "Dół"

#: framework/premium/features/content-blocks/admin-ui.php:384
msgid "Up"
msgstr "Góra"

#: framework/premium/features/content-blocks.php:193,
#: framework/premium/features/content-blocks.php:199
msgid "Content Blocks"
msgstr "Bloki treści"

#: framework/premium/features/content-blocks.php:194,
#: framework/premium/extensions/mega-menu/options.php:346,
#: framework/premium/features/content-blocks/content-block-layer.php:163,
#: framework/premium/features/content-blocks/content-block-layer.php:214,
#: framework/features/header/items/account/options.php:247,
#: framework/premium/features/premium-header/items/content-block/config.php:4
#: framework/premium/static/js/blocks/ContentBlock.js:78
msgid "Content Block"
msgstr "Blok treści"

#: framework/premium/features/content-blocks.php:195,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:138,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:482,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:273
msgid "Add New"
msgstr "Dodaj nowy"

#: framework/premium/features/content-blocks.php:196
msgid "Add New Content Block"
msgstr "Dodaj nowy blok treści"

#: framework/premium/features/content-blocks.php:197
#: framework/premium/static/js/blocks/ContentBlock.js:89
msgid "Edit Content Block"
msgstr "Edytuj blok treści"

#: framework/premium/features/content-blocks.php:198
#: framework/premium/static/js/hooks/CreateHook.js:33
msgid "New Content Block"
msgstr "Nowy blok treści"

#: framework/premium/features/content-blocks.php:200
msgid "View Content Block"
msgstr "Zobacz blok treści"

#: framework/premium/features/content-blocks.php:201
msgid "Search Content Blocks"
msgstr "Szukaj bloku treści"

#: framework/premium/features/content-blocks.php:202,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:145,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:489,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:280
msgid "Nothing found"
msgstr "Nic nie znaleziono"

#: framework/premium/features/content-blocks.php:203,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:146,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:490,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:281
msgid "Nothing found in Trash"
msgstr "Nic nie znaleziono w koszu"

#: framework/premium/features/premium-footer.php:14,
#: framework/premium/features/premium-footer.php:28
msgid "Footer Menu 1"
msgstr "Menu stopki 1"

#: framework/premium/features/premium-footer.php:29,
#: framework/premium/features/premium-footer/items/menu-secondary/config.php:4
msgid "Footer Menu 2"
msgstr "Menu stopki 2"

#: framework/premium/features/premium-header.php:57
msgid "Header Menu 3"
msgstr "Menu nagłówka 3"

#: framework/premium/features/premium-header.php:202
msgid "Header Widget Area "
msgstr "Obszar widżetów nagłówka"

#: framework/extensions/trending/customizer.php:251,
#: framework/premium/features/premium-header.php:258,
#: framework/premium/features/premium-header.php:339,
#: framework/premium/features/premium-header.php:359,
#: framework/premium/features/premium-header.php:376,
#: framework/premium/features/socials.php:31,
#: framework/features/blocks/contact-info/options.php:121,
#: framework/features/blocks/contact-info/options.php:186,
#: framework/features/blocks/contact-info/options.php:249,
#: framework/features/blocks/contact-info/options.php:312,
#: framework/features/blocks/contact-info/options.php:375,
#: framework/features/blocks/contact-info/options.php:438,
#: framework/features/blocks/contact-info/options.php:501,
#: framework/features/blocks/search/options.php:86,
#: framework/premium/extensions/mega-menu/options.php:463,
#: framework/features/header/items/account/options.php:347,
#: framework/features/header/items/account/options.php:404,
#: framework/features/header/items/account/options.php:745,
#: framework/features/header/items/account/options.php:785,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:693,
#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:20,
#: framework/premium/features/premium-header/items/contacts/options.php:114,
#: framework/premium/features/premium-header/items/contacts/options.php:159,
#: framework/premium/features/premium-header/items/contacts/options.php:204,
#: framework/premium/features/premium-header/items/contacts/options.php:249,
#: framework/premium/features/premium-header/items/contacts/options.php:295,
#: framework/premium/features/premium-header/items/contacts/options.php:340,
#: framework/premium/features/premium-header/items/contacts/options.php:385,
#: framework/premium/features/premium-header/items/search-input/options.php:79,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:30,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:28,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:28
msgid "Icon"
msgstr "Ikonka"

#: framework/premium/features/premium-header.php:277,
#: framework/features/blocks/about-me/options.php:168,
#: framework/features/blocks/contact-info/options.php:538,
#: framework/features/blocks/share-box/options.php:122,
#: framework/features/blocks/socials/options.php:74,
#: framework/premium/features/premium-header/items/contacts/options.php:426
msgid "Icons Size"
msgstr "Rozmiar ikonek"

#: framework/premium/features/premium-header.php:289,
#: framework/premium/extensions/mega-menu/options.php:509
msgid "Icon Position"
msgstr "Pozycja ikony"

#: framework/premium/features/premium-header.php:296,
#: framework/premium/extensions/mega-menu/options.php:516,
#: framework/premium/extensions/shortcuts/customizer.php:886,
#: framework/features/header/items/account/options.php:561,
#: framework/features/header/items/account/options.php:1027,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:95,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:129,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:130
msgid "Left"
msgstr "Do lewej"

#: framework/premium/features/premium-header.php:297,
#: framework/premium/extensions/mega-menu/options.php:517,
#: framework/premium/extensions/shortcuts/customizer.php:887,
#: framework/features/header/items/account/options.php:562,
#: framework/features/header/items/account/options.php:1031,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:96,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:130,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:131
msgid "Right"
msgstr "Do prawej"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:14
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/index.js:56
msgid "Newsletter"
msgstr "Biuletyn"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:13,
#: framework/features/blocks/contact-info/options.php:39,
#: framework/premium/extensions/mega-menu/options.php:544,
#: framework/premium/extensions/shortcuts/customizer.php:1187,
#: framework/premium/extensions/shortcuts/customizer.php:1229,
#: framework/premium/extensions/shortcuts/customizer.php:1271,
#: framework/features/header/items/account/options.php:577,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:32,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:72,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:98,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:547,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:582,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:616,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:486,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:517,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:547
#: static/js/editor/blocks/breadcrumbs/Edit.js:54
#: static/js/editor/blocks/contact-info/Edit.js:107
msgid "Text"
msgstr "Tekst"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:19,
#: framework/features/blocks/about-me/options.php:67
msgid "You can add here some arbitrary HTML code."
msgstr "Możesz dodać tutaj dowolny kod HTML."

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:74
msgid "Container Style"
msgstr "Styl kontenera"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:82,
#: framework/premium/features/content-blocks/options/404.php:85,
#: framework/premium/features/content-blocks/options/archive.php:162,
#: framework/premium/features/content-blocks/options/header.php:103,
#: framework/premium/features/content-blocks/options/hook.php:212,
#: framework/premium/features/content-blocks/options/maintenance.php:82,
#: framework/premium/features/content-blocks/options/nothing_found.php:103,
#: framework/premium/features/content-blocks/options/single.php:94,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:22
msgid "Boxed"
msgstr "Opakowane"

#: framework/extensions/product-reviews/views/single-top.php:238
msgid "Specs"
msgstr "Szczegóły"

#: framework/extensions/newsletter-subscribe/helpers.php:147
msgid "Email address"
msgstr "Adres e-mail"

#: framework/features/header/modal/login.php:28,
#: framework/features/header/modal/register.php:53
msgid "Password"
msgstr "Hasło"

#: framework/features/header/modal/login.php:53
msgid "Remember Me"
msgstr "Zapamiętaj mnie"

#: framework/features/header/modal/login.php:57
msgid "Forgot Password?"
msgstr "Nie pamiętasz hasła?"

#: framework/features/header/modal/login.php:79
msgid "Log In"
msgstr "Zaloguj się"

#: framework/features/header/modal/login.php:23,
#: framework/features/header/modal/lostpassword.php:5
msgid "Username or Email Address"
msgstr "Nazwa użytkownika lub adres e-mail"

#: framework/features/header/modal/lostpassword.php:13
msgid "Get New Password"
msgstr "Zdobądź nowe hasło"

#: framework/features/header/modal/register.php:41
msgid "Username"
msgstr "Nazwa użytkownika"

#: framework/features/header/modal/register.php:106
msgid "Registration confirmation will be emailed to you."
msgstr "Potwierdzenie rejestracji zostanie wysłane na podany adres e-mail."

#: framework/features/header/modal/register.php:112
msgid "Register"
msgstr "Zarejestruj się"

#: framework/helpers/exts-configs.php:60
msgid "Connect your Adobe Fonts project and use the selected fonts throughout Blocksy and your favorite page builder."
msgstr "Połącz swój projekt Adobe Fonts i używaj wybranych czcionek w Blocksy i swoim ulubionym kreatorze stron."

#: framework/helpers/exts-configs.php:71
msgid "Inject custom code snippets throughout your website. The extension works globally or on a per post/page basis."
msgstr "Wstrzykuj niestandardowe fragmenty kodu w całej witrynie. Rozszerzenie działa globalnie lub dla każdego postu/strony."

#: framework/premium/extensions/code-snippets/extension.php:42,
#: framework/premium/extensions/code-snippets/extension.php:97,
#: framework/premium/extensions/code-snippets/extension.php:142
msgid "After body open scripts"
msgstr "After body open scripts"

#: framework/helpers/exts-configs.php:97
msgid "Upload an unlimited number of custom fonts or variable fonts and use them throughout Blocksy and your favorite page builder."
msgstr "Prześlij nieograniczoną liczbę niestandardowych czcionek lub zmiennych czcionek i używaj ich w Blocksy i swoim ulubionym kreatorze stron."

#: framework/helpers/exts-configs.php:109
msgid "Serve your chosen Google Fonts from your own web server. This will increase the loading speed and makes sure your website complies with the privacy regulations."
msgstr "Serwuj wybrane czcionki Google Fonts z własnego serwera internetowego. Zwiększy to szybkość ładowania i upewni się, że Twoja witryna jest zgodna z przepisami dotyczącymi prywatności."

#: framework/helpers/exts-configs.php:124
msgid "Create beautiful personalised menus that set your website apart from the others. Add icons and badges to your entries and even add Content Blocks inside your dropdowns."
msgstr "Twórz piękne, spersonalizowane menu, które wyróżni Twoją witrynę na tle innych. Dodawaj ikony i plakietki do swoich wpisów, a nawet dodawaj bloki treści do rozwijanych menu."

#: framework/premium/extensions/mega-menu/extension.php:160
msgid "Menu Item Settings"
msgstr "Ustawienia pozycji menu"