=== CookieYes - <PERSON><PERSON> Banner for Cookie Consent (Easy to setup GDPR/CCPA Compliant Cookie Notice) ===
Contributors: webtoffee, cookieyesdev
Donate link: https://www.cookieyes.com/ 
Tags: cookie notice, cookie consent, cookie banner, eu cookie law, cookieyes
Requires at least: 5.0.0
Tested up to: 6.8.1
Requires PHP: 5.6
Stable tag: 3.3.1
License: GPLv3
License URI: http://www.gnu.org/licenses/gpl-3.0.html

Easily set up cookie consent banner, notice, and policy pages for compliance with global cookie laws (GDPR, DSGVO, RGPD, CCPA/CPRA, etc).

== Description ==

The CookieYes plugin enables seamless cookie consent and GDPR compliance by integrating an intuitive cookie banner into your website. It offers full support for GDPR (DSGVO, RGPD) and CCPA/CPRA to easily obtain user consent for cookies, ensuring legal compliance.

Customizable cookie banner designs and options make configuring a GDPR-compliant cookie consent solution simple. The plugin also supports various other global privacy regulations beyond GDPR, including LGPD (Brazil), CNIL (France), PIPEDA (Canada), Law 25 (Quebec), POPIA (South Africa), nFADP (Switzerland), Privacy Act (Australia), PDPL (Saudi Arabia), PDPL (Argentina), PDPL (Andorra), DPA (Faroe Islands), and the California Consumer Privacy Act (CCPA/CPRA). Additionally, it's designed to align with significant state privacy laws in the US, such as VCDPA (Virginia), CPA (Colorado), CTDPA (Connecticut), and UCPA (Utah).

Our cookie plugin ranks among the top WordPress solutions for cookie compliance and is certified by Google, providing a comprehensive suite of features, including policy page generators.

== Key Features == 

- **Cookie consent banner**: The plugin will enable a cookie consent banner with Accept and Reject options.

- **Customization**: Customize the cookie notice style so it blends with your existing website: change the colours, fonts, styles, position on the page, and even how it behaves when you click “Accept All”.

- **Automatic scanning**: Single-click automatic scanning and categorization of cookies.

- **Cookie auto-blocking**: Automatically block non-essential cookies to ensure GDPR compliance.

- **Cookie list**: This lets you display the list of cookies neatly in a table on your Privacy & Cookies Policy page by using a shortcode. You can also add new cookies manually, edit existing ones (CookieID, Category, Description, Script URL Pattern), and delete cookies as needed.

- **Preference center**: Offer a second layer of control through a preference center within the banner. Allow site visitors to provide granular consent for specific cookie categories, giving them the choice to opt in or opt-out.

- **Revisit consent button**: Visitors can either permanently dismiss the cookie banner or access it later through a revisit consent button, which can be tailored in terms of colour and position.

- **Consent logging**: Log users' consent and export it into a CSV file.

- **CCPA/CPRA opt-out**: The plugin can be configured to have a CCPA/CPRA ‘Do Not Sell or Share My Personal Information’ control on the cookie notice.

- **Google Consent Mode support**: Implement Google Consent Mode v2 easily to keep ads effective.

- **Auto-translation**: Auto-translate the banner in 30+ languages as per the user’s browser language. Compatible with WPML and Polylang.

- **Policy generators**: Includes Cookie Policy and Privacy Policy generators.

- **Integration with CookieYes Web App**: Free connection with CookieYes web app to access advanced features (cookie scan, consent log, etc.) and manage all settings from the web app account.

Here is how you can integrate the CookieYes plugin with our web app:

https://www.youtube.com/watch?v=qw2iYti4aZ8

== Additional Features == 

- **Opt-In and Opt-Out banner**: Opt-in consent banner for GDPR & opt-out banner for CCPA/CPRA.
- **Footer link**: Add the "Do Not Sell or Share My Personal Information" link to your website footer using an HTML code.
- **Placement options**: Choose to display the cookie banner in either the header or footer of your website, based on your preference.
- **Integration compatibility**: Compatible with major caching servers and plugins.
- **Preview functionality**: Easily preview the cookie banner within the admin settings.


== The CookieYes GDPR Cookie Consent Plugin Uses The Following Cookie Only ==

- "cookieyes-consent" – CookieYes sets this cookie to remember users' consent preferences so that their preferences are respected on their subsequent visits to this site. It does not collect or store any personal information of the site visitors.


== Setup Guide ==

Our [WordPress cookie plugin user guide](https://www.cookieyes.com/documentation/install-cookieyes-wordpress-plugin/?utm_source=wordpress&utm_medium=plugin&utm_campaign=wordpress_page&utm_content=l_1&ref=WPCY "Installation and Setup guide") will help you set up the plugin on your website.


== Useful links ==

[Support](https://www.cookieyes.com/support/?utm_source=wordpress&utm_medium=plugin&utm_campaign=wordpress_page&utm_content=l_1&ref=WPCY)
[Plugin FAQ](https://www.cookieyes.com/documentation/wordpress-plugin-faq/?utm_source=wordpress&utm_medium=plugin&utm_campaign=wordpress_page&utm_content=l_1&ref=WPCY)
[Knowledge base](https://www.cookieyes.com/knowledge-base/?utm_source=wordpress&utm_medium=plugin&utm_campaign=wordpress_page&utm_content=l_1&ref=WPCY)
[GDPR cookie consent](https://cookieyes.com/blog/gdpr-cookie-consent/?utm_source=wordpress&utm_medium=plugin&utm_campaign=wordpress_page&utm_content=l_1&ref=WPCY)
[Cookie Law](https://cookieyes.com/blog/cookie-law/?utm_source=wordpress&utm_medium=plugin&utm_campaign=wordpress_page&utm_content=l_1&ref=WPCY)
[Cookie banner guide](https://www.cookieyes.com/blog/cookie-banner/?utm_source=wordpress&utm_medium=plugin&utm_campaign=wordpress_page&utm_content=l_1&ref=WPCY)


== CookieYes GDPR Cookie Consent plugin premium features ==

Access the following features by subscribing to a premium plan (Basic, Pro, or Ultimate) on the CookieYes web app account. [See all features and compare plans](https://www.cookieyes.com/pricing/?utm_source=wordpress&utm_medium=plugin&utm_campaign=wordpress_page&utm_content=l_1&ref=WPCY)

&#9989; **Increased pageviews limit**: Increase your pageviews limit, extending from 100,000 to unlimited. </li>
&#9989; **Enhanced scanning capacity**: Our tool can scan up to 8,000 pages in a single scan. </li>
&#9989; **GDPR/US laws template**: Offer opt-in consent banner for GDPR and opt-out banner for US privacy laws such as CCPA/CPRA. </li>
&#9989; **Global Privacy Control**: Respect the user’s GPC setting if enabled. </li>
&#9989; **TCF v2.2 integration**: We seamlessly integrate with TCF v2.2, the IAB Europe Consent Framework for Publishers. Our registered CMP ID is 401. </li>
&#9989; **Advanced customization**: Customize your banner with advanced design options such as custom CSS to suit your brand's identity. </li>
&#9989; **Popup layout**: Add a cookie notification in the form of a ‘Popup’. </li>
&#9989; **Geo-targeting banner**: Target your cookie banner based on location, including GDPR banner for Worldwide, EU & UK, and Other countries, as well as CCPA/CPRA banner for Worldwide, United States, and Other countries. </li>
&#9989; **Scan behind login**: You can scan behind login pages to ensure comprehensive coverage. </li>
&#9989; **Scheduled scans**: Set up scans on a schedule that works for you. </li>
&#9989; **Custom branding**: Add your brand logo to the cookie banner for a personalized touch. </li>

= NOTE: INSTALLING THIS PLUGIN ALONE DOES NOT MAKE YOUR SITE GDPR AND CCPA COMPLIANT. SINCE EACH SITE USES DIFFERENT COOKIES, YOU NEED TO ENSURE YOU HAVE THE NECESSARY CONFIGURATIONS IN PLACE WITH A SUPPORTING COOKIE NOTICE. = 


== Frequently Asked Questions ==

= The cookie notice header doesn’t work on my browser =
Please report a bug on the support forum. Make sure to include the following information:

* Your URL (We need it to help you!)
* WordPress version (found in the bottom right-hand corner of the dashboard)
* Browser where the issue is observed (e.g., Firefox, Chrome, IE)
* Describe the problem. The more information you provide, the faster we can respond.


= What does this plugin do? =
This WordPress cookie plugin will:

* Add a cookie banner to the top/bottom of all pages on your website, notifying visitors that you have a clearly defined privacy and cookie policy.
* Allow you to record which cookies your site uses and neatly display a list of these cookies. You can add this list to your Privacy Policy page or Cookie Policy page using a shortcode.
Note: If you connect your site to the CookieYes web app, you have to replace the shortcode with an HTML code.

= What is a cookie notice? =
A cookie notice, also known as a cookie banner, is a message informing visitors about the site's use of cookies. It typically explains what cookies are, why they are used, and how they affect user privacy. It often includes options for users to consent to or manage cookie preferences, ensuring compliance with privacy laws like the GDPR and CCPA. The goal is to provide transparency and allow users to make informed decisions about their data.

= Do visitors now have to accept/refuse cookies in order to use websites? =
No, Only the ‘Necessary’ cookies will be loaded until the user gives consent. 

= Does this plugin stop cookies from being stored? =
Visitors can reject the category of cookies they don't want to be installed. In such cases, all cookies in that category will be blocked for the user.

But if another plugin adds scripts or places a cookie, we have no control. We have no way of knowing what plugins you use or how they work, so blocking/deleting cookies on a WordPress website/blog might possibly/probably break your site. Larger websites with substantial budgets may well invest in such technology, but this plugin aims to help you provide a certain level of compliance without taking drastic action.

= Does this plugin guarantee that I comply with GDPR? =
This plugin can be used to comply with the cookie consent requirements of major data privacy laws such as the GDPR. However, you need to assess your website's use of cookies to confirm that it aligns with GDPR guidelines. If you are seeking specialist legal advice regarding your website, we suggest that you consult a lawyer to understand how you can achieve full GDPR compliance.

= What is GDPR? =
GDPR, or the General Data Protection Regulation is a comprehensive data protection law in the EU aimed at safeguarding EU individuals' privacy and regulating how organizations handle their personal data. It is also known as DatenSchutz-Grundverordnung or DSGVO in German.

= What is CCPA? =
CCPA is the California Consumer Privacy Act, a law granting California residents more control over their personal information held by businesses. Businesses must be transparent, offer opt-out options, and ensure data security.

= What is GDPR cookie consent? =
GDPR requires websites to obtain explicit consent from users before storing or accessing cookies on their devices. This is known as GDPR cookie consent.

GDPR mandates cookie consent because cookies allow websites to collect data and track users across multiple sessions. Since this impacts user privacy, consent is required before enabling cookies, especially non-essential cookies like analytics, advertisements, etc.

= What is CCPA compliance? =
CCPA Compliance refers to adherence to the California Consumer Privacy Act (CCPA), a law designed to protect the privacy rights of California residents. It mandates that businesses handling the personal information of California residents provide certain rights and protections to those individuals. Compliance involves ensuring that businesses meet the CCPA's requirements regarding data collection and processing.

= Does the plugin block cookies before consent? =
Non-essential cookies are automatically blocked until explicit consent is given by users. Essential or strictly necessary cookies are allowed by default to ensure site functionality.

= Can I export or log user consent as per GDPR requirements? =
Yes, you can export a log of all user cookie consent and preferences related to cookies. This helps meet GDPR compliance needs.

= Can the plugin help me generate a GDPR-compliant cookie policy? =
Yes, we provide an easy wizard to customize and generate a GDPR-compliant cookie policy based on your scanned site cookies.

= Does CookieYes integrate with WP Consent API? =
Yes, CookieYes integrates with [WP Consent API](https://wordpress.org/plugins/wp-consent-api). This allows for efficient sharing of user consent data with other compatible plugins, enhancing privacy compliance and streamlining cookie consent management on your WordPress site.

== Installation ==

= Automatic installation =

- Go to **Plugins** from your WordPress dashboard and select **Add New Plugin**.

- Search for “CookieYes” and **Install Now**.

- Click **Activate** to enable the plugin.

= Manual installation =

- Download the CookieYes plugin zip file from the WordPress.org page.

- Go to **Plugins** from your WordPress dashboard and select **Add New Plugin**.

- Click **Upload Plugin** and choose the downloaded zip file.

- Click **Install Now** and after installation, click **Activate Plugin**.

Alternatively, you can upload the zip using the FTP application. Extract the zip file and upload the folder to your **/wp-content/plugins/** directory. Once uploaded, activate the plugin from your WordPress dashboard.

= To connect the plugin to the CookieYes web app: =

**_(Please note that the Web App integration is optional. You can still use the plugin without connecting it to the app.)_**

- After activating the plugin, click **CookieYes** on your WordPress dashboard menu.

- Click **Connect to a new account**. You will be prompted to sign up for a new account or log in if you already have one.

= Plugin updates =

Every time we update the plugin, you will see a notification on the installed plugins page. You can directly update the plugin from the WordPress dashboard. We recommend keeping the plugin updated to avail new functionalities and security features.


== Screenshots ==

1. Cookie consent banner on the user-end
2. Cookie preference center on the user-end
3. CookieYes GDPR cookie consent WordPress plugin - Admin panel
4. Cookie banner settings for cookie compliance
5. Manage cookies for cookie compliance
6. Add new cookie
7. Languages for cookie banner auto-translation
8. Edit cookie banner content in multiple languages
9. Privacy policy generator and cookie policy generator

== Changelog ==

= 3.3.1 =

[Add] - Added a promotional banner for the AccessYes widget inside the plugin dashboard for non-connected users.  
[Add] - Added an AccessYes widget section inside the plugin dashboard for connected users.

= 3.3.0 =

[Compatibility] - Tested OK with WordPress version 6.8.1
[Add] - Connection nudges added to the plugin page and WordPress admin dashboard.  
[Add] - Opt-out trial flow implemented for free users.  
[Enhancement] - Accessibility improvements.  
[Enhancement] - Improved plugin security.  
[Fix] - Minor bug fixes.

= 3.2.10 = 

[Compatibility] - Tested OK with WordPress version 6.8
[Enhancement] - Improved UI in plans page.

= 3.2.9 =

[Compatibility] – Tested OK up to WordPress version 6.7.2.  
[Enhancement] – UI/UX improvements in the Dashboard for both web app-connected and non-connected users.  
[Add] – Display of warning banner for plugin-connected websites.  


= 3.2.8 =

[Compatibility] – Tested OK with WordPress version 6.7
[Add] - Support Custom name for dataLayer
[Fix] - Minor bug fixes.

= 3.2.7 =

[Add] - Implemented exit intent popup.
[Tweak] - Declared compliance with WP Consent API.
[Enhancement] - UI/UX improvements.
[Fix] - Minor bug fixes.

= 3.2.6 =

[Compatibility] – Tested OK with WordPress version 6.6
[Add] - WP Consent API integration.
[Add] - Option to enable/disable the close button on US state laws banner.

= 3.2.5 =

[Add] - Google Consent Mode implementation via UI.
[Fix] - Minor bug fixes.

= 3.2.4 =

[Fix] - Minor bug fixes.

= 3.2.3 =

[Add] - An option to migrate to the new UI is added to the legacy UI.
[Fix] - Minor bug fixes.

= 3.2.2 =

[Compatibility] – Tested OK with WordPress version 6.5
[Enhancement] - Cookie banner auto-translation for 40+ languages.
[Tweak] - Limited the total languages to two for stand-alone plugin.
[Fix] - Minor bug fixes.

= 3.2.1 =

[Fix] - Confirmation page missing while connecting to webapp.
[Enhancement] - Show percentage values in the consent log chart tooltip.

= 3.2.0 =

[Enhancement] - UI/UX improvements.
[Add] - Chart to display pageviews data for connected users from past 7 days.
[Tweak] - Limited consent log chart data to past 7 days.
[Fix] - Minor bug fixes.

= 3.1.8 =

[Add] - Option to choose sidebar for preference center layout.
[Add] - Option to show categories on first layer.
[Fix] - Minor bug fixes.

= 3.1.7 =

[Compatibility] - Tested OK with WordPress version 6.4
[Tweak] - Changed tooltip of revisit consent button to "Consent preferences"
[Fix] - Minor bug fixes.

= 3.1.6 =

[Fix] - Review banner is reappearing for some users.

= 3.1.5 =

[Enhancement] - Increased Consent expiration upper limit to 9999.
[Fix] - UI issues on Collapse menu.
[Fix] - Translation issue on editing content from Languages tab.

= 3.1.4 = 

[Enhancement] - WCAG/AA compliance for cookie banner.
[Fix] - Minor UI changes and bug fixes.

= 3.1.3 = 

Tested OK with WordPress version 6.3

= 3.1.2 = 

[Fix] - UI/UX improvements
[Fix] - Updated email not reflected under "Site Settings" tab for connected users
[Fix] - Cookie notice message not updated while switching the consent template

= 3.1.1 = 

[Fix] - Polish characters were not displayed correctly in banner 

= 3.1.0 = 

PHP 8.2 compatibility

= 3.0.9 = 

Tested OK with WordPress version 6.2

= 3.0.8 = 

[Enhancement] - Support for CPRA ( Do Not Sell or Share My Personal Information )
[Enhancement] - Option to edit show more/less texts
[Enhancement] - Compatibility with Bedrock WordPress boilerplate

= 3.0.7 = 

[Enhancement] - New CCPA Opt-out center
[Fix] - Not able to change the Optout preference center text colour
[Fix] - Not able to disable the "Reject All" button from preference center

= 3.0.6 = 

[Enhancement] - Clickable video placeholders
[Fix] - Revisit consent button colour is not reset on colour scheme changes

= 3.0.5 = 

[Enhancement] - Reduced revisit consent button size

= 3.0.4 = 

[Fix] - Table existing check failure on the IIS web server
[Enhancement] - Added option to reset the plugin settings & switch to the new plugin UI
[Enhancement] - Added width and height attribute to all the img tags on the cookie banner

= 3.0.3 = 

* [Enhancement] - HTML support for banner and cookie descriptions
* [Fix] - Style conflict with Elementor theme templates

= 3.0.2 = 

* [Enhancement] - Added a custom method to revisit consent outside the banner.
* [Enhancement] - Updated Chart.js library
* [Enhancement] - Enabled automatic updates for the plugin.

= 3.0.1 = 

* [Enhancement] - Added error notice to the plugin if necessary tables are not created.

= 3.0.0 = 

* UI/UX improvements
* New cookie consent banner design (compliant with WCAG guidelines)
* Advanced banner customization options (GDPR/CCPA cookie banner templates, set consent expiration period, enable/disable prior consent, show/hide categories on the banner, new banner layouts, advanced content customization, light/dark/custom colour scheme, etc.)
* Banner preview in the customizer
* Pre-built translations for multiple languages
* Privacy Policy Generator
* Access to additional free features with web app connection (cookie scan, consent log, etc.)

= 2.1.3 =

* [Enhancement] - Revisit consent will be enabled by default
* [Enhancement] - Update in policy generator

= 2.1.2 =

* Fix - Accept/Reject button is not working as expected with some themes.

= 2.1.1 =

* Fix - Compatibility issue with PHP 7.3.0 and below.

= 2.1.0 =

* Improved: Validation & escaping for Security Enhancement.


= 2.0.9 =

* Fix - Removed cache flush helper functions
* Tested ok with WordPress version 5.9

= 2.0.8 =

* Fix - Compatibility issue with Wordpress 4.7.0 and below.

= 2.0.7 =

* Fix - Conflict with W3 Total Cache plugin
* Fix - Accessibility issue ( Hidden element has a focusable content )
* Fix - Opacity of the cookie banner is set to 0.1 after closing the settings popup.


= 2.0.6 =

* Fix: Removed extract() function from the shortcodes module to improve security.

= 2.0.5 =

* Enhancement: Disable cookie bar on page builders.
* Tested ok with Wordpress version 5.8

= 2.0.4 =

* Bug fix: Do not sell option appears before Accept all button option on the admin settings.
* Tested ok with Wordpress version 5.6
* Enhancement: Added escaping to input attributes.

= 2.0.3 =

* Enhancement - Default cookie banner message update.

= 2.0.2 =

* Enhancement - Added reject button by default
* Enhancement - Help text improvements

= 2.0.1 =

* Enhancement - New shortcode [cookie_accept_all] to add accept all button to the banner
* Enhancement - MonsterInsights integration
* Enhancement - New filter `wt_cli_set_secure_cookies` to set plugin cookies as secure
* Fix - Site map issue with cookie category
* Fix - Cookie categories are not ordered properly in secondary languages

= 2.0.0 =

* Introduced website cookie scanner
* Introduced dynamic cookie categories

= 1.9.5 =

* Tested ok with Wordpress version 5.6

= 1.9.4 =

* Renamed show again tab to revisit consent
* New shortcode `wt_cli_manage_consent` option for revisit consent

= 1.9.3 =

* Fix: Editors unable to access the dashboard due to recent security update

= 1.9.2 =

* Improvement: Option to change the title for necessary and non-necessary categories
* Improvement: Added support for Official Facebook Pixel, Smash Balloon Instagram Feed & Twitter Feed plugins.

= 1.9.1 =

* Fix - Conflict with the Divi page builder

= 1.9.0 =

* Fix - Javascript error on IE after 1.8.9 update
* Fix - Conflict with the theme customizer
* Tested ok with Wordpress version 5.5
* Updated translation files for Finnish

= 1.8.9 =

* Included support for CCPA ( Do not sell personal information )
* Added javascript-based solution for running third-party scripts
* Added support for external integration to modify the default behaviour of the plugin

= 1.8.8 =

* Introduced 'wt_cli_enable_cache_flush' filter to enable / disable cache flushing
* Cookie elements are excluded from the Google search engine indexing  (<! - googleoff: all ->)

= 1.8.7 =

* Tested ok with Wordpress version 5.4.0

= 1.8.6 =

* [Bug fix] Privacy overview is not visible if the content is in Cyrillic alphabets
* [Improvements] Fixed compatibility issues with Decorator – WooCommerce Email Customizer

= 1.8.5 =

* Bug fix w.r.t the plugin release version 1.8.4

= 1.8.4 =

* Defaulted privacy overview heading tag to H4
* Introduced 'wt_cli_change_privacy_overview_title_tag' filter to change privacy overview heading tag
* Introduced 'wt_cli_add_custom_cookie_categories_name' filter to change cookie category names

= 1.8.3 =

* PHP 7.4 compatibility
* Security fix

= 1.8.2 =

* Tested ok with WP 5.3

= 1.8.1 =

* Added option in admin to modify the default button state of the non-necessary cookie category
* [Bug fix] Show more / show less issue when custom text editor is used in the privacy overview content.
* Updated translations
* Read me updates

= 1.8.0 =

* [Bug fix] Warning fixes

= 1.7.9 =

* Fixed W3C Validation issues
* [Bug fix] Hide non-necessary category on cookie settings popup if it is not enabled
* [Bug fix] Translation issue with category cookies

= 1.7.8 =

* Fixed issues with translations
* [user_consent_state] added missing space in user consent states
* Fixed issues with web accessibility

= 1.7.7 =

* Introduced new cookie settings popup option
* Added a shortcode [cookie_settings] to control settings
* Option to add descriptions for both necessary and non-necessary cookies
* Tested ok with WP 5.2
* [Bug fix] Responsive styling issues for cookie audit table winter theme


= 1.7.6 =

* Updated translation files for German, Danish, French, Dutch & Spanish
* Added support for Portuguese
* [Bug fix] [cookie_audit] shortcode conflict with heading

= 1.7.5 =

* Defaulted the minimize cookie bar option for Read more link to disabled state.
* The cookie message bar has been defaulted to include Reject option in addition to Accept.

= 1.7.4 =

* Cookie policy generator
* Added a shortcode [user_consent_state]
* Updated shortcode [cookie_audit] with option to configure columns
* Margin option added in all button shortcodes
* Introduced an option to hide cookie bar on policy page
* Tested ok with WP 5.1

= 1.7.3 =

* Translation updated - Danish, French, German
* Readme updates
* Minor UI changes

= 1.7.2 =

* Tested ok with Wordpress version 5.0.2
* Compatibility added for Pixelyoursite plugin


= 1.7.1 =

* Cookie notice as `Widget` option added

= 1.7.0 =

* Bug: Submenu sorting issue for custom user roles

= 1.6.10 =

* Bug: submenu sorting issue for non administrator accounts
* Bug: Flashing the cookie bar on page load.

= 1.6.9 =

* New enclosing shortcode added for better control of third party content. [cookie_after_accept]..content goes here..[/cookie_after_accept]
* Basic support included for Litespeed cache, WP Super Cache, Site ground cache, Endurance Cache, WP Fastest Cache.
* Option to choose from the built-in pages for 'Read More' link.

= 1.6.8 =

* Corrected German translations (thanks to Max B)
* Bug in 'Show cookie bar on selected pages' Filter fixed
* W3TC page cache compatibility added

= 1.6.7 =

* In the previous update a title with default text ‘This website uses cookies‘ would get added to the existing cookie bar. In this update this title will get removed to that it is blank by default. In case you need this to be added again you need to explicitly add suitable content for the title.
* Addressed the conflict with [cookie_reject] shortcode for previous users who had the reject button option enabled.
* Optimized the plugin js and css assets to render only in plugin pages.

= 1.6.6 =

* Improved UI.
* Enhancements to cookie message bar - included title and option for overlay layout.

= 1.6.5 =

* Filter added. (filter to edit audit table head)

= 1.6.4 =

* Class change.
* Bug fix.

= 1.6.3 =

* Styling issues with winter style of cookie audit table shortcode fixed.
* Functionality issues with reject button on open URL fixed.
* Text corrections

= 1.6.2 =

* Issue with reject button colour fixed.

= 1.6.1 =

* Filter to display cookie bar only on selected pages.

= 1.6.0 =

* Issue with dual accept button.

= 1.5.9 =

* Issue with the plugin interfering with breadcrumb fixed.

= 1.5.8 =

* Support for underline tag in the message bar.

= 1.5.7 =
* Danish translation added

= 1.5.6 =
* Changed Reject button colour for Open URL and Close Header options.
* Padding for message header
* Audit table mobile view compatible

= 1.5.5 =
* GDPR compliance updates.

= 1.5.4 =
* Tested OK with WordPress 4.9.5
* GDPR compliance updates.

= 1.5.3 =
* Bug fix: Buttons now handle apostrophes correctly
* Bug fix: Added <tr> to table head for [cookie_audit] table for W3 Validator (thanks to davidebabylonia for finding and suggesting the solution)

= 1.5.2 =
* Minor bug fix: adds version number to cli-admin.css

= 1.5.1 =
* Bug fix: HTML5 validation fix for shortcode links (thanks to davidebabylonia)
* Added JavaScript version number for greater compatibility

= 1.5 =
* Major update: the cookie bar is now inserted into the page via wp_footer rather than using jQuery (for better performance and greater browser compatibility)
* Update: if the cookie bar is in the header, there is now an option to fix the bar to the header using position:fixed

= 1.4.3 =
* jQuery 'reload' bug fix on accept

= 1.4.1 =
* Bug fix: fixed browser compatibility issue in cookielawinfo.js

= 1.4 =
* New feature: accept policy & close cookie bar on scroll (an option available under Italian law)
* New feature: if cookie bar is set to header it is fixed to the top of the screen (using CSS "position:fixed")

= 1.3.2 =
* Bug fix: changed filename from wpml.xml to wpml-config.xml

= 1.3.1 =
* Fixing header/SVN tagging issue

= 1.3 =
* Adding WPML support (wpml.xml)

= 1.2.2 =
* Bug fix: removed extra '{' from cli-tables.css
* Bug fix: fixed cookie_button shortcode text bug
* Modified help pages, text and contact information

= 1.2.1 =
* Added plugin settings page link to plugins.php
* Bug fix: custom posts bug affecting some other plugins

= 1.2 =
* Removed 3rd party JavaScript "jQuery.cookie" which can cause issues with certain versions of Apache server
* Added native JavaScript cookie getter/setter
* Removed JavaScript debug routine
* Replaced JavaScript 'eval' with JSON.parse() for improved security: requires IE8+ (all other browsers fine though- who would have thought?)
* Improved JavaScript performance and compatibility by removing global variables and running as inline function

= 1.1 =
* New feature: auto-hide cookie bar after (configurable) delay
* New feature: added responsive design to cookie audit table (thanks to Mark Wiltshire)
* Upgrade: now using WP3.5 colour picker
* Bug fix: Cookie Audit table now shows maximum of 50 posts (was 10, which was a bit restrictive)
* Bug fix: Cookie Law Info now only visible to admins
* Bug fix: fixed typo on Dashboard help section for the [delete_cookies] shortcode
* Bug fix: fixed "invalid header" bug
* Performance enhancement: removed jQueryUI from admin panel and added custom (slimline) code
* Performance enhancement: compressed CSS a bit
* Performance enhancement: cookie audit CSS is now only downloaded on the page on which it is needed

= 1.0.3 =
* Fixed bug where JavaScript generated an http 404 error.

= 0.9 =
* Improved design & appearance
* Cookie Law Info bar can be shown in header or footer
* Customise fonts
* New animations on page load / close header
* Option to switch off "show again tab" - or position it anywhere horizontally in the header or footer. Additionally the styling has been improved and you are now able to customise the message.
* New button styles: better styling and effects, greater control
* Customise your message using HTML and 5 shortcodes with quick-start default options
* "Cookie Audit" module - document the cookies your site uses then display them in your privacy policy via a shortcode
* Enhanced dashboard
* Enhanced help section
* Refactored codebase, improved jQuery performance
* Bugs fixed: no more slashes in Message Box

= 0.8.3 =
* First public release.

== Upgrade Notice ==

= 3.3.1 =

[Add] - Added a promotional banner for the AccessYes widget inside the plugin dashboard for non-connected users.  
[Add] - Added an AccessYes widget section inside the plugin dashboard for connected users.