/*! elementor - v3.30.0 - 01-07-2025 */
/*! For license information please see checklist.min.js.LICENSE.txt */
(()=>{var e={92584:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{QueryClient:()=>l.QueryClient,QueryClientProvider:()=>l.QueryClientProvider,createQueryClient:()=>createQueryClient,useInfiniteQuery:()=>l.useInfiniteQuery,useMutation:()=>l.useMutation,useQuery:()=>l.useQuery,useQueryClient:()=>l.useQueryClient}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(51688),l=r(51688);function createQueryClient(){return new c.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,refetchOnReconnect:!1}}})}},73921:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function useAjax(){var e=(0,u.useState)(null),t=(0,i.default)(e,2),r=t[0],n=t[1],s="initial",c={status:s,isComplete:!1,response:null},l=(0,u.useState)(c),p=(0,i.default)(l,2),f=p[0],d=p[1],h={reset:function reset(){return d(s)}},y=function(){var e=(0,a.default)(o.default.mark((function _callee(e){return o.default.wrap((function _callee$(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise((function(t,r){var n=new FormData;if(e.data){for(var o in e.data)n.append(o,e.data[o]);e.data.nonce||n.append("_nonce",elementorCommon.config.ajax.nonce)}var s=_objectSpread(_objectSpread({type:"post",url:elementorCommon.config.ajax.url,headers:{},cache:!1,contentType:!1,processData:!1},e),{},{data:n,success:function success(e){t(e)},error:function error(e){r(e)}});jQuery.ajax(s)})));case 1:case"end":return t.stop()}}),_callee)})));return function runRequest(t){return e.apply(this,arguments)}}();return(0,u.useEffect)((function(){r&&y(r).then((function(e){var t=e.success?"success":"error";d((function(r){return _objectSpread(_objectSpread({},r),{},{status:t,response:null==e?void 0:e.data})}))})).catch((function(e){var t,r=408===e.status?"timeout":null===(t=e.responseJSON)||void 0===t?void 0:t.data;d((function(e){return _objectSpread(_objectSpread({},e),{},{status:"error",response:r})}))})).finally((function(){d((function(e){return _objectSpread(_objectSpread({},e),{},{isComplete:!0})}))}))}),[r]),{ajax:r,setAjax:n,ajaxState:f,ajaxActions:h,runRequest:y}};var o=n(r(61790)),s=n(r(85707)),a=n(r(58155)),i=n(r(18821)),u=r(41594);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){(0,s.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}},50288:(e,t,r)=>{"use strict";var n=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&{}.hasOwnProperty.call(e,a)){var i=s?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(n,a,i):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(41594)),a=n(r(10906)),i=r(86956),u=r(92584),c=r(54142),l=n(r(50003)),p=r(20244);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}t.default=function App(){var e=elementorCommon.config.isRTL,t=(0,u.useQuery)({queryKey:["steps"],queryFn:p.fetchSteps,gcTime:0,enabled:!1}),r=t.error,n=t.data,o=t.refetch,f=(0,u.useQuery)({queryKey:["statusData"],queryFn:p.fetchUserProgress,gcTime:0,enabled:!1}),d=f.error,h=f.data,y=f.refetch,b=function fetchData(){o(),y()};return(0,s.useEffect)((function(){return b(),(0,c.__privateListenTo)((0,c.commandEndEvent)("document/save/save"),(function(e){var t,r=e.args;"kit"===(null==r||null===(t=r.document)||void 0===t||null===(t=t.config)||void 0===t?void 0:t.type)&&b()}))}),[]),d||!h||r||null==n||!n.length?null:s.default.createElement(i.DirectionProvider,{rtl:e},s.default.createElement(i.ThemeProvider,{colorScheme:"light"},s.default.createElement(l.default,{steps:(0,a.default)(n),userProgress:h})))}},54871:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(41594)),s=n(r(61790)),a=n(r(85707)),i=n(r(58155)),u=r(86956),c=r(12470),l=n(r(62688)),p=r(20244),f=r(55041),d=f.STEP.IS_MARKED_COMPLETED,h=f.STEP.IS_ABSOLUTE_COMPLETED,y=f.STEP.IS_IMMUTABLE_COMPLETED,b=f.MIXPANEL_CHECKLIST_STEPS.DONE,m=f.MIXPANEL_CHECKLIST_STEPS.UNDONE,g=f.MIXPANEL_CHECKLIST_STEPS.ACTION,v=f.MIXPANEL_CHECKLIST_STEPS.UPGRADE,O=function ChecklistCardContent(e){var t=e.step,r=e.setSteps,n=t.config,l=n.id,O=n.description,_=n.learn_more_url,P=n.learn_more_text,E=n.image_src,w=n.promotion_data,S=w?(null==w?void 0:w.text)||(0,c.__)("Upgrade Now","elementor"):t.config.cta_text,j=w?w.url:t.config.cta_url,C=t[h],x=t[y],M=t[d],R=!C&&!x&&!w,T=function(){var e=(0,i.default)(s.default.mark((function _callee(){return s.default.wrap((function _callee$(e){for(;;)switch(e.prev=e.next){case 0:if(w?(0,p.addMixpanelTrackingChecklistSteps)(t.config.id,v):(0,p.addMixpanelTrackingChecklistSteps)(t.config.id,g),elementor&&f.STEP_IDS_TO_COMPLETE_IN_EDITOR.includes(l)&&f.PANEL_ROUTES[l]){e.next=3;break}return e.abrupt("return",window.open(j,"_blank"));case 3:return e.next=5,$e.run("panel/global/open");case 5:$e.route(f.PANEL_ROUTES[l]);case 6:case"end":return e.stop()}}),_callee)})));return function redirectHandler(){return e.apply(this,arguments)}}(),k=function(){var e=(0,i.default)(s.default.mark((function _callee2(){var e;return s.default.wrap((function _callee2$(r){for(;;)switch(r.prev=r.next){case 0:return e=M,M?(0,p.addMixpanelTrackingChecklistSteps)(t.config.id,m):(0,p.addMixpanelTrackingChecklistSteps)(t.config.id,b),r.prev=2,Q(d,!e),r.next=6,(0,p.updateStep)(l,(0,a.default)({},d,!e));case 6:r.next=11;break;case 8:r.prev=8,r.t0=r.catch(2),Q(d,e);case 11:case"end":return r.stop()}}),_callee2,null,[[2,8]])})));return function toggleMarkAsDone(){return e.apply(this,arguments)}}(),Q=function updateStepsState(e,n){r((function(r){return r.map((function(r){return(0,p.getAndUpdateStep)(t.config.id,r,e,n)}))}))};return o.default.createElement(u.Card,{elevation:0,square:!0,"data-step-id":l},o.default.createElement(u.CardMedia,{image:E,sx:{height:180}}),o.default.createElement(u.CardContent,null,o.default.createElement(u.Typography,{variant:"body2",color:"text.secondary",component:"p"},O+" ",o.default.createElement(u.Link,{href:_,target:"_blank",rel:"noreferrer",underline:"hover",color:"info.main",noWrap:!0},P))),o.default.createElement(u.CardActions,null,R?o.default.createElement(u.Button,{size:"small",color:"secondary",variant:"text",onClick:k},M?(0,c.__)("Unmark as done","elementor"):(0,c.__)("Mark as done","elementor")):null,o.default.createElement(u.Button,{color:w?"promotion":"primary",size:"small",variant:"contained",onClick:T},S)))};t.default=O;O.propTypes={step:l.default.object.isRequired,setSteps:l.default.func.isRequired}},8762:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(41594)),s=n(r(85707)),a=n(r(62688)),i=n(r(54871)),u=r(86956),c=r(44048),l=r(20244),p=r(55041);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){(0,s.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var f=p.STEP.PROMOTION_DATA,d=p.MIXPANEL_CHECKLIST_STEPS.TITLE,h=p.MIXPANEL_CHECKLIST_STEPS.ACCORDION_SECTION;function CheckListItem(e){var t=e.expandedIndex,r=e.setExpandedIndex,n=e.setSteps,s=e.index,a=e.step,p=s===t?{transform:"rotate(180deg)"}:{},y=(0,l.isStepChecked)(a),b=a.config[f];return o.default.createElement(o.default.Fragment,null,o.default.createElement(u.ListItemButton,{onClick:function handleExpandClick(){(0,l.addMixpanelTrackingChecklistSteps)(a.config.id,d,h),r(s===t?-1:s)},"data-step-id":a.config.id,dense:!0},o.default.createElement(u.ListItemIcon,null,o.default.createElement(u.Checkbox,{"data-is-checked":y,icon:o.default.createElement(c.RadioButtonUncheckedIcon,null),checkedIcon:o.default.createElement(c.CircleCheckFilledIcon,{color:"primary"}),edge:"start",checked:y,tabIndex:-1,inputProps:{"aria-labelledby":a.config.title}})),o.default.createElement(u.ListItemText,{primary:a.config.title,primaryTypographyProps:{variant:"body2"}}),b?function getUpgradeIcon(){return"default"===(null==b?void 0:b.icon)?o.default.createElement(c.UpgradeIcon,{color:"promotion",sx:{mr:1}}):o.default.createElement(u.SvgIcon,{color:"promotion",sx:{mr:1}},o.default.createElement("img",{src:null==b?void 0:b.icon,alt:b.iconAlt||""}))}():null,o.default.createElement(c.ChevronDownIcon,{sx:_objectSpread(_objectSpread({},p),{},{transition:"300ms"})})),o.default.createElement(u.Collapse,{in:s===t},o.default.createElement(i.default,{step:a,setSteps:n})))}t.default=CheckListItem;CheckListItem.propTypes={step:a.default.object.isRequired,expandedIndex:a.default.number,setExpandedIndex:a.default.func.isRequired,setSteps:a.default.func.isRequired,index:a.default.number.isRequired}},19070:(e,t,r)=>{"use strict";var n=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&{}.hasOwnProperty.call(e,a)){var i=s?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(n,a,i):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(41594)),a=n(r(18821)),i=r(86956),u=n(r(8762)),c=n(r(62688)),l=n(r(26917)),p=r(20244);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var f=function ChecklistWrapper(e){var t=e.steps,r=e.setSteps,n=e.isMinimized,o=(0,s.useState)(-1),c=(0,a.default)(o,2),f=c[0],d=c[1],h=t.filter(p.isStepChecked).length===t.length;return s.default.createElement(i.Box,{sx:{transition:"400ms",maxHeight:n?0:"645px"}},s.default.createElement(i.List,{component:"div",sx:{py:0}},t.map((function(e,t){return s.default.createElement(s.Fragment,{key:t},t?s.default.createElement(i.Divider,null):null,s.default.createElement(u.default,{step:e,setSteps:r,setExpandedIndex:d,expandedIndex:f,index:t}))}))),h?s.default.createElement(l.default,null):null)};t.default=f;f.propTypes={steps:c.default.array.isRequired,setSteps:c.default.func.isRequired,isMinimized:c.default.bool.isRequired}},50003:(e,t,r)=>{"use strict";var n=r(62688),o=r(96784),s=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=s(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&{}.hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(n,a,i):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(41594)),i=o(r(61790)),u=o(r(85707)),c=o(r(58155)),l=o(r(18821)),p=o(r(35843)),f=o(r(19070)),d=r(86956),h=r(55041),y=r(20244);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var b=h.USER_PROGRESS.IS_POPUP_MINIMIZED,m=function Checklist(e){var t=(0,a.useState)(e.steps),r=(0,l.default)(t,2),n=r[0],o=r[1],s=(0,a.useState)(!!e.userProgress[b]),h=(0,l.default)(s,2),m=h[0],g=h[1],v=function(){var e=(0,c.default)(i.default.mark((function _callee(){var e;return i.default.wrap((function _callee$(t){for(;;)switch(t.prev=t.next){case 0:return e=m,t.prev=1,g(!e),t.next=5,(0,y.updateUserProgress)((0,u.default)({},b,!e));case 5:t.next=10;break;case 7:t.prev=7,t.t0=t.catch(1),g(e);case 10:case"end":return t.stop()}}),_callee,null,[[1,7]])})));return function toggleIsMinimized(){return e.apply(this,arguments)}}();return(0,a.useEffect)((function(){o(e.steps)}),[e.steps]),a.default.createElement(d.Paper,{elevation:5,sx:{position:"fixed",width:"360px",bottom:"40px",insetInlineEnd:"40px",zIndex:"99999",hidden:!0,maxHeight:"645px",overflowY:"auto"}},a.default.createElement(p.default,{steps:n,isMinimized:m,toggleIsMinimized:v}),a.default.createElement(f.default,{steps:n,setSteps:o,isMinimized:m}))};m.propTypes={steps:n.array.isRequired,userProgress:n.object.isRequired};t.default=m},35843:(e,t,r)=>{"use strict";var n=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=n(r(61790)),a=n(r(85707)),i=n(r(58155)),u=r(86956),c=r(12470),l=n(r(56741)),p=n(r(62688)),f=r(92584),d=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&{}.hasOwnProperty.call(e,a)){var i=s?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(n,a,i):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(41594)),h=r(20244),y=r(55041),b=r(44048);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var m=y.USER_PROGRESS.CHECKLIST_CLOSED_IN_THE_EDITOR_FOR_FIRST_TIME,g=y.MIXPANEL_CHECKLIST_STEPS.CHECKLIST_HEADER_CLOSE,v=function Header(e){var t=e.steps,r=e.isMinimized,n=e.toggleIsMinimized,o=(0,f.useQuery)({queryKey:["closedForFirstTime"],queryFn:h.fetchUserProgress}).data,p=(null==o?void 0:o[m])||!1,y=function(){var e=(0,i.default)(s.default.mark((function _callee(){return s.default.wrap((function _callee$(e){for(;;)switch(e.prev=e.next){case 0:if((0,h.addMixpanelTrackingChecklistHeader)(g),p){e.next=5;break}return e.next=4,(0,h.updateUserProgress)((0,a.default)({},m,!0));case 4:window.dispatchEvent(new CustomEvent("elementor/checklist/first_close",{detail:{message:"firstClose"}}));case 5:(0,h.toggleChecklistPopup)();case 6:case"end":return e.stop()}}),_callee)})));return function closeChecklist(){return e.apply(this,arguments)}}();return d.createElement(d.Fragment,null,d.createElement(u.AppBar,{elevation:0,position:"sticky",sx:{p:2,backgroundColor:"background.default"}},d.createElement(u.Toolbar,{variant:"dense",disableGutters:!0},d.createElement(u.Typography,{variant:"subtitle1",sx:{flexGrow:1}},(0,c.__)("Let's make a productivity boost","elementor")),d.createElement(u.IconButton,{size:"small",onClick:n,"aria-expanded":!r},r?d.createElement(b.ExpandDiagonalIcon,null):d.createElement(b.MinimizeDiagonalIcon,null)),d.createElement(u.CloseButton,{sx:{mr:-.5},size:"small",onClick:y})),d.createElement(l.default,{steps:t})),d.createElement(u.Divider,null))};v.propTypes={steps:p.default.array.isRequired,isMinimized:p.default.bool.isRequired,toggleIsMinimized:p.default.func.isRequired};t.default=v},56741:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(41594)),s=r(86956),a=n(r(62688)),i=r(20244),u=function Progress(e){var t=e.steps,r=100*t.filter(i.isStepChecked).length/t.length;return o.default.createElement(s.Box,{sx:{display:"flex",alignItems:"center",gap:1}},o.default.createElement(s.Box,{sx:{width:"100%"}},o.default.createElement(s.LinearProgress,{variant:"determinate",value:r})),o.default.createElement(s.Box,{sx:{width:"fit-content"}},o.default.createElement(s.Typography,{variant:"body2",color:"text.secondary"},"".concat(Math.round(r),"%"))))};t.default=u;u.propTypes={steps:a.default.array.isRequired}},16712:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r(41594)),s=r(86956),a=r(12470),i=n(r(62688)),u=function ReminderModal(e){var t=e.setOpen;return o.default.createElement(s.Card,{elevation:0,sx:{maxWidth:336},className:"e-checklist-infotip-first-time-closed"},o.default.createElement(s.CardContent,null,o.default.createElement(s.Typography,{variant:"subtitle2",sx:{mb:2}},(0,a.__)("Looking for your Launchpad Checklist?","elementor")),o.default.createElement(s.Typography,{variant:"body2"},(0,a.__)("Click the launch icon to continue setting up your site.","elementor"))),o.default.createElement(s.CardActions,null,o.default.createElement(s.Button,{size:"small",variant:"contained",className:"infotip-first-time-closed-button",onClick:function closeChecklist(e){e.stopPropagation(),t(!1)}},(0,a.__)("Got it","elementor"))))};t.default=u;u.propTypes={setOpen:i.default.func.isRequired}},26917:(e,t,r)=>{"use strict";var n=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&{}.hasOwnProperty.call(e,a)){var i=s?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(n,a,i):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(41594)),a=r(86956),i=r(12470),u=n(r(73921)),c=r(20244),l=r(55041);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var p=l.MIXPANEL_CHECKLIST_STEPS.ACTION,f=l.MIXPANEL_CHECKLIST_STEPS.WELL_DONE;t.default=function SuccessMessage(){var e=(0,u.default)(),t=e.ajaxState,r=e.setAjax;return(0,s.useEffect)((function(){if("success"===t.status)setTimeout((function(){$e.commands.run("checklist/toggle-icon",!1)}),0)}),[t]),s.default.createElement(a.Card,{elevation:0,square:!0,className:"e-checklist-done"},s.default.createElement(a.CardMedia,{image:"https://assets.elementor.com/checklist/v1/images/checklist-step-7.jpg",sx:{height:180}}),s.default.createElement(a.CardContent,{sx:{textAlign:"center"}},s.default.createElement(a.Typography,{variant:"h6",color:"text.primary"},(0,i.__)("You're on your way!","elementor")),s.default.createElement(a.Typography,{variant:"body2",color:"text.secondary",component:"p"},(0,i.__)("With these steps, you've got a great base for a robust website. Enjoy your web creation journey!","elementor"))),s.default.createElement(a.CardActions,{sx:{justifyContent:"center"}},s.default.createElement(a.Button,{color:"primary",size:"small",variant:"contained",onClick:function hideChecklist(){(0,c.addMixpanelTrackingChecklistSteps)(f,p),r({data:{action:"elementor_ajax",actions:JSON.stringify({save_editorPreferences_settings:{action:"save_editorPreferences_settings",data:{data:{show_launchpad_checklist:""}}}})}})}},(0,i.__)("Got it","elementor"))))}},46581:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Steps",{enumerable:!0,get:function get(){return n.Steps}}),Object.defineProperty(t,"UserProgress",{enumerable:!0,get:function get(){return o.UserProgress}});var n=r(65414),o=r(9892)},65414:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.Steps=void 0;var o=n(r(39805)),s=n(r(40989)),a=n(r(15118)),i=n(r(29402)),u=n(r(87861));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}var c=t.Steps=function(e){function Steps(){return(0,o.default)(this,Steps),function _callSuper(e,t,r){return t=(0,i.default)(t),(0,a.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,i.default)(e).constructor):t.apply(e,r))}(this,Steps,arguments)}return(0,u.default)(Steps,e),(0,s.default)(Steps,null,[{key:"getEndpointFormat",value:function getEndpointFormat(){return"checklist/steps/{id}"}}])}($e.modules.CommandData);t.default=c},9892:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.UserProgress=void 0;var o=n(r(39805)),s=n(r(40989)),a=n(r(15118)),i=n(r(29402)),u=n(r(87861));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}var c=t.UserProgress=function(e){function UserProgress(){return(0,o.default)(this,UserProgress),function _callSuper(e,t,r){return t=(0,i.default)(t),(0,a.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,i.default)(e).constructor):t.apply(e,r))}(this,UserProgress,arguments)}return(0,u.default)(UserProgress,e),(0,s.default)(UserProgress,null,[{key:"getEndpointFormat",value:function getEndpointFormat(){return"checklist/user-progress"}}])}($e.modules.CommandData);t.default=c},33318:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ToggleIcon",{enumerable:!0,get:function get(){return o.ToggleIcon}}),Object.defineProperty(t,"TogglePopup",{enumerable:!0,get:function get(){return n.TogglePopup}});var n=r(21947),o=r(88564)},88564:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.ToggleIcon=void 0;var o=n(r(39805)),s=n(r(40989)),a=n(r(15118)),i=n(r(29402)),u=n(r(87861)),c=n(r(85707)),l=n(r(21947)),p=r(20244);function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}var f=t.ToggleIcon=function(e){function ToggleIcon(){return(0,o.default)(this,ToggleIcon),function _callSuper(e,t,r){return t=(0,i.default)(t),(0,a.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,i.default)(e).constructor):t.apply(e,r))}(this,ToggleIcon,arguments)}return(0,u.default)(ToggleIcon,e),(0,s.default)(ToggleIcon,[{key:"apply",value:function apply(e){document.body.querySelector('[aria-label="Checklist"]').parentElement.style.display=e?"block":"none",!e&&l.default.isOpen&&(0,p.toggleChecklistPopup)()}}])}($e.modules.CommandBase);(0,c.default)(f,"isSettingsOn",!0);t.default=f},21947:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.TogglePopup=void 0;var o=n(r(41594)),s=n(r(39805)),a=n(r(40989)),i=n(r(15118)),u=n(r(29402)),c=n(r(87861)),l=n(r(85707)),p=n(r(50288)),f=r(92584),d=n(r(7470)),h=r(20244),y=r(55041);function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}var b=new f.QueryClient,m=t.TogglePopup=function(e){function TogglePopup(){return(0,s.default)(this,TogglePopup),function _callSuper(e,t,r){return t=(0,u.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,u.default)(e).constructor):t.apply(e,r))}(this,TogglePopup,arguments)}return(0,c.default)(TogglePopup,e),(0,a.default)(TogglePopup,[{key:"apply",value:function apply(e){TogglePopup.isOpen?this.unmount():this.mount(),TogglePopup.isOpen=!TogglePopup.isOpen,e.isOpen=TogglePopup.isOpen,(0,h.updateUserProgress)((0,l.default)({},y.USER_PROGRESS.LAST_OPENED_TIMESTAMP,TogglePopup.isOpen))}},{key:"mount",value:function mount(){this.setRootElement(),TogglePopup.rootElement.render(o.default.createElement(f.QueryClientProvider,{client:b},o.default.createElement(p.default,null)))}},{key:"unmount",value:function unmount(){TogglePopup.rootElement.unmount(),document.body.removeChild(document.body.querySelector("#e-checklist"))}},{key:"setRootElement",value:function setRootElement(){var e=document.body.querySelector("#e-checklist");e||((e=document.createElement("div")).id="e-checklist",document.body.appendChild(e)),TogglePopup.rootElement=d.default.createRoot(e)}}])}($e.modules.CommandBase);(0,l.default)(m,"rootElement",null),(0,l.default)(m,"isOpen",!1);t.default=m},46352:(e,t,r)=>{"use strict";var n=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=n(r(39805)),a=n(r(40989)),i=n(r(15118)),u=n(r(29402)),c=n(r(87861)),l=_interopRequireWildcard(r(33318)),p=_interopRequireWildcard(r(46581));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&{}.hasOwnProperty.call(e,a)){var i=s?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(n,a,i):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function Component(){return(0,s.default)(this,Component),function _callSuper(e,t,r){return t=(0,u.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,u.default)(e).constructor):t.apply(e,r))}(this,Component,arguments)}return(0,c.default)(Component,e),(0,a.default)(Component,[{key:"getNamespace",value:function getNamespace(){return"checklist"}},{key:"defaultCommands",value:function defaultCommands(){return this.importCommands(l)}},{key:"defaultData",value:function defaultData(){return this.importCommands(p)}}],[{key:"getEndpointFormat",value:function getEndpointFormat(){return"checklist"}}])}($e.modules.ComponentBase)},83725:(e,t,r)=>{"use strict";var n=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.editorAppBarLink=void 0;var s=_interopRequireWildcard(r(39739)),a=r(12470),i=_interopRequireWildcard(r(41594)),u=n(r(25233)),c=r(20244),l=r(92584),p=r(33318);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&{}.hasOwnProperty.call(e,a)){var i=s?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(n,a,i):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}var f=new l.QueryClient;t.editorAppBarLink=function editorAppBarLink(){s.utilitiesMenu.registerLink({id:"app-bar-menu-item-checklist",priority:5,useProps:function useProps(){return{title:(0,a.__)("Checklist","elementor"),icon:function icon(){return i.createElement(l.QueryClientProvider,{client:f},i.createElement(u.default,null))},onClick:function onClick(){(0,c.addMixpanelTrackingChecklistTopBar)(p.TogglePopup.isOpen),(0,c.toggleChecklistPopup)()}}}})}},25233:(e,t,r)=>{"use strict";var n=r(96784),o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=n(r(18821)),a=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&{}.hasOwnProperty.call(e,a)){var i=s?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(n,a,i):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}(r(41594)),i=a,u=r(92584),c=r(54142),l=n(r(13507)),p=r(86956),f=n(r(16712)),d=r(55041),h=r(20244);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var y=d.USER_PROGRESS.CHECKLIST_CLOSED_IN_THE_EDITOR_FOR_FIRST_TIME;t.default=function TopBarIcon(){var e=(0,a.useState)(!1),t=(0,s.default)(e,2),r=t[0],n=t[1],o=(0,a.useState)(!1),d=(0,s.default)(o,2),b=d[0],m=d[1],g=(0,u.useQuery)({queryKey:["closedForFirstTime"],queryFn:h.fetchUserProgress}),v=g.error,O=g.data,_=null==O?void 0:O[y];return(0,a.useEffect)((function(){return(0,c.__privateListenTo)((0,c.commandEndEvent)("checklist/toggle-popup"),(function(e){n(e.args.isOpen)}))}),[r]),(0,a.useEffect)((function(){var e=function handleFirstClosed(){m(!0)};return window.addEventListener("elementor/checklist/first_close",e),function(){window.removeEventListener("elementor/checklist/first_close",e)}}),[]),v?null:r&&!_?i.createElement(l.default,null):i.createElement(p.Infotip,{placement:"bottom-start",content:i.createElement(f.default,{setHasRoot:n,setOpen:m}),open:b,PopperProps:{modifiers:[{name:"offset",options:{offset:[-16,12]}}]}},i.createElement(l.default,null))}},55041:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.USER_PROGRESS_ROUTE=t.USER_PROGRESS=t.STEP_IDS_TO_COMPLETE_IN_EDITOR=t.STEPS_ROUTE=t.STEP=t.PANEL_ROUTES=t.MIXPANEL_CHECKLIST_STEPS=void 0;t.STEPS_ROUTE="checklist/steps",t.USER_PROGRESS_ROUTE="checklist/user-progress",t.STEP={IS_MARKED_COMPLETED:"is_marked_completed",IS_IMMUTABLE_COMPLETED:"is_immutable_completed",IS_ABSOLUTE_COMPLETED:"is_absolute_completed",PROMOTION_DATA:"promotion_data"},t.USER_PROGRESS={LAST_OPENED_TIMESTAMP:"last_opened_timestamp",SHOULD_OPEN_IN_EDITOR:"should_open_in_editor",CHECKLIST_CLOSED_IN_THE_EDITOR_FOR_FIRST_TIME:"first_closed_checklist_in_editor",IS_POPUP_MINIMIZED:"is_popup_minimized",EDITOR_VISIT_COUNT:"e_editor_counter"},t.STEP_IDS_TO_COMPLETE_IN_EDITOR=["add_logo","set_fonts_and_colors"],t.PANEL_ROUTES={add_logo:"panel/global/settings-site-identity",set_fonts_and_colors:"panel/global/global-typography"},t.MIXPANEL_CHECKLIST_STEPS={UPGRADE:"upgrade",ACTION:"action",DONE:"done",UNDONE:"undone",TITLE:"title",WELL_DONE:"well_done",CHECKLIST_HEADER_CLOSE:"checklistHeaderClose",ACCORDION_SECTION:"accordionSection"}},20244:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.addMixpanelTrackingChecklistHeader=function addMixpanelTrackingChecklistHeader(e){var t=getDocumentMetaDataMixpanel();return elementor.editorEvents.dispatchEvent(elementor.editorEvents.config.names.elementorEditor.checklist[e],_objectSpread({location:elementor.editorEvents.config.locations.elementorEditor,secondaryLocation:elementor.editorEvents.config.secondaryLocations.checklistHeader,trigger:elementor.editorEvents.config.triggers.click,element:elementor.editorEvents.config.elements.buttonIcon},t))},t.addMixpanelTrackingChecklistSteps=function addMixpanelTrackingChecklistSteps(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"button",n=getDocumentMetaDataMixpanel();e=e.replace(/_/g,"");var o="checklist_steps_".concat(t,"_").concat(e);return elementor.editorEvents.dispatchEvent(o,_objectSpread({location:elementor.editorEvents.config.locations.elementorEditor,secondaryLocation:elementor.editorEvents.config.secondaryLocations.checklistSteps,trigger:elementor.editorEvents.config.triggers.click,element:elementor.editorEvents.config.elements[r]},n))},t.addMixpanelTrackingChecklistTopBar=function addMixpanelTrackingChecklistTopBar(e){var t=getDocumentMetaDataMixpanel(),r=e?"launchpadOff":"launchpadOn";return elementor.editorEvents.dispatchEvent(elementor.editorEvents.config.names.topBar[r],_objectSpread({location:elementor.editorEvents.config.locations.topBar,secondaryLocation:elementor.editorEvents.config.secondaryLocations.launchpad,trigger:elementor.editorEvents.config.triggers.toggleClick,element:elementor.editorEvents.config.elements.buttonIcon},t))},t.dispatchChecklistOpenEvent=function dispatchChecklistOpenEvent(){var e=getDocumentMetaDataMixpanel();return elementor.editorEvents.dispatchEvent(elementor.editorEvents.config.names.elementorEditor.checklist.checklistFirstPopup,_objectSpread({location:elementor.editorEvents.config.locations.elementorEditor,secondaryLocation:elementor.editorEvents.config.secondaryLocations.launchpad,trigger:elementor.editorEvents.config.triggers.editorLoaded,element:elementor.editorEvents.config.elements.launchpadChecklist},e))},t.fetchSteps=function fetchSteps(){return _fetchSteps.apply(this,arguments)},t.fetchUserProgress=function fetchUserProgress(){return _fetchUserProgress.apply(this,arguments)},t.getAndUpdateStep=function getAndUpdateStep(e,t,r,n){if(t.config.id!==e)return t;return _objectSpread(_objectSpread({},t),{},(0,s.default)({},r,n))},t.getDocumentMetaDataMixpanel=getDocumentMetaDataMixpanel,t.isStepChecked=function isStepChecked(e){return!e[p]&&(e[u]||e[c]||e[l])},t.toggleChecklistPopup=function toggleChecklistPopup(){$e.run("checklist/toggle-popup")},t.updateStep=function updateStep(e,t){return _updateStep.apply(this,arguments)},t.updateUserProgress=function updateUserProgress(e){return _updateUserProgress.apply(this,arguments)};var o=n(r(61790)),s=n(r(85707)),a=n(r(58155)),i=r(55041);function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach((function(t){(0,s.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var u=i.STEP.IS_MARKED_COMPLETED,c=i.STEP.IS_ABSOLUTE_COMPLETED,l=i.STEP.IS_IMMUTABLE_COMPLETED,p=i.STEP.PROMOTION_DATA;function _fetchSteps(){return(_fetchSteps=(0,a.default)(o.default.mark((function _callee(){var e,t;return o.default.wrap((function _callee$(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,$e.data.get(i.STEPS_ROUTE,{},{refresh:!0});case 2:return t=r.sent,r.abrupt("return",(null==t||null===(e=t.data)||void 0===e?void 0:e.data)||null);case 4:case"end":return r.stop()}}),_callee)})))).apply(this,arguments)}function _fetchUserProgress(){return(_fetchUserProgress=(0,a.default)(o.default.mark((function _callee2(){var e,t;return o.default.wrap((function _callee2$(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,$e.data.get(i.USER_PROGRESS_ROUTE,{},{refresh:!0});case 2:return t=r.sent,r.abrupt("return",(null==t||null===(e=t.data)||void 0===e?void 0:e.data)||null);case 4:case"end":return r.stop()}}),_callee2)})))).apply(this,arguments)}function _updateStep(){return(_updateStep=(0,a.default)(o.default.mark((function _callee3(e,t){return o.default.wrap((function _callee3$(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,$e.data.update(i.STEPS_ROUTE,_objectSpread({id:e},t),{id:e});case 2:return r.abrupt("return",r.sent);case 3:case"end":return r.stop()}}),_callee3)})))).apply(this,arguments)}function _updateUserProgress(){return(_updateUserProgress=(0,a.default)(o.default.mark((function _callee4(e){return o.default.wrap((function _callee4$(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,$e.data.update(i.USER_PROGRESS_ROUTE,e);case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),_callee4)})))).apply(this,arguments)}function getDocumentMetaDataMixpanel(){return{postId:elementor.getPreviewContainer().document.config.id,postTitle:elementor.getPreviewContainer().model.attributes.settings.attributes.post_title,postTypeTitle:elementor.getPreviewContainer().document.config.post_type_title,documentType:elementor.getPreviewContainer().document.config.type}}},40362:(e,t,r)=>{"use strict";var n=r(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,r,o,s,a){if(a!==n){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},62688:(e,t,r)=>{e.exports=r(40362)()},56441:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},7470:(e,t,r)=>{"use strict";var n=r(75206);t.createRoot=n.createRoot,t.hydrateRoot=n.hydrateRoot},2192:(e,t,r)=>{"use strict";var n=r(41594),o=Symbol.for("react.element"),s=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,i=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,r){var n,s={},c=null,l=null;for(n in void 0!==r&&(c=""+r),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(l=t.ref),t)a.call(t,n)&&!u.hasOwnProperty(n)&&(s[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===s[n]&&(s[n]=t[n]);return{$$typeof:o,type:e,key:c,ref:l,props:s,_owner:i.current}}t.Fragment=s,t.jsx=q,t.jsxs=q},62540:(e,t,r)=>{"use strict";e.exports=r(2192)},41594:e=>{"use strict";e.exports=React},75206:e=>{"use strict";e.exports=ReactDOM},39739:e=>{"use strict";e.exports=elementorV2.editorAppBar},54142:e=>{"use strict";e.exports=elementorV2.editorV1Adapters},44048:e=>{"use strict";e.exports=elementorV2.icons},13507:e=>{"use strict";e.exports=elementorV2.icons.RocketIcon},86956:e=>{"use strict";e.exports=elementorV2.ui},12470:e=>{"use strict";e.exports=wp.i18n},78113:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},70569:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},91819:(e,t,r)=>{var n=r(78113);e.exports=function _arrayWithoutHoles(e){if(Array.isArray(e))return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},36417:e=>{e.exports=function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},58155:e=>{function asyncGeneratorStep(e,t,r,n,o,s,a){try{var i=e[s](a),u=i.value}catch(e){return void r(e)}i.done?t(u):Promise.resolve(u).then(n,o)}e.exports=function _asyncToGenerator(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var s=e.apply(t,r);function _next(e){asyncGeneratorStep(s,n,o,_next,_throw,"next",e)}function _throw(e){asyncGeneratorStep(s,n,o,_next,_throw,"throw",e)}_next(void 0)}))}},e.exports.__esModule=!0,e.exports.default=e.exports},39805:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},40989:(e,t,r)=>{var n=r(45498);function _defineProperties(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,n(o.key),o)}}e.exports=function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},85707:(e,t,r)=>{var n=r(45498);e.exports=function _defineProperty(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports},29402:e=>{function _getPrototypeOf(t){return e.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,_getPrototypeOf(t)}e.exports=_getPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},87861:(e,t,r)=>{var n=r(91270);e.exports=function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&n(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},20365:e=>{e.exports=function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},65474:e=>{e.exports=function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,s,a,i=[],u=!0,c=!1;try{if(s=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=s.call(r)).done)&&(i.push(n.value),i.length!==t);u=!0);}catch(e){c=!0,o=e}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return i}},e.exports.__esModule=!0,e.exports.default=e.exports},11018:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},78687:e=>{e.exports=function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},15118:(e,t,r)=>{var n=r(10564).default,o=r(36417);e.exports=function _possibleConstructorReturn(e,t){if(t&&("object"==n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return o(e)},e.exports.__esModule=!0,e.exports.default=e.exports},53051:(e,t,r)=>{var n=r(10564).default;function _regeneratorRuntime(){"use strict";e.exports=_regeneratorRuntime=function _regeneratorRuntime(){return r},e.exports.__esModule=!0,e.exports.default=e.exports;var t,r={},o=Object.prototype,s=o.hasOwnProperty,a=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},u=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function define(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{define({},"")}catch(t){define=function define(e,t,r){return e[t]=r}}function wrap(e,t,r,n){var o=t&&t.prototype instanceof Generator?t:Generator,s=Object.create(o.prototype),i=new Context(n||[]);return a(s,"_invoke",{value:makeInvokeMethod(e,r,i)}),s}function tryCatch(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}r.wrap=wrap;var p="suspendedStart",f="suspendedYield",d="executing",h="completed",y={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}var b={};define(b,u,(function(){return this}));var m=Object.getPrototypeOf,g=m&&m(m(values([])));g&&g!==o&&s.call(g,u)&&(b=g);var v=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(b);function defineIteratorMethods(e){["next","throw","return"].forEach((function(t){define(e,t,(function(e){return this._invoke(t,e)}))}))}function AsyncIterator(e,t){function invoke(r,o,a,i){var u=tryCatch(e[r],e,o);if("throw"!==u.type){var c=u.arg,l=c.value;return l&&"object"==n(l)&&s.call(l,"__await")?t.resolve(l.__await).then((function(e){invoke("next",e,a,i)}),(function(e){invoke("throw",e,a,i)})):t.resolve(l).then((function(e){c.value=e,a(c)}),(function(e){return invoke("throw",e,a,i)}))}i(u.arg)}var r;a(this,"_invoke",{value:function value(e,n){function callInvokeWithMethodAndArg(){return new t((function(t,r){invoke(e,n,t,r)}))}return r=r?r.then(callInvokeWithMethodAndArg,callInvokeWithMethodAndArg):callInvokeWithMethodAndArg()}})}function makeInvokeMethod(e,r,n){var o=p;return function(s,a){if(o===d)throw Error("Generator is already running");if(o===h){if("throw"===s)throw a;return{value:t,done:!0}}for(n.method=s,n.arg=a;;){var i=n.delegate;if(i){var u=maybeInvokeDelegate(i,n);if(u){if(u===y)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var c=tryCatch(e,r,n);if("normal"===c.type){if(o=n.done?h:f,c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=h,n.method="throw",n.arg=c.arg)}}}function maybeInvokeDelegate(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,maybeInvokeDelegate(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var s=tryCatch(o,e.iterator,r.arg);if("throw"===s.type)return r.method="throw",r.arg=s.arg,r.delegate=null,y;var a=s.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function pushTryEntry(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function resetTryEntry(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function Context(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(pushTryEntry,this),this.reset(!0)}function values(e){if(e||""===e){var r=e[u];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function next(){for(;++o<e.length;)if(s.call(e,o))return next.value=e[o],next.done=!1,next;return next.value=t,next.done=!0,next};return a.next=a}}throw new TypeError(n(e)+" is not iterable")}return GeneratorFunction.prototype=GeneratorFunctionPrototype,a(v,"constructor",{value:GeneratorFunctionPrototype,configurable:!0}),a(GeneratorFunctionPrototype,"constructor",{value:GeneratorFunction,configurable:!0}),GeneratorFunction.displayName=define(GeneratorFunctionPrototype,l,"GeneratorFunction"),r.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===GeneratorFunction||"GeneratorFunction"===(t.displayName||t.name))},r.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,GeneratorFunctionPrototype):(e.__proto__=GeneratorFunctionPrototype,define(e,l,"GeneratorFunction")),e.prototype=Object.create(v),e},r.awrap=function(e){return{__await:e}},defineIteratorMethods(AsyncIterator.prototype),define(AsyncIterator.prototype,c,(function(){return this})),r.AsyncIterator=AsyncIterator,r.async=function(e,t,n,o,s){void 0===s&&(s=Promise);var a=new AsyncIterator(wrap(e,t,n,o),s);return r.isGeneratorFunction(t)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},defineIteratorMethods(v),define(v,l,"Generator"),define(v,u,(function(){return this})),define(v,"toString",(function(){return"[object Generator]"})),r.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function next(){for(;r.length;){var e=r.pop();if(e in t)return next.value=e,next.done=!1,next}return next.done=!0,next}},r.values=values,Context.prototype={constructor:Context,reset:function reset(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(resetTryEntry),!e)for(var r in this)"t"===r.charAt(0)&&s.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function stop(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function dispatchException(e){if(this.done)throw e;var r=this;function handle(n,o){return a.type="throw",a.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],a=o.completion;if("root"===o.tryLoc)return handle("end");if(o.tryLoc<=this.prev){var i=s.call(o,"catchLoc"),u=s.call(o,"finallyLoc");if(i&&u){if(this.prev<o.catchLoc)return handle(o.catchLoc,!0);if(this.prev<o.finallyLoc)return handle(o.finallyLoc)}else if(i){if(this.prev<o.catchLoc)return handle(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return handle(o.finallyLoc)}}}},abrupt:function abrupt(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&s.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(a)},complete:function complete(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function finish(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),resetTryEntry(r),y}},catch:function _catch(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;resetTryEntry(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function delegateYield(e,r,n){return this.delegate={iterator:values(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},r}e.exports=_regeneratorRuntime,e.exports.__esModule=!0,e.exports.default=e.exports},91270:e=>{function _setPrototypeOf(t,r){return e.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,_setPrototypeOf(t,r)}e.exports=_setPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},18821:(e,t,r)=>{var n=r(70569),o=r(65474),s=r(37744),a=r(11018);e.exports=function _slicedToArray(e,t){return n(e)||o(e,t)||s(e,t)||a()},e.exports.__esModule=!0,e.exports.default=e.exports},10906:(e,t,r)=>{var n=r(91819),o=r(20365),s=r(37744),a=r(78687);e.exports=function _toConsumableArray(e){return n(e)||o(e)||s(e)||a()},e.exports.__esModule=!0,e.exports.default=e.exports},11327:(e,t,r)=>{var n=r(10564).default;e.exports=function toPrimitive(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},45498:(e,t,r)=>{var n=r(10564).default,o=r(11327);e.exports=function toPropertyKey(e){var t=o(e,"string");return"symbol"==n(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},37744:(e,t,r)=>{var n=r(78113);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return n(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},61790:(e,t,r)=>{var n=r(53051)();e.exports=n;try{regeneratorRuntime=n}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=n:Function("r","regeneratorRuntime = r")(n)}},52803:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{FocusManager:()=>p,focusManager:()=>f}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(66133),l=r(73025),p=class extends c.Subscribable{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!l.isServer&&window.addEventListener){const listener=()=>e();return window.addEventListener("visibilitychange",listener,!1),()=>{window.removeEventListener("visibilitychange",listener)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach((t=>{t(e)}))}isFocused(){return"boolean"==typeof this.#e?this.#e:"hidden"!==globalThis.document?.visibilityState}},f=new p},28620:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,a={};function defaultTransformerFn(e){return e}function dehydrateMutation(e){return{mutationKey:e.options.mutationKey,state:e.state,...e.options.scope&&{scope:e.options.scope},...e.meta&&{meta:e.meta}}}function dehydrateQuery(e,t){return{state:{...e.state,...void 0!==e.state.data&&{data:t(e.state.data)}},queryKey:e.queryKey,queryHash:e.queryHash,..."pending"===e.state.status&&{promise:e.promise?.then(t).catch((e=>Promise.reject(new Error("redacted"))))},...e.meta&&{meta:e.meta}}}function defaultShouldDehydrateMutation(e){return e.state.isPaused}function defaultShouldDehydrateQuery(e){return"success"===e.state.status}function dehydrate(e,t={}){const r=t.shouldDehydrateMutation??e.getDefaultOptions().dehydrate?.shouldDehydrateMutation??defaultShouldDehydrateMutation,n=e.getMutationCache().getAll().flatMap((e=>r(e)?[dehydrateMutation(e)]:[])),o=t.shouldDehydrateQuery??e.getDefaultOptions().dehydrate?.shouldDehydrateQuery??defaultShouldDehydrateQuery,s=t.serializeData??e.getDefaultOptions().dehydrate?.serializeData??defaultTransformerFn;return{mutations:n,queries:e.getQueryCache().getAll().flatMap((e=>o(e)?[dehydrateQuery(e,s)]:[]))}}function hydrate(e,t,r){if("object"!=typeof t||null===t)return;const n=e.getMutationCache(),o=e.getQueryCache(),s=r?.defaultOptions?.deserializeData??e.getDefaultOptions().hydrate?.deserializeData??defaultTransformerFn,a=t.mutations||[],i=t.queries||[];a.forEach((({state:t,...o})=>{n.build(e,{...e.getDefaultOptions().hydrate?.mutations,...r?.defaultOptions?.mutations,...o},t)})),i.forEach((({queryKey:t,state:n,queryHash:a,meta:i,promise:u})=>{let c=o.get(a);const l=void 0===n.data?n.data:s(n.data);if(c){if(c.state.dataUpdatedAt<n.dataUpdatedAt){const{fetchStatus:e,...t}=n;c.setState({...t,data:l})}}else c=o.build(e,{...e.getDefaultOptions().hydrate?.queries,...r?.defaultOptions?.queries,queryKey:t,queryHash:a,meta:i},{...n,data:l,fetchStatus:"idle"});if(u){const e=Promise.resolve(u).then(s);c.fetch(void 0,{initialPromise:e})}}))}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(a,{defaultShouldDehydrateMutation:()=>defaultShouldDehydrateMutation,defaultShouldDehydrateQuery:()=>defaultShouldDehydrateQuery,dehydrate:()=>dehydrate,hydrate:()=>hydrate}),e.exports=(t=a,((e,t,a,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))s.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(i=n(t,u))||i.enumerable});return e})(r({},"__esModule",{value:!0}),t))},51934:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e},u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{CancelledError:()=>c.CancelledError,InfiniteQueryObserver:()=>h.InfiniteQueryObserver,Mutation:()=>w.Mutation,MutationCache:()=>y.MutationCache,MutationObserver:()=>b.MutationObserver,QueriesObserver:()=>d.QueriesObserver,Query:()=>E.Query,QueryCache:()=>l.QueryCache,QueryClient:()=>p.QueryClient,QueryObserver:()=>f.QueryObserver,defaultShouldDehydrateMutation:()=>P.defaultShouldDehydrateMutation,defaultShouldDehydrateQuery:()=>P.defaultShouldDehydrateQuery,dehydrate:()=>P.dehydrate,focusManager:()=>g.focusManager,hashKey:()=>O.hashKey,hydrate:()=>P.hydrate,isCancelledError:()=>_.isCancelledError,isServer:()=>O.isServer,keepPreviousData:()=>O.keepPreviousData,matchMutation:()=>O.matchMutation,matchQuery:()=>O.matchQuery,notifyManager:()=>m.notifyManager,onlineManager:()=>v.onlineManager,replaceEqualDeep:()=>O.replaceEqualDeep,skipToken:()=>O.skipToken}),e.exports=(n=u,__copyProps(o({},"__esModule",{value:!0}),n));var c=r(7785),l=r(18976),p=r(56587),f=r(63524),d=r(55828),h=r(75360),y=r(69239),b=r(3877),m=r(7734),g=r(52803),v=r(97692),O=r(73025),_=r(7785),P=r(28620);((e,t,r)=>{__copyProps(e,t,"default"),r&&__copyProps(r,t,"default")})(u,r(98813),e.exports);var E=r(44478),w=r(46071)},52200:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{hasNextPage:()=>hasNextPage,hasPreviousPage:()=>hasPreviousPage,infiniteQueryBehavior:()=>infiniteQueryBehavior}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(73025);function infiniteQueryBehavior(e){return{onFetch:(t,r)=>{const n=t.options,o=t.fetchOptions?.meta?.fetchMore?.direction,s=t.state.data?.pages||[],a=t.state.data?.pageParams||[];let i={pages:[],pageParams:[]},u=0;const fetchFn=async()=>{let r=!1;const l=(0,c.ensureQueryFn)(t.options,t.fetchOptions),fetchPage=async(e,n,o)=>{if(r)return Promise.reject();if(null==n&&e.pages.length)return Promise.resolve(e);const s={queryKey:t.queryKey,pageParam:n,direction:o?"backward":"forward",meta:t.options.meta};var a;a=s,Object.defineProperty(a,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",(()=>{r=!0})),t.signal)});const i=await l(s),{maxPages:u}=t.options,p=o?c.addToStart:c.addToEnd;return{pages:p(e.pages,i,u),pageParams:p(e.pageParams,n,u)}};if(o&&s.length){const e="backward"===o,t={pages:s,pageParams:a},r=(e?getPreviousPageParam:getNextPageParam)(n,t);i=await fetchPage(t,r,e)}else{const t=e??s.length;do{const e=0===u?a[0]??n.initialPageParam:getNextPageParam(n,i);if(u>0&&null==e)break;i=await fetchPage(i,e),u++}while(u<t)}return i};t.options.persister?t.fetchFn=()=>t.options.persister?.(fetchFn,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=fetchFn}}}function getNextPageParam(e,{pages:t,pageParams:r}){const n=t.length-1;return t.length>0?e.getNextPageParam(t[n],t,r[n],r):void 0}function getPreviousPageParam(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}function hasNextPage(e,t){return!!t&&null!=getNextPageParam(e,t)}function hasPreviousPage(e,t){return!(!t||!e.getPreviousPageParam)&&null!=getPreviousPageParam(e,t)}},75360:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{InfiniteQueryObserver:()=>p}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(63524),l=r(52200),p=class extends c.QueryObserver{constructor(e,t){super(e,t)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(e,t){super.setOptions({...e,behavior:(0,l.infiniteQueryBehavior)()},t)}getOptimisticResult(e){return e.behavior=(0,l.infiniteQueryBehavior)(),super.getOptimisticResult(e)}fetchNextPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"forward"}}})}fetchPreviousPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"backward"}}})}createResult(e,t){const{state:r}=e,n=super.createResult(e,t),{isFetching:o,isRefetching:s,isError:a,isRefetchError:i}=n,u=r.fetchMeta?.fetchMore?.direction,c=a&&"forward"===u,p=o&&"forward"===u,f=a&&"backward"===u,d=o&&"backward"===u;return{...n,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:(0,l.hasNextPage)(t,r.data),hasPreviousPage:(0,l.hasPreviousPage)(t,r.data),isFetchNextPageError:c,isFetchingNextPage:p,isFetchPreviousPageError:f,isFetchingPreviousPage:d,isRefetchError:i&&!c&&!f,isRefetching:s&&!p&&!d}}}},46071:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{Mutation:()=>f,getDefaultState:()=>getDefaultState}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(7734),l=r(95181),p=r(7785),f=class extends l.Removable{#n;#o;#s;constructor(e){super(),this.mutationId=e.mutationId,this.#o=e.mutationCache,this.#n=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#n.includes(e)||(this.#n.push(e),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#n=this.#n.filter((t=>t!==e)),this.scheduleGc(),this.#o.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#n.length||("pending"===this.state.status?this.scheduleGc():this.#o.remove(this))}continue(){return this.#s?.continue()??this.execute(this.state.variables)}async execute(e){this.#s=(0,p.createRetryer)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{this.#a({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#a({type:"pause"})},onContinue:()=>{this.#a({type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#o.canRun(this)});const t="pending"===this.state.status,r=!this.#s.canStart();try{if(!t){this.#a({type:"pending",variables:e,isPaused:r}),await(this.#o.config.onMutate?.(e,this));const t=await(this.options.onMutate?.(e));t!==this.state.context&&this.#a({type:"pending",context:t,variables:e,isPaused:r})}const n=await this.#s.start();return await(this.#o.config.onSuccess?.(n,e,this.state.context,this)),await(this.options.onSuccess?.(n,e,this.state.context)),await(this.#o.config.onSettled?.(n,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(n,null,e,this.state.context)),this.#a({type:"success",data:n}),n}catch(t){try{throw await(this.#o.config.onError?.(t,e,this.state.context,this)),await(this.options.onError?.(t,e,this.state.context)),await(this.#o.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,t,e,this.state.context)),t}finally{this.#a({type:"error",error:t})}}finally{this.#o.runNext(this)}}#a(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),c.notifyManager.batch((()=>{this.#n.forEach((t=>{t.onMutationUpdate(e)})),this.#o.notify({mutation:this,type:"updated",action:e})}))}};function getDefaultState(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},69239:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{MutationCache:()=>d}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(7734),l=r(46071),p=r(73025),f=r(66133),d=class extends f.Subscribable{constructor(e={}){super(),this.config=e,this.#i=new Map,this.#u=Date.now()}#i;#u;build(e,t,r){const n=new l.Mutation({mutationCache:this,mutationId:++this.#u,options:e.defaultMutationOptions(t),state:r});return this.add(n),n}add(e){const t=scopeFor(e),r=this.#i.get(t)??[];r.push(e),this.#i.set(t,r),this.notify({type:"added",mutation:e})}remove(e){const t=scopeFor(e);if(this.#i.has(t)){const r=this.#i.get(t)?.filter((t=>t!==e));r&&(0===r.length?this.#i.delete(t):this.#i.set(t,r))}this.notify({type:"removed",mutation:e})}canRun(e){const t=this.#i.get(scopeFor(e))?.find((e=>"pending"===e.state.status));return!t||t===e}runNext(e){const t=this.#i.get(scopeFor(e))?.find((t=>t!==e&&t.state.isPaused));return t?.continue()??Promise.resolve()}clear(){c.notifyManager.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}getAll(){return[...this.#i.values()].flat()}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,p.matchMutation)(t,e)))}findAll(e={}){return this.getAll().filter((t=>(0,p.matchMutation)(e,t)))}notify(e){c.notifyManager.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){const e=this.getAll().filter((e=>e.state.isPaused));return c.notifyManager.batch((()=>Promise.all(e.map((e=>e.continue().catch(p.noop))))))}};function scopeFor(e){return e.options.scope?.id??String(e.mutationId)}},3877:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{MutationObserver:()=>d}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(46071),l=r(7734),p=r(66133),f=r(73025),d=class extends p.Subscribable{#c;#l=void 0;#p;#f;constructor(e,t){super(),this.#c=e,this.setOptions(t),this.bindMethods(),this.#d()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){const t=this.options;this.options=this.#c.defaultMutationOptions(e),(0,f.shallowEqualObjects)(this.options,t)||this.#c.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#p,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,f.hashKey)(t.mutationKey)!==(0,f.hashKey)(this.options.mutationKey)?this.reset():"pending"===this.#p?.state.status&&this.#p.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#p?.removeObserver(this)}onMutationUpdate(e){this.#d(),this.#h(e)}getCurrentResult(){return this.#l}reset(){this.#p?.removeObserver(this),this.#p=void 0,this.#d(),this.#h()}mutate(e,t){return this.#f=t,this.#p?.removeObserver(this),this.#p=this.#c.getMutationCache().build(this.#c,this.options),this.#p.addObserver(this),this.#p.execute(e)}#d(){const e=this.#p?.state??(0,c.getDefaultState)();this.#l={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#h(e){l.notifyManager.batch((()=>{if(this.#f&&this.hasListeners()){const t=this.#l.variables,r=this.#l.context;"success"===e?.type?(this.#f.onSuccess?.(e.data,t,r),this.#f.onSettled?.(e.data,null,t,r)):"error"===e?.type&&(this.#f.onError?.(e.error,t,r),this.#f.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach((e=>{e(this.#l)}))}))}}},7734:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,a={};function createNotifyManager(){let e=[],t=0,notifyFn=e=>{e()},batchNotifyFn=e=>{e()},scheduleFn=e=>setTimeout(e,0);const schedule=r=>{t?e.push(r):scheduleFn((()=>{notifyFn(r)}))};return{batch:r=>{let n;t++;try{n=r()}finally{t--,t||(()=>{const t=e;e=[],t.length&&scheduleFn((()=>{batchNotifyFn((()=>{t.forEach((e=>{notifyFn(e)}))}))}))})()}return n},batchCalls:e=>(...t)=>{schedule((()=>{e(...t)}))},schedule,setNotifyFunction:e=>{notifyFn=e},setBatchNotifyFunction:e=>{batchNotifyFn=e},setScheduler:e=>{scheduleFn=e}}}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(a,{createNotifyManager:()=>createNotifyManager,notifyManager:()=>i}),e.exports=(t=a,((e,t,a,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))s.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(i=n(t,u))||i.enumerable});return e})(r({},"__esModule",{value:!0}),t));var i=createNotifyManager()},97692:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{OnlineManager:()=>p,onlineManager:()=>f}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(66133),l=r(73025),p=class extends c.Subscribable{#y=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!l.isServer&&window.addEventListener){const onlineListener=()=>e(!0),offlineListener=()=>e(!1);return window.addEventListener("online",onlineListener,!1),window.addEventListener("offline",offlineListener,!1),()=>{window.removeEventListener("online",onlineListener),window.removeEventListener("offline",offlineListener)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#y!==e&&(this.#y=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#y}},f=new p},55828:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{QueriesObserver:()=>d}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(7734),l=r(63524),p=r(66133),f=r(73025);function difference(e,t){return e.filter((e=>!t.includes(e)))}var d=class extends p.Subscribable{#c;#b;#m;#g;#n;#v;#O;#_;constructor(e,t,r){super(),this.#c=e,this.#g=r,this.#m=[],this.#n=[],this.#b=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#n.forEach((e=>{e.subscribe((t=>{this.#P(e,t)}))}))}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#n.forEach((e=>{e.destroy()}))}setQueries(e,t,r){this.#m=e,this.#g=t,c.notifyManager.batch((()=>{const e=this.#n,t=this.#E(this.#m);t.forEach((e=>e.observer.setOptions(e.defaultedQueryOptions,r)));const n=t.map((e=>e.observer)),o=n.map((e=>e.getCurrentResult())),s=n.some(((t,r)=>t!==e[r]));(e.length!==n.length||s)&&(this.#n=n,this.#b=o,this.hasListeners()&&(difference(e,n).forEach((e=>{e.destroy()})),difference(n,e).forEach((e=>{e.subscribe((t=>{this.#P(e,t)}))})),this.#h()))}))}getCurrentResult(){return this.#b}getQueries(){return this.#n.map((e=>e.getCurrentQuery()))}getObservers(){return this.#n}getOptimisticResult(e,t){const r=this.#E(e).map((e=>e.observer.getOptimisticResult(e.defaultedQueryOptions)));return[r,e=>this.#w(e??r,t),()=>this.#S(r,e)]}#S(e,t){const r=this.#E(t);return r.map(((t,n)=>{const o=e[n];return t.defaultedQueryOptions.notifyOnChangeProps?o:t.observer.trackResult(o,(e=>{r.forEach((t=>{t.observer.trackProp(e)}))}))}))}#w(e,t){return t?(this.#v&&this.#b===this.#_&&t===this.#O||(this.#O=t,this.#_=this.#b,this.#v=(0,f.replaceEqualDeep)(this.#v,t(e))),this.#v):e}#E(e){const t=new Map(this.#n.map((e=>[e.options.queryHash,e]))),r=[];return e.forEach((e=>{const n=this.#c.defaultQueryOptions(e),o=t.get(n.queryHash);o?r.push({defaultedQueryOptions:n,observer:o}):r.push({defaultedQueryOptions:n,observer:new l.QueryObserver(this.#c,n)})})),r}#P(e,t){const r=this.#n.indexOf(e);-1!==r&&(this.#b=function replaceAt(e,t,r){const n=e.slice(0);return n[t]=r,n}(this.#b,r,t),this.#h())}#h(){if(this.hasListeners()){this.#v!==this.#w(this.#S(this.#b,this.#m),this.#g?.combine)&&c.notifyManager.batch((()=>{this.listeners.forEach((e=>{e(this.#b)}))}))}}}},44478:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{Query:()=>d,fetchState:()=>fetchState}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(73025),l=r(7734),p=r(7785),f=r(95181),d=class extends f.Removable{#j;#C;#x;#s;#M;#R;constructor(e){super(),this.#R=!1,this.#M=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#x=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#j=function getDefaultState(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,n=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#j,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#s?.promise}setOptions(e){this.options={...this.#M,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#x.remove(this)}setData(e,t){const r=(0,c.replaceData)(this.state.data,e,this.options);return this.#a({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#a({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#s?.promise;return this.#s?.cancel(e),t?t.then(c.noop).catch(c.noop):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#j)}isActive(){return this.observers.some((e=>!1!==(0,c.resolveEnabled)(e.options.enabled,this)))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===c.skipToken||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((e=>e.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!(0,c.timeUntilStale)(this.state.dataUpdatedAt,e)}onFocus(){const e=this.observers.find((e=>e.shouldFetchOnWindowFocus()));e?.refetch({cancelRefetch:!1}),this.#s?.continue()}onOnline(){const e=this.observers.find((e=>e.shouldFetchOnReconnect()));e?.refetch({cancelRefetch:!1}),this.#s?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#x.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter((t=>t!==e)),this.observers.length||(this.#s&&(this.#R?this.#s.cancel({revert:!0}):this.#s.cancelRetry()),this.scheduleGc()),this.#x.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#a({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#s)return this.#s.continueRetry(),this.#s.promise;if(e&&this.setOptions(e),!this.options.queryFn){const e=this.observers.find((e=>e.options.queryFn));e&&this.setOptions(e.options)}const r=new AbortController,addSignalProperty=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#R=!0,r.signal)})},n={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{const e=(0,c.ensureQueryFn)(this.options,t),r={queryKey:this.queryKey,meta:this.meta};return addSignalProperty(r),this.#R=!1,this.options.persister?this.options.persister(e,r,this):e(r)}};addSignalProperty(n),this.options.behavior?.onFetch(n,this),this.#C=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===n.fetchOptions?.meta||this.#a({type:"fetch",meta:n.fetchOptions?.meta});const onError=e=>{(0,p.isCancelledError)(e)&&e.silent||this.#a({type:"error",error:e}),(0,p.isCancelledError)(e)||(this.#x.config.onError?.(e,this),this.#x.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#s=(0,p.createRetryer)({initialPromise:t?.initialPromise,fn:n.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0!==e){try{this.setData(e)}catch(e){return void onError(e)}this.#x.config.onSuccess?.(e,this),this.#x.config.onSettled?.(e,this.state.error,this),this.scheduleGc()}else onError(new Error(`${this.queryHash} data is undefined`))},onError,onFail:(e,t)=>{this.#a({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#a({type:"pause"})},onContinue:()=>{this.#a({type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode,canRun:()=>!0}),this.#s.start()}#a(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...fetchState(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const r=e.error;return(0,p.isCancelledError)(r)&&r.revert&&this.#C?{...this.#C,fetchStatus:"idle"}:{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),l.notifyManager.batch((()=>{this.observers.forEach((e=>{e.onQueryUpdate()})),this.#x.notify({query:this,type:"updated",action:e})}))}};function fetchState(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,p.canFetch)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},18976:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{QueryCache:()=>d}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(73025),l=r(44478),p=r(7734),f=r(66133),d=class extends f.Subscribable{constructor(e={}){super(),this.config=e,this.#m=new Map}#m;build(e,t,r){const n=t.queryKey,o=t.queryHash??(0,c.hashQueryKeyByOptions)(n,t);let s=this.get(o);return s||(s=new l.Query({cache:this,queryKey:n,queryHash:o,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(n)}),this.add(s)),s}add(e){this.#m.has(e.queryHash)||(this.#m.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#m.get(e.queryHash);t&&(e.destroy(),t===e&&this.#m.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){p.notifyManager.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return this.#m.get(e)}getAll(){return[...this.#m.values()]}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,c.matchQuery)(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>(0,c.matchQuery)(e,t))):t}notify(e){p.notifyManager.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){p.notifyManager.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){p.notifyManager.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}}},56587:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{QueryClient:()=>b}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(73025),l=r(18976),p=r(69239),f=r(52803),d=r(97692),h=r(7734),y=r(52200),b=class{#T;#o;#M;#k;#Q;#D;#I;#q;constructor(e={}){this.#T=e.queryCache||new l.QueryCache,this.#o=e.mutationCache||new p.MutationCache,this.#M=e.defaultOptions||{},this.#k=new Map,this.#Q=new Map,this.#D=0}mount(){this.#D++,1===this.#D&&(this.#I=f.focusManager.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#T.onFocus())})),this.#q=d.onlineManager.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#T.onOnline())})))}unmount(){this.#D--,0===this.#D&&(this.#I?.(),this.#I=void 0,this.#q?.(),this.#q=void 0)}isFetching(e){return this.#T.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#o.findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return this.#T.get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),r=this.#T.build(this,t),n=r.state.data;return void 0===n?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime((0,c.resolveStaleTime)(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(n))}getQueriesData(e){return this.#T.findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,r){const n=this.defaultQueryOptions({queryKey:e}),o=this.#T.get(n.queryHash),s=o?.state.data,a=(0,c.functionalUpdate)(t,s);if(void 0!==a)return this.#T.build(this,n).setData(a,{...r,manual:!0})}setQueriesData(e,t,r){return h.notifyManager.batch((()=>this.#T.findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,r)]))))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return this.#T.get(t.queryHash)?.state}removeQueries(e){const t=this.#T;h.notifyManager.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const r=this.#T,n={type:"active",...e};return h.notifyManager.batch((()=>(r.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries(n,t))))}cancelQueries(e,t={}){const r={revert:!0,...t},n=h.notifyManager.batch((()=>this.#T.findAll(e).map((e=>e.cancel(r)))));return Promise.all(n).then(c.noop).catch(c.noop)}invalidateQueries(e,t={}){return h.notifyManager.batch((()=>{if(this.#T.findAll(e).forEach((e=>{e.invalidate()})),"none"===e?.refetchType)return Promise.resolve();const r={...e,type:e?.refetchType??e?.type??"active"};return this.refetchQueries(r,t)}))}refetchQueries(e,t={}){const r={...t,cancelRefetch:t.cancelRefetch??!0},n=h.notifyManager.batch((()=>this.#T.findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(c.noop)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(n).then(c.noop)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const r=this.#T.build(this,t);return r.isStaleByTime((0,c.resolveStaleTime)(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(c.noop).catch(c.noop)}fetchInfiniteQuery(e){return e.behavior=(0,y.infiniteQueryBehavior)(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(c.noop).catch(c.noop)}ensureInfiniteQueryData(e){return e.behavior=(0,y.infiniteQueryBehavior)(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return d.onlineManager.isOnline()?this.#o.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#T}getMutationCache(){return this.#o}getDefaultOptions(){return this.#M}setDefaultOptions(e){this.#M=e}setQueryDefaults(e,t){this.#k.set((0,c.hashKey)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#k.values()],r={};return t.forEach((t=>{(0,c.partialMatchKey)(e,t.queryKey)&&Object.assign(r,t.defaultOptions)})),r}setMutationDefaults(e,t){this.#Q.set((0,c.hashKey)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#Q.values()];let r={};return t.forEach((t=>{(0,c.partialMatchKey)(e,t.mutationKey)&&(r={...r,...t.defaultOptions})})),r}defaultQueryOptions(e){if(e._defaulted)return e;const t={...this.#M.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,c.hashQueryKeyByOptions)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===c.skipToken&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#M.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#T.clear(),this.#o.clear()}}},63524:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{QueryObserver:()=>y}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(52803),l=r(7734),p=r(44478),f=r(66133),d=r(89675),h=r(73025),y=class extends f.Subscribable{constructor(e,t){super(),this.options=t,this.#c=e,this.#F=null,this.#A=(0,d.pendingThenable)(),this.options.experimental_prefetchInRender||this.#A.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#c;#L=void 0;#N=void 0;#l=void 0;#U;#K;#A;#F;#W;#B;#H;#G;#z;#$;#V=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#L.addObserver(this),shouldFetchOnMount(this.#L,this.options)?this.#X():this.updateResult(),this.#Y())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return shouldFetchOn(this.#L,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return shouldFetchOn(this.#L,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#J(),this.#Z(),this.#L.removeObserver(this)}setOptions(e,t){const r=this.options,n=this.#L;if(this.options=this.#c.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,h.resolveEnabled)(this.options.enabled,this.#L))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#ee(),this.#L.setOptions(this.options),r._defaulted&&!(0,h.shallowEqualObjects)(this.options,r)&&this.#c.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#L,observer:this});const o=this.hasListeners();o&&shouldFetchOptionally(this.#L,n,this.options,r)&&this.#X(),this.updateResult(t),!o||this.#L===n&&(0,h.resolveEnabled)(this.options.enabled,this.#L)===(0,h.resolveEnabled)(r.enabled,this.#L)&&(0,h.resolveStaleTime)(this.options.staleTime,this.#L)===(0,h.resolveStaleTime)(r.staleTime,this.#L)||this.#te();const s=this.#re();!o||this.#L===n&&(0,h.resolveEnabled)(this.options.enabled,this.#L)===(0,h.resolveEnabled)(r.enabled,this.#L)&&s===this.#$||this.#ne(s)}getOptimisticResult(e){const t=this.#c.getQueryCache().build(this.#c,e),r=this.createResult(t,e);return function shouldAssignObserverCurrentProperties(e,t){if(!(0,h.shallowEqualObjects)(e.getCurrentResult(),t))return!0;return!1}(this,r)&&(this.#l=r,this.#K=this.options,this.#U=this.#L.state),r}getCurrentResult(){return this.#l}trackResult(e,t){const r={};return Object.keys(e).forEach((n=>{Object.defineProperty(r,n,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(n),t?.(n),e[n])})})),r}trackProp(e){this.#V.add(e)}getCurrentQuery(){return this.#L}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#c.defaultQueryOptions(e),r=this.#c.getQueryCache().build(this.#c,t);return r.fetch().then((()=>this.createResult(r,t)))}fetch(e){return this.#X({...e,cancelRefetch:e.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#l)))}#X(e){this.#ee();let t=this.#L.fetch(this.options,e);return e?.throwOnError||(t=t.catch(h.noop)),t}#te(){this.#J();const e=(0,h.resolveStaleTime)(this.options.staleTime,this.#L);if(h.isServer||this.#l.isStale||!(0,h.isValidTimeout)(e))return;const t=(0,h.timeUntilStale)(this.#l.dataUpdatedAt,e)+1;this.#G=setTimeout((()=>{this.#l.isStale||this.updateResult()}),t)}#re(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#L):this.options.refetchInterval)??!1}#ne(e){this.#Z(),this.#$=e,!h.isServer&&!1!==(0,h.resolveEnabled)(this.options.enabled,this.#L)&&(0,h.isValidTimeout)(this.#$)&&0!==this.#$&&(this.#z=setInterval((()=>{(this.options.refetchIntervalInBackground||c.focusManager.isFocused())&&this.#X()}),this.#$))}#Y(){this.#te(),this.#ne(this.#re())}#J(){this.#G&&(clearTimeout(this.#G),this.#G=void 0)}#Z(){this.#z&&(clearInterval(this.#z),this.#z=void 0)}createResult(e,t){const r=this.#L,n=this.options,o=this.#l,s=this.#U,a=this.#K,i=e!==r?e.state:this.#N,{state:u}=e;let c,l={...u},f=!1;if(t._optimisticResults){const o=this.hasListeners(),s=!o&&shouldFetchOnMount(e,t),a=o&&shouldFetchOptionally(e,r,t,n);(s||a)&&(l={...l,...(0,p.fetchState)(u.data,e.options)}),"isRestoring"===t._optimisticResults&&(l.fetchStatus="idle")}let{error:y,errorUpdatedAt:b,status:m}=l;if(t.select&&void 0!==l.data)if(o&&l.data===s?.data&&t.select===this.#W)c=this.#B;else try{this.#W=t.select,c=t.select(l.data),c=(0,h.replaceData)(o?.data,c,t),this.#B=c,this.#F=null}catch(e){this.#F=e}else c=l.data;if(void 0!==t.placeholderData&&void 0===c&&"pending"===m){let e;if(o?.isPlaceholderData&&t.placeholderData===a?.placeholderData)e=o.data;else if(e="function"==typeof t.placeholderData?t.placeholderData(this.#H?.state.data,this.#H):t.placeholderData,t.select&&void 0!==e)try{e=t.select(e),this.#F=null}catch(e){this.#F=e}void 0!==e&&(m="success",c=(0,h.replaceData)(o?.data,e,t),f=!0)}this.#F&&(y=this.#F,c=this.#B,b=Date.now(),m="error");const g="fetching"===l.fetchStatus,v="pending"===m,O="error"===m,_=v&&g,P=void 0!==c,E={status:m,fetchStatus:l.fetchStatus,isPending:v,isSuccess:"success"===m,isError:O,isInitialLoading:_,isLoading:_,data:c,dataUpdatedAt:l.dataUpdatedAt,error:y,errorUpdatedAt:b,failureCount:l.fetchFailureCount,failureReason:l.fetchFailureReason,errorUpdateCount:l.errorUpdateCount,isFetched:l.dataUpdateCount>0||l.errorUpdateCount>0,isFetchedAfterMount:l.dataUpdateCount>i.dataUpdateCount||l.errorUpdateCount>i.errorUpdateCount,isFetching:g,isRefetching:g&&!v,isLoadingError:O&&!P,isPaused:"paused"===l.fetchStatus,isPlaceholderData:f,isRefetchError:O&&P,isStale:isStale(e,t),refetch:this.refetch,promise:this.#A};if(this.options.experimental_prefetchInRender){const finalizeThenableIfPossible=e=>{"error"===E.status?e.reject(E.error):void 0!==E.data&&e.resolve(E.data)},recreateThenable=()=>{const e=this.#A=E.promise=(0,d.pendingThenable)();finalizeThenableIfPossible(e)},t=this.#A;switch(t.status){case"pending":e.queryHash===r.queryHash&&finalizeThenableIfPossible(t);break;case"fulfilled":"error"!==E.status&&E.data===t.value||recreateThenable();break;case"rejected":"error"===E.status&&E.error===t.reason||recreateThenable()}}return E}updateResult(e){const t=this.#l,r=this.createResult(this.#L,this.options);if(this.#U=this.#L.state,this.#K=this.options,void 0!==this.#U.data&&(this.#H=this.#L),(0,h.shallowEqualObjects)(r,t))return;this.#l=r;const n={};!1!==e?.listeners&&(()=>{if(!t)return!0;const{notifyOnChangeProps:e}=this.options,r="function"==typeof e?e():e;if("all"===r||!r&&!this.#V.size)return!0;const n=new Set(r??this.#V);return this.options.throwOnError&&n.add("error"),Object.keys(this.#l).some((e=>{const r=e;return this.#l[r]!==t[r]&&n.has(r)}))})()&&(n.listeners=!0),this.#h({...n,...e})}#ee(){const e=this.#c.getQueryCache().build(this.#c,this.options);if(e===this.#L)return;const t=this.#L;this.#L=e,this.#N=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#Y()}#h(e){l.notifyManager.batch((()=>{e.listeners&&this.listeners.forEach((e=>{e(this.#l)})),this.#c.getQueryCache().notify({query:this.#L,type:"observerResultsUpdated"})}))}};function shouldFetchOnMount(e,t){return function shouldLoadOnMount(e,t){return!1!==(0,h.resolveEnabled)(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)}(e,t)||void 0!==e.state.data&&shouldFetchOn(e,t,t.refetchOnMount)}function shouldFetchOn(e,t,r){if(!1!==(0,h.resolveEnabled)(t.enabled,e)){const n="function"==typeof r?r(e):r;return"always"===n||!1!==n&&isStale(e,t)}return!1}function shouldFetchOptionally(e,t,r,n){return(e!==t||!1===(0,h.resolveEnabled)(n.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&isStale(e,r)}function isStale(e,t){return!1!==(0,h.resolveEnabled)(t.enabled,e)&&e.isStaleByTime((0,h.resolveStaleTime)(t.staleTime,e))}},95181:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{Removable:()=>l}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(73025),l=class{#oe;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,c.isValidTimeout)(this.gcTime)&&(this.#oe=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(c.isServer?1/0:3e5))}clearGcTimeout(){this.#oe&&(clearTimeout(this.#oe),this.#oe=void 0)}}},7785:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{CancelledError:()=>d,canFetch:()=>canFetch,createRetryer:()=>createRetryer,isCancelledError:()=>isCancelledError}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(52803),l=r(97692),p=r(89675),f=r(73025);function defaultRetryDelay(e){return Math.min(1e3*2**e,3e4)}function canFetch(e){return"online"!==(e??"online")||l.onlineManager.isOnline()}var d=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function isCancelledError(e){return e instanceof d}function createRetryer(e){let t,r=!1,n=0,o=!1;const s=(0,p.pendingThenable)(),canContinue=()=>c.focusManager.isFocused()&&("always"===e.networkMode||l.onlineManager.isOnline())&&e.canRun(),canStart=()=>canFetch(e.networkMode)&&e.canRun(),resolve=r=>{o||(o=!0,e.onSuccess?.(r),t?.(),s.resolve(r))},reject=r=>{o||(o=!0,e.onError?.(r),t?.(),s.reject(r))},pause=()=>new Promise((r=>{t=e=>{(o||canContinue())&&r(e)},e.onPause?.()})).then((()=>{t=void 0,o||e.onContinue?.()})),run=()=>{if(o)return;let t;const s=0===n?e.initialPromise:void 0;try{t=s??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(resolve).catch((t=>{if(o)return;const s=e.retry??(f.isServer?0:3),a=e.retryDelay??defaultRetryDelay,i="function"==typeof a?a(n,t):a,u=!0===s||"number"==typeof s&&n<s||"function"==typeof s&&s(n,t);!r&&u?(n++,e.onFail?.(n,t),(0,f.sleep)(i).then((()=>canContinue()?void 0:pause())).then((()=>{r?reject(t):run()}))):reject(t)}))};return{promise:s,cancel:t=>{o||(reject(new d(t)),e.abort?.())},continue:()=>(t?.(),s),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart,start:()=>(canStart()?run():pause().then(run),s)}}},66133:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,a={};((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(a,{Subscribable:()=>i}),e.exports=(t=a,((e,t,a,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))s.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(i=n(t,u))||i.enumerable});return e})(r({},"__esModule",{value:!0}),t));var i=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},89675:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,a={};function pendingThenable(){let e,t;const r=new Promise(((r,n)=>{e=r,t=n}));function finalize(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch((()=>{})),r.resolve=t=>{finalize({status:"fulfilled",value:t}),e(t)},r.reject=e=>{finalize({status:"rejected",reason:e}),t(e)},r}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(a,{pendingThenable:()=>pendingThenable}),e.exports=(t=a,((e,t,a,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))s.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(i=n(t,u))||i.enumerable});return e})(r({},"__esModule",{value:!0}),t))},98813:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty;e.exports=(t={},((e,t,a,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))s.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(i=n(t,u))||i.enumerable});return e})(r({},"__esModule",{value:!0}),t))},73025:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,a={};((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(a,{addToEnd:()=>addToEnd,addToStart:()=>addToStart,ensureQueryFn:()=>ensureQueryFn,functionalUpdate:()=>functionalUpdate,hashKey:()=>hashKey,hashQueryKeyByOptions:()=>hashQueryKeyByOptions,isPlainArray:()=>isPlainArray,isPlainObject:()=>isPlainObject,isServer:()=>i,isValidTimeout:()=>isValidTimeout,keepPreviousData:()=>keepPreviousData,matchMutation:()=>matchMutation,matchQuery:()=>matchQuery,noop:()=>noop,partialMatchKey:()=>partialMatchKey,replaceData:()=>replaceData,replaceEqualDeep:()=>replaceEqualDeep,resolveEnabled:()=>resolveEnabled,resolveStaleTime:()=>resolveStaleTime,shallowEqualObjects:()=>shallowEqualObjects,skipToken:()=>u,sleep:()=>sleep,timeUntilStale:()=>timeUntilStale}),e.exports=(t=a,((e,t,a,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))s.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(i=n(t,u))||i.enumerable});return e})(r({},"__esModule",{value:!0}),t));var i="undefined"==typeof window||"Deno"in globalThis;function noop(){}function functionalUpdate(e,t){return"function"==typeof e?e(t):e}function isValidTimeout(e){return"number"==typeof e&&e>=0&&e!==1/0}function timeUntilStale(e,t){return Math.max(e+(t||0)-Date.now(),0)}function resolveStaleTime(e,t){return"function"==typeof e?e(t):e}function resolveEnabled(e,t){return"function"==typeof e?e(t):e}function matchQuery(e,t){const{type:r="all",exact:n,fetchStatus:o,predicate:s,queryKey:a,stale:i}=e;if(a)if(n){if(t.queryHash!==hashQueryKeyByOptions(a,t.options))return!1}else if(!partialMatchKey(t.queryKey,a))return!1;if("all"!==r){const e=t.isActive();if("active"===r&&!e)return!1;if("inactive"===r&&e)return!1}return("boolean"!=typeof i||t.isStale()===i)&&((!o||o===t.state.fetchStatus)&&!(s&&!s(t)))}function matchMutation(e,t){const{exact:r,status:n,predicate:o,mutationKey:s}=e;if(s){if(!t.options.mutationKey)return!1;if(r){if(hashKey(t.options.mutationKey)!==hashKey(s))return!1}else if(!partialMatchKey(t.options.mutationKey,s))return!1}return(!n||t.state.status===n)&&!(o&&!o(t))}function hashQueryKeyByOptions(e,t){return(t?.queryKeyHashFn||hashKey)(e)}function hashKey(e){return JSON.stringify(e,((e,t)=>isPlainObject(t)?Object.keys(t).sort().reduce(((e,r)=>(e[r]=t[r],e)),{}):t))}function partialMatchKey(e,t){return e===t||typeof e==typeof t&&(!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&!Object.keys(t).some((r=>!partialMatchKey(e[r],t[r]))))}function replaceEqualDeep(e,t){if(e===t)return e;const r=isPlainArray(e)&&isPlainArray(t);if(r||isPlainObject(e)&&isPlainObject(t)){const n=r?e:Object.keys(e),o=n.length,s=r?t:Object.keys(t),a=s.length,i=r?[]:{};let u=0;for(let o=0;o<a;o++){const a=r?o:s[o];(!r&&n.includes(a)||r)&&void 0===e[a]&&void 0===t[a]?(i[a]=void 0,u++):(i[a]=replaceEqualDeep(e[a],t[a]),i[a]===e[a]&&void 0!==e[a]&&u++)}return o===a&&u===o?e:i}return t}function shallowEqualObjects(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}function isPlainArray(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function isPlainObject(e){if(!hasObjectPrototype(e))return!1;const t=e.constructor;if(void 0===t)return!0;const r=t.prototype;return!!hasObjectPrototype(r)&&(!!r.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype)}function hasObjectPrototype(e){return"[object Object]"===Object.prototype.toString.call(e)}function sleep(e){return new Promise((t=>{setTimeout(t,e)}))}function replaceData(e,t,r){return"function"==typeof r.structuralSharing?r.structuralSharing(e,t):!1!==r.structuralSharing?replaceEqualDeep(e,t):t}function keepPreviousData(e){return e}function addToEnd(e,t,r=0){const n=[...e,t];return r&&n.length>r?n.slice(1):n}function addToStart(e,t,r=0){const n=[t,...e];return r&&n.length>r?n.slice(0,-1):n}var u=Symbol();function ensureQueryFn(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==u?e.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`))}},84776:(e,t,r)=>{"use strict";var n,o=Object.create,s=Object.defineProperty,a=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of i(t))c.call(e,o)||o===r||s(e,o,{get:()=>t[o],enumerable:!(n=a(t,o))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(l,{HydrationBoundary:()=>HydrationBoundary}),e.exports=(n=l,__copyProps(s({},"__esModule",{value:!0}),n));var p=((e,t,r)=>(r=null!=e?o(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),f=r(51934),d=r(59762),HydrationBoundary=({children:e,options:t={},state:r,queryClient:n})=>{const o=(0,d.useQueryClient)(n),[s,a]=p.useState(),i=p.useRef(t);return i.current=t,p.useMemo((()=>{if(r){if("object"!=typeof r)return;const e=o.getQueryCache(),t=r.queries||[],n=[],u=[];for(const r of t){const t=e.get(r.queryHash);if(t){const e=r.state.dataUpdatedAt>t.state.dataUpdatedAt,n=s?.find((e=>e.queryHash===r.queryHash));e&&(!n||r.state.dataUpdatedAt>n.state.dataUpdatedAt)&&u.push(r)}else n.push(r)}n.length>0&&(0,f.hydrate)(o,{queries:n},i.current),u.length>0&&a((e=>e?[...e,...u]:u))}}),[o,s,r]),p.useEffect((()=>{s&&((0,f.hydrate)(o,{queries:s},i.current),a(void 0))}),[o,s]),e}},59762:(e,t,r)=>{"use strict";var n,o=Object.create,s=Object.defineProperty,a=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of i(t))c.call(e,o)||o===r||s(e,o,{get:()=>t[o],enumerable:!(n=a(t,o))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(l,{QueryClientContext:()=>d,QueryClientProvider:()=>QueryClientProvider,useQueryClient:()=>useQueryClient}),e.exports=(n=l,__copyProps(s({},"__esModule",{value:!0}),n));var p=((e,t,r)=>(r=null!=e?o(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),f=r(62540),d=p.createContext(void 0),useQueryClient=e=>{const t=p.useContext(d);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},QueryClientProvider=({client:e,children:t})=>(p.useEffect((()=>(e.mount(),()=>{e.unmount()})),[e]),(0,f.jsx)(d.Provider,{value:e,children:t}))},16729:(e,t,r)=>{"use strict";var n,o=Object.create,s=Object.defineProperty,a=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of i(t))c.call(e,o)||o===r||s(e,o,{get:()=>t[o],enumerable:!(n=a(t,o))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(l,{QueryErrorResetBoundary:()=>QueryErrorResetBoundary,useQueryErrorResetBoundary:()=>useQueryErrorResetBoundary}),e.exports=(n=l,__copyProps(s({},"__esModule",{value:!0}),n));var p=((e,t,r)=>(r=null!=e?o(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),f=r(62540);function createValue(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var d=p.createContext(createValue()),useQueryErrorResetBoundary=()=>p.useContext(d),QueryErrorResetBoundary=({children:e})=>{const[t]=p.useState((()=>createValue()));return(0,f.jsx)(d.Provider,{value:t,children:"function"==typeof e?e(t):e})}},74151:(e,t,r)=>{"use strict";var n,o=Object.create,s=Object.defineProperty,a=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of i(t))c.call(e,o)||o===r||s(e,o,{get:()=>t[o],enumerable:!(n=a(t,o))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(l,{ensurePreventErrorBoundaryRetry:()=>ensurePreventErrorBoundaryRetry,getHasError:()=>getHasError,useClearResetErrorBoundary:()=>useClearResetErrorBoundary}),e.exports=(n=l,__copyProps(s({},"__esModule",{value:!0}),n));var p=((e,t,r)=>(r=null!=e?o(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),f=r(63315),ensurePreventErrorBoundaryRetry=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},useClearResetErrorBoundary=e=>{p.useEffect((()=>{e.clearReset()}),[e])},getHasError=({result:e,errorResetBoundary:t,throwOnError:r,query:n})=>e.isError&&!t.isReset()&&!e.isFetching&&n&&(0,f.shouldThrowError)(r,[e.error,n])},51688:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e},__reExport=(e,t,r)=>(__copyProps(e,t,"default"),r&&__copyProps(r,t,"default")),u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{HydrationBoundary:()=>v.HydrationBoundary,IsRestoringProvider:()=>S.IsRestoringProvider,QueryClientContext:()=>g.QueryClientContext,QueryClientProvider:()=>g.QueryClientProvider,QueryErrorResetBoundary:()=>O.QueryErrorResetBoundary,infiniteQueryOptions:()=>m.infiniteQueryOptions,queryOptions:()=>b.queryOptions,useInfiniteQuery:()=>w.useInfiniteQuery,useIsFetching:()=>_.useIsFetching,useIsMutating:()=>P.useIsMutating,useIsRestoring:()=>S.useIsRestoring,useMutation:()=>E.useMutation,useMutationState:()=>P.useMutationState,usePrefetchInfiniteQuery:()=>y.usePrefetchInfiniteQuery,usePrefetchQuery:()=>h.usePrefetchQuery,useQueries:()=>c.useQueries,useQuery:()=>l.useQuery,useQueryClient:()=>g.useQueryClient,useQueryErrorResetBoundary:()=>O.useQueryErrorResetBoundary,useSuspenseInfiniteQuery:()=>f.useSuspenseInfiniteQuery,useSuspenseQueries:()=>d.useSuspenseQueries,useSuspenseQuery:()=>p.useSuspenseQuery}),e.exports=(n=u,__copyProps(o({},"__esModule",{value:!0}),n)),__reExport(u,r(51934),e.exports),__reExport(u,r(62143),e.exports);var c=r(37963),l=r(16743),p=r(37063),f=r(28619),d=r(44459),h=r(94860),y=r(48380),b=r(5938),m=r(28218),g=r(59762),v=r(84776),O=r(16729),_=r(81809),P=r(26803),E=r(3188),w=r(81355),S=r(21683)},28218:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,a={};function infiniteQueryOptions(e){return e}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(a,{infiniteQueryOptions:()=>infiniteQueryOptions}),e.exports=(t=a,((e,t,a,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))s.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(i=n(t,u))||i.enumerable});return e})(r({},"__esModule",{value:!0}),t))},21683:(e,t,r)=>{"use strict";var n,o=Object.create,s=Object.defineProperty,a=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of i(t))c.call(e,o)||o===r||s(e,o,{get:()=>t[o],enumerable:!(n=a(t,o))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(l,{IsRestoringProvider:()=>d,useIsRestoring:()=>useIsRestoring}),e.exports=(n=l,__copyProps(s({},"__esModule",{value:!0}),n));var p=((e,t,r)=>(r=null!=e?o(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),f=p.createContext(!1),useIsRestoring=()=>p.useContext(f),d=f.Provider},5938:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,a={};function queryOptions(e){return e}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(a,{queryOptions:()=>queryOptions}),e.exports=(t=a,((e,t,a,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))s.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(i=n(t,u))||i.enumerable});return e})(r({},"__esModule",{value:!0}),t))},92816:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,a={};((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(a,{defaultThrowOnError:()=>defaultThrowOnError,ensureSuspenseTimers:()=>ensureSuspenseTimers,fetchOptimistic:()=>fetchOptimistic,shouldSuspend:()=>shouldSuspend,willFetch:()=>willFetch}),e.exports=(t=a,((e,t,a,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))s.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(i=n(t,u))||i.enumerable});return e})(r({},"__esModule",{value:!0}),t));var defaultThrowOnError=(e,t)=>void 0===t.state.data,ensureSuspenseTimers=e=>{e.suspense&&(void 0===e.staleTime&&(e.staleTime=1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))},willFetch=(e,t)=>e.isLoading&&e.isFetching&&!t,shouldSuspend=(e,t)=>e?.suspense&&t.isPending,fetchOptimistic=(e,t,r)=>t.fetchOptimistic(e).catch((()=>{r.clearReset()}))},62143:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty;e.exports=(t={},((e,t,a,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))s.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(i=n(t,u))||i.enumerable});return e})(r({},"__esModule",{value:!0}),t))},35442:(e,t,r)=>{"use strict";var n,o=Object.create,s=Object.defineProperty,a=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of i(t))c.call(e,o)||o===r||s(e,o,{get:()=>t[o],enumerable:!(n=a(t,o))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(l,{useBaseQuery:()=>useBaseQuery}),e.exports=(n=l,__copyProps(s({},"__esModule",{value:!0}),n));var p=((e,t,r)=>(r=null!=e?o(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),f=r(51934),d=r(59762),h=r(16729),y=r(74151),b=r(21683),m=r(92816),g=r(63315);function useBaseQuery(e,t,r){const n=(0,d.useQueryClient)(r),o=(0,b.useIsRestoring)(),s=(0,h.useQueryErrorResetBoundary)(),a=n.defaultQueryOptions(e);n.getDefaultOptions().queries?._experimental_beforeQuery?.(a),a._optimisticResults=o?"isRestoring":"optimistic",(0,m.ensureSuspenseTimers)(a),(0,y.ensurePreventErrorBoundaryRetry)(a,s),(0,y.useClearResetErrorBoundary)(s);const i=!n.getQueryCache().get(a.queryHash),[u]=p.useState((()=>new t(n,a))),c=u.getOptimisticResult(a);if(p.useSyncExternalStore(p.useCallback((e=>{const t=o?g.noop:u.subscribe(f.notifyManager.batchCalls(e));return u.updateResult(),t}),[u,o]),(()=>u.getCurrentResult()),(()=>u.getCurrentResult())),p.useEffect((()=>{u.setOptions(a,{listeners:!1})}),[a,u]),(0,m.shouldSuspend)(a,c))throw(0,m.fetchOptimistic)(a,u,s);if((0,y.getHasError)({result:c,errorResetBoundary:s,throwOnError:a.throwOnError,query:n.getQueryCache().get(a.queryHash)}))throw c.error;if(n.getDefaultOptions().queries?._experimental_afterQuery?.(a,c),a.experimental_prefetchInRender&&!f.isServer&&(0,m.willFetch)(c,o)){const e=i?(0,m.fetchOptimistic)(a,u,s):n.getQueryCache().get(a.queryHash)?.promise;e?.catch(g.noop).finally((()=>{u.updateResult()}))}return a.notifyOnChangeProps?c:u.trackResult(c)}},81355:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{useInfiniteQuery:()=>useInfiniteQuery}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(51934),l=r(35442);function useInfiniteQuery(e,t){return(0,l.useBaseQuery)(e,c.InfiniteQueryObserver,t)}},81809:(e,t,r)=>{"use strict";var n,o=Object.create,s=Object.defineProperty,a=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of i(t))c.call(e,o)||o===r||s(e,o,{get:()=>t[o],enumerable:!(n=a(t,o))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(l,{useIsFetching:()=>useIsFetching}),e.exports=(n=l,__copyProps(s({},"__esModule",{value:!0}),n));var p=((e,t,r)=>(r=null!=e?o(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),f=r(51934),d=r(59762);function useIsFetching(e,t){const r=(0,d.useQueryClient)(t),n=r.getQueryCache();return p.useSyncExternalStore(p.useCallback((e=>n.subscribe(f.notifyManager.batchCalls(e))),[n]),(()=>r.isFetching(e)),(()=>r.isFetching(e)))}},3188:(e,t,r)=>{"use strict";var n,o=Object.create,s=Object.defineProperty,a=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of i(t))c.call(e,o)||o===r||s(e,o,{get:()=>t[o],enumerable:!(n=a(t,o))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(l,{useMutation:()=>useMutation}),e.exports=(n=l,__copyProps(s({},"__esModule",{value:!0}),n));var p=((e,t,r)=>(r=null!=e?o(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),f=r(51934),d=r(59762),h=r(63315);function useMutation(e,t){const r=(0,d.useQueryClient)(t),[n]=p.useState((()=>new f.MutationObserver(r,e)));p.useEffect((()=>{n.setOptions(e)}),[n,e]);const o=p.useSyncExternalStore(p.useCallback((e=>n.subscribe(f.notifyManager.batchCalls(e))),[n]),(()=>n.getCurrentResult()),(()=>n.getCurrentResult())),s=p.useCallback(((e,t)=>{n.mutate(e,t).catch(h.noop)}),[n]);if(o.error&&(0,h.shouldThrowError)(n.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:s,mutateAsync:o.mutate}}},26803:(e,t,r)=>{"use strict";var n,o=Object.create,s=Object.defineProperty,a=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of i(t))c.call(e,o)||o===r||s(e,o,{get:()=>t[o],enumerable:!(n=a(t,o))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(l,{useIsMutating:()=>useIsMutating,useMutationState:()=>useMutationState}),e.exports=(n=l,__copyProps(s({},"__esModule",{value:!0}),n));var p=((e,t,r)=>(r=null!=e?o(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),f=r(51934),d=r(59762);function useIsMutating(e,t){return useMutationState({filters:{...e,status:"pending"}},(0,d.useQueryClient)(t)).length}function getResult(e,t){return e.findAll(t.filters).map((e=>t.select?t.select(e):e.state))}function useMutationState(e={},t){const r=(0,d.useQueryClient)(t).getMutationCache(),n=p.useRef(e),o=p.useRef(null);return o.current||(o.current=getResult(r,e)),p.useEffect((()=>{n.current=e})),p.useSyncExternalStore(p.useCallback((e=>r.subscribe((()=>{const t=(0,f.replaceEqualDeep)(o.current,getResult(r,n.current));o.current!==t&&(o.current=t,f.notifyManager.schedule(e))}))),[r]),(()=>o.current),(()=>o.current))}},48380:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{usePrefetchInfiniteQuery:()=>usePrefetchInfiniteQuery}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(59762);function usePrefetchInfiniteQuery(e,t){const r=(0,c.useQueryClient)(t);r.getQueryState(e.queryKey)||r.prefetchInfiniteQuery(e)}},94860:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{usePrefetchQuery:()=>usePrefetchQuery}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(59762);function usePrefetchQuery(e,t){const r=(0,c.useQueryClient)(t);r.getQueryState(e.queryKey)||r.prefetchQuery(e)}},37963:(e,t,r)=>{"use strict";var n,o=Object.create,s=Object.defineProperty,a=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of i(t))c.call(e,o)||o===r||s(e,o,{get:()=>t[o],enumerable:!(n=a(t,o))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(l,{useQueries:()=>useQueries}),e.exports=(n=l,__copyProps(s({},"__esModule",{value:!0}),n));var p=((e,t,r)=>(r=null!=e?o(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:s(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),f=r(51934),d=r(59762),h=r(21683),y=r(16729),b=r(74151),m=r(92816),g=r(63315);function useQueries({queries:e,...t},r){const n=(0,d.useQueryClient)(r),o=(0,h.useIsRestoring)(),s=(0,y.useQueryErrorResetBoundary)(),a=p.useMemo((()=>e.map((e=>{const t=n.defaultQueryOptions(e);return t._optimisticResults=o?"isRestoring":"optimistic",t}))),[e,n,o]);a.forEach((e=>{(0,m.ensureSuspenseTimers)(e),(0,b.ensurePreventErrorBoundaryRetry)(e,s)})),(0,b.useClearResetErrorBoundary)(s);const[i]=p.useState((()=>new f.QueriesObserver(n,a,t))),[u,c,l]=i.getOptimisticResult(a,t.combine);p.useSyncExternalStore(p.useCallback((e=>o?g.noop:i.subscribe(f.notifyManager.batchCalls(e))),[i,o]),(()=>i.getCurrentResult()),(()=>i.getCurrentResult())),p.useEffect((()=>{i.setQueries(a,t,{listeners:!1})}),[a,t,i]);const v=u.some(((e,t)=>(0,m.shouldSuspend)(a[t],e)))?u.flatMap(((e,t)=>{const r=a[t];if(r){const t=new f.QueryObserver(n,r);if((0,m.shouldSuspend)(r,e))return(0,m.fetchOptimistic)(r,t,s);(0,m.willFetch)(e,o)&&(0,m.fetchOptimistic)(r,t,s)}return[]})):[];if(v.length>0)throw Promise.all(v);const O=u.find(((e,t)=>{const r=a[t];return r&&(0,b.getHasError)({result:e,errorResetBoundary:s,throwOnError:r.throwOnError,query:n.getQueryCache().get(r.queryHash)})}));if(O?.error)throw O.error;return c(l())}},16743:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{useQuery:()=>useQuery}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(51934),l=r(35442);function useQuery(e,t){return(0,l.useBaseQuery)(e,c.QueryObserver,t)}},28619:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{useSuspenseInfiniteQuery:()=>useSuspenseInfiniteQuery}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(51934),l=r(35442),p=r(92816);function useSuspenseInfiniteQuery(e,t){return(0,l.useBaseQuery)({...e,enabled:!0,suspense:!0,throwOnError:p.defaultThrowOnError},c.InfiniteQueryObserver,t)}},44459:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{useSuspenseQueries:()=>useSuspenseQueries}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));r(51934);var c=r(37963),l=r(92816);function useSuspenseQueries(e,t){return(0,c.useQueries)({...e,queries:e.queries.map((e=>({...e,suspense:!0,throwOnError:l.defaultThrowOnError,enabled:!0,placeholderData:void 0})))},t)}},37063:(e,t,r)=>{"use strict";var n,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(u,{useSuspenseQuery:()=>useSuspenseQuery}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))i.call(e,u)||u===r||o(e,u,{get:()=>t[u],enumerable:!(n=s(t,u))||n.enumerable});return e})(o({},"__esModule",{value:!0}),n));var c=r(51934),l=r(35442),p=r(92816);function useSuspenseQuery(e,t){return(0,l.useBaseQuery)({...e,enabled:!0,suspense:!0,throwOnError:p.defaultThrowOnError,placeholderData:void 0},c.QueryObserver,t)}},63315:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,a={};function shouldThrowError(e,t){return"function"==typeof e?e(...t):!!e}function noop(){}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(a,{noop:()=>noop,shouldThrowError:()=>shouldThrowError}),e.exports=(t=a,((e,t,a,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))s.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(i=n(t,u))||i.enumerable});return e})(r({},"__esModule",{value:!0}),t))}},t={};function __webpack_require__(r){var n=t[r];if(void 0!==n)return n.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,__webpack_require__),o.exports}(()=>{"use strict";var e=__webpack_require__(96784),t=e(__webpack_require__(61790)),r=e(__webpack_require__(58155)),n=__webpack_require__(83725),o=e(__webpack_require__(46352)),s=__webpack_require__(55041),a=__webpack_require__(20244);function checklistStartup(){return _checklistStartup.apply(this,arguments)}function _checklistStartup(){return(_checklistStartup=(0,r.default)(t.default.mark((function _callee(){var e;return t.default.wrap((function _callee$(t){for(;;)switch(t.prev=t.next){case 0:if(!("yes"!==elementor.getPreferences("show_launchpad_checklist"))){t.next=5;break}$e.commands.run("checklist/toggle-icon",!1),t.next=9;break;case 5:return t.next=7,(0,a.fetchUserProgress)();case 7:null!=(e=t.sent)&&e[s.USER_PROGRESS.SHOULD_OPEN_IN_EDITOR]&&((0,a.toggleChecklistPopup)(),(0,a.dispatchChecklistOpenEvent)());case 9:elementor.off("document:loaded",checklistStartup);case 10:case"end":return t.stop()}}),_callee)})))).apply(this,arguments)}$e.components.register(new o.default),(0,n.editorAppBarLink)(),elementorCommon.elements.$window.on("elementor:loaded",(function elementorLoaded(){elementor.on("document:loaded",checklistStartup),elementorCommon.elements.$window.off("elementor:loaded",elementorLoaded)}))})()})();