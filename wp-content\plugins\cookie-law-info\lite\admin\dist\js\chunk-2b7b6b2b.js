(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2b7b6b2b"],{"049b":function(t,e,i){},"0ac9":function(t,e,i){t.exports=i.p+"img/add-language.svg"},2477:function(t,e,i){},3136:function(t,e,i){"use strict";i("049b")},"37f1":function(t,e,i){t.exports=i.p+"img/cookieyes-logo.svg"},"4a95":function(t,e,i){t.exports=i.p+"img/status.svg"},"4abb":function(t,e,i){},"50c3":function(t,e,i){t.exports=i.p+"img/arrow-right.svg"},5544:function(t,e,i){t.exports=i.p+"img/location.svg"},"57e4":function(t,e,i){"use strict";i("ed2e")},"5fb2":function(t,e,i){"use strict";i("4abb")},"6b78":function(t,e,i){"use strict";i("ddbc")},7478:function(t,e,i){"use strict";i("2477")},7630:function(t,e,i){},"78d0":function(t,e,i){t.exports=i.p+"img/reg.svg"},8628:function(t,e,i){},9133:function(t,e,i){"use strict";i("8628")},9573:function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t._self._c;return"/dashboard/plans"===t.$route.path?e("div",[e("transition",{attrs:{name:"fade"}},[e("router-view")],1)],1):e("div",{staticClass:"cky-section cky-section-dashboard cky-zero-padding cky-zero--margin"},[e("div",{staticClass:"cky-row"},[e("div",{staticClass:"cky-col-12"},[e("notice-migration"),t.isSuspended||t.isTrialSubscriptionCancelled||t.isPaidSubscriptionCancelled||t.isPaymentMethodMissing?e("notice-suspended"):t._e(),e("cky-connect-success")],1)]),e("div",{class:[t.connected?"cky-row":"cky-column"]},[e("div",{class:[t.connected?"cky-col-9":"cky-col-10"]},[e("cky-connect-notice"),e("cky-dashboard-overview"),!t.connected||t.loading||t.syncing?t._e():e("div",{staticClass:"cky-section-content"},[e("div",{staticClass:"cky-section-row"},[e("div",{staticClass:"cky-col-7"},[e("cky-scan-summary")],1),e("div",{staticClass:"cky-col-5"},[e("cky-consent-chart")],1)]),e("div",{staticClass:"cky-section-row"},[e("cky-pageview-graph")],1)])],1),t.connected&&!t.loading?e("div",{staticClass:"cky-section-upgrade cky-col-3"},[e("accessYes-widget-promo"),e("cky-faq-widget")],1):t._e()])])},n=[],a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cky-scan-summary-section"},[e("cky-card",{attrs:{title:t.$i18n.__("Cookie Summary","cookie-law-info"),loading:t.cardLoader},scopedSlots:t._u([{key:"body",fn:function(){return[e("div",{staticClass:"cky-stats-section"},t._l(t.statistics,(function(t){return e("cky-stats-card",{key:t.slug,attrs:{statistics:t}})})),1)]},proxy:!0}])})],1)},o=[],c=i("f9c4"),r=i("9610"),l=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cky-stats-col"},[e("div",{staticClass:"cky-row"},[t.statistics.icon?e("div",{staticClass:"cky-stats-icon"},[e("cky-icon",{attrs:{icon:t.statistics.icon,width:t.iconWidth,color:t.iconColor}})],1):t._e(),e("div",{staticClass:"cky-stats-content"},[e("div",{staticClass:"cky-stats-title"},[t._v(t._s(t.statistics.title))]),"lastscan"!==t.statistics.slug?e("div",{staticClass:"cky-stats-count"},[t._v(t._s(t.statistics.count))]):e("div",{staticClass:"cky-stats-count"},[t.successScan.date&&t.account.connected?e("span",{staticStyle:{"font-size":"14px"}},[t._v(" "+t._s(t.successScan.date.date||t.$i18n.__("Not available","cookie-law-info"))+" "),e("span",[t._v(t._s(t.successScan.date.time||""))])]):e("span",[t._v(t._s(t.$i18n.__("Not available","cookie-law-info")))])])])])])},d=[],u=i("1f3d"),p={components:{CkyIcon:u["a"]},name:"CkyStatsCard",props:{statistics:Object,iconWidth:{type:String,default:"30"},iconColor:{type:String,default:"#000000"}},computed:{getLoadingClass(){return{"cky-loading":this.loading}},account(){return this.getOption("account")},successScan(){return this.getInfo("success_scan")||{}}}},y=p,g=(i("6b78"),i("2877")),k=Object(g["a"])(y,l,d,!1,null,null,null),f=k.exports,h={name:"CkyScanSummary",components:{CkyCard:r["a"],CkyStatsCard:f},data(){return{loading:!0,stats:[{slug:"cookies",icon:"cookie",title:this.$i18n.__("Total cookies","cookie-law-info"),count:0},{slug:"categories",icon:"categories",title:this.$i18n.__("Categories","cookie-law-info"),count:0},{slug:"lastscan",icon:"scan",title:this.$i18n.__("Last successful scan (UTC)","cookie-law-info"),count:"Not available"},{slug:"pages",icon:"pages",title:this.$i18n.__("Pages scanned","cookie-law-info"),count:0}]}},methods:{async getstats(){this.loading=!0;try{const t=await c["a"].get({path:"dashboard/summary"});t&&this.stats.forEach((function(e){const i=t[e.slug]?t[e.slug]:0;e.count=i})),this.loading=!1}catch(t){console.error(t)}}},computed:{statistics(){return this.stats},cardLoader(){return!this.$store.state.settings.info||this.loading}},created(){this.getstats()}},_=h,w=(i("c340"),Object(g["a"])(_,a,o,!1,null,null,null)),b=w.exports,m=function(){var t=this,e=t._self._c;return t.showNotice?e("cky-notice",{ref:"ReviewNotice",staticClass:"cky-notice-migration",attrs:{type:"info"}},[e("div",{staticClass:"cky-row cky-align-center"},[e("div",{staticClass:"cky-col-12"},[e("div",{staticClass:"cky-align-center"},[e("p",{staticStyle:{"margin-bottom":"5px","margin-right":"15px"}},[e("b",[t._v(t._s(t.message)+" ")])]),e("a",{staticClass:"cky-button cky-button-outline",attrs:{href:t.legacyURL}},[t._v(" "+t._s(t.$i18n.__("Switch back to old UI","cookie-law-info"))+" ")])])])])]):t._e()},v=[],C=i("462b"),$={name:"NoticeMigration",components:{CkyNotice:C["a"]},data(){return{showNotice:!!window.ckyAppNotices.migration_notice,legacyURL:window.ckyGlobals.legacyURL}},computed:{message(){return this.showNotice&&window.ckyAppNotices.migration_notice.message||""}},methods:{async removeNotice(){await c["a"].post({path:"/settings/notices/migration_notice"}),this.$refs.ReviewNotice.isShown=!1},async switchToLegacy(){await c["a"].post({path:"/settings/legacy"})}},mounted(){}},x=$,S=(i("d407"),Object(g["a"])(x,m,v,!1,null,null,null)),A=S.exports,I=function(){var t=this,e=t._self._c;return e("cky-notice",{ref:"SuspendedNotice",staticClass:"cky-notice-suspended",attrs:{type:"warning"}},[e("div",{staticClass:"cky-row cky-align-center cky-suspended-notice"},[e("div",{staticClass:"cky-warning-icon"},[e("img",{attrs:{src:i("fb71"),alt:"warning"}})]),e("div",{staticClass:"cky-suspended-message-container"},[e("div",{staticClass:"cky-suspended-message-header"},[e("h4",[t._v(" "+t._s(t.noticeContent.header)+" ")])]),e("div",{staticClass:"cky-suspended-message"},[e("p",{domProps:{innerHTML:t._s(t.noticeContent.message)}})]),e("div",{staticClass:"cky-suspended-note"},[e("p",{domProps:{innerHTML:t._s(t.note)}})])]),e("div",{staticClass:"cky-suspended-actions"},[e("cky-button",{staticClass:"cky-external-link",nativeOn:{click:function(e){return t.noticeContent.buttonAction.apply(null,arguments)}}},[t._v(" "+t._s(t.noticeContent.buttonText)+" ")])],1)])])},L=[],T={name:"NoticeSuspended",components:{CkyNotice:C["a"]},computed:{getWebsiteInfo(){return this.getInfo("website")},isSuspended(){return this.getWebsiteInfo&&"suspended"===this.getWebsiteInfo.status},isSubscriptionCancelled(){return this.getWebsiteInfo&&"subscriptionCancelled"===this.getWebsiteInfo.status},isTrial(){return this.getWebsiteInfo&&this.getWebsiteInfo.is_trial},isDraft(){return"draft"===this.getInfo("status")},gracePeriod(){return this.getWebsiteInfo&&this.getWebsiteInfo.grace_period_ends_at?this.getWebsiteInfo.grace_period_ends_at:0},noticeContent(){const t={header:"",message:"",buttonText:"",buttonAction:()=>{}},e=(t,e)=>this.$i18n.sprintf(this.$i18n.__(t,"cookie-law-info"),e),i=()=>{const t=this.$router.getAppRedirectURL("settings/organizations-and-sites");window.open(t,"_blank")};if(this.isSubscriptionCancelled){const s=this.isTrial&&!this.getWebsiteInfo.payment_status;t.header=this.$i18n.__(s?"Your trial subscription is due for cancellation":"Your subscription is due for cancellation","cookie-law-info"),t.message=e(s?"Your trial subscription will be cancelled at the end of your billing period on <b>%s</b>, and your site will be removed from the web app account":"Your subscription will be cancelled at the end of your billing period on <b>%s</b>, and your site will be removed from the web app account.",this.gracePeriod).replace("%s",s?"trial subscription":"subscription"),t.buttonText=this.$i18n.__("Reactivate subscription","cookie-law-info"),t.buttonAction=i}else this.getWebsiteInfo.payment_status||(this.isTrial&&!this.isSubscriptionCancelled&&this.isDraft?(t.header=this.$i18n.__("Start your free trial to activate your banner","cookie-law-info"),t.message=e("Your cookie banner is currently inactive. Add a payment method to start your 14-day free trial and activate your banner. If you don't proceed with the trial by <b>%s</b>,your site will be removed from the web app account",this.gracePeriod),t.buttonText=this.$i18n.__("Start your free trial","cookie-law-info"),t.buttonAction=()=>{const t=this.$router.redirectToAppWithQuery({website:this.getInfo("id"),startTrial:!0});window.open(t,"_blank")}):"paymentMethodMissingPaid"!==this.getWebsiteInfo.status||this.isTrial?"free"===this.getWebsiteInfo.selected_plan&&!this.isTrial||this.isTrial&&"free"!==this.getWebsiteInfo.selected_plan&&this.isSuspended?(t.header=this.$i18n.__("Select a plan to activate your banner","cookie-law-info"),t.message=e("Your cookie banner is currently inactive. Choose a plan to activate your banner and unlock advanced customisation and features. If you don't proceed with a plan by  <b>%s</b>, your site will be removed from the web app account.",this.gracePeriod),t.buttonText=this.$i18n.__("Select a plan","cookie-law-info"),t.buttonAction=()=>{const t=this.$router.getAppRedirectURL("wp-plan-selector");window.open(t,"_blank")}):!this.isTrial&&"free"!==this.getWebsiteInfo.selected_plan&&this.isSuspended&&(t.header=this.$i18n.__("Complete your payment to activate your banner","cookie-law-info"),t.message=e("Your site is currently suspended and your cookie banner is inactive due to payment failure. Complete your payment to activate the banner. If you don't proceed with the payment by  <b>%s</b>, your site will be removed from the web app account",this.gracePeriod),t.buttonText=this.$i18n.__("Complete your payment","cookie-law-info"),t.buttonAction=i):(t.header=this.$i18n.__("No payment method available for this site","cookie-law-info"),t.message=e("Add a payment method before the next renewal date, <b>%s</b>, to avoid suspension of your site. If no payment method is added by this date, your site will be removed from the web app account within 30 days of suspension.",this.gracePeriod),t.buttonText=this.$i18n.__("Add payment method","cookie-law-info"),t.buttonAction=i));return t},note(){return this.$i18n.sprintf(this.$i18n.__("%sNote:%s To use the standalone plugin, you can <a href='%s'>disconnect</a> from the web app. However, you'll lose advanced features and customizations.","cookie-law-info"),"<b>","</b>","admin.php?page=cookie-law-info#/settings")}}},P=T,M=(i("5fb2"),Object(g["a"])(P,I,L,!1,null,null,null)),W=M.exports,U=i("919d"),B=function(){var t=this,e=t._self._c;return t.account.connected&&!t.syncing&&t.pluginStatus?e("cky-notice",{staticClass:"cky-connect-notice",attrs:{type:"default"}},[e("div",{staticClass:"cky-row"},[e("div",{staticClass:"cky-col-12"},[e("h4",{staticClass:"cky-admin-notice-header"},[e("cky-icon",{attrs:{icon:"successCircle",color:"#00aa63",width:"16px"}}),t._v(" "+t._s(t.$i18n.__("Your website is connected to CookieYes web app","cookie-law-info"))+" ")],1),e("div",{staticClass:"cky-connect-notice-message"},[e("p",[t._v(" "+t._s(t.$i18n.__("You can access all the plugin settings (Cookie Banner, Cookie Manager, Languages and Policy Generators) on the web app and unlock new features like Cookie Scanner and Consent Log.","cookie-law-info"))+" ")])]),e("button",{staticClass:"cky-button cky-external-link",on:{click:function(e){return e.preventDefault(),t.$router.redirectToApp()}}},[t._v(" "+t._s(t.$i18n.__("Go to Web App","cookie-law-info"))+" ")])])])]):t.tablesMissing?t._e():e("div",{staticClass:"cky-connect-notice cky-connect-notice-disabled",attrs:{type:"default"}},[e("div",{staticClass:"cky-row cky-align-center"},[e("div",{staticClass:"cky-col-12"},[e("cky-connect-card",{ref:"ckycard",staticClass:"cky-connect-card",attrs:{title:t.$i18n.__("Get started with CookieYes","cookie-law-info"),tagline:t.$i18n.__("Welcome to CookieYes! To become legally compliant for your use of cookies, here’s what you need to do.","cookie-law-info"),showIcon:!0},scopedSlots:t._u([{key:"body",fn:function(){return[e("div",{staticClass:"cky-connect-step cky-row"},[e("div",{staticClass:"cky-connect-icon"},[e("div",{staticClass:"cky-icon-container-dot"},[e("img",{attrs:{src:i("f222")}})]),e("div",{staticClass:"cky-connect-line"})]),e("div",{staticClass:"cky-connect-steps"},[e("p",{staticClass:"cky-connect-step-title"},[t._v(" "+t._s(t.$i18n.__("Activate your cookie banner","cookie-law-info"))+" ")]),e("p",{staticClass:"cky-connect-step-description"},[e("b",[t._v(t._s(t.$i18n.__("Well done!","cookie-law-info")))]),t._v(" 🎉 "+t._s(t.$i18n.__("You have successfully implemented a cookie banner on your website.","cookie-law-info"))+" ")])])]),e("div",{staticClass:"cky-connect-step cky-row"},[e("div",{staticClass:"cky-column"},[e("div",{staticClass:"cky-icon-container-dot"},[e("div",{staticClass:"cky-icon-container"})])]),e("div",{staticClass:"cky-connect-steps"},[e("p",{staticClass:"cky-connect-step-title cky-focus-text"},[t._v(" "+t._s(t.$i18n.__("Connect and scan your website"))+" ")]),e("p",{staticClass:"cky-connect-step-description"},[t._v(" "+t._s(t.$i18n.__("To initiate an automatic cookie scan, you need to connect to the CookieYes web app. By connecting you can: ","cookie-law-info"))+" ")]),e("div",{staticClass:"cky-benefit-description"},[e("div",{staticClass:"cky-benefit-steps"},[e("div",{staticClass:"cky-icon-tiny"},[e("img",{attrs:{src:i("f222")}})]),e("p",{staticClass:"cky-connect-step-description cky-connect-benefits"},[t._v(" "+t._s(t.$i18n.__("Detect cookies and trackers on all web pages","cookie-law-info"))+" ")])]),e("div",{staticClass:"cky-benefit-steps"},[e("div",{staticClass:"cky-icon-tiny"},[e("img",{attrs:{src:i("f222")}})]),e("p",{staticClass:"cky-connect-step-description cky-connect-benefits"},[t._v(" "+t._s(t.$i18n.__("Automatically classify cookies into categories","cookie-law-info"))+" ")])]),e("div",{staticClass:"cky-benefit-steps"},[e("div",{staticClass:"cky-icon-tiny"},[e("img",{attrs:{src:i("f222")}})]),e("p",{staticClass:"cky-connect-step-description cky-connect-benefits"},[t._v(" "+t._s(t.$i18n.__("Generate a detailed cookie declaration","cookie-law-info"))+" ")])])]),e("router-link",{attrs:{to:"dashboard/plans",custom:""},scopedSlots:t._u([{key:"default",fn:function({navigate:i}){return[e("button",{staticClass:"cky-button",on:{click:i}},[t._v(" "+t._s(t.$i18n.__("Connect to a new account","cookie-law-info"))+" ")])]}}])}),e("button",{staticClass:"cky-button cky-existing-account cky-external-link",on:{click:t.handleExistingAccount}},[t._v(" "+t._s(t.$i18n.__("Connect to an existing account","cookie-law-info"))+" ")]),e("button",{staticClass:"cky-button cky-later-button",on:{click:t.expandAccordion}},[t._v(" "+t._s(t.$i18n.__("Do it later","cookie-law-info"))+" ")])],1)])]},proxy:!0}])})],1)])])},O=[],N=i("c068"),R=i("2f62"),Y=function(){var t=this,e=t._self._c;return t.pluginStatus?e("div",{class:{"cky-card":!0,"cky-cursor-pointer":!t.isExpanded,"cky-aria-expanded":t.isExpanded&&t.showIcon}},[t.title?e("div",{staticClass:"cky-card-header",on:{click:t.expandAccordion}},[e("div",{staticClass:"cky-row cky-space-between"},[e("div",{staticClass:"cky-title-spacing"},[e("div",{staticClass:"cky-row"},[e("h5",{staticClass:"cky-card-title"},[t._v(" "+t._s(t.title)+" ")])]),t.tagline?e("p",{staticClass:"cky-tagline"},[t._v(" "+t._s(t.tagline)+" ")]):t._e()]),t.showIcon?e("div",{class:{"cky-card-icon":!0,"cky-arrow-upward":t.isExpanded}},[e("img",{attrs:{src:i("50c3")}})]):t._e()]),t.hasActions?e("div",{staticClass:"cky-card-actions"},[t._t("headerAction")],2):t._e()]):t._e(),t.hasBodySlot?e("div",{class:t.getBodyClass},[t.loading?e("cky-card-loader"):t._t("body")],2):t._e(),t._t("outside"),t.hasFooterSlot?e("div",{staticClass:"cky-card-footer"},[t._t("footer")],2):t._e()],2):t._e()},G=[],q=i("17aa"),E={components:{CkyCardLoader:q["a"]},name:"CkyCard",props:{showIcon:{type:Boolean,default:!1},title:{type:String,required:!1},tagline:{type:String,required:!1,default:""},bodyClass:{type:String,default:""},loading:{type:Boolean,default:!1},fullWidth:{type:Boolean,default:!1}},data(){return{isExpanded:this.$store.state.settings.expand}},computed:{...Object(R["d"])("settings",["expand"]),hasActions(){return!!this.$slots.headerAction},hasBodySlot(){return!!this.$slots.body},hasFooterSlot(){return!!this.$slots.footer},getLoadingClass(){return this.loading?"cky-loading":""},getBodyClass(){return{"cky-card-body":!0,[this.bodyClass]:this.bodyClass}},pluginStatus(){return this.$store.state.settings.status}},methods:{async expandAccordion(){this.isExpanded=!this.isExpanded,this.$store.state.settings.expand=this.isExpanded,await c["a"].post({path:"/settings/expand",data:{expand:this.isExpanded}})}}},D=E,j=Object(g["a"])(D,Y,G,!1,null,null,null),z=j.exports,H={name:"CkyConnectNotice",mixins:[N["a"]],components:{CkyNotice:C["a"],CkyIcon:u["a"],CkyConnectCard:z},data(){return{syncing:!1,contents:{connect:this.$i18n.sprintf(this.$i18n.__("Create a free account to connect with %sCookieYes web app%s. After connecting, you can manage all your settings from the web app and access advanced features:","cookie-law-info"),"<b>","</b>"),pageviews:this.$i18n.sprintf(this.$i18n.__('You can continue using the plugin without connecting to the web app if you wish so. Please note that the standalone version of the plugin doesn\'t provide some advanced features. However, it offers unlimited <a href="%s" target="_blank">pageviews</a> in contrast to that of the web app-connected version.',"cookie-law-info"),"https://www.cookieyes.com/documentation/pageview-pricing/")}}},methods:{async removeNotice(){await c["a"].post({path:"/settings/notices/connect_notice",data:{}})},expandAccordion(){this.$refs.ckycard.expandAccordion()},handleExistingAccount(){this.classApplied="login",this.connectToApp(!0)}},computed:{...Object(R["d"])("settings",{info:"info",pluginStatus:"status"}),account(){return this.getOption("account")},showNotice(){return!!window.ckyAppNotices.connect_notice},tablesMissing(){return!!this.info.tables_missing}},mounted(){this.account.connected||(this.$root.$on("beforeConnection",()=>{this.syncing=!0}),this.$root.$on("afterConnection",()=>{}),this.$root.$on("afterSyncing",()=>{this.syncing=!1}))}},F=H,J=(i("7478"),Object(g["a"])(F,B,O,!1,null,null,null)),Q=J.exports,V=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cky-faq-container"},[e("div",{staticClass:"cky-faq-title"},[e("h4",[t._v(" "+t._s(t.title)+" ")])]),e("cky-accordion",t._l(t.faqs,(function(i){return e("cky-accordion-item",{key:i.id,scopedSlots:t._u([{key:"cky-accordion-trigger",fn:function(){return[e("p",{staticClass:"cky-app-accordion-title"},[t._v(" "+t._s(i.question)+" ")])]},proxy:!0},{key:"cky-accordion-content",fn:function(){return[e("p",[t._v(" "+t._s(i.answer)+" ")])]},proxy:!0}],null,!0)})})),1)],1)},K=[],X=i("a9f4"),Z=i("b02b"),tt={name:"CkyFaqWidget",components:{CkyAccordion:X["a"],CkyAccordionItem:Z["a"]},data(){return{title:this.$i18n.__("Frequently Asked Questions","cookie-law-info"),faqs:[{id:"faq1",question:this.$i18n.__("How do I customise the cookie consent banner?","cookie-law-info"),answer:this.$i18n.__('You can customise the banner by clicking the "Customise Banner" button on the plugin dashboard. It will take you to the web app settings, where you have several options to customise the banner to your liking.',"cookie-law-info")},{id:"faq2",question:this.$i18n.__("How do I scan web pages for cookies?","cookie-law-info"),answer:this.$i18n.__('Click the "Go to Web App" to access the web app. There, you will find the option to initiate a cookie scan for your website. Our premium plan offers a scheduled scan feature that automates this process for you.',"cookie-law-info")},{id:"faq3",question:this.$i18n.__("What are pageviews?","cookie-law-info"),answer:this.$i18n.__("Pageviews are the number of times the web pages containing CookieYes banner have been loaded or reloaded. This excludes known bot traffic.","cookie-law-info")},{id:"faq4",question:this.$i18n.__("What happens if the monthly pageview limit exceeds?","cookie-law-info"),answer:this.$i18n.__("The cookie banner will no longer be displayed on your site, which will result in non-compliance. You can either upgrade to a higher plan for an increased pageview limit or disconnect your site from the web app.","cookie-law-info")},{id:"faq5",question:this.$i18n.__("What happens if I disconnect my site from the app?","cookie-law-info"),answer:this.$i18n.__("When you disconnect from the web app, you can continue using the plugin. However, this means you will lose your banner customisation and access to advanced features.","cookie-law-info")},{id:"faq6",question:this.$i18n.__("How do I disconnect the plugin from the web app?","cookie-law-info"),answer:this.$i18n.__('Go to "Site settings" on the plugin dashboard and click "Disconnect" to disconnect the plugin from the web app.',"cookie-law-info")}]}}},et=tt,it=Object(g["a"])(et,V,K,!1,null,null,null),st=it.exports,nt=function(){var t=this,e=t._self._c;return t.pluginStatus&&!t.tablesMissing?e("div",{class:["cky-dashboard-overview",{connected:!!t.account.connected}]},[e("div",{staticClass:"cky-row"},[e("div",{staticClass:"cky-col-12"},[e("cky-card",{attrs:{title:t.$i18n.__("Overview","cookie-law-info"),loading:t.cardLoader},scopedSlots:t._u([{key:"body",fn:function(){return[t.hasBannerErrors?e("div",{staticClass:"cky-card-row"},[e("cky-notice",{attrs:{type:t.noticeType,showIcon:!0}},[e("p",{domProps:{innerHTML:t._s(t.getBannerError())}}),e("p",{domProps:{innerHTML:t._s(t.disconnectMessage)}})])],1):t._e(),e("div",{staticClass:"cky-card-row"},[e("div",{staticClass:"cky-info-widget-container"},[e("div",{staticClass:"cky-info-widget"},[e("div",{staticClass:"cky-info-widget-icon"},[e("img",{attrs:{src:i("4a95"),alt:"layout"}})]),e("div",{staticClass:"cky-info-widget-content"},[e("span",{staticClass:"cky-info-widget-title"},[t._v(t._s(t.$i18n.__("Banner status","cookie-law-info")))]),t.bannerStatus?e("span",{staticClass:"cky-info-widget-text",staticStyle:{color:"#00aa62"}},[t._v(" "+t._s(t.$i18n.__("Active","cookie-law-info"))+" "),e("a",{staticClass:"cky-actions-link cky-button-icon",attrs:{href:t.getSiteURL(),target:"_blank"}},[e("cky-icon",{attrs:{icon:"eye"}})],1)]):e("span",{staticClass:"cky-info-widget-text cky-status-error"},[t._v(" "+t._s(t.$i18n.__("Inactive","cookie-law-info"))+" ")])])]),e("div",{staticClass:"cky-info-widget"},[e("div",{staticClass:"cky-info-widget-icon"},[e("img",{attrs:{src:i("78d0"),alt:"layout"}})]),e("div",{staticClass:"cky-info-widget-content"},[e("span",{staticClass:"cky-info-widget-title"},[t._v(t._s(t.$i18n.__("Regulation","cookie-law-info")))]),t.account.connected?e("span",{staticClass:"cky-info-widget-text"},[t._v(" "+t._s(t.applicableLaws)+" "),e("a",{staticClass:"cky-action-link",on:{click:function(e){return t.$router.redirectToApp("customize")}}},[t._v(t._s(t.$i18n.__("Change","cookie-law-info"))+" ")])]):e("span",{staticClass:"cky-info-widget-text"},[t._v(" "+t._s(t.applicableLaws)+" "),e("router-link",{attrs:{to:{name:"customize"},custom:""},scopedSlots:t._u([{key:"default",fn:function({navigate:i}){return[e("a",{staticClass:"cky-action-link",on:{click:i}},[t._v(" "+t._s(t.$i18n.__("Change","cookie-law-info"))+" ")])]}}],null,!1,**********)})],1)])]),e("div",{staticClass:"cky-info-widget"},[e("div",{staticClass:"cky-info-widget-icon"},[e("img",{attrs:{src:i("c3cb"),alt:"layout"}})]),e("div",{staticClass:"cky-info-widget-content"},[e("span",{staticClass:"cky-info-widget-title"},[t._v(t._s(t.$i18n.__("Language","cookie-law-info")))]),t.account.connected?e("span",{staticClass:"cky-info-widget-text"},[t._v(" "+t._s(t.defaultLanguage.name)+" "),t.isFreePlan?e("a",{staticClass:"cky-action-link",on:{click:t.showLanguageUpgradeModal}},[t._v(" "+t._s(t.$i18n.__("Add languages","cookie-law-info"))+" "),e("img",{staticClass:"cky-crown-img",attrs:{src:i("7cd5")}})]):e("a",{staticClass:"cky-action-link",on:{click:function(e){return t.$router.redirectToApp("languages")}}},[t._v(" "+t._s(t.$i18n.__("Add languages","cookie-law-info"))+" ")])]):e("span",{staticClass:"cky-info-widget-text"},[t._v(" "+t._s(t.defaultLanguage.name)+" "),e("a",{staticClass:"cky-action-link",on:{click:t.showConnectModal}},[t._v(" "+t._s(t.$i18n.__("Add languages","cookie-law-info"))+" "),e("img",{staticClass:"cky-crown-img",attrs:{src:i("7cd5")}})])]),e("cky-connect-modal",{ref:"ckyConnectModal",attrs:{availablePlan:"premium",feature:"language_limit"},scopedSlots:t._u([{key:"title",fn:function(){return[e("img",{attrs:{src:i("0ac9"),alt:"languages"}})]},proxy:!0},{key:"message",fn:function(){return[e("div",{staticClass:"cky-feature-text"},[t._v(" "+t._s(t.$i18n.__("Display your banner in multiple languages tailored for your audience","cookie-law-info"))+" ")]),e("div",{staticClass:"cky-available-wrapper"},[e("span",{staticClass:"cky-available-text",domProps:{innerHTML:t._s(t.$i18n.__("Available in: <b>All premium plans</b>","cookie-law-info"))}})])]},proxy:!0}],null,!1,*********)}),e("cky-upgrade-modal",{ref:"ckyLanguageUpgradeModal",attrs:{feature:"language_limit",upgrade_source:"cywpal"},scopedSlots:t._u([{key:"title",fn:function(){return[e("img",{attrs:{src:i("0ac9"),alt:"languages"}})]},proxy:!0},{key:"message",fn:function(){return[e("div",{staticClass:"cky-feature-text"},[t._v(" "+t._s(t.$i18n.__("Display your banner in multiple languages tailored for your audience","cookie-law-info"))+" ")]),e("div",{staticClass:"cky-available-wrapper"},[e("span",{staticClass:"cky-available-text",domProps:{innerHTML:t._s(t.$i18n.__("Available in: <b>All premium plans</b>","cookie-law-info"))}})])]},proxy:!0}],null,!1,*********)})],1)]),e("div",{staticClass:"cky-info-widget"},[e("div",{staticClass:"cky-info-widget-icon"},[e("img",{attrs:{src:i("5544"),alt:"layout"}})]),e("div",{staticClass:"cky-info-widget-content"},[e("span",{staticClass:"cky-info-widget-title"},[t._v(t._s(t.$i18n.__("Targeted location","cookie-law-info")))]),e("span",{staticClass:"cky-info-widget-text"},[t.account.connected?e("span",{staticStyle:{"font-size":"14px"}},[t._v(" "+t._s(t.capitalizeString(t.targetedLocation))+" "),!t.isFreeorBasicPlan&&!t.isAgencyPlan||t.isGeoTargeted?t.isGeoTargeted?t._e():e("a",{staticClass:"cky-action-link",on:{click:function(e){return t.$router.redirectToApp("customize")}}},[t._v(" "+t._s(t.$i18n.__("Geo-target","cookie-law-info"))+" ")]):e("a",{staticClass:"cky-action-link",on:{click:t.showLocationUpgradeModal}},[t._v(" "+t._s(t.$i18n.__("Geo-target","cookie-law-info"))+" "),e("img",{staticClass:"cky-crown-img",attrs:{src:i("7cd5")}})])]):e("span",[t._v(t._s(t.$i18n.__("Worldwide","cookie-law-info"))+" "),e("a",{staticClass:"cky-action-link",on:{click:t.showLocationModal}},[t._v(" "+t._s(t.$i18n.__("Geo-target","cookie-law-info"))+" "),e("img",{staticClass:"cky-crown-img",attrs:{src:i("7cd5")}})])])]),e("cky-connect-modal",{ref:"ckyLocationModal",attrs:{availablePlan:"pro",feature:"config_geo_rules"},scopedSlots:t._u([{key:"title",fn:function(){return[e("img",{attrs:{src:i("e622"),alt:"location"}})]},proxy:!0},{key:"message",fn:function(){return[e("div",{staticClass:"cky-feature-text"},[t._v(" "+t._s(t.$i18n.__("Display your banner to visitors from selected locations!","cookie-law-info"))+" ")]),e("div",{staticClass:"cky-available-wrapper"},[e("span",{staticClass:"cky-available-text",domProps:{innerHTML:t._s(t.$i18n.__("Available in: <b>Pro</b> and <b>Ultimate</b> plans","cookie-law-info"))}})])]},proxy:!0}],null,!1,279373251)}),e("cky-upgrade-modal",{ref:"ckyLocationUpgradeModal",attrs:{feature:"config_geo_rules",upgrade_source:"cywpgt"},scopedSlots:t._u([{key:"title",fn:function(){return[e("img",{attrs:{src:i("e622"),alt:"location"}})]},proxy:!0},{key:"message",fn:function(){return[e("div",{staticClass:"cky-feature-text"},[t._v(" "+t._s(t.$i18n.__("Display your banner to visitors from selected locations!","cookie-law-info"))+" ")]),e("div",{staticClass:"cky-available-wrapper"},[e("span",{staticClass:"cky-available-text",domProps:{innerHTML:t._s(t.$i18n.__("Available in: <b>Pro</b> and <b>Ultimate</b> plans","cookie-law-info"))}})])]},proxy:!0}],null,!1,********)})],1)])])])]},proxy:!0},{key:"footer",fn:function(){return[t.account.connected?e("div",{staticClass:"cky-card-row"},[e("div",{staticClass:"cky-card-row-actions"},[e("a",{staticClass:"cky-button cky-button-outline cky-external-link cky-button-medium",on:{click:function(e){return t.$router.redirectToApp("customize")}}},[t._v(t._s(t.$i18n.__("Customise Banner","cookie-law-info"))+" ")])])]):e("div",{staticClass:"cky-card-row"},[e("div",{staticClass:"cky-card-row-actions"},[e("router-link",{attrs:{to:{name:"customize"},custom:""},scopedSlots:t._u([{key:"default",fn:function({navigate:i}){return[e("a",{staticClass:"cky-button cky-button-outline cky-button-medium cky-external-link",on:{click:i}},[t._v(t._s(t.$i18n.__("Customise Banner","cookie-law-info"))+" ")])]}}],null,!1,*********)})],1)])]},proxy:!0}],null,!1,**********)})],1)])]):t._e()},at=[],ot=i("9947"),ct=function(){var t=this,e=t._self._c;return e("cky-modal",{ref:"ckyUpgradeModal",staticClass:"cky-upgrade-modal cky-text-center",attrs:{type:"info",visible:t.visible,dismissable:t.dismissable},scopedSlots:t._u([{key:"body",fn:function(){return[e("h2",{staticClass:"cky-upgrade-modal-title"},[t._t("title")],2),e("p",{staticClass:"cky-upgrade-modal-message"},[t._t("message",(function(){return[t._v(" "+t._s(t.$i18n.__("To upgrade, create a new CookieYes account, or connect to an existing account and access premium features! After connecting, you can manage all your settings from the web app.","cookie-law-info"))+" ")]}))],2)]},proxy:!0},{key:"footer",fn:function(){return[e("div",{staticClass:"cky-app-modal-actions cky-justify-center"},[e("div",{staticClass:"cky-upgrade-button-container"},[e("cky-button",{ref:"ckyButtonUpgradeNew",staticClass:"cky-center cky-button-icon",nativeOn:{click:function(e){return t.redirectToPlanSelector.apply(null,arguments)}}},[e("cky-icon",{attrs:{icon:"crown",width:"20"}}),t._v(" "+t._s(t.$i18n.__("Upgrade now","cookie-law-info"))+" ")],1)],1)])]},proxy:!0}],null,!0)})},rt=[],lt=i("8a80"),dt={name:"CkyUpgradeModal",components:{CkyModal:lt["a"],CkyIcon:u["a"]},props:{visible:{type:Boolean,default:!1},feature:{type:String,default:""},upgrade_source:{type:String,default:""}},methods:{show(){this.$refs.ckyUpgradeModal&&this.$refs.ckyUpgradeModal.show()},close(){this.$refs.ckyUpgradeModal&&this.$refs.ckyUpgradeModal.close()},redirectToPlanSelector(){const t={website:this.getInfo("id")};let e=this.$router.getAppRedirectURL("wp-plan-selector",t);this.feature&&(e+="&feature="+this.feature),this.upgrade_source&&(e+="&upgrade_source="+this.upgrade_source),window.open(e,"_blank")}},computed:{dismissable(){return!0===!this.visible}},mounted(){this.$root.$on("afterConnection",()=>{this.close()})}},ut=dt,pt=(i("9133"),Object(g["a"])(ut,ct,rt,!1,null,null,null)),yt=pt.exports,gt={name:"CkyDashboardOverview",components:{CkyCard:r["a"],CkyNotice:C["a"],CkyIcon:u["a"],CkyConnectModal:ot["a"],CkyUpgradeModal:yt},data(){return{noticeType:"error",subscriptionURL:window.ckyGlobals.webApp.url+"/settings/organizations-and-sites",disconnectMessage:this.$i18n.sprintf(this.$i18n.__('Alternatively, you can <a href="%s">disconnect</a> your site from the web app and continue using the standalone version of the plugin. Please note that by doing so, you will lose your banner customisation and access to advanced features.',"cookie-law-info"),"admin.php?page=cookie-law-info#/settings")}},methods:{getSiteURL(){const t=new URL(window.ckyGlobals.site.url);return t.searchParams.append("cky_preview",!0),t.toString()},getBannerError(){return!(!this.info.website||!this.connected)&&(!!this.info.pageviews.exceeded&&("banner_disabled"===this.info.status?this.bannerErrors.pageviewsExceeded:(this.noticeType="warning",this.bannerErrors.pageviewsWarning)))},capitalizeString(t){return t.charAt(0).toUpperCase()+t.slice(1)},showConnectModal(){this.$refs.ckyConnectModal.show()},showLocationModal(){this.$refs.ckyLocationModal.show()},showLanguageUpgradeModal(){this.$refs.ckyLanguageUpgradeModal.show()},showLocationUpgradeModal(){this.$refs.ckyLocationUpgradeModal.show()}},computed:{...Object(R["c"])("languages",{defaultLanguage:"getDefault"}),...Object(R["d"])("settings",["info"]),cardLoader(){return!this.info||this.loading},banner(){return this.$store.state.banners.current},consentLogs(){return this.getInfo("consent_logs")&&this.getInfo("consent_logs").status||!1},account(){return this.getOption("account")},connected(){return!!this.account.connected},applicableLaws(){if(this.account.connected){const t=this.getInfo("banners");if(t.laws){const e=t.laws.split(/\s*&\s*/);return"ccpa"===t.laws?"US State Laws":e.includes("ccpa")&&e.includes("gdpr")?t.is_iab_enabled?"GDPR (IAB TCF v2.2) & US State Laws":"GDPR & US State Laws":t.is_iab_enabled?"GDPR (IAB TCF v2.2)":"GDPR"}return"GDPR"}{const t=this.banner?this.banner.properties.settings.applicableLaw:"";return"gdpr"===t?"GDPR":"US State Laws"}},targetedLocation(){if(this.account.connected){const t=this.getInfo("banners");if(t.targetedLocation)return t.targetedLocation}return"worldwide"},isGeoTargeted(){return"worldwide"!==this.targetedLocation},pluginStatus(){return this.$store.state.settings.status},tablesMissing(){return!!this.info.tables_missing},bannerStatus(){return!this.info.website||!this.connected||!(this.info.pageviews&&this.info.pageviews.exceeded&&"banner_disabled"==this.info.status||"suspended"===this.info.website.status||this.info.banner_disabled_manually)},hasBannerErrors(){return!!this.getBannerError()},gracePeriod(){return this.info&&this.info.website&&this.info.website.grace_period_ends_at?this.info.website.grace_period_ends_at:0},bannerErrors(){return{pageviewsWarning:this.$i18n.sprintf(this.$i18n.__('<b>Pageview limit exceeded</b>: Upgrade to a higher plan to increase your pageview limit and continue displaying the banner on this site. Visit <a href="%s" target="_blank">Organisations & Sites</a> to upgrade plan.',"cookie-law-info"),this.subscriptionURL),pageviewsExceeded:this.$i18n.sprintf(this.$i18n.__('<b>Pageview limit exceeded</b>: Upgrade to a higher plan to increase your pageview limit and continue displaying the banner on this site. Visit <a href="%s" target="_blank">Organisations & Sites</a> to upgrade plan and activate your banner.',"cookie-law-info"),this.subscriptionURL)}},isFreePlan(){return!!this.info&&"Free"===this.info.plan.name},isFreeorBasicPlan(){return!!this.info&&("Free"===this.info.plan.name||"Basic"===this.info.plan.name)},isAgencyPlan(){return!!this.info&&"Agency"===this.info.plan.name}}},kt=gt,ft=(i("9ce3"),Object(g["a"])(kt,nt,at,!1,null,null,null)),ht=ft.exports,_t=function(){var t=this,e=t._self._c;return e("cky-card",{staticClass:"cky-promo-widget",scopedSlots:t._u([{key:"body",fn:function(){return[e("div",{staticClass:"cky-promo-widget-header"},[e("div",{staticClass:"cky-promo-widget-logo-circle"},[e("img",{attrs:{src:i("6a11"),alt:"AccessYes Icon"}})]),e("div",{staticClass:"cky-promo-widget-title-block"},[e("div",{staticClass:"cky-promo-widget-title-row"},[e("img",{staticClass:"cky-promo-widget-title-icon",attrs:{src:i("e02a"),alt:"AccessYes Icon"}}),e("span",{staticClass:"cky-promo-widget-title-text"},[t._v("AccessYes")])]),e("img",{staticClass:"cky-promo-widget-by",attrs:{src:i("37f1"),alt:"by CookieYes"}})])]),e("h2",{staticClass:"cky-promo-widget-headline"},[t._v("Make your website more accessible")]),e("div",{staticClass:"cky-promo-widget-desc"},[t._v(" The EAA sets new digital accessibility standards for EU from "),e("span",{staticClass:"cky-promo-widget-date"},[t._v("June 28, 2025.")])]),e("div",{staticClass:"cky-promo-widget-desc cky-promo-widget-desc-secondary"},[t._v(" Our free accessibility widget helps you align with them! ")]),e("ul",{staticClass:"cky-promo-widget-checklist"},[e("li",[e("img",{staticClass:"cky-promo-widget-check",attrs:{src:i("f222"),alt:""}}),t._v(" No-code setup")]),e("li",[e("img",{staticClass:"cky-promo-widget-check",attrs:{src:i("f222"),alt:""}}),t._v(" WCAG-based features")]),e("li",[e("img",{staticClass:"cky-promo-widget-check",attrs:{src:i("f222"),alt:""}}),t._v(" Lightweight & free")])]),e("button",{staticClass:"cky-button cky-promo-widget-cta",on:{click:t.onCtaClick}},[t._v("Install free widget now")])]},proxy:!0}])})},wt=[],bt={name:"AccessYesWidgetPromo",components:{CkyCard:r["a"]},methods:{onCtaClick(){window.open("https://wordpress.org/plugins/accessibility-widget/","_blank","noopener,noreferrer")}}},mt=bt,vt=(i("57e4"),Object(g["a"])(mt,_t,wt,!1,null,null,null)),Ct=vt.exports,$t=i("c4aa"),xt={name:"Dashboard",mixins:[N["a"]],components:{NoticeMigration:A,NoticeSuspended:W,CkyScanSummary:b,CkyConnectSuccess:U["a"],CkyConnectNotice:Q,CkyDashboardOverview:ht,CkyFaqWidget:st,AccessYesWidgetPromo:Ct,CkyConsentChart:()=>Promise.all([i.e("chunk-6c8091d8"),i.e("chunk-e11526e4")]).then(i.bind(null,"03b4")),CkyPageviewGraph:()=>Promise.all([i.e("chunk-6c8091d8"),i.e("chunk-7bf1d985")]).then(i.bind(null,"fe52"))},data(){return{scanStatus:!0,loading:!0,syncing:!1}},methods:{loadBanner:async function(){await $t["a"].getActiveBanner()},connectScan(){this.connectToApp(),this.$root.$on("afterConnection",()=>{this.$refs.ckyButtonConnectScan.startLoading()})},connectLog(){this.connectToApp(),this.$root.$on("afterConnection",()=>{this.$refs.ckyButtonConnectLog.startLoading()})},getSiteURL(){const t=new URL(window.ckyGlobals.site.url);return t.searchParams.append("cky_preview",!0),t.toString()}},computed:{getWebsiteInfo(){return this.getInfo("website")},banner(){return this.$store.state.banners.current},isTrial(){return this.getWebsiteInfo&&this.getWebsiteInfo.is_trial},isSuspended(){return this.getWebsiteInfo&&"suspended"===this.getWebsiteInfo.status},isTrialSubscriptionCancelled(){return this.getWebsiteInfo&&"subscriptionCancelled"===this.getWebsiteInfo.status&&this.isTrial},isPaidSubscriptionCancelled(){return this.getWebsiteInfo&&"subscriptionCancelled"===this.getWebsiteInfo.status&&!this.isTrial&&this.getWebsiteInfo.payment_status},isPaymentMethodMissing(){return this.getWebsiteInfo&&"paymentMethodMissingPaid"===this.getWebsiteInfo.status&&!this.isTrial&&!this.getWebsiteInfo.payment_status},consentLogs(){return this.getInfo("consent_logs")&&this.getInfo("consent_logs").status||!1},account(){return this.getOption("account")},bannerStatus(){return this.getInfo("banners")&&this.getInfo("banners").status||!1},scans(){return this.getInfo("scans")&&this.getInfo("scans")||{}},plan(){return this.getInfo("plan").name||"free"},freePlan(){return"free"===this.plan.toLowerCase()},...Object(R["c"])("languages",{defaultLanguage:"getDefault"})},async created(){this.loading=!0;try{await this.loadBanner(),this.loading=!1,this.$root.$on("beforeConnection",()=>{this.syncing=!0}),this.$root.$on("afterSyncing",()=>{this.syncing=!1})}catch(t){console.error(t)}}},St=xt,At=(i("3136"),Object(g["a"])(St,s,n,!1,null,"73813c10",null));e["default"]=At.exports},"9b0d":function(t,e,i){},"9ce3":function(t,e,i){"use strict";i("c11d")},c11d:function(t,e,i){},c340:function(t,e,i){"use strict";i("7630")},c3cb:function(t,e,i){t.exports=i.p+"img/lang.svg"},d407:function(t,e,i){"use strict";i("9b0d")},ddbc:function(t,e,i){},e02a:function(t,e,i){t.exports=i.p+"img/accessyes-icon.svg"},e622:function(t,e,i){t.exports=i.p+"img/geo-location.svg"},ed2e:function(t,e,i){},f222:function(t,e,i){t.exports=i.p+"img/check.svg"},fb71:function(t,e,i){t.exports=i.p+"img/circle-warning.svg"}}]);