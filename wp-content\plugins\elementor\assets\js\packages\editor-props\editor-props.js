/*! For license information please see editor-props.js.LICENSE.txt */
!function(){"use strict";var r={"@elementor/schema":function(r){r.exports=window.elementorV2.schema}},e={};function t(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={exports:{}};return r[n](i,i.exports,t),i.exports}t.d=function(r,e){for(var n in e)t.o(e,n)&&!t.o(r,n)&&Object.defineProperty(r,n,{enumerable:!0,get:e[n]})},t.o=function(r,e){return Object.prototype.hasOwnProperty.call(r,e)},t.r=function(r){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})};var n={};!function(){t.r(n),t.d(n,{CLASSES_PROP_KEY:function(){return s},backgroundColorOverlayPropTypeUtil:function(){return j},backgroundGradientOverlayPropTypeUtil:function(){return w},backgroundImageOverlayPropTypeUtil:function(){return k},backgroundImagePositionOffsetPropTypeUtil:function(){return S},backgroundImageSizeScalePropTypeUtil:function(){return E},backgroundOverlayPropTypeUtil:function(){return x},backgroundPropTypeUtil:function(){return U},blurFilterPropTypeUtil:function(){return C},booleanPropTypeUtil:function(){return A},borderRadiusPropTypeUtil:function(){return c},borderWidthPropTypeUtil:function(){return l},boxShadowPropTypeUtil:function(){return u},brightnessFilterPropTypeUtil:function(){return I},classesPropTypeUtil:function(){return p},colorPropTypeUtil:function(){return f},colorStopPropTypeUtil:function(){return $},createArrayPropUtils:function(){return o},createPropUtils:function(){return e},dimensionsPropTypeUtil:function(){return m},evaluateTerm:function(){return L},filterEmptyValues:function(){return H},filterPropTypeUtil:function(){return R},gradientColorStopPropTypeUtil:function(){return V},imageAttachmentIdPropType:function(){return y},imagePropTypeUtil:function(){return b},imageSrcPropTypeUtil:function(){return d},isEmpty:function(){return Q},isTransformable:function(){return Y},keyValuePropTypeUtil:function(){return q},layoutDirectionPropTypeUtil:function(){return T},linkPropTypeUtil:function(){return O},mergeProps:function(){return J},moveTransformPropTypeUtil:function(){return F},numberPropTypeUtil:function(){return z},positionPropTypeUtil:function(){return _},shadowPropTypeUtil:function(){return a},shouldApplyEffect:function(){return K},sizePropTypeUtil:function(){return g},stringPropTypeUtil:function(){return h},strokePropTypeUtil:function(){return P},transformPropTypeUtil:function(){return G},urlPropTypeUtil:function(){return v}});var r=t("@elementor/schema");function e(e,t){const n=r.z.strictObject({$$type:r.z.literal(e),value:t,disabled:r.z.boolean().optional()});function o(r){return n.safeParse(r).success}return{extract:function(r){return o(r)?r.value:null},isValid:o,create:function(r,t){const n="function"==typeof r?r:()=>r,{base:i,disabled:a}=t||{};if(!i)return{$$type:e,value:n(),...a&&{disabled:a}};if(!o(i))throw new Error(`Cannot create prop based on invalid value: ${JSON.stringify(i)}`);return{$$type:e,value:n(i.value),...a&&{disabled:a}}},schema:n,key:e}}function o(t,n){return e(`${t}-array`,r.z.array(n))}var i=r.z.any().nullable(),a=e("shadow",r.z.strictObject({position:i,hOffset:i,vOffset:i,blur:i,spread:i,color:i})),u=e("box-shadow",r.z.array(a.schema)),c=e("border-radius",r.z.strictObject({"start-start":i,"start-end":i,"end-start":i,"end-end":i})),l=e("border-width",r.z.strictObject({"block-start":i,"block-end":i,"inline-start":i,"inline-end":i})),s="classes",p=e(s,r.z.array(r.z.string().regex(/^[a-z][a-z-_0-9]*$/i))),f=e("color",r.z.string()),b=e("image",r.z.strictObject({src:i,size:i})),y=e("image-attachment-id",r.z.number()),d=e("image-src",r.z.strictObject({id:i,url:r.z.null()}).or(r.z.strictObject({id:r.z.null(),url:i}))),m=e("dimensions",r.z.strictObject({"block-start":i,"block-end":i,"inline-start":i,"inline-end":i})),z=e("number",r.z.number().nullable()),g=e("size",r.z.strictObject({unit:r.z.enum(["px","em","rem","%","vw","vh"]),size:r.z.number()}).or(r.z.strictObject({unit:r.z.literal("auto"),size:r.z.literal("")})).or(r.z.strictObject({unit:r.z.literal("custom"),size:r.z.string()}))),h=e("string",r.z.string().nullable()),P=e("stroke",r.z.strictObject({color:i,width:i})),v=e("url",r.z.string().nullable()),T=e("layout-direction",r.z.object({row:r.z.any(),column:r.z.any()})),O=e("link",r.z.strictObject({destination:i,label:i,isTargetBlank:i})),U=e("background",r.z.strictObject({color:i,"background-overlay":i})),j=e("background-color-overlay",i),w=e("background-gradient-overlay",i),k=e("background-image-overlay",i),N=j.schema.or(w.schema).or(k.schema),x=e("background-overlay",r.z.array(N)),S=e("background-image-position-offset",i),E=e("background-image-size-scale",i),A=e("boolean",r.z.boolean().nullable()),$=e("color-stop",r.z.strictObject({color:i,offset:i})),V=e("gradient-color-stop",r.z.array($.schema)),q=e("key-value",r.z.strictObject({key:i,value:i})),_=e("object-position",r.z.strictObject({x:g.schema.nullable(),y:g.schema.nullable()})),C=e("blur",i),I=e("brightness",i),M=C.schema.or(I.schema),R=e("filter",r.z.array(M)),F=e("transform-move",r.z.strictObject({x:g.schema.nullable(),y:g.schema.nullable(),z:g.schema.nullable()})),B=e("scale",r.z.strictObject({x:g.schema.nullable(),y:g.schema.nullable(),z:g.schema.nullable()})),D=F.schema.or(B.schema),G=e("transform",r.z.array(D));function J(r,e){const t=structuredClone(r);return Object.entries(e).forEach((([r,e])=>{null==e?delete t[r]:t[r]=e})),t}function K({relation:r,terms:e},t){if(!e.length)return!1;const n=function(r){switch(r){case"or":return"some";case"and":return"every";default:throw new Error(`Relation not supported ${r}`)}}(r);return e[n]((r=>{return function(r){return"relation"in r}(r)?K(r,t):L(r,(e=r.path,n=t,e.reduce(((r,e)=>"object"==typeof r&&null!==r&&e in r?r[e]?.value:null),n)));var e,n}))}function L(r,e){const{value:t,operator:n}=r;switch(n){case"eq":case"ne":return e===t==("eq"===n);case"gt":case"lte":if(isNaN(Number(e))||isNaN(Number(t)))throw new Error("Mathematical comparison requires numeric values.");return Number(e)>Number(t)==("gt"===n);case"lt":case"gte":if(isNaN(Number(e))||isNaN(Number(t)))throw new Error("Mathematical comparison requires numeric values.");return Number(e)<Number(t)==("lt"===n);case"in":case"nin":if(!Array.isArray(t))throw new Error('The "in" and "nin" operators require an array for comparison.');return t.includes(e)===("in"===n);case"contains":case"ncontains":if(("string"!=typeof e||"string"!=typeof t)&&!Array.isArray(e))throw new Error('The "contains" and "ncontains" operators require a string or an array for comparison.');return"contains"===n===e.includes(t);case"exists":case"not_exist":return"exists"===n==(!!e||0===e||!1===e);default:return!1}}var W=r.z.object({$$type:r.z.string(),value:r.z.any(),disabled:r.z.boolean().optional()}),Y=r=>W.safeParse(r).success,H=r=>Q(r)?null:Array.isArray(r)?r.map(H).filter((r=>!Q(r))):"object"==typeof r?Object.fromEntries(Object.entries(r).map((([r,e])=>[r,H(e)])).filter((([,r])=>!Q(r)))):r,Q=r=>r&&Y(r)?Q(r.value):X(r)||Z(r)||rr(r),X=r=>null==r||""===r,Z=r=>Array.isArray(r)&&r.every(Q),rr=r=>"object"==typeof r&&Z(Object.values(r))}(),(window.elementorV2=window.elementorV2||{}).editorProps=n}(),window.elementorV2.editorProps?.init?.();