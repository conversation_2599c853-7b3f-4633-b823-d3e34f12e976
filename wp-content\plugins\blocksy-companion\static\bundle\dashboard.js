!function(){var e={487:function(e){var t={utf8:{stringToBytes:function(e){return t.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(t.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],n=0;n<e.length;n++)t.push(255&e.charCodeAt(n));return t},bytesToString:function(e){for(var t=[],n=0;n<e.length;n++)t.push(String.fromCharCode(e[n]));return t.join("")}}};e.exports=t},184:function(e,t){var n;
/*!
  Copyright (c) 2017 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var a=typeof n;if("string"===a||"number"===a)e.push(n);else if(Array.isArray(n)&&n.length){var c=o.apply(null,n);c&&e.push(c)}else if("object"===a)for(var i in n)r.call(n,i)&&n[i]&&e.push(i)}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()},12:function(e){var t,n;t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return 16711935&n.rotl(e,8)|**********&n.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=n.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],n=0,r=0;n<e.length;n++,r+=8)t[r>>>5]|=e[n]<<24-r%32;return t},wordsToBytes:function(e){for(var t=[],n=0;n<32*e.length;n+=8)t.push(e[n>>>5]>>>24-n%32&255);return t},bytesToHex:function(e){for(var t=[],n=0;n<e.length;n++)t.push((e[n]>>>4).toString(16)),t.push((15&e[n]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],n=0;n<e.length;n+=2)t.push(parseInt(e.substr(n,2),16));return t},bytesToBase64:function(e){for(var n=[],r=0;r<e.length;r+=3)for(var o=e[r]<<16|e[r+1]<<8|e[r+2],a=0;a<4;a++)8*r+6*a<=8*e.length?n.push(t.charAt(o>>>6*(3-a)&63)):n.push("=");return n.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/gi,"");for(var n=[],r=0,o=0;r<e.length;o=++r%4)0!=o&&n.push((t.indexOf(e.charAt(r-1))&Math.pow(2,-2*o+8)-1)<<2*o|t.indexOf(e.charAt(r))>>>6-2*o);return n}},e.exports=n},162:function(e,t,n){var r,o,a;o=[],void 0===(a="function"==typeof(r=function(){"use strict";function t(e,t){return void 0===t?t={autoBom:!1}:"object"!=typeof t&&(console.warn("Deprecated: Expected third argument to be a object"),t={autoBom:!t}),t.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob(["\ufeff",e],{type:e.type}):e}function r(e,t,n){var r=new XMLHttpRequest;r.open("GET",e),r.responseType="blob",r.onload=function(){i(r.response,t,n)},r.onerror=function(){console.error("could not download file")},r.send()}function o(e){var t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch(e){}return 200<=t.status&&299>=t.status}function a(e){try{e.dispatchEvent(new MouseEvent("click"))}catch(n){var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(t)}}var c="object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof n.g&&n.g.global===n.g?n.g:void 0,i=c.saveAs||("object"!=typeof window||window!==c?function(){}:"download"in HTMLAnchorElement.prototype?function(e,t,n){var i=c.URL||c.webkitURL,l=document.createElement("a");t=t||e.name||"download",l.download=t,l.rel="noopener","string"==typeof e?(l.href=e,l.origin===location.origin?a(l):o(l.href)?r(e,t,n):a(l,l.target="_blank")):(l.href=i.createObjectURL(e),setTimeout((function(){i.revokeObjectURL(l.href)}),4e4),setTimeout((function(){a(l)}),0))}:"msSaveOrOpenBlob"in navigator?function(e,n,c){if(n=n||e.name||"download","string"!=typeof e)navigator.msSaveOrOpenBlob(t(e,c),n);else if(o(e))r(e,n,c);else{var i=document.createElement("a");i.href=e,i.target="_blank",setTimeout((function(){a(i)}))}}:function(e,t,n,o){if((o=o||open("","_blank"))&&(o.document.title=o.document.body.innerText="downloading..."),"string"==typeof e)return r(e,t,n);var a="application/octet-stream"===e.type,i=/constructor/i.test(c.HTMLElement)||c.safari,l=/CriOS\/[\d]+/.test(navigator.userAgent);if((l||a&&i)&&"object"==typeof FileReader){var s=new FileReader;s.onloadend=function(){var e=s.result;e=l?e:e.replace(/^data:[^;]*;/,"data:attachment/file;"),o?o.location.href=e:location=e,o=null},s.readAsDataURL(e)}else{var u=c.URL||c.webkitURL,m=u.createObjectURL(e);o?o.location=m:location.href=m,o=null,setTimeout((function(){u.revokeObjectURL(m)}),4e4)}});c.saveAs=i.saveAs=i,e.exports=i})?r.apply(t,o):r)||(e.exports=a)},738:function(e){function t(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
e.exports=function(e){return null!=e&&(t(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&t(e.slice(0,0))}(e)||!!e._isBuffer)}},568:function(e,t,n){var r,o,a,c,i;r=n(12),o=n(487).utf8,a=n(738),c=n(487).bin,(i=function(e,t){e.constructor==String?e=t&&"binary"===t.encoding?c.stringToBytes(e):o.stringToBytes(e):a(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var n=r.bytesToWords(e),l=8*e.length,s=**********,u=-271733879,m=-**********,p=271733878,f=0;f<n.length;f++)n[f]=16711935&(n[f]<<8|n[f]>>>24)|**********&(n[f]<<24|n[f]>>>8);n[l>>>5]|=128<<l%32,n[14+(l+64>>>9<<4)]=l;var d=i._ff,h=i._gg,b=i._hh,g=i._ii;for(f=0;f<n.length;f+=16){var y=s,v=u,_=m,E=p;s=d(s,u,m,p,n[f+0],7,-680876936),p=d(p,s,u,m,n[f+1],12,-389564586),m=d(m,p,s,u,n[f+2],17,606105819),u=d(u,m,p,s,n[f+3],22,-**********),s=d(s,u,m,p,n[f+4],7,-176418897),p=d(p,s,u,m,n[f+5],12,**********),m=d(m,p,s,u,n[f+6],17,-**********),u=d(u,m,p,s,n[f+7],22,-45705983),s=d(s,u,m,p,n[f+8],7,1770035416),p=d(p,s,u,m,n[f+9],12,-1958414417),m=d(m,p,s,u,n[f+10],17,-42063),u=d(u,m,p,s,n[f+11],22,-1990404162),s=d(s,u,m,p,n[f+12],7,1804603682),p=d(p,s,u,m,n[f+13],12,-40341101),m=d(m,p,s,u,n[f+14],17,-1502002290),s=h(s,u=d(u,m,p,s,n[f+15],22,1236535329),m,p,n[f+1],5,-165796510),p=h(p,s,u,m,n[f+6],9,-1069501632),m=h(m,p,s,u,n[f+11],14,643717713),u=h(u,m,p,s,n[f+0],20,-373897302),s=h(s,u,m,p,n[f+5],5,-701558691),p=h(p,s,u,m,n[f+10],9,38016083),m=h(m,p,s,u,n[f+15],14,-660478335),u=h(u,m,p,s,n[f+4],20,-405537848),s=h(s,u,m,p,n[f+9],5,568446438),p=h(p,s,u,m,n[f+14],9,-1019803690),m=h(m,p,s,u,n[f+3],14,-187363961),u=h(u,m,p,s,n[f+8],20,1163531501),s=h(s,u,m,p,n[f+13],5,-1444681467),p=h(p,s,u,m,n[f+2],9,-51403784),m=h(m,p,s,u,n[f+7],14,1735328473),s=b(s,u=h(u,m,p,s,n[f+12],20,-1926607734),m,p,n[f+5],4,-378558),p=b(p,s,u,m,n[f+8],11,-2022574463),m=b(m,p,s,u,n[f+11],16,1839030562),u=b(u,m,p,s,n[f+14],23,-35309556),s=b(s,u,m,p,n[f+1],4,-1530992060),p=b(p,s,u,m,n[f+4],11,1272893353),m=b(m,p,s,u,n[f+7],16,-155497632),u=b(u,m,p,s,n[f+10],23,-1094730640),s=b(s,u,m,p,n[f+13],4,681279174),p=b(p,s,u,m,n[f+0],11,-358537222),m=b(m,p,s,u,n[f+3],16,-722521979),u=b(u,m,p,s,n[f+6],23,76029189),s=b(s,u,m,p,n[f+9],4,-640364487),p=b(p,s,u,m,n[f+12],11,-421815835),m=b(m,p,s,u,n[f+15],16,530742520),s=g(s,u=b(u,m,p,s,n[f+2],23,-995338651),m,p,n[f+0],6,-198630844),p=g(p,s,u,m,n[f+7],10,1126891415),m=g(m,p,s,u,n[f+14],15,-1416354905),u=g(u,m,p,s,n[f+5],21,-57434055),s=g(s,u,m,p,n[f+12],6,1700485571),p=g(p,s,u,m,n[f+3],10,-1894986606),m=g(m,p,s,u,n[f+10],15,-1051523),u=g(u,m,p,s,n[f+1],21,-2054922799),s=g(s,u,m,p,n[f+8],6,1873313359),p=g(p,s,u,m,n[f+15],10,-30611744),m=g(m,p,s,u,n[f+6],15,-1560198380),u=g(u,m,p,s,n[f+13],21,1309151649),s=g(s,u,m,p,n[f+4],6,-145523070),p=g(p,s,u,m,n[f+11],10,-1120210379),m=g(m,p,s,u,n[f+2],15,718787259),u=g(u,m,p,s,n[f+9],21,-343485551),s=s+y>>>0,u=u+v>>>0,m=m+_>>>0,p=p+E>>>0}return r.endian([s,u,m,p])})._ff=function(e,t,n,r,o,a,c){var i=e+(t&n|~t&r)+(o>>>0)+c;return(i<<a|i>>>32-a)+t},i._gg=function(e,t,n,r,o,a,c){var i=e+(t&r|n&~r)+(o>>>0)+c;return(i<<a|i>>>32-a)+t},i._hh=function(e,t,n,r,o,a,c){var i=e+(t^n^r)+(o>>>0)+c;return(i<<a|i>>>32-a)+t},i._ii=function(e,t,n,r,o,a,c){var i=e+(n^(t|~r))+(o>>>0)+c;return(i<<a|i>>>32-a)+t},i._blocksize=16,i._digestsize=16,e.exports=function(e,t){if(null==e)throw new Error("Illegal argument "+e);var n=r.wordsToBytes(i(e,t));return t&&t.asBytes?n:t&&t.asString?c.bytesToString(n):r.bytesToHex(n)}},61:function(e,t,n){var r=n(698).default;function o(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e.exports=o=function(){return n},e.exports.__esModule=!0,e.exports.default=e.exports;var t,n={},a=Object.prototype,c=a.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},l="function"==typeof Symbol?Symbol:{},s=l.iterator||"@@iterator",u=l.asyncIterator||"@@asyncIterator",m=l.toStringTag||"@@toStringTag";function p(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(t){p=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var o=t&&t.prototype instanceof _?t:_,a=Object.create(o.prototype),c=new L(r||[]);return i(a,"_invoke",{value:P(e,n,c)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=f;var h="suspendedStart",b="suspendedYield",g="executing",y="completed",v={};function _(){}function E(){}function w(){}var k={};p(k,s,(function(){return this}));var O=Object.getPrototypeOf,x=O&&O(O(N([])));x&&x!==a&&c.call(x,s)&&(k=x);var j=w.prototype=_.prototype=Object.create(k);function C(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function S(e,t){function n(o,a,i,l){var s=d(e[o],e,a);if("throw"!==s.type){var u=s.arg,m=u.value;return m&&"object"==r(m)&&c.call(m,"__await")?t.resolve(m.__await).then((function(e){n("next",e,i,l)}),(function(e){n("throw",e,i,l)})):t.resolve(m).then((function(e){u.value=e,i(u)}),(function(e){return n("throw",e,i,l)}))}l(s.arg)}var o;i(this,"_invoke",{value:function(e,r){function a(){return new t((function(t,o){n(e,r,t,o)}))}return o=o?o.then(a,a):a()}})}function P(e,n,r){var o=h;return function(a,c){if(o===g)throw Error("Generator is already running");if(o===y){if("throw"===a)throw c;return{value:t,done:!0}}for(r.method=a,r.arg=c;;){var i=r.delegate;if(i){var l=D(i,r);if(l){if(l===v)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===h)throw o=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=g;var s=d(e,n,r);if("normal"===s.type){if(o=r.done?y:b,s.arg===v)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=y,r.method="throw",r.arg=s.arg)}}}function D(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,D(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var a=d(o,e.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,v;var c=a.arg;return c?c.done?(n[e.resultName]=c.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,v):c:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function z(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function M(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(z,this),this.reset(!0)}function N(e){if(e||""===e){var n=e[s];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function n(){for(;++o<e.length;)if(c.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return a.next=a}}throw new TypeError(r(e)+" is not iterable")}return E.prototype=w,i(j,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:E,configurable:!0}),E.displayName=p(w,m,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===E||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,p(e,m,"GeneratorFunction")),e.prototype=Object.create(j),e},n.awrap=function(e){return{__await:e}},C(S.prototype),p(S.prototype,u,(function(){return this})),n.AsyncIterator=S,n.async=function(e,t,r,o,a){void 0===a&&(a=Promise);var c=new S(f(e,t,r,o),a);return n.isGeneratorFunction(t)?c:c.next().then((function(e){return e.done?e.value:c.next()}))},C(j),p(j,m,"Generator"),p(j,s,(function(){return this})),p(j,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},n.values=N,L.prototype={constructor:L,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(M),!e)for(var n in this)"t"===n.charAt(0)&&c.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(r,o){return i.type="throw",i.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],i=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var l=c.call(a,"catchLoc"),s=c.call(a,"finallyLoc");if(l&&s){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&c.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),M(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;M(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:N(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},n}e.exports=o,e.exports.__esModule=!0,e.exports.default=e.exports},698:function(e){function t(n){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},687:function(e,t,n){var r=n(61)();e.exports=r;try{regeneratorRuntime=r}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=r:Function("r","regeneratorRuntime = r")(r)}}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r].call(a.exports,a,a.exports,n),a.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,n||"default");if("object"!=e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}function r(e,n,r){return(n=t(n))in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}var o=window.wp.element,a=window.ctEvents,c=n.n(a),i=window.wp.i18n,l=window.blocksyOptions;function s(e,t,n,r,o,a,c){try{var i=e[a](c),l=i.value}catch(e){return void n(e)}i.done?t(l):Promise.resolve(l).then(r,o)}function u(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function c(e){s(a,r,o,c,i,"next",e)}function i(e){s(a,r,o,c,i,"throw",e)}c(void 0)}))}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function p(e,t){if(e){if("string"==typeof e)return m(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?m(e,t):void 0}}function f(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,c,i=[],l=!0,s=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(i.push(r.value),i.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=n.return&&(c=n.return(),Object(c)!==c))return}finally{if(s)throw o}}return i}}(e,t)||p(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var d=n(687),h=n.n(d),b=function(){var e=u(h().mark((function e(){var t,n;return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,fetch("".concat(wp.ajax.settings.url,"?action=blocksy_flush_permalinks&nonce=").concat(ctDashboardLocalizations.dashboard_actions_nonce),{method:"POST"});case 2:if(200===(t=e.sent).status){e.next=5;break}return e.abrupt("return");case 5:return e.next=7,t.json();case 7:if(n=e.sent,n.success){e.next=11;break}return e.abrupt("return");case 11:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();function g(e){return function(e){if(Array.isArray(e))return m(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||p(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(e,n){for(var r=0;r<n.length;r++){var o=n[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,t(o.key),o)}}var v=function(){var e={};return e.promise=new Promise((function(t,n){e.resolve=t,e.reject=n})),e},_=n(568),E=n.n(_);function w(t){if(Array.isArray(t))return"[".concat(t.map((function(e){return w(e)})).join(","),"]");if("object"===e(t)&&null!==t){var n="",r=Object.keys(t).sort();n+="{".concat(JSON.stringify(r));for(var o=0;o<r.length;o++)n+="".concat(w(t[r[o]]),",");return"".concat(n,"}")}return"".concat(JSON.stringify(t))}var k=function(e){return E()(w(e))};function O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?O(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var j=0,C=1,S=2,P={cacheKey:{state:j,response:null,payoad:{}}},D={},z=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return function(e,t,n){t&&y(e.prototype,t),n&&y(e,n),Object.defineProperty(e,"prototype",{writable:!1})}(e,[{key:"parseOptions",value:function(e){var t=e.fetcherName,n=void 0===t?null:t,r=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,["fetcherName"]);return{args:{fetcherName:n},inputFetchOptions:r}}},{key:"fetch",value:function(e,t,n){var r=this.parseOptions(n).args,o=k(x(x({},t),{},{url:e}));if(P[o]||(P[o]={state:j,response:null,payload:{waitingForResponse:[]}},r.fetcherName&&D[r.fetcherName]&&(D[r.fetcherName].forEach((function(e){e.abort()})),D[r.fetcherName]=[])),P[o].state===S){var a=v();return a.resolve(P[o].response.clone()),a.promise}if(P[o].state===C)return this.fetchLoadingState(o,e,t,n);if(P[o].state===j)return this.fetchEmptyState(o,e,t,n);throw new Error("Invalid state",{cacheEntry:P[o]})}},{key:"fetchLoadingState",value:function(e,t,n,r){var o=v();return P[e].payload.waitingForResponse.push(o),o.promise}},{key:"fetchEmptyState",value:function(e,t,n,r){var o=this.parseOptions(r),a=o.args,c=o.inputFetchOptions;P[e].state=C;var i=new AbortController;a.fetcherName&&(D[a.fetcherName]||(D[a.fetcherName]=[]),D[a.fetcherName].push(i));var l=v(),s=x({method:"POST",headers:{"Content-Type":"application/json"},signal:i.signal},c);return"POST"===s.method&&(s.body=JSON.stringify(n)),fetch(t,s).then((function(t){P[e].response=t.clone(),[l].concat(g(P[e].payload.waitingForResponse)).forEach((function(t){t.resolve(P[e].response.clone())})),P[e].payload={waitingForResponse:[]},P[e].state=S,a.fetcherName&&(D[a.fetcherName]=[])})).catch((function(t){[l].concat(g(P[e].payload.waitingForResponse)).forEach((function(t){t.reject(P[e].response)})),P[e].payload={waitingForResponse:[]},P[e].state=j})),l.promise}}]),e}(),M=new z,L=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return M.fetch(e,t,n)},N=function(){var e=new URL("https://startersites.io?route=v2/demo/get_all");return Object.keys(ctDashboardLocalizations.plugin_data.retrieve_demos_data).forEach((function(t){e.searchParams.append(t,ctDashboardLocalizations.plugin_data.retrieve_demos_data[t])})),ctDashboardLocalizations.plugin_data.is_pro&&e.searchParams.append("is_pro","true"),e.searchParams.append("companion_version",ctDashboardLocalizations.plugin_data.plugin_version),L(e.toString(),{},{method:"GET",headers:{"Content-Type":"application/json",Accept:"application/json"}})};function B(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function F(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?B(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):B(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var T=null,A=!1,V=function(){var e=f((0,o.useState)(!T),2),t=e[0],n=e[1],r=f((0,o.useState)(T||[]),2),a=r[0],i=r[1],l=f((0,o.useState)(A),2),s=l[0],m=l[1],p=f((0,o.useState)({controller:null}),2),d=p[0].controller,g=p[1],y=function(){var e=u(h().mark((function e(){var t,r,o,a,c,l,s,u,p,f=arguments;return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=F({verbose:!1,extension:null,extAction:null},f.length>0&&void 0!==f[0]?f[0]:{}),r=t.verbose,o=t.extension,a=t.extAction,r&&n(!0),d&&d.abort(),e.prev=4,e.next=7,N();case 7:(c=e.sent).status&&511===c.status&&(A=!0,m(!0)),e.next=13;break;case 11:e.prev=11,e.t0=e.catch(4);case 13:return"AbortController"in window&&(d=new AbortController,g({controller:d})),e.next=16,fetch("".concat(wp.ajax.settings.url,"?action=blocksy_extensions_status&nonce=").concat(ctDashboardLocalizations.dashboard_actions_nonce),F({method:"POST",signal:d.signal},o&&a?{body:JSON.stringify({extension:o,extAction:a})}:{}));case 16:if(200===(l=e.sent).status){e.next=19;break}return e.abrupt("return");case 19:return e.next=21,l.json();case 21:if(s=e.sent,u=s.success,p=s.data,u){e.next=26;break}return e.abrupt("return");case 26:if(null!=a&&a.require_refresh&&b(),i(p),T=p,n(!1),!o){e.next=32;break}return e.abrupt("return",p[o]);case 32:return e.abrupt("return",p);case 33:case"end":return e.stop()}}),e,null,[[4,11]])})));return function(){return e.apply(this,arguments)}}();return(0,o.useEffect)((function(){T||y({verbose:!0});var e=function(){y()};return c().on("blocksy_exts_sync_exts",e),function(){c().off("blocksy_exts_sync_exts",e)}}),[]),{syncExts:y,isLoading:t,exts_status:a,forceEmptyExts:s,setExtsStatus:function(e){var t=e(a);T=t,i(e)}}},H=function(){return(0,o.createElement)("div",{class:"ct-demo-notification"},(0,o.createElement)("b",null,"Yikes! Something went wrong"),(0,o.createElement)("p",null,"Unfortunately something went wrong during initialization, please ",(0,o.createElement)("a",{href:"https://creativethemes.com/blocksy/support/",target:"_blank"},"contact us from here")," and we will assist you."))},I=function(e){var t=e.navigate,n=V(),r=n.forceEmptyExts,a=n.exts_status;return(0,o.useEffect)((function(){r||Object.keys(a).length>0&&t("/extensions/".concat(Object.keys(a)[0]))}),[a,r]),r?(0,o.createElement)(H,null):(0,o.createElement)("div",{className:"ct-extensions-container"},(0,o.createElement)(l.Transition,{items:!0,from:{opacity:0},enter:[{opacity:1}],leave:[{opacity:0}],initial:null,config:function(e,t){return"leave"===t?{duration:300}:{delay:300,duration:300}}},(function(e,t){return t?(0,o.createElement)(l.animated.p,{style:e,className:"ct-loading-text"},(0,o.createElement)("svg",{width:"16",height:"16",viewBox:"0 0 100 100"},(0,o.createElement)("g",{transform:"translate(50,50)"},(0,o.createElement)("g",{transform:"scale(1)"},(0,o.createElement)("circle",{cx:"0",cy:"0",r:"50",fill:"currentColor"}),(0,o.createElement)("circle",{cx:"0",cy:"-26",r:"12",fill:"#ffffff",transform:"rotate(161.634)"},(0,o.createElement)("animateTransform",{attributeName:"transform",type:"rotate",calcMode:"linear",values:"0 0 0;360 0 0",keyTimes:"0;1",dur:"1s",begin:"0s",repeatCount:"indefinite"}))))),(0,i.__)("Loading Extensions Status...","blocksy-companion")):null})))},G=n(184),R=n.n(G);function q(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function U(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?q(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):q(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var W=function(e){var t=e.navigate,n=e.currentExtension,r=e.exts_status,a=(0,o.useMemo)((function(){return Object.values(r).map((function(e,t){return U(U({},e),{},{name:Object.keys(r)[t]})})).find((function(e){return e.config.pro}))}),[r]),c=(0,o.useMemo)((function(){return Object.values(r).map((function(e,t){return U(U({},e),{},{name:Object.keys(r)[t]})})).filter((function(e){return!e.config.pro}))}),[r]),l=(0,o.useMemo)((function(){return Object.values(r).map((function(e,t){return U(U({},e),{},{name:Object.keys(r)[t]})})).filter((function(e){return e.config.pro})).filter((function(e){return"white-label"!==e.name||!e.data||!e.data.locked}))}),[r]);return(0,o.createElement)("div",{className:"ct-extensions-menu"},[{label:(0,i.__)("Free Extensions","blocksy-companion"),exts:c,order:[]}].concat(g(a?[{label:(0,i.__)("Pro Extensions","blocksy-companion"),exts:l,order:["woocommerce-extra","post-types-extra","local-google-fonts","custom-fonts","adobe-typekit"]}]:[])).map((function(e){var r=e.label,a=e.exts,c=e.order,l=[].concat(g(c.map((function(e){return a.find((function(t){return t.name===e}))}))),g(a.filter((function(e){var t=e.name;return!c.includes(t)})).sort((function(e,t){return e.config.name.localeCompare(t.config.name)})))).filter((function(e){return!!e}));return(0,o.createElement)(o.Fragment,{key:r},(0,o.createElement)("h4",null,r),(0,o.createElement)("ul",null,l.map((function(e){var r=e.name,a=e.config,c=(e.status,e.__object);return(0,o.createElement)("li",{key:r,className:R()({selected:n&&n.name===r,active:!!c}),onClick:function(){t("/extensions/".concat(r))}},a.name,(0,o.createElement)("span",{"data-tooltip-reveal":"top"},(0,o.createElement)("i",{className:"ct-tooltip"},(0,i.__)("Active","blocksy-companion"))))}))))})))},Z=(window.ctDashboardLocalizations||{}).DashboardContext,J=((Z||{}).Provider,(Z||{}).Consumer,Z),Y=((0,i.__)("Free","blocksy-companion"),(0,i.__)("Personal","blocksy-companion"),(0,i.__)("Business","blocksy-companion"),(0,i.__)("Agency","blocksy-companion"),(0,i.__)("Personal","blocksy-companion"),(0,i.__)("Business","blocksy-companion"),(0,i.__)("Agency","blocksy-companion"),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.currentPlan,e.requiredPlan),n=void 0===t?"personal":t,r=e.personal,a=void 0===r?{title:(0,i.__)("This is a Pro feature","blocksy-companion"),description:(0,i.__)("Upgrade to any pro plan and get instant access to this and many other feature.","blocksy-companion")}:r,c=e.professional,s=void 0===c?{description:(0,i.__)("Upgrade to the business or agency plan and get instant access to this and many other features.","blocksy-companion")}:c,u=e.agency,m=void 0===u?{description:(0,i.__)("Upgrade to the agency plan and get instant access to this and many other features.","blocksy-companion")}:u,p=f((0,o.useState)(!1),2),d=p[0],h=p[1];return{showNotice:function(e){h(e||!0)},content:(0,o.createElement)(l.Overlay,{items:d,className:"ct-onboarding-modal",onDismiss:function(){return h(!1)},render:function(){return(0,o.createElement)("div",{className:"ct-modal-content"},(0,o.createElement)("svg",{width:"55",height:"55",viewBox:"0 0 40.5 48.3"},(0,o.createElement)("path",{fill:"#2d82c8",d:"M33.4 29.4l7.1 12.3-7.4.6-4 6-7.3-12.9"}),(0,o.createElement)("path",{d:"M33.5 29.6L26 42.7l-4.2-7.3 11.6-6 .1.2zM0 41.7l7.5.6 3.9 6 7.2-12.4-11-7.3L0 41.7z",fill:"#2271b1"}),(0,o.createElement)("path",{d:"M39.5 18.7c0 1.6-2.4 2.8-2.7 4.3-.4 1.5 1 3.8.2 5.1-.8 1.3-3.4 1.2-4.5 2.3-1.1 1.1-1 3.7-2.3 4.5-1.3.8-3.6-.6-5.1-.2-1.5.4-2.7 2.7-4.3 2.7S18 35 16.5 34.7c-1.5-.4-3.8 1-5.1.2s-1.2-3.4-2.3-4.5-3.7-1-4.5-2.3.6-3.6.2-5.1-2.7-2.7-2.7-4.3 2.4-2.8 2.7-4.3c.4-1.5-1-3.8-.2-5.1C5.4 8 8.1 8.1 9.1 7c1.1-1.1 1-3.7 2.3-4.5s3.6.6 5.1.2C18 2.4 19.2 0 20.8 0c1.6 0 2.8 2.4 4.3 2.7 1.5.4 3.8-1 5.1-.2 1.3.8 1.2 3.4 2.3 4.5 1.1 1.1 3.7 1 4.5 2.3s-.6 3.6-.2 5.1c.3 1.5 2.7 2.7 2.7 4.3z",fill:"#599fd9"}),(0,o.createElement)("path",{d:"M23.6 7c-6.4-1.5-12.9 2.5-14.4 8.9-.7 3.1-.2 6.3 1.5 9.1 1.7 2.7 4.3 4.6 7.4 5.4.9.2 1.9.3 2.8.3 2.2 0 4.4-.6 6.3-1.8 2.7-1.7 4.6-4.3 5.4-7.5C34 15 30 8.5 23.6 7zm7 14c-.6 2.6-2.2 4.8-4.5 6.2-2.3 1.4-5 1.8-7.6 1.2-2.6-.6-4.8-2.2-6.2-4.5-1.4-2.3-1.8-5-1.2-7.6.6-2.6 2.2-4.8 4.5-6.2 1.6-1 3.4-1.5 5.2-1.5.8 0 1.5.1 2.3.3 5.4 1.3 8.7 6.7 7.5 12.1zm-8.2-4.5l3.7.5-2.7 2.7.7 3.7-3.4-1.8-3.3 1.8.6-3.7-2.7-2.7 3.8-.5 1.6-3.4 1.7 3.4z",fill:"#fff"})),"personal"===n&&(0,o.createElement)(o.Fragment,null,(0,o.createElement)("h2",{className:"ct-modal-title"},a.title),(0,o.createElement)("p",null,a.description)),"professional"===n&&(0,o.createElement)(o.Fragment,null,(0,o.createElement)("span",null,"Required plan"),(0,o.createElement)("h2",{className:"ct-modal-title"},(0,i.__)("Business or Agency","blocksy-companion")),(0,o.createElement)("p",null,s.description)),"agency"===n&&(0,o.createElement)(o.Fragment,null,(0,o.createElement)("span",null,"Required plan"),(0,o.createElement)("h2",{className:"ct-modal-title"},(0,i.__)("Agency","blocksy-companion")),(0,o.createElement)("p",null,m.description)),(0,o.createElement)("div",{className:"ct-modal-actions has-divider","data-buttons":"2"},(0,o.createElement)("a",{href:ctDashboardLocalizations.plugin_data?ctDashboardLocalizations.plugin_data.modal_links["compare-plans"]:"https://creativethemes.com/blocksy/pricing/#comparison-free-vs-pro",target:"_blank",className:"button"},(0,i.__)("Compare Plans","blocksy-companion")),(0,o.createElement)("a",{href:ctDashboardLocalizations.plugin_data.modal_links?ctDashboardLocalizations.plugin_data.modal_links.pricing:"https://creativethemes.com/blocksy/pricing/",target:"_blank",className:"button button-primary"},(0,i.__)("Upgrade Now","blocksy-companion"))))}})}});function X(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function $(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?X(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):X(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var K=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t=$({strategy:"pro-ext",modalTitle:(0,i.__)("This is a Pro extension","blocksy-companion")},t);var n=f((0,o.useState)(!1),2),r=(n[0],n[1],ctDashboardLocalizations.plugin_data.is_pro),a=((0,o.useContext)(J).history,!r&&e.config.pro),c=ctDashboardLocalizations.plugin_data.current_plan,l="personal";e.config.plans&&(e.config.plans.includes("agency_v2")&&(l="agency"),e.config.plans.includes("professional_v2")&&(l="professional"));var s=Y($({currentPlan:c,requiredPlan:l},t)),u=s.content,m=s.showNotice;return{isPro:r,isProInFree:!r&&e.config.pro||e.config.plans&&e.config.plans.length&&!e.config.plans.includes(c),showNotice:m,content:"pro-ext"===t.strategy&&a||"pro"===t.strategy&&!r||e.config.plans&&e.config.plans.length&&!e.config.plans.includes(c)?u:null}},Q=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){},n=f((0,o.useState)(!1),2),r=n[0],a=n[1],c=K(e),i=c.isProInFree,l=c.showNotice,s=c.content,m=function(){var n=u(h().mark((function n(){var r,o,c,s=arguments;return h().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(r=s.length>0&&void 0!==s[0]?s[0]:function(){},o=!(s.length>1&&void 0!==s[1])||s[1],!i){n.next=8;break}if(!o||!e.config.features){n.next=6;break}return r(),n.abrupt("return");case 6:return l(),n.abrupt("return");case 8:if(r(),!o){n.next=25;break}return(c=new FormData).append("ext",e.name),c.append("action",e.__object?"blocksy_extension_deactivate":"blocksy_extension_activate"),c.append("nonce",ctDashboardLocalizations.dashboard_actions_nonce),a(!0),n.prev=15,n.next=18,fetch(ctDashboardLocalizations.ajax_url,{method:"POST",body:c});case 18:e.config.require_refresh&&b(),t(),n.next=24;break;case 22:n.prev=22,n.t0=n.catch(15);case 24:a(!1);case 25:case"end":return n.stop()}}),n,null,[[15,22]])})));return function(){return n.apply(this,arguments)}}();return[r,m,s]},ee=window.wp.hooks;function te(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ne(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?te(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):te(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var re=function(e){var t=e.navigate,n=e.currentExtension,a=e.onExtsSync,c=e.setExtsStatus,l=f((0,o.useState)(!1),2),s=l[0],u=l[1],m=f(Q(n),3),p=(m[0],m[1]),d=m[2],h={content:null,showExtension:!0,activationStrategy:"default"},b=n?(0,ee.applyFilters)("blocksy.extensions.current_extension_content",h,{extension:n,onExtsSync:a,setExtsStatus:c,navigate:t}):h,y=!!n&&n.__object;"from-custom-content"===b.activationStrategy&&(y=s);var v=[].concat(g(n&&n.config.documentation&&!ctDashboardLocalizations.plugin_data.hide_docs_section?[(0,o.createElement)("a",{target:"_blank",href:n.config.documentation},(0,o.createElement)("svg",{width:"15px",height:"15px",viewBox:"0 0 24 24",fill:"currentColor"},(0,o.createElement)("path",{d:"M22.9,1.1h-6.5C14.6,1.1,13,2,12,3.3C11,2,9.4,1.1,7.6,1.1H1.1C0.5,1.1,0,1.6,0,2.2v16.4c0,0.6,0.5,1.1,1.1,1.1h7.6c1.2,0,2.2,1,2.2,2.2c0,0.6,0.5,1.1,1.1,1.1c0.6,0,1.1-0.5,1.1-1.1c0-1.2,1-2.2,2.2-2.2h7.6c0.6,0,1.1-0.5,1.1-1.1V2.2C24,1.6,23.5,1.1,22.9,1.1z M10.9,18c-0.6-0.4-1.4-0.6-2.2-0.6H2.2V3.3h5.5c1.8,0,3.3,1.5,3.3,3.3V18z M21.8,17.5h-6.5c-0.8,0-1.5,0.2-2.2,0.6V6.5c0-1.8,1.5-3.3,3.3-3.3h5.5V17.5z"})),(0,i.__)("Documentation","blocksy-companion"))]:[]),g(n&&n.config.video&&!ctDashboardLocalizations.plugin_data.hide_video_section?[(0,o.createElement)("a",{target:"_blank",href:n.config.video},(0,o.createElement)("svg",{width:"15px",height:"15px",viewBox:"0 0 24 24",fill:"currentColor"},(0,o.createElement)("path",{d:"M12,0C5.4,0,0,5.4,0,12s5.4,12,12,12s12-5.4,12-12S18.6,0,12,0z M12,21.8c-5.4,0-9.8-4.4-9.8-9.8c0-5.4,4.4-9.8,9.8-9.8c5.4,0,9.8,4.4,9.8,9.8C21.8,17.4,17.4,21.8,12,21.8z M16.1,11.3c0.2,0.2,0.4,0.4,0.4,0.7c0,0.3-0.1,0.6-0.4,0.7L11,16.1c-0.1,0.1-0.3,0.1-0.5,0.1c-0.1,0-0.3,0-0.4-0.1c-0.3-0.1-0.5-0.4-0.5-0.8V8.6c0-0.3,0.2-0.6,0.5-0.8c0.3-0.1,0.6-0.1,0.9,0L16.1,11.3z"})),(0,i.__)("Video Tutorial","blocksy-companion"))]:[]),g(n&&n.config.customize&&y?[(0,o.createElement)("a",{href:n.config.customize,target:"_blank",className:"ct-button"},(0,o.createElement)("svg",{width:"15px",height:"15px",viewBox:"0 0 24 24",fill:"currentColor"},(0,o.createElement)("path",{d:"M4 11c.6 0 1-.4 1-1V3c0-.6-.4-1-1-1s-1 .4-1 1v7c0 .6.4 1 1 1zM12 11c-.6 0-1 .4-1 1v9c0 .6.4 1 1 1s1-.4 1-1v-9c0-.6-.4-1-1-1zM20 13c.6 0 1-.4 1-1V3c0-.6-.4-1-1-1s-1 .4-1 1v9c0 .6.4 1 1 1zM7 13H1c-.6 0-1 .4-1 1s.4 1 1 1h2v6c0 .6.4 1 1 1s1-.4 1-1v-6h2c.6 0 1-.4 1-1s-.4-1-1-1zM15 7h-2V3c0-.6-.4-1-1-1s-1 .4-1 1v4H9c-.6 0-1 .4-1 1s.4 1 1 1h6c.6 0 1-.4 1-1s-.4-1-1-1zM23 15h-6c-.6 0-1 .4-1 1s.4 1 1 1h2v4c0 .6.4 1 1 1s1-.4 1-1v-4h2c.6 0 1-.4 1-1s-.4-1-1-1z"})),(0,i.__)("Customize","blocksy-companion"))]:[]));return(0,o.useEffect)((function(){n&&b.showExtension||t("/extensions")}),[]),n?(0,o.createElement)("div",{className:"ct-extension-container"},(0,o.createElement)("div",{className:"ct-extension-info"},(0,o.createElement)("h4",null,(0,o.createElement)("span",{className:"ct-extension-icon",dangerouslySetInnerHTML:{__html:n&&n.config.icon||'<svg width="16" height="16" viewBox="0 0 24 24">\n\t\t\t\t\t\t\t<path\n\t\t\t\t\t\t\t\tfillRule="evenodd"\n\t\t\t\t\t\t\t\td="M22.3,10.7H24V7.6l-1.5-6.2h-21L0,7.6v3.1h1.7v11.7h20.7V10.7z M20.3,10.7H3.7v9.7h5.7v-7h5.3v7h5.7V10.7zM13,3.4h2.8l0.6,4.3l0,1H13V3.4z M8.2,3.4H11v5.3H7.6l0-1L8.2,3.4z M18.4,7.5l-0.6-4.1h3.1L22,7.8v0.9h-3.6L18.4,7.5z M6.2,3.4H3.1L2,7.8v0.9h3.6l0-1.2L6.2,3.4z"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</svg>'}}),n&&n.config.name,n&&(!n.config.requirement||n.config.requirement&&n.config.requirement.check)&&(0,o.createElement)("div",{className:R()("ct-option-switch",{"ct-active":y}),onClick:function(){"from-custom-content"!==b.activationStrategy?p((function(){c((function(e){return ne(ne({},e),{},r({},n.name,ne(ne({},e[n.name]),{},{__object:e[n.name].__object?null:{}})))}))})):u(!s)}},(0,o.createElement)("span",null))),n&&n.config.description&&(0,o.createElement)("p",null,n.config.description),v.length>0&&(0,o.createElement)("div",{className:"ct-extension-actions"},v.map((function(e,t){return(0,o.createElement)(o.Fragment,{key:t},e)})))),y&&n.config.features&&(!n.config.requirement||n.config.requirement&&n.config.requirement.check)&&(0,o.createElement)("div",{className:"ct-extension-modules"},n.config.features.map((function(e){return(0,o.createElement)("div",{className:"ct-extension-module",key:e.id},(0,o.createElement)("h5",null,e.title,(0,o.createElement)("div",{className:R()("ct-option-switch",{"ct-active":n.data&&n.data.settings.features[e.id]}),onClick:function(){p((function(){a({extAction:{type:"update-features",require_refresh:!(null==e||!e.require_refresh),settings:{features:ne(ne({},n.data.settings.features),{},r({},e.id,!n.data.settings.features[e.id]))}}}),c((function(t){return ne(ne({},t),{},r({},n.name,ne(ne({},t[n.name]),{},{data:ne(ne({},t[n.name].data),{},{settings:ne(ne({},t[n.name].data.settings),{},{features:ne(ne({},t[n.name].data.settings.features),{},r({},e.id,!t[n.name].data.settings.features[e.id]))})})})))}))}),!1)}},(0,o.createElement)("span",null))),(0,o.createElement)("p",null,e.description),e.documentation&&!ctDashboardLocalizations.plugin_data.hide_docs_section||(e.customize||e.manage)&&n.data&&n.data.settings.features[e.id]?(0,o.createElement)("div",{className:"ct-extension-module-actions"},e.documentation&&!ctDashboardLocalizations.plugin_data.hide_docs_section&&(0,o.createElement)("a",{href:e.documentation,target:"_blank"},(0,o.createElement)("svg",{width:"14px",height:"14px",viewBox:"0 0 24 24",fill:"currentColor"},(0,o.createElement)("path",{d:"M23 2.1h-6.6c-1.8 0-3.4.9-4.4 2.3C11 3 9.4 2.1 7.6 2.1H1c-.6 0-1 .4-1 1v16.5c0 .6.4 1 1 1h7.7c1.3 0 2.3 1 2.3 2.3 0 .6.4 1 1 1s1-.4 1-1c0-1.3 1-2.3 2.3-2.3H23c.6 0 1-.4 1-1V3.1c0-.6-.4-1-1-1zM11 19.3c-.7-.4-1.5-.7-2.3-.7H2V4.1h5.6c1.9 0 3.4 1.5 3.4 3.4v11.8zm11-.7h-6.7c-.8 0-1.6.2-2.3.7V7.5c0-1.9 1.5-3.4 3.4-3.4H22v14.5z"})),(0,i.__)("Documentation","blocksy-companion")),(e.customize||e.manage)&&n.data&&n.data.settings.features[e.id]&&(0,o.createElement)("a",{href:e.customize||e.manage,target:"_blank"},(0,o.createElement)("svg",{width:"14px",height:"14px",viewBox:"0 0 24 24",fill:"currentColor"},(0,o.createElement)("path",{d:"M4 11c.6 0 1-.4 1-1V3c0-.6-.4-1-1-1s-1 .4-1 1v7c0 .6.4 1 1 1zM12 11c-.6 0-1 .4-1 1v9c0 .6.4 1 1 1s1-.4 1-1v-9c0-.6-.4-1-1-1zM20 13c.6 0 1-.4 1-1V3c0-.6-.4-1-1-1s-1 .4-1 1v9c0 .6.4 1 1 1zM7 13H1c-.6 0-1 .4-1 1s.4 1 1 1h2v6c0 .6.4 1 1 1s1-.4 1-1v-6h2c.6 0 1-.4 1-1s-.4-1-1-1zM15 7h-2V3c0-.6-.4-1-1-1s-1 .4-1 1v4H9c-.6 0-1 .4-1 1s.4 1 1 1h6c.6 0 1-.4 1-1s-.4-1-1-1zM23 15h-6c-.6 0-1 .4-1 1s.4 1 1 1h2v4c0 .6.4 1 1 1s1-.4 1-1v-4h2c.6 0 1-.4 1-1s-.4-1-1-1z"})),e.customize?(0,i.__)("Customize","blocksy-companion"):(0,i.__)("Manage","blocksy-companion"))):null)}))),n&&n.config.requirement&&!n.config.requirement.check&&n.config.requirement.message&&(0,o.createElement)("div",{className:"ct-extension-options ct-newsletter-subscribe-options"},(0,o.createElement)("div",{className:"ct-extension-requirement"},(0,o.createElement)("span",null,(0,o.createElement)("svg",{width:"16",height:"16",fill:"#ffffff",viewBox:"0 0 24 24"},(0,o.createElement)("path",{d:"M12,23.6c-1.4,0-2.6-1-2.8-2.3L8.9,20h6.2l-0.3,1.3C14.6,22.6,13.4,23.6,12,23.6z M24,17.8H0l3.1-2c0.5-0.3,0.9-0.7,1.1-1.3c0.5-1,0.5-2.2,0.5-3.2V7.6c0-4.1,3.2-7.3,7.3-7.3s7.3,3.2,7.3,7.3v3.6c0,1.1,0.1,2.3,0.5,3.2c0.3,0.5,0.6,1,1.1,1.3L24,17.8zM6.1,15.6h11.8c0,0-0.1-0.1-0.1-0.2c-0.7-1.3-0.7-2.9-0.7-4.2V7.6c0-2.8-2.2-5.1-5.1-5.1c-2.8,0-5.1,2.2-5.1,5.1v3.6c0,1.3-0.1,2.9-0.7,4.2C6.1,15.5,6.1,15.6,6.1,15.6z"}))),n.config.requirement.message)),y&&b.content,d):null},oe=function(){return ctDashboardLocalizations.plugin_data.hide_support_section?null:(0,o.createElement)("div",{className:"ct-support-container"},(0,o.createElement)("h2",null,(0,i.__)("Need help or advice?","blocksy-companion")),(0,o.createElement)("p",null,(0,i.__)("Got a question or need help with the theme? You can always submit a support ticket or ask for help in our friendly Facebook community.","blocksy-companion")),(0,o.createElement)("div",{className:"ct-support-buttons"},(0,o.createElement)("a",{href:ctDashboardLocalizations.support_url,className:"ct-button","data-hover":"blue",target:"_blank"},(0,i.__)("Submit a Support Ticket","blocksy-companion")),(0,o.createElement)("a",{href:"https://www.facebook.com/groups/blocksy.community",className:"ct-button","data-hover":"blue",target:"_blank"},(0,i.__)("Join Facebook Community","blocksy-companion"))))};function ae(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ce(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ae(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ae(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var ie=function(e){(0,o.useContext)(J).history;var t=e.navigate,n=V(),r=n.forceEmptyExts,a=n.exts_status,c=n.syncExts,s=n.isLoading,u=n.setExtsStatus,m=null;return a[e.extension]&&(m=ce(ce({},a[e.extension]),{},{name:e.extension})),r?(0,o.createElement)(H,null):(0,o.createElement)("div",{className:"ct-extensions-container"},(0,o.createElement)(l.Transition,{items:s,from:{opacity:0},enter:[{opacity:1}],leave:[{opacity:0}],initial:null,config:function(e,t){return"leave"===t?{duration:300}:{delay:300,duration:300}}},(function(e,n){return n?(0,o.createElement)(l.animated.p,{style:e,className:"ct-loading-text"},(0,o.createElement)("svg",{width:"16",height:"16",viewBox:"0 0 100 100"},(0,o.createElement)("g",{transform:"translate(50,50)"},(0,o.createElement)("g",{transform:"scale(1)"},(0,o.createElement)("circle",{cx:"0",cy:"0",r:"50",fill:"currentColor"}),(0,o.createElement)("circle",{cx:"0",cy:"-26",r:"12",fill:"#ffffff",transform:"rotate(161.634)"},(0,o.createElement)("animateTransform",{attributeName:"transform",type:"rotate",calcMode:"linear",values:"0 0 0;360 0 0",keyTimes:"0;1",dur:"1s",begin:"0s",repeatCount:"indefinite"}))))),(0,i.__)("Loading Extensions Status...","blocksy-companion")):(0,o.createElement)(l.animated.div,{style:e},(0,o.createElement)("section",{className:"ct-extensions-list"},(0,o.createElement)(W,{currentExtension:m,exts_status:a,navigate:t}),(0,o.createElement)(re,{navigate:t,currentExtension:m,setExtsStatus:u,onExtsSync:function(){return c(ce(ce({},arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),{},{extension:m.name}))}})),(0,o.createElement)(o.Fragment,null,(0,o.createElement)(oe,null)))})))},le=function(e){var t=e.children,n=e.activated,r=e.checked,a=e.onChange,c=e.className,i=e.position,l=void 0===i?"end":i,s=[].concat(g("start"===l?[t]:[]),[(0,o.createElement)("span",{className:R()("ct-checkbox",{active:r}),key:"icon"},(0,o.createElement)("svg",{width:"10",height:"8",viewBox:"0 0 11.2 9.1"},(0,o.createElement)("polyline",{className:"check",points:"1.2,4.8 4.4,7.9 9.9,1.2 "})))],g("end"===l?[t]:[]));return(0,o.createElement)("div",{onClick:function(){return a()},className:R()("ct-checkbox-container",c,{activated:n})},s)};function se(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ue(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?se(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):se(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var me=function(e){return({gutenberg:"Gutenberg","stackable-ultimate-gutenberg-blocks":"Stackable - Gutenberg Blocks","wpforms-lite":"WPForms - Contact Form",woocommerce:"WooCommerce",elementor:"Elementor",brizy:"Brizy",getwid:"Getwid","simply-gallery-block":"SimpLy Gallery Block & Lightbox","recipe-card-blocks-by-wpzoom":"Recipe Card Blocks by WPZOOM","map-block-gutenberg":"Map Block for Google Maps","mb-custom-post-type":"MB Custom Post Types & Custom Taxonomies",leadin:"HubSpot","block-slider":"Block Slider","ht-slider-for-elementor":"HT Slider For Elementor","modula-best-grid-gallery":"Modula - Image Gallery","advanced-custom-fields":"Advanced Custom Fields (ACF)","greenshift-animation-and-page-builder-blocks":"Greenshift – Gutenberg Blocks",fluentform:"Fluent Forms","translatepress-multilingual":"TranslatePress","fluent-booking":"Fluent Booking"}[e]||e).replace(/\b\w/,(function(e){return e.toUpperCase()}))},pe=function(e){var t=e.demoConfiguration,n=e.setDemoConfiguration,r=e.style,a=(0,o.useContext)(ft),c=a.currentDemo,s=a.demos_list,u=a.pluginsStatus,m=(a.setCurrentDemo,f((c||"").split(":"),2)),p=m[0];m[1],s.filter((function(e){return e.name===p||""}));return(0,o.createElement)(l.animated.div,{style:r},(0,o.createElement)("div",{className:"ct-demo-plugins"},(0,o.createElement)("i",{className:"ct-demo-icon"},(0,o.createElement)("svg",{width:"40",height:"40",viewBox:"0 0 40 40"},(0,o.createElement)("path",{fill:"#0C7AB3",d:"M20,0v7.6c0,0.3-0.2,0.5-0.5,0.5h-1.5c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c-0.5-0.7-1.3-1.1-2.1-1.1c-1.5,0-2.6,1.2-2.6,2.6c0,1.5,1.2,2.6,2.6,2.6c0.8,0,1.6-0.4,2.1-1.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0.1,0c0,0,0,0,0,0h1.5c0,0,0,0,0,0c0.3,0,0.5,0.2,0.5,0.5V20h8.1v-0.8c-0.8-0.7-1.3-1.7-1.3-2.8c0-2,1.7-3.7,3.7-3.7c2,0,3.7,1.7,3.7,3.7c0,1.1-0.5,2.1-1.3,2.8V20H40C40,9,31,0,20,0z"}),(0,o.createElement)("path",{fill:"#3497D3",d:"M20,40v-7.6c0-0.3,0.2-0.5,0.5-0.5h1.5c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0.5,0.7,1.3,1.1,2.1,1.1c1.5,0,2.6-1.2,2.6-2.6c0-1.5-1.2-2.6-2.6-2.6c-0.8,0-1.6,0.4-2.1,1.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0-0.1,0c0,0,0,0,0,0h-1.5c0,0,0,0,0,0c-0.3,0-0.5-0.2-0.5-0.5V20h-8.1v0.8c0.8,0.7,1.3,1.7,1.3,2.8c0,2-1.7,3.7-3.7,3.7c-2,0-3.7-1.7-3.7-3.7c0-1.1,0.5-2.1,1.3-2.8V20H0C0,31,9,40,20,40z"}))),(0,o.createElement)("h2",null,(0,i.__)("Install & Activate Plugins","blocksy-companion")),(0,o.createElement)("p",null,(0,i.__)("The following plugins are required for this starter site in order to work properly.","blocksy-companion")),(0,o.createElement)("div",{className:"ct-checkboxes-container","data-type":"reversed"},t.plugins.map((function(e){var r=e.plugin,a=e.enabled;return(0,o.createElement)(o.Fragment,{key:r},!u[r]&&(0,o.createElement)(le,{key:r,checked:a,onChange:function(){return n(ue(ue({},t),{},{plugins:t.plugins.map((function(e){return e.plugin===r?ue(ue({},e),{},{enabled:!a}):e}))}))}},me(r)),u[r]&&(0,o.createElement)(le,{activated:!0,checked:!0,onChange:function(){}},me(r)))})))))};function fe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function de(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?fe(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):fe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var he=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"conjunction";return new Intl.ListFormat("en-GB",{style:"long",type:t}).format(e)},be=function(e){var t=e.demo,n=(0,o.useContext)(ft),r=n.currentlyInstalledDemo,a=n.demos_list,c=n.setCurrentDemo,l=n.setInstallerBlockingReleased,s=K({config:de({pro:!!t.is_pro},t.plans?{plans:t.plans}:{})},{strategy:"pro",personal:{title:(0,i.__)("This is a Pro starter site","blocksy-companion"),description:(0,i.__)("Upgrade to any pro plan and get instant access to this starter site and many other features.","blocksy-companion")},professional:{description:(0,i.__)("Upgrade to the business or agency plan and get instant access to this starter site and many other features.","blocksy-companion")},agency:{description:(0,i.__)("Upgrade to the agency plan and get instant access to this starter site and many other features.","blocksy-companion")}}),u=s.isProInFree,m=s.showNotice,p=s.content;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)("li",{className:R()("ct-single-demo",{"ct-is-pro":t.is_pro})},(0,o.createElement)("figure",null,(0,o.createElement)("img",{src:t.screenshot}),(0,o.createElement)("section",null,t.is_pro&&(0,o.createElement)(o.Fragment,null,(0,o.createElement)("h3",null,(0,i.__)("Required plan","blocksy-companion")),(0,o.createElement)("span",{className:"ct-required-plans"},he((t.plans?t.plans:["personal_v2","professional_v2","agency_v2"]).filter((function(e){return e.indexOf("v2")>-1})).map((function(e){return{personal_v2:(0,i.__)("Personal","blocksy-companion"),professional_v2:(0,i.__)("Business","blocksy-companion"),agency_v2:(0,i.__)("Agency","blocksy-companion")}[e]})),"disjunction"))),(0,o.createElement)("h3",null,(0,i.__)("Available for","blocksy-companion")),(0,o.createElement)("span",{className:"ct-available-builders"},he(a.filter((function(e){return e.name===t.name||""})).sort((function(e,t){return e.builder<t.builder?-1:e.builder>t.builder?1:0})).map((function(e){var t=e.builder;return me(t)||"Gutenberg"}))))),t.is_pro&&(0,o.createElement)("span",{className:"ct-pro-badge"},"PRO")),(0,o.createElement)("div",{className:"ct-demo-actions"},(0,o.createElement)("h4",null,t.name),(0,o.createElement)("div",null,(0,o.createElement)("a",{className:"ct-button",target:"_blank",href:t.url},(0,i.__)("Preview","blocksy-companion")),(0,o.createElement)("button",{className:"ct-button-primary",onClick:function(){u?m():(l(!1),c(t.name))}},r&&r.demo.indexOf(t.name)>-1?(0,i.__)("Modify","blocksy-companion"):(0,i.__)("Import","blocksy-companion"))))),p)},ge=function(){var e=(0,o.useContext)(ft).demos_list;return(0,o.createElement)("ul",null,e.filter((function(t,n){return e.map((function(e){return e.name})).indexOf(t.name)===n})).map((function(e){return(0,o.createElement)(be,{key:e.name,demo:e})})))},ye=function(e){var t=e.currentDemoWithVariation;e.demoContent.content;return[{title:(0,i.__)("Import content","blocksy-companion"),params:{action:"blocksy_demo_install_content",demo_name:t}}]};function ve(e){if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(e=function(e,t){if(!e)return;if("string"==typeof e)return _e(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _e(e,t)}(e))){var t=0,n=function(){};return{s:n,n:function(){return t>=e.length?{done:!0}:{done:!1,value:e[t++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,o,a=!0,c=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return a=e.done,e},e:function(e){c=!0,o=e},f:function(){try{a||null==r.return||r.return()}finally{if(c)throw o}}}}function _e(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ee(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function we(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ee(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ee(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var ke=function(e){var t=new URLSearchParams(we({nonce:ctDashboardLocalizations.dashboard_actions_nonce,wp_customize:"on"},e));return"".concat(ctDashboardLocalizations.ajax_url,"?").concat(t.toString())},Oe=(0,i.__)("Unfortunately, your hosting configuration doesn't meet the minimum requirements for importing a starter site.","blocksy-companion"),xe=function(e){e.preventDefault(),e.returnValue=""},je=function(e){var t=e.demoConfiguration,n=e.pluginsStatus,r=e.is_child_theme,o=e.includeMetaSteps,a=void 0!==o&&o,c=[];return a&&c.push("register_current_demo"),t.child_theme&&(r||c.push("child_theme")),t.plugins.filter((function(e){var t=e.enabled,r=e.plugin;return!!t&&!n[r]})).length>0&&c.push("plugins"),t.content.erase_content&&c.push("erase_content"),t.content.options&&c.push("options"),t.content.widgets&&c.push("widgets"),t.content.content&&c.push("content"),a&&c.push("install_finish"),c},Ce=function(e){var t=e.currentDemoWithVariation,n=e.demoConfiguration,r=e.pluginsStatus,o=(e.demoContent,n.plugins.filter((function(e){var t=e.enabled,n=e.plugin;return t&&!r[n]})).map((function(e){return e.plugin})));return{register_current_demo:{requests:[{title:(0,i.__)("Preparing data...","blocksy-companion"),params:{action:"blocksy_demo_register_current_demo",demo_name:t}}]},child_theme:{requests:[{title:(0,i.__)("Child theme","blocksy-companion"),params:{action:"blocksy_demo_install_child_theme"}}]},plugins:{requests:o.map((function(e){return{title:(0,i.sprintf)((0,i.__)("Installing %s","blocksy-companion"),me(e)),params:{action:"blocksy_demo_activate_plugins",plugins:e}}}))},erase_content:{requests:[{title:(0,i.__)("Erase content","blocksy-companion"),params:{action:"blocksy_demo_erase_content"}}]},options:{requests:[{title:(0,i.__)("Import options","blocksy-companion"),params:{action:"blocksy_demo_install_options",demo_name:t}}]},widgets:{requests:[{title:(0,i.__)("Import widgets","blocksy-companion"),params:{action:"blocksy_demo_install_widgets",demo_name:t}}]},content:{requests:ye(e)},install_finish:{requests:[{title:(0,i.__)("Final touches","blocksy-companion"),params:{action:"blocksy_demo_install_finish"}}]}}};function Se(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Pe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Se(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Se(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function De(e){if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(e=function(e,t){if(!e)return;if("string"==typeof e)return ze(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ze(e,t)}(e))){var t=0,n=function(){};return{s:n,n:function(){return t>=e.length?{done:!0}:{done:!1,value:e[t++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,o,a=!0,c=!1;return{s:function(){r=e[Symbol.iterator]()},n:function(){var e=r.next();return a=e.done,e},e:function(e){c=!0,o=e},f:function(){try{a||null==r.return||r.return()}finally{if(c)throw o}}}}function ze(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Me=function(e){var t=e.style,n=e.nextStep,a=((0,o.useContext)(J).is_child_theme,(0,o.useContext)(ft)),c=a.setCurrentlyInstalledDemo,s=a.setCurrentDemo,m=a.currentDemo,p=a.demos_list,d=a.pluginsStatus,b=a.setPluginsStatus,g=f((0,o.useState)("idle"),2),y=g[0],v=g[1],_=f((m||"").split(":"),2),E=_[0],w=(_[1],p.filter((function(e){return e.name===E}))),k=function(){var e=u(h().mark((function e(){var t,n,o,a,c,l,s,u;return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t={erase_content:{requests:[{title:(0,i.__)("Erase content","blocksy-companion"),params:{action:"blocksy_demo_erase_content",wp_customize:"on"}}]},deactivate_demo_plugins:{requests:[{title:(0,i.__)("Deactivate demo plugins","blocksy-companion"),params:{action:"blocksy_demo_deactivate_plugins",plugins:w[0].plugins.join(":")}}]},deregister_current_demo:{requests:[{title:(0,i.__)("Erase content","blocksy-companion"),params:{action:"blocksy_demo_deregister_current_demo"}}]}},n=0,o=["erase_content","deactivate_demo_plugins","deregister_current_demo"];case 3:if(!(n<o.length)){e.next=33;break}if(a=o[n],(c=t[a])&&0!==c.requests.length){e.next=8;break}return e.abrupt("continue",30);case 8:l=De(c.requests),e.prev=9,l.s();case 11:if((s=l.n()).done){e.next=22;break}return u=s.value,e.next=15,fetch(ke(u.params),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(Pe({},u.body||{}))});case 15:if(200===e.sent.status){e.next=19;break}return v("done"),e.abrupt("break",22);case 19:"deactivate_demo_plugins"===a&&b(Pe(Pe({},d),w[0].plugins.reduce((function(e,t){return Pe(Pe({},e),{},r({},t,!1))}),{})));case 20:e.next=11;break;case 22:e.next=27;break;case 24:e.prev=24,e.t0=e.catch(9),l.e(e.t0);case 27:return e.prev=27,l.f(),e.finish(27);case 30:n++,e.next=3;break;case 33:v("done");case 34:case"end":return e.stop()}}),e,null,[[9,24,27,30]])})));return function(){return e.apply(this,arguments)}}();return(0,o.createElement)(l.animated.div,{className:"ct-modify-demo",style:t},(0,o.createElement)("i",{className:"ct-demo-icon"},(0,o.createElement)("svg",{width:"36",height:"36",viewBox:"0 0 40 40"},(0,o.createElement)("path",{d:"M5.71,40a1,1,0,0,1-1-1V21.59a1,1,0,0,1,1.91,0V39.05A1,1,0,0,1,5.71,40Zm1-31.83V1.07A1,1,0,0,0,5.71,0a1,1,0,0,0-1,1.07v7.1a1,1,0,0,0,1,1.07A1,1,0,0,0,6.67,8.17ZM21,39.05V34.29a1,1,0,1,0-1.9,0v4.76a1,1,0,1,0,1.9,0Zm0-18.14V1a1,1,0,1,0-1.9,0V20.91a1,1,0,1,0,1.9,0ZM35.24,39.05V26.35a1,1,0,0,0-1.91,0v12.7a1,1,0,0,0,1.91,0Zm0-26.25V1a1,1,0,1,0-1.91,0V12.8a1,1,0,1,0,1.91,0Z",transform:"translate(-0.71)",fill:"#dae3e8"}),(0,o.createElement)("path",{d:"M5.71,18.06a5,5,0,1,1,5-5A5,5,0,0,1,5.71,18.06ZM20,30.76a5,5,0,1,1,5-5A5,5,0,0,1,20,30.76Zm14.29-7.93a5,5,0,1,1,5-5A5,5,0,0,1,34.29,22.83Z",transform:"translate(-0.71)",fill:"#0c7ab3"}))),"idle"===y&&(0,o.createElement)("h2",null,(0,i.__)("This starter site is already installed","blocksy-companion")),"loading"===y&&(0,o.createElement)("h2",null,"Removing starter site..."),"done"===y&&(0,o.createElement)(o.Fragment,null,(0,o.createElement)("h2",null,(0,i.__)("Starter Site Removed","blocksy-companion")),(0,o.createElement)("div",{className:"ct-modify-actions"},(0,o.createElement)("button",{className:"ct-demo-btn ct-dismiss",onClick:function(e){e.preventDefault(),s("".concat(E,":hide"))}},(0,i.__)("Dismiss","blocksy-companion")))),"idle"===y&&(0,o.createElement)(o.Fragment,null,(0,o.createElement)("p",null,(0,i.__)("What steps do you want to perform next?","blocksy-companion")),(0,o.createElement)("div",{className:"ct-modify-actions"},(0,o.createElement)("button",{className:"ct-demo-btn demo-remove",onClick:function(e){v("loading"),k(),e.preventDefault(),c()}},(0,i.__)("Remove","blocksy-companion")),(0,o.createElement)("button",{className:"ct-demo-btn",onClick:function(e){e.preventDefault(),n()}},(0,i.__)("Reinstall","blocksy-companion")))))};function Le(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ne(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Le(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Le(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Be=function(e){var t=e.demoConfiguration,n=e.setDemoConfiguration,r=e.style,a=(0,o.useContext)(J),c=a.is_child_theme,s=a.child_theme_exists;return(0,o.createElement)(l.animated.div,{className:"ct-demo-child",style:r},(0,o.createElement)("i",{className:"ct-demo-icon"},(0,o.createElement)("svg",{width:"40",height:"40",viewBox:"0 0 43 41.1"},(0,o.createElement)("path",{fill:"#DBE7EE",d:"M0,39.5c0,0.9,0.7,1.6,1.5,1.6h32.3c0.9,0,1.5-0.7,1.5-1.6V14H0V39.5z"}),(0,o.createElement)("path",{fill:"#BDC8D7",d:"M18.2,41.1h15.6c0.9,0,1.5-0.7,1.5-1.6V14H7.6L8,32.4L18.2,41.1z"}),(0,o.createElement)("path",{fill:"#BDC8D7",d:"M0,15.6V9.8c0-0.9,0.7-1.6,1.5-1.6h32.3c0.9,0,1.5,0.7,1.5,1.6v5.8H0z"}),(0,o.createElement)("path",{fill:"#3497D3",d:"M7.6,31.3c0,0.9,0.7,1.6,1.5,1.6h32.4c0.9,0,1.5-0.7,1.5-1.6V5.8H7.6V31.3z"}),(0,o.createElement)("path",{fill:"#0C7AB3",d:"M7.6,7.4V1.6C7.6,0.7,8.3,0,9.1,0h32.4C42.4,0,43,0.7,43,1.6v5.8H7.6z"}),(0,o.createElement)("rect",{x:"11.2",y:"11",fill:"#44ACDF",width:"16.8",height:"17.9"}),(0,o.createElement)("rect",{x:"31.5",y:"11",fill:"#44ACDF",width:"7.9",height:"17.9"}))),s?(0,o.createElement)("h2",null,(0,i.__)("Activate Child Theme","blocksy-companion")):(0,o.createElement)("h2",null,(0,i.__)("Install Child Theme","blocksy-companion")),!c&&(0,o.createElement)(o.Fragment,null,s?(0,o.createElement)("p",null,(0,i.__)("We strongly recommend to activate the child theme, this way you will have freedom to make changes without breaking the parent theme.","blocksy-companion")):(0,o.createElement)("p",null,(0,i.__)("We strongly recommend to install the child theme, this way you will have freedom to make changes without breaking the parent theme.","blocksy-companion")),(0,o.createElement)("div",{className:"ct-checkboxes-container","data-type":"bordered:reversed"},(0,o.createElement)(le,{checked:t.child_theme,position:"end",onChange:function(){return n(Ne(Ne({},t),{},{child_theme:!t.child_theme}))}},s?(0,i.__)("Activate Child Theme","blocksy-companion"):(0,i.__)("Install Child Theme","blocksy-companion")))),c&&(0,i.__)("You already have a child theme properly installed and activated. Move on.","blocksy-companion"),(0,o.createElement)("a",{href:"https://developer.wordpress.org/themes/advanced-topics/child-themes/",target:"_blank"},(0,i.__)("Learn more about child themes","blocksy-companion")))};function Fe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Te(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Fe(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Fe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Ae=function(e){var t=e.demoConfiguration,n=e.setDemoConfiguration,r=e.style,a=(0,o.useContext)(ft),c=a.currentDemo,s=a.demos_list,u=(a.pluginsStatus,a.setCurrentDemo,f((c||"").split(":"),2)),m=u[0],p=(u[1],s.filter((function(e){return e.name===m||""})));return(0,o.createElement)(l.animated.div,{style:r},p.length>1&&(0,o.createElement)("div",{className:"ct-demo-builder"},(0,o.createElement)("i",{className:"ct-demo-icon"},(0,o.createElement)("svg",{width:"52",height:"40",viewBox:"0 0 52 40"},(0,o.createElement)("path",{fill:"#DBE7EE",d:"M0,38.1C0,39.1,0.9,40,1.8,40h39.3c1.1,0,1.8-0.9,1.8-1.9v-31H0V38.1z"}),(0,o.createElement)("path",{fill:"#CFDBE4",d:"M13.8,14.6v18.8h22.6V14.6H13.8zM34.8,31.9H15.4V16.1h19.4V31.9z"}),(0,o.createElement)("path",{fill:"#BDC8D7",d:"M13.1,15.3L13.1,15.3c0-0.8,0.6-1.4,1.4-1.4l0,0c0.8,0,1.4,0.6,1.4,1.4l0,0c0,0.8-0.6,1.4-1.4,1.4l0,0C13.7,16.8,13.1,16.1,13.1,15.3z M34.1,15.3L34.1,15.3c0-0.8,0.6-1.4,1.4-1.4l0,0c0.8,0,1.4,0.6,1.4,1.4l0,0c0,0.8-0.6,1.4-1.4,1.4l0,0C34.8,16.8,34.1,16.1,34.1,15.3z M13.1,32.7L13.1,32.7c0-0.8,0.6-1.4,1.4-1.4l0,0c0.8,0,1.4,0.6,1.4,1.4l0,0c0,0.8-0.6,1.4-1.4,1.4l0,0C13.7,34.1,13.1,33.5,13.1,32.7z M34.1,32.7L34.1,32.7c0-0.8,0.6-1.4,1.4-1.4l0,0c0.8,0,1.4,0.6,1.4,1.4l0,0c0,0.8-0.6,1.4-1.4,1.4l0,0C34.8,34.1,34.1,33.5,34.1,32.7z M23.3,15.3L23.3,15.3c0-0.8,0.6-1.4,1.4-1.4l0,0c0.8,0,1.4,0.6,1.4,1.4l0,0c0,0.8-0.6,1.4-1.4,1.4l0,0C24,16.8,23.3,16.1,23.3,15.3z M4.2,13.9h5.9v7.9H4.2V13.9zM4.2,23.3h5.9v2.9H4.2V23.3zM0,9V1.9C0,0.9,0.9,0,1.8,0h39.3c1.1,0,1.8,0.9,1.8,1.9V9H0z M42.9,35.4V10.9h-9.3v15.2L42.9,35.4zM7.2,27.6c-1.6,0-3,1.3-3,3c0,1.6,1.3,3,3,3s3-1.3,3-3C10.2,28.9,8.8,27.6,7.2,27.6z"}),(0,o.createElement)("path",{fill:"#0C7AB3",d:"M50,27.8H35.6c-1.1,0-2-0.9-2-2v-18c0-1.1,0.9-2,2-2H50c1.1,0,2,0.9,2,2v18C52,26.9,51.1,27.8,50,27.8z"}),(0,o.createElement)("path",{fill:"#44ACDF",d:"M49,17.5H36.8c-0.7,0-1.2-0.5-1.2-1.2V9.1c0-0.7,0.5-1.2,1.2-1.2H49c0.7,0,1.2,0.5,1.2,1.2v7.3C50.2,17,49.6,17.5,49,17.5z M50.2,20.4v-0.1c0-0.5-0.4-1-1-1H36.5c-0.5,0-1,0.4-1,1v0.1c0,0.5,0.4,1,1,1h12.7C49.7,21.4,50.2,20.9,50.2,20.4z M40.8,25.2h-4.3c-0.5,0-1-0.4-1-1v-0.1c0-0.5,0.4-1,1-1h4.3c0.5,0,1,0.4,1,1v0.1C41.7,24.8,41.3,25.2,40.8,25.2z M49.2,25.2h-4.3c-0.5,0-1-0.4-1-1v-0.1c0-0.5,0.4-1,1-1h4.3c0.5,0,1,0.4,1,1v0.1C50.2,24.8,49.7,25.2,49.2,25.2z"}),(0,o.createElement)("path",{fill:"#C8E6F4",d:"M47.4,11.2h-9.1c-0.2,0-0.5-0.2-0.5-0.4v0c0-0.2,0.2-0.4,0.5-0.4h9.1c0.2,0,0.5,0.2,0.5,0.4v0C47.8,11,47.6,11.2,47.4,11.2z M47.9,14.7L47.9,14.7c0-0.2-0.2-0.5-0.5-0.5h-9.1c-0.2,0-0.4,0.2-0.4,0.4v0c0,0.2,0.2,0.4,0.4,0.4h9.1C47.7,15.1,47.9,14.9,47.9,14.7z"}),(0,o.createElement)("path",{fill:"#FFFFFF",d:"M26.3,20.8h-2.9l-2.9,7.9H23l0.3-0.7h2.8l0.3,0.7h2.7L26.3,20.8z M23.9,25.8l0.8-2.2h0l0.8,2.2H23.9zM46.5,10.7c0,0.8-0.7,1.5-1.5,1.5s-1.5-0.7-1.5-1.5c0-0.8,0.7-1.5,1.5-1.5S46.5,9.9,46.5,10.7zM42.1,14.7c0,0.8-0.7,1.5-1.5,1.5s-1.5-0.7-1.5-1.5s0.7-1.5,1.5-1.5S42.1,13.9,42.1,14.7z"}))),(0,o.createElement)("h2",null,(0,i.__)("Choose Page Builder","blocksy-companion")),(0,o.createElement)("p",null,(0,i.__)("This starter site can be imported and used with one of these page builders. Please select one in order to continue.","blocksy-companion")),(0,o.createElement)("ul",{"data-count":p.length},p.sort((function(e,t){return e.builder<t.builder?-1:e.builder>t.builder?1:0})).map((function(e){var r=e.builder,a=e.plugins;return(0,o.createElement)("li",{className:R()({active:r===(null===t.builder?p[0].builder:t.builder)}),onClick:function(){n(Te(Te({},t),{},{builder:r,plugins:a.map((function(e){return{plugin:e,enabled:!0}}))}))}},(0,o.createElement)("figure",null,(0,o.createElement)("span",{className:R()("ct-checkbox",{active:r===(null===t.builder?p[0].builder:t.builder)})},(0,o.createElement)("svg",{width:"10",height:"8",viewBox:"0 0 11.2 9.1"},(0,o.createElement)("polyline",{className:"check",points:"1.2,4.8 4.4,7.9 9.9,1.2 "}))),""===r&&(0,o.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 150 100"},(0,o.createElement)("path",{d:"M122.5 35.5c-1.7-1.1-4-.7-5.1 1C110.8 46.4 96.8 47 96 47h-.3c-17.4 0-24 14.8-24.3 15.4-.8 1.9.1 4 1.9 4.8.5.2 1 .3 1.5.3 1.4 0 2.7-.8 3.4-2.2.1-.1 4.6-10.3 16.3-11v19c-.5 4.1-2.4 7.3-5.8 9.7-3.6 2.5-8.3 3.8-14.1 3.8-7 0-12.7-2.4-16.9-7.2-4.3-4.8-6.4-11.5-6.4-20.2l.1-20.9c.3-7.7 2.4-13.8 6.4-18.2 4.3-4.8 9.9-7.2 16.9-7.2 5.8 0 10.6 1.3 14.1 3.8 3.6 2.5 5.6 5.9 5.9 10.3v.5c0 2.5 2.1 4.6 4.6 4.6 2.5 0 4.6-2.1 4.6-4.6v-.5c-.7-6.6-3.7-11.9-9.1-15.8-5.4-4-12.2-5.9-20.4-5.9-9.7 0-17.6 3.2-23.5 9.6-5.6 6-8.6 13.8-8.9 23.5 0 .7-.1 1.3-.1 2l.1 18.8h-.1c0 10.7 3 19.2 9 25.5 6 6.4 13.8 9.6 23.5 9.6 8.2 0 14.9-1.9 20.4-5.9 5-3.6 7.9-8.4 8.9-14.3l.2-21c6.1-1.5 14.4-4.8 19.6-12.7 1.3-1.7.8-4-1-5.1z"})),"brizy"===r&&(0,o.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 150 100"},(0,o.createElement)("path",{d:"M14.6 36.7L75 0l60.4 36.7L75 73.4 14.6 36.7zm21.7.9L75 61.2l38.8-23.6L75 14 36.3 37.6z",fill:"#181c25"}),(0,o.createElement)("path",{fill:"#a7b2dd",d:"M14.6 63.2l10.8-6.5L75 86.8l49.9-30 10.5 6.4L75 100z"})),"elementor"===r&&(0,o.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 150 100"},(0,o.createElement)("path",{d:"M32.5 7.6h17v84.9h-17V7.6zm34 84.9h51v-17h-51v17zm0-34h51v-17h-51v17zm0-51v17h51v-17h-51z"}))),(0,o.createElement)("div",{className:"builder-name"},me(r)||"Gutenberg"))})))))};function Ve(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function He(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ve(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ve(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Ie=function(e){var t=e.demoConfiguration,n=e.setDemoConfiguration,a=(e.currentDemo,e.style);return(0,o.createElement)(l.animated.div,{style:a},(0,o.createElement)("div",{className:"ct-demo-content"},(0,o.createElement)("i",{className:"ct-demo-icon"},(0,o.createElement)("svg",{width:"40",height:"40",viewBox:"0 0 40 40"},(0,o.createElement)("path",{d:"M25,22.67a5,5,0,0,1-10,0H0V36a3.33,3.33,0,0,0,3.33,3.33H36.67A3.33,3.33,0,0,0,40,36V22.67Z",transform:"translate(0 -0.67)",fill:"#bdc8d7"}),(0,o.createElement)("rect",{x:"2.5",y:"14",width:"35",height:"3",rx:"1.5",fill:"#0c7ab3"}),(0,o.createElement)("rect",{x:"5",y:"7",width:"30",height:"3",rx:"1.5",fill:"#3497d3"}),(0,o.createElement)("rect",{x:"7.5",width:"25",height:"3",rx:"1.5",fill:"#44acdf"}))),(0,o.createElement)("h2",null,(0,i.__)("Import Content","blocksy-companion")),(0,o.createElement)("p",null,(0,i.__)("This will import posts, pages, comments, navigation menus, custom fields, terms and custom posts.","blocksy-companion")),(0,o.createElement)("div",{className:"ct-checkboxes-container","data-type":"reversed"},["options","widgets","content"].map((function(e){return(0,o.createElement)(le,{checked:t.content[e],onChange:function(){return n(He(He({},t),{},{content:He(He({},t.content),{},r({},e,!t.content[e]))}))},key:e},e.split("_").map((function(e){return e.replace(/^\w/,(function(e){return e.toUpperCase()}))})).join(" "))})),(0,o.createElement)(le,{checked:t.content.erase_content,className:"ct-demo-erase",onChange:function(){return n(He(He({},t),{},{content:He(He({},t.content),{},{erase_content:!t.content.erase_content})}))}},(0,o.createElement)("div",null,(0,i.__)("Clean Install","blocksy-companion"),(0,o.createElement)("i",null,(0,i.__)("This option will remove the previous imported content and will perform a fresh and clean install.","blocksy-companion")))))))},Ge=function(){var e=(0,o.useContext)(J),t=e.home_url,n=e.customizer_url;return(0,o.createElement)("div",{className:"ct-install-success"},(0,o.createElement)("h2",null,(0,i.__)("Starter Site Imported Successfully","blocksy-companion")),(0,o.createElement)("p",null,(0,i.__)("Now you can view your website or start customizing it","blocksy-companion")),(0,o.createElement)("div",null,(0,o.createElement)("a",{href:n,className:"ct-button"},(0,i.__)("Customize","blocksy-companion")),(0,o.createElement)("a",{href:t,target:"_blank",className:"ct-button-primary"},(0,i.__)("View site","blocksy-companion"))))},Re=function(e){var t=e.lastMessage,n=e.progress;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)("i",{className:"ct-demo-icon"},(0,o.createElement)("svg",{width:"40",height:"40",viewBox:"0 0 50 50"},(0,o.createElement)("path",{class:"g1",d:"M47,38.8c0.3-1,0.5-2,0.5-3.1c0-1.1-0.2-2.1-0.5-3.1l0.2-0.1l1.8-1.7l-1.8-3.1l-2.3,0.7l-0.2,0.1c-1.4-1.5-3.3-2.7-5.4-3.1V25l-0.6-2.4h-3.5L34.5,25v0.3c-2.1,0.5-4,1.6-5.4,3.1l-0.2-0.1l-2.3-0.7l-1.8,3.1l1.7,1.7l0.2,0.1c-0.3,1-0.5,2-0.5,3.1c0,1.1,0.2,2.1,0.5,3.1l-0.2,0.1l-1.8,1.7l1.8,3.1l2.3-0.7l0.2-0.1c1.4,1.5,3.3,2.7,5.4,3.1v0.3l0.6,2.4h3.5l0.6-2.4V46c2.1-0.5,4-1.6,5.4-3.1l0.2,0.1l2.3,0.7l1.8-3.1l-1.7-1.7L47,38.8z M36.9,41.5c-3.3,0-5.9-2.6-5.9-5.9s2.6-5.9,5.9-5.9s5.9,2.6,5.9,5.9S40.1,41.5,36.9,41.5z"}),(0,o.createElement)("path",{class:"g2",d:"M21.2,32.2c0.2-0.8,0.4-1.7,0.4-2.5c0-0.9-0.1-1.7-0.4-2.5l0.3-0.2l1.7-1.7l-1.8-3.1L19.1,23l-0.3,0.2c-1.2-1.2-2.7-2.1-4.4-2.5v-0.3l-0.6-2.4h-3.5l-0.6,2.4v0.3c-1.7,0.4-3.2,1.3-4.4,2.5L5.1,23l-2.3-0.7L1,25.4L2.7,27L3,27.2c-0.2,0.8-0.4,1.7-0.4,2.5c0,0.9,0.1,1.7,0.4,2.5l-0.3,0.1L1,34.1l1.8,3.1l2.3-0.7l0.3-0.1c1.2,1.2,2.7,2.1,4.4,2.5v0.3l0.6,2.4h3.5l0.6-2.4v-0.3c1.7-0.4,3.2-1.3,4.4-2.5l0.3,0.1l2.3,0.7l1.8-3.1l-1.7-1.7L21.2,32.2z M12.1,34.4c-2.6,0-4.7-2.1-4.7-4.7S9.5,25,12.1,25s4.7,2.1,4.7,4.7S14.7,34.4,12.1,34.4z"}),(0,o.createElement)("path",{class:"g3",d:"M37.7,15.7c0.2-0.8,0.4-1.7,0.4-2.5c0-0.9-0.1-1.7-0.4-2.5l0.3-0.2l1.7-1.7l-1.8-3.1l-2.3,0.7l-0.3,0.2c-1.2-1.2-2.7-2.1-4.4-2.5V3.8l-0.6-2.4h-3.5l-0.6,2.4v0.3c-1.7,0.4-3.2,1.3-4.4,2.5l-0.3-0.2l-2.3-0.7l-1.8,3.1l1.7,1.7l0.3,0.2c-0.2,0.8-0.4,1.7-0.4,2.5c0,0.9,0.1,1.7,0.4,2.5l-0.3,0.1l-1.7,1.7l1.8,3.1l2.3-0.7l0.3-0.1c1.2,1.2,2.7,2.1,4.4,2.5v0.3l0.6,2.4h3.5l0.6-2.4v-0.3c1.7-0.4,3.2-1.3,4.4-2.5l0.3,0.1l2.3,0.7l1.8-3.1L38,15.9L37.7,15.7z M28.6,17.9c-2.6,0-4.7-2.1-4.7-4.7s2.1-4.7,4.7-4.7s4.7,2.1,4.7,4.7S31.2,17.9,28.6,17.9z"}))),(0,o.createElement)("h2",null,(0,i.__)("Installing","blocksy-companion"),"..."),(0,o.createElement)("p",null,(0,i.__)("Please be patient and don't refresh this page, the import process may take a while, this also depends on your server.","blocksy-companion")),(0,o.createElement)("div",{className:"ct-progress-info"},t,(0,o.createElement)("span",null,Math.round(n),"%")),(0,o.createElement)("div",{style:{"--progress":"".concat(n,"%")},className:"ct-installer-progress"},(0,o.createElement)("div",null)))},qe=function(e){var t=e.isError;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)("i",{className:"ct-demo-icon"},(0,o.createElement)("svg",{width:"37",height:"37",viewBox:"0 0 40 40"},(0,o.createElement)("path",{fill:"#BDC8D7",d:"M30.7,25.4L14.6,9.3c0.7-2.5,0-5.2-1.9-7.2c-2.4-2.3-6-2.7-8.8-1.3l4.5,4.5L7.9,7.9L5.3,8.4L0.8,3.9c-1.5,2.8-1,6.4,1.3,8.7c2,2,4.7,2.6,7.2,1.9l16.1,16.1c-0.7,2.5,0,5.2,1.9,7.2c2.3,2.3,5.9,2.8,8.7,1.3l-4.5-4.5L32,32l2.6-0.5l4.5,4.5c1.5-2.8,1-6.4-1.3-8.7C35.9,25.4,33.1,24.7,30.7,25.4z"}),(0,o.createElement)("polygon",{fill:"#44ACDF",points:"34.6,11.3 39.8,3.7 36.3,0.2 28.7,5.4 28.7,7.8 11.8,24.7 15.3,28.2 32.2,11.3 "}),(0,o.createElement)("path",{fill:"#0C7AB3",d:"M18.4,27.5l-5.9-5.9c-0.4-0.4-1-0.4-1.4,0s-0.4,1,0,1.4l0,0L0.7,33.5c-0.7,0.7-0.7,1.7,0,2.3l3.5,3.5c0.7,0.7,1.7,0.7,2.3,0L17,28.9l0,0c0.2,0.2,0.5,0.3,0.7,0.3s0.5-0.1,0.7-0.3C18.8,28.5,18.8,27.9,18.4,27.5z"}))),(0,o.createElement)("h2",null,(0,i.__)("Can't Import Starter Site","blocksy-companion")),(0,o.createElement)("p",null,t),(0,o.createElement)("a",{href:"https://creativethemes.com/blocksy/docs/troubleshooting/starter-site-import-stuck-at-xx/",className:"ct-demo-btn",target:"_blank"},"More Information"))},Ue=function(e){var t=e.demoConfiguration,n=e.style,r=function(e){var t=(0,o.useContext)(ft),n=t.demos_list,r=t.currentDemo,a=t.setInstallerBlockingReleased,c=t.setCurrentlyInstalledDemo,l=t.pluginsStatus,s=(0,o.useContext)(J).is_child_theme,m=f((0,o.useState)(!1),2),p=m[0],d=m[1],b=f((0,o.useState)(null),2),g=b[0],y=b[1],v=f((0,o.useState)(!1),2),_=v[0],E=v[1],w=f((0,o.useState)(0),2),k=w[0],O=w[1],x=(0,o.useMemo)((function(){var t=f((r||"").split(":"),2),o=t[0],a=(t[1],n.filter((function(e){return e.name===o})).sort((function(e,t){return e.builder<t.builder?-1:e.builder>t.builder?1:0})));return"".concat(r,":").concat(null===e.builder?a[0].builder:e.builder)}),[r,e,n]),j=(0,o.useMemo)((function(){return je({demoConfiguration:e,pluginsStatus:l,is_child_theme:s,includeMetaSteps:!0})}),[e,l,s]),C=(0,o.useCallback)(function(){var t=u(h().mark((function t(n){var r,o,c,i,s,u,m,p,f,b,g,v,_;return h().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:r=Ce({currentDemoWithVariation:x,demoConfiguration:e,pluginsStatus:l,demoContent:n}),o=j.reduce((function(e,t){return e+r[t].requests.length}),0),c=0,i={},s=ve(j),t.prev=5,s.s();case 7:if((u=s.n()).done){t.next=49;break}if(m=u.value,(p=r[m])&&0!==p.requests.length){t.next=12;break}return t.abrupt("continue",47);case 12:f=ve(p.requests),t.prev=13,f.s();case 15:if((b=f.n()).done){t.next=39;break}return g=b.value,y(g.title),t.next=20,fetch(ke(g.params),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(we({requestsPayload:i},g.body||{}))});case 20:if(200===(v=t.sent).status){t.next=24;break}return E(Oe),t.abrupt("break",39);case 24:return t.next=26,v.json();case 26:if(_=t.sent){t.next=30;break}return E(Oe),t.abrupt("break",39);case 30:if(_.success){t.next=33;break}return E(_.data&&_.data.message?_.data.message:Oe),t.abrupt("break",39);case 33:_.data&&null!=_.data&&"Object"===_.data.constructor.name&&(i=we(we({},i),_.data)),c++,O(c/o*100),o===c&&(console.timeEnd("Blocksy:Dashboard:DemoInstall"),d(!0),a(!0),window.removeEventListener("beforeunload",xe));case 37:t.next=15;break;case 39:t.next=44;break;case 41:t.prev=41,t.t0=t.catch(13),f.e(t.t0);case 44:return t.prev=44,f.f(),t.finish(44);case 47:t.next=7;break;case 49:t.next=54;break;case 51:t.prev=51,t.t1=t.catch(5),s.e(t.t1);case 54:return t.prev=54,s.f(),t.finish(54);case 57:case"end":return t.stop()}}),t,null,[[5,51,54,57],[13,41,44,47]])})));return function(e){return t.apply(this,arguments)}}(),[j,O]),S=(0,o.useCallback)(u(h().mark((function t(){var n,o;return h().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return window.addEventListener("beforeunload",xe),console.time("Blocksy:Dashboard:DemoInstall"),c({demo:"".concat(r,":").concat(e.builder)}),y((0,i.__)("Preparing data...","blocksy-companion")),t.next=6,fetch(ke({action:"blocksy_demo_get_content_preliminary_data",demo_name:x}));case 6:if(200!==(n=t.sent).status){t.next=15;break}return t.next=10,n.json();case 10:if((o=t.sent).success){t.next=14;break}return E(o.data.message||Oe),t.abrupt("return");case 14:C(o.data);case 15:case"end":return t.stop()}}),t)}))),[C,x]);return(0,o.useEffect)((function(){S()}),[]),{isCompleted:p,isError:_,lastMessage:g,progress:k}}(t),a=r.isCompleted,c=r.isError,s=r.lastMessage,m=r.progress,p=a?"complete":c?"error":"progress";return(0,o.createElement)(l.animated.div,{className:"ct-demo-install",style:n},(0,o.createElement)(l.Transition,{initial:!0,items:p,from:{opacity:0},enter:[{opacity:1}],leave:[{opacity:0}],config:function(e,t){return"leave"===t?{duration:300}:{delay:300,duration:300}}},(function(e,t){return(0,o.createElement)(l.animated.div,{style:e},"complete"===t&&(0,o.createElement)(Ge,null),"error"===t&&(0,o.createElement)(qe,{isError:c}),"progress"===t&&(0,o.createElement)(Re,{lastMessage:s,progress:m}))})))},We=function(e){e.location,e.navigate;var t=f((0,o.useState)(!0),2),n=(t[0],t[1],(0,o.useContext)(ft)),r=n.installerBlockingReleased,a=n.demos_list,c=n.currentDemo,s=n.pluginsStatus,u=n.currentlyInstalledDemo,m=n.setCurrentDemo,p=n.filters,d=(0,o.useContext)(J).is_child_theme,h=f((0,o.useState)(u),2),b=h[0],y=h[1],v=f((0,o.useState)({builder:"",child_theme:!1,plugins:[],content:{options:!0,widgets:!0,content:!0,erase_content:!0}}),2),_=v[0],E=v[1],w=f((0,o.useState)(0),2),k=w[0],O=w[1],x=f((c||"").split(":"),2),j=x[0],C=(x[1],["modify_demo","child_theme","builder","plugins","content","installer"].filter((function(e){if(!c)return!1;if("modify_demo"===e){if(!b)return!1;if(-1===b.demo.indexOf(j))return!1}if("child_theme"===e&&d)return!1;var t=a.filter((function(e){return e.name===j})).sort((function(e,t){return e.builder<t.builder?-1:e.builder>t.builder?1:0}));return("plugins"!==e||0!==t.reduce((function(e,t){return[].concat(g(e),g(t.plugins||[]))}),[]).filter((function(e){return!s[e]})).length)&&("builder"!==e||t.length>1)}))),S=C[k];return(0,o.useEffect)((function(){if(j&&!(c.indexOf(":hide")>-1)){var e=a.filter((function(e){return e.name===j})).sort((function(e,t){return e.builder<t.builder?-1:e.builder>t.builder?1:0}));O(0),y(u);var t=e[0];if("all"!==p.builder){var n=e.find((function(e){return e.builder===p.builder}));n&&(t=n)}E({builder:t.builder,child_theme:!1,plugins:t.plugins.map((function(e){return{plugin:e,enabled:!0}})),content:{options:!0,widgets:!0,content:!0,erase_content:!0}})}}),[c]),(0,o.createElement)(l.Overlay,{items:c,isVisible:function(e){return e&&-1===e.indexOf(":hide")},className:R()("ct-demo-modal",{"ct-demo-installer":"installer"===S||"modify_demo"===S}),onDismiss:function(){("installer"!==S||r)&&m("".concat(j,":hide"))},render:function(){return(0,o.createElement)("div",{className:"ct-modal-content ct-demo-step-container"},(0,o.createElement)("div",{className:"ct-current-step"},(0,o.createElement)(l.Transition,{items:S,from:{opacity:0},enter:{opacity:1},leave:{opacity:0},initial:!1,config:function(e,t){return"leave"===t?{duration:150}:{delay:150,duration:150}}},(function(e,t){return(0,o.createElement)(o.Fragment,null,"modify_demo"===t&&(0,o.createElement)(Me,{demoConfiguration:_,nextStep:function(){O(Math.min(k+1,C.length-1))},style:e}),"child_theme"===t&&(0,o.createElement)(Be,{style:e,demoConfiguration:_,setDemoConfiguration:E}),"plugins"===t&&(0,o.createElement)(pe,{demoConfiguration:_,style:e,setDemoConfiguration:E}),"builder"===t&&(0,o.createElement)(Ae,{style:e,demoConfiguration:_,setDemoConfiguration:E}),"content"===t&&(0,o.createElement)(Ie,{style:e,demoConfiguration:_,setDemoConfiguration:E}),"installer"===t&&(0,o.createElement)(Ue,{style:e,demoConfiguration:_}))}))),"installer"!==S&&"modify_demo"!==S&&(0,o.createElement)("div",{className:"ct-demo-step-controls"},k>0&&(0,o.createElement)("button",{className:"ct-demo-btn demo-back-btn",onClick:function(){O(Math.max(k-1,0))}},(0,i.__)("Back","blocksy-companion")),C.length>2&&(0,o.createElement)("ul",{className:"ct-steps-pills"},C.map((function(e,t){return t===C.length-1?null:(0,o.createElement)("li",{className:R()({active:e===S}),key:e},t+1)}))),(0,o.createElement)("button",{className:"ct-demo-btn demo-main-btn",disabled:"content"===S&&0===je({demoConfiguration:_,pluginsStatus:s,is_child_theme:d}).length,onClick:function(){O(Math.min(k+1,C.length-1))}},"content"===S?(0,i.__)("Install","blocksy-companion"):(0,i.__)("Next","blocksy-companion"))))}})},Ze=window.wp.components,Je=window.React;function Ye(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Ye(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}var Xe=function(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Ye(e))&&(r&&(r+=" "),r+=t);return r};const $e=(0,o.forwardRef)((({className:e,isPressed:t,...n},r)=>{const a={...n,className:Xe(e,{"is-pressed":t})||void 0,"aria-hidden":!0,focusable:!1};return(0,o.createElement)("svg",{...a,ref:r})}));$e.displayName="SVG";var Ke=(0,Je.createElement)($e,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,Je.createElement)((e=>(0,o.createElement)("path",e)),{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})),Qe=n(162),et=n.n(Qe);function tt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function nt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?tt(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):tt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var rt=function(e){e.allPlans,e.allCategories;var t=f((0,o.useState)(!1),2),n=t[0],r=t[1],a=f((0,o.useState)(!1),2),c=a[0],s=a[1],m=f((0,o.useState)([]),2),p=m[0],d=m[1],b=f((0,o.useState)(null),2),y=b[0],v=b[1],_=f((0,o.useState)(""),2),E=_[0],w=_[1],k=f((0,o.useState)([]),2),O=k[0],x=k[1];(0,o.useEffect)((function(){var e=function(){var e=u(h().mark((function e(){var t,n,r,o,a,c;return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return(t=new FormData).append("action","blocksy_demo_get_export_data"),t.append("nonce",ctDashboardLocalizations.dashboard_actions_nonce),e.prev=3,e.next=6,fetch(ctDashboardLocalizations.ajax_url,{method:"POST",body:t});case 6:if(200!==(n=e.sent).status){e.next=14;break}return e.next=10,n.json();case 10:r=e.sent,o=r.success,a=r.data,o&&((c=a.data)&&c.builder&&w(c.builder),c&&c.plugins&&x(c.plugins),c&&c.demoId&&v(parseFloat(c.demoId)));case 14:e.next=18;break;case 16:e.prev=16,e.t0=e.catch(3);case 18:case"end":return e.stop()}}),e,null,[[3,16]])})));return function(){return e.apply(this,arguments)}}(),t=function(){var e=u(h().mark((function e(){var t,n;return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch("https://creativethemes.com/blocksy/wp-json/ct/v1/starter-sites",{method:"GET"});case 3:if(200!==(t=e.sent).status){e.next=9;break}return e.next=7,t.json();case 7:n=e.sent,d(n);case 9:e.next=13;break;case 11:e.prev=11,e.t0=e.catch(0);case 13:case"end":return e.stop()}}),e,null,[[0,11]])})));return function(){return e.apply(this,arguments)}}();e(),t()}),[]);var j=function(){var e=u(h().mark((function e(){var t,n,o,a,c,i,l,s;return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r(!0),(t=new FormData).append("action","blocksy_demo_export"),t.append("nonce",ctDashboardLocalizations.dashboard_actions_nonce),t.append("demoId",y),t.append("builder",E),t.append("plugins",O.join(",")),t.append("wp_customize","on"),e.prev=8,e.next=11,fetch(ctDashboardLocalizations.ajax_url,{method:"POST",body:t});case 11:if(200!==(n=e.sent).status){e.next=19;break}return e.next=15,n.json();case 15:o=e.sent,a=o.success,c=o.data,a&&(i=p.find((function(e){return e.id===y})),"elementor"===(l=nt(nt({name:i.title,url:i.demo_live_link,is_pro:"Pro"===i.is_pro,categories:i.categories,keywords:i.keywords},"Pro"===i.is_pro&&i.plans?{plans:["personal","professional","agency"].concat(g(i.plans.map((function(e){return"".concat(e,"_v2")}))))}:{}),c.demo)).builder.toLowerCase()&&(l.elementor_experiment_container=!0),console.log("Blocksy:Dashboard:DemoInstall:exported",{remoteDemo:i,finalDemo:l}),s=new Blob([JSON.stringify(l)],{type:"text/plain;charset=utf-8"}),et().saveAs(s,"".concat(i.title,".json")));case 19:e.next=23;break;case 21:e.prev=21,e.t0=e.catch(8);case 23:r(!1);case 24:case"end":return e.stop()}}),e,null,[[8,21]])})));return function(){return e.apply(this,arguments)}}();return(0,o.createElement)("div",{className:"ct-filter-trigger-export"},(0,o.createElement)("button",{type:"button",className:"components-button has-icon has-text",onClick:function(e){s(!0)}},(0,o.createElement)("svg",{"aria-hidden":"true",width:"24",height:"24",viewBox:"0 0 24 24",fillRule:"evenodd",fill:"currentColor"},(0,o.createElement)("path",{d:"M20 15v5H4v-5h1.5v3.5h6V6.8l-4 4-1-1.1L12.2 4l6.3 5.7-1 1.1L13 6.7v11.8h5.5V15H20z"})),(0,i.__)("Export Site")),(0,o.createElement)(l.Overlay,{items:c,className:"ct-site-export-modal",onDismiss:function(){return s(!1)},render:function(){return(0,o.createElement)("div",{className:"ct-modal-content"},(0,o.createElement)("h2",null,"Export Settings"),(0,o.createElement)("div",{className:"ct-site-export-settings ct-modal-scroll"},(0,o.createElement)("section",{className:"general-section has-divider"},(0,o.createElement)("label",null,(0,o.createElement)("span",{className:"ct-label"},(0,i.__)("Starter site","blocksy-companion")),(0,o.createElement)("select",{value:y,onChange:function(e){var t=e.target.value;return v(parseFloat(t))}},(0,o.createElement)("option",{value:""},(0,i.__)("Select a starter site","blocksy-companion")),p.sort((function(e,t){var n=e.title.toLowerCase(),r=t.title.toLowerCase();return n<r?-1:n>r?1:0})).map((function(e){return(0,o.createElement)("option",{value:e.id,key:e.id},e.title)})))),(0,o.createElement)("label",null,(0,o.createElement)("span",{className:"ct-label"},(0,i.__)("Builder","blocksy-companion")),(0,o.createElement)("input",{type:"text",placeholder:(0,i.__)("Builder","blocksy-companion"),value:E,onChange:function(e){var t=e.target.value;return w(t)}}))),(0,o.createElement)("section",{className:"plugins-section"},(0,o.createElement)("h4",null,"Required plugins"),(0,o.createElement)("div",{className:"ct-bundled-plugins-list grid-labels"},Object.keys({gutenberg:"Gutenberg","stackable-ultimate-gutenberg-blocks":"Stackable - Gutenberg Blocks","wpforms-lite":"WPForms - Contact Form",woocommerce:"WooCommerce",elementor:"Elementor",brizy:"Brizy",getwid:"Getwid","simply-gallery-block":"SimpLy Gallery Block & Lightbox","recipe-card-blocks-by-wpzoom":"Recipe Card Blocks by WPZOOM","map-block-gutenberg":"Map Block for Google Maps","mb-custom-post-type":"MB Custom Post Types & Custom Taxonomies",leadin:"HubSpot","block-slider":"Block Slider","ht-slider-for-elementor":"HT Slider For Elementor","modula-best-grid-gallery":"Modula - Image Gallery","advanced-custom-fields":"Advanced Custom Fields (ACF)","greenshift-animation-and-page-builder-blocks":"Greenshift – Gutenberg Blocks",fluentform:"Fluent Forms","translatepress-multilingual":"TranslatePress","fluent-booking":"Fluent Booking"}).filter((function(e){return"gutenberg"!==e})).map((function(e){return(0,o.createElement)("label",{tabindex:"0",onClick:function(t){t.preventDefault(),x((function(t){return t.includes(e)?t.filter((function(t){return t!==e})):[].concat(g(t),[e])}))}},(0,o.createElement)("input",{type:"checkbox",checked:O.indexOf(e)>-1,onChange:function(e){e.target.checked}}),(0,o.createElement)("span",null,{gutenberg:"Gutenberg","stackable-ultimate-gutenberg-blocks":"Stackable - Gutenberg Blocks","wpforms-lite":"WPForms - Contact Form",woocommerce:"WooCommerce",elementor:"Elementor",brizy:"Brizy",getwid:"Getwid","simply-gallery-block":"SimpLy Gallery Block & Lightbox","recipe-card-blocks-by-wpzoom":"Recipe Card Blocks by WPZOOM","map-block-gutenberg":"Map Block for Google Maps","mb-custom-post-type":"MB Custom Post Types & Custom Taxonomies",leadin:"HubSpot","block-slider":"Block Slider","ht-slider-for-elementor":"HT Slider For Elementor","modula-best-grid-gallery":"Modula - Image Gallery","advanced-custom-fields":"Advanced Custom Fields (ACF)","greenshift-animation-and-page-builder-blocks":"Greenshift – Gutenberg Blocks",fluentform:"Fluent Forms","translatepress-multilingual":"TranslatePress","fluent-booking":"Fluent Booking"}[e]))}))))),(0,o.createElement)("div",{className:"ct-modal-actions has-divider"},(0,o.createElement)("button",{className:"button button-primary",disabled:n,onClick:function(){return j()}},n?(0,i.__)("Loading...","blocksy-companion"):(0,i.__)("Export site","blocksy-companion"))))}}))};function ot(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function at(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ot(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ot(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var ct={all:(0,i.__)("All Plans","blocksy-companion"),free:(0,i.__)("Free","blocksy-companion"),pro:(0,i.__)("Pro","blocksy-companion")},it=function(){var e=(0,o.useContext)(ft),t=e.unfiltered_demos_list,n=e.filters,r=e.setFilters,a=e.allPlans,c=e.allCategories,l=t.reduce((function(e,t){var n="gutenberg";return t.builder&&(n=t.builder),e.includes(n)||e.push(n),e}),[]);return(0,o.createElement)("div",{className:"ct-demo-filters"},(0,o.createElement)(Ze.DropdownMenu,{className:"ct-filter-trigger-categories",menuProps:{className:"ct-filter-dropdown-categories"},icon:(0,o.createElement)("svg",{"aria-hidden":"true",viewBox:"0 0 24 24",fillRule:"evenodd",fill:"currentColor"},(0,o.createElement)("path",{d:"M18 5.5h-3c-.3 0-.5.2-.5.5v3c0 .*******.5h3c.3 0 .5-.2.5-.5V6c0-.3-.2-.5-.5-.5zm2 .5c0-1.1-.9-2-2-2h-3c-1.1 0-2 .9-2 2v3c0 1.1.9 2 2 2h3c1.1 0 2-.9 2-2V6zM9 14.5H6c-.3 0-.5.2-.5.5v3c0 .*******.5h3c.3 0 .5-.2.5-.5v-3c0-.3-.2-.5-.5-.5zm2 .5c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2v3c0 1.1.9 2 2 2h3c1.1 0 2-.9 2-2v-3zm4-.5h3c.3 0 .5.2.5.5v3c0 .3-.2.5-.5.5h-3c-.3 0-.5-.2-.5-.5v-3c0-.3.2-.5.5-.5zm3-1.5c1.1 0 2 .9 2 2v3c0 1.1-.9 2-2 2h-3c-1.1 0-2-.9-2-2v-3c0-1.1.9-2 2-2h3zM9 5.5H6c-.3 0-.5.2-.5.5v3c0 .*******.5h3c.3 0 .5-.2.5-.5V6c0-.3-.2-.5-.5-.5zm2 .5c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2v3c0 1.1.9 2 2 2h3c1.1 0 2-.9 2-2V6z"})),label:"all"===n.category?(0,i.__)("All Categories","blocksy-companion"):n.category,text:"all"===n.category?(0,i.__)("All Categories","blocksy-companion"):n.category},(function(){return["all"].concat(g(c)).map((function(e){var t=n.category===e;return(0,o.createElement)(Ze.MenuItem,{key:e,icon:t?(0,o.cloneElement)(Ke,{width:24,height:24}):null,isSelected:t,onClick:function(){r(at(at({},n),{},{category:e}))}},"all"===e?(0,i.__)("All Categories","blocksy-companion"):e)}))})),(0,o.createElement)(Ze.DropdownMenu,{className:"ct-filter-trigger-plans",menuProps:{className:"ct-filter-dropdown-plans"},icon:(0,o.createElement)("svg",{"aria-hidden":"true",viewBox:"0 0 24 24",fillRule:"evenodd",fill:"currentColor"},(0,o.createElement)("path",{d:"m17.3 19.8-5.2-2.7h-.2l-5.2 2.7 1-5.8c0-.1 0-.2-.1-.2l-4.2-4L9.2 9c.1 0 .2-.1.2-.1L12 3.6l2.6 5.2c0 .1.1.1.2.1l5.8.8-4.2 4.1c-.1.1-.1.1-.1.2l1 5.8zM12 15.6c.3 0 .6.1.8.2l2.5 1.3-.5-2.8c-.1-.6.1-1.1.5-1.5l2-2-2.8-.4c-.6-.1-1.1-.4-1.3-1L12 6.9l-1.2 2.5c-.3.5-.7.9-1.3 1l-2.8.4 2 2c.4.4.6 1 .5 1.5l-.5 2.8 2.5-1.3c.2-.1.5-.2.8-.2z"})),label:ct[n.plan],text:ct[n.plan]},(function(){return["all","free","pro"].map((function(e){var t=n.plan===e;return(0,o.createElement)(Ze.MenuItem,{key:e,icon:t?(0,o.cloneElement)(Ke,{width:24,height:24}):null,isSelected:t,onClick:function(){r(at(at({},n),{},{plan:e}))}},ct[e])}))})),(0,o.createElement)(Ze.DropdownMenu,{className:"ct-filter-trigger-builders",menuProps:{className:"ct-filter-dropdown-builders"},icon:(0,o.createElement)("svg",{"aria-hidden":"true",viewBox:"0 0 24 24",fillRule:"evenodd",fill:"currentColor"},(0,o.createElement)("path",{d:"M18 4H6c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zM6 5.5h12c.3 0 .5.2.5.5v3h-13V6c0-.3.2-.5.5-.5zM5.5 18v-7.5h3v8H6c-.3 0-.5-.2-.5-.5zm12.5.5h-8v-8h8.5V18c0 .3-.2.5-.5.5z"})),label:"all"===n.builder?(0,i.__)("All Builders","blocksy-companion"):{gutenberg:"Gutenberg","stackable-ultimate-gutenberg-blocks":"Stackable - Gutenberg Blocks","wpforms-lite":"WPForms - Contact Form",woocommerce:"WooCommerce",elementor:"Elementor",brizy:"Brizy",getwid:"Getwid","simply-gallery-block":"SimpLy Gallery Block & Lightbox","recipe-card-blocks-by-wpzoom":"Recipe Card Blocks by WPZOOM","map-block-gutenberg":"Map Block for Google Maps","mb-custom-post-type":"MB Custom Post Types & Custom Taxonomies",leadin:"HubSpot","block-slider":"Block Slider","ht-slider-for-elementor":"HT Slider For Elementor","modula-best-grid-gallery":"Modula - Image Gallery","advanced-custom-fields":"Advanced Custom Fields (ACF)","greenshift-animation-and-page-builder-blocks":"Greenshift – Gutenberg Blocks",fluentform:"Fluent Forms","translatepress-multilingual":"TranslatePress","fluent-booking":"Fluent Booking"}[n.builder],text:"all"===n.builder?(0,i.__)("All Builders","blocksy-companion"):{gutenberg:"Gutenberg","stackable-ultimate-gutenberg-blocks":"Stackable - Gutenberg Blocks","wpforms-lite":"WPForms - Contact Form",woocommerce:"WooCommerce",elementor:"Elementor",brizy:"Brizy",getwid:"Getwid","simply-gallery-block":"SimpLy Gallery Block & Lightbox","recipe-card-blocks-by-wpzoom":"Recipe Card Blocks by WPZOOM","map-block-gutenberg":"Map Block for Google Maps","mb-custom-post-type":"MB Custom Post Types & Custom Taxonomies",leadin:"HubSpot","block-slider":"Block Slider","ht-slider-for-elementor":"HT Slider For Elementor","modula-best-grid-gallery":"Modula - Image Gallery","advanced-custom-fields":"Advanced Custom Fields (ACF)","greenshift-animation-and-page-builder-blocks":"Greenshift – Gutenberg Blocks",fluentform:"Fluent Forms","translatepress-multilingual":"TranslatePress","fluent-booking":"Fluent Booking"}[n.builder]},(function(){return["all"].concat(g(l)).map((function(e){var t=n.builder===e;return(0,o.createElement)(Ze.MenuItem,{key:e,icon:t?(0,o.cloneElement)(Ke,{width:24,height:24}):null,isSelected:t,onClick:function(){r(at(at({},n),{},{builder:e}))}},"all"===e?(0,i.__)("All Builders","blocksy-companion"):{gutenberg:"Gutenberg","stackable-ultimate-gutenberg-blocks":"Stackable - Gutenberg Blocks","wpforms-lite":"WPForms - Contact Form",woocommerce:"WooCommerce",elementor:"Elementor",brizy:"Brizy",getwid:"Getwid","simply-gallery-block":"SimpLy Gallery Block & Lightbox","recipe-card-blocks-by-wpzoom":"Recipe Card Blocks by WPZOOM","map-block-gutenberg":"Map Block for Google Maps","mb-custom-post-type":"MB Custom Post Types & Custom Taxonomies",leadin:"HubSpot","block-slider":"Block Slider","ht-slider-for-elementor":"HT Slider For Elementor","modula-best-grid-gallery":"Modula - Image Gallery","advanced-custom-fields":"Advanced Custom Fields (ACF)","greenshift-animation-and-page-builder-blocks":"Greenshift – Gutenberg Blocks",fluentform:"Fluent Forms","translatepress-multilingual":"TranslatePress","fluent-booking":"Fluent Booking"}[e])}))})),ct_localizations.is_dev_mode&&(0,o.createElement)(rt,{allPlans:a,allCategories:c}),(0,o.createElement)("div",{className:"ct-filter-search"},(0,o.createElement)("svg",{"aria-hidden":"true",width:"13",height:"13",viewBox:"0 0 15 15",fill:"currentColor"},(0,o.createElement)("path",{d:"M14.8,13.7L12,11c0.9-1.2,1.5-2.6,1.5-4.2c0-3.7-3-6.8-6.8-6.8S0,3,0,6.8s3,6.8,6.8,6.8c1.6,0,3.1-0.6,4.2-1.5l2.8,2.8c0.1,0.1,0.3,0.2,0.5,0.2s0.4-0.1,0.5-0.2C15.1,14.5,15.1,14,14.8,13.7z M1.5,6.8c0-2.9,2.4-5.2,5.2-5.2S12,3.9,12,6.8S9.6,12,6.8,12S1.5,9.6,1.5,6.8z"})),(0,o.createElement)("input",{type:"text",placeholder:(0,i.__)("Search for a starter site...","blocksy-companion"),value:n.search,onChange:function(e){r(at(at({},n),{},{search:e.target.value}))}})))},lt=[{id:"personal",title:"Personal"},{id:"professional",title:"Professional"},{id:"agency",title:"Agency"},{id:"personal_v2",title:"Personal v2"},{id:"professional_v2",title:"Professional v2"},{id:"agency_v2",title:"Agency v2"}],st=["Blog","Business","Ecommerce","News","Nonprofit","Personal","Portfolio","Travel"];function ut(e,t){var n=t.length,r=e.length;if(r>n)return!1;if(r===n)return e===t;e:for(var o=0,a=0;o<r;o++){for(var c=e.charCodeAt(o);a<n;)if(t.charCodeAt(a++)===c)continue e;return!1}return!0}var mt=function(e,t){var n=e;return"all"!==t.plan&&(n=n.filter((function(e){return!("free"===t.plan&&e.is_pro||"pro"===t.plan&&!e.is_pro)}))),"all"!==t.category&&(n=n.filter((function(e){return!!e.categories&&e.categories.includes(t.category)}))),t.search&&(n=n.filter((function(e){return!(!e.keywords||!e.keywords.split(",").some((function(e){return ut(t.search.toLowerCase(),e.trim().toLowerCase())})))||ut(t.search.toLowerCase(),e.name.toLowerCase())}))),"all"!==t.builder&&(n=n.filter((function(n){return!!e.find((function(e){return e.name===n.name&&(e.builder||"gutenberg")===t.builder}))}))),n},pt=function(e){var t=e.demos_list,n=f((0,o.useState)({search:"",category:"all",builder:"all",plan:"all"}),2),r=n[0];return{filters:r,setFilters:n[1],unfiltered_demos_list:t,demos_list:mt(t.filter((function(e){return!e.dev_v2||ct_localizations.is_dev_mode})),r),allPlans:lt,allCategories:st,display:function(){return(0,o.createElement)(it,null)}}},ft=(0,o.createContext)({demos:[]}),dt=null,ht=function(e){e.children,e.path,e.location;var t=f((0,o.useState)(!dt),2),n=t[0],r=t[1],a=f((0,o.useState)(dt||[]),2),c=a[0],s=a[1],m=f((0,o.useState)(ctDashboardLocalizations.plugin_data.active_plugins),2),p=m[0],d=m[1],b=f((0,o.useState)(null),2),g=b[0],y=b[1],v=f((0,o.useState)(ctDashboardLocalizations.plugin_data.current_installed_demo),2),_=v[0],E=v[1],w=pt({demos_list:c}),k=f((0,o.useState)(!1),2),O=k[0],x=k[1],j=f((0,o.useState)({isError:!1,message:"",reason:"generic"}),2),C=j[0],S=j[1],P=f((0,o.useState)({builder:""}),2),D=(P[0],P[1],f((0,o.useState)(!1),2)),z=D[0],M=D[1],L=function(){var e=u(h().mark((function e(){var t,n,o=arguments;return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return o.length>0&&void 0!==o[0]&&o[0]&&r(!0),e.prev=2,e.next=5,N();case 5:if(!(t=e.sent).status||511!==t.status){e.next=9;break}return x(!0),e.abrupt("return");case 9:return e.next=11,t.json();case 11:n=e.sent,s(n),dt=n,e.next=19;break;case 16:e.prev=16,e.t0=e.catch(2),console.error("Blocksy:Dashboard:DemoInstall:demos_list",{response:e.t0,reason:"frontend_starter_sites_fetch_failed"});case 19:r(!1);case 20:case"end":return e.stop()}}),e,null,[[2,16]])})));return function(){return e.apply(this,arguments)}}();return(0,o.useEffect)((function(){ctDashboardLocalizations.plugin_data.demo_install_error?(r(!1),S({isError:!0,message:ctDashboardLocalizations.plugin_data.demo_install_error,reason:"generic"})):L(!dt)}),[]),O?(0,o.createElement)(H,null):(0,o.createElement)("div",{className:"ct-demos-list-container"},(0,o.createElement)(l.Transition,{items:n,from:{opacity:0},enter:[{opacity:1}],leave:[{opacity:0}],config:function(e,t){return"leave"===t?{duration:300}:{delay:300,duration:300}}},(function(e,t){return t?(0,o.createElement)(l.animated.p,{style:e,className:"ct-loading-text"},(0,o.createElement)("svg",{width:"16",height:"16",viewBox:"0 0 100 100"},(0,o.createElement)("g",{transform:"translate(50,50)"},(0,o.createElement)("g",{transform:"scale(1)"},(0,o.createElement)("circle",{cx:"0",cy:"0",r:"50",fill:"currentColor"}),(0,o.createElement)("circle",{cx:"0",cy:"-26",r:"12",fill:"#ffffff",transform:"rotate(161.634)"},(0,o.createElement)("animateTransform",{attributeName:"transform",type:"rotate",calcMode:"linear",values:"0 0 0;360 0 0",keyTimes:"0;1",dur:"1s",begin:"0s",repeatCount:"indefinite"}))))),(0,i.__)("Loading Starter Sites...","blocksy-companion")):C.isError?(0,o.createElement)(l.animated.div,{style:e},"ajax_request_failed"===C.reason&&(0,o.createElement)("div",{className:"ct-demo-notification",dangerouslySetInnerHTML:{__html:sprintf((0,i.__)("Your site is misconfigured and AJAX requests are not reaching your backend. Please click %shere%s to find the common causes and possible solutions to this.<br> Error code - %s","blocksy-companion"),'<a href="https://creativethemes.com/blocksy/docs/troubleshooting/starter-site-common-issues-possible-solutions/" target="_blank">',"</a>",C.message)}}),"remote_fetch_failed"===C.reason&&(0,o.createElement)("div",{className:"ct-demo-notification",dangerouslySetInnerHTML:{__html:sprintf((0,i.__)("Failed to retrieve starter sites list.<br> Error code - %s","blocksy-companion"),C.message)}}),"generic"===C.reason&&(0,o.createElement)("div",{className:"ct-demo-notification",dangerouslySetInnerHTML:{__html:C.message}}),(0,o.createElement)(oe,null)):(0,o.createElement)(l.animated.div,{style:e},(0,o.createElement)(o.Fragment,null,(0,o.createElement)(ft.Provider,{value:{currentDemo:g,pluginsStatus:p,setPluginsStatus:d,installerBlockingReleased:z,setInstallerBlockingReleased:M,setCurrentDemo:y,currentlyInstalledDemo:_,setCurrentlyInstalledDemo:E,unfiltered_demos_list:w.unfiltered_demos_list,demos_list:w.demos_list,filters:w.filters,setFilters:w.setFilters,allPlans:w.allPlans,allCategories:w.allCategories}},w.display(),(0,o.createElement)(ge,null),(0,o.createElement)(We,null)),(0,o.createElement)(oe,null)))})))};function bt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}N().then((function(e){511===e.status&&fetch("".concat(ctDashboardLocalizations.ajax_url,"?action=blocksy_dashboard_handle_incorrect_license"),{method:"POST",body:JSON.stringify({}),headers:{Accept:"application/json","Content-Type":"application/json"}}).then((function(e){e.success,e.data}))})).catch((function(e){console.error("Error:",e)})),c().on("ct:dashboard:routes",(function(e){e.push({Component:function(e){return(0,o.createElement)(I,e)},path:"/extensions"}),e.push({Component:function(e){return(0,o.createElement)(ie,e)},path:"/extensions/:extension"}),"yes"===ctDashboardLocalizations.plugin_data.has_demo_install&&e.push({Component:function(e){return(0,o.createElement)(ht,e)},path:"/demos"})})),c().on("ct:dashboard:navigation-links",(function(e){"yes"===ctDashboardLocalizations.plugin_data.has_demo_install&&e.push({text:(0,i.__)("Starter Sites","blocksy-companion"),path:"demos",getProps:function(e){var t=e.isPartiallyCurrent;e.isCurrent;return t?{"aria-current":"page"}:{}}}),e.push({text:(0,i.__)("Extensions","blocksy-companion"),path:"/extensions",onClick:function(e){location.hash.indexOf("extensions")>-1&&e.preventDefault()},getProps:function(e){var t=e.isPartiallyCurrent,n=e.isCurrent;return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?bt(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):bt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},t||n?{"aria-current":"page"}:{})}})})),c().on("ct:dashboard:heading:after",(function(e){ctDashboardLocalizations.plugin_data.is_pro&&(e.content=(0,o.createElement)("span",null,"PRO"))}))}()}();