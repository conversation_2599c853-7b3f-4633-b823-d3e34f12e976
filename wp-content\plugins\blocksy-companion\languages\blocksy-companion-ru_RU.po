# Translation of Blocksy Companion Pro in Russian
# This file is distributed under the same license as the Blocksy Companion Pro package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-05-15 19:06:22+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n % 10 == 1 && n % 100 != 11) ? 0 : ((n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14)) ? 1 : 2);\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: ru\n"
"Project-Id-Version: Blocksy Companion Pro\n"

#: static/js/dashboard/helpers/useUpsellModal.js:88
msgid "Business or Agency"
msgstr "Про или агентство"

#: static/js/options/ConditionsManager/SingleCondition.js:482
msgid "Select value"
msgstr "Выбрать значение"

#: framework/helpers/exts-configs.php:385
msgid "Product Waitlist"
msgstr "Список ожидания товаров"

#: framework/helpers/exts-configs.php:386
msgid "Allow your customers to sign up for a waitlist for products that are out of stock and get notified when they are back in stock."
msgstr "Позволяет клиентам магазина записываться в список ожидания товаров, которых нет на складе, и получать уведомления, когда они снова появятся в продаже."

#: framework/features/blocks/share-box/options.php:103
msgid "Clipboard"
msgstr "Буфер"

#: framework/premium/extensions/shortcuts/customizer.php:609,
#: framework/premium/extensions/shortcuts/customizer.php:635,
#: framework/premium/extensions/shortcuts/views/bar.php:53,
#: framework/features/header/items/account/views/login.php:554,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:4,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/feature.php:189,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/feature.php:193,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-account.php:67
msgid "Waitlist"
msgstr "Список ожидания"

#: framework/premium/extensions/shortcuts/customizer.php:895
msgid "Tooltip Visibility"
msgstr "Видимость подсказки"

#: framework/premium/extensions/shortcuts/customizer.php:1351
msgid "Container Backdrop Blur"
msgstr "Размытие фона контейнера"

#: framework/premium/features/content-blocks/hooks-manager.php:957
msgid "Added to Cart: Before product"
msgstr "Добавлено в корзину: перед товаром"

#: framework/premium/features/content-blocks/hooks-manager.php:961
msgid "Added to Cart: Before actions"
msgstr "Добавлено в корзину: перед действиями"

#: framework/premium/features/content-blocks/hooks-manager.php:965
msgid "Added to Cart: Before suggested products"
msgstr "Добавлено в корзину: перед предложенными товарами"

#: framework/premium/features/content-blocks/hooks-manager.php:969
msgid "Added to Cart: After suggested products"
msgstr "Добавлено в корзину: после предложенными товарами"

#: framework/premium/features/content-blocks/hooks-manager.php:971
msgid "WooCommerce: Added to Cart"
msgstr "WooCommerce: добавлено в корзину"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:294
msgid "Upsell Products"
msgstr "Апсэл-товары"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:295
msgid "Cross-sell Products"
msgstr "Кроссел-товары"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:506
msgid "Auto Close Panel"
msgstr "Автозакрытие панели"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:507
msgid "Automatically close the panel when a filter option is selected."
msgstr "Автоматическое закрытие панели после выбора параметра фильтра."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:15
msgid "Form Type"
msgstr "Тип формы"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:29
msgid "Form Max Width"
msgstr "Max ширина формы"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:41
msgid "Enable For Backorders"
msgstr "Включить для предзаказов"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:42
msgid "Allow users to join the waitlist even if the product is on backorder."
msgstr "Позволяет пользователям записываться в список ожидания, даже если товар находится в стадии предзаказа."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:55
msgid "Show Users Count"
msgstr "Счётчик ожидающих"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:56
msgid "Display a counter that reflects the current number of users on the waitlist."
msgstr "Отображение счётчика, отражающего текущее количество пользователей в списке ожидания."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:69
msgid "Logged In Users Only"
msgstr "Только авторизованным"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:70
msgid "Display the waitlist feature exclusively to users who are logged in."
msgstr "Отображение опции списка ожидания исключительно для пользователей, которые вошли в систему."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:83
msgid "Subscription Confirmation"
msgstr "Подтверждение подписки"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:84
msgid "Specify which users should verify their waitlist subscription through email confirmation."
msgstr "Какие пользователи должны подтверждать своё добавление в список ожидания по ссылке в письме."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:107
msgid "Waitlist Form Display Conditions"
msgstr "Отображение формы предзаказа"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:108
msgid "Choose where you want this Waitlist Form to be displayed."
msgstr "Где нужно отображать форму включения в список ожидания."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:160
msgid "Message Font"
msgstr "Шрифт сообщения"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:168
msgid "Message Color"
msgstr "Цвет сообщения"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:225
msgid "Container Padding"
msgstr "Внутренний отступ"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/table.php:40,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:46
msgid "Actions"
msgstr "Действия"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:96,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:200,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:221,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:282
msgid "Invalid request"
msgstr "Недопустимый запрос"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:106
msgid "No waitlist found"
msgstr "Список ожидания не найден"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:127,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:128,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:162
msgid "Waitlists"
msgstr "Списки ожидания"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:153
msgid "Number of items per page"
msgstr "Количество элементов на странице"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:171
msgid "Waitlist for %s"
msgstr "Список ожидания %s"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:213
msgid "Invalid email"
msgstr "Неверный Email"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:229
msgid "You are already on the waitlist"
msgstr "Вы уже включены в список ожидания"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:267
msgid "You have been added to the waitlist"
msgstr "Вы добавлены в список ожидания"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:291
msgid "You have been removed from the waitlist"
msgstr "Вы удалены из списка ожидания"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-layer.php:33
msgid "%s %s joined the waitlist for this item."
msgstr "%s %s присоединился к списку ожидания получения этого товара."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-layer.php:243
msgid "Waitlist Form"
msgstr "Форма предзаказа"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-mailer.php:78
msgid "Your waitlist subscription has been successfully canceled."
msgstr "Ваша предзаказ был успешно отменён."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-mailer.php:117
msgid "Your waitlist subscription has been successfully confirmed."
msgstr "Ваша предзаказ подтверждён."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-table.php:11
msgid "Search Products"
msgstr "Поиск товаров"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:14
msgid "Export Subscribers"
msgstr "Экспорт ожидающих"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:63
msgid "Guest"
msgstr "Гость"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:75
msgid "Edit this customer"
msgstr "Править клиента"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:76
msgid "Edit"
msgstr "Править"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:115,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:182
msgid "Delete"
msgstr "Удалить"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:123
msgid "View"
msgstr "Просмотр"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:153
msgid "%s ago"
msgstr "%s назад"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:169
msgid "Is registered"
msgstr "Зарегистрирован"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:170
msgid "Date created"
msgstr "Дата создания"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:8
msgid "Waitlist - Back in Stock Notification"
msgstr "Предзаказ – уведомление о наличии"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:9
msgid "This email is sent when a product is back in stock"
msgstr "Это письмо отправляется, когда товар снова появляется на складе."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:10
msgid "A product you are waiting for is back in stock"
msgstr "Товар, который вы ждёте, снова в наличии"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:11
msgid "Good news! The product you have been waiting for is now back in stock!"
msgstr "Хорошие новости! Товар, который вы так долго ждали, снова в наличии!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:8
msgid "Waitlist - Confirm Subscription"
msgstr "Предзаказ – подтверждение"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:9
msgid "This email is sent when a user subscribes to a product stock alert and should confirm their subscription"
msgstr "Это письмо отправляется, когда пользователь делает предзаказ и должен подтвердить его."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:10
msgid "Confirm waitlist subscription"
msgstr "Подтверждение предзаказа"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:11
msgid "Get notified when {product_title} is back in stock"
msgstr "Подписка на уведомление, когда {product_title} снова будет в наличии."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:8
msgid "Waitlist - Subscription Confirmed"
msgstr "Предзаказ - подписка подтверждена"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:9
msgid "This email is sent after a user confirmed the subscription to a product stock alert"
msgstr "Это письмо отправляется пользователю после того, как он подтвердил подписку на оповещение о наличии товара на складе."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:10
msgid "Waitlist subscription confirmed"
msgstr "Предзаказ подтверждён"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:11
msgid "You will be notified when {product_title} is back in stock"
msgstr "Вы будете уведомлены, когда {product_title} снова появится на складе."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/waitlist-email.php:35,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/waitlist-email.php:139
msgid "Customer"
msgstr "Покупатель"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:27
msgid "Enter your email"
msgstr "Введите свой Email"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:40
msgid "Join Waitlist"
msgstr "Список ожидания"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:53
msgid "This product is currently sold out!"
msgstr "Товар распродан!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:61
msgid "No worries! Please enter your e-mail address and we will promptly notify you as soon as the item is back in stock."
msgstr "Введите свой Email, чтобы получить уведомление когда товар появится на складе."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:69
msgid "Great! You have been added to the waitlist for this product. Please check your inbox and confirm the subscription to this waitlist."
msgstr "Вы были добавлены в список ожидания этого товара. Проверьте свой почтовый ящик и подтвердите своё включение в список ожидания."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:97
msgid "Great! You have been added to the waitlist for this product. You will receive an email as soon as the item is back in stock."
msgstr "Вы добавлены в список ожидания этого товара. Вам придёт письмо, как только товар появится на складе."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:151,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:86
msgid "Unsubscribe"
msgstr "Отписаться"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:65
msgid "Yes"
msgstr "Да"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:65
msgid "No"
msgstr "Нет"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:21
msgid "You don't have any products in your waitlist yet."
msgstr "В вашем списке ожидания пока нет ни одного товара."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:42
msgid "Confirmed"
msgstr "Подтверждено"

#. translators: %s User name.
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:19,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:17,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:17,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:12,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:10,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-subscription-confirmed.php:10
msgid "Hi, %s!"
msgstr "Привет, %s!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:31,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:19
msgid "Great news! The %s from your waitlist is now back in stock!"
msgstr "Отличные новости! %s из вашего списка ожидания теперь снова в наличии!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:42,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:24
msgid "Click the link below to secure your purchase before it is gone!"
msgstr "Перейдите по ссылке ниже, чтобы оформить покупку, пока товар не раскупили!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:26,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:15
msgid "You have requested to join the waitlist for this item:"
msgstr "Вы подали заявку на включение в список ожидания этого товара:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:62
msgid "Click the button below to confirm your subscription. Once confirmed, we will notify you when the item is back in stock."
msgstr "Нажмите на кнопку ниже, чтобы подтвердить подписку. После подтверждения вы получите уведомление, когда товар снова появится на складе."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:66
msgid "Confirm Subscription"
msgstr "Подтверждение подписки"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:70,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:26
msgid "Please note, the confirmation period is 2 days."
msgstr "Срок подтверждения составляет 2 дня."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:77,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:65
msgid "If you don't want to receive any further notifications, please %s"
msgstr "Если больше не хотите получать уведомления, вы можете %s"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:78,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:66
msgid "unsubscribe"
msgstr "отписаться"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:26,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-subscription-confirmed.php:15
msgid "You have been successfully added to the waitlist for the following item:"
msgstr "Вы добавлены в список ожидания следующего товара:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:28,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:17
msgid "Product:"
msgstr "Товар:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:29,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:18
msgid "Price:"
msgstr "Цена:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:30
msgid "Add to cart:"
msgstr "Добавить в корзину:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:19
msgid "Product link:"
msgstr "Ссылка на товар:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:23
msgid "Click the link below to confirm your subscription. Once confirmed, we will notify you when the item is back in stock."
msgstr "Нажмите на ссылку ниже, чтобы подтвердить подписку. После п��дтверждения вы получите уведомление, когда товар снова появится на складе."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:32,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-subscription-confirmed.php:25
msgid "If you don't want to receive any further notifications, please unsubscribe by clicking on this link - %s"
msgstr "Если вы больше не хотите получать уведомления, откажитесь от подписки, перейдя по этой ссылке: %s"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:127
msgid "API URL"
msgstr "URL API"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:304
msgid "More information on how to generate an API key for ActiveCampaign can be found %shere%s."
msgstr "Более подробную информацию о том, как сгенерировать API-ключ для активной кампании, можно найти %sздесь%s."

#: static/js/dashboard/helpers/useUpsellModal.js:18
#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:111
msgid "Business"
msgstr "Профессиональный"

#: framework/helpers/exts-configs.php:377,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:4
msgid "Added to Cart Popup"
msgstr "Окно корзины"

#: framework/helpers/exts-configs.php:378
msgid "Show a dynamic confirmation popup with product recommendations whenever items are added to the cart."
msgstr "Отображение всплывающего окна динамического подтверждения с рекомендациями по товару при каждом добавлении в корзину."

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:15
msgid "Trigger Popup On"
msgstr "Вкл. триггер окна"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:26
msgid "Product Page"
msgstr "Страница товара"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:91
msgid "Description Length"
msgstr "Длина описания"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:102
msgid "Cart Button"
msgstr "Кнопка корзины"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:112
msgid "Checkout Button"
msgstr "Кнопка оформления"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:121
msgid "Continue Shopping Button"
msgstr "Кнопка продолжения покупок"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:140
msgid "Shipping Info"
msgstr "Информация о доставке"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:149
msgid "Tax Info"
msgstr "Информация о налогах"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:158
msgid "Total Info"
msgstr "Итоговая инфо"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:264
msgid "Suggested Products"
msgstr "Предлагаемые товары"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:292,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/suggested-products.php:51
msgid "Related Products"
msgstr "Сопутствующие товары"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:293
msgid "Recently Viewed Products"
msgstr "Недавно просмотренные"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:368
msgid "Products Card Type"
msgstr "Тип карточки товара"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:382
msgid "Products Visibility"
msgstr "Видимость товаров"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:516
msgid "Popup Options"
msgstr "Настройки окна"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:736,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:752,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:315,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:96
msgid "Popup Backdrop"
msgstr "Фон окна"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:768
msgid "Close Icon Size"
msgstr "Размер значка закрытия"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/view.php:95
msgid "Product succesfully added to your cart!"
msgstr "Товар успешно добавлен в вашу корзину!"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/view.php:102
msgid "Close Modal"
msgstr "Закрытие окна"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:331,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:124
msgid "Popup Shadow"
msgstr "Тень окна"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:350,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:143
msgid "Popup Border Radius"
msgstr "Скругление углов"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/added-product.php:227
msgid "Shipping Cost"
msgstr "Цена доставки"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/added-product.php:236
msgid "Tax Amount"
msgstr "Сумма налога"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/added-product.php:245
msgid "Cart Total"
msgstr "Итог корзины"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/product-actions.php:24
msgid "View Cart"
msgstr "Просмотр корзины"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/product-actions.php:34
msgid "Checkout"
msgstr "Оформление"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/product-actions.php:44
msgid "Continue Shopping"
msgstr "Продолжить покупки"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/suggested-products.php:53
msgid "Recently Viewed"
msgstr "Недавно просмотренные"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:340
msgid "More information on how to create a list in MailPoet can be found %shere%s."
msgstr "Более подробную информацию о том, как создать список в MailPoet, можно найти %sздесь%s."

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:9
msgid "Color Mode"
msgstr "Цветовой режим"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:17
msgid "One Color"
msgstr "Один цвет"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:18
msgid "Dual Color"
msgstr "Два цвета"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:28
msgid "Colors"
msgstr "Цвета"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:50
msgid "Color 1"
msgstr "Цвет 1"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:58
msgid "Color 2"
msgstr "Цвет 2"

#: framework/features/blocks/share-box/options.php:110,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:88
msgid "Tooltip"
msgstr "Подсказка"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:108
msgid "Tooltip Text"
msgstr "Текст подсказки"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:111
msgid "{term_name}"
msgstr "{term_name}"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:122
msgid "Tooltip Image"
msgstr "Изображение подсказки"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:135
msgid "Subtype"
msgstr "Подтип"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:174
msgid "here"
msgstr "здесь"

#: framework/premium/features/premium-header/items/contacts/options.php:21
msgid "Item Visibility"
msgstr "Видимость товара"

#: framework/premium/features/premium-header/items/language-switcher/options.php:36
msgid "Hide Missing Language"
msgstr "Скрыть пропущенный язык"

#: framework/premium/extensions/woocommerce-extra/features/filters/filter-types/taxonomies.php:52
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Preview.js:94
msgid "Please wait until the lookup table is generated."
msgstr "Подождите пока генерируется таблица поиска."

#: framework/premium/extensions/woocommerce-extra/features/filters/filter-types/taxonomies.php:472
msgid "Collapse"
msgstr "Свернуть"

#: framework/premium/extensions/woocommerce-extra/features/filters/filter-types/taxonomies.php:473
msgid "Expand"
msgstr "Развернуть"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/filter-presenter.php:64
msgid "category"
msgstr "категория"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/filter-presenter.php:108
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Preview.js:32
msgid "Find by %s"
msgstr "Найти по %s"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:434
msgid "Regenerate the product taxonomies lookup table"
msgstr "Восстановление таблицы поиска таксономий товаров"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:435
msgid "This tool will regenerate the product taxonomies lookup table data from existing product(s) data. This process may take a while."
msgstr "Этот механизм восстановит данные таблицы поиска таксономий товаров из существующих данных о товарах. Этот процесс может занять некоторое время."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:446
msgid "Product taxonomies lookup table data is regenerating"
msgstr "Обновляются данные таблицы поиска таксономий товаров"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:459
msgid "Regenerate"
msgstr "Восстановить"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:453
msgid "Filling in progress (%d)"
msgstr "Заполнение в процессе (%d)"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:494
msgid "Resume the product taxonomies lookup table regeneration"
msgstr "Продолжить обновление таблицы поиска таксономий товаров"

#. translators: %1$s = count of products already processed.
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:497
msgid "This tool will resume the product taxonomies lookup table regeneration at the point in which it was aborted (%1$s products were already processed)."
msgstr "Этот механизм возобновит обновление таблицы поиска таксономий товаров с того момента, на котором оно было прервано (%1$s уже обработано)."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:516
msgid "Product taxonomies lookup table regeneration process has been resumed."
msgstr "Процесс обновления таблицы поиска таксономий товаров возобновлён."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:518
msgid "Resume"
msgstr "Продолжить"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:466
msgid "Abort the product taxonomies lookup table regeneration"
msgstr "Прервать обновление таблицы поиска таксономий товаров"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:486
msgid "Product taxonomies lookup table regeneration process has been aborted."
msgstr "Процесс обновления таблицы поиска таксономий товаров прерван."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:488
msgid "Abort"
msgstr "Прервать"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:158
msgid "Container Border Color"
msgstr "Цвет границы контейнера"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:189
msgid "Container Background Color"
msgstr "Цвет фона контейнера"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:143
msgid "API Version"
msgstr "Версия API"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:268
msgid "More information on how to generate an API key for Brevo can be found %shere%s."
msgstr "Более подробную информацию о том, как сгенерировать API-ключ для Brevo, можно найти %sздесь%s."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:834
msgid "Hide Documentation Links"
msgstr "Скрыть ссылки на документацию"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:842
msgid "Hide Video Links"
msgstr "Скрыть ссылки на видео"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:104
msgid "Display the currently active filters."
msgstr "Отображение активных фильтров."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:47
msgid "Category 1"
msgstr "Категория 1"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:53
msgid "Category 2"
msgstr "Категория 2"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:66
msgid "Attribute 1"
msgstr "Атрибут 1"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:72
msgid "Attribute 2"
msgstr "Атрибут 2"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:12
msgid "Date"
msgstr "Дата"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:10
msgid "Filter by Price Controls"
msgstr "Настройка фильтра по ценам"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:106
msgid "Filter by Price"
msgstr "Фильтрация по ценам"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:11
msgid "Widget for filtering the WooCommerce products by price."
msgstr "Виджет фильтрации товаров WooCommerce по ценам."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:58
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:473
msgid "Show Tooltip"
msgstr "Отображение подсказки"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:70
msgid "Show Prices"
msgstr "Отображение цен"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Disabled.js:6
msgid "Please select a valid taxonomy."
msgstr "Выберите корректную таксономию."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:174
msgid "Filter Settings"
msgstr "Настройки фильтра"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:253
msgid "Show Search Box"
msgstr "Отображение поля поиска"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:496
msgid "Container Maximum Height"
msgstr "Макс. высота контейнера"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:55
msgid "Exclude Speciffic Items"
msgstr "Исключить некоторые товары"

#: static/js/dashboard/VersionMismatch.js:62
#: static/js/notifications/VersionMismatchNotice.js:74
msgid "Update Blocksy Theme Now"
msgstr "Обновить тему сейчас"

#: static/js/dashboard/screens/DemoInstall.js:183
msgid "Your site is misconfigured and AJAX requests are not reaching your backend. Please click %shere%s to find the common causes and possible solutions to this.<br> Error code - %s"
msgstr "Ваш сайт неправильно настроен, и AJAX-запросы не доходят до вашего сервера. Нажмите %sсюда%s, чтобы узнать о наиболее распространенных причинах и возможных решениях этой проблемы. <br>Код ошибки - %s"

#: static/js/dashboard/screens/DemoInstall.js:201
msgid "Failed to retrieve starter sites list.<br> Error code - %s"
msgstr "Не удалось получить список стартовых сайтов. <br>Код ошибки - %s"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:129
msgid "Installing %s"
msgstr "Установка %s"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:345
msgid "Preparing data..."
msgstr "Подготовка данных..."

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:89
msgid "Required plan"
msgstr "Требуемый тариф"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:13
msgid "All Plans"
msgstr "Все тарифы"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:15
msgid "Pro"
msgstr "Про"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:182
msgid "All Builders"
msgstr "Все конструкторы"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:206
msgid "Search for a starter site..."
msgstr "Поиск стартового сайта..."

#: static/js/dashboard/screens/SiteExport.js:176
msgid "Export Site"
msgstr "Экспорт сайта"

#: static/js/dashboard/screens/SiteExport.js:191
msgid "Starter site"
msgstr "Стартовый сайт"

#: static/js/dashboard/screens/SiteExport.js:203
msgid "Select a starter site"
msgstr "Выберите стартовый сайт"

#: static/js/editor/blocks/about-me/index.js:15
msgid "About Me Controls"
msgstr "Настройки анкеты"

#: static/js/editor/blocks/about-me/index.js:43
msgid "Showcase your personal information across your website."
msgstr "Отображение личной информации на сайте."

#: static/js/editor/blocks/breadcrumbs/Preview.js:63
msgid "Subpage"
msgstr "Подстраница"

#: static/js/editor/blocks/breadcrumbs/index.js:11
msgid "Breadcrumbs"
msgstr "Цепочка навигации"

#: static/js/editor/blocks/breadcrumbs/index.js:12
msgid "Display navigational links, showing users their path within the site."
msgstr "Отображение навигационных ссылок, указывающих место текущей страницы в структуре сайта."

#: static/js/editor/blocks/contact-info/index.js:15
msgid "Contact Info Controls"
msgstr "Настройки контактов"

#: static/js/editor/blocks/contact-info/index.js:52
msgid "Display essential contact details to your visitors."
msgstr "Отображение посетителям контактных данных."

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:102
msgid "9:16"
msgstr "9:16"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:106
msgid "3:4"
msgstr "3:4"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:110
msgid "2:3"
msgstr "2:3"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:133
msgid "Width"
msgstr "Ширина"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:157
msgid "Height"
msgstr "Высота"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:178
msgid "Scale"
msgstr "Масштаб"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:209
msgid "Resolution"
msgstr "Разрешение"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:215
msgid "Select the size of the source image."
msgstr "Выберите размер исходного изображения."

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:57
msgid "Image Settings"
msgstr "Настройки изображения"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:77
msgid "Aspect Ratio"
msgstr "Пропорции"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:82
msgid "Original"
msgstr "Исходные"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:90
msgid "16:9"
msgstr "16:9"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:94
msgid "4:3"
msgstr "4:3"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:98
msgid "3:2"
msgstr "3:2"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:100
msgid "Icon/Logo"
msgstr "Значок/лого"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:138
msgid "Expand on click"
msgstr "Развернуть по нажатию"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:151
msgid "Video thumbnail"
msgstr "Миниатюра видео"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:161
msgid "Image Hover Effect"
msgstr "Эффект при наведении"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:174
msgid "Zoom In"
msgstr "Наезд"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:178
msgid "Zoom Out"
msgstr "Отъезд"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:202
msgid "Alternative Text"
msgstr "Альтернативный текст"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:215
msgid "Describe the purpose of the image."
msgstr "Опишите назначение изображения."

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:221
msgid "Leave empty if decorative."
msgstr "Оставьте пустым, если это декорация."

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:238
msgid "Image size"
msgstr "Размер изображения"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:271
msgid "Logo Gap"
msgstr "Отступ логотипа"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:323
msgid "Custom field fallback"
msgstr "Откат произвольного поля"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:343
msgid "Term additional class"
msgstr "Дополнительный класс"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:350
msgid "Additional class for term items. Useful for styling."
msgstr "Дополнительный класс для элементов объекта."

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:45
msgid "Content Source"
msgstr "Источник содержимого"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:51
msgid "Search for field"
msgstr "Поиск по полю"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:87
msgid "Image Source"
msgstr "Источник изображения"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:14
msgid "Change heading level"
msgstr "Изменить уровень заголовка"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:38
msgid "Heading 1"
msgstr "Заголовок 1"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:39
msgid "Heading 2"
msgstr "Заголовок 2"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:40
msgid "Heading 3"
msgstr "Заголовок 3"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:41
msgid "Heading 4"
msgstr "Заголовок 4"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:42
msgid "Heading 5"
msgstr "Заголовок 5"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:43
msgid "Heading 6"
msgstr "Заголовок 6"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:44
msgid "Paragraph"
msgstr "Параграф"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:45
msgid "Span"
msgstr "Span"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:46
msgid "Div"
msgstr "Div"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:101
msgid "Archive Image"
msgstr "Изображение архива"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:38
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:136
msgid "Stock Status"
msgstr "Статус наличия"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:24
#: static/js/editor/blocks/dynamic-data/preview-parts/wp/TermTextPreview.js:8
msgid "Term Title"
msgstr "Заголовок объекта"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:28
#: static/js/editor/blocks/dynamic-data/preview-parts/wp/TermTextPreview.js:9
msgid "Term Description"
msgstr "Описание объекта"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:32
msgid "Term Image"
msgstr "Изображение объекта"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:36
#: static/js/editor/blocks/dynamic-data/preview-parts/wp/TermTextPreview.js:10
msgid "Term Count"
msgstr "Счё��чик объекта"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:50
msgid "Excerpt"
msgstr "Отрывок"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:55
msgid "Post Date"
msgstr "Дата записи"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:65
msgid "Terms"
msgstr "Объекты"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:91
#: static/js/editor/blocks/dynamic-data/preview-parts/WpFieldPreview.js:46
msgid "Archive Title"
msgstr "Заголовок архива"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:96
#: static/js/editor/blocks/dynamic-data/preview-parts/WpFieldPreview.js:50
msgid "Archive Description"
msgstr "Описание архива"

#: static/js/editor/blocks/dynamic-data/index.js:17
msgid "Fetch and display content from various sources."
msgstr "Извлечение и отображение содержимого из различных источников."

#: static/js/editor/blocks/dynamic-data/index.js:33
msgid "Dynamic Title"
msgstr "Динамический заголовок"

#: static/js/editor/blocks/dynamic-data/index.js:37
msgid "Dynamic Excerpt"
msgstr "Динамичный отрывок"

#: static/js/editor/blocks/dynamic-data/index.js:41
msgid "Dynamic Post Date"
msgstr "Динамическая дата записи"

#: static/js/editor/blocks/dynamic-data/index.js:45
msgid "Dynamic Comments"
msgstr "Динамические комментарии"

#: static/js/editor/blocks/dynamic-data/index.js:49
msgid "Dynamic Terms"
msgstr "Динамические объекты"

#: static/js/editor/blocks/dynamic-data/index.js:53
msgid "Dynamic Author"
msgstr "Динамический автор"

#: static/js/editor/blocks/dynamic-data/index.js:57
msgid "Dynamic Featured Image"
msgstr "Динамическое изображение"

#: static/js/editor/blocks/dynamic-data/index.js:61
msgid "Dynamic Author Avatar"
msgstr "Динамический аватар автора"

#: static/js/editor/blocks/dynamic-data/index.js:65
msgid "Dynamic Price"
msgstr "Динамическая цена"

#: static/js/editor/blocks/dynamic-data/index.js:69
msgid "Dynamic Stock Status"
msgstr "Динамический статус наличия"

#: static/js/editor/blocks/dynamic-data/index.js:73
msgid "Dynamic Brands"
msgstr "Динамичные бренды"

#: static/js/editor/blocks/dynamic-data/index.js:77
msgid "Dynamic SKU"
msgstr "Динамический артикул"

#: static/js/editor/blocks/dynamic-data/index.js:81
msgid "Dynamic Rating"
msgstr "Динамический рейтинг"

#: static/js/editor/blocks/dynamic-data/index.js:85
msgid "Dynamic Term Title"
msgstr "Динамическое название объекта"

#: static/js/editor/blocks/dynamic-data/index.js:89
msgid "Dynamic Term Description"
msgstr "Динамическое описание объекта"

#: static/js/editor/blocks/dynamic-data/index.js:93
msgid "Dynamic Term Count"
msgstr "Динамический счётчик объекта"

#: static/js/editor/blocks/dynamic-data/index.js:97
msgid "Dynamic Term Image"
msgstr "Динамическое изображение объекта"

#: static/js/editor/blocks/dynamic-data/preview-parts/woo/RatingPreview.js:13
msgid "Rated %s out of 5"
msgstr "Рейтинг: %s из 5"

#: static/js/editor/blocks/dynamic-data/utils.js:15
msgid "Unknown"
msgstr "Неизвестно"

#: static/js/editor/blocks/post-template/Edit.js:170
#: static/js/editor/blocks/tax-template/Edit.js:152
msgid "List view"
msgstr "Вид списка"

#: static/js/editor/blocks/post-template/Edit.js:176
#: static/js/editor/blocks/tax-template/Edit.js:158
msgid "Grid view"
msgstr "Вид сетки"

#: static/js/editor/blocks/post-template/Edit.js:209
#: static/js/editor/blocks/tax-template/Edit.js:190
msgid "Tablet Columns"
msgstr "Колонки планшета"

#: static/js/editor/blocks/post-template/Edit.js:225
#: static/js/editor/blocks/tax-template/Edit.js:206
msgid "Mobile Columns"
msgstr "Колонки смартфона"

#: static/js/editor/blocks/post-template/index.js:13
msgid "Post Template"
msgstr "Шаблон записи"

#: static/js/editor/blocks/query/Edit.js:120
#: static/js/editor/blocks/tax-query/Edit.js:123
msgid "Reset layout"
msgstr "Сброс макета"

#: static/js/editor/blocks/query/Edit.js:186
msgid "Pagination"
msgstr "Разбивка на страницы"

#: static/js/editor/blocks/query/Edit.js:208
#: static/js/editor/blocks/tax-query/Edit.js:189
msgid "Block ID"
msgstr "ID блока"

#: static/js/editor/blocks/query/Edit.js:214
#: static/js/editor/blocks/tax-query/Edit.js:195
msgid "Please look at the documentation for more information on why this is useful."
msgstr "Ознакомьтесь с документацией для получения дополнительной информации о том, почему это полезно."

#: static/js/editor/blocks/query/edit/PatternSelectionModal.js:82
#: static/js/editor/blocks/tax-query/edit/PatternSelectionModal.js:68
msgid "Choose a pattern"
msgstr "Выберите паттерн"

#: static/js/editor/blocks/query/edit/PatternSelectionModal.js:91
#: static/js/editor/blocks/tax-query/edit/PatternSelectionModal.js:77
msgid "Search for patterns"
msgstr "Поиск паттернов"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:133
msgid "Publish Date"
msgstr "Дата публикации"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:173
msgid "Menu Order"
msgstr "Порядок меню"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:216
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:187
msgid "Order"
msgstr "Порядок"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:224
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:195
msgid "Descending"
msgstr "По убыванию"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:232
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:203
msgid "Ascending"
msgstr "По возрастанию"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:275
msgid "Sticky Posts"
msgstr "Закреплённые"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:299
msgid "Only"
msgstr "Только"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:318
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:222
msgid "Parameters"
msgstr "Параметры"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:105
#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:91
msgid "Create Custom Layout"
msgstr "Создание произвольного макета"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:37
#: static/js/editor/blocks/query/index.js:12
msgid "Advanced Posts"
msgstr "Особые записи"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:41
msgid "Inherit the Customizer layout, start with a pattern or create a custom layout"
msgstr "Используйте макет настройщика, начните с шаблона или создайте собственный макет"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:56
msgid "Inherit From Customizer"
msgstr "Наследовать из настройщика"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:66
#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:57
msgid "Choose Pattern"
msgstr "Выберите паттерн"

#: static/js/editor/blocks/query/edit/TaxonomyControls.js:167
msgid "Search for a term"
msgstr "Поиск объекта"

#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:47
msgid "Include %s"
msgstr "Включая %s"

#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:75
msgid "Related"
msgstr "Связанные"

#: static/js/editor/blocks/query/index.js:13
#: static/js/editor/blocks/tax-query/index.js:13
msgid "Create advanced queries based on your specified criterias."
msgstr "Создание расширенных запросов на основе заданных критериев."

#: static/js/editor/blocks/search/Edit.js:163
msgid "Button Outside"
msgstr "Кнопка снаружи"

#: static/js/editor/blocks/search/Edit.js:180
msgid "Use button with text"
msgstr "Использовать кнопку с текстом"

#: static/js/editor/blocks/search/Edit.js:282
msgid "Button Icon Color"
msgstr "Цвет значка кнопки"

#: static/js/editor/blocks/search/Edit.js:379
msgid "Dropdown Background Color"
msgstr "Цвет фона выпадающего списка"

#: static/js/editor/blocks/search/Edit.js:400
msgid "Dropdown Shadow Color"
msgstr "Цвет тени выпадающего списка"

#: static/js/editor/blocks/search/Preview.js:24
msgid "Select category"
msgstr "Выберите категорию"

#: static/js/editor/blocks/search/index.js:16
msgid "Advanced Search"
msgstr "Расширенный поиск"

#: static/js/editor/blocks/search/index.js:17
msgid "Quickly find specific content on your site."
msgstr "Быстрое нахождение определённого содержимого на сайте."

#: static/js/editor/blocks/share-box/Edit.js:110
#: static/js/editor/blocks/socials/Edit.js:110
msgid "Icons Background Colors"
msgstr "Цвета фона значков"

#: static/js/editor/blocks/share-box/Edit.js:142
#: static/js/editor/blocks/socials/Edit.js:142
msgid "Icons Border Colors"
msgstr "Цвета границ значков"

#: static/js/editor/blocks/share-box/index.js:15
msgid "Share Box Controls"
msgstr "Настройки блока шеринга"

#: static/js/editor/blocks/share-box/index.js:45
msgid "Share content on social media, boosting visibility & engagement."
msgstr "Распространение контента в соцсетях повышает узнаваемость и вовлеченность."

#: static/js/editor/blocks/socials/index.js:15
msgid "Socials Controls"
msgstr "Настройки соцсетей"

#: static/js/editor/blocks/socials/index.js:45
msgid "Display your social media profiles and boost the site engagement."
msgstr "Отображение профилей в соцсетях повышает вовлеченность сайта."

#: static/js/editor/blocks/socials/index.js:47
#: static/js/editor/blocks/widgets-wrapper/index.js:58
msgid "Socials"
msgstr "Соцсети"

#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:120
msgid "ID"
msgstr "ID"

#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:136
msgid "Count"
msgstr "Счётчик"

#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:40
#: static/js/editor/blocks/tax-query/index.js:12
msgid "Advanced Taxonomies"
msgstr "Расширенные таксономии"

#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:44
msgid "Start with a pattern or create a custom layout"
msgstr "Начните с шаблона или создайте собственный макет"

#: static/js/editor/blocks/tax-template/index.js:13
msgid "Taxonomy Template"
msgstr "Шаблон таксономии"

#: static/js/editor/blocks/widgets-wrapper/Edit.js:81
msgid "Expandable Container"
msgstr "Расширяемый контейнер"

#: static/js/editor/blocks/widgets-wrapper/index.js:40
msgid "Widgets Wrapper"
msgstr "Оболочка виджетов"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:41
msgid "Parameters options"
msgstr "Настройки параметров"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:46
msgid "All options are currently hidden"
msgstr "Все настройки сейчас скрыты"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:82
msgid "All options reset"
msgstr "Сброс всех настроек"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:87
msgid "Reset all"
msgstr "Сбросить все"

#: static/js/options/ConditionsManager/ExpireCondition.js:119
msgid "The expiration date cannot be set earlier than the start date."
msgstr "Дата истечения срока не может быть ранее даты начала действия."

#: framework/features/demo-install.php:155,
#: framework/features/demo-install/content-installer.php:164,
#: framework/features/demo-install/content-installer.php:159,
#: framework/features/demo-install/demo-register.php:9
msgid "No demo name provided."
msgstr "Название демо не указано."

#: framework/helpers/exts-configs.php:369,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:248,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:271,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:4
msgid "Stock Scarcity"
msgstr "Нехватка запасов"

#: framework/helpers/exts-configs.php:370
msgid "Show the remaining stock of a product to create a sense of urgency and encourage your visitors to make a purchase."
msgstr "Отображение оставшихся запасов товара, для создания ощущения срочности и побуждения посетителей совершить покупку."

#: framework/views/theme-mismatch.php:36
#: static/js/dashboard/VersionMismatch.js:19
#: static/js/notifications/VersionMismatchNotice.js:27
msgid "Action required - please update Blocksy theme to the latest version!"
msgstr "Необходимо обновить активную тему до последней версии!"

#: framework/views/theme-mismatch.php:41
#: static/js/dashboard/VersionMismatch.js:25
#: static/js/notifications/VersionMismatchNotice.js:35
msgid "We detected that you are using an outdated version of Blocksy theme."
msgstr "Система обнаружила, что вы используете устаревшую версию темы."

#: framework/views/theme-mismatch.php:45
#: static/js/dashboard/VersionMismatch.js:32
#: static/js/notifications/VersionMismatchNotice.js:44
msgid "In order to take full advantage of all features the core has to offer - please install and activate the latest version of Blocksy theme."
msgstr "Чтобы в полной мере воспользоваться всеми возможностями, установите и активируйте также тему Blocksy."

#: framework/extensions/newsletter-subscribe/customizer.php:94,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:127
msgid "Make Name Field Required"
msgstr "Сделать поле \"Имя\" обязательным для заполнения"

#: framework/extensions/trending/customizer.php:121
msgid "Tag"
msgstr "Метка"

#: framework/extensions/trending/customizer.php:142
msgid "Taxonomy Source"
msgstr "Источник таксономии"

#: framework/extensions/trending/customizer.php:216
msgid "Module Title Icon Source"
msgstr "Источник значка заголовка модуля"

#: framework/extensions/trending/customizer.php:303
msgid "Products Status"
msgstr "Статусы товаров"

#: framework/extensions/trending/customizer.php:307
msgid "On Sale"
msgstr "Со скидкой"

#: framework/extensions/trending/customizer.php:308
msgid "Top Rated"
msgstr "Рейтинговое"

#: framework/extensions/trending/customizer.php:309
msgid "Best Sellers"
msgstr "Лидеры продаж"

#: framework/extensions/trending/customizer.php:409
msgid "Show Product Price"
msgstr "Показать цену"

#: framework/extensions/trending/customizer.php:423
msgid "Show Taxonomy"
msgstr "Показать таксономию"

#: framework/extensions/trending/customizer.php:441
msgid "Taxonomy Style"
msgstr "Стиль таксономии"

#: framework/extensions/trending/customizer.php:450
msgid "Underline"
msgstr "Подчеркивание"

#: framework/extensions/trending/customizer.php:465,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:55
msgid "Image Width"
msgstr "Ширина изображения"

#: framework/extensions/trending/customizer.php:693,
#: framework/extensions/trending/customizer.php:825
msgid "Taxonomy Font"
msgstr "Шрифт таксономии"

#: framework/extensions/trending/customizer.php:708,
#: framework/extensions/trending/customizer.php:748
msgid "Taxonomies Font Color"
msgstr "Цвет шриф��а таксономий"

#: framework/extensions/trending/customizer.php:780
msgid "Taxonomies Button Color"
msgstr "Цвет кнопки таксономии"

#: framework/extensions/trending/customizer.php:835
msgid "Taxonomy Font Color"
msgstr "Цвет шрифта таксономии"

#: framework/extensions/trending/customizer.php:860,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:248,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:476,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:494
msgid "Image Border Radius"
msgstr "Скругление углов"

#: framework/features/blocks/blocks.php:19
msgctxt "Block pattern category"
msgid "Blocksy"
msgstr "Blocksy"

#: framework/features/blocks/blocks.php:24
msgid "Patterns that contain buttons and call to actions."
msgstr "Паттерны, содержащие кнопки и призывы к действию."

#: framework/features/blocks/blocks.php:71
msgid "Home Page Text"
msgstr "Текст главной страницы"

#: framework/features/demo-install/child-theme.php:9
msgid "Sorry, you don't have permission to install child themes."
msgstr "У вас нет разрешения на установку дочерних тем."

#: framework/features/demo-install/content-eraser.php:23
msgid "Sorry, you don't have permission to erase content."
msgstr "У вас нет разрешения на удаление содержимого."

#: framework/features/demo-install/content-installer.php:69
msgid "Sorry, you don't have permission to install content."
msgstr "У вас нет разрешения на установку."

#: framework/features/demo-install/content-installer.php:195,
#: framework/features/demo-install/content-installer.php:189
msgid "No demo data found."
msgstr "Демо-данные не найдены."

#: framework/features/demo-install/content-installer.php:351,
#: framework/features/demo-install/content-installer.php:346
msgid "No pages to assign."
msgstr "Нет страниц для применения."

#: framework/features/demo-install/install-finish.php:23
msgid "Sorry, you don't have permission to finish the installation."
msgstr "У вас нет разрешения на завершение установки."

#: framework/features/demo-install/options-import.php:38
msgid "Sorry, you don't have permission to install options."
msgstr "У вас нет прав на установку параметров."

#: framework/features/demo-install/options-import.php:50,
#: framework/features/demo-install/options-import.php:45,
#: framework/features/demo-install/options-import.php:80,
#: framework/features/demo-install/options-import.php:75,
#: framework/features/demo-install/widgets-import.php:48,
#: framework/features/demo-install/widgets-import.php:43
msgid "No demo to install"
msgstr "Нет демо-версии для установки"

#: framework/features/demo-install/plugins-uninstaller.php:9
msgid "Sorry, you don't have permission to uninstall plugins."
msgstr "У вас нет разрешения на удаление плагинов."

#: framework/features/demo-install/required-plugins.php:37
msgid "Sorry, you don't have permission to install plugins."
msgstr "У вас нет разрешения на установку плагинов."

#: framework/features/demo-install/required-plugins.php:49,
#: framework/features/demo-install/required-plugins.php:44
msgid "No plugins to install."
msgstr "Нет плагинов для установки."

#: framework/features/demo-install/widgets-import.php:36
msgid "Sorry, you don't have permission to install widgets."
msgstr "У вас нет разрешения на установку виджетов."

#: framework/features/demo-install/widgets-import.php:79,
#: framework/features/demo-install/widgets-import.php:73
msgid "No widgets to install."
msgstr "Нет виджетов для установки."

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:98
msgid "Fields Gap"
msgstr "О��ступ полей"

#: framework/features/blocks/about-me/options.php:21
msgid "User Source"
msgstr "Источник пользователя"

#: framework/features/blocks/about-me/options.php:26
msgid "Dynamic"
msgstr "Динамический"

#: framework/features/blocks/about-me/options.php:37
msgid "User"
msgstr "Пользователь"

#: framework/features/blocks/about-me/options.php:92
msgid "Image Shape"
msgstr "Форма изображения"

#: framework/features/blocks/about-me/options.php:104
msgid "Alignment"
msgstr "Выравнивание"

#: framework/features/blocks/about-me/options.php:119,
#: framework/features/blocks/socials/options.php:19
msgid "Social Channels"
msgstr "Социальные каналы"

#: framework/features/blocks/about-me/options.php:195,
#: framework/features/blocks/share-box/options.php:149,
#: framework/features/blocks/socials/options.php:101
msgid "Official"
msgstr "Официальное"

#: framework/features/blocks/about-me/view.php:197
msgid "View Profile"
msgstr "Смотреть профиль"

#: framework/features/blocks/contact-info/options.php:44
msgid "Contact Information"
msgstr "Контактная информация"

#: framework/features/blocks/dynamic-data/options.php:24,
#: framework/features/blocks/dynamic-data/options.php:49
msgid "Date type"
msgstr "Тип даты"

#: framework/features/blocks/dynamic-data/options.php:31
msgid "Published Date"
msgstr "Дата публикации"

#: framework/features/blocks/dynamic-data/options.php:32
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:141
msgid "Modified Date"
msgstr "Дата изменения"

#: framework/features/blocks/dynamic-data/options.php:39
msgid "Default format"
msgstr "Формат по умолчанию"

#: framework/features/blocks/dynamic-data/options.php:41
msgid "Example: January 24, 2022"
msgstr "Пример: 21 января 2022"

#: framework/features/blocks/dynamic-data/options.php:75
msgid "Custom date format"
msgstr "Произвольный формат"

#: framework/features/blocks/dynamic-data/options.php:79
msgid "Enter a date or time"
msgstr "Введите дату или время"

#: framework/features/blocks/dynamic-data/options.php:96,
#: framework/features/blocks/dynamic-data/options.php:97,
#: framework/features/blocks/dynamic-data/views/wp-field.php:194
msgid "No comments"
msgstr "Комментариев нет"

#: framework/features/blocks/dynamic-data/options.php:102,
#: framework/features/blocks/dynamic-data/options.php:103,
#: framework/features/blocks/dynamic-data/views/wp-field.php:195
msgid "One comment"
msgstr "Один комментарий"

#: framework/features/blocks/dynamic-data/options.php:108
msgid "Multiple comments"
msgstr "Несколько комментариев"

#: framework/features/blocks/dynamic-data/options.php:120
msgid "Separator"
msgstr "Разделитель"

#: framework/features/blocks/dynamic-data/options.php:132
msgid "Author Field"
msgstr "Поле автора"

#: framework/features/blocks/dynamic-data/options.php:139
msgid "Nickname"
msgstr "Ник"

#: framework/features/blocks/dynamic-data/options.php:138
msgid "Display Name"
msgstr "Отображаемое имя"

#: framework/features/blocks/dynamic-data/options.php:140
msgid "First Name"
msgstr "Имя"

#: framework/features/blocks/dynamic-data/options.php:141
msgid "Last Name"
msgstr "Фамилия"

#: framework/features/blocks/dynamic-data/options.php:172
msgid "Link to post"
msgstr "Ссылка на запись"

#: framework/features/blocks/dynamic-data/options.php:176
msgid "Link to author page"
msgstr "Ссылка на страницу автора"

#: framework/features/blocks/dynamic-data/options.php:180
msgid "Link to term page"
msgstr "Ссылка на страницу объекта"

#: framework/features/blocks/dynamic-data/options.php:184
msgid "Link to archive page"
msgstr "Ссылка на страницу архива"

#: framework/features/blocks/dynamic-data/options.php:197
msgid "Open in new tab"
msgstr "Открывать в новой вкладке"

#: framework/features/blocks/dynamic-data/options.php:203
msgid "Link Rel"
msgstr "Rel ссылки"

#: framework/features/blocks/dynamic-data/options.php:220
msgid "Terms accent color"
msgstr "Акцентный цвет объектов"

#: framework/features/blocks/search/options.php:147,
#: framework/features/blocks/search/view.php:265
msgid "Select Category"
msgstr "Выбрать категорию"

#: framework/features/blocks/search/options.php:175,
#: framework/premium/features/premium-header/items/search-input/options.php:164
msgid "Taxonomy Children"
msgstr "Дочерние таксономии"

#: framework/features/blocks/search/options.php:205,
#: framework/premium/features/premium-header/items/search-input/options.php:192
msgid "Search Through Taxonomies"
msgstr "Поиск по таксономиям"

#: framework/features/blocks/search/options.php:209,
#: framework/premium/features/premium-header/items/search-input/options.php:196
msgid "Search through taxonomies from selected custom post types."
msgstr "Выполнение поиска по таксономиям выбранных типов записей."

#: framework/features/blocks/share-box/options.php:15
msgid "Share Icons"
msgstr "Значки соцсетей"

#: framework/features/blocks/share-box/options.php:43
msgid "Reddit"
msgstr "Reddit"

#: framework/features/blocks/share-box/options.php:49
msgid "Hacker News"
msgstr "Hacker News"

#: framework/features/blocks/share-box/options.php:67
msgid "Telegram"
msgstr "Telegram"

#: framework/features/blocks/share-box/options.php:73
msgid "Viber"
msgstr "Viber"

#: framework/features/blocks/share-box/options.php:79
msgid "WhatsApp"
msgstr "WhatsApp"

#: framework/features/blocks/share-box/options.php:85
msgid "Flipboard"
msgstr "Flipboard"

#: framework/features/blocks/share-box/options.php:91
msgid "Line"
msgstr "Line"

#: framework/features/conditions/rules/archive-loop.php:6
msgid "Archive Item with Taxonomy ID"
msgstr "ID Архивного элемента с таксономией"

#: framework/features/conditions/rules/archive-loop.php:13
msgid "WooCommerce Archive Item with Taxonomy ID"
msgstr "ID элемента архива WooCommerce с таксономией"

#: framework/features/conditions/rules/archive-loop.php:19
msgid "Archive Loop Speciffic"
msgstr "Специфичный для архивного цикла"

#: framework/features/conditions/rules/posts.php:10
msgid "Post Archives"
msgstr "Архивы записей"

#: framework/features/conditions/rules/woo.php:54
msgid "Single Product ID"
msgstr "ID отдельного товара"

#: framework/features/conditions/rules/woo.php:59
msgid "Single Product with Taxonomy ID"
msgstr "ID отдельного товара с таксономией"

#: framework/premium/extensions/woocommerce-extra/utils.php:141
msgid "Private: %s"
msgstr "Приватное: %s"

#: framework/premium/extensions/woocommerce-extra/utils.php:131
msgid "Protected: %s"
msgstr "Защищённое: %s"

#: framework/premium/features/content-blocks/hooks-manager.php:733
msgid "WooCommerce Single Product"
msgstr "Отдельный товар WooCommerce"

#: framework/premium/features/media-video/options.php:119
msgid "Video Size"
msgstr "Размер видео"

#: framework/premium/features/media-video/options.php:127
#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:192
msgid "Contain"
msgstr "Вписывание"

#: framework/premium/features/media-video/options.php:128
#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:187
msgid "Cover"
msgstr "Перекрытие"

#. translators: placeholder here means the actual URL.
#: framework/premium/features/media-video/options.php:132
msgid "Choose how the video will fill its container. More info about this can be found %shere%s."
msgstr "Как видео будет размещено в контейнере. Более подробную информацию об этом можно найти %sздесь%s."

#: framework/features/blocks/dynamic-data/views/avatar-field.php:30
msgid "%s Avatar"
msgstr "Аватар %s"

#: framework/features/blocks/query/block-patterns/posts-layout-1.php:4
msgid "Posts - Layout 1"
msgstr "Записи - макет 1"

#: framework/features/blocks/query/block-patterns/posts-layout-2.php:4
msgid "Posts - Layout 2"
msgstr "Записи - макет 2"

#: framework/features/blocks/query/block-patterns/posts-layout-3.php:4
msgid "Posts - Layout 3"
msgstr "Записи - макет 3"

#: framework/features/blocks/query/block-patterns/posts-layout-4.php:4
msgid "Posts - Layout 4"
msgstr "Записи - макет 4"

#: framework/features/blocks/query/block-patterns/posts-layout-5.php:4
msgid "Posts - Layout 5"
msgstr "Записи - макет 5"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-1.php:4
msgid "Taxonomies - Layout 1"
msgstr "Таксономии - макет 1"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-2.php:4
msgid "Taxonomies - Layout 2"
msgstr "Таксономии - макет 2"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-3.php:4
msgid "Taxonomies - Layout 3"
msgstr "Таксономии - макет 3"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-4.php:4
msgid "Taxonomies - Layout 4"
msgstr "Таксономии - макет 4"

#: framework/features/header/items/account/options.php:15
msgid "Action Link"
msgstr "Ссылка действия"

#: framework/features/header/items/account/options.php:49
msgid "Additional User Info"
msgstr "Доп. информация"

#: framework/features/header/items/account/options.php:53
msgid "Available fields: {user_email}, {user_name}, {user_role}"
msgstr "Доступные поля: {user_email}, {user_name}, {user_role}"

#: framework/features/header/items/account/options.php:142
msgid "Menu"
msgstr "Меню"

#: framework/features/header/items/account/options.php:717
msgid "Items Hover Effect"
msgstr "Эффект при наведении"

#: framework/features/header/items/account/options.php:727
msgid "Boxed Color"
msgstr "Цве�� блока"

#: framework/features/header/items/account/options.php:1945
msgid "Link Active"
msgstr "Активная ссылка"

#: framework/premium/features/content-blocks/options/popup.php:262
msgid "Close Trigger Delay"
msgstr "Задержка при закрытии"

#: framework/premium/features/content-blocks/options/popup.php:269
msgid "Set the close delay time (in seconds) after the form submit action is detected."
msgstr "Установка времени задержки закрытия (в секундах) после отправки формы."

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:100,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:813
msgid "Featured Icon/Logo"
msgstr "Значок/логотип"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:570
msgid "Upvote"
msgstr "Плюсануть"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:584
msgid "Downvote"
msgstr "Минусануть"

#: framework/premium/extensions/woocommerce-extra/features/brands/brands-import-export.php:17,
#: framework/premium/extensions/woocommerce-extra/features/brands/brands-import-export.php:69
msgid "Blocksy Brands"
msgstr "Бренды Blocksy"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/select.php:18
msgid "Choose page"
msgstr "Выбор страницы"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/select.php:33
msgid "Choose a custom thank you page for this product."
msgstr "Выбор произвольной страницы благодарности за покупку товара."

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:49
msgid "Product Image Visibility"
msgstr "Видимость изобр. товара"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:87
msgid "Product Price & Stock Visibility"
msgstr "Видимость цены и наличия товара"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:42
msgid "Trigger Icon Type"
msgstr "Тип значка триггера"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:77
msgid "Trigger Visibility"
msgstr "Видимость триггера"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:107
msgid "Trigger Label"
msgstr "Ярлык триггера"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:233
msgid "Panel Default State"
msgstr "Исходное состояние панели"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:240
msgid "Closed"
msgstr "Закрыто"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:241
msgid "Opened"
msgstr "Открыто"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:488
msgid "Panel AJAX Reveal"
msgstr "Ajax-раскрытие панели"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:140
msgid "Autoplay Gallery"
msgstr "Автозапуск галереи"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:152
msgid "Delay (in seconds)"
msgstr "Задержка (сек.)"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:219
msgid "Columns Spacing"
msgstr "Интервал колонок"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:247,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:275
msgid "Arrows Visibility"
msgstr "Видимость стрелок"

#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:11
msgid "Prev/Next Arrow"
msgstr "Стрелки вперёд/назад"

#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:41
msgid "Prev/Next Background"
msgstr "Фон вперёд/назад"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:372,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:160
msgid "Add {items} more items to get free shipping!"
msgstr "Добавьте ещё {items} товаров, чтобы получить бесплатную доставку!"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:52
msgid "Count Criteria"
msgstr "Критерий подсчёта"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:69,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:59,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:246,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/price-filter.php:299,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:53,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:34,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:34
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:128
msgid "Price"
msgstr "Цена"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:60
msgid "Items"
msgstr "Товары"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:86
msgid "Goal Items"
msgstr "Целевое количество"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:90
msgid "Amount of items the client has to buy in order to get free shipping."
msgstr "Количество товаров, которые клиент должен приобрести, чтобы получить бесплатную доставку."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:168
msgid "You can use dynamic code tags such as {items} inside this option."
msgstr "Можно использовать теги динамического кода, такие как {items}, внутри этого параметра."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:195,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:62
msgid "Bar Color"
msgstr "Цвет панели"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:187,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:29
msgid "🚨 Hurry up! Only {items} units left in stock!"
msgstr "🚨 Поторопитесь! Осталось только {items}!"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:15
msgid "Stock Threshold"
msgstr "Пороговый уровень запасов"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:23
msgid "Show the stock scarcity module when product stock is below this number."
msgstr "Отображение модуля дефицита товара, когда складской запас меньше указанного значения."

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:27
msgid "Message"
msgstr "Сообщение"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:43
msgid "Bar Height"
msgstr "Высота панели"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:686
msgid "Swatches removed"
msgstr "Образцы удалены"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:693
msgid "Swatches saved"
msgstr "Образцы сохранены"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:755
msgid "Custom Attributes"
msgstr "Произвольные атрибуты"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:761,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:800,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:873
msgid "Terms Limit"
msgstr "Лимит объектов"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:764,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:803
msgid "Set how many terms you want to display in this attribute."
msgstr "Сколько объектов нужно отображать в данном атрибуте."

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:774,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:813,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:887
#: static/js/editor/blocks/query/Edit.js:178
#: static/js/editor/blocks/tax-query/Edit.js:165
msgid "Limit"
msgstr "Лимит"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:876
msgid "Set how many terms you want to display in each attribute."
msgstr "Сколько объектов нужно отображать в каждом атрибуте."

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:179,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:462
msgid "Mixed Swatches"
msgstr "Смешанные образцы"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:233
msgid "Generate Variation URL"
msgstr "Генерировать URL вариации"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:239
msgid "Generate a shareable single product page URL with pre-selected variation attributes."
msgstr "Генерирование URL-адреса страницы отдельного товара, доступного для повторного использования, с заранее установленными атрибутами вариации."

#: static/js/options/ConditionsManager/SingleCondition.js:446
msgid "Display if query string is present in URL"
msgstr "Отображать, если строка запроса присутствует в URL"

#: static/js/options/DisplayCondition.js:62
msgid "Add Conditions"
msgstr "Добавление условий"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:68
msgid "Upgrade to the agency plan and get instant access to this starter site and many other features."
msgstr "Перейдите на тариф агентства и получите мгновенный доступ к этому стартовому сайту и многим другим функциям."

#: static/js/dashboard/screens/Extensions/CurrentExtension.js:316
msgid "Documentation"
msgstr "Документация"

#: static/js/dashboard/screens/Extensions/CurrentExtension.js:346
msgid "Manage"
msgstr "Управление"

#: static/js/dashboard/screens/Extensions/CurrentExtension.js:86
msgid "Video Tutorial"
msgstr "Видеоруководство"

#: static/js/options/ConditionsManager.js:207
msgid "Advanced Mode"
msgstr "Расширенный режим"

#: static/js/options/ConditionsManager/PostIdPicker.js:78
msgid "Select product"
msgstr "Выбрать товар"

#: static/js/options/ConditionsManager/ScheduleDate.js:149
msgid "Monday"
msgstr "Понедельник"

#: static/js/options/ConditionsManager/ScheduleDate.js:154
msgid "Tuesday"
msgstr "Вторник"

#: static/js/options/ConditionsManager/ScheduleDate.js:159
msgid "Wednesday"
msgstr "Среда"

#: static/js/options/ConditionsManager/ScheduleDate.js:164
msgid "Thursday"
msgstr "Четверг"

#: static/js/options/ConditionsManager/ScheduleDate.js:169
msgid "Friday"
msgstr "Пятница"

#: static/js/options/ConditionsManager/ScheduleDate.js:174
msgid "Saturday"
msgstr "Суббота"

#: static/js/options/ConditionsManager/ScheduleDate.js:179
msgid "Sunday"
msgstr "Воскресенье"

#: static/js/options/ConditionsManager/ScheduleDate.js:21
msgid "Mon"
msgstr "Пн"

#: static/js/options/ConditionsManager/ScheduleDate.js:211
msgid "Start Time"
msgstr "Время начала"

#: static/js/options/ConditionsManager/ScheduleDate.js:22
msgid "Tue"
msgstr "Вт"

#: static/js/options/ConditionsManager/ScheduleDate.js:23
msgid "Wed"
msgstr "Ср"

#: static/js/options/ConditionsManager/ScheduleDate.js:234
msgid "Stop Time"
msgstr "Время окончания"

#: static/js/options/ConditionsManager/ScheduleDate.js:24
msgid "Thu"
msgstr "Чт"

#: static/js/options/ConditionsManager/ScheduleDate.js:25
msgid "Fri"
msgstr "Пт"

#: static/js/options/ConditionsManager/ScheduleDate.js:26
msgid "Sat"
msgstr "Сб"

#: static/js/options/ConditionsManager/ScheduleDate.js:27
msgid "Sun"
msgstr "Вс"

#: static/js/options/ConditionsManager/ScheduleDate.js:58
msgid "Every day"
msgstr "Каждый день"

#: static/js/options/ConditionsManager/ScheduleDate.js:66
msgid "Only weekends"
msgstr "Только по выходным"

#: static/js/options/ConditionsManager/ScheduleDate.js:74
msgid "Only weekdays"
msgstr "Только по будням"

#: static/js/options/ConditionsManager/SingleCondition.js:325
msgid "Select sub field"
msgstr "Выберите подполе"

#: static/js/options/ConditionsManager/SingleCondition.js:378
msgid "Display based on referer domain"
msgstr "Отображение на основе домена отправителя"

#: static/js/options/ConditionsManager/SingleCondition.js:412
msgid "Display if cookie is present"
msgstr "Отображать, если присутствуют куки"

#: static/js/dashboard/helpers/useUpsellModal.js:42
msgid "Upgrade to the agency plan and get instant access to this and many other features."
msgstr "Перейдите на тариф агентства и получите мгновенный доступ к этой и многим другим функциям."

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:53
msgid "This is a Pro starter site"
msgstr "Это профессиональный стартовый сайт"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:54
msgid "Upgrade to any pro plan and get instant access to this starter site and many other features."
msgstr "Перейдите на любой профессиональный тариф и получите мгновенный доступ к этому стартовому сайту и многим другим функциям."

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:61
msgid "Upgrade to the business or agency plan and get instant access to this starter site and many other features."
msgstr "Перейдите на профессиональный или агентский тариф и получите мгновенный доступ к этому стартовому сайту и многим другим функциям."

#: static/js/dashboard/helpers/useProExtensionInFree.js:14
msgid "This is a Pro extension"
msgstr "Это профессиональное расширение"

#: static/js/dashboard/helpers/useUpsellModal.js:103
#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:115
msgid "Agency"
msgstr "Агентство"

#: static/js/dashboard/helpers/useUpsellModal.js:11
#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:14
msgid "Free"
msgstr "Бесплатный"

#: static/js/dashboard/helpers/useUpsellModal.js:122
msgid "Compare Plans"
msgstr "Сравнение тарифов"

#: static/js/dashboard/helpers/useUpsellModal.js:17
#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:107
msgid "Personal"
msgstr "Персональный"

#: static/js/dashboard/helpers/useUpsellModal.js:27
msgid "This is a Pro feature"
msgstr "Это профессиональный функционал"

#: static/js/dashboard/helpers/useUpsellModal.js:28
msgid "Upgrade to any pro plan and get instant access to this and many other feature."
msgstr "Перейдите на любой профессиональный тариф и получите мгновенный доступ к этой и многим другим функциям."

#: static/js/dashboard/helpers/useUpsellModal.js:35
msgid "Upgrade to the business or agency plan and get instant access to this and many other features."
msgstr "Перейдите на профессиональный или агентский тариф и получите мгновенный доступ к этой и многим другим функциям."

#: static/js/dashboard/NoTheme.js:31
msgid "In order to take full advantage of all features it has to offer - please install and activate the Blocksy theme also."
msgstr "Чтобы в полной мере воспользоваться всеми возможностями, установите и активируйте также тему Blocksy."

#: static/js/dashboard/NoTheme.js:65
msgid "Install and activate the Blocksy theme"
msgstr "Установите и активируйте тему Blocksy"

#: static/js/dashboard/NoTheme.js:18
msgid "Action Required - Install Blocksy Theme"
msgstr "Требуется действие - установите тему Blocksy"

#: static/js/dashboard/NoTheme.js:24
msgid "Blocksy Companion is the complementary plugin to Blocksy theme. It adds a bunch of great features to the theme and acts as an unlocker for the Blocksy Pro package."
msgstr "Blocksy Companion - это дополнительный плагин темы Blocksy. Он добавляет в тему множество полезных функций и служит для разблокировки пакета Blocksy Pro."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:423
msgid "Show Image Frame"
msgstr "Рамка вокруг изображения"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:444
msgid "Show Label"
msgstr "Отображать ярлык"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:457
msgid "Show Counter"
msgstr "Отображение счётчика"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:164
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:82
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:526
msgid "Show Reset Button"
msgstr "Кнопка сброса"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:168
msgid "Show or hide reset filter button."
msgstr "Кнопка сброса фильтров."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:9
msgid "Shop Filters Controls"
msgstr "Настройки фильтров товаров"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:135
msgid "Shop Filters"
msgstr "Фильтры"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:10
msgid "Widget for filtering the WooCommerce products loop by category, attribute or brand."
msgstr "Виджет фильтрации товаров WooCommerce по категориям, атрибутам или брендам."

#: framework/premium/static/js/blocks/ContentBlock.js:26
msgid "Insert a specific Content Block anywhere on the site."
msgstr "Вставьте определённый блок контента в любое место сайта."

#: framework/premium/static/js/hooks/CreateHook.js:156
msgid "Nothing Found Template"
msgstr "Шаблон о��сутствия результатов"

#: framework/premium/static/js/hooks/CreateHook.js:164
msgid "Maintenance Template"
msgstr "Шаблон техобслуживания"

#: framework/premium/static/js/media-video/components/EditVideoMeta.js:44
msgid "Video Options"
msgstr "Параметры видео"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:75
msgid "Display order overhiew section."
msgstr "Отображение раздела обзора заказа."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:90
msgid "Order Details"
msgstr "Детали заказа"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:94
msgid "Display order details section."
msgstr "Отображение раздела деталей заказа."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:177
msgid "Filter By"
msgstr "Фильтр"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:92
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:221
msgid "Attribute"
msgstr "Артибут"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:11
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:145
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:192
msgid "Display Type"
msgstr "Формат"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:154
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:200
msgid "List"
msgstr "Список"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:387
msgid "Image Aspect Ratio"
msgstr "Пропорции изображения"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:408
msgid "Image Max width"
msgstr "Макс. ширина изображения"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:233
msgid "Multiple Selections"
msgstr "Множественный выбор"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:237
msgid "Allow selecting multiple items in a filter."
msgstr "Позволяет выбрать несколько пунктов в фильтре."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:118
msgid "Select attribute"
msgstr "Выбрать атрибут"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:305
msgid "Show Hierarchy"
msgstr "Показать иерархию"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:321
msgid "Expandable"
msgstr "Разворачиваемый"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:336
#: static/js/editor/blocks/widgets-wrapper/Edit.js:95
msgid "Expanded by Default"
msgstr "Развёрнуто по умолчанию"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:283
msgid "Show Checkboxes"
msgstr "Показывать флажки"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:364
msgid "Show Brands Images"
msgstr "Отображать логотипы брендов"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:368
msgid "Show Swatches"
msgstr "Отобьражать образцы"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/VariableTab.js:30
msgid "Upload only the %s.woff2%s or %s.ttf%s font file formats. Don't convert variable fonts by yourself. Ask the font provider to hand a correct file or the %svariable%s font won't work."
msgstr "Загружайте только файлы шрифтов в форматах %s.woff2%s или %s.ttf%s. Не конвертируйте вариативные шрифты самостоятельно. Попросите поставщика шрифтов предоставить правильный файл, иначе %svariable%s шрифта не будет работать."

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/AdvancedTab.js:54
msgid "Preload Subsets"
msgstr "Предзагрузка наборов"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/GeneralTab.js:10
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:65
msgid "Select Variations"
msgstr "Выбрать вариации"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:255
msgid "Local Google Fonts Settings"
msgstr "Настройки локальных шрифтов Google"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:267
msgid "Select font"
msgstr "Выбрать шрифт"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:335
msgid "Download Font"
msgstr "Скачать шрифт"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:217
msgid "Save Font"
msgstr "Сохранить шрифт"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:608
msgid "Companion Plugin Details"
msgstr "Детали плагина сравнения"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Disabled.js:7
msgid "Please select a valid attribute."
msgstr "Выберите корректный атрибут."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:41
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:130
msgid "Exclude %s"
msgstr "Исключать %s"

#: framework/features/conditions/rules/woo.php:35
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:31
msgid "Product Attributes"
msgstr "Атрибуты товара"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/CustomerDetails.js:10
msgid "Billing address"
msgstr "Платёжный адрес"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/CustomerDetails.js:34
msgid "Shipping address"
msgstr "Адрес доставки"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:64
msgid "Subtotal"
msgstr "Подытог"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:77
msgid "Shipping"
msgstr "Доставка"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:79
msgid "Free shipping"
msgstr "Бесплатная доставка"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:83
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:42
msgid "Payment method"
msgstr "Способ оплаты"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:85
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:43
msgid "Cash on delivery"
msgstr "Оплата при доставке"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:88
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:28
msgid "Total"
msgstr "Итого"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:8
msgid "Order number"
msgstr "Номер заказа"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:108
msgid "Customer Details"
msgstr "Информация о клиенте"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:112
msgid "Display customer details section."
msgstr "Отображение раздела информации о клиенте."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:71
msgid "Order Overview"
msgstr "Обзор заказа"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:89
msgid "Once you insert your %sProject ID%s and click the \"Fetch Fonts\" button, your fonts will become available in all theme’s typography options."
msgstr "Как только вы вставите %sProject ID%s и нажмете кнопку \" Получить шрифты\", ваши шрифты станут доступны во всех опциях типографики темы."

#: framework/premium/extensions/custom-fonts/dashboard-static/js/CustomFonts.js:135
#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:320
msgid "Upload Font"
msgstr "Загрузить шрифт"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/PreloadTab.js:14
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/AdvancedTab.js:19
msgid "Preload Variations"
msgstr "Предзагрузка вариаций"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:356
msgid "Simple Font"
msgstr "Простой шрифт"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:360
msgid "Variable Font"
msgstr "Вариативный шрифт"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:364
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:116
msgid "Preload"
msgstr "Предзагрузка"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:174
#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:20
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:342
msgid "Available Fonts"
msgstr "Доступные шрифты"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:322
msgid "More information on how to generate an API key for Campaign Monitor can be found %shere%s."
msgstr "Подробную информацию о генерации API-ключа для монитора кампаний, можно найти %sздесь%s."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:83
msgid "Connect Newsletter Provider"
msgstr "Подключить сервис рассылки"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:96
msgid "Provider"
msgstr "Сервис"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/ListPicker.js:91
msgid "Fetching..."
msgstr "Получение..."

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:164
msgid "Please enter a valid Project ID to get all fonts."
msgstr "Введите действительный ID проекта, чтобы получить все шрифты."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:254
msgid "This provider is used only for testing purposes. It doesnt register any real subscribers."
msgstr "Этот сервис используется только в целях тестирования. Он не регистрирует реальных подписчиков."

#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:486,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:520,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:555,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:589,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:428,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:463,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:494,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:524
msgid "Badge Color"
msgstr "Цвет отметки"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:221
#: static/js/editor/blocks/search/Edit.js:281
msgid "Button Text Color"
msgstr "Цвет текста кнопки"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/index.js:17
msgid "Newsletter Controls"
msgstr "Настройки рассылки"

#: framework/features/blocks/search/options.php:126,
#: framework/premium/features/premium-header/items/search-input/options.php:119
msgid "Live Results Product Status"
msgstr "Оперативные результаты статуса товара"

#: framework/features/blocks/search/options.php:138,
#: framework/premium/features/premium-header/items/search-input/options.php:132
msgid "Taxonomy Filter"
msgstr "Фильтр таксономий"

#: framework/features/blocks/search/options.php:156,
#: framework/premium/features/premium-header/items/search-input/options.php:144
msgid "Filter Visibility"
msgstr "Фильтр видимости"

#: framework/premium/features/premium-header/items/search-input/options.php:795
msgid "Input Border Radius"
msgstr "Скругление границы поля"

#: framework/premium/features/premium-header/items/search-input/options.php:823
msgid "Dropdown Font"
msgstr "Шрифт выпадающего списка"

#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:604
msgid "You don't have any products in your compare list yet."
msgstr "В ваш список сравнения пуст."

#: framework/features/blocks/dynamic-data/views/woo-field.php:25,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:481
#: static/js/editor/blocks/dynamic-data/preview-parts/woo/StockPreview.js:6
msgid "In Stock"
msgstr "В наличии"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:12,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:37,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/inline-filter.php:78,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/list-filter.php:109
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:198
msgid "Active Filters"
msgstr "Активные фильтры"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:22
msgid "Active Filters Label"
msgstr "Ярлык активных фильтров"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/inline-filter.php:26,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/list-filter.php:31
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:32
msgid "Reset Filters"
msgstr "Сброс фильтров"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/filter-presenter.php:149,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/price-filter.php:188
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/Preview.js:59
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Preview.js:183
msgid "Reset Filter"
msgstr "Сброс фильтра"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:728,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:24,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:46,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:142
msgid "Color"
msgstr "Цвет"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:78
msgid "Short Name"
msgstr "Краткое название"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:245
msgid "Sorry, this product is unavailable. Please choose a different combination."
msgstr "К сожалению, данный товар недоступен. Выберите другую комбинацию."

#: framework/features/blocks/dynamic-data/views/woo-field.php:26,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:246,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:481,
#: framework/premium/extensions/woocommerce-extra/features/swatches/includes/swatch-element-render.php:47,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:57
#: static/js/editor/blocks/dynamic-data/preview-parts/woo/StockPreview.js:7
msgid "Out of Stock"
msgstr "Нет в наличии"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:540
msgid "Display Variations Inline"
msgstr "Отображение в строку"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:636,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:736,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:833
msgid "Swatches"
msgstr "Образцы"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:16,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:250
msgid "Color Swatches"
msgstr "Образцы цветов"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:20,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:74,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:129,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:183
msgid "Swatch Shape"
msgstr "Форма образца"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:26,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:80,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:135,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:189
msgid "Round"
msgstr "Круглая"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:87,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:142,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:196
msgid "Single Page Swatch Size"
msgstr "Размер образца отдельной страницы"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:42,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:96,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:151,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:205
msgid "Widget Swatch Size"
msgstr "Размер образца виджета"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:111,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:166,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:220
msgid "Archive Cards Swatch Size"
msgstr "Размер образца карточки архива"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:70,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:294
msgid "Image Swatches"
msgstr "Образцы-картинки"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:125,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:338
msgid "Button Swatches"
msgstr "Образцы-кнопки"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:395
msgid "Wishlist Button"
msgstr "Кнопка списка желаний"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:58
msgid "Specific Product Variation "
msgstr "Определённая вариация товара"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:61
msgid "This option will allow you to add a speciffic product variation to wishlist."
msgstr "Эта опция позволяет добавлять в список желаний конкретные вариации товаров."

#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:601,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/views/table-no-results.php:3,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:19
msgid "Browse products"
msgstr "Просмотр товаров"

#: framework/features/blocks/contact-info/options.php:533,
#: framework/premium/features/premium-header/items/contacts/options.php:417
msgid "Link Icons"
msgstr "Значки ссылок"

#: framework/premium/features/premium-header/items/contacts/view.php:59
msgid "Download"
msgstr "Скачать"

#: framework/premium/features/premium-header/items/divider/options.php:22,
#: framework/premium/features/premium-header/items/divider/options.php:51,
#: framework/premium/features/premium-header/items/divider/options.php:66,
#: framework/premium/features/premium-header/items/divider/options.php:81
msgid "Style & Color"
msgstr "Стиль и цвет"

#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:87
msgid "Dropdown Arrow"
msgstr "Стрелка выпадающего списка"

#: framework/premium/features/premium-header/items/menu-tertiary/config.php:4
msgid "Menu 3"
msgstr "Меню 3"

#: framework/features/blocks/search/options.php:119,
#: framework/premium/features/premium-header/items/search-input/options.php:111
msgid "Live Results Product Price"
msgstr "Оперативные результаты цены товара"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:274
msgid "Add New Size Guide"
msgstr "Добавить таблицу размеров"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:275
msgid "Edit Size Guide"
msgstr "Править таблицу размеров"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:276
msgid "New Size Guide"
msgstr "Новая таблица размеров"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:278
msgid "View Size Guide"
msgstr "Просмотр таблицы размеров"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:279
msgid "Search Size Guides"
msgstr "Поиск по таблицам размеров"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/modal-view.php:25,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/side-view.php:34
msgid "Close Sizes Modal"
msgstr "Закрыть окно таблицы размеров"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:29
msgid "Size Guide Display Conditions"
msgstr "Условия отображения таблицы размеров"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:30
msgid "Choose where you want this size guide to be displayed."
msgstr "Где нужно разместить данную таблицу размеров."

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:243
msgid "Sorry, no products matched your selection. Please choose a different combination."
msgstr "К сожалению, ни один товар не соответствует вашему запросу. Выберите другую комбинацию."

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:244
msgid "Please select some product options before adding this product to your cart."
msgstr "Выберите вариацию товара, прежде чем добавлять его в корзину."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:76
msgid "Amount the client has to reach in order to get free shipping."
msgstr "Сумма, на которую необходимо совершить заказ, чтобы получить бесплатную доставку."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:104
msgid "The calculation method will be based on WooCommerce zones."
msgstr "Метод расчёта будет основан на зонах доставки WooCommerce."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:119
msgid "Discount Calculation"
msgstr "Расчёт скидки"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:129
msgid "Include or exclude the discount code when calculating the shipping progress."
msgstr "Включение или исключение скидки купона при расчёте стоимости доставки."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:133,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:158
msgid "Default Message"
msgstr "Сообщение по умолчанию"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:143
msgid "You can use dynamic code tags such as {price} inside this option."
msgstr "Внутри этой опции можно использовать теги динамического кода, такие как {price}."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:174
msgid "Success Message"
msgstr "Сообщение об успехе"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:5,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:271,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:277
msgid "Size Guides"
msgstr "Таблицы размеров"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:16
msgid "Size Guide Placement"
msgstr "Расположение таблицы размеров"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:23
msgid "Side Panel"
msgstr "Боковая панель"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:33
msgid "Reveal From"
msgstr "Раскрывать из"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:303,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:30
msgid "Columns & Products"
msgstr "Колонки и товары"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:330,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:57
msgid "Number of products"
msgstr "Количество товаров"

#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:32
msgid "Share Box Icons Color"
msgstr "Цвет социальных значков"

#: framework/helpers/exts-configs.php:330,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:180,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:4
msgid "Free Shipping Bar"
msgstr "Панель бесплатной доставки"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:183
msgid "Show if cart is empty"
msgstr "Показать, если корзина пуста"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:382,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:176
msgid "Congratulations! You got free shipping 🎉"
msgstr "Вам положена бесплатная доставка"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:359,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:135
msgid "Add {price} more to get free shipping!"
msgstr "Добавьте ещё {price}, чтобы получить бесплатную доставку!"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:15
msgid "Show In Cart Page"
msgstr "Отображать на странице корзины"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:21
msgid "Show In Checkout Page"
msgstr "Отображать при оформлении заказа"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:27
msgid "Show In Mini Cart"
msgstr "Отображать в мини-корзине"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:34
msgid "Calculation Method"
msgstr "Метод расчёта"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:69
msgid "Goal Amount"
msgstr "Сумма цели"

#: framework/premium/extensions/woocommerce-extra/features/filters/feature.php:126
msgid "Automatically scroll page to top after user interaction."
msgstr "Автоматическая прокрутка страницы вверх после действия."

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-cart/feature.php:167
msgid "Shopping Cart"
msgstr "Корзина покупок"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-cart/feature.php:169
msgid "Close cart drawer"
msgstr "Закрыть выезжающую корзину"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/feature.php:152,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:525
msgid "WooCommerce Filters Canvas"
msgstr "Холст фильтров WooCommerce"

#: framework/premium/extensions/shortcuts/customizer.php:477,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:4
msgid "Filters Canvas"
msgstr "Холст фильтров"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:177
msgid "Panel Height"
msgstr "Высота панели"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:187
msgid "Auto"
msgstr "Авто"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:198
msgid "Custom Height"
msgstr "Особая высота"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:216
msgid "Panel Columns"
msgstr "Колонки панели"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:279,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:100
msgid "Panel Backdrop"
msgstr "Задник панели"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:516
msgid "Widget Area Source"
msgstr "Источник области виджетов"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:524
msgid "WooCommerce Sidebar"
msgstr "Сайдбар WooCommerce"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:110,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:242
msgid "Days"
msgstr "Дни"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:111,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:256
msgid "Hours"
msgstr "Часы"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:115,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:270
msgid "Min"
msgstr "Мин."

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:116,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:284
msgid "Sec"
msgstr "Сек."

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:298,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:330
msgid "Hurry up! This sale ends in"
msgstr "Торопитесь! Распродажа до"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:324
msgid "Countdown Box"
msgstr "Блок таймера"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/helpers.php:36
msgid "Quick view"
msgstr "Быстрый просмотр"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:19
msgid "Additional Actions"
msgstr "Дополнительные действия"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:25
msgid "Buttons Type"
msgstr "Тип кнопок"

#: framework/extensions/trending/customizer.php:449,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:80,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:727
msgid "Button"
msgstr "Кнопка"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:72
msgid "Modal Trigger"
msgstr "Триггер модального окна"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:82
msgid "Card"
msgstr "Карточка"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:91
msgid "Modal Width"
msgstr "Ширина модального окна"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:102
msgid "Product Navigation"
msgstr "Навигация по товарам"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:108
msgid "Display next/previous buttons that will help to easily navigate through products."
msgstr "Отображение кнопок следующий/предыдущий, которые помогают перемещаться между товарами."

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:175,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:412,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:130,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:138
msgid "Title Font"
msgstr "Шрифт заголовка"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:210,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:442,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:168
msgid "Price Font"
msgstr "Шрифт цены"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:219,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:450,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:176
msgid "Price Color"
msgstr "Цвет цены"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:219
msgid "Add To Cart Button"
msgstr "Кнопка добавления в корзину"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:291
msgid "View Cart Button"
msgstr "Кнопка просмотра корзины"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:361
msgid "Product Page Button"
msgstr "Кнопка страницы товара"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:46
msgid "How many days the products will be marked as \"New\" after creation."
msgstr "Количество дней в течение которых товары помечены как новые с момента создания."

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:136,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:142
msgid "Product Tabs"
msgstr "Вкладки товара"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:137
msgid "Product Tab"
msgstr "Вкладка товара"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:139
msgid "Add New Product Tab"
msgstr "Добавить вкладку товара"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:140
msgid "Edit Product Tab"
msgstr "Править вкладку товара"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:141
msgid "New Product Tab"
msgstr "Новая вкладка товара"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:143
msgid "View Product Tab"
msgstr "Просмотр вкладки товара"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:144
msgid "Search Product Tabs"
msgstr "Поиск по вкладкам товаров"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:10
msgid "Product Tab Display Conditions"
msgstr "Условия отображения вкладки товаров"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:11
msgid "Choose where you want this product tab to be displayed."
msgstr "Где нужно отображать эту вкладку товаров."

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:27
msgid "Tab Order"
msgstr "Порядок вкладок"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:32
msgid "Default tabs order: Description - 10, Additional Information - 20, Reviews - 30."
msgstr "Порядок вкладок по умолчанию: описание - 10, доп. инфо - 20, отзывы - 30."

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:106
msgid "Payment Gateways"
msgstr "Платёжные шлюзы"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:112,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:65
msgid "Shipping Methods"
msgstr "Способы доставки"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:265
msgid "Thank you Page"
msgstr "Страница благодарности"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:480,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:486
msgid "Thank You Pages"
msgstr "Страницы благодарности"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:481
msgid "Thank You Page"
msgstr "Страница благодарности"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:483
msgid "Add New Thank You Page"
msgstr "Добавить страницу благодарности"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:484
msgid "Edit Thank You Page"
msgstr "Править страницу благодарности"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:485
msgid "New Thank You Page"
msgstr "Новая страница благодарности"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:487
msgid "View Thank You Page"
msgstr "Просмотр страницы благодарности"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:488
msgid "Search Thank You Pages"
msgstr "Поиск по страницам благодарности"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:78
msgid "Payment Gateway"
msgstr "Платёжный шлюз"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:106
msgid "Priority"
msgstr "Приоритет"

#: framework/premium/extensions/woocommerce-extra/features/cart-page/options.php:5
msgid "Coupon Form"
msgstr "Форма купона"

#: framework/premium/extensions/woocommerce-extra/features/filters/feature.php:119
msgid "AJAX Filtering"
msgstr "Ajax-фильтрация"

#: framework/premium/extensions/woocommerce-extra/features/filters/feature.php:125
msgid "Scroll to Top"
msgstr "Прокрутка вверх"

#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:276
msgid "Compare Products"
msgstr "Сравнение товаров"

#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:282
msgid "Close Compare Modal"
msgstr "Закрытие окна сравнения"

#: framework/premium/extensions/woocommerce-extra/features/compare/helpers.php:93,
#: framework/premium/extensions/woocommerce-extra/features/compare/helpers.php:152
msgid "Add to compare"
msgstr "Добавить в сравнение"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:46
msgid "Compare Placement"
msgstr "Размещение сравнения"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:53
#: static/js/editor/blocks/breadcrumbs/Preview.js:46
msgid "Page"
msgstr "Страница"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:62
msgid "Select Page"
msgstr "Выбрать страницу"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:69
msgid "Select a page where the compare table will be outputted."
msgstr "Страница, на которой будет выводиться таблица сравнения."

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:75
msgid "Compare Table Fields"
msgstr "Поля таблицы сравнения"

#: framework/features/blocks/dynamic-data/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:162
msgid "Length"
msgstr "Длина"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:130,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:186
msgid "Attributes"
msgstr "Атрибуты"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:266,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:470
msgid "Availability"
msgstr "Наличие"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:469
msgid "Modal Border Radius"
msgstr "Скругление углов окна"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:366
msgid "Compare Bar"
msgstr "Панель сравнения"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:394
msgid "Button Icon"
msgstr "Значок кнопки"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:449
msgid "Compare Bar Display Conditions"
msgstr "Условия отображения панели сравнения"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:450
msgid "Add one or more conditions to display the Compare bar."
msgstr "Добавьте условие (или несколько) для отображения панели сравнения."

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:463,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:223,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:295,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:365
msgid "Button Font Color"
msgstr "Цвет шрифта кнопки"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:494,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:253,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:325,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:393
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:250
#: static/js/editor/blocks/search/Edit.js:311
msgid "Button Background Color"
msgstr "Цвет фона кнопки"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:15,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:5
msgid "New Badge"
msgstr "Новая отметка"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:55,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:53
msgid "Featured Badge"
msgstr "Отметка рекомендации"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/feature.php:168,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:82
msgid "HOT"
msgstr "Горячее"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/feature.php:202,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:35
msgid "NEW"
msgstr "Новинка"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:32,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:79
msgid "Badge Label"
msgstr "Ярлык отметки"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:40
msgid "Label Duration"
msgstr "Срок ярлыка"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:57
msgid "Allow users to upload images when leaving a review."
msgstr "Разрешить пользователям прикреплять изображения к отзывам."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:73
msgid "Image Lightbox"
msgstr "Лайтбокс изображения"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:74
msgid "Allow users to open attached review images in lightbox."
msgstr "Разрешить пользователям открывать прикрепленные к отзывам изображения в лайтбоксе."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:88
msgid "Review Voting"
msgstr "Рейтинги отзывов"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:89
msgid "Allow users to upvote reviews."
msgstr "Разрешить пользователям голосовать за отзывы."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:105
msgid "Allowed Users"
msgstr "Допущенные пользователи"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:106
msgid "Set which users are allowed to vote."
msgstr "Установите, каким пользователям разрешено голосовать."

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:5
msgid "Affiliate Products"
msgstr "Партнёрские продукты"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:12
msgid "Product Archive"
msgstr "Архив товаров"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:16,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:70
msgid "Image Affiliate Link"
msgstr "Партнерская ссылка в изображении"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:27,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:48,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:81
msgid "Open In New Tab"
msgstr "Открывать в новой вкладке"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:36
msgid "Title Affiliate Link"
msgstr "Заголовок партнёрской ссылки"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:90
msgid "Open Button Link In New Tab"
msgstr "Открыть ссылку кнопки в новой вкладке"

#: framework/premium/extensions/woocommerce-extra/features/cart-page/options.php:14
msgid "Quantity Auto Update"
msgstr "Автообновление количества"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:37
msgid "Product brands base"
msgstr "База брендов товаров"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:49
msgid "brand"
msgstr "бренд"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:500,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:532
msgid "About Brands"
msgstr "О брендах"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:501,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:534
msgid "About %s"
msgstr "О бренде %s"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:558,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:578,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:640,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:707,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:718,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:493
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:146
msgid "Brands"
msgstr "Бренды"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:562,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:150,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:177,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:221,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:239,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:257,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:275
msgid "Sticky Row"
msgstr "Плавающий ряд"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:593,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:644
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:257
msgid "Logo Size"
msgstr "Размер логотипа"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:605,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:656
msgid "Logos Gap"
msgstr "Интервал логотипов"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:708
msgid "Brand"
msgstr "Бренд"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:709
msgid "Search Brands"
msgstr "Поиск бренда"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:710
msgid "All Brands"
msgstr "Все бренды"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:711
msgid "Parent Brand"
msgstr "Родительский бренд"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:712
msgid "Parent Brand:"
msgstr "Родительский бренд:"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:713
msgid "View Brand"
msgstr "Просмотр бренда"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:714
msgid "Edit Brand"
msgstr "Править бренд"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:715
msgid "Update Brand"
msgstr "Обновить бренд"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:716
msgid "Add New Brand"
msgstr "Добавить новый"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:717
msgid "New Brand Name"
msgstr "Название бренда"

#: framework/premium/extensions/woocommerce-extra/features/brands/options.php:5
msgid "Product Brand Tab"
msgstr "Вкладка бренда товара"

#: framework/premium/extensions/woocommerce-extra/features/brands/options.php:20
msgid "Brand Name In Tab Title"
msgstr "Название бренда в заголовке вкладки"

#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:5
msgid "Product Image"
msgstr "Изображение товара"

#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:54
msgid "Quantity Input"
msgstr "Ввод количества"

#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:228
msgid "Compare Button"
msgstr "Кнопка сравнения"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:139
msgid "Text Hover"
msgstr "Текст при наведении"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:144
msgid "Background Initial"
msgstr "Исходный фон"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:376
msgid "Star"
msgstr "Звезда"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:376
msgid "Stars"
msgstr "Звёзды"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:442
msgid "%s%% of customers recommend this product."
msgstr "%s %% пользователей рекомендуют этот товар."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:604
msgid "%s of %s found this review helpful"
msgstr "%s из %s сочли этот отзыв полезным"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:666,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:44
msgid "Review Title"
msgstr "Заголовок отзыва"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:694
msgid "Upload Image (Optional)"
msgstr "Загрузка изображения (не обязательно)"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:11
msgid "Reviews Order"
msgstr "Порядок отзывов"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:18
msgid "Oldest First"
msgstr "Сначала старые"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:19
msgid "Newest First"
msgstr "Сначала новые"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:20
msgid "Low Rating First"
msgstr "Сначала низкие оценки"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:21
msgid "High Rating First"
msgstr "Сначала высокие оценки"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:22
msgid "Most Relevant"
msgstr "Наиболее уместный"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:32
msgid "Average Score"
msgstr "Средняя балл"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:33
msgid "Display an average score for all reviews."
msgstr "Отображение среднего балла всех отзывов."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:45
msgid "Allow users to add a title when leaving a review."
msgstr "Разрешить пользователям добавлять заголовок при оставлении отзыва."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:56
msgid "Image Upload"
msgstr "Загрузка изображения"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/view.php:78
msgid "Color mode switch"
msgstr "Переключатель цветового режима"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:92
msgid "Items Counter"
msgstr "Счётчик товаров"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:20,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:13
msgid "Slider"
msgstr "Слайдер"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:41
msgid "Columns & Posts"
msgstr "Колонки и записи"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:49,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:311,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:38
msgid "Number of columns"
msgstr "Количество колонок"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:68
msgid "Number of posts"
msgstr "Количество записей"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:91,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:344,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:75
msgid "Autoplay"
msgstr "Автозапуск"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:112,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:356,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:91
msgid "Delay (Seconds)"
msgstr "Задержка (в секундах)"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:113,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:357,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:92
msgid "Specify the amount of time (in seconds) to delay between automatically cycling an item."
msgstr "Промежуток времени задержки (в секундах) между циклами автоматического воспр��изведения файла."

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:140
msgid "Choose the default color mode that a user will see when it visits your site."
msgstr "Цветовой режим по умолчанию, который пользователь будет видеть при первом посещении сайта."

#: framework/premium/features/content-blocks/options/popup.php:577
msgid "Enable this option if you want to lock the page scroll while the popup is triggered."
msgstr "Опция блокировки прокрутки страницы во время срабатывания модального окна."

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/config.php:4
msgid "Color Switch"
msgstr "Переключатель цвета"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:39
msgid "Reverse Icon State"
msgstr "Реверсивное состояние значка"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:102
msgid "Dark Mode Label"
msgstr "Ярлык тёмного режима"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:114
msgid "Light Mode Label"
msgstr "Ярлык светлого режима"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:118,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/view.php:54
msgid "Light Mode"
msgstr "Светлый режим"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:128
msgid "Default Color Mode"
msgstr "Цветовой режим по умолчанию"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:136
msgid "Light"
msgstr "Светлый"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:137
msgid "Dark"
msgstr "Тёмный"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:138
msgid "OS Aware"
msgstr "Распознавание ОС"

#: framework/premium/features/content-blocks/options/popup.php:326
msgid "Set after how many days the popup will relaunch if the additional close trigger is met."
msgstr "Количество дней, через которое модальное окно будет запускаться повторно, если будет выполнен дополнительный триггер закрытия."

#: framework/premium/features/content-blocks/options/popup.php:446
msgid "Load Content With AJAX"
msgstr "Загрузка содержимого с помощью Ajax"

#: framework/premium/features/content-blocks/options/popup.php:450
msgid "Enable this option if you want to load the popup content using AJAX."
msgstr "Опция загрузки содержимого модального окна с помощью Ajax."

#: framework/premium/features/content-blocks/options/popup.php:459
msgid "Reload Content"
msgstr "Перезагрузка контента"

#: framework/premium/features/content-blocks/options/popup.php:466
#: static/js/options/ConditionsManager/ScheduleDate.js:78
msgid "Never"
msgstr "Никогда"

#: framework/premium/features/content-blocks/options/popup.php:467
msgid "Always"
msgstr "Всегда"

#: framework/premium/features/content-blocks/options/popup.php:469
msgid "Set this option to always if you have dynamic content inside the popup in order to keep everything up to date."
msgstr "Значение параметра «всегда» подходит для динамически меняющегося содержимого модального окна."

#: framework/premium/features/content-blocks/options/popup.php:495,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:637
msgid "Popup Visibility"
msgstr "Видимость модального окна"

#: framework/premium/features/content-blocks/options/popup.php:573
msgid "Scroll Lock"
msgstr "Блокировка прокрутки"

#: framework/premium/features/content-blocks/options/popup.php:250
msgid "Set the button class selector that will trigger popup to close."
msgstr "Установка селектора класса кнопки, вызывающего закрытие модального окна."

#: framework/premium/features/content-blocks/options/popup.php:280
msgid "Relaunch Trigger"
msgstr "Триггер повторного запуска"

#: framework/premium/features/content-blocks/options/popup.php:286
msgid "Never relaunch"
msgstr "Никогда не запускать повторно"

#: framework/premium/features/content-blocks/options/popup.php:297
msgid "Days After Close"
msgstr "Дни после закрытия"

#: framework/premium/features/content-blocks/options/popup.php:303
msgid "Set after how many days the popup will relaunch."
msgstr "Количество дней, по прошествии которых модальное окно будет запущено повторно."

#: framework/premium/features/content-blocks/options/popup.php:313
msgid "Days After Form Submit"
msgstr "Дни после отправки формы"

#: framework/premium/features/content-blocks/options/popup.php:317
msgid "Days After Button Click"
msgstr "Дни после нажатия кнопки"

#: framework/premium/features/content-blocks/options/maintenance.php:16
msgid "Add one or more conditions to display the Maintenance block."
msgstr "Добавление одного или нескольких условий для отображения блока техобслуживания."

#: framework/premium/features/content-blocks/options/popup.php:53
msgid "Popup Display Conditions"
msgstr "Условия отображения модального окна"

#: framework/premium/features/content-blocks/options/popup.php:54
msgid "Choose where you want this popup to be displayed."
msgstr "Где нужно отображать это модальное окно."

#: framework/premium/features/content-blocks/options/popup.php:78
msgid "On element click"
msgstr "Нажатие на элемент"

#: framework/premium/features/content-blocks/options/popup.php:153
msgid "Close Popup On Scroll Back"
msgstr "Закрытие модального окна при прокрутке назад"

#: framework/premium/features/content-blocks/options/popup.php:216
msgid "Additional Close Trigger"
msgstr "Дополнительный триггер закрытия"

#: framework/premium/features/content-blocks/options/popup.php:223
msgid "On form submit"
msgstr "Отправка формы"

#: framework/premium/features/content-blocks/options/popup.php:224
msgid "On button click"
msgstr "Нажатие кнопки"

#: framework/premium/features/content-blocks/options/popup.php:235
msgid "The popup will auto-close if a form submit action is detected inside of it."
msgstr "Модальное окно автоматически закроется, если в нём произойдёт отправка формы."

#: framework/premium/features/content-blocks/options/popup.php:247
msgid "Button Class Selector"
msgstr "Селектор класса кнопки"

#: framework/premium/features/content-blocks/options/hook.php:41,
#: framework/premium/features/content-blocks/options/nothing_found.php:35
msgid "Choose where you want this content block to be displayed."
msgstr "Выберите, где нужно отображать этот блок содержимого."

#: framework/premium/features/content-blocks/options/hook.php:283
msgid "Select a post/page to preview it's content inside the editor while building the hook."
msgstr "Выберите запись/страницу, чтобы просмотреть её содержимое в редакторе во время создания зацепа."

#: framework/premium/features/content-blocks/options/maintenance.php:15
msgid "Maintenance Block Display Conditions"
msgstr "Условия отображения блока техобслуживания"

#: framework/premium/features/media-video/options.php:110
msgid "Display a minimalistic view of the video player."
msgstr "Отображение минималистичного видеоплеера."

#: framework/premium/features/performance-typography/feature.php:116
msgid "Preconnect Google Fonts"
msgstr "Предподключение шрифтов Google"

#: framework/premium/features/performance-typography/feature.php:127
msgid "Preconnect Adobe Typekit Fonts"
msgstr "Предподключение шрифтов Adobe Typekit"

#: framework/features/header/items/account/config.php:4
msgid "Account"
msgstr "Профиль"

#: framework/features/header/items/account/options.php:12
msgid "User Info"
msgstr "Данные пользователя"

#: framework/features/header/items/account/options.php:60
msgid "User Avatar"
msgstr "Аватар"

#: framework/features/header/items/account/options.php:91,
#: framework/features/header/items/account/options.php:658,
#: framework/features/header/items/account/views/login.php:164,
#: framework/features/header/items/account/views/login.php:364
msgid "Edit Profile"
msgstr "Править профиль"

#: framework/features/header/items/account/options.php:101,
#: framework/features/header/items/account/options.php:105,
#: framework/features/header/items/account/options.php:667,
#: framework/features/header/items/account/views/login.php:170,
#: framework/features/header/items/account/views/login.php:385
msgid "Log Out"
msgstr "Выйти"

#: framework/features/header/items/account/options.php:185,
#: framework/features/header/items/account/options.php:189,
#: framework/features/header/items/account/views/login.php:591
msgid "Dokan Dashboard"
msgstr "Панель управления Dokan"

#: framework/features/header/items/account/options.php:199,
#: framework/features/header/items/account/options.php:203,
#: framework/features/header/items/account/views/login.php:625
msgid "Dokan Shop"
msgstr "Магазин Dokan"

#: framework/features/header/items/account/options.php:215,
#: framework/features/header/items/account/options.php:219,
#: framework/features/header/items/account/views/login.php:657
msgid "Tutor LMS Dashboard"
msgstr "Панель управления Tutor LMS"

#: framework/features/header/items/account/options.php:235,
#: framework/features/header/items/account/views/login.php:684
msgid "bbPress Dashboard"
msgstr "Панель управления bbPress"

#: framework/features/header/items/account/options.php:619,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:32
msgid "Link"
msgstr "Ссылка"

#: framework/features/header/items/account/options.php:630
msgid "Dropdown Items"
msgstr "Выпадающие элементы"

#: framework/features/header/items/account/options.php:687
msgid "Link To"
msgstr "Ссылка на"

#: framework/premium/extensions/color-mode-switch/includes/logo-enhancements.php:34
msgid "Dark Mode Logo"
msgstr "Логотип тёмного режима"

#: framework/premium/extensions/woocommerce-extra/features/sku-layer.php:60,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:248,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:434
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:140
msgid "SKU"
msgstr "Артикул"

#: framework/premium/features/content-blocks/options/404.php:84,
#: framework/premium/features/content-blocks/options/archive.php:161,
#: framework/premium/features/content-blocks/options/header.php:102,
#: framework/premium/features/content-blocks/options/hook.php:211,
#: framework/premium/features/content-blocks/options/maintenance.php:81,
#: framework/premium/features/content-blocks/options/nothing_found.php:102,
#: framework/premium/features/content-blocks/options/single.php:93
msgid "Wide"
msgstr "Широкий"

#: framework/premium/features/content-blocks/options/archive.php:10
msgid "Replace Conditions"
msgstr "Условия замены"

#: framework/premium/features/content-blocks/options/archive.php:15
msgid "Template Replace Conditions"
msgstr "Условия замены шаблона"

#: framework/premium/features/content-blocks/options/archive.php:16,
#: framework/premium/features/content-blocks/options/header.php:36,
#: framework/premium/features/content-blocks/options/single.php:16
msgid "Choose where you want this template to be displayed."
msgstr "Выберите, где нужно применять этот шаблон."

#: framework/premium/features/content-blocks/options/archive.php:17
msgid "Add Replace Condition"
msgstr "Добавить условие замены"

#: framework/premium/features/content-blocks/options/archive.php:142,
#: framework/premium/features/content-blocks/options/single.php:74
msgid "Left Sidebar"
msgstr "Левый сайдбар"

#: framework/premium/features/content-blocks/options/archive.php:147,
#: framework/premium/features/content-blocks/options/single.php:79
msgid "Right Sidebar"
msgstr "Правый сайдбар"

#: framework/premium/features/content-blocks/options/header.php:35,
#: framework/premium/features/content-blocks/options/single.php:15
msgid "Template Display Conditions"
msgstr "Условия отображения шаблона"

#: framework/premium/features/content-blocks/options/hook.php:40
msgid "Content Block Display Conditions"
msgstr "Условия отображения блока содержимого"

#. translators: placeholder here means the actual URL.
#: framework/premium/features/media-video/options.php:77
msgid "YouTube won't store information about visitors on your website unless they play the video. More info about this can be found %shere%s."
msgstr "YouTube не будет хранить информацию о посетителях вашего сайта, пока они не воспроизведут видео. Более подробную информацию об этом ищите %sздесь%s."

#: framework/premium/features/media-video/options.php:91
msgid "Autoplay Video"
msgstr "Автозапуск видео"

#: framework/premium/features/media-video/options.php:94
msgid "Automatically start video playback after the gallery is loaded."
msgstr "Автоматическое воспроизведение видео после загрузки галереи."

#: framework/premium/features/media-video/options.php:99
msgid "Loop Video"
msgstr "Зациклить видео"

#: framework/premium/features/media-video/options.php:102
msgid "Start video again after it ends."
msgstr "Запуск видео снова после того, как оно закончится."

#: framework/premium/features/media-video/options.php:107
msgid "Simplified Player"
msgstr "Упрощённый проигрыватель"

#: framework/premium/features/content-blocks/hooks-manager.php:720
msgid "After single product \"Add to cart\" button"
msgstr "После кнопки добавления в корзину отдельного товара"

#: framework/premium/features/content-blocks/hooks-manager.php:725
msgid "Before single product meta"
msgstr "Перед метаданными товара"

#: framework/premium/features/content-blocks/hooks-manager.php:730
msgid "After single product meta"
msgstr "После метаданных товара"

#: framework/premium/features/media-video/options.php:6
msgid "Video Source"
msgstr "Источник видео"

#: framework/premium/features/media-video/options.php:25
msgid "Upload Video"
msgstr "Загрузить видео"

#: framework/premium/features/media-video/options.php:29
msgid "Upload an MP4 file into the media library."
msgstr "Загрузить файл MP4 в медиатеку."

#: framework/premium/features/media-video/options.php:42
msgid "YouTube Url"
msgstr "URL YouTube"

#: framework/premium/features/media-video/options.php:44
msgid "Enter a valid YouTube media URL."
msgstr "Введите действительный URL видео на YouTube."

#: framework/premium/features/media-video/options.php:57
msgid "Vimeo Url"
msgstr "URL Vimeo"

#: framework/premium/features/media-video/options.php:59
msgid "Enter a valid Vimeo media URL."
msgstr "Введите действительный URL видео на Vimeo."

#: framework/premium/features/media-video/options.php:72
msgid "YouTube Privacy Enhanced Mode"
msgstr "Расширенный режим конфиденциальности YouTube"

#: framework/premium/features/content-blocks/hooks-manager.php:234
msgid "After first post meta"
msgstr "После мета первой записи"

#: framework/premium/features/content-blocks/hooks-manager.php:242
msgid "After second post meta"
msgstr "После мета второй записи"

#: framework/premium/features/content-blocks/hooks-manager.php:580
msgid "Offcanvas Cart - Empty State"
msgstr "Плавающая корзина – пустая"

#: framework/premium/features/content-blocks/hooks-manager.php:705
msgid "Before single product gallery"
msgstr "Перед галереей отдельного товара"

#: framework/premium/features/content-blocks/hooks-manager.php:710
msgid "After single product gallery"
msgstr "После галереи отдельного товара"

#: framework/premium/features/content-blocks/hooks-manager.php:715
msgid "Before single product \"Add to cart\" button"
msgstr "Перед кнопкой добавления в корзину отдельного товара"

#: framework/premium/extensions/mega-menu/options.php:532,
#: framework/premium/extensions/mega-menu/options.php:875
msgid "Badge Settings"
msgstr "Настройки отметки"

#: framework/premium/extensions/mega-menu/options.php:764
msgid "Column Background"
msgstr "Фон колонки"

#: framework/premium/extensions/shortcuts/customizer.php:673,
#: framework/premium/extensions/shortcuts/customizer.php:699,
#: framework/premium/extensions/shortcuts/views/bar.php:55,
#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:241,
#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:245,
#: framework/premium/extensions/woocommerce-extra/features/compare/helpers.php:110,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:36,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:418,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/bar.php:53,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/config.php:4,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:140,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/view.php:97
msgid "Compare"
msgstr "Сравнение"

#: framework/premium/extensions/shortcuts/customizer.php:1124
msgid "Item Background Color"
msgstr "Цвет фона элемента"

#: framework/premium/extensions/shortcuts/customizer.php:1205
msgid "Wishlist Badge Color"
msgstr "Цвет значка избранного"

#: framework/premium/extensions/shortcuts/customizer.php:1247
msgid "Compare Badge Color"
msgstr "Цвет значка сравнения"

#: framework/premium/extensions/sidebars/extension.php:185
msgid "Remove Widget Area"
msgstr "Удалить область виджетов"

#: framework/premium/extensions/woocommerce-extra/config.php:21
msgid "This extension requires the WooCommerce plugin to be installed and activated."
msgstr "Для этого расширения требуется установить и активировать плагин WooCommerce."

#: framework/premium/extensions/woocommerce-extra/extension.php:359
msgid "Cart Page"
msgstr "Страница корзины"

#: framework/premium/features/content-blocks/admin-ui.php:269
msgid "Nothing Found"
msgstr "Ничего не найдено"

#: framework/premium/features/content-blocks/admin-ui.php:270
msgid "Maintenance"
msgstr "Техобслуживание"

#: framework/premium/features/content-blocks/admin-ui.php:374
msgid "On click to element"
msgstr "При нажатии по элементу"

#: framework/extensions/product-reviews/extension.php:321,
#: framework/premium/features/content-blocks/content-block-layer.php:194,
#: framework/premium/features/content-blocks/content-block-layer.php:244,
#: framework/premium/extensions/woocommerce-extra/features/sku-layer.php:64,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:739,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:617,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:668,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:339,
#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:203,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:190,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:251,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:274,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:903,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-layer.php:246
msgid "Bottom Spacing"
msgstr "Отступ снизу "

#: framework/premium/features/content-blocks/hooks-manager.php:194
msgid "Before first post meta"
msgstr "Перед мета первой записи"

#: framework/premium/features/content-blocks/hooks-manager.php:202
msgid "Before second post meta"
msgstr "Перед мета второй записи"

#: framework/features/conditions/rules/woo.php:80,
#: framework/features/conditions/rules/woo.php:95
msgid "Product with Taxonomy ID"
msgstr "Товар с ID таксономии"

#: framework/premium/extensions/color-mode-switch/extension.php:96
msgid "Dark Mode Color Palette"
msgstr "Палитра тёмного режима"

#: framework/premium/extensions/mega-menu/options.php:72
msgid "Dropdown Custom Width"
msgstr "Особая ширина выпадающего"

#: framework/premium/extensions/mega-menu/options.php:309
msgid "AJAX Content Loading"
msgstr "Ajax-загрузка содержимого"

#: framework/premium/extensions/mega-menu/options.php:312
msgid "If you have complex data inside your mega menu you can enable this option in order to load the dropdown content with AJAX and improve the website loading time."
msgstr "Если в мегаменю требуется разместить сложные данные, можно включить эту опцию, чтобы выпадающее содержимое загружалось с помощью Ajax и сократилось время загрузки сайта."

#: framework/premium/extensions/mega-menu/options.php:403
msgid "Content Visibility"
msgstr "Видимость содержимого"

#: framework/premium/features/clone-cpt.php:127
msgid "Post creation failed, could not find original post: "
msgstr "Ошибка создания записи, не удалось найти исходную запись: "

#: framework/premium/features/clone-cpt.php:184
msgid "Post copy created."
msgstr "Копия записи создана."

#: framework/premium/features/local-gravatars.php:34
msgid "Store Gravatars Locally"
msgstr "Локальные Gravatar'ы"

#: framework/premium/features/local-gravatars.php:39
msgid "Store and load Gravatars locally for increased privacy and performance."
msgstr "Хранение и загрузка Gravatar'ов локал��но для повышения уровней конфиденциальности и производительности."

#: framework/premium/features/premium-header.php:314,
#: framework/premium/features/socials.php:13,
#: framework/features/blocks/contact-info/options.php:103,
#: framework/features/blocks/contact-info/options.php:168,
#: framework/features/blocks/contact-info/options.php:231,
#: framework/features/blocks/contact-info/options.php:294,
#: framework/features/blocks/contact-info/options.php:357,
#: framework/features/blocks/contact-info/options.php:420,
#: framework/features/blocks/contact-info/options.php:483,
#: framework/features/header/items/account/options.php:375,
#: framework/features/header/items/account/options.php:756,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:9
msgid "Icon Source"
msgstr "Источник значка"

#: framework/premium/features/socials.php:41
msgid "URL Source"
msgstr "Источник URL"

#: framework/premium/features/socials.php:59
msgid "Custom URL"
msgstr "Произвольный URL"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:60
msgid "Form Style"
msgstr "Стиль формы"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:68
msgid "Stacked"
msgstr "Стопкой"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:137,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:152
msgid "Your email *"
msgstr "Ваш Email *"

#: framework/extensions/newsletter-subscribe/providers/demo.php:16
msgid "Demo List"
msgstr "Список демо"

#: framework/features/conditions/rules/basic.php:10,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:12
msgid "All Products"
msgstr "Все товары"

#: framework/features/conditions/rules/basic.php:40
msgid "All Singulars"
msgstr "Все отдельные"

#: framework/features/conditions/rules/basic.php:51
msgid "All Archives"
msgstr "Все архивы"

#: framework/features/conditions/rules/date-time.php:5
msgid "Date & Time"
msgstr "Дата и время"

#: framework/features/conditions/rules/date-time.php:9
msgid "Range Date/Time"
msgstr "Диапазон дат/времени"

#: framework/features/conditions/rules/date-time.php:14
#: static/js/options/ConditionsManager/ScheduleDate.js:143
msgid "Recurring Days"
msgstr "Повторяющиеся дни"

#: framework/features/conditions/rules/requests.php:5
msgid "Requests"
msgstr "Запросы"

#: framework/features/conditions/rules/requests.php:9
msgid "Request Referer"
msgstr "Отправитель запроса"

#: framework/features/conditions/rules/requests.php:14
msgid "Request Cookie"
msgstr "Куки запроса"

#: framework/features/conditions/rules/requests.php:19
msgid "Request URL"
msgstr "URL запроса"

#: framework/features/conditions/rules/specific.php:31
msgid "Post with Author ID"
msgstr "Запись с ID автора"

#: framework/features/conditions/rules/woo.php:75,
#: framework/features/conditions/rules/woo.php:90
msgid "Product ID"
msgstr "ID товара"

#: framework/extensions/cookies-consent/customizer.php:199
msgid "Accept Button"
msgstr "Кнопка принятия"

#: framework/extensions/cookies-consent/customizer.php:266
msgid "Decline Button"
msgstr "Кнопка отказа"

#: framework/extensions/cookies-consent/extension.php:105
msgid "Please accept the Privacy Policy in order to comment."
msgstr "Пожалуйста, примите политику конфиденциальности сайта, чтобы оставить комментарий."

#: framework/extensions/cookies-consent/extension.php:106
msgid "Comment Submission Failure"
msgstr "Ошибка отправки комментария"

#: framework/extensions/newsletter-subscribe/customizer.php:28,
#: framework/extensions/newsletter-subscribe/helpers.php:24,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:15
msgid "Enter your email address below and subscribe to our newsletter"
msgstr "Введите свой Email ниже и подпишитесь на рассылку новостей"

#: framework/extensions/newsletter-subscribe/customizer.php:389
msgid "Container Shadow"
msgstr "Тень контейнера"

#: framework/extensions/trending/customizer.php:588
msgid "Module Title Font"
msgstr "Шрифт заголовка модуля"

#: framework/extensions/trending/customizer.php:596
msgid "Module Title Color"
msgstr "Цвет заголовка модуля"

#: framework/extensions/trending/customizer.php:647
msgid "Posts Title Font"
msgstr "Шрифт заголовков сообщений"

#: framework/extensions/trending/customizer.php:656
msgid "Posts Title Font Color"
msgstr "Цвет заголовков сообщений"

#: framework/extensions/trending/customizer.php:871
msgid "Arrows Color"
msgstr "Цвет стрелок"

#: framework/premium/features/clone-cpt.php:42,
#: framework/premium/features/clone-cpt.php:45
msgid "Duplicate"
msgstr "Дублировать"

#: framework/premium/features/clone-cpt.php:55
msgid "No post to duplicate"
msgstr "Нет записей для дублирования"

#: framework/helpers/exts-configs.php:314
msgid "Create a customized order “Thank You” page for your customers, giving them a personalized experience."
msgstr "Создание страницы благодарности для клиентов сайта, для создания хорошего впечатления."

#: framework/helpers/exts-configs.php:322,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:5
msgid "Advanced Reviews"
msgstr "Расширенные отзывы"

#: framework/helpers/exts-configs.php:323
msgid "Enhance your WooCommerce reviews with rich content, images and a thumbs up system that help your shoppers find the perfect product."
msgstr "Улучшение отзывов в WooCommerce дополнительным функционалом, изображениями и кнопкой оценки, помогающими покупателям найти лучшие товары."

#: framework/extensions/cookies-consent/customizer.php:112
msgid "Forms Cookies Content"
msgstr "Формирование содержимого куки"

#: framework/helpers/exts-configs.php:362
msgid "Better management for affiliate products with a few simple options that strengthen the external integration with these."
msgstr "Улучшенное управление партнерскими продуктами с помощью нескольких простых опций, усиливающих внешнюю интеграцию с ними."

#: framework/helpers/exts-configs.php:295
msgid "Custom Tabs"
msgstr "Произвольные вкладки"

#: framework/helpers/exts-configs.php:296
msgid "Present additional information about your products by adding new custom tabs to the product information section."
msgstr "Предоставление дополнительной информации о товарах, добавив нестандартные вкладки в раздел информации о товаре."

#: framework/helpers/exts-configs.php:304,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:58,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:62,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:272,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/helpers.php:47,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/modal-view.php:18,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/side-view.php:27
msgid "Size Guide"
msgstr "Таблица размеров"

#: framework/helpers/exts-configs.php:305
msgid "Show a size chart guide so that your visitors can pick the right size for them when ordering a product."
msgstr "Отображение памятки, помогающей посетителям выбрать подходящий размер при заказе товара."

#: framework/helpers/exts-configs.php:313
msgid "Custom Thank You Pages"
msgstr "Произвольные страницы благодарности"

#: framework/helpers/exts-configs.php:279
msgid "Catch the attention of your clients by showcasing your product variations as colour, image or button swatches."
msgstr "Привлечение внимания клиентов, демонстрацией вариаций товаров в виде образцов цвета, изображений или кнопок размеров."

#: framework/helpers/exts-configs.php:286,
#: framework/features/conditions/rules/woo.php:42
msgid "Product Brands"
msgstr "Бренды товаров"

#: framework/helpers/exts-configs.php:287
msgid "Categorise products by brands and show their logo in archive or single page so users could discover more about their makers."
msgstr "Классификация товаров по брендам и отображения их логотипов в списках и на отдельных страницах, позволяя пользователи узнавать о производителях."

#: framework/helpers/exts-configs.php:361
msgid "Affiliate Product Links"
msgstr "Ссылки на партнёрские продукты"

#: framework/helpers/exts-configs.php:339
msgid "Replace the standard product gallery with additional layouts which can showcase the photos as a grid or even a slider."
msgstr "Замена стандартной галереи товаров расширенными макетами, способными отображать фотографии в виде сетки или слайдера."

#: framework/helpers/exts-configs.php:354
msgid "Search by SKU"
msgstr "Поиск по артикулу"

#: framework/helpers/exts-configs.php:355
msgid "Advanced searching for products by their SKU classification can be useful in cases of vast product catalogues."
msgstr "Расширенный поиск товаров по их артикулу может быть полезен при наличии обширного ассортимента."

#: framework/helpers/exts-configs.php:331
msgid "Add a visual cue that tells your visitors how much the cart total must be to be able to benefit of free shipping."
msgstr "Добавление визуальной подсказки, сообщающей покупателям, сумму покупки, позволяющей воспользоваться бесплатной доставкой."

#: framework/helpers/exts-configs.php:278,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:4
msgid "Variation Swatches"
msgstr "Образцы вариаций"

#: framework/helpers/exts-configs.php:271
msgid "Compare products with a clear and concise table system that gives your users a way to make a quick decision."
msgstr "Сравнение товаров с помощью чёткой и лаконичной системы таблиц, которая дает пользователям возможность быстро принимать решение."

#: framework/helpers/exts-configs.php:346
msgid "Product Share Box"
msgstr "Блок публикации в соцсетях"

#: framework/helpers/exts-configs.php:347
msgid "Enable social sharing abilities for products available on the site, letting even more users discover your great shop selection."
msgstr "Возможность публикации информации о товарах, доступных на сайте, в соцсетях, что позволит еще большему количеству пользователей ознакомиться с ассортиментом магазина."

#: framework/helpers/exts-configs.php:338
msgid "Advanced Gallery"
msgstr "Расширенная галерея"

#: framework/helpers/exts-configs.php:247
msgid "Preview the available products and let your users make quick and informative decisions about their purchase."
msgstr "Просмотр доступных товаров и помощь клиентам быстро и легко принимать особнованные ре��ения об их покупке."

#: framework/helpers/exts-configs.php:254,
#: framework/premium/extensions/shortcuts/views/bar.php:54
msgid "Filters"
msgstr "Фильтры"

#: framework/helpers/exts-configs.php:255
msgid "Drill down the product list with new filtering widgets, an off canvas area for them and showing the active filters on the page."
msgstr "Детализация списка товаров с помощью новых виджетов фильтрации, выделяя для них область за пределами холста и отображая активные фильтры на странице."

#: framework/helpers/exts-configs.php:263
msgid "A set of features that lets you create easily your dream products wishlists and share them with friends and family."
msgstr "Набор функций, позволяющих легко создавать списки понравившихся товаров и делиться ими с друзьями и семьёй."

#: framework/helpers/exts-configs.php:270
msgid "Compare View"
msgstr "Таблица сравнения"

#: framework/helpers/exts-configs.php:239
msgid "Adds the “add to cart” actions to the product page as a floating bar if the product summary has disappeared from view."
msgstr "Добавление действия добавления в корзину на странице товара в виде плавающей панели, появляющейся когда краткое описание товара исчезает из поля зрения."

#: framework/helpers/exts-configs.php:246,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:58
msgid "Quick View"
msgstr "Быстрый просмотр"

#: framework/helpers/exts-configs.php:157
msgid "Let your guests easily filter the posts by their category or tags taxonomy terms, instantly drilling down the listings."
msgstr "Мгновенная фильтрация посетителями записей по рубрикам или меткам при просмотре списков."

#: framework/helpers/exts-configs.php:163
msgid "Taxonomy Customisations"
msgstr "Настройки таксономии"

#: framework/helpers/exts-configs.php:164
msgid "Additional customisation options for your taxonomies such as hero backgrounds and custom colour labels."
msgstr "Дополнительные возможности настройки таксономий сайта, таких как фон геройского блока и цветовые ярлычки."

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:227
msgctxt "Extension Brand Name"
msgid "Shop Extra"
msgstr "Функционал магазина"

#: framework/helpers/exts-configs.php:143
msgid "Display the approximate reading time of an article, so that visitors know what to expect when starting to read the content."
msgstr "Отображение приблизительного времени прочтения статьи, чтобы посетители знали, чего ожидать, приступая к чтению."

#: framework/helpers/exts-configs.php:149
#: static/js/editor/blocks/dynamic-data/index.js:100
msgid "Dynamic Data"
msgstr "Динамические данные"

#: framework/helpers/exts-configs.php:150
msgid "Integrates custom fields solutions in the meta layers of a post and presents additional information."
msgstr "Интеграция пользовательских полей в мета-слои записи и предоставление дополнительной информации."

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:83
msgctxt "Extension Brand Name"
msgid "Color Mode Switch"
msgstr "Переключатель цветового режима"

#: framework/helpers/exts-configs.php:84
msgid "Add a dark colour scheme and switch to your website, which will make it pleasant to look at in low light environments."
msgstr "Добавление тёмной цветовой гаммы и переключение сайта в комфортный для просмотра при слабом освещении режим."

#. translators: 1: link open 2: link close
#: framework/features/account-auth.php:253
msgid "Registration complete. Please check your email, then visit the %1$slogin page%2$s."
msgstr "Регистрация завершена. Проверьте свою электронную почту, затем перейдите на %1$sстраницу входа%2$s."

#: framework/dashboard.php:34,
#: framework/features/header/items/account/options.php:23,
#: framework/features/header/items/account/options.php:73,
#: framework/features/header/items/account/options.php:77,
#: framework/features/header/items/account/options.php:649,
#: framework/features/header/items/account/views/login.php:158,
#: framework/features/header/items/account/views/login.php:335
msgid "Dashboard"
msgstr "Панель управления"

#: framework/dashboard.php:521
msgid "You do not have sufficient permissions to access this page."
msgstr "У вас недостаточно разрешений для доступа к этой странице."

#: framework/theme-integration.php:221,
#: framework/features/blocks/share-box/options.php:25
msgid "X (Twitter)"
msgstr "X (Twitter)"

#: framework/theme-integration.php:234
msgid "Mastodon"
msgstr "Mastodon"

#: framework/features/conditions/rules/pages.php:39
msgid "Privacy Policy Page"
msgstr "Страница политики конфиденциальности"

#: framework/features/conditions/rules/pages.php:46
msgid "Author Archives"
msgstr "Архивы авторов"

#: framework/features/conditions/rules/woo.php:104,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:42
msgid "WooCommerce"
msgstr "WooCommerce"

#: framework/features/conditions/rules/woo.php:15
msgid "Shop Home"
msgstr "Главная магазина"

#: framework/features/conditions/rules/woo.php:20,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:66
msgid "Single Product"
msgstr "Отдельный товар"

#: framework/features/conditions/rules/woo.php:25
msgid "Product Archives"
msgstr "Архивы товаров"

#: framework/features/conditions/rules/woo.php:30,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:91
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:27
msgid "Product Categories"
msgstr "Категории товаров"

#: framework/features/conditions/rules/woo.php:49
msgid "Product Tags"
msgstr "Метки товаров"

#: framework/features/conditions/rules/cpt.php:53
msgid "Custom Post Types"
msgstr "Произвольные типы записей"

#: framework/features/conditions/rules/localization.php:15
msgid "Current Language"
msgstr "Текущий язык"

#: framework/features/conditions/rules/bbPress.php:11,
#: framework/features/header/items/account/options.php:231
msgid "bbPress"
msgstr "bbPress"

#: framework/features/conditions/rules/bbPress.php:15,
#: framework/features/header/items/account/options.php:24,
#: framework/features/header/items/account/options.php:87
msgid "Profile"
msgstr "Профиль"

#: framework/extensions/newsletter-subscribe/helpers.php:142
msgid "First name"
msgstr "Имя"

#: framework/extensions/trending/customizer.php:537
msgid "Display Location"
msgstr "Место отображения"

#: framework/extensions/trending/customizer.php:545
msgid "Before Footer"
msgstr "Перед подвалом"

#: framework/extensions/trending/customizer.php:550
msgid "After Footer"
msgstr "После подвала"

#: framework/extensions/trending/customizer.php:555
msgid "After Header"
msgstr "После шапки"

#: framework/extensions/trending/customizer.php:572
msgid "Trending Block Display Conditions"
msgstr "Условия отображения блока популярных"

#: framework/extensions/trending/customizer.php:573
msgid "Add one or more conditions to display the trending block."
msgstr "Добавьте одно или несколько условий для отображения блока популярных записей."

#: framework/premium/features/content-blocks/admin-ui.php:641
msgid "Hide Hooks"
msgstr "Спрятать зацепы"

#: framework/premium/features/content-blocks/admin-ui.php:642
msgid "Show Hooks"
msgstr "Показать зацепы"

#: framework/premium/features/content-blocks/admin-ui.php:698
msgid "Hide Theme Hooks"
msgstr "Спрятать зацепы темы"

#: framework/premium/features/content-blocks/admin-ui.php:699
msgid "Show Theme Hooks"
msgstr "Показать зацепы темы"

#: framework/premium/features/content-blocks/admin-ui.php:707
msgid "Hide WooCommerce Hooks"
msgstr "Спрятать зацепы WooCommerce"

#: framework/premium/features/content-blocks/admin-ui.php:708
msgid "Show WooCommerce Hooks"
msgstr "Показать зацепы WooCommerce"

#: framework/features/header/modal/register.php:76
msgid "A link to set a new password will be sent to your email address."
msgstr "Ссылка для установки нового пароля будет отправлена на ваш Email."

#: framework/premium/extensions/code-snippets/extension.php:31,
#: framework/premium/extensions/code-snippets/extension.php:86,
#: framework/premium/extensions/code-snippets/extension.php:131
msgid "Header scripts"
msgstr "Скрипты в шапке"

#: framework/premium/extensions/code-snippets/extension.php:53,
#: framework/premium/extensions/code-snippets/extension.php:108,
#: framework/premium/extensions/code-snippets/extension.php:153
msgid "Footer scripts"
msgstr "Скрипты в подвале"

#: framework/premium/extensions/mega-menu/options.php:377,
#: framework/premium/extensions/mega-menu/options.php:385,
#: framework/premium/features/content-blocks/content-block-layer.php:170,
#: framework/premium/features/content-blocks/content-block-layer.php:178,
#: framework/premium/features/content-blocks/content-block-layer.php:221,
#: framework/premium/features/content-blocks/content-block-layer.php:229,
#: framework/features/header/items/account/options.php:254,
#: framework/features/header/items/account/options.php:262,
#: framework/premium/features/premium-header/items/content-block/options.php:13,
#: framework/premium/features/premium-header/items/content-block/options.php:21
#: framework/premium/static/js/blocks/ContentBlock.js:110
msgid "Select Content Block"
msgstr "Выберите блок содержимого"

#: framework/premium/extensions/mega-menu/options.php:380,
#: framework/premium/features/content-blocks/content-block-layer.php:173,
#: framework/premium/features/content-blocks/content-block-layer.php:224,
#: framework/features/header/items/account/options.php:257,
#: framework/premium/features/premium-header/items/content-block/options.php:16
msgid "Create a new content Block/Hook"
msgstr "Создать блок содержимого/зацепа"

#: framework/premium/extensions/mega-menu/options.php:637
msgid "Heading Font"
msgstr "Шрифт заголовка"

#: framework/premium/features/content-blocks/admin-ui.php:155
msgid "Enable"
msgstr "Включить"

#: framework/premium/features/content-blocks/admin-ui.php:156
msgid "Disable"
msgstr "Выключить"

#: framework/premium/features/content-blocks/admin-ui.php:211
msgid "Enabled %s content block."
msgid_plural "Enabled %s content blocks."
msgstr[0] "Включен %s блок содержимого."
msgstr[1] "Включено %s блока содержимого."
msgstr[2] "Включено %s блоков содержимого."

#: framework/premium/features/content-blocks/admin-ui.php:236
msgid "Disabled %s content block."
msgid_plural "Disabled %s content blocks."
msgstr[0] "Выключен %s блок содержимого."
msgstr[1] "Выключено %s блока содержимого."
msgstr[2] "Выключено %s блоков содержимого."

#: framework/premium/features/content-blocks/admin-ui.php:264
msgid "404 Page"
msgstr "Страница 404"

#: framework/premium/features/content-blocks/admin-ui.php:267,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:16,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:64
msgid "Archive"
msgstr "Архив"

#: framework/premium/features/content-blocks/admin-ui.php:268,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:17,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:65
msgid "Single"
msgstr "Отдельная запись"

#: framework/premium/features/content-blocks/hooks-manager.php:39
msgid "WP body open"
msgstr "Открытие body WP"

#: framework/premium/features/content-blocks/hooks-manager.php:340
msgid "Before related posts"
msgstr "Перед связанными записями"

#: framework/premium/features/content-blocks/hooks-manager.php:341,
#: framework/premium/features/content-blocks/hooks-manager.php:348,
#: framework/premium/features/content-blocks/hooks-manager.php:355,
#: framework/premium/features/content-blocks/hooks-manager.php:362,
#: framework/premium/features/content-blocks/hooks-manager.php:369,
#: framework/premium/features/content-blocks/hooks-manager.php:376,
#: framework/premium/features/content-blocks/hooks-manager.php:383,
#: framework/premium/features/content-blocks/hooks-manager.php:390,
#: framework/premium/features/content-blocks/hooks-manager.php:398,
#: framework/premium/features/content-blocks/hooks-manager.php:405
msgid "Related posts"
msgstr "Связанные записи"

#: framework/premium/features/content-blocks/hooks-manager.php:347
msgid "Related posts top"
msgstr "Верх связанных записей"

#: framework/premium/features/content-blocks/hooks-manager.php:368
msgid "Card top"
msgstr "Верх карты"

#: framework/premium/features/content-blocks/hooks-manager.php:375
msgid "Before featured image"
msgstr "Перед изображением записи"

#: framework/premium/features/content-blocks/hooks-manager.php:382
msgid "After featured image"
msgstr "После изображения записи"

#: framework/premium/features/content-blocks/hooks-manager.php:389
msgid "Card bottom"
msgstr "Низ карты"

#: framework/premium/features/content-blocks/hooks-manager.php:397
msgid "Related posts bottom"
msgstr "Низ связанных записей"

#: framework/premium/features/content-blocks/hooks-manager.php:404
msgid "After related posts"
msgstr "После связанных записей"

#: framework/premium/features/content-blocks/hooks-manager.php:568
msgid "Offcanvas Filters - Top"
msgstr "Плавающий блок фильтров - верх"

#: framework/premium/features/content-blocks/hooks-manager.php:574
msgid "Offcanvas Filters - Bottom"
msgstr "Плавающий блок фильтров - низ"

#: framework/premium/features/content-blocks/options/archive.php:31,
#: framework/premium/features/content-blocks/options/single.php:30
msgid "Replacement Behavior"
msgstr "Поведение замены"

#: framework/premium/features/content-blocks/options/archive.php:38
msgid "Only Card"
msgstr "Только карта"

#: framework/premium/features/content-blocks/options/archive.php:39,
#: framework/premium/features/content-blocks/options/single.php:38
msgid "Full Page"
msgstr "Вся страница"

#: framework/premium/features/content-blocks/options/404.php:51,
#: framework/premium/features/content-blocks/options/archive.php:118,
#: framework/premium/features/content-blocks/options/maintenance.php:48,
#: framework/premium/features/content-blocks/options/nothing_found.php:69,
#: framework/premium/features/content-blocks/options/single.php:50
msgid "Page Structure"
msgstr "Структура страницы"

#: framework/premium/features/content-blocks/options/single.php:37
msgid "Content Area"
msgstr "Область содержимого"

#: framework/premium/features/content-blocks/options/single.php:99
msgid "Content Area Vel Spacing"
msgstr "Отступы области содержимого"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:81
msgid "Filter Source"
msgstr "Источник фильтра"

#: framework/features/blocks/contact-info/options.php:590,
#: framework/premium/features/premium-header/items/contacts/options.php:6
msgid "Items Direction"
msgstr "Направление элементов"

#: framework/features/blocks/contact-info/options.php:597,
#: framework/premium/features/premium-header/items/contacts/options.php:14
msgid "Horizontal"
msgstr "Горизонтальное"

#: framework/features/blocks/contact-info/options.php:596,
#: framework/premium/features/premium-header/items/contacts/options.php:13
msgid "Vertical"
msgstr "Вертикальное"

#: framework/premium/features/premium-header/items/contacts/options.php:889,
#: framework/premium/features/premium-header/items/contacts/options.php:931,
#: framework/premium/features/premium-header/items/contacts/options.php:969,
#: framework/premium/features/premium-header/items/contacts/options.php:1007
#: static/js/editor/blocks/about-me/Edit.js:148
#: static/js/editor/blocks/contact-info/Edit.js:162
msgid "Icons Background Color"
msgstr "Цвет фона значков"

#: framework/premium/features/premium-header/items/contacts/options.php:893,
#: framework/premium/features/premium-header/items/contacts/options.php:935,
#: framework/premium/features/premium-header/items/contacts/options.php:973,
#: framework/premium/features/premium-header/items/contacts/options.php:1011
#: static/js/editor/blocks/about-me/Edit.js:179
#: static/js/editor/blocks/contact-info/Edit.js:193
msgid "Icons Border Color"
msgstr "Цвет границ значков"

#: framework/premium/static/js/options/IconPicker/CustomIcon.js:22
msgid "Click to upload"
msgstr "Нажмите, чтобы загрузить"

#: framework/premium/static/js/options/IconPicker/Modal.js:135
msgid "All Icons"
msgstr "Все значки"

#: static/js/options/ConditionsManager/SingleCondition.js:296
msgid "All authors"
msgstr "Все авторы"

#: static/js/dashboard/screens/DemoInstall/components/Error.js:25
msgid "Can't Import Starter Site"
msgstr "Не удаётся импортировать стартовый сайт"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:31
msgid "Unfortunately, your hosting configuration doesn't meet the minimum requirements for importing a starter site."
msgstr "Конфигурация вашего хостинга не соответствует минимальным требованиям для импорта начального сайта."

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/views/panel.php:62
msgid "Close filters modal"
msgstr "Закрыть модальное окно фильтров"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/view.php:45
msgid "Close quick view"
msgstr "Закрыть быстрый просмотр"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/helpers.php:45
msgid "Quick view toggle"
msgstr "Тумблер быстрого просмотра"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/helpers.php:32
msgid "Quick view icon"
msgstr "Значок быстрого просмотра"

#: framework/features/header/items/account/options.php:1724,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:314,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:185
msgid "Close Button Type"
msgstr "Тип кнопки закрытия"

#: framework/features/header/items/account/options.php:726,
#: framework/features/header/items/account/options.php:1731,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:323,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:23,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:194
msgid "Simple"
msgstr "Прост."

#: framework/features/header/items/account/options.php:1732,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:324,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:195
msgid "Border"
msgstr "Граница"

#: framework/features/header/items/account/options.php:1778,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:366,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:248,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:254,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:298,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:382,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:466
msgid "Border Color"
msgstr "Цвет границы"

#: framework/premium/features/content-blocks/hooks-manager.php:170
msgid "Before description"
msgstr "До описания"

#: framework/premium/features/content-blocks/hooks-manager.php:178
msgid "Before breadcrumbs"
msgstr "До цепочки навигации"

#: framework/premium/features/content-blocks/hooks-manager.php:218
msgid "After description"
msgstr "После описания"

#: framework/premium/features/content-blocks/hooks-manager.php:226
msgid "After breadcrumbs"
msgstr "После цепочки навигации"

#: framework/premium/features/content-blocks/hooks-manager.php:630
msgid "Before shop loop item actions"
msgstr "До цикла действий с товарами"

#: framework/premium/features/content-blocks/hooks-manager.php:635
msgid "After shop loop item actions"
msgstr "После цикла действий с товарами"

#. translators: placeholder here means the actual URL.
#: framework/features/blocks/socials/options.php:24
msgid "Configure the social links in Customizer ➝ General ➝ %sSocial Network Accounts%s."
msgstr "Настройте ссылок соцсетей в Настройщик ➝ Общее ➝ %sПрофили в соцсетях%s."

#: framework/features/header/items/account/options.php:306
msgid "Customizing: Logged out State"
msgstr "Настройка: статус неавторизованности"

#: framework/features/header/items/account/options.php:1088
msgid "User Visibility"
msgstr "Видимость пользователя"

#: framework/features/header/items/account/options.php:1099,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:116,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:95
msgid "Logged In"
msgstr "Зарегистрированные"

#: framework/features/header/items/account/options.php:1100,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:117,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:96
msgid "Logged Out"
msgstr "Гости"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1385
msgid "Custom Field"
msgstr "Произвольные поля"

#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:114
msgid "%s min"
msgid_plural "%s mins"
msgstr[0] "%s минута"
msgstr[1] "%s минуты"
msgstr[2] "%s минут"

#: framework/premium/features/content-blocks/options/archive.php:98
msgid "Default Card Layout"
msgstr "Стандартный макет карточки"

#: framework/premium/features/content-blocks/options/archive.php:103
msgid "Inherit card wrapper settings from Customizer (background color, spacing, shadow)."
msgstr "Наследование настроек оболочки карточки из настройщика (цвет фона, интервал, тень)."

#: framework/premium/features/content-blocks/options/archive.php:53,
#: framework/premium/features/content-blocks/options/hook.php:277,
#: framework/premium/features/content-blocks/options/popup.php:483,
#: framework/premium/features/content-blocks/options/single.php:151
msgid "Dynamic Content Preview"
msgstr "Предпросмотр динамического содержимого"

#: framework/premium/features/content-blocks/options/archive.php:60
msgid "Select a post/page to preview it's content inside the editor while building the archive."
msgstr "Выберите запись/страницу для предпросмотра её содержимого в редакторе при создании архива."

#: framework/premium/features/content-blocks/options/archive.php:66
msgid "Editor/Card Width"
msgstr "Ширина редактора/карточки"

#: framework/premium/features/content-blocks/options/archive.php:77
msgid "Set the editor width for better understanging the layout you are building (just for preview purpose, this option won't apply in frontend)."
msgstr "Устака ширины редактора для лучшего понимания создаваемого макета (только для предпросмотра, параметр не применяется во внешнем интерфейсе)."

#: framework/premium/features/content-blocks/options/popup.php:203
msgid "After X Pages"
msgstr "После X страниц"

#: framework/premium/features/content-blocks/options/popup.php:209
msgid "Set after how many visited pages the popup block will appear."
msgstr "После скольких посещённых страниц появится модальное окно."

#: framework/premium/features/content-blocks/options/popup.php:489,
#: framework/premium/features/content-blocks/options/single.php:157
msgid "Select a post/page to preview it's content inside the editor while building the post/page."
msgstr "Выберите запись/страницу для предпросмотра её содержимого в редакторе при настройки записи/страницы."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:236
msgid "More information on how to generate an API key for Mailerlite can be found %shere%s. Please note that it is required at least one group to be created in your account for the integration to work. More info on how to create a group %shere%s."
msgstr "Дополнительную информацию о генерации ключа API для Mailerlite, можно найти %sздесь%s. Для работы интеграции требуется, чтобы в вашем профиле была создана хотя бы одна группа. Подробнее о создании группы читайте %sздесь%s."

#: framework/premium/static/js/hooks/CodeEditor.js:59
msgid "Code Editor"
msgstr "Редактор кода"

#: framework/premium/static/js/hooks/CreateHook.js:101
msgid "Template Type"
msgstr "Тип шаблона"

#: framework/premium/static/js/hooks/CreateHook.js:116
msgid "Archive Template"
msgstr "Шаблон архива"

#: framework/premium/static/js/hooks/CreateHook.js:124
msgid "Single Template"
msgstr "Отдельный шаблон"

#: framework/premium/static/js/hooks/CreateHook.js:178
msgid "Hook Name"
msgstr "Название зацепа"

#: framework/premium/static/js/hooks/CreateHook.js:182
msgid "Popup Name"
msgstr "Название модального окна"

#: framework/premium/static/js/hooks/CreateHook.js:186
msgid "Template Name"
msgstr "Название шаблона"

#: framework/premium/features/content-blocks/admin-ui.php:263,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:52,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:22
#: framework/premium/static/js/hooks/CreateHook.js:77
msgid "Popup"
msgstr "Модальное окно"

#: framework/premium/static/js/hooks/CreateHook.js:86
msgid "Custom Template"
msgstr "Произвольный шаблон"

#: framework/premium/static/js/options/IconPicker.js:20
msgid "Theme Icons"
msgstr "Значки темы"

#: framework/premium/static/js/options/IconPicker.js:26
msgid "FontAwesome Brands"
msgstr "FontAwesome бренды"

#: framework/premium/static/js/options/IconPicker.js:32
msgid "FontAwesome Solid"
msgstr "FontAwesome залитые"

#: framework/premium/static/js/options/IconPicker.js:38
msgid "FontAwesome Regular"
msgstr "FontAwesome обычные"

#: framework/premium/static/js/typography/providers/kadence.js:21
#: framework/premium/static/js/typography/providers/plus-addons.js:23
#: framework/premium/static/js/typography/providers/stackable.js:23
msgid "%s Local Google Fonts"
msgstr "Локальные Google-шрифты %s"

#: framework/premium/static/js/typography/providers/kadence.js:26
#: framework/premium/static/js/typography/providers/plus-addons.js:27
#: framework/premium/static/js/typography/providers/stackable.js:27
msgid "%s Typekit"
msgstr "Typekit %s"

#: framework/premium/static/js/typography/providers/kadence.js:31
#: framework/premium/static/js/typography/providers/stackable.js:31
msgid "%s Custom Fonts"
msgstr "Custom Fonts %s"

#: framework/premium/static/js/typography/providers/kadence.js:59
msgid "Normal"
msgstr "Обычный"

#: framework/premium/static/js/typography/providers/kadence.js:83
msgid "Inherit"
msgstr "Наследовать"

#: framework/premium/static/js/typography/providers/plus-addons.js:31
msgid "%s Custom"
msgstr "Кастом %s"

#: framework/premium/static/js/typography/providers/plus-addons.js:35
msgid "%s System"
msgstr "Система %s"

#: framework/premium/features/premium-header.php:22,
#: framework/premium/features/premium-header.php:58
msgid "Mobile Menu 1"
msgstr "Мобильное меню 1"

#: framework/premium/features/premium-header.php:59,
#: framework/premium/features/premium-header/items/mobile-menu-secondary/config.php:4
msgid "Mobile Menu 2"
msgstr "Мобильное меню 2"

#: framework/extensions/newsletter-subscribe/providers/active-campaign.php:154,
#: framework/extensions/newsletter-subscribe/providers/brevo.php:116,
#: framework/extensions/newsletter-subscribe/providers/campaign-monitor.php:136,
#: framework/extensions/newsletter-subscribe/providers/convertkit.php:97,
#: framework/extensions/newsletter-subscribe/providers/demo.php:40,
#: framework/extensions/newsletter-subscribe/providers/mailerlite-classic.php:104,
#: framework/extensions/newsletter-subscribe/providers/mailerlite-new.php:123,
#: framework/extensions/newsletter-subscribe/providers/mailpoet.php:93
msgid "Thank you for subscribing to our newsletter!"
msgstr "Благодарим за подписку на нашу рассылку!"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:59
msgctxt "Extension Brand Name"
msgid "Adobe Fonts"
msgstr "Шрифты Adobe"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:70,
#: framework/premium/extensions/code-snippets/extension.php:24,
#: framework/premium/extensions/code-snippets/extension.php:79,
#: framework/premium/extensions/code-snippets/extension.php:122
msgctxt "Extension Brand Name"
msgid "Custom Code Snippets"
msgstr "Произвольный код"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:96
msgctxt "Extension Brand Name"
msgid "Custom Fonts"
msgstr "Произвольные шрифты"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:108
msgctxt "Extension Brand Name"
msgid "Local Google Fonts"
msgstr "Локальные шрифты Google"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:123
msgctxt "Extension Brand Name"
msgid "Advanced Menu"
msgstr "Расширенное меню"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:136
msgctxt "Extension Brand Name"
msgid "Post Types Extra"
msgstr "Дополнительные типы записей"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:179,
#: framework/premium/extensions/shortcuts/config.php:5,
#: framework/premium/extensions/shortcuts/customizer.php:751
msgctxt "Extension Brand Name"
msgid "Shortcuts Bar"
msgstr "Мобильная панель кнопок"

#: framework/premium/extensions/shortcuts/customizer.php:314
msgid "Set link to nofollow"
msgstr "Установить ссылкам nofollow"

#: framework/premium/extensions/shortcuts/customizer.php:320
msgid "Custom class"
msgstr "Произвольный класс"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:194
msgctxt "Extension Brand Name"
msgid "Multiple Sidebars"
msgstr "Несколько сайдбаров"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:214
msgctxt "Extension Brand Name"
msgid "White Label"
msgstr "Белая этикетка"

#: framework/features/blocks/share-box/options.php:132,
#: framework/features/blocks/socials/options.php:84
msgid "Icons Spacing"
msgstr "Отступ значков"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/feature.php:157
msgid "Add widgets here."
msgstr "Добавляйте виджеты сюда."

#. translators: 1: link open 2: link close
#: framework/features/account-auth.php:106
msgid "Check your email for the confirmation link, then visit the %slogin page%s."
msgstr "Проверьте свою электронную почту на наличие ссылки для подтверждения, а затем перейдите на %sстраницу входа%s."

#: framework/features/account-auth.php:226
msgid "Your account was created successfully. Your login details have been sent to your email address. Please visit the %1$slogin page%2$s."
msgstr "Ваша учётная запись была успешно создана. Данные для входа отправлены на ваш адрес электронной почты. Перейдите на %1$sстраницу входа%2$s."

#: framework/features/account-auth.php:238
msgid "Your account was created successfully and a password has been sent to your email address. Please visit the %1$slogin page%2$s."
msgstr "Ваша учётная запись была успешно создана, и пароль был отправлен на ваш адрес электронной почты. Пожалуйста, перейдите на %1$sстраницу входа%2$s."

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/cookies-consent/config.php:5,
#: framework/extensions/cookies-consent/customizer.php:5
msgctxt "Extension Brand Name"
msgid "Cookies Consent"
msgstr "Согласие на cookie"

#: framework/extensions/cookies-consent/customizer.php:76
msgid "Accept Button text"
msgstr "Текст кнопки подтверждения"

#: framework/extensions/cookies-consent/customizer.php:85
msgid "Decline Button text"
msgstr "Текст кнопки отказа"

#: framework/extensions/cookies-consent/customizer.php:88,
#: framework/extensions/cookies-consent/helpers.php:11
msgid "Decline"
msgstr "Отклоняю"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/newsletter-subscribe/config.php:5
msgctxt "Extension Brand Name"
msgid "Newsletter Subscribe"
msgstr "Подписка на рассылку"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/product-reviews/config.php:5
msgctxt "Extension Brand Name"
msgid "Product Reviews"
msgstr "Обзоры товаров"

#: framework/extensions/product-reviews/metabox.php:60
msgid "Please note that some of this information (price, sku, brand) won't be displayed on the front-end. It is solely used for Google's Schema.org markup."
msgstr "Часть этой информации (цена, артикул, бренд) не будет отображаться во внешнем интерфейсе. Он используется исключительно для разметки Google Schema.org."

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/trending/config.php:5,
#: framework/extensions/trending/customizer.php:160
msgctxt "Extension Brand Name"
msgid "Trending Posts"
msgstr "Популярные записи"

#: framework/extensions/trending/customizer.php:477,
#: framework/features/blocks/about-me/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:32,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:133,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:73
msgid "Image Size"
msgstr "Размер изображения"

#: framework/features/header/account-modal.php:25
msgid "Close account modal"
msgstr "Закрыть модальное окно профиля"

#: framework/features/header/header-options.php:68
msgid "Effect"
msgstr "Эффект"

#: framework/features/header/header-options.php:84
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:73
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:68
msgid "Offset"
msgstr "Смещение"

#: framework/premium/features/content-blocks/admin-ui.php:275
msgid "All types"
msgstr "Все типы"

#: framework/premium/features/content-blocks/admin-ui.php:378,
#: framework/premium/features/content-blocks/options/popup.php:82
msgid "After x pages"
msgstr "После X страниц"

#: framework/premium/features/content-blocks/popups.php:376
msgid "Close popup"
msgstr "Закрыть модальное окно"

#: framework/premium/features/media-video/options.php:13
msgid "Upload"
msgstr "Загрузить"

#: framework/premium/static/js/header/CreateHeader.js:117
msgid "Picker header"
msgstr "Заголовок подборщика"

#: framework/premium/static/js/header/CreateHeader.js:134
#: static/js/header/PanelsManager.js:58
msgid "Global Header"
msgstr "Глобальная шапка"

#: framework/premium/static/js/header/CreateHeader.js:173
msgid "Create New Header"
msgstr "Создать новую шапку"

#: framework/premium/static/js/header/CreateHeader.js:50
msgid "Create new header"
msgstr "Создать новую шапку"

#: framework/premium/static/js/header/CreateHeader.js:53
msgid "Create a new header and assign it to different pages or posts based on your conditions."
msgstr "Создайте новую шапку и назначьте её различным страницам или записям."

#: framework/premium/static/js/header/CreateHeader.js:71
msgid "Header name"
msgstr "Название шапки"

#: framework/premium/static/js/hooks/CodeEditor.js:238
msgid "Yes, continue"
msgstr "Да, продолжить"

#: framework/premium/static/js/hooks/CodeEditor.js:150
msgid "Use code editor"
msgstr "Открыть редактор кода"

#: framework/premium/static/js/hooks/CodeEditor.js:153
msgid "Exit code editor"
msgstr "Закрыть редактор кода"

#: framework/premium/static/js/hooks/CodeEditor.js:165
msgid "Heads up!"
msgstr "Осторожно!"

#: framework/premium/static/js/hooks/CodeEditor.js:167
msgid "Enabling & disabling the code editor will erase everything from your post editor and this action is irreversible."
msgstr "Включение и отключение редактора кода приведёт к удалению всего текущего содержимого редактора записей. Действие необратимо!"

#: framework/premium/static/js/hooks/CodeEditor.js:174
msgid "Are you sure you want to continue?"
msgstr "Уверены, что хотите продолжать?"

#: framework/premium/static/js/hooks/CreateHook.js:223
msgid "Create Content Block"
msgstr "Создать блок содержимого"

#: framework/premium/static/js/hooks/CreateHook.js:36
msgid "Please select the type of your content block and place it in the location you want based on your display and user conditions."
msgstr "Выберите тип блока содержимого и разместите его в подходящем месте, в зависимости от особенностей экрана пользователя."

#: framework/premium/static/js/hooks/CreateHook.js:108
msgid "Select template type..."
msgstr "Выберите тип шаблона..."

#: framework/premium/features/content-blocks/admin-ui.php:262
#: framework/premium/static/js/hooks/CreateHook.js:68
msgid "Custom Content/Hooks"
msgstr "Произвольное содержимое/зацеп"

#: framework/premium/static/js/hooks/CreateHook.js:148
msgid "404 Page Template"
msgstr "Шаблон страницы 404"

#: framework/premium/static/js/hooks/CreateHook.js:132
msgid "Header Template"
msgstr "Шаблон шапки"

#: framework/premium/static/js/hooks/CreateHook.js:140
msgid "Footer Template"
msgstr "Шаблон подвала"

#: framework/premium/static/js/options/IconPicker.js:137
msgid "Change Icon"
msgstr "Изменить значок"

#: framework/premium/static/js/options/IconPicker.js:155
msgid "Remove Icon"
msgstr "Удалить значок"

#: framework/premium/static/js/options/IconPicker.js:161
msgid "Select"
msgstr "Выбрать"

#: framework/premium/static/js/options/IconPicker/Modal.js:148
msgid "Upload Icon"
msgstr "Загрузить значок"

#: framework/premium/static/js/options/IconPicker/CustomIcon.js:34
msgid "For performance and customization reasons, only SVG files are allowed."
msgstr "Ради производительности и настраиваемости разрешены только SVG-файлы."

#: framework/premium/static/js/options/IconPicker/IconsList.js:24
msgid "Search icon"
msgstr " Значок поиска"

#: framework/premium/static/js/options/MultipleLocationsSelect.js:151
msgid "Add New Location"
msgstr "Добавить новое положение"

#: framework/premium/static/js/options/MultipleLocationsSelect.js:53
msgid "Select location"
msgstr "Выберите место"

#: static/js/dashboard.js:64
msgid "Starter Sites"
msgstr "Стартовые сайты"

#: static/js/dashboard.js:76
msgid "Extensions"
msgstr "Расширения"

#: static/js/header/EditConditions.js:151
#: static/js/options/DisplayCondition.js:98
msgid "Save Conditions"
msgstr "Сохранить условия"

#: static/js/header/EditConditions.js:107
msgid "Add one or more conditions in order to display your header."
msgstr "Добавьте одно или несколько условий для отображения шапки сайта."

#: static/js/header/PanelsManager.js:174
msgid "Remove header"
msgstr "Удалить шапку"

#: static/js/header/PanelsManager.js:198
msgid "Remove Header"
msgstr "Удаление шапки"

#: static/js/header/PanelsManager.js:201
msgid "You are about to remove a custom header, are you sure you want to continue?"
msgstr "Вы собираетесь удалить настроенную шапку! Уверены, что хотите продолжить?"

#: static/js/dashboard/helpers/SubmitSupport.js:18
msgid "Need help or advice?"
msgstr "Нужна помощь или совет?"

#: static/js/dashboard/helpers/SubmitSupport.js:21
msgid "Got a question or need help with the theme? You can always submit a support ticket or ask for help in our friendly Facebook community."
msgstr "Есть вопрос или нужна помощь по теме? Вы всегда можете отправить заявку в службу поддержки или попросить о помощи в нашем дружелюбном сообществе на Facebook."

#: static/js/dashboard/helpers/SubmitSupport.js:33
msgid "Submit a Support Ticket"
msgstr "Запрос в службу поддержки"

#: static/js/dashboard/helpers/SubmitSupport.js:41
msgid "Join Facebook Community"
msgstr "Вступить в сообщество на Facebook"

#: static/js/dashboard/helpers/useUpsellModal.js:134
msgid "Upgrade Now"
msgstr "Апгрейдить сейчас"

#: static/js/options/ConditionsManager/SingleCondition.js:114
msgid "Select rule"
msgstr "Выберите правило"

#: static/js/options/ConditionsManager/SingleCondition.js:204
msgid "Select taxonomy"
msgstr "Выберите таксономию"

#: static/js/options/ConditionsManager/SingleCondition.js:236
msgid "Select language"
msgstr "Выберите язык"

#: static/js/options/ConditionsManager/SingleCondition.js:292
msgid "Select user"
msgstr "Выберите пользователя"

#: static/js/options/ConditionsManager/SingleCondition.js:265
msgid "Current user"
msgstr "Текущий пользователь"

#: static/js/options/DisplayCondition.js:28
msgid "Add Display Condition"
msgstr "Добавить условие отображения"

#: framework/features/conditions-manager.php:242,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:126
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:283
#: static/js/options/ConditionsManager/SingleCondition.js:97
msgid "Include"
msgstr "Включая"

#: framework/features/conditions-manager.php:242,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:127
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:291
#: static/js/options/ConditionsManager/SingleCondition.js:98
msgid "Exclude"
msgstr "Исключить"

#: framework/premium/static/js/options/PreviewedPostsSelect.js:170
#: static/js/options/ConditionsManager/PostIdPicker.js:68
msgid "Type to search by ID or title..."
msgstr "Печатайте для поиска по ID или названию..."

#: framework/premium/static/js/options/PreviewedPostsSelect.js:174
#: static/js/options/ConditionsManager/PostIdPicker.js:74
msgid "Select post"
msgstr "Выберите запись"

#: static/js/options/ConditionsManager/PostIdPicker.js:76
msgid "Select page"
msgstr "Выберите страницу"

#: static/js/options/CustomizerOptionsManager.js:113
msgid "Import Options"
msgstr "Импорт настроек"

#: static/js/options/CustomizerOptionsManager.js:116
msgid "Easily import the theme customizer settings."
msgstr "Легко экспортируйте параметры настройщика темы."

#: static/js/options/CustomizerOptionsManager.js:140
msgid "Click or drop to upload a file..."
msgstr "Указать или перетащить файл..."

#: static/js/options/CustomizerOptionsManager.js:239
msgid "Import Customizations"
msgstr "Импорт настроек"

#: static/js/options/CustomizerOptionsManager.js:249
msgid "Copy Options"
msgstr "Копирование настроек"

#: static/js/options/CustomizerOptionsManager.js:252
msgid "Copy and import your customizations from parent or child theme."
msgstr "Копирование и импорт настроек из родительской или дочерней темы."

#: static/js/options/CustomizerOptionsManager.js:308
msgid "Copy From Parent Theme"
msgstr "Копировать из родительской темы"

#: static/js/options/CustomizerOptionsManager.js:314
msgid "Copy From Child Theme"
msgstr "Копировать из дочерней темы"

#: static/js/options/CustomizerOptionsManager.js:321
msgid "You are about to copy all the settings from your parent theme into the child theme. Are you sure you want to continue?"
msgstr "Вы собираетесь скопировать все настройки из родительской темы в дочернюю тему. Уверены что хотите продолжить?"

#: static/js/options/CustomizerOptionsManager.js:327
msgid "You are about to copy all the settings from your child theme into the parent theme. Are you sure you want to continue?"
msgstr "Вы собираетесь скопировать все настройки из дочерней темы в родительскую тему. Уверены что хотите продолжить?"

#: static/js/options/CustomizerOptionsManager.js:376
msgid "Yes, I am sure"
msgstr "Да, я уверен"

#: static/js/options/CustomizerOptionsManager.js:390
msgid "Export Settings"
msgstr "Параметры экспорта"

#: static/js/options/CustomizerOptionsManager.js:394
msgid "Choose what set of settings you want to export."
msgstr "Выберите, какой набор настроек вы хотите экспортировать."

#: static/js/options/CustomizerOptionsManager.js:439
msgid "Customizer settings"
msgstr "Опции настройщика"

#: static/js/options/CustomizerOptionsManager.js:443
msgid "Widgets settings"
msgstr "Настройки виджетов"

#: static/js/options/CustomizerOptionsManager.js:536
msgid "Export"
msgstr "Экспорт"

#: static/js/options/CustomizerOptionsManager.js:87
msgid "Export Options"
msgstr "Экспорт настроек"

#: static/js/options/CustomizerOptionsManager.js:90
msgid "Easily export the theme customizer settings."
msgstr "Легко экспортируйте параметры настройщика темы."

#: static/js/options/CustomizerOptionsManager.js:107
msgid "Export Customizations"
msgstr "Экспортировать настройки"

#: static/js/options/DisplayCondition.js:19
msgid "Transparent Header Display Conditions"
msgstr "Условия отображения прозрачной шапки"

#: static/js/options/DisplayCondition.js:23
msgid "Add one or more conditions to display the transparent header."
msgstr "Добавьте одно или несколько условий для отображения прозрачного заголовка."

#: static/js/dashboard/screens/DemoInstall.js:166
msgid "Loading Starter Sites..."
msgstr "Загрузка стартовых сайтов..."

#: static/js/dashboard/screens/DemoInstall/components/Progress.js:29
msgid "Installing"
msgstr "Установка"

#: static/js/dashboard/screens/DemoInstall/components/Progress.js:34
msgid "Please be patient and don't refresh this page, the import process may take a while, this also depends on your server."
msgstr "Наберитесь терпения и не обновляйте эту страницу, процесс импорта может занять некоторое время, это также зависит от вашего сервера."

#: static/js/dashboard/screens/DemoInstall/DemoToInstall.js:306
msgid "Back"
msgstr "Назад"

#: static/js/dashboard/screens/DemoInstall/DemoToInstall.js:346
msgid "Install"
msgstr "Установить"

#: static/js/dashboard/screens/DemoInstall/DemoToInstall.js:347
msgid "Next"
msgstr "Далее"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:182
msgid "Import"
msgstr "Импорт"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:127
msgid "Available for"
msgstr "Доступно для"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:166
msgid "Preview"
msgstr "Просмотр"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:181
msgid "Modify"
msgstr "Изменить"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:22
msgid "Starter Site Imported Successfully"
msgstr "Стартовый сайт успешно импортирован"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:25
msgid "Now you can view your website or start customizing it"
msgstr "Теперь вы можете просмотреть свой сайт или приступить к его настройке."

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:33
#: static/js/dashboard/screens/Extensions/CurrentExtension.js:342
msgid "Customize"
msgstr "Настроить"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:40
msgid "View site"
msgstr "Посмотреть сайт"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:12
msgid "copying child theme sources"
msgstr "копирование источников дочерней темы"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:16
msgid "activating child theme"
msgstr "активация дочерней темы"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:21
msgid "installing plugin %s"
msgstr "установка плагина %s"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:28
msgid "activating plugin %s"
msgstr "активация плагина %s"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:34
msgid "downloading demo widgets"
msgstr "загрузка демонстрационных виджетов"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:38
msgid "installing demo widgets"
msgstr "установка демонстрационных виджетов"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:42
msgid "downloading demo options"
msgstr "загрузка демонстрационных опций"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:46
msgid "importing images from customizer"
msgstr "импорт изображений из настройщика"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:50
msgid "import customizer options"
msgstr "импорт параметров настройщика"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:54
msgid "activating required extensions"
msgstr "активация необходимых расширений"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:58
msgid "removing previously installed posts"
msgstr "удаление ранее созданных записей"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:62
msgid "removing previously installed taxonomies"
msgstr "удаление ранее созданных таксономий"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:66
msgid "removing default WordPress pages"
msgstr "удаление стандартных страниц WordPress"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:70
msgid "resetting customizer options"
msgstr "сброс параметров настройщика"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:74
msgid "resetting widgets"
msgstr "сброс виджетов"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:89
msgid "users"
msgstr "пользователи"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:90
msgid "terms"
msgstr "термины"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:91
msgid "images"
msgstr "изображения"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:92
msgid "posts"
msgstr "записи"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:93
msgid "comments"
msgstr "комментарии"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:118
msgid "Child theme"
msgstr "Дочерняя тема"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:143
#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:70
msgid "Erase content"
msgstr "Стереть содержимое"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:185
msgid "Final touches"
msgstr "Последние штрихи"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:155
msgid "Import options"
msgstr "Импортировать настройки"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:168
msgid "Import widgets"
msgstr "Импорт виджетов"

#: static/js/dashboard/screens/DemoInstall/Installer/contentCalculation.js:9
msgid "Import content"
msgstr "Импорт содержимого"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:108
msgid "You already have a child theme properly installed and activated. Move on."
msgstr "У вас уже есть правильно установленная и активированная дочерняя тема. Двигайтесь дальше."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:116
msgid "Learn more about child themes"
msgstr "Подробнее о дочерней теме"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:70
msgid "We strongly recommend to install the child theme, this way you will have freedom to make changes without breaking the parent theme."
msgstr "Настоятельно рекомендуем установить дочернюю тему, с ней у вас появится возможность вносить изменения в код, не нарушая родительскую тему."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:77
msgid "We strongly recommend to activate the child theme, this way you will have freedom to make changes without breaking the parent theme."
msgstr "Настоятельно рекомендуем активировать дочернюю тему, с ней у вас появится возможность вносить изменения в код, не нарушая родительскую тему."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:97
msgid "Install Child Theme"
msgstr "Установка дочерней темы"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:98
msgid "Activate Child Theme"
msgstr "Активировать дочернюю тему"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:56
msgid "Import Content"
msgstr "Импорт содержимого"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:59
msgid "This will import posts, pages, comments, navigation menus, custom fields, terms and custom posts."
msgstr "Будут импортированы записи, страницы, комментарии, меню навигации, произвольные поля, термины и произвольные типы записей."

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:104
msgid "Clean Install"
msgstr "Чистая установка"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:106
msgid "This option will remove the previous imported content and will perform a fresh and clean install."
msgstr "Опция удалит импортированное ранее содержимое и выполнит новую чистую установку."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:147
msgid "This starter site is already installed"
msgstr "Этот стартовый сайт уже установлен"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:156
msgid "Starter Site Removed"
msgstr "Стартовый сайт удалён"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:165
msgid "Dismiss"
msgstr "Скрыть"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:174
msgid "What steps do you want to perform next?"
msgstr "К каким шагам хотите перейти?"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:191
msgid "Remove"
msgstr "Удалить"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:200
msgid "Reinstall"
msgstr "Переустановить"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:55
msgid "Deactivate demo plugins"
msgstr "Деактивировать демо-плагины"

#: static/js/dashboard/screens/DemoInstall/Wizzard/PickBuilder.js:62
msgid "Choose Page Builder"
msgstr "Выберите конструктор страниц"

#: static/js/dashboard/screens/DemoInstall/Wizzard/PickBuilder.js:65
msgid "This starter site can be imported and used with one of these page builders. Please select one in order to continue."
msgstr "Этот стартовый сайт можно импортировать и использовать с одним из этих конструкторов страниц. Выберите подходящий вариант, чтобы продолжить."

#: static/js/dashboard/screens/DemoInstall/Wizzard/Plugins.js:71
msgid "Install & Activate Plugins"
msgstr "Установка и активация плагинов"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Plugins.js:73
msgid "The following plugins are required for this starter site in order to work properly."
msgstr "Следующие плагины необходимы для для правильной работы этого стартового сайта."

#: static/js/dashboard/screens/Extension.js:95
#: static/js/dashboard/screens/Extensions.js:86
msgid "Loading Extensions Status..."
msgstr "Загрузка статусов расширений..."

#: static/js/dashboard/screens/Extensions/Sidebar.js:60
msgid "Free Extensions"
msgstr "Бесплатные расширения"

#: static/js/dashboard/screens/Extensions/Sidebar.js:68
msgid "Pro Extensions"
msgstr "Профессиональные расширения"

#: static/js/dashboard/screens/SiteExport.js:239
msgid "Builder"
msgstr "Конструктор"

#: static/js/dashboard/screens/SiteExport.js:311
msgid "Export site"
msgstr "Экспорт сайта"

#: framework/premium/extensions/mega-menu/extension.php:325
msgid "New"
msgstr "Нов."

#: framework/premium/extensions/mega-menu/options.php:16,
#: framework/premium/extensions/mega-menu/options.php:581
msgid "Mega Menu Settings"
msgstr "Настройки мега-меню"

#: framework/premium/extensions/mega-menu/options.php:28
msgid "Dropdown Width"
msgstr "Ширина выпадающего меню"

#: framework/premium/extensions/mega-menu/options.php:36,
#: framework/premium/extensions/mega-menu/options.php:49
msgid "Content Width"
msgstr "Ширина содержимого"

#: framework/premium/extensions/mega-menu/options.php:37,
#: framework/premium/extensions/mega-menu/options.php:58
msgid "Full Width"
msgstr "Полная ширина"

#: framework/premium/extensions/mega-menu/options.php:38,
#: framework/premium/features/content-blocks/options/archive.php:86
msgid "Custom Width"
msgstr "Произвольная ширина"

#: framework/premium/extensions/mega-menu/options.php:57
msgid "Default Width"
msgstr "Стандартная ширина"

#: framework/premium/extensions/mega-menu/options.php:86,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:178,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:197
msgid "Columns"
msgstr "Колонки"

#: framework/premium/extensions/mega-menu/options.php:333
msgid "Custom Content"
msgstr "Произвольное содержимое"

#: framework/premium/extensions/mega-menu/options.php:337
msgid "Content Type"
msgstr "Тип содержимого"

#: framework/premium/extensions/mega-menu/options.php:344
msgid "Default (Menu Item)"
msgstr "Стандартный (пункт меню)"

#: framework/premium/extensions/mega-menu/options.php:345
msgid "Custom Text"
msgstr "Произвольный текст"

#: framework/premium/extensions/mega-menu/options.php:432,
#: framework/premium/extensions/mega-menu/options.php:791
msgid "Item Label Settings"
msgstr "Настройки ярлыка пункта"

#: framework/premium/extensions/mega-menu/options.php:437
msgid "Item Label"
msgstr "Ярлык пункта"

#: framework/premium/extensions/mega-menu/options.php:448
msgid "Enabled"
msgstr "Влючено"

#: framework/premium/extensions/mega-menu/options.php:449,
#: framework/premium/features/content-blocks/options/404.php:111,
#: framework/premium/features/content-blocks/options/archive.php:188,
#: framework/premium/features/content-blocks/options/header.php:129,
#: framework/premium/features/content-blocks/options/hook.php:238,
#: framework/premium/features/content-blocks/options/maintenance.php:108,
#: framework/premium/features/content-blocks/options/nothing_found.php:129,
#: framework/premium/features/content-blocks/options/single.php:120
msgid "Disabled"
msgstr "Отключено"

#: framework/premium/extensions/mega-menu/options.php:450
msgid "Heading"
msgstr "Заголовок"

#: framework/premium/extensions/mega-menu/options.php:456
msgid "Label Link"
msgstr "Ссылка ярлыка"

#: framework/extensions/trending/customizer.php:496,
#: framework/premium/extensions/mega-menu/options.php:550,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:154,
#: framework/premium/features/premium-header/items/contacts/options.php:526,
#: framework/premium/features/premium-header/items/language-switcher/options.php:105,
#: framework/premium/features/premium-header/items/search-input/options.php:223
msgid "Vertical Alignment"
msgstr "Вертикальное выравнивание"

#: framework/features/header/header-options.php:209,
#: framework/features/header/header-options.php:236,
#: framework/features/header/header-options.php:251,
#: framework/features/header/header-options.php:266,
#: framework/premium/extensions/mega-menu/options.php:585,
#: framework/premium/extensions/shortcuts/customizer.php:1181,
#: framework/premium/extensions/shortcuts/customizer.php:1223,
#: framework/premium/extensions/shortcuts/customizer.php:1265,
#: framework/features/header/items/account/options.php:1733,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:37,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:77,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:325,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:196,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:541,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:577,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:611,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:480,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:512,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:542
msgid "Background"
msgstr "Фон"

#: framework/premium/extensions/mega-menu/options.php:599
msgid "Link Color"
msgstr "Цвет ссылки"

#: framework/premium/extensions/mega-menu/options.php:619,
#: framework/features/header/items/account/options.php:1933,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:634,
#: framework/premium/features/premium-header/items/contacts/options.php:641,
#: framework/premium/features/premium-header/items/contacts/options.php:681,
#: framework/premium/features/premium-header/items/contacts/options.php:720
#: static/js/editor/blocks/breadcrumbs/Edit.js:60
#: static/js/editor/blocks/contact-info/Edit.js:114
msgid "Link Initial"
msgstr "Ссылка исходная"

#: framework/premium/extensions/mega-menu/options.php:624
msgid "Link Hover/Active"
msgstr "Ссылка при наведении/активная"

#: framework/premium/extensions/mega-menu/options.php:629,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:149
msgid "Background Hover"
msgstr "Фон при наведении"

#: framework/premium/extensions/mega-menu/options.php:646,
#: framework/premium/extensions/mega-menu/options.php:803
msgid "Heading Color"
msgstr "Цвет заголовка"

#: framework/premium/extensions/mega-menu/options.php:658,
#: framework/premium/extensions/mega-menu/options.php:677,
#: framework/premium/extensions/mega-menu/options.php:815
msgid "Initial Color"
msgstr "Исходный цвет"

#: framework/extensions/cookies-consent/customizer.php:147,
#: framework/extensions/newsletter-subscribe/customizer.php:196,
#: framework/premium/extensions/mega-menu/options.php:665,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:342
#: static/js/editor/blocks/about-me/Edit.js:89
#: static/js/editor/blocks/breadcrumbs/Edit.js:43
#: static/js/editor/blocks/contact-info/Edit.js:96
msgid "Text Color"
msgstr "Цвет текста"

#: framework/premium/extensions/mega-menu/options.php:684,
#: framework/premium/extensions/shortcuts/customizer.php:1282,
#: framework/premium/features/premium-header/items/search-input/options.php:904,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:161
msgid "Items Divider"
msgstr "Разделитель элементов"

#: framework/premium/extensions/mega-menu/options.php:699
msgid "Columns Divider"
msgstr "Разделитель колонок"

#: framework/premium/extensions/mega-menu/options.php:714,
#: framework/premium/features/premium-header/items/search-input/options.php:886
msgid "Dropdown Shadow"
msgstr "Тень выпадающего списка"

#: framework/premium/extensions/mega-menu/options.php:746
msgid "Column Settings"
msgstr "Настройки колонки"

#: framework/premium/extensions/mega-menu/options.php:750
msgid "Column Spacing"
msgstr "Отступ колонки"

#: framework/premium/extensions/mega-menu/options.php:825,
#: framework/features/header/items/account/options.php:1327,
#: framework/features/header/items/account/options.php:1366,
#: framework/features/header/items/account/options.php:1409,
#: framework/features/header/items/account/options.php:1450,
#: framework/features/header/items/account/options.php:1738,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:300,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:328,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:359,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:388,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:331,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:213,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:353,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:382,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:413,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:442,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:354,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:382,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:417,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:450,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:308,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:336,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:367,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:396
#: static/js/editor/blocks/share-box/Edit.js:81
#: static/js/editor/blocks/socials/Edit.js:81
msgid "Icon Color"
msgstr "Цвет значка"

#: framework/helpers/exts-configs.php:137
msgid "Enables support for Custom Fields inside archive cards and single page post title, adds a reading progress bar for your posts and lets you set featured images and colors for your categories archives."
msgstr "Включает поддержку настраиваемых полей внутри карточек архива и заголовка отельной страницы записи, добавляет индикатор прогресса чтения для записей и позволяет устанавливать изображения и цвета для архивов категорий."

#: framework/helpers/exts-configs.php:180,
#: framework/premium/extensions/shortcuts/config.php:6
msgid "Easily turn your websites into mobile first experiences. You can easily add the most important actions at the bottom of the screen for easy access."
msgstr "Легко превратите свой сайт в адаптированный для мобильных устройств. Выведите органы наиболее важных действий в нижнюю часть экрана для быстрого доступа."

#: framework/premium/extensions/shortcuts/customizer.php:12,
#: framework/premium/extensions/shortcuts/customizer.php:38,
#: framework/premium/extensions/shortcuts/customizer.php:790,
#: framework/premium/extensions/shortcuts/extension.php:173,
#: framework/premium/extensions/shortcuts/views/bar.php:13,
#: framework/premium/extensions/shortcuts/views/bar.php:46
msgid "Home"
msgstr "Главная"

#: framework/features/blocks/contact-info/options.php:139,
#: framework/premium/extensions/shortcuts/customizer.php:71,
#: framework/premium/extensions/shortcuts/customizer.php:97,
#: framework/premium/extensions/shortcuts/customizer.php:799,
#: framework/premium/extensions/shortcuts/extension.php:182,
#: framework/premium/extensions/shortcuts/views/bar.php:22,
#: framework/premium/extensions/shortcuts/views/bar.php:47,
#: framework/premium/features/premium-header/items/contacts/options.php:127
msgid "Phone"
msgstr "Телефон"

#: framework/premium/extensions/shortcuts/customizer.php:201,
#: framework/premium/extensions/shortcuts/customizer.php:227,
#: framework/premium/extensions/shortcuts/views/bar.php:49
msgid "Scroll Top"
msgstr "Прокрутка вверх"

#: framework/premium/extensions/shortcuts/customizer.php:352,
#: framework/premium/extensions/shortcuts/customizer.php:378,
#: framework/premium/extensions/shortcuts/views/bar.php:50
msgid "Cart"
msgstr "Корзина"

#: framework/premium/extensions/shortcuts/customizer.php:411,
#: framework/premium/extensions/shortcuts/customizer.php:437
msgid "Shop"
msgstr "Магазин"

#: framework/helpers/exts-configs.php:262,
#: framework/premium/extensions/shortcuts/customizer.php:545,
#: framework/premium/extensions/shortcuts/customizer.php:571,
#: framework/premium/extensions/shortcuts/views/bar.php:52,
#: framework/features/header/items/account/views/login.php:520,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:309,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:406,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:410,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:438,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:442,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/helpers.php:168,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:17,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/config.php:4,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:141,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/view.php:100
msgid "Wishlist"
msgstr "Избранное"

#: framework/premium/extensions/shortcuts/customizer.php:784
msgid "Shortcuts"
msgstr "Ярлыки"

#: framework/premium/extensions/shortcuts/customizer.php:839,
#: framework/features/header/items/account/options.php:520,
#: framework/features/header/items/account/options.php:983,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:38,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:91,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:92
msgid "Label Visibility"
msgstr "Видимость подписи"

#: framework/premium/extensions/shortcuts/customizer.php:879,
#: framework/features/header/items/account/options.php:554,
#: framework/features/header/items/account/options.php:1017,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:88,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:122,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:123
msgid "Label Position"
msgstr "Положение ярлыка"

#: framework/premium/extensions/shortcuts/customizer.php:888,
#: framework/premium/features/content-blocks/hooks-manager.php:477,
#: framework/features/header/items/account/options.php:563,
#: framework/features/header/items/account/options.php:1035,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:97,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:21,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:131,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:132
msgid "Bottom"
msgstr "Внизу"

#: framework/premium/extensions/mega-menu/options.php:482,
#: framework/premium/extensions/shortcuts/customizer.php:925,
#: framework/features/header/items/account/options.php:508,
#: framework/features/header/items/account/options.php:882,
#: framework/features/header/items/account/options.php:968,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:46,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:439,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:202,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:42,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:73,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:74
msgid "Icon Size"
msgstr "Размер значка"

#: framework/premium/extensions/shortcuts/customizer.php:936,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:383
msgid "Container Height"
msgstr "Высота контейнера"

#: framework/premium/extensions/shortcuts/customizer.php:951
msgid "Container Max Width"
msgstr "Макс. ширина контейнера"

#: framework/premium/extensions/shortcuts/customizer.php:975
msgid "Scroll Interaction"
msgstr "Реакция на прокрутку"

#: framework/premium/extensions/shortcuts/customizer.php:981
msgid "Hide"
msgstr "Скрывать"

#: framework/premium/extensions/shortcuts/customizer.php:1023
msgid "Shortcuts Bar Display Conditions"
msgstr "Условия отображения панели ярлыков"

#: framework/premium/extensions/shortcuts/customizer.php:1024
msgid "Add one or more conditions to display the shortcuts bar."
msgstr " Добавьте одно или несколько условий для отображения панели ярлыков."

#: framework/premium/extensions/shortcuts/customizer.php:1048,
#: framework/features/header/items/account/options.php:1895,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:184,
#: framework/premium/features/premium-header/items/contacts/options.php:577,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:105,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:212,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:42
msgid "Font"
msgstr "Шрифт"

#: framework/features/blocks/about-me/options.php:187,
#: framework/features/blocks/share-box/options.php:141,
#: framework/features/blocks/socials/options.php:93,
#: framework/premium/extensions/shortcuts/customizer.php:1092,
#: framework/premium/features/premium-header/items/contacts/options.php:751,
#: framework/premium/features/premium-header/items/contacts/options.php:780,
#: framework/premium/features/premium-header/items/contacts/options.php:811,
#: framework/premium/features/premium-header/items/contacts/options.php:840
#: static/js/editor/blocks/about-me/Edit.js:117
#: static/js/editor/blocks/contact-info/Edit.js:133
msgid "Icons Color"
msgstr "Цвет значков"

#: framework/premium/extensions/shortcuts/customizer.php:1163
msgid "Cart Badge Color"
msgstr "Цвет метки корзины"

#: framework/premium/extensions/shortcuts/customizer.php:1302
msgid "Items Divider Height"
msgstr "Высота разделителя элементов"

#: framework/premium/extensions/shortcuts/customizer.php:1316,
#: framework/features/header/items/account/options.php:2016,
#: framework/premium/features/content-blocks/options/popup.php:545,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:701,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:150,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:175
msgid "Shadow"
msgstr "Тень"

#: framework/helpers/exts-configs.php:195
msgid "Create unlimited personalized sets of widget areas and display them on any page or post using our conditional logic functionality."
msgstr "Создавайте сколько угодно персонализированных наборов областей виджетов и отображайте их на любой странице или записи, используя функциональность условной логики."

#: framework/helpers/exts-configs.php:199
msgid "Create New Sidebar"
msgstr "Создать сайдбар"

#: framework/premium/extensions/sidebars/form.php:3
#: framework/premium/extensions/sidebars/static/js/main.js:50
msgid "Create Sidebar/Widget Area"
msgstr "Создать сайдбар/область виджетов"

#: framework/premium/extensions/sidebars/form.php:6
msgid "In order to create a new sidebar/widget area simply enter a name in the input below and click the Create Sidebar button."
msgstr "Чтобы создать новый сайдбар/область виджетов, просто введите название в поле ввода ниже и нажмите кнопку «Создать сайдбар»."

#: framework/premium/extensions/sidebars/form.php:16
#: framework/premium/extensions/sidebars/static/js/main.js:66
msgid "Create Sidebar"
msgstr "Создать сайдбар"

#: framework/premium/extensions/sidebars/form.php:20
msgid "Available Sidebars/Widget Areas"
msgstr "Доступные сайдбары/области виджетов"

#: framework/helpers/exts-configs.php:215
msgid "Replace Blocksy's branding with your own. Easily hide licensing info and other sections of the theme and companion plugin from your clients and make your final product look more professional."
msgstr "Заменяйте айдентику Blocksy на свою собственную. Легко скрывайте информацию о лицензировании и другие разделы темы и сопутствующего плагина от своих клиентов и делайте свой конечный продукт более профессиональным."

#: framework/helpers/exts-configs.php:228
msgid "Make the shopping experience better for your visitors! Add features such as Product Quick View, Wishlist functionality and a Floating Add to Cart button. Customize the single product gallery/slider and the layout."
msgstr "Сделайте процесс покупок лучше для посетителей своего сайта! Добавьте такие функции, как быстрый просмотр товара, раздел списка желаний и плавающая кнопка добавления в корзину. Настраивайте галерею/слайдер отдельного товара и его макет."

#: framework/premium/extensions/woocommerce-extra/features/quick-view/feature.php:14
msgid "Quick View Button"
msgstr "Кнопка быстрого просмотра"

#: framework/features/header/items/account/options.php:467,
#: framework/features/header/items/account/options.php:841,
#: framework/features/header/items/account/options.php:929,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/archive-card/feature.php:13,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:66,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:111,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:65,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:66
msgid "Type 3"
msgstr "Тип 3"

#: framework/features/header/items/account/options.php:477,
#: framework/features/header/items/account/options.php:851,
#: framework/features/header/items/account/options.php:939,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:71,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:116
msgid "Type 4"
msgstr "Тип 4"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/views/panel.php:61
msgid "Available Filters"
msgstr "Доступные фильтры"

#: framework/premium/extensions/woocommerce-extra/extension.php:128
msgid "Quick view title before"
msgstr "Перед заголовком быстрого просмотра"

#: framework/premium/extensions/woocommerce-extra/extension.php:133
msgid "Quick view title after"
msgstr "После заголовка быстрого просмотра"

#: framework/premium/extensions/woocommerce-extra/extension.php:138
msgid "Quick view price before"
msgstr "До цены быстрого просмотра"

#: framework/premium/extensions/woocommerce-extra/extension.php:143
msgid "Quick view price after"
msgstr "После цены быстрого просмотра"

#: framework/premium/extensions/woocommerce-extra/extension.php:148
msgid "Quick view summary before"
msgstr "До обзора быстрого просмотра"

#: framework/premium/extensions/woocommerce-extra/extension.php:153
msgid "Quick view summary after"
msgstr "После обзора быстрого просмотра"

#: framework/helpers/exts-configs.php:238,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:4
msgid "Floating Cart"
msgstr "Плавающая корзина"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:15
msgid "Position"
msgstr "Положение"

#: framework/premium/features/content-blocks/hooks-manager.php:439,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:20
msgid "Top"
msgstr "Верху"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:68
msgid "Product Title Visibility"
msgstr "Видимость заголовка товара"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:30
msgid "Floating Cart Visibility"
msgstr "Видимость плавающей корзины"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/view.php:77
msgid "Go to product page"
msgstr "Открыть страницу товара"

#: framework/premium/extensions/shortcuts/customizer.php:507,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/helpers.php:40,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:111
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:108
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:137
msgid "Filter"
msgstr "Фильтр"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:532
msgid "Filter Widgets"
msgstr "Виджеты фильтра"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:536
msgid "Widgets"
msgstr "Виджеты"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:580
msgid "Widgets Vertical Spacing"
msgstr "Вертикальный отступ виджетов"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:598
msgid "Widgets Font"
msgstr "Шрифт виджетов"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:606
msgid "Widgets Font Color"
msgstr "Цвет шрифта виджетов"

#: framework/features/header/items/account/options.php:1927,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:134,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:628,
#: framework/premium/features/premium-header/items/contacts/options.php:635,
#: framework/premium/features/premium-header/items/contacts/options.php:676,
#: framework/premium/features/premium-header/items/contacts/options.php:715
msgid "Text Initial"
msgstr "Исходный текст"

#: framework/features/header/items/account/options.php:1939,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:639,
#: framework/premium/features/premium-header/items/contacts/options.php:647,
#: framework/premium/features/premium-header/items/contacts/options.php:687,
#: framework/premium/features/premium-header/items/contacts/options.php:726
#: static/js/editor/blocks/breadcrumbs/Edit.js:66
#: static/js/editor/blocks/contact-info/Edit.js:123
msgid "Link Hover"
msgstr "Ссылка при наведении"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:123
msgid "Panel Reveal"
msgstr "Раскрытие панели"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:131,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:39
msgid "Left Side"
msgstr "Слева"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:132,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:40
msgid "Right Side"
msgstr "Справа"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:137,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:45
msgid "Panel Width"
msgstr "Ширина панели"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:295,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:163
msgid "Panel Shadow"
msgstr "Тень панели"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:263,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:76
msgid "Panel Background"
msgstr "Фон панели"

#: framework/premium/features/content-blocks/options/popup.php:615,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:779
msgid "Close Icon Color"
msgstr "Цвет значка закрытия"

#: framework/premium/features/content-blocks/options/popup.php:646
msgid "Close Icon Background"
msgstr "Фон значка закрытия"

#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:175
#: static/js/editor/blocks/share-box/index.js:47
msgid "Share Box"
msgstr "Блок отправки в соцсети"

#: framework/premium/features/content-blocks/hooks-manager.php:11
msgid "WP head"
msgstr "Хэдер WP"

#: framework/premium/features/content-blocks/hooks-manager.php:13,
#: framework/premium/features/content-blocks/hooks-manager.php:22,
#: framework/premium/features/content-blocks/hooks-manager.php:32,
#: framework/premium/features/content-blocks/hooks-manager.php:41
msgid "Head"
msgstr "Хэдер"

#: framework/premium/features/content-blocks/hooks-manager.php:20
msgid "WP head start"
msgstr "Начало хэдера WP"

#: framework/premium/features/content-blocks/hooks-manager.php:30
msgid "WP head end"
msgstr "Конец хэдера WP"

#: framework/premium/features/content-blocks/hooks-manager.php:48
msgid "Header before"
msgstr "Перед хэдером"

#: framework/premium/features/content-blocks/admin-ui.php:265,
#: framework/premium/features/content-blocks/hooks-manager.php:50,
#: framework/premium/features/content-blocks/hooks-manager.php:59
msgid "Header"
msgstr "Хэдер"

#: framework/premium/features/content-blocks/hooks-manager.php:57
msgid "Header after"
msgstr "После хэдера"

#: framework/premium/features/content-blocks/hooks-manager.php:66
msgid "Desktop top"
msgstr "Верх десктопа"

#: framework/premium/features/content-blocks/hooks-manager.php:68,
#: framework/premium/features/content-blocks/hooks-manager.php:77,
#: framework/premium/features/content-blocks/hooks-manager.php:86,
#: framework/premium/features/content-blocks/hooks-manager.php:95
msgid "Header offcanvas"
msgstr "Плавающий блок шапки"

#: framework/premium/features/content-blocks/hooks-manager.php:75
msgid "Desktop bottom"
msgstr "Низ десктопа"

#: framework/premium/features/content-blocks/hooks-manager.php:84
msgid "Mobile top"
msgstr "Верх мобильного"

#: framework/premium/features/content-blocks/hooks-manager.php:93
msgid "Mobile bottom"
msgstr "Низ мобильного"

#: framework/premium/features/content-blocks/hooks-manager.php:102
msgid "Sidebar before"
msgstr "До сайдбара"

#: framework/premium/features/content-blocks/hooks-manager.php:103,
#: framework/premium/features/content-blocks/hooks-manager.php:110,
#: framework/premium/features/content-blocks/hooks-manager.php:117,
#: framework/premium/features/content-blocks/hooks-manager.php:124
msgid "Left/Right sidebar"
msgstr "Левый/правый сайдбар"

#: framework/premium/features/content-blocks/hooks-manager.php:109
msgid "Sidebar start"
msgstr "Начало сайдбара"

#: framework/premium/features/content-blocks/hooks-manager.php:116
msgid "Sidebar end"
msgstr "Конец сайдбара"

#: framework/premium/features/content-blocks/hooks-manager.php:123
msgid "Sidebar after"
msgstr "После сайдбара"

#: framework/premium/features/content-blocks/hooks-manager.php:130
msgid "Dynamic sidebar before"
msgstr "До динамического сайдбара"

#: framework/premium/features/content-blocks/hooks-manager.php:131,
#: framework/premium/features/content-blocks/hooks-manager.php:138,
#: framework/premium/features/content-blocks/hooks-manager.php:146
msgid "All widget areas"
msgstr "Все области виджетов"

#: framework/premium/features/content-blocks/hooks-manager.php:137
msgid "Dynamic sidebar"
msgstr "Динамический сайдбар"

#: framework/premium/features/content-blocks/hooks-manager.php:145
msgid "Dynamic sidebar after"
msgstr "После динамического сайдбара"

#: framework/premium/features/content-blocks/hooks-manager.php:154
msgid "Before section"
msgstr "После раздела"

#: framework/premium/features/content-blocks/hooks-manager.php:155,
#: framework/premium/features/content-blocks/hooks-manager.php:163,
#: framework/premium/features/content-blocks/hooks-manager.php:171,
#: framework/premium/features/content-blocks/hooks-manager.php:179,
#: framework/premium/features/content-blocks/hooks-manager.php:187,
#: framework/premium/features/content-blocks/hooks-manager.php:195,
#: framework/premium/features/content-blocks/hooks-manager.php:203,
#: framework/premium/features/content-blocks/hooks-manager.php:211,
#: framework/premium/features/content-blocks/hooks-manager.php:219,
#: framework/premium/features/content-blocks/hooks-manager.php:227,
#: framework/premium/features/content-blocks/hooks-manager.php:235,
#: framework/premium/features/content-blocks/hooks-manager.php:243,
#: framework/premium/features/content-blocks/hooks-manager.php:251,
#: framework/premium/features/content-blocks/hooks-manager.php:259
msgid "Page/post title"
msgstr "Заголовок страницы/записи"

#: framework/premium/features/content-blocks/hooks-manager.php:162,
#: framework/premium/features/content-blocks/hooks-manager.php:312,
#: framework/premium/features/content-blocks/hooks-manager.php:354
msgid "Before title"
msgstr "Перед заголовком"

#: framework/premium/features/content-blocks/hooks-manager.php:186
msgid "Before post meta"
msgstr "До метаданных записи"

#: framework/premium/features/content-blocks/hooks-manager.php:210,
#: framework/premium/features/content-blocks/hooks-manager.php:319,
#: framework/premium/features/content-blocks/hooks-manager.php:361
msgid "After title"
msgstr "После заголовка"

#: framework/premium/features/content-blocks/hooks-manager.php:250
msgid "After post meta"
msgstr "После метаданных записи"

#: framework/premium/features/content-blocks/hooks-manager.php:258
msgid "After section"
msgstr "После раздела"

#: framework/premium/features/content-blocks/hooks-manager.php:266
msgid "Before content"
msgstr "Перед содержимым"

#: framework/premium/features/content-blocks/hooks-manager.php:274,
#: framework/premium/features/content-blocks/hooks-manager.php:447
msgid "Top content"
msgstr "Верх содержимого"

#: framework/premium/features/content-blocks/hooks-manager.php:282,
#: framework/premium/features/content-blocks/hooks-manager.php:469
msgid "Bottom content"
msgstr "Низ содержимого"

#: framework/premium/features/content-blocks/hooks-manager.php:290
msgid "After content"
msgstr "После содержимого"

#: framework/premium/features/content-blocks/hooks-manager.php:298
msgid "Before comments"
msgstr "Перед комментами"

#: framework/premium/features/content-blocks/hooks-manager.php:299,
#: framework/premium/features/content-blocks/hooks-manager.php:306,
#: framework/premium/features/content-blocks/hooks-manager.php:313,
#: framework/premium/features/content-blocks/hooks-manager.php:320,
#: framework/premium/features/content-blocks/hooks-manager.php:327,
#: framework/premium/features/content-blocks/hooks-manager.php:334
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:60
msgid "Comments"
msgstr "Комменты"

#: framework/premium/features/content-blocks/hooks-manager.php:305
msgid "Top comments"
msgstr "Верх комментов"

#: framework/premium/features/content-blocks/hooks-manager.php:326
msgid "Bottom comments"
msgstr "Низ комментов"

#: framework/premium/features/content-blocks/hooks-manager.php:333
msgid "After comments"
msgstr "После комментов"

#: framework/premium/features/content-blocks/hooks-manager.php:411,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:705
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:298
msgid "Before"
msgstr "Перед"

#: framework/premium/features/content-blocks/hooks-manager.php:412,
#: framework/premium/features/content-blocks/hooks-manager.php:419
msgid "Loop"
msgstr "Цикл"

#: framework/premium/features/content-blocks/hooks-manager.php:418,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:716
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:307
msgid "After"
msgstr "После"

#: framework/premium/features/content-blocks/hooks-manager.php:425
msgid "Start"
msgstr "Начало"

#: framework/premium/features/content-blocks/hooks-manager.php:426,
#: framework/premium/features/content-blocks/hooks-manager.php:433
msgid "Loop card"
msgstr "Карточка цикла"

#: framework/premium/features/content-blocks/hooks-manager.php:432
msgid "End"
msgstr "Конец"

#: framework/premium/features/content-blocks/hooks-manager.php:455
msgid "After certain number of blocks"
msgstr "После определённого количества блоков"

#: framework/premium/features/content-blocks/hooks-manager.php:462
msgid "Before certain number of headings"
msgstr "После определённого количества заголовков"

#: framework/premium/features/content-blocks/hooks-manager.php:485
msgid "Login form start"
msgstr "Начало формы входа"

#: framework/premium/features/content-blocks/hooks-manager.php:486,
#: framework/premium/features/content-blocks/hooks-manager.php:493,
#: framework/premium/features/content-blocks/hooks-manager.php:500,
#: framework/premium/features/content-blocks/hooks-manager.php:507,
#: framework/premium/features/content-blocks/hooks-manager.php:514,
#: framework/premium/features/content-blocks/hooks-manager.php:521,
#: framework/premium/features/content-blocks/hooks-manager.php:528,
#: framework/premium/features/content-blocks/hooks-manager.php:535,
#: framework/premium/features/content-blocks/hooks-manager.php:542,
#: framework/premium/features/content-blocks/hooks-manager.php:549
msgid "Auth forms"
msgstr "Формы входа"

#: framework/premium/features/content-blocks/hooks-manager.php:492
msgid "Login form end"
msgstr "Конец формы входа"

#: framework/premium/features/content-blocks/hooks-manager.php:499
msgid "Login form modal start"
msgstr "Начало модальной формы входа"

#: framework/premium/features/content-blocks/hooks-manager.php:506
msgid "Login form modal end"
msgstr "Конец модальной формы входа"

#: framework/premium/features/content-blocks/hooks-manager.php:513
msgid "Register form start"
msgstr "Начало формы регистрации"

#: framework/premium/features/content-blocks/hooks-manager.php:520
msgid "Register form end"
msgstr "Конец формы регистрации"

#: framework/premium/features/content-blocks/hooks-manager.php:527
msgid "Register form modal start"
msgstr "Начало модальной формы регистрации"

#: framework/premium/features/content-blocks/hooks-manager.php:534
msgid "Register form modal end"
msgstr "Конец модальной формы регистрации"

#: framework/premium/features/content-blocks/hooks-manager.php:541
msgid "Lost password form modal start"
msgstr "Начало модальной формы восстановления"

#: framework/premium/features/content-blocks/hooks-manager.php:548
msgid "Lost password form modal end"
msgstr "Конец модальной формы восстановления"

#: framework/premium/features/content-blocks/hooks-manager.php:556
msgid "Before main content"
msgstr "До основного содержимого"

#: framework/premium/features/content-blocks/hooks-manager.php:562
msgid "After main content"
msgstr "После основного содержимого"

#: framework/premium/features/content-blocks/hooks-manager.php:583
msgid "WooCommerce Global"
msgstr "Весь WooCommerce"

#: framework/premium/features/content-blocks/hooks-manager.php:588
msgid "Archive description"
msgstr "Описание архива"

#: framework/premium/features/content-blocks/hooks-manager.php:593
msgid "Before shop loop"
msgstr "До цикла магазина"

#: framework/premium/features/content-blocks/hooks-manager.php:610
msgid "Before shop loop item title"
msgstr "Перед заголовком товара цикла магазина"

#: framework/premium/features/content-blocks/hooks-manager.php:615
msgid "After shop loop item title"
msgstr "После заголовка товара цикла магазина"

#: framework/premium/features/content-blocks/hooks-manager.php:620
msgid "Before shop loop item price"
msgstr "Перед ценой товара цикла магазина"

#: framework/premium/features/content-blocks/hooks-manager.php:625
msgid "After shop loop item price"
msgstr "После цены товара цикла магазина"

#: framework/premium/features/content-blocks/hooks-manager.php:640
msgid "After shop loop"
msgstr "После цикла магазина"

#: framework/premium/features/content-blocks/hooks-manager.php:642
msgid "WooCommerce Archive"
msgstr "Архив WooCommerce"

#: framework/premium/features/content-blocks/hooks-manager.php:647
msgid "Before single product"
msgstr "До отдельного товара"

#: framework/premium/features/content-blocks/hooks-manager.php:665
msgid "Product meta start"
msgstr "Начало метаданных товара"

#: framework/premium/features/content-blocks/hooks-manager.php:669
msgid "Product meta end"
msgstr "Конец метаданных товара"

#: framework/premium/features/content-blocks/hooks-manager.php:673
msgid "Share"
msgstr "Отправка в соцсети"

#: framework/premium/features/content-blocks/hooks-manager.php:677
msgid "After single product"
msgstr "После отдельного товара"

#: framework/premium/features/content-blocks/hooks-manager.php:683
msgid "Before single product excerpt"
msgstr "До отрывка отдельного товара"

#: framework/premium/features/content-blocks/hooks-manager.php:688
msgid "After single product excerpt"
msgstr "После отрывка отдельного товара"

#: framework/premium/features/content-blocks/hooks-manager.php:693
msgid "Before single product tabs"
msgstr "До вкладок отдельного товара"

#: framework/premium/features/content-blocks/hooks-manager.php:699
msgid "After single product tabs"
msgstr "После вкладок отдельного товара"

#: framework/premium/features/content-blocks/hooks-manager.php:738
msgid "Cart is empty"
msgstr "Корзина пуста"

#: framework/premium/features/content-blocks/hooks-manager.php:742
msgid "Before cart"
msgstr "До корзины"

#: framework/premium/features/content-blocks/hooks-manager.php:746
msgid "Before cart table"
msgstr "До таблицы корзины"

#: framework/premium/features/content-blocks/hooks-manager.php:750
msgid "Before cart contents"
msgstr "После содержимого корзины"

#: framework/premium/features/content-blocks/hooks-manager.php:754
msgid "Cart contents"
msgstr "Содержимое корзины"

#: framework/premium/features/content-blocks/hooks-manager.php:758
msgid "After cart contents"
msgstr "После содержимого корзины"

#: framework/premium/features/content-blocks/hooks-manager.php:762
msgid "Cart coupon"
msgstr "Купон корзины"

#: framework/premium/features/content-blocks/hooks-manager.php:766
msgid "Cart actions"
msgstr "Действия корзины"

#: framework/premium/features/content-blocks/hooks-manager.php:770
msgid "After cart table"
msgstr "После таблицы корзины"

#: framework/premium/features/content-blocks/hooks-manager.php:774
msgid "Cart collaterals"
msgstr "Collaterals корзины"

#: framework/premium/features/content-blocks/hooks-manager.php:778
msgid "Before cart totals"
msgstr "До итогов корзины"

#: framework/premium/features/content-blocks/hooks-manager.php:782
msgid "Cart totals before order total"
msgstr "Итоги корзины до итогов заказа"

#: framework/premium/features/content-blocks/hooks-manager.php:786
msgid "Cart totals after order total"
msgstr "Итоги корзины после итогов заказа"

#: framework/premium/features/content-blocks/hooks-manager.php:790
msgid "Proceed to checkout"
msgstr "Перейти к оформлению заказа"

#: framework/premium/features/content-blocks/hooks-manager.php:794
msgid "After cart totals"
msgstr "После итогов корзины"

#: framework/premium/features/content-blocks/hooks-manager.php:798
msgid "After cart"
msgstr "После корзины"

#: framework/premium/features/content-blocks/hooks-manager.php:803
msgid "Before Mini Cart"
msgstr "До мини-корзины"

#: framework/premium/features/content-blocks/hooks-manager.php:808
msgid "Before Mini Cart Contents"
msgstr "До содержимого мини-корзины"

#: framework/premium/features/content-blocks/hooks-manager.php:813
msgid "Mini Cart Contents"
msgstr "Содержимое мини-корзины"

#: framework/premium/features/content-blocks/hooks-manager.php:818
msgid "Widget Shopping Cart Before Buttons"
msgstr "Виджет корзины перед кнопками"

#: framework/premium/features/content-blocks/hooks-manager.php:823
msgid "Widget Shopping Cart After Buttons"
msgstr "Виджет корзины после кнопок"

#: framework/premium/features/content-blocks/hooks-manager.php:828
msgid "After Mini Cart"
msgstr "После мини-корзины"

#: framework/premium/features/content-blocks/hooks-manager.php:830
msgid "WooCommerce Cart"
msgstr "Корзина WooCommerce"

#: framework/premium/features/content-blocks/hooks-manager.php:836
msgid "Before checkout form"
msgstr "До формы оформления заказа"

#: framework/premium/features/content-blocks/hooks-manager.php:840
msgid "Before customer details"
msgstr "До сведений о клиенте"

#: framework/premium/features/content-blocks/hooks-manager.php:844
msgid "After customer details"
msgstr "После сведений о клиенте"

#: framework/premium/features/content-blocks/hooks-manager.php:848
msgid "Checkout billing"
msgstr "Выставление счёта"

#: framework/premium/features/content-blocks/hooks-manager.php:852
msgid "Before checkout billing form"
msgstr "До формы выставления счёта"

#: framework/premium/features/content-blocks/hooks-manager.php:856
msgid "After checkout billing form"
msgstr "После формы выставления счёта"

#: framework/premium/features/content-blocks/hooks-manager.php:860
msgid "Before order notes"
msgstr "До примечания к заказу"

#: framework/premium/features/content-blocks/hooks-manager.php:864
msgid "After order notes"
msgstr "После примечания к заказу"

#: framework/premium/features/content-blocks/hooks-manager.php:868
msgid "Checkout shipping"
msgstr "Условия доставки"

#: framework/premium/features/content-blocks/hooks-manager.php:872
msgid "Checkout before order review"
msgstr "Оформление перед отзывом"

#: framework/premium/features/content-blocks/hooks-manager.php:876
msgid "Checkout order review"
msgstr "Просмотр оформляемого заказа"

#: framework/premium/features/content-blocks/hooks-manager.php:880
msgid "Review order before cart contents"
msgstr "До содержимого корзины просмотра заказа"

#: framework/premium/features/content-blocks/hooks-manager.php:884
msgid "Review order after cart contents"
msgstr "После содержимого корзины просмотра заказа"

#: framework/premium/features/content-blocks/hooks-manager.php:888
msgid "Review order before order total"
msgstr "До итога просмотра заказа"

#: framework/premium/features/content-blocks/hooks-manager.php:892
msgid "Review order after order total"
msgstr "После итога просмотра заказа"

#: framework/premium/features/content-blocks/hooks-manager.php:896
msgid "Review order before payment"
msgstr "До платежа просмотра заказа"

#: framework/premium/features/content-blocks/hooks-manager.php:900
msgid "Review order before submit"
msgstr "До платежа просмотра заказа"

#: framework/premium/features/content-blocks/hooks-manager.php:904
msgid "Review order after submit"
msgstr "После подтверждения просмотра заказа"

#: framework/premium/features/content-blocks/hooks-manager.php:908
msgid "Review order after payment"
msgstr "После платежа просмотра заказа"

#: framework/premium/features/content-blocks/hooks-manager.php:912
msgid "Checkout after order review"
msgstr "Оформление после отзыва"

#: framework/premium/features/content-blocks/hooks-manager.php:916
msgid "After checkout form"
msgstr "После формы оформлени заказа"

#: framework/premium/features/content-blocks/hooks-manager.php:919
msgid "WooCommerce Checkout"
msgstr "Оформление заказа WooCommerce"

#: framework/premium/features/content-blocks/hooks-manager.php:925
msgid "Before my account"
msgstr "Перед профилем"

#: framework/premium/features/content-blocks/hooks-manager.php:929
msgid "Before account navigation"
msgstr "Перед навигацией профиля"

#: framework/premium/features/content-blocks/hooks-manager.php:933
msgid "Account navigation"
msgstr "Навигация профиля"

#: framework/premium/features/content-blocks/hooks-manager.php:937
msgid "After account navigation"
msgstr "После навигации профиля"

#: framework/premium/features/content-blocks/hooks-manager.php:941
msgid "Account content"
msgstr "Содержимое профиля"

#: framework/premium/features/content-blocks/hooks-manager.php:945
msgid "Account dashboard"
msgstr "Консоль профиля"

#: framework/premium/features/content-blocks/hooks-manager.php:949
msgid "After my account"
msgstr "После профиля"

#: framework/premium/features/content-blocks/hooks-manager.php:951,
#: framework/features/header/items/account/options.php:169,
#: framework/features/header/items/account/options.php:284,
#: framework/features/header/items/account/options.php:285
msgid "WooCommerce Account"
msgstr "WooCommerce-профиль"

#: framework/premium/features/content-blocks/hooks-manager.php:977
msgid "WP footer"
msgstr "Футер WP"

#: framework/premium/features/content-blocks/admin-ui.php:266,
#: framework/premium/features/content-blocks/hooks-manager.php:978,
#: framework/premium/features/content-blocks/hooks-manager.php:986,
#: framework/premium/features/content-blocks/hooks-manager.php:994
msgid "Footer"
msgstr "футер"

#: framework/premium/features/content-blocks/hooks-manager.php:985
msgid "Footer before"
msgstr "До футера"

#: framework/premium/features/content-blocks/hooks-manager.php:993
msgid "Footer after"
msgstr "После футера"

#: framework/premium/features/content-blocks/hooks-manager.php:1009
msgid "Custom Hook (%s)"
msgstr "Произвольный зацеп (%s)"

#: framework/premium/features/content-blocks/hooks-manager.php:1015,
#: framework/premium/features/content-blocks/options/hook.php:116
#: framework/premium/static/js/options/MultipleLocationsSelect.js:94
msgid "After Block Number"
msgstr "После номера блока"

#: framework/premium/features/content-blocks/hooks-manager.php:1021,
#: framework/premium/features/content-blocks/options/hook.php:133
#: framework/premium/static/js/options/MultipleLocationsSelect.js:116
msgid "Before Heading Number"
msgstr "До номера заголовка"

#: static/js/editor/blocks/about-me/index.js:45
msgid "About Me"
msgstr "О себе"

#: framework/features/blocks/about-me/options.php:16
msgid "About me"
msgstr "О себе"

#: framework/features/blocks/about-me/options.php:49,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:31,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:729,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:68,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:99,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:143,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:166,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:166
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:96
msgid "Image"
msgstr "Изображение"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:126
msgid "Upload Image"
msgstr "Загрузить изображение"

#: framework/features/blocks/about-me/options.php:53,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:96,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:106,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:809,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:819,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:72
msgid "Select Image"
msgstr "Выбрать изображение"

#: framework/features/blocks/about-me/options.php:54
msgid "Change Image"
msgstr "Изменить изображение"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:46,
#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:20,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:123,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:112,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:65
msgid "Image Ratio"
msgstr "Пропорции изображения"

#: framework/features/blocks/contact-info/options.php:520,
#: framework/premium/extensions/shortcuts/customizer.php:307
msgid "Open link in new tab"
msgstr "Открывать в новой вкладке"

#: framework/features/blocks/contact-info/options.php:35
#: static/js/editor/blocks/contact-info/index.js:54
msgid "Contact Info"
msgstr "Контакты"

#: framework/features/blocks/contact-info/options.php:51,
#: framework/features/blocks/contact-info/options.php:81,
#: framework/features/blocks/contact-info/view.php:15,
#: framework/premium/features/premium-header/items/contacts/options.php:57,
#: framework/premium/features/premium-header/items/contacts/options.php:88,
#: framework/premium/features/premium-header/items/contacts/view.php:35
msgid "Address:"
msgstr "Адрес:"

#: framework/features/blocks/contact-info/options.php:59,
#: framework/features/blocks/contact-info/options.php:146,
#: framework/features/blocks/contact-info/view.php:23,
#: framework/premium/features/premium-header/items/contacts/options.php:65,
#: framework/premium/features/premium-header/items/contacts/options.php:133,
#: framework/premium/features/premium-header/items/contacts/view.php:43
msgid "Phone:"
msgstr "Телефон:"

#: framework/features/blocks/contact-info/options.php:67,
#: framework/features/blocks/contact-info/options.php:209,
#: framework/features/blocks/contact-info/view.php:31,
#: framework/premium/features/premium-header/items/contacts/options.php:73,
#: framework/premium/features/premium-header/items/contacts/options.php:178,
#: framework/premium/features/premium-header/items/contacts/view.php:51
msgid "Mobile:"
msgstr "Мобильный:"

#: framework/features/blocks/contact-info/options.php:75,
#: framework/premium/features/premium-header/items/contacts/options.php:82
msgid "Address"
msgstr "Адрес"

#: framework/features/blocks/contact-info/options.php:94,
#: framework/features/blocks/contact-info/options.php:159,
#: framework/features/blocks/contact-info/options.php:222,
#: framework/features/blocks/contact-info/options.php:285,
#: framework/features/blocks/contact-info/options.php:348,
#: framework/features/blocks/contact-info/options.php:411,
#: framework/features/blocks/contact-info/options.php:474,
#: framework/premium/features/premium-header/items/contacts/options.php:107,
#: framework/premium/features/premium-header/items/contacts/options.php:152,
#: framework/premium/features/premium-header/items/contacts/options.php:197,
#: framework/premium/features/premium-header/items/contacts/options.php:242,
#: framework/premium/features/premium-header/items/contacts/options.php:288,
#: framework/premium/features/premium-header/items/contacts/options.php:333,
#: framework/premium/features/premium-header/items/contacts/options.php:378
msgid "Link (optional)"
msgstr "Ссылка (необязательно)"

#: framework/features/blocks/contact-info/options.php:265,
#: framework/premium/features/premium-header/items/contacts/options.php:217
msgid "Work Hours"
msgstr "Часы роботы"

#: framework/features/blocks/contact-info/options.php:272,
#: framework/premium/features/premium-header/items/contacts/options.php:223
msgid "Opening hours"
msgstr "Часы приёма"

#: framework/features/blocks/contact-info/options.php:328,
#: framework/premium/features/premium-header/items/contacts/options.php:263
msgid "Fax"
msgstr "Факс"

#: framework/features/blocks/contact-info/options.php:335,
#: framework/premium/features/premium-header/items/contacts/options.php:269
msgid "Fax:"
msgstr "Факс: "

#: framework/features/blocks/contact-info/options.php:398,
#: framework/premium/features/premium-header/items/contacts/options.php:314
msgid "Email:"
msgstr "Email:"

#: framework/features/blocks/contact-info/options.php:454,
#: framework/premium/features/premium-header/items/contacts/options.php:353
msgid "Website"
msgstr "Сайт"

#: framework/features/blocks/contact-info/options.php:461,
#: framework/premium/features/premium-header/items/contacts/options.php:359
msgid "Website:"
msgstr "Сайт:"

#: framework/features/blocks/about-me/options.php:85,
#: framework/premium/features/content-blocks/options/archive.php:73
msgid "Small"
msgstr "Маленький"

#: framework/features/blocks/about-me/options.php:87
msgid "Large"
msgstr "Большой"

#: framework/features/blocks/about-me/options.php:200,
#: framework/features/blocks/contact-info/options.php:557,
#: framework/features/blocks/share-box/options.php:154,
#: framework/features/blocks/socials/options.php:106,
#: framework/premium/features/premium-header/items/contacts/options.php:445
msgid "Icons Shape Type"
msgstr "Подложка значков"

#: framework/features/blocks/about-me/options.php:97,
#: framework/features/blocks/about-me/options.php:205,
#: framework/features/blocks/contact-info/options.php:565,
#: framework/features/blocks/share-box/options.php:161,
#: framework/features/blocks/socials/options.php:113,
#: framework/premium/features/premium-header/items/contacts/options.php:452
msgid "Rounded"
msgstr "Круглая"

#: framework/features/blocks/about-me/options.php:98,
#: framework/features/blocks/about-me/options.php:206,
#: framework/features/blocks/contact-info/options.php:566,
#: framework/features/blocks/share-box/options.php:162,
#: framework/features/blocks/socials/options.php:114,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:27,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:136,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:190,
#: framework/premium/features/premium-header/items/contacts/options.php:453
#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:86
msgid "Square"
msgstr "Квадратная"

#: framework/features/blocks/about-me/options.php:215,
#: framework/features/blocks/contact-info/options.php:575,
#: framework/features/blocks/share-box/options.php:171,
#: framework/features/blocks/socials/options.php:123,
#: framework/premium/features/premium-header/items/contacts/options.php:464
msgid "Shape Fill Type"
msgstr "Вид подложки"

#: framework/features/blocks/about-me/options.php:220,
#: framework/features/blocks/contact-info/options.php:582,
#: framework/features/blocks/share-box/options.php:178,
#: framework/features/blocks/socials/options.php:130,
#: framework/premium/features/premium-header/items/contacts/options.php:472
msgid "Solid"
msgstr "Сплошная фигура"

#: framework/features/blocks/about-me/options.php:219,
#: framework/features/blocks/contact-info/options.php:581,
#: framework/features/blocks/share-box/options.php:177,
#: framework/features/blocks/socials/options.php:129,
#: framework/premium/features/premium-header/items/contacts/options.php:471
msgid "Outline"
msgstr "Контур"

#: framework/extensions/trending/customizer.php:273,
#: framework/extensions/trending/customizer.php:287
#: static/js/editor/blocks/query/Edit.js:169
msgid "Post Type"
msgstr "Тип записи"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:149
msgid "Most commented"
msgstr "Обсуждаемое"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:165
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:144
msgid "Random"
msgstr "Произволу"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:117
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:112
msgid "Order by"
msgstr "Диапазон вывода"

#: framework/features/blocks/dynamic-data/options.php:109,
#: framework/features/blocks/dynamic-data/views/wp-field.php:196
msgid "% comments"
msgstr "Комментарии (%)"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:80
msgid "Author Avatar"
msgstr "Аватар автора"

#: framework/features/blocks/socials/options.php:15
msgid "Social Icons"
msgstr "Значки соцсетей"

#: framework/features/blocks/about-me/options.php:124
msgid "You can configure social URLs in %s."
msgstr "Откройте %s, чтобы настроить URL-адреса социальных сетей."

#: framework/features/blocks/about-me/options.php:156,
#: framework/features/blocks/socials/options.php:62,
#: framework/premium/features/premium-header/items/contacts/options.php:405
msgid "Open links in new tab"
msgstr "Открывать в новой вкладке"

#: framework/features/blocks/about-me/options.php:163,
#: framework/features/blocks/contact-info/options.php:527,
#: framework/features/blocks/share-box/options.php:117,
#: framework/features/blocks/socials/options.php:69,
#: framework/premium/features/premium-header/items/contacts/options.php:411
msgid "Set links to nofollow"
msgstr "Установить ссылкам nofollow"

#: framework/features/header/items/account/options.php:4
msgid "Profile Page"
msgstr "Страница профиля"

#: framework/features/header/items/account/options.php:5
msgid "Dashboard Page"
msgstr "Страница консоли"

#: framework/features/header/items/account/options.php:7,
#: framework/features/header/items/account/options.php:36,
#: framework/features/header/items/account/options.php:117,
#: framework/features/header/items/account/options.php:280,
#: framework/features/header/items/account/views/login.php:396
msgid "Custom Link"
msgstr "Произвольная ссылка"

#: framework/features/header/items/account/options.php:6
msgid "Logout"
msgstr "Выход"

#: framework/features/header/items/account/options.php:279
msgid "Modal"
msgstr "Модальное окно"

#: framework/features/header/items/account/options.php:295
msgid "Customizing: Logged in State"
msgstr "Настройка: статус авторизации"

#: framework/features/header/items/account/options.php:319
msgid "Logged In Options"
msgstr "Опции авторизованных"

#: framework/features/header/items/account/options.php:324
msgid "Logged Out Options"
msgstr "Опции неавторизованных"

#: framework/features/header/items/account/options.php:611,
#: framework/features/header/items/account/options.php:1059
msgid "Account Action"
msgstr "Действие профиля"

#: framework/features/header/items/account/options.php:145
msgid "Select Menu"
msgstr "Выбор меню"

#: framework/features/header/items/account/options.php:151
msgid "Select menu..."
msgstr "Выберите меню..."

#. translators: placeholder here means the actual URL.
#: framework/features/header/items/account/options.php:155
msgid "Manage your menus in the %sMenus screen%s."
msgstr "Управление пунктами меню в %sразделе «Меню»%s."

#: framework/features/header/items/account/options.php:702,
#: framework/features/header/items/account/options.php:1073
msgid "Custom Page Link"
msgstr "Ссылка на страницу"

#: framework/features/header/items/account/options.php:340,
#: framework/features/header/items/account/options.php:739
msgid "Account Image"
msgstr "Изображение профиля"

#: framework/features/header/items/account/options.php:346
msgid "Avatar"
msgstr "Аватар"

#: framework/features/header/items/account/options.php:357
msgid "Avatar Size"
msgstr "Размер аватара"

#: framework/features/header/items/account/options.php:487,
#: framework/features/header/items/account/options.php:861,
#: framework/features/header/items/account/options.php:949
msgid "Type 5"
msgstr "Тип 5"

#: framework/features/header/items/account/options.php:497,
#: framework/features/header/items/account/options.php:871,
#: framework/features/header/items/account/options.php:959
msgid "Type 6"
msgstr "Тип 6"

#: framework/features/header/items/account/options.php:570
msgid "Label Type"
msgstr "Тип ярлыка"

#: framework/features/header/items/account/options.php:587,
#: framework/features/header/items/account/options.php:1045,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:136,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:137
msgid "Label Text"
msgstr "Текст ярлыка"

#: framework/features/header/items/account/options.php:173,
#: framework/features/header/items/account/options.php:597,
#: framework/features/header/items/account/views/login.php:100,
#: framework/features/header/items/account/views/login.php:472
msgid "My Account"
msgstr "Мой профиль"

#: framework/features/header/items/account/options.php:1125,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:163,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:167,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:170
msgid "Label Font"
msgstr "Шрифт ярлыка"

#: framework/features/header/items/account/options.php:1135,
#: framework/features/header/items/account/options.php:1174,
#: framework/features/header/items/account/options.php:1218,
#: framework/features/header/items/account/options.php:1260
msgid "Label Color"
msgstr "Цвет ярлыка"

#: framework/features/header/header-options.php:214,
#: framework/features/header/items/account/options.php:1140,
#: framework/features/header/items/account/options.php:1332,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:179,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:305,
#: framework/premium/features/premium-header/items/contacts/options.php:590,
#: framework/premium/features/premium-header/items/contacts/options.php:756,
#: framework/premium/features/premium-header/items/contacts/options.php:901,
#: framework/premium/features/premium-header/items/divider/options.php:27,
#: framework/premium/features/premium-header/items/search-input/options.php:271,
#: framework/premium/features/premium-header/items/search-input/options.php:401,
#: framework/premium/features/premium-header/items/search-input/options.php:531,
#: framework/premium/features/premium-header/items/search-input/options.php:667,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:227,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:358,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:57,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:183,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:359,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:492,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:186,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:313,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:434
msgid "Default State"
msgstr "Стандартный режим"

#: framework/features/header/header-options.php:219,
#: framework/features/header/items/account/options.php:1148,
#: framework/features/header/items/account/options.php:1340,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:184,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:310,
#: framework/premium/features/premium-header/items/contacts/options.php:595,
#: framework/premium/features/premium-header/items/contacts/options.php:761,
#: framework/premium/features/premium-header/items/contacts/options.php:909,
#: framework/premium/features/premium-header/items/divider/options.php:32,
#: framework/premium/features/premium-header/items/search-input/options.php:276,
#: framework/premium/features/premium-header/items/search-input/options.php:406,
#: framework/premium/features/premium-header/items/search-input/options.php:536,
#: framework/premium/features/premium-header/items/search-input/options.php:672,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:232,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:363,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:62,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:191,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:364,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:500,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:191,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:318,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:442
msgid "Transparent State"
msgstr "Режим прозра��ности"

#: framework/features/header/header-options.php:227,
#: framework/features/header/items/account/options.php:1161,
#: framework/features/header/items/account/options.php:1353,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:193,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:319,
#: framework/premium/features/premium-header/items/contacts/options.php:604,
#: framework/premium/features/premium-header/items/contacts/options.php:770,
#: framework/premium/features/premium-header/items/contacts/options.php:919,
#: framework/premium/features/premium-header/items/divider/options.php:41,
#: framework/premium/features/premium-header/items/search-input/options.php:285,
#: framework/premium/features/premium-header/items/search-input/options.php:415,
#: framework/premium/features/premium-header/items/search-input/options.php:545,
#: framework/premium/features/premium-header/items/search-input/options.php:681,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:241,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:372,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:71,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:204,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:373,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:510,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:200,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:327,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:452
msgid "Sticky State"
msgstr "Режим закрепления"

#: framework/features/header/items/account/options.php:1499
msgid "Item Margin"
msgstr "Отступы элемента"

#: framework/features/header/items/account/options.php:1514
msgid "Modal Options"
msgstr "Настройки модального окна"

#: framework/features/header/items/account/options.php:1705,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:451
msgid "Modal Shadow"
msgstr "Тень модального окна"

#: framework/features/header/items/account/options.php:1677,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:422
msgid "Modal Background"
msgstr "Фон модального окна"

#: framework/features/header/items/account/options.php:1691,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:437
msgid "Modal Backdrop"
msgstr "Задний фон модального окна"

#: framework/features/blocks/contact-info/options.php:13,
#: framework/features/header/items/account/options.php:2059,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:441,
#: framework/premium/features/premium-header/items/contacts/options.php:486,
#: framework/premium/features/premium-header/items/contacts/options.php:541,
#: framework/premium/features/premium-header/items/language-switcher/options.php:120,
#: framework/premium/features/premium-header/items/language-switcher/options.php:251,
#: framework/premium/features/premium-header/items/search-input/options.php:239,
#: framework/premium/features/premium-header/items/search-input/options.php:936,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:644,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:578
msgid "Element Visibility"
msgstr "Видимость элемента"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:655
msgid "You have no %s fields declared for this custom post type."
msgstr "У вас нет полей %s, заявленных для этого произвольного типа записей."

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:667,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1401,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1412
msgid "Field"
msgstr "Поле"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:679,
#: framework/premium/features/premium-header/items/language-switcher/options/common.php:24
msgid "Label"
msgstr "Ярлык"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:728
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:319
msgid "Fallback"
msgstr "Обратная совместимость"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:755
msgid "%s Field"
msgstr "Поле %s"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1399
msgid "%s %s Font"
msgstr "Шрифт %s %s"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1410
msgid "%s %s Color"
msgstr "Цвет %s %s"

#: framework/helpers/exts-configs.php:142,
#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:11,
#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:68
msgid "Read Time"
msgstr "Время чтения"

#: framework/premium/extensions/post-types-extra/features/filtering/helpers.php:191,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:195,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:842
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:65
msgid "All"
msgstr "Все"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:92,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:803
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:75
msgid "Featured Image"
msgstr "Изображение"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:110
msgid "Accent Color"
msgstr "Цвет акцента"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/helpers.php:150,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/helpers.php:209
msgid "Add to wishlist"
msgstr "Добавить в список желаний"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:9
msgid "Select a page"
msgstr "Выберите страницу"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:23
msgid "Show Wishlist Page To"
msgstr "Показать страницу списка желаний"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:29
msgid "Logged Users"
msgstr "Вошедшим"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:30
msgid "All Users"
msgstr "Всем пользователям"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:40
msgid "Wishlist Page"
msgstr "Страница списка желаний"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:46
msgid "The page you select here will display the wish list for your logged out users."
msgstr " На странице, которую вы выберете здесь, будет отображаться список желаний для невошедших в систему пользователей."

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:25
msgid "Archive Page"
msgstr "Страница архивов"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:227,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:240,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:276,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:273,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:303,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:332,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:405,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:435,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:464,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:103,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:133,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:162
msgid "Hover/Active"
msgstr "При наведении/активная"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/views/table-no-results.php:6
msgid "You don't have any products in your wish list yet."
msgstr "В вашем списке желаний ещё нет товаров."

#: framework/premium/extensions/woocommerce-extra/features/archive-card/feature.php:56,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:54,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:82
msgid "Add to cart"
msgstr "Добавить в корзину"

#: framework/premium/extensions/woocommerce-extra/features/compare/views/bar.php:101,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:142,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:145,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/views/columns/product-remove-button.php:12
msgid "Remove Product"
msgstr "Удалить товар"

#: framework/premium/extensions/woocommerce-extra/includes/woo-import-export.php:36
msgid "Blocksy Variation Images"
msgstr "Изображения вариаций"

#: framework/premium/features/content-blocks/options/404.php:33,
#: framework/premium/features/content-blocks/options/header.php:55,
#: framework/premium/features/content-blocks/options/hook.php:159,
#: framework/premium/features/content-blocks/options/maintenance.php:30,
#: framework/premium/features/content-blocks/options/nothing_found.php:51,
#: framework/premium/features/content-blocks/options/popup.php:41
msgid "Container Structure"
msgstr "Структура контейнера"

#: framework/premium/features/content-blocks/options/404.php:65,
#: framework/premium/features/content-blocks/options/archive.php:132,
#: framework/premium/features/content-blocks/options/header.php:83,
#: framework/premium/features/content-blocks/options/hook.php:192,
#: framework/premium/features/content-blocks/options/maintenance.php:62,
#: framework/premium/features/content-blocks/options/nothing_found.php:83,
#: framework/premium/features/content-blocks/options/single.php:64
msgid "Narrow Width"
msgstr "Узкая ширина"

#: framework/premium/features/content-blocks/options/404.php:70,
#: framework/premium/features/content-blocks/options/archive.php:137,
#: framework/premium/features/content-blocks/options/header.php:88,
#: framework/premium/features/content-blocks/options/hook.php:197,
#: framework/premium/features/content-blocks/options/maintenance.php:67,
#: framework/premium/features/content-blocks/options/nothing_found.php:88,
#: framework/premium/features/content-blocks/options/single.php:69
msgid "Normal Width"
msgstr "Нормальная ширина"

#: framework/premium/features/content-blocks/options/404.php:76,
#: framework/premium/features/content-blocks/options/archive.php:153,
#: framework/premium/features/content-blocks/options/header.php:94,
#: framework/premium/features/content-blocks/options/hook.php:203,
#: framework/premium/features/content-blocks/options/maintenance.php:73,
#: framework/premium/features/content-blocks/options/nothing_found.php:94,
#: framework/premium/features/content-blocks/options/single.php:85
msgid "Content Area Style"
msgstr "Стиль области содержимого"

#: framework/premium/features/content-blocks/options/404.php:90,
#: framework/premium/features/content-blocks/options/archive.php:167,
#: framework/premium/features/content-blocks/options/header.php:108,
#: framework/premium/features/content-blocks/options/hook.php:217,
#: framework/premium/features/content-blocks/options/maintenance.php:87,
#: framework/premium/features/content-blocks/options/nothing_found.php:108
msgid "Content Area Vertical Spacing"
msgstr "Вертикальные отступы области содержимого"

#: framework/premium/features/content-blocks/options/404.php:102,
#: framework/premium/features/content-blocks/options/archive.php:179,
#: framework/premium/features/content-blocks/options/header.php:120,
#: framework/premium/features/content-blocks/options/hook.php:229,
#: framework/premium/features/content-blocks/options/maintenance.php:99,
#: framework/premium/features/content-blocks/options/nothing_found.php:120,
#: framework/premium/features/content-blocks/options/single.php:111
msgid "Top & Bottom"
msgstr "Верху и внизу"

#: framework/premium/features/content-blocks/options/404.php:105,
#: framework/premium/features/content-blocks/options/archive.php:182,
#: framework/premium/features/content-blocks/options/header.php:123,
#: framework/premium/features/content-blocks/options/hook.php:232,
#: framework/premium/features/content-blocks/options/maintenance.php:102,
#: framework/premium/features/content-blocks/options/nothing_found.php:123,
#: framework/premium/features/content-blocks/options/single.php:114
msgid "Only Top"
msgstr "Только вверху"

#: framework/premium/features/content-blocks/options/404.php:108,
#: framework/premium/features/content-blocks/options/archive.php:185,
#: framework/premium/features/content-blocks/options/header.php:126,
#: framework/premium/features/content-blocks/options/hook.php:235,
#: framework/premium/features/content-blocks/options/maintenance.php:105,
#: framework/premium/features/content-blocks/options/nothing_found.php:126,
#: framework/premium/features/content-blocks/options/single.php:117
msgid "Only Bottom"
msgstr "Только внизу"

#: framework/premium/features/content-blocks/options/hook.php:60
msgid "Location & Priority"
msgstr "Положение и приоритет"

#: framework/premium/features/content-blocks/options/hook.php:23,
#: framework/premium/features/content-blocks/options/hook.php:100
#: framework/premium/static/js/options/MultipleLocationsSelect.js:76
msgid "Custom Hook"
msgstr "Произвольный зацеп"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:730
msgid "Mixed"
msgstr "Разное"

#: framework/premium/features/content-blocks/options/popup.php:438,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:628
msgid "Popup Position"
msgstr "Положение модального окна"

#: framework/premium/features/content-blocks/options/popup.php:384,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:573
msgid "Popup Size"
msgstr "Размер модального окна"

#: framework/premium/features/content-blocks/options/popup.php:390,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:580
msgid "Small Size"
msgstr "Маленький размер"

#: framework/premium/features/content-blocks/options/popup.php:391,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:581
msgid "Medium Size"
msgstr "Средний размер"

#: framework/premium/features/content-blocks/options/popup.php:392,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:582
msgid "Large Size"
msgstr "Большой размер"

#: framework/premium/features/content-blocks/options/popup.php:393,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:583
msgid "Custom Size"
msgstr "Произвольный"

#: framework/premium/features/content-blocks/options/popup.php:403,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:593
msgid "Max Width"
msgstr "Максимальная ширина"

#: framework/premium/features/content-blocks/options/popup.php:419,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:609
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:510
msgid "Max Height"
msgstr "Максимальная высота"

#: framework/premium/features/content-blocks/options/popup.php:339,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:525
msgid "Popup Animation"
msgstr "Анимация модального окна"

#: framework/premium/features/content-blocks/options/popup.php:345,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:531
msgid "Fade in fade out"
msgstr "Нарастание/затухание"

#: framework/premium/features/content-blocks/options/popup.php:346,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:532
msgid "Zoom in zoom out"
msgstr "Наезд/отъезд"

#: framework/premium/features/content-blocks/options/popup.php:347,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:533
msgid "Slide in from left"
msgstr "Скольжение слева"

#: framework/premium/features/content-blocks/options/popup.php:348,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:534
msgid "Slide in from right"
msgstr "Скольжение справа"

#: framework/premium/features/content-blocks/options/popup.php:349,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:535
msgid "Slide in from top"
msgstr "Скольжение сверху"

#: framework/premium/features/content-blocks/options/popup.php:350,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:536
msgid "Slide in from bottom"
msgstr "Скольжение снизу"

#: framework/premium/features/content-blocks/options/popup.php:355,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:542
msgid "Animation Speed"
msgstr "Скорость анимации"

#: framework/premium/features/content-blocks/options/popup.php:372,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:560
msgid "Entrance Value"
msgstr "Входное значение"

#: framework/premium/features/content-blocks/options/popup.php:69
msgid "Trigger Condition"
msgstr "Условия срабатывания"

#: framework/premium/features/content-blocks/options/popup.php:93,
#: framework/premium/features/content-blocks/options/popup.php:111
msgid "Element Class"
msgstr "Класс элемента"

#: framework/premium/features/content-blocks/options/popup.php:99,
#: framework/premium/features/content-blocks/options/popup.php:117
msgid "Separate each class by comma if you have multiple elements."
msgstr " Разделяйте каждый класс запятой, если у вас несколько элементов."

#: framework/premium/features/content-blocks/options/popup.php:129
msgid "Scroll Direction"
msgstr "Направление прокрутки"

#: framework/premium/features/content-blocks/options/popup.php:134
msgid "Scroll Down"
msgstr "Прокрутка вниз"

#: framework/premium/features/content-blocks/options/popup.php:135
msgid "Scroll Up"
msgstr "Прокрутка вверх"

#: framework/premium/features/content-blocks/options/popup.php:140
msgid "Scroll Distance"
msgstr "Длина прокрутки"

#: framework/premium/features/content-blocks/options/popup.php:149
msgid "Set the scroll distance till the popup block will appear."
msgstr "Установите дистанцию прокрутки до появления всплывающего блока."

#: framework/premium/features/content-blocks/options/popup.php:167
msgid "Inactivity Time"
msgstr "Время бездействия"

#: framework/premium/features/content-blocks/options/popup.php:173
msgid "Set the inactivity time (in seconds) till the popup block will appear."
msgstr "Установите время бездействия (в секундах) до появления всплывающего блока."

#: framework/premium/features/content-blocks/options/popup.php:185
msgid "After X Time"
msgstr "Через определённое время"

#: framework/premium/features/content-blocks/options/popup.php:191
msgid "Set after how much time (in seconds) the popup block will appear."
msgstr "Установите, через какое время (в секундах) появится всплывающий блок."

#: framework/premium/features/content-blocks/options/popup.php:522,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:676
msgid "Padding"
msgstr "Внутренний отступ"

#: framework/features/header/items/account/options.php:2034,
#: framework/premium/features/content-blocks/options/popup.php:533,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:298,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:688,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:455,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:330,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:194
msgid "Border Radius"
msgstr "Скругление углов"

#: framework/premium/features/content-blocks/options/popup.php:563,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:666
msgid "Popup Offset"
msgstr "Смещение модального окна"

#: framework/premium/features/content-blocks/options/popup.php:581
msgid "Container Overflow"
msgstr "Переполнение контейнера"

#: framework/premium/features/content-blocks/options/popup.php:588
msgid "Hidden"
msgstr "Скрытие"

#: framework/premium/features/content-blocks/options/popup.php:589
msgid "Visible"
msgstr "Отображение"

#: framework/premium/features/content-blocks/options/popup.php:590
msgid "Scroll"
msgstr "Прокрутка"

#: framework/premium/features/content-blocks/options/popup.php:592
msgid "Control what happens to the content that is too big to fit into the popup."
msgstr "Контролируйте, что происходит с содержимым, которое слишком велико для всплывающего окна."

#: framework/premium/features/content-blocks/options/popup.php:596
msgid "Close Button"
msgstr "Кнопка закрытия"

#: framework/premium/features/content-blocks/options/popup.php:604
msgid "Inside"
msgstr "Внутри"

#: framework/premium/features/content-blocks/options/popup.php:605
msgid "Outside"
msgstr "Снаружи"

#: framework/premium/features/content-blocks/options/popup.php:679,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:720,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:299,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:72
msgid "Popup Background"
msgstr "Заливка модального окна"

#: framework/premium/features/content-blocks/options/popup.php:693
msgid "Popup Backdrop Background"
msgstr "Задний фон модального окна"

#: framework/helpers/exts-configs.php:156,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:25
msgid "Posts Filter"
msgstr "Фильтр записей"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:59
msgid "Filtering Behavior"
msgstr "Поведение фильтрации"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:67
msgid "Instant Reload"
msgstr "Мгновенная перезагрузка"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:68
msgid "Page Reload"
msgstr "Перезагрузка страницы"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:103
msgid "Items Horizontal Spacing"
msgstr "Горизонтальный интервал"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:115,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:79
msgid "Items Vertical Spacing"
msgstr "Вертикальный интервал"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:127
msgid "Container Bottom Spacing"
msgstr "Нижний отступ контейнера"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:139,
#: framework/premium/features/premium-header/items/contacts/options.php:510,
#: framework/premium/features/premium-header/items/language-switcher/options.php:90,
#: framework/premium/features/premium-header/items/search-input/options.php:207
msgid "Horizontal Alignment"
msgstr "Горизонтальное выравнивание"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:284
msgid "Button Padding"
msgstr "Внутненние отступы кнопки"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:4
msgid "Read Progress"
msgstr "Прогресс чтения"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:24
msgid "Indicator Height"
msgstr "Высота индикатора"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:34
msgid "Auto Hide"
msgstr "Автоскрытие"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:38
msgid "Automatically hide the read progress bar once you arrive at the bottom of the article."
msgstr "Автоматическое скрытие индикатора прогресса чтения, как только статья заканчивается."

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:71
msgid "Main Color"
msgstr "Главный цвет"

#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:83,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:84
msgid "Icon Badge"
msgstr "Подпись значка"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:174,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:178,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:181
msgid "Label Font Color"
msgstr "Цвет шрифта ярлыка"

#: framework/premium/features/premium-header/items/contacts/config.php:4
msgid "Contacts"
msgstr "Контакты"

#: framework/features/blocks/about-me/options.php:178,
#: framework/features/blocks/contact-info/options.php:548,
#: framework/premium/features/premium-header/items/contacts/options.php:435,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:16
msgid "Items Spacing"
msgstr "Расстояние между элементами"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:420,
#: framework/premium/features/premium-header/items/contacts/options.php:1051,
#: framework/premium/features/premium-header/items/content-block/options.php:40,
#: framework/premium/features/premium-header/items/divider/options.php:107,
#: framework/premium/features/premium-header/items/divider/options.php:125,
#: framework/premium/features/premium-header/items/search-input/options.php:804,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:478,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:172,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:625,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:557
msgid "Margin"
msgstr "Отступы"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:106,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/view.php:45
msgid "Dark Mode"
msgstr "Тёмный режим"

#: framework/features/header/items/account/options.php:68,
#: framework/features/header/items/account/options.php:1999,
#: framework/premium/features/premium-header/items/divider/config.php:4
msgid "Divider"
msgstr "Разделитель"

#: framework/premium/features/premium-header/items/divider/options.php:6
msgid "Size"
msgstr "Размер"

#: framework/features/conditions/rules/localization.php:11,
#: framework/premium/features/premium-header/items/language-switcher/config.php:14
msgid "Languages"
msgstr "Языки"

#: framework/premium/features/premium-header/items/language-switcher/options.php:274
msgid "Top Level Options"
msgstr "Параметры верхнего уровня"

#: framework/features/header/items/account/options.php:618,
#: framework/premium/features/premium-header/items/language-switcher/options.php:170
msgid "Dropdown"
msgstr "Выпадающий список"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:23
msgid "Flag"
msgstr "Флаг"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:39
msgid "Label Style"
msgstr "Стиль ярлыка"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:46
msgid "Long"
msgstr "Длинный"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:47
msgid "Short"
msgstr "Короткий"

#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:26
msgid "Hide Current Language"
msgstr "Скрывать текущий язык"

#: framework/features/header/items/account/options.php:1890,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:59
msgid "Dropdown Options"
msgstr "Настройки выпадающего списка"

#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:70
msgid "Dropdown Top Offset"
msgstr "Смещение сверху выпадающего списка"

#: framework/premium/features/premium-header/items/search-input/config.php:4
msgid "Search Box"
msgstr "Поисковая строка"

#: framework/features/blocks/search/options.php:46,
#: framework/features/blocks/search/options.php:52,
#: framework/features/blocks/search/options.php:64,
#: framework/features/blocks/search/options.php:145,
#: framework/premium/features/premium-header/items/search-input/options.php:45
msgid "Placeholder Text"
msgstr "Текст-заполнитель"

#: framework/premium/features/premium-header/items/search-input/options.php:55
msgid "Input Maximum Width"
msgstr "Максимальная ширина по��я"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:87,
#: framework/features/blocks/search/options.php:70,
#: framework/premium/features/premium-header/items/search-input/options.php:67
msgid "Input Height"
msgstr "Высота поля"

#: framework/features/blocks/search/options.php:97,
#: framework/premium/features/premium-header/items/search-input/options.php:88
msgid "Live Results"
msgstr "Моментальные результаты"

#: framework/features/blocks/search/options.php:109,
#: framework/premium/features/premium-header/items/search-input/options.php:100
msgid "Live Results Images"
msgstr "Изображения моментальных результатов"

#: framework/features/blocks/search/options.php:185,
#: framework/premium/features/premium-header/items/search-input/options.php:175
msgid "Search Through Criteria"
msgstr "Поиск по критериям"

#: framework/features/blocks/search/options.php:187,
#: framework/premium/features/premium-header/items/search-input/options.php:176
msgid "Chose in which post types do you want to perform searches."
msgstr "Выберите, в каких типах записей будет выполняться поиск."

#: framework/premium/features/premium-header/items/search-input/options.php:396,
#: framework/premium/features/premium-header/items/search-input/options.php:425,
#: framework/premium/features/premium-header/items/search-input/options.php:457,
#: framework/premium/features/premium-header/items/search-input/options.php:487
msgid "Input Icon Color"
msgstr "Цвет значка ввода"

#: framework/premium/features/premium-header/items/search-input/options.php:833
#: static/js/editor/blocks/search/Edit.js:346
msgid "Dropdown Text Color"
msgstr "Цвет текста выпадающего списка"

#: framework/premium/features/premium-header/items/search-input/options.php:864
msgid "Dropdown Background"
msgstr "Фон выпадающего списка"

#: framework/premium/features/premium-header/items/widget-area-1/config.php:4
msgid "Widget Area"
msgstr "Область виджета"

#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker.js:14
#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker/Implementation.js:72
#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:362
#: static/js/dashboard/NoTheme.js:64 static/js/dashboard/VersionMismatch.js:61
#: static/js/dashboard/screens/SiteExport.js:310
#: static/js/notifications/VersionMismatchNotice.js:73
msgid "Loading..."
msgstr "Загрузка…"

#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker/Implementation.js:73
msgid "Invalid API Key..."
msgstr "Неверный API-ключ..."

#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker/Implementation.js:101
#: framework/extensions/newsletter-subscribe/dashboard-static/js/ListPicker.js:119
msgid "Select list..."
msgstr "Выберите список..."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:218
msgid "More information on how to generate an API key for Mailchimp can be found %shere%s."
msgstr "Информацию о том, как сгенерировать ключ API для Mailchimp, можно найти %sздесь%s."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:286
msgid "More information on how to generate an API key for ConvertKit can be found %shere%s."
msgstr "Информацию о том, как сгенерировать ключ API для Mailerlite, можно найти %sздесь%s."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:184
msgid "API Key"
msgstr "API-ключ"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:363
#: framework/extensions/product-reviews/static/js/ProductReviews.js:94
#: framework/premium/extensions/mega-menu/static/js/SettingsManager.js:280
#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:111
#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:910
#: framework/premium/static/js/footer/EditConditions.js:143
#: framework/premium/static/js/media-video/components/EditVideoMeta.js:106
msgid "Save Settings"
msgstr "Сохранить настройки"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:153
msgid "Pick Mailing Service"
msgstr "Выбор службы доставки"

#: framework/extensions/product-reviews/static/js/ProductReviews.js:22
msgid "Product Reviews Settings"
msgstr "Настройки обзоров товаров"

#: framework/extensions/product-reviews/static/js/ProductReviews.js:25
msgid "Configure the slugs for single and category pages of the product review custom post type."
msgstr "Настройте ярлыки для страницы и категории типа публикации обзора товара."

#: framework/extensions/product-reviews/static/js/ProductReviews.js:43
msgid "Single Slug"
msgstr "Ярлык страницы"

#: framework/extensions/product-reviews/static/js/ProductReviews.js:49
msgid "Category Slug"
msgstr "Ярлык категории"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:84
msgid "Adobe Fonts Settings"
msgstr "Настройки шрифтов Adobe"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:116
msgid "Project ID"
msgstr "ID проекта"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:158
msgid "Fetch Fonts"
msgstr "Получить шрифты"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:182
#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:54
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:39
msgid "Variations"
msgstr "Вариации"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/CustomFonts.js:74
msgid "Custom Fonts Settings"
msgstr "Пользовательские настройки шрифтов"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/CustomFonts.js:76
msgid "Here you can see all your custom fonts that can be used in all typography options across the theme."
msgstr "Здесь вы можете увидеть все свои пользовательские шрифты, которые можно использовать во всех вариантах типографики в теме."

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:28
msgid "Dynamic Font"
msgstr "Динамический шрифт"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:37
msgid "Variable font"
msgstr "Вариативный шрифт"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:78
#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:322
msgid "Edit Font"
msgstr "Править шрифт"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:100
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:78
msgid "Remove Font"
msgstr "Удалить шрифт"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:216
msgid "Change"
msgstr "Изменить"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:217
msgid "Choose"
msgstr "Выбрать"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:127
#: static/js/options/ConditionsManager/SingleCondition.js:95
msgid "Select variation"
msgstr "Выберите вариацию"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:147
msgid "Regular"
msgstr "Обычный"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:154
#: framework/premium/static/js/typography/providers/kadence.js:71
msgid "Italic"
msgstr "Курсивный"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/RegularTab.js:30
msgid "Upload only the %s.woff2%s or %s.ttf%s font file formats (see browser coverage %shere%s). Use %sthis converter tool%s if you don't have these font formats."
msgstr "Загружайте только файлы шрифтов в форматах %s.woff2%s или %s.ttf%s (смотрите покрытие браузера %sздесь%s). Используйте %sэтот конвертер%s, если у вас нет таких форматов шрифтов."

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:90
msgid "Font Name"
msgstr "Название шрифта"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:459
msgid "Save Custom Font"
msgstr "Сохранить пользовательский шрифт"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:79
msgid "Add Variation"
msgstr "Добавить вариацию"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:258
msgid "Download a font and serve it directly from your server, this is handy for those who want to comply with GDPR regulations or serve the font via CDN."
msgstr "Загрузите шрифт и подгружайте его прямо со своего сервера. Это удобно для тех, кто хочет соблюдать правила GDPR или раздавать шрифт через CDN."

#: framework/premium/extensions/mega-menu/static/js/SettingsManager.js:189
msgid "Item Settings"
msgstr "Настройки элемента"

#: framework/premium/extensions/sidebars/static/js/BlockWidgetControls.js:60
msgid "Remove Sidebar"
msgstr "Удалить сайдбар"

#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:65
msgid "%s - Sidebar Display Conditions"
msgstr "%s - условия отображения сайдбара"

#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:73
msgid "Add one or more conditions in order to display your sidebar."
msgstr "Добавьте одно или несколько условий для отображения сайдбара."

#: framework/premium/extensions/sidebars/static/js/main.js:53
msgid "Enter a name in the input below and hit the Create Sidebar button."
msgstr "Введите название в поле ниже и нажмите кнопку «Создать сайдбар»."

#: framework/premium/extensions/sidebars/static/js/main.js:60
msgid "Sidebar name"
msgstr "Название сайдбара"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:108
msgid "Advanced"
msgstr "Дополнительно"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:123
msgid "Agency Details"
msgstr "Информация об агентстве"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:131
msgid "Agency Name"
msgstr "Название компании"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:159
msgid "Agency URL"
msgstr "URL сайта агенства"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:187
msgid "Agency Support/Contact Form URL"
msgstr "URL контактной формы/портала поддержки агентства"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:220
msgid "Theme Details"
msgstr "Детали темы"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:228
msgid "Theme Name"
msgstr "Название темы"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:256
msgid "Theme Description"
msgstr "Описание темы"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:286
msgid "Theme Screenshot URL"
msgstr "URL скриншота темы"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:384
msgid "You can insert the link to a self hosted image or upload one. The recommended image size is 1200px wide by 900px tall."
msgstr "Вы можете вставить ссылку на собственное изображение или загрузить его. Рекомендуемый размер изображения: 1200 пикселей в ширину и 900 пикселей в высоту."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:393
msgid "Theme Icon URL"
msgstr "URL значка темы"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:489
msgid "You can insert the link to a self hosted image or upload one. The recommended image size is 18px wide by 18px tall."
msgstr "Вы можете вставить ссылку на собственное изображение или загрузить его. Рекомендуемый размер изображения: 18 на 18 пикселей."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:498
msgid "Gutenberg Options Panel Icon URL"
msgstr "URL значка панели параметров Gutenberg'а"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:596
msgid "You can insert the link to a self hosted image or upload one. Please note that only icons in SVG format are allowed here to not break the editor interactiveness."
msgstr "Вы можете вставить ссылку на собственное изображение или загрузить его. Обратите внимание, что здесь разрешены только иконки в формате SVG, чтобы не нарушать интерактивность редактора."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:616
msgid "Plugin Name"
msgstr "Название плагина"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:644
msgid "Plugin Description"
msgstr "Описание плагина"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:674
msgid "Plugin Thumbnail URL"
msgstr "URL миниатюры плагина"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:764
msgid "Choose File"
msgstr "Выберите файл"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:772
msgid "You can insert the link to a self hosted image or upload one. The recommended image size is 256px wide by 256px tall."
msgstr "Вы можете вставить ссылку на собственное изображение или загрузить его. Рекомендуемый размер изображения: 256 на 256 пикселей."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:795
msgid "Hide Account Menu Item"
msgstr "Скрыть в меню профиль"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:803
msgid "Hide Starter Sites Tab"
msgstr "Скрыть вкладку стартовых сайтов"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:810
msgid "Hide Useful Plugins Tab"
msgstr "Скрыть вкладку полезных плагинов"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:818
msgid "Hide Changelog Tab"
msgstr "Скрыть вкладку журнала изменений"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:826
msgid "Hide Support Section"
msgstr "Скрыть раздел поддержки"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:876
msgid "Hide White Label Extension"
msgstr "Скрыть расширение белой этикетки"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:891
msgid "Please note that the white label extension will be hidden if this option is enabled. In order to bring it back you have to hit the SHIFT key and click on the dashboard logo."
msgstr "Обратите внимание, что расширение белой этикетки будет скрыто, если включить эту опцию. Чтобы вернуть его, вам нужно будет нажать клавишу SHIFT и нажать на логотип консоли темы."

#: framework/premium/static/js/footer/CloneItem.js:66
#: framework/premium/static/js/header/CloneItem.js:66
msgid "Clone Item"
msgstr "Дублировать элемент"

#: framework/premium/static/js/footer/CloneItem.js:90
#: framework/premium/static/js/header/CloneItem.js:90
msgid "Remove Item"
msgstr "Удалить объект"

#: framework/premium/static/js/footer/CreateFooter.js:107
#: framework/premium/static/js/header/CreateHeader.js:106
msgid "Copy elements & styles from"
msgstr "Использовать образец настроек"

#: framework/premium/static/js/footer/CreateFooter.js:118
msgid "Picker Footer"
msgstr "Подборщик подвала"

#: framework/premium/static/js/footer/CreateFooter.js:133
#: framework/premium/static/js/footer/PanelsManager.js:52
msgid "Global Footer"
msgstr "Глобальный подвал"

#: framework/premium/static/js/footer/CreateFooter.js:137
#: framework/premium/static/js/header/CreateHeader.js:138
msgid "Secondary"
msgstr "Второстепенный"

#: framework/premium/static/js/footer/CreateFooter.js:141
#: framework/premium/static/js/header/CreateHeader.js:142
msgid "Centered"
msgstr "Центрированный"

#: framework/premium/static/js/footer/CreateFooter.js:172
msgid "Create New Footer"
msgstr "Создать новый подвал"

#: framework/premium/static/js/footer/CreateFooter.js:50
msgid "Create new footer"
msgstr "Создание нового подвала"

#: framework/premium/static/js/footer/CreateFooter.js:53
msgid "Create a new footer and assign it to different pages or posts based on your conditions."
msgstr "Создайте новый подвал и назначьте его разным страницам или записям, в соответствии со своими целями."

#: framework/premium/static/js/footer/CreateFooter.js:72
msgid "Footer name"
msgstr "Название подвала"

#: framework/premium/static/js/footer/EditConditions.js:100
msgid "Add one or more conditions in order to display your footer."
msgstr "Добавьте одно или несколько условий для отображения подвала."

#: framework/premium/static/js/footer/EditConditions.js:84
#: static/js/header/EditConditions.js:88
msgid "Add/Edit Conditions"
msgstr "Создать/изменить условия"

#: framework/premium/static/js/footer/PanelsManager.js:169
msgid "Remove footer"
msgstr "Удалить подвал"

#: framework/premium/static/js/footer/PanelsManager.js:193
msgid "Remove Footer"
msgstr "Удаление подвала"

#: framework/premium/static/js/footer/PanelsManager.js:196
msgid "You are about to remove a custom footer, are you sure you want to continue?"
msgstr "Вы собираетесь удалить пользовательский подвал. Уверены, что хотите продолжить?"

#: framework/premium/static/js/footer/PanelsManager.js:212
#: framework/premium/static/js/hooks/CodeEditor.js:189
#: static/js/header/PanelsManager.js:217
#: static/js/options/CustomizerOptionsManager.js:463
msgid "Cancel"
msgstr "Отмена"

#: framework/premium/static/js/footer/PanelsManager.js:228
#: static/js/header/PanelsManager.js:233
msgid "Confirm"
msgstr "Подтверждаю"

#: framework/premium/static/js/footer/PanelsManager.js:68
#: static/js/header/PanelsManager.js:74
#: static/js/options/DisplayCondition.js:61
msgid "Edit Conditions"
msgstr "Изменить условия"

#. translators: %s: PHP version
#: blocksy-companion.php:182
msgid "Blocksy requires PHP version %s+, plugin is currently NOT RUNNING."
msgstr "Blocksy требует PHP версии %s+, плагин сейчас НЕ РАБОТАЕТ."

#. translators: %s: WordPress version
#: blocksy-companion.php:193
msgid "Blocksy requires WordPress version %s+. Because you are using an earlier version, the plugin is currently NOT RUNNING."
msgstr "Blocksy требуется WordPress версии %s+. Поскольку здесь установлена более ранняя версия, плагин сейчас НЕ РАБОТАЕТ."

#: framework/premium/extensions/adobe-typekit/extension.php:46
msgid "Adobe Typekit"
msgstr "Adobe Typekit"

#: framework/premium/extensions/custom-fonts/extension.php:154
msgid "Custom Fonts"
msgstr "Произвольные шрифты"

#: framework/premium/extensions/local-google-fonts/extension.php:122
msgid "Local Google Fonts"
msgstr "Локальные шрифты Google"

#: framework/theme-integration.php:220,
#: framework/features/blocks/share-box/options.php:19
msgid "Facebook"
msgstr "Facebook"

#: framework/theme-integration.php:222,
#: framework/features/blocks/share-box/options.php:37
msgid "LinkedIn"
msgstr "LinkedIn"

#: framework/theme-integration.php:223
msgid "Dribbble"
msgstr "Dribbble"

#: framework/theme-integration.php:224
msgid "Instagram"
msgstr "Instagram"

#: framework/theme-integration.php:225,
#: framework/features/blocks/share-box/options.php:31
msgid "Pinterest"
msgstr "Pinterest"

#: framework/theme-integration.php:226
msgid "WordPress"
msgstr "WordPress"

#: framework/theme-integration.php:227
msgid "GitHub"
msgstr "GitHub"

#: framework/theme-integration.php:228,
#: framework/features/blocks/about-me/options.php:86,
#: framework/premium/features/content-blocks/options/archive.php:74
msgid "Medium"
msgstr "Medium"

#: framework/theme-integration.php:229,
#: framework/premium/features/media-video/options.php:14
msgid "YouTube"
msgstr "YouTube"

#: framework/theme-integration.php:230,
#: framework/premium/features/media-video/options.php:15
msgid "Vimeo"
msgstr "Vimeo"

#: framework/theme-integration.php:231,
#: framework/features/blocks/share-box/options.php:55
msgid "VKontakte"
msgstr "VKontakte"

#: framework/theme-integration.php:232,
#: framework/features/blocks/share-box/options.php:61
msgid "Odnoklassniki"
msgstr "Odnoklassniki"

#: framework/theme-integration.php:233
msgid "TikTok"
msgstr "TikTok"

#: framework/theme-integration.php:278
msgid "Companion"
msgstr "Компаньон"

#: framework/theme-integration.php:296
msgid "PRO"
msgstr "Про"

#: framework/features/account-auth.php:119,
#: framework/features/account-auth.php:267
msgid "Check your email"
msgstr "Проверьте свою почту"

#: framework/features/account-auth.php:273
msgid "Registration Form"
msgstr "Форма регистрации"

#: framework/features/account-auth.php:274
msgid "Register For This Site"
msgstr "Зарегистрируйтесь на этом сайте"

#: framework/features/conditions/rules/cpt.php:17
msgid "%s Single"
msgstr "%s единичный"

#: framework/features/conditions/rules/cpt.php:27
msgid "%s Archive"
msgstr "Архив %s"

#: framework/features/conditions/rules/cpt.php:40
msgid "%s %s Taxonomy"
msgstr "Таксономия %s %s"

#: framework/features/conditions/rules/basic.php:3
msgid "Entire Website"
msgstr "Весь сайт"

#: framework/features/conditions/rules/basic.php:57
msgid "Basic"
msgstr "Основное"

#: framework/extensions/trending/customizer.php:4,
#: framework/features/blocks/query/block.php:19,
#: framework/features/blocks/search/options.php:6,
#: framework/features/conditions/rules/posts.php:33,
#: framework/premium/features/premium-header/items/search-input/options.php:4
msgid "Posts"
msgstr "Записи"

#: framework/features/conditions/rules/posts.php:27,
#: framework/premium/features/content-blocks/hooks-manager.php:440,
#: framework/premium/features/content-blocks/hooks-manager.php:448,
#: framework/premium/features/content-blocks/hooks-manager.php:456,
#: framework/premium/features/content-blocks/hooks-manager.php:463,
#: framework/premium/features/content-blocks/hooks-manager.php:470,
#: framework/premium/features/content-blocks/hooks-manager.php:478
msgid "Single Post"
msgstr "Отдельная запись"

#: framework/features/conditions/rules/posts.php:15
msgid "Post Categories"
msgstr "Рубрики записей"

#: framework/features/conditions/rules/posts.php:20
msgid "Post Tags"
msgstr "Метки записей"

#: framework/features/blocks/search/options.php:7,
#: framework/features/conditions/rules/pages.php:52,
#: framework/premium/features/premium-header/items/search-input/options.php:5
msgid "Pages"
msgstr "Страницы"

#: framework/features/conditions/rules/pages.php:8
msgid "Single Page"
msgstr "Отдельная страница"

#: framework/features/conditions/rules/specific.php:48
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:69
msgid "Specific"
msgstr "Определённая"

#: framework/features/conditions/rules/specific.php:10
msgid "Post ID"
msgstr "ID записи"

#: framework/features/conditions/rules/specific.php:15
msgid "Page ID"
msgstr "ID страницы"

#: framework/features/conditions/rules/specific.php:20
#: static/js/options/ConditionsManager/PostIdPicker.js:79
msgid "Custom Post Type ID"
msgstr "ID произвольного типа записи"

#: framework/features/conditions/rules/specific.php:38,
#: framework/features/conditions/rules/woo.php:65
msgid "Taxonomy ID"
msgstr "ID таксономии"

#: framework/features/conditions/rules/specific.php:25
msgid "Post with Taxonomy ID"
msgstr "Запись с ID таксономии"

#: framework/features/conditions/rules/pages.php:15
msgid "404"
msgstr "404"

#: framework/features/blocks/search/options.php:60,
#: framework/features/blocks/search/options.php:66,
#: framework/features/blocks/search/view.php:80,
#: framework/features/blocks/search/view.php:261,
#: framework/features/conditions/rules/pages.php:22,
#: framework/premium/features/premium-header/items/search-input/options.php:48,
#: framework/premium/features/premium-header/items/search-input/view.php:126
#: static/js/editor/blocks/query/edit/PatternSelectionModal.js:92
#: static/js/editor/blocks/search/Preview.js:23
#: static/js/editor/blocks/tax-query/edit/PatternSelectionModal.js:78
msgid "Search"
msgstr "Поиск"

#: framework/features/conditions/rules/pages.php:27
msgid "Blog"
msgstr "Блог"

#: framework/features/conditions/rules/pages.php:33
msgid "Front Page"
msgstr "Внешняя страница"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:70
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:157
msgid "Author"
msgstr "Автор"

#: framework/features/conditions/rules/user-auth.php:20
msgid "User Auth"
msgstr "Авторизация пользователя"

#: framework/features/conditions/rules/user-auth.php:24
msgid "User Logged In"
msgstr "Авторизованные пользователи"

#: framework/features/conditions/rules/user-auth.php:29
msgid "User Logged Out"
msgstr "Неавторизованные пользователи"

#: framework/features/conditions/rules/user-auth.php:34
msgid "User Role"
msgstr "Роли пользователей"

#: framework/premium/features/content-blocks/options/404.php:19,
#: framework/premium/features/content-blocks/options/header.php:19,
#: framework/premium/features/content-blocks/options/hook.php:24,
#: framework/premium/features/content-blocks/options/nothing_found.php:19,
#: framework/premium/features/content-blocks/options/popup.php:25
msgid "Other"
msgstr "Прочее"

#: framework/features/conditions-manager.php:307
msgid "Language"
msgstr "Язык"

#: framework/features/demo-install.php:98
msgid "Your PHP installation doesn't have support for XML. Please install the <i>xml</i> or <i>simplexml</i> PHP extension in order to be able to install starter sites. You might need to contact your hosting provider to assist you in doing so."
msgstr "Ваша установка PHP не поддерживает XML. Установите расширение PHP <i>XML</i> или <i>simplexml</i>, чтобы иметь возможность устанавливать стартовые сайты. Возможно, вам потребуется связаться с вашим хостинг-провайдером, чтобы он помог вам в этом."

#: framework/features/dynamic-css.php:48
msgid "Dynamic CSS Output"
msgstr "Динамический вывод CSS"

#: framework/features/dynamic-css.php:52
msgid "The strategy of outputting the dynamic CSS. File - all the CSS code will be placed in a static file, otherwise it will be placed inline in head."
msgstr "Стратегия вывода динамического CSS. Файл - весь код CSS будет помещен в статический файл, в противном случае он будет помещен в заголовок."

#: framework/features/dynamic-css.php:54
msgid "File"
msgstr "Файлом"

#: framework/features/dynamic-css.php:55,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:67,
#: framework/premium/features/premium-header/items/language-switcher/options.php:165
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:159
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:204
msgid "Inline"
msgstr "Встроенный"

#: framework/features/google-analytics.php:69
msgid "Google Analytics v4"
msgstr "Google Analytics v4"

#: framework/features/google-analytics.php:74
msgid "Link your Google Analytics 4 tracking ID. More info and instructions can be found %shere%s."
msgstr "Подключите свой ID Google Analytics 4. Более подробную информацию и инструкции можно найти %sздесь%s."

#: framework/features/opengraph-meta-data.php:13
msgid "OpenGraph Meta Data"
msgstr "Метаданные Opengraph"

#: framework/features/opengraph-meta-data.php:17
msgid "Enable the OpenGraph rich meta data features for your website."
msgstr "Функционал расширенных метаданных OpenGraph."

#: framework/features/opengraph-meta-data.php:26
msgid "Facebook Page URL"
msgstr "URL страницы Facebook"

#: framework/features/opengraph-meta-data.php:33
msgid "Facebook App ID"
msgstr "Facebook App ID"

#: framework/features/opengraph-meta-data.php:40
msgid "Twitter Username"
msgstr "Имя пользователя Twitter"

#: framework/extensions/cookies-consent/config.php:6
msgid "Display a cookie acceptance box in order to comply with the privacy regulations in your country."
msgstr "Отображайте блок согласия на cookie, чтобы соответствовать правилам конфиденциальности в вашей стране."

#: framework/extensions/cookies-consent/customizer.php:15,
#: framework/extensions/newsletter-subscribe/customizer.php:12,
#: framework/extensions/product-reviews/extension.php:382,
#: framework/extensions/product-reviews/metabox.php:6,
#: framework/extensions/trending/customizer.php:173,
#: framework/features/header/header-options.php:6,
#: framework/premium/extensions/mega-menu/options.php:6,
#: framework/premium/extensions/shortcuts/customizer.php:761,
#: framework/features/header/items/account/options.php:330,
#: framework/premium/features/content-blocks/options/404.php:55,
#: framework/premium/features/content-blocks/options/archive.php:122,
#: framework/premium/features/content-blocks/options/header.php:73,
#: framework/premium/features/content-blocks/options/hook.php:182,
#: framework/premium/features/content-blocks/options/maintenance.php:52,
#: framework/premium/features/content-blocks/options/nothing_found.php:73,
#: framework/premium/features/content-blocks/options/popup.php:30,
#: framework/premium/features/content-blocks/options/single.php:54,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:6,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:35,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:279,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:520,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:41,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:120,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:378,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:20,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:66,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:11,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:10,
#: framework/premium/features/premium-header/items/contacts/options.php:45,
#: framework/premium/features/premium-header/items/content-block/options.php:5,
#: framework/premium/features/premium-header/items/language-switcher/options.php:278,
#: framework/premium/features/premium-header/items/search-input/options.php:40,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:19,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:163,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:63,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:5,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:5
#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:352
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:111
#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:104
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:43
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:38
msgid "General"
msgstr "Общее"

#: framework/extensions/cookies-consent/customizer.php:28,
#: framework/premium/extensions/shortcuts/customizer.php:773,
#: framework/features/header/items/account/options.php:447,
#: framework/features/header/items/account/options.php:821,
#: framework/features/header/items/account/options.php:909,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:23,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:48,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:375,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:31,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:56,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:101,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:55,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:56
msgid "Type 1"
msgstr "Тип 1"

#: framework/extensions/cookies-consent/customizer.php:33,
#: framework/premium/extensions/shortcuts/customizer.php:778,
#: framework/features/header/items/account/options.php:457,
#: framework/features/header/items/account/options.php:831,
#: framework/features/header/items/account/options.php:919,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:28,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:53,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:376,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:36,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:61,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:106,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:60,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:61
msgid "Type 2"
msgstr "Тип 2"

#: framework/extensions/cookies-consent/customizer.php:40
msgid "Cookie period"
msgstr "Период использования cookie"

#: framework/extensions/cookies-consent/customizer.php:48
msgid "One hour"
msgstr "Один час"

#: framework/extensions/cookies-consent/customizer.php:49
msgid "One day"
msgstr "Один день"

#: framework/extensions/cookies-consent/customizer.php:50
msgid "One week"
msgstr "Одна неделя"

#: framework/extensions/cookies-consent/customizer.php:51
msgid "One month"
msgstr "Один месяц"

#: framework/extensions/cookies-consent/customizer.php:52
msgid "Three months"
msgstr "Три месяца"

#: framework/extensions/cookies-consent/customizer.php:53
msgid "Six months"
msgstr "Шесть месяцев"

#: framework/extensions/cookies-consent/customizer.php:54
msgid "One year"
msgstr "Один год"

#: framework/extensions/cookies-consent/customizer.php:55
msgid "Forever"
msgstr "Неограничен"

#: framework/extensions/cookies-consent/customizer.php:62,
#: framework/features/blocks/contact-info/options.php:87,
#: framework/features/blocks/contact-info/options.php:152,
#: framework/features/blocks/contact-info/options.php:215,
#: framework/features/blocks/contact-info/options.php:278,
#: framework/features/blocks/contact-info/options.php:341,
#: framework/features/blocks/contact-info/options.php:404,
#: framework/features/blocks/contact-info/options.php:467,
#: framework/premium/features/content-blocks/hooks-manager.php:267,
#: framework/premium/features/content-blocks/hooks-manager.php:275,
#: framework/premium/features/content-blocks/hooks-manager.php:283,
#: framework/premium/features/content-blocks/hooks-manager.php:291,
#: framework/premium/features/premium-header/items/contacts/options.php:97,
#: framework/premium/features/premium-header/items/contacts/options.php:142,
#: framework/premium/features/premium-header/items/contacts/options.php:187,
#: framework/premium/features/premium-header/items/contacts/options.php:232,
#: framework/premium/features/premium-header/items/contacts/options.php:278,
#: framework/premium/features/premium-header/items/contacts/options.php:323,
#: framework/premium/features/premium-header/items/contacts/options.php:368
msgid "Content"
msgstr "Содержимое"

#: framework/extensions/cookies-consent/customizer.php:64,
#: framework/extensions/cookies-consent/helpers.php:7
msgid "We use cookies to ensure that we give you the best experience on our website."
msgstr "Мы используем cookie, чтобы обеспечить максимальное удобство использования нашего веб-сайта."

#: framework/features/blocks/search/options.php:58
msgid "Button Text"
msgstr "Текст кнопки"

#: framework/extensions/cookies-consent/customizer.php:80,
#: framework/extensions/cookies-consent/helpers.php:10
msgid "Accept"
msgstr "Принимаю"

#: framework/extensions/cookies-consent/customizer.php:142,
#: framework/extensions/newsletter-subscribe/customizer.php:170,
#: framework/extensions/product-reviews/extension.php:422,
#: framework/extensions/trending/customizer.php:582,
#: framework/features/header/header-options.php:203,
#: framework/premium/extensions/mega-menu/options.php:567,
#: framework/premium/extensions/shortcuts/customizer.php:1032,
#: framework/features/header/items/account/options.php:1107,
#: framework/premium/features/content-blocks/options/404.php:119,
#: framework/premium/features/content-blocks/options/archive.php:196,
#: framework/premium/features/content-blocks/options/header.php:137,
#: framework/premium/features/content-blocks/options/hook.php:246,
#: framework/premium/features/content-blocks/options/maintenance.php:116,
#: framework/premium/features/content-blocks/options/nothing_found.php:137,
#: framework/premium/features/content-blocks/options/popup.php:517,
#: framework/premium/features/content-blocks/options/single.php:128,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:147,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:178,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:169,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:406,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:661,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:289,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:458,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:108,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:253,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:570,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:124,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:132,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:190,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:66,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:245,
#: framework/premium/features/premium-header/items/contacts/options.php:571,
#: framework/premium/features/premium-header/items/content-block/options.php:36,
#: framework/premium/features/premium-header/items/language-switcher/options.php:284,
#: framework/premium/features/premium-header/items/search-input/options.php:260,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:66,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:100,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:152,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:154
msgid "Design"
msgstr "Дизайн"

#: framework/extensions/cookies-consent/customizer.php:203,
#: framework/extensions/cookies-consent/customizer.php:270,
#: framework/premium/extensions/mega-menu/options.php:879,
#: framework/premium/extensions/shortcuts/customizer.php:1058,
#: framework/features/header/items/account/options.php:1518,
#: framework/features/header/items/account/options.php:1902,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:202,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:233,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:262,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:195,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:113,
#: framework/premium/features/premium-header/items/contacts/options.php:585,
#: framework/premium/features/premium-header/items/contacts/options.php:614,
#: framework/premium/features/premium-header/items/contacts/options.php:655,
#: framework/premium/features/premium-header/items/contacts/options.php:694,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:111,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:222,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:251,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:281,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:310,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:52,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:81,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:111,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:140,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:217,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:261,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:303,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:209,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:240,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:269
msgid "Font Color"
msgstr "Цвет шрифта"

#: framework/extensions/cookies-consent/customizer.php:164,
#: framework/extensions/cookies-consent/customizer.php:191,
#: framework/extensions/cookies-consent/customizer.php:220,
#: framework/extensions/cookies-consent/customizer.php:251,
#: framework/extensions/cookies-consent/customizer.php:287,
#: framework/extensions/cookies-consent/customizer.php:316,
#: framework/extensions/newsletter-subscribe/customizer.php:188,
#: framework/extensions/newsletter-subscribe/customizer.php:213,
#: framework/extensions/newsletter-subscribe/customizer.php:245,
#: framework/extensions/newsletter-subscribe/customizer.php:276,
#: framework/extensions/newsletter-subscribe/customizer.php:313,
#: framework/extensions/newsletter-subscribe/customizer.php:345,
#: framework/extensions/trending/customizer.php:610,
#: framework/extensions/trending/customizer.php:673,
#: framework/extensions/trending/customizer.php:726,
#: framework/extensions/trending/customizer.php:766,
#: framework/extensions/trending/customizer.php:798,
#: framework/extensions/trending/customizer.php:848,
#: framework/extensions/trending/customizer.php:889,
#: framework/premium/extensions/mega-menu/options.php:845,
#: framework/premium/extensions/mega-menu/options.php:892,
#: framework/premium/extensions/mega-menu/options.php:911,
#: framework/premium/extensions/shortcuts/customizer.php:1076,
#: framework/premium/extensions/shortcuts/customizer.php:1110,
#: framework/premium/extensions/shortcuts/customizer.php:1142,
#: framework/features/header/items/account/options.php:1198,
#: framework/features/header/items/account/options.php:1242,
#: framework/features/header/items/account/options.php:1284,
#: framework/features/header/items/account/options.php:1389,
#: framework/features/header/items/account/options.php:1432,
#: framework/features/header/items/account/options.php:1473,
#: framework/features/header/items/account/options.php:1539,
#: framework/features/header/items/account/options.php:1572,
#: framework/features/header/items/account/options.php:1610,
#: framework/features/header/items/account/options.php:1653,
#: framework/features/header/items/account/options.php:1758,
#: framework/features/header/items/account/options.php:1801,
#: framework/features/header/items/account/options.php:1852,
#: framework/features/header/items/account/options.php:1974,
#: framework/premium/features/content-blocks/options/popup.php:632,
#: framework/premium/features/content-blocks/options/popup.php:663,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:219,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:250,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:279,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:345,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:376,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:405,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1430,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:220,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:234,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:270,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:196,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:232,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:433,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:463,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:797,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:480,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:511,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:126,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:349,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:383,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:422,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:151,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:180,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:159,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:189,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:209,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:239,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:269,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:311,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:341,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:381,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:409,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:27,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:50,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:215,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:231,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:267,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:308,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:82,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:275,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:319,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:363,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:403,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:442,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:487,
#: framework/premium/features/premium-header/items/contacts/options.php:797,
#: framework/premium/features/premium-header/items/contacts/options.php:828,
#: framework/premium/features/premium-header/items/contacts/options.php:857,
#: framework/premium/features/premium-header/items/contacts/options.php:956,
#: framework/premium/features/premium-header/items/contacts/options.php:994,
#: framework/premium/features/premium-header/items/contacts/options.php:1032,
#: framework/premium/features/premium-header/items/search-input/options.php:313,
#: framework/premium/features/premium-header/items/search-input/options.php:345,
#: framework/premium/features/premium-header/items/search-input/options.php:375,
#: framework/premium/features/premium-header/items/search-input/options.php:443,
#: framework/premium/features/premium-header/items/search-input/options.php:475,
#: framework/premium/features/premium-header/items/search-input/options.php:505,
#: framework/premium/features/premium-header/items/search-input/options.php:573,
#: framework/premium/features/premium-header/items/search-input/options.php:605,
#: framework/premium/features/premium-header/items/search-input/options.php:635,
#: framework/premium/features/premium-header/items/search-input/options.php:708,
#: framework/premium/features/premium-header/items/search-input/options.php:738,
#: framework/premium/features/premium-header/items/search-input/options.php:768,
#: framework/premium/features/premium-header/items/search-input/options.php:851,
#: framework/premium/features/premium-header/items/search-input/options.php:879,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:84,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:105,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:128,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:154,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:268,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:298,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:327,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:399,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:430,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:459,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:98,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:128,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:157,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:241,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:285,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:327,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:403,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:438,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:471,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:226,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:257,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:286,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:353,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:384,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:413
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:261
#: static/js/editor/blocks/about-me/Edit.js:189
#: static/js/editor/blocks/contact-info/Edit.js:203
#: static/js/editor/blocks/search/Edit.js:409
#: static/js/editor/blocks/share-box/Edit.js:152
#: static/js/editor/blocks/socials/Edit.js:152
msgid "Initial"
msgstr "Исходный"

#: framework/extensions/cookies-consent/customizer.php:170,
#: framework/extensions/cookies-consent/customizer.php:226,
#: framework/extensions/cookies-consent/customizer.php:257,
#: framework/extensions/cookies-consent/customizer.php:292,
#: framework/extensions/cookies-consent/customizer.php:321,
#: framework/extensions/newsletter-subscribe/customizer.php:219,
#: framework/extensions/newsletter-subscribe/customizer.php:350,
#: framework/extensions/trending/customizer.php:678,
#: framework/extensions/trending/customizer.php:731,
#: framework/extensions/trending/customizer.php:772,
#: framework/extensions/trending/customizer.php:804,
#: framework/extensions/trending/customizer.php:895,
#: framework/premium/extensions/mega-menu/options.php:850,
#: framework/premium/extensions/shortcuts/customizer.php:1082,
#: framework/premium/extensions/shortcuts/customizer.php:1116,
#: framework/premium/extensions/shortcuts/customizer.php:1148,
#: framework/features/header/items/account/options.php:1207,
#: framework/features/header/items/account/options.php:1250,
#: framework/features/header/items/account/options.php:1292,
#: framework/features/header/items/account/options.php:1398,
#: framework/features/header/items/account/options.php:1440,
#: framework/features/header/items/account/options.php:1481,
#: framework/features/header/items/account/options.php:1545,
#: framework/features/header/items/account/options.php:1764,
#: framework/features/header/items/account/options.php:1810,
#: framework/features/header/items/account/options.php:1861,
#: framework/features/header/items/account/options.php:1979,
#: framework/premium/features/content-blocks/options/popup.php:638,
#: framework/premium/features/content-blocks/options/popup.php:669,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:225,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:255,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:284,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:351,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:381,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:410,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1436,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:802,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:486,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:517,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:354,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:389,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:428,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:245,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:275,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:317,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:347,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:386,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:414,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:63,
#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:56,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:236,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:273,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:314,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:281,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:325,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:369,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:409,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:448,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:493,
#: framework/premium/features/premium-header/items/contacts/options.php:803,
#: framework/premium/features/premium-header/items/contacts/options.php:833,
#: framework/premium/features/premium-header/items/contacts/options.php:862,
#: framework/premium/features/premium-header/items/contacts/options.php:961,
#: framework/premium/features/premium-header/items/contacts/options.php:999,
#: framework/premium/features/premium-header/items/contacts/options.php:1037,
#: framework/premium/features/premium-header/items/search-input/options.php:856,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:133,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:250,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:293,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:335,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:409,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:443,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:476,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:232,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:262,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:291,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:359,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:389,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:418
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:270
#: static/js/editor/blocks/about-me/Edit.js:198
#: static/js/editor/blocks/contact-info/Edit.js:212
#: static/js/editor/blocks/search/Edit.js:368
#: static/js/editor/blocks/share-box/Edit.js:161
#: static/js/editor/blocks/socials/Edit.js:161
msgid "Hover"
msgstr "При наведении"

#: framework/extensions/newsletter-subscribe/customizer.php:328,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:253
msgid "Button Color"
msgstr "Цвет кнопки"

#: framework/extensions/cookies-consent/customizer.php:178,
#: framework/extensions/cookies-consent/customizer.php:234,
#: framework/extensions/cookies-consent/customizer.php:299,
#: framework/premium/extensions/mega-menu/options.php:899,
#: framework/features/header/items/account/options.php:1829,
#: framework/features/header/items/account/options.php:1953,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:134,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:405,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:289,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:421,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:92,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:141
msgid "Background Color"
msgstr "Цвет фона"

#: framework/extensions/cookies-consent/customizer.php:98
msgid "Maximum Width"
msgstr "Максимальная ширина"

#: framework/extensions/cookies-consent/customizer.php:119
msgid "I accept the %sPrivacy Policy%s*"
msgstr "Я принимаю %sПолитику конфиденциальности%s*"

#: framework/extensions/cookies-consent/customizer.php:123
msgid "This text will appear under each comment form and subscribe form."
msgstr "Данный текст будет отображаться под каждой формой комментария и формой подписки."

#: framework/extensions/cookies-consent/helpers.php:55
msgid "I accept the %sPrivacy Policy%s"
msgstr "Я принимаю %sПолитику конфиденциальности%s"

#: framework/features/blocks/about-me/options.php:128
msgid "Customizer"
msgstr "Настройщик"

#: framework/extensions/newsletter-subscribe/config.php:6
msgid "Easily capture new leads for your newsletter with the help of a widget, shortcode or even a block inserted on your pages or posts."
msgstr "Легко собирайте контакты новых потенциальных клиентов для своей рассылки с помощью виджета, шорткода или даже блока, вставленного в ваши страницы или записи."

#: framework/extensions/newsletter-subscribe/customizer.php:4
msgid "Subscribe Form"
msgstr "Форма подписки"

#: framework/extensions/newsletter-subscribe/customizer.php:18,
#: framework/features/blocks/about-me/options.php:15,
#: framework/features/blocks/contact-info/options.php:34,
#: framework/features/blocks/contact-info/options.php:80,
#: framework/features/blocks/contact-info/options.php:145,
#: framework/features/blocks/contact-info/options.php:208,
#: framework/features/blocks/contact-info/options.php:271,
#: framework/features/blocks/contact-info/options.php:334,
#: framework/features/blocks/contact-info/options.php:397,
#: framework/features/blocks/contact-info/options.php:460,
#: framework/features/blocks/share-box/options.php:14,
#: framework/features/blocks/socials/options.php:14,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:582,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:327,
#: framework/premium/features/premium-header/items/contacts/options.php:87,
#: framework/premium/features/premium-header/items/contacts/options.php:132,
#: framework/premium/features/premium-header/items/contacts/options.php:177,
#: framework/premium/features/premium-header/items/contacts/options.php:222,
#: framework/premium/features/premium-header/items/contacts/options.php:268,
#: framework/premium/features/premium-header/items/contacts/options.php:313,
#: framework/premium/features/premium-header/items/contacts/options.php:358
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:45
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:125
msgid "Title"
msgstr "Заголовок"

#: framework/extensions/newsletter-subscribe/customizer.php:20,
#: framework/extensions/newsletter-subscribe/helpers.php:21,
#: framework/extensions/newsletter-subscribe/helpers.php:69
msgid "Newsletter Updates"
msgstr "Обновления рассылки"

#: framework/extensions/newsletter-subscribe/customizer.php:26,
#: framework/features/blocks/about-me/options.php:64,
#: framework/features/blocks/dynamic-data/options.php:142,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:78,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:159,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:248
msgid "Description"
msgstr "Описание"

#: framework/extensions/newsletter-subscribe/helpers.php:72
msgid "Enter your email address below to subscribe to our newsletter"
msgstr "Введите ниже свой Email, чтобы подписаться на нашу рассылку"

#: framework/extensions/newsletter-subscribe/customizer.php:41,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:34
msgid "List Source"
msgstr "Источник списка"

#: framework/extensions/newsletter-subscribe/customizer.php:48,
#: framework/extensions/product-reviews/metabox.php:17,
#: framework/extensions/trending/customizer.php:232,
#: framework/extensions/trending/customizer.php:306,
#: framework/extensions/trending/customizer.php:448,
#: framework/features/header/header-options.php:76,
#: framework/premium/features/premium-header.php:322,
#: framework/premium/features/socials.php:20,
#: framework/premium/features/socials.php:48,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:40,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:81,
#: framework/features/blocks/contact-info/options.php:110,
#: framework/features/blocks/contact-info/options.php:175,
#: framework/features/blocks/contact-info/options.php:238,
#: framework/features/blocks/contact-info/options.php:301,
#: framework/features/blocks/contact-info/options.php:364,
#: framework/features/blocks/contact-info/options.php:427,
#: framework/features/blocks/contact-info/options.php:490,
#: framework/features/header/items/account/options.php:385,
#: framework/features/header/items/account/options.php:766,
#: framework/premium/features/content-blocks/options/hook.php:166,
#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:19,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:12,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:17,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:17
msgid "Default"
msgstr "По умолчанию"

#: framework/extensions/newsletter-subscribe/customizer.php:49,
#: framework/extensions/trending/customizer.php:236,
#: framework/premium/features/premium-header.php:323,
#: framework/premium/features/socials.php:21,
#: framework/premium/features/socials.php:49,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:41,
#: framework/features/blocks/about-me/options.php:27,
#: framework/features/blocks/about-me/options.php:194,
#: framework/features/blocks/contact-info/options.php:111,
#: framework/features/blocks/contact-info/options.php:176,
#: framework/features/blocks/contact-info/options.php:239,
#: framework/features/blocks/contact-info/options.php:302,
#: framework/features/blocks/contact-info/options.php:365,
#: framework/features/blocks/contact-info/options.php:428,
#: framework/features/blocks/contact-info/options.php:491,
#: framework/features/blocks/dynamic-data/options.php:64,
#: framework/features/blocks/share-box/options.php:148,
#: framework/features/blocks/socials/options.php:100,
#: framework/features/conditions/rules/custom.php:33,
#: framework/premium/extensions/shortcuts/customizer.php:260,
#: framework/premium/extensions/shortcuts/customizer.php:287,
#: framework/features/header/items/account/options.php:25,
#: framework/features/header/items/account/options.php:389,
#: framework/features/header/items/account/options.php:770,
#: framework/premium/features/content-blocks/options/archive.php:75,
#: framework/premium/features/content-blocks/options/hook.php:168,
#: framework/premium/features/content-blocks/options/popup.php:287,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:616,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:196,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:188,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:41,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:843,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:18,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:18
#: static/js/editor/blocks/dynamic-data/utils.js:10
msgid "Custom"
msgstr "Произвольный"

#: framework/extensions/newsletter-subscribe/customizer.php:61,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:50
#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:199
msgid "List ID"
msgstr "ID списка"

#: framework/extensions/newsletter-subscribe/customizer.php:79,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:109
msgid "Name Field"
msgstr "Поле Имя"

#: framework/extensions/newsletter-subscribe/customizer.php:117,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:121
msgid "Name Label"
msgstr "Ярлык имени"

#: framework/extensions/newsletter-subscribe/customizer.php:119,
#: framework/extensions/newsletter-subscribe/extension.php:208,
#: framework/extensions/newsletter-subscribe/helpers.php:37,
#: framework/extensions/newsletter-subscribe/helpers.php:81,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:122,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:147
msgid "Your name"
msgstr "Ваше имя"

#: framework/extensions/newsletter-subscribe/customizer.php:128,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:136
msgid "Mail Label"
msgstr "Ярлык Email'а"

#: framework/extensions/newsletter-subscribe/customizer.php:130,
#: framework/extensions/newsletter-subscribe/extension.php:209,
#: framework/extensions/newsletter-subscribe/helpers.php:41,
#: framework/extensions/newsletter-subscribe/helpers.php:82
msgid "Your email"
msgstr "Ваш Email"

#: framework/extensions/newsletter-subscribe/customizer.php:137,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:142,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:414
msgid "Button Label"
msgstr "Ярлык кнопки"

#: framework/extensions/newsletter-subscribe/customizer.php:139,
#: framework/extensions/newsletter-subscribe/extension.php:203,
#: framework/extensions/newsletter-subscribe/helpers.php:31,
#: framework/extensions/newsletter-subscribe/helpers.php:76,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:143,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:21
msgid "Subscribe"
msgstr "Подписаться"

#: framework/extensions/newsletter-subscribe/customizer.php:149,
#: framework/extensions/trending/customizer.php:516,
#: framework/premium/extensions/shortcuts/customizer.php:48,
#: framework/premium/extensions/shortcuts/customizer.php:113,
#: framework/premium/extensions/shortcuts/customizer.php:178,
#: framework/premium/extensions/shortcuts/customizer.php:237,
#: framework/premium/extensions/shortcuts/customizer.php:326,
#: framework/premium/extensions/shortcuts/customizer.php:388,
#: framework/premium/extensions/shortcuts/customizer.php:447,
#: framework/premium/extensions/shortcuts/customizer.php:517,
#: framework/premium/extensions/shortcuts/customizer.php:581,
#: framework/premium/extensions/shortcuts/customizer.php:645,
#: framework/premium/extensions/shortcuts/customizer.php:709,
#: framework/premium/extensions/shortcuts/customizer.php:996,
#: framework/premium/features/content-blocks/options/header.php:167,
#: framework/premium/features/content-blocks/options/hook.php:300,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:155,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:423,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:43
msgid "Visibility"
msgstr "Видимость"

#: framework/extensions/newsletter-subscribe/customizer.php:160,
#: framework/extensions/trending/customizer.php:528,
#: framework/features/header/header-options.php:108,
#: framework/features/header/header-options.php:190,
#: framework/features/blocks/contact-info/options.php:24,
#: framework/features/blocks/search/options.php:168,
#: framework/premium/extensions/mega-menu/options.php:413,
#: framework/premium/extensions/shortcuts/customizer.php:62,
#: framework/premium/extensions/shortcuts/customizer.php:127,
#: framework/premium/extensions/shortcuts/customizer.php:192,
#: framework/premium/extensions/shortcuts/customizer.php:251,
#: framework/premium/extensions/shortcuts/customizer.php:341,
#: framework/premium/extensions/shortcuts/customizer.php:402,
#: framework/premium/extensions/shortcuts/customizer.php:461,
#: framework/premium/extensions/shortcuts/customizer.php:531,
#: framework/premium/extensions/shortcuts/customizer.php:595,
#: framework/premium/extensions/shortcuts/customizer.php:659,
#: framework/premium/extensions/shortcuts/customizer.php:723,
#: framework/premium/extensions/shortcuts/customizer.php:861,
#: framework/premium/extensions/shortcuts/customizer.php:918,
#: framework/premium/extensions/shortcuts/customizer.php:1010,
#: framework/features/header/items/account/options.php:532,
#: framework/features/header/items/account/options.php:995,
#: framework/premium/features/content-blocks/options/header.php:178,
#: framework/premium/features/content-blocks/options/hook.php:311,
#: framework/premium/features/content-blocks/options/popup.php:506,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:70,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:168,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:396,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:651,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:436,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:42,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:61,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:80,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:98,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:90,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:258,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:286,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:49,
#: framework/premium/features/premium-header/items/contacts/options.php:32,
#: framework/premium/features/premium-header/items/contacts/options.php:553,
#: framework/premium/features/premium-header/items/language-switcher/options.php:131,
#: framework/premium/features/premium-header/items/search-input/options.php:157,
#: framework/premium/features/premium-header/items/search-input/options.php:250,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:56,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:104,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:105
msgid "Desktop"
msgstr "Десктоп"

#: framework/extensions/newsletter-subscribe/customizer.php:161,
#: framework/extensions/trending/customizer.php:529,
#: framework/features/blocks/contact-info/options.php:25,
#: framework/features/blocks/search/options.php:169,
#: framework/premium/extensions/shortcuts/customizer.php:63,
#: framework/premium/extensions/shortcuts/customizer.php:128,
#: framework/premium/extensions/shortcuts/customizer.php:193,
#: framework/premium/extensions/shortcuts/customizer.php:252,
#: framework/premium/extensions/shortcuts/customizer.php:342,
#: framework/premium/extensions/shortcuts/customizer.php:403,
#: framework/premium/extensions/shortcuts/customizer.php:462,
#: framework/premium/extensions/shortcuts/customizer.php:532,
#: framework/premium/extensions/shortcuts/customizer.php:596,
#: framework/premium/extensions/shortcuts/customizer.php:660,
#: framework/premium/extensions/shortcuts/customizer.php:724,
#: framework/premium/extensions/shortcuts/customizer.php:862,
#: framework/premium/extensions/shortcuts/customizer.php:919,
#: framework/premium/extensions/shortcuts/customizer.php:1011,
#: framework/features/header/items/account/options.php:533,
#: framework/features/header/items/account/options.php:996,
#: framework/features/header/items/account/options.php:2069,
#: framework/premium/features/content-blocks/options/header.php:179,
#: framework/premium/features/content-blocks/options/hook.php:312,
#: framework/premium/features/content-blocks/options/popup.php:507,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:71,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:452,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:169,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:397,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:652,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:437,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:43,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:62,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:99,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:91,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:259,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:287,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:50,
#: framework/premium/features/premium-header/items/contacts/options.php:33,
#: framework/premium/features/premium-header/items/contacts/options.php:497,
#: framework/premium/features/premium-header/items/contacts/options.php:554,
#: framework/premium/features/premium-header/items/language-switcher/options.php:132,
#: framework/premium/features/premium-header/items/language-switcher/options.php:261,
#: framework/premium/features/premium-header/items/search-input/options.php:158,
#: framework/premium/features/premium-header/items/search-input/options.php:251,
#: framework/premium/features/premium-header/items/search-input/options.php:947,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:57,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:105,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:655,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:106,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:589
msgid "Tablet"
msgstr " Планшет"

#: framework/extensions/newsletter-subscribe/customizer.php:162,
#: framework/extensions/trending/customizer.php:530,
#: framework/features/header/header-options.php:110,
#: framework/features/header/header-options.php:192,
#: framework/features/blocks/contact-info/options.php:26,
#: framework/features/blocks/contact-info/options.php:202,
#: framework/features/blocks/search/options.php:170,
#: framework/premium/extensions/mega-menu/options.php:414,
#: framework/premium/extensions/shortcuts/customizer.php:64,
#: framework/premium/extensions/shortcuts/customizer.php:129,
#: framework/premium/extensions/shortcuts/customizer.php:194,
#: framework/premium/extensions/shortcuts/customizer.php:253,
#: framework/premium/extensions/shortcuts/customizer.php:343,
#: framework/premium/extensions/shortcuts/customizer.php:404,
#: framework/premium/extensions/shortcuts/customizer.php:463,
#: framework/premium/extensions/shortcuts/customizer.php:533,
#: framework/premium/extensions/shortcuts/customizer.php:597,
#: framework/premium/extensions/shortcuts/customizer.php:661,
#: framework/premium/extensions/shortcuts/customizer.php:725,
#: framework/premium/extensions/shortcuts/customizer.php:863,
#: framework/premium/extensions/shortcuts/customizer.php:920,
#: framework/premium/extensions/shortcuts/customizer.php:1012,
#: framework/features/header/items/account/options.php:534,
#: framework/features/header/items/account/options.php:997,
#: framework/features/header/items/account/options.php:2070,
#: framework/premium/features/content-blocks/options/header.php:180,
#: framework/premium/features/content-blocks/options/hook.php:313,
#: framework/premium/features/content-blocks/options/popup.php:508,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:72,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:453,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:170,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:398,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:653,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:438,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:44,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:63,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:82,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:100,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:92,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:260,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:288,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:51,
#: framework/premium/features/premium-header/items/contacts/options.php:34,
#: framework/premium/features/premium-header/items/contacts/options.php:172,
#: framework/premium/features/premium-header/items/contacts/options.php:498,
#: framework/premium/features/premium-header/items/contacts/options.php:555,
#: framework/premium/features/premium-header/items/language-switcher/options.php:133,
#: framework/premium/features/premium-header/items/language-switcher/options.php:262,
#: framework/premium/features/premium-header/items/search-input/options.php:159,
#: framework/premium/features/premium-header/items/search-input/options.php:252,
#: framework/premium/features/premium-header/items/search-input/options.php:948,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:58,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:106,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:656,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:107,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:590
msgid "Mobile"
msgstr "Смартфон"

#: framework/extensions/newsletter-subscribe/customizer.php:175,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:183,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:420,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:138,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:146
msgid "Title Color"
msgstr "Цвет заголовка"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:197
msgid "Description Color"
msgstr "Цвет описания"

#: framework/extensions/newsletter-subscribe/customizer.php:227,
#: framework/features/header/items/account/options.php:1553,
#: framework/premium/features/premium-header/items/search-input/options.php:266,
#: framework/premium/features/premium-header/items/search-input/options.php:295,
#: framework/premium/features/premium-header/items/search-input/options.php:327,
#: framework/premium/features/premium-header/items/search-input/options.php:357
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:128
#: static/js/editor/blocks/search/Edit.js:196
msgid "Input Font Color"
msgstr "Цвет шрифта поля"

#: framework/extensions/newsletter-subscribe/customizer.php:251,
#: framework/extensions/newsletter-subscribe/customizer.php:282,
#: framework/extensions/newsletter-subscribe/customizer.php:318,
#: framework/features/header/items/account/options.php:1579,
#: framework/features/header/items/account/options.php:1617,
#: framework/features/header/items/account/options.php:1663,
#: framework/premium/features/premium-header/items/search-input/options.php:319,
#: framework/premium/features/premium-header/items/search-input/options.php:350,
#: framework/premium/features/premium-header/items/search-input/options.php:380,
#: framework/premium/features/premium-header/items/search-input/options.php:449,
#: framework/premium/features/premium-header/items/search-input/options.php:480,
#: framework/premium/features/premium-header/items/search-input/options.php:510,
#: framework/premium/features/premium-header/items/search-input/options.php:579,
#: framework/premium/features/premium-header/items/search-input/options.php:610,
#: framework/premium/features/premium-header/items/search-input/options.php:640,
#: framework/premium/features/premium-header/items/search-input/options.php:713,
#: framework/premium/features/premium-header/items/search-input/options.php:743,
#: framework/premium/features/premium-header/items/search-input/options.php:773
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:210
#: static/js/editor/blocks/search/Edit.js:269
msgid "Focus"
msgstr "Фокус"

#: framework/extensions/newsletter-subscribe/customizer.php:259,
#: framework/features/header/items/account/options.php:1588,
#: framework/premium/features/premium-header/items/search-input/options.php:526,
#: framework/premium/features/premium-header/items/search-input/options.php:555,
#: framework/premium/features/premium-header/items/search-input/options.php:587,
#: framework/premium/features/premium-header/items/search-input/options.php:617
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:159
#: static/js/editor/blocks/search/Edit.js:223
msgid "Input Border Color"
msgstr "Цвет границы поля"

#: framework/extensions/newsletter-subscribe/customizer.php:296,
#: framework/features/header/items/account/options.php:1631,
#: framework/premium/features/premium-header/items/search-input/options.php:662,
#: framework/premium/features/premium-header/items/search-input/options.php:690,
#: framework/premium/features/premium-header/items/search-input/options.php:720,
#: framework/premium/features/premium-header/items/search-input/options.php:750
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:190
#: static/js/editor/blocks/search/Edit.js:250
msgid "Input Background Color"
msgstr "Цвет фона поля"

#: framework/extensions/newsletter-subscribe/customizer.php:357,
#: framework/extensions/trending/customizer.php:903,
#: framework/premium/extensions/shortcuts/customizer.php:1335,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:525,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:209
msgid "Container Background"
msgstr "Фон контейнера"

#: framework/extensions/newsletter-subscribe/customizer.php:373,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:193
msgid "Container Border"
msgstr "Граница контейнера"

#: framework/extensions/newsletter-subscribe/customizer.php:408,
#: framework/extensions/trending/customizer.php:919
msgid "Container Inner Spacing"
msgstr "Внутренние отступы контейнера"

#: framework/extensions/newsletter-subscribe/customizer.php:422,
#: framework/premium/extensions/shortcuts/customizer.php:1368,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:237
msgid "Container Border Radius"
msgstr "Скругление контейнера"

#: framework/extensions/newsletter-subscribe/extension.php:155
msgid "Disable Subscribe Form"
msgstr "Отключить форму подписки"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:253,
#: framework/features/blocks/about-me/options.php:58,
#: framework/features/header/items/account/options.php:578,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:167
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:128
msgid "Name"
msgstr "Имя"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:262,
#: framework/features/blocks/contact-info/options.php:391,
#: framework/features/blocks/dynamic-data/options.php:143,
#: framework/features/blocks/share-box/options.php:97,
#: framework/features/header/modal/register.php:47,
#: framework/premium/extensions/shortcuts/customizer.php:136,
#: framework/premium/extensions/shortcuts/customizer.php:162,
#: framework/premium/extensions/shortcuts/views/bar.php:48,
#: framework/premium/features/premium-header/items/contacts/options.php:308,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:168
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:23
msgid "Email"
msgstr "Email"

#: framework/extensions/product-reviews/extension.php:540,
#: framework/extensions/product-reviews/extension.php:541,
#: framework/extensions/product-reviews/extension.php:544,
#: framework/extensions/product-reviews/extension.php:546
msgid "Product Reviews"
msgstr "Обзоры товаров"

#: framework/extensions/product-reviews/config.php:6
msgid "This extension lets you easily create an affiliate marketing type of website by giving you options to create a personalized product review and use your affiliate links to direct your readers to the purchase page."
msgstr "Создавайте веб-сайты для партнерского маркетинга, публикуйте персонализированные обзоры товаров и вставляйте партнёрские ссылки, чтобы направлять своих читателей на страницу покупки."

#: framework/extensions/product-reviews/extension.php:318,
#: framework/extensions/product-reviews/views/single-top.php:139
msgid "Overall Score"
msgstr "Общий балл"

#: framework/extensions/product-reviews/extension.php:376
msgid "Review Summary"
msgstr "Итоги обзора"

#: framework/extensions/product-reviews/extension.php:387
msgid "Scores Box Width"
msgstr "Ширина блока очков"

#: framework/extensions/product-reviews/extension.php:397
msgid "Read More Button"
msgstr "Кнопка «Читать далее»"

#: framework/extensions/product-reviews/extension.php:408
msgid "Buy Now Button"
msgstr "Кнопка «Купить сейчас»"

#: framework/extensions/product-reviews/extension.php:256,
#: framework/extensions/product-reviews/extension.php:427
msgid "Star Rating Color"
msgstr "Цвет звёзд рейтинга"

#: framework/extensions/product-reviews/extension.php:274,
#: framework/extensions/product-reviews/extension.php:444,
#: framework/extensions/product-reviews/extension.php:472,
#: framework/extensions/product-reviews/extension.php:493,
#: framework/premium/extensions/mega-menu/options.php:855,
#: framework/features/header/items/account/options.php:1985,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:221,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:227,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:88,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:94,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:286,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:330,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:375,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:414,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:454,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:498
#: static/js/dashboard/screens/Extensions/Sidebar.js:120
msgid "Active"
msgstr "Активно"

#: framework/extensions/product-reviews/extension.php:280,
#: framework/extensions/product-reviews/extension.php:450
msgid "Inactive"
msgstr "Неактивно"

#: framework/extensions/product-reviews/extension.php:458
msgid "Overll Score Text"
msgstr "Текст опросного листа"

#: framework/extensions/product-reviews/extension.php:480
msgid "Overll Score Background"
msgstr "Фон опросного листа"

#: framework/extensions/product-reviews/extension.php:545
msgid "Product Review"
msgstr "Обзор товара"

#: framework/extensions/product-reviews/extension.php:547
msgid "Parent Product Review"
msgstr "Обзор родительского товара"

#: framework/extensions/product-reviews/extension.php:548
msgid "All Reviews"
msgstr "Все обзоры"

#: framework/extensions/product-reviews/extension.php:549
msgid "View Product Review"
msgstr "Просмотр обзора товара"

#: framework/extensions/product-reviews/extension.php:550
msgid "Add New Product Review"
msgstr "Добавить обзор товара"

#: framework/extensions/product-reviews/extension.php:551
msgid "Add New Review"
msgstr "Добавить новый"

#: framework/extensions/product-reviews/extension.php:552
msgid "Edit Product Review"
msgstr "Изменить обзор товара"

#: framework/extensions/product-reviews/extension.php:553
msgid "Update Product Review"
msgstr "Обновить обзор товара"

#: framework/extensions/product-reviews/extension.php:554
msgid "Search Product Review"
msgstr "Поиск обзора товара"

#: framework/extensions/product-reviews/extension.php:555
msgid "Not Found"
msgstr "Ничего не найдено"

#: framework/extensions/product-reviews/extension.php:556
msgid "Not found in Trash"
msgstr "В корзине не найдено"

#: framework/extensions/product-reviews/extension.php:590,
#: framework/extensions/product-reviews/extension.php:600,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:100,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:190
msgid "Categories"
msgstr "Рубрики"

#: framework/extensions/product-reviews/extension.php:591,
#: framework/extensions/trending/customizer.php:36,
#: framework/extensions/trending/customizer.php:117
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:88
msgid "Category"
msgstr "Рубрика"

#: framework/extensions/product-reviews/extension.php:592
msgid "Search Category"
msgstr "Поиск рубрики"

#: framework/extensions/product-reviews/extension.php:593
#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:88
msgid "All Categories"
msgstr "Все рубрики"

#: framework/extensions/product-reviews/extension.php:594
msgid "Parent Category"
msgstr "Родительская рубрика"

#: framework/extensions/product-reviews/extension.php:595
msgid "Parent Category:"
msgstr "Родительская рубрика:"

#: framework/extensions/product-reviews/extension.php:596
msgid "Edit Category"
msgstr "Изменить рубрику"

#: framework/extensions/product-reviews/extension.php:597
msgid "Update Category"
msgstr "Обновить рубрику"

#: framework/extensions/product-reviews/extension.php:598
msgid "Add New Category"
msgstr "Создать рубрику"

#: framework/extensions/product-reviews/extension.php:599
msgid "New Category Name"
msgstr "Название новой рубрики"

#. translators: %s is the theme name.
#: framework/extensions/product-reviews/extension.php:618
msgid "%s Settings"
msgstr "Настройки %s"

#: framework/dashboard.php:476, framework/dashboard.php:477,
#: framework/extensions/product-reviews/extension.php:619
msgid "Blocksy"
msgstr "Blocksy"

#: framework/extensions/product-reviews/helpers.php:30,
#: framework/extensions/product-reviews/metabox.php:181,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:230,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:394
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:132
msgid "Rating"
msgstr "Рейтинг"

#: framework/extensions/product-reviews/metabox.php:10
msgid "Review Entity"
msgstr "Объект обзора"

#: framework/extensions/product-reviews/metabox.php:18,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/table.php:39,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:34,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:52,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:33,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:33
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:11
msgid "Product"
msgstr "Товар"

#: framework/extensions/product-reviews/metabox.php:19
msgid "Book"
msgstr "Книга"

#: framework/extensions/product-reviews/metabox.php:21
msgid "Creative Work Season"
msgstr "Сезон творческого продукта"

#: framework/extensions/product-reviews/metabox.php:22
msgid "Creative Work Series"
msgstr "Серия творческого продукта"

#: framework/extensions/product-reviews/metabox.php:23
msgid "Episode"
msgstr "Эпизод"

#: framework/extensions/product-reviews/metabox.php:25
msgid "Game"
msgstr "Игра"

#: framework/extensions/product-reviews/metabox.php:27
msgid "Local Business"
msgstr "Местный бизнес"

#: framework/extensions/product-reviews/metabox.php:28
msgid "Media Object"
msgstr "Медиа-объект"

#: framework/extensions/product-reviews/metabox.php:29
msgid "Movie"
msgstr "Фильм"

#: framework/extensions/product-reviews/metabox.php:30
msgid "Music Playlist"
msgstr "Музыкальный плейлист"

#: framework/extensions/product-reviews/metabox.php:31
msgid "Music Recording"
msgstr "Музыкальная запись"

#: framework/extensions/product-reviews/metabox.php:32
msgid "Organization"
msgstr "Организация"

#: framework/extensions/product-reviews/metabox.php:38
msgid "More info about review entity and how to choose the right one can be found %shere%s."
msgstr "Более подробную информацию об обозреваемом объекте и о том, как выбрать подходящий, можно найти %shere%s."

#: framework/extensions/product-reviews/metabox.php:69
msgid "Product Price"
msgstr "Цена товара"

#: framework/extensions/product-reviews/metabox.php:76
msgid "Product SKU"
msgstr "Артикул"

#: framework/extensions/product-reviews/metabox.php:83
msgid "Product Brand"
msgstr "Бренд"

#: framework/extensions/product-reviews/metabox.php:96
msgid "Gallery"
msgstr "Галерея"

#: framework/extensions/product-reviews/metabox.php:107
msgid "Affiliate Button Label"
msgstr "Ярлык партнёрской кнопки"

#: framework/extensions/product-reviews/metabox.php:109,
#: framework/extensions/product-reviews/views/single-top.php:156
msgid "Buy Now"
msgstr "Купить сейчас"

#: framework/extensions/product-reviews/metabox.php:114
msgid "Affiliate Link"
msgstr "Партнёрская ссылка"

#: framework/extensions/product-reviews/metabox.php:120
msgid "Open Link In New Tab"
msgstr "Открывать в новой вкладке"

#: framework/extensions/product-reviews/metabox.php:127
msgid "Sponsored Attribute"
msgstr "Рекламный атрибут"

#: framework/extensions/product-reviews/metabox.php:150
msgid "Read More Button Label"
msgstr "Ярлык кнопки «Читать далее»"

#: framework/extensions/product-reviews/metabox.php:152,
#: framework/extensions/product-reviews/views/single-top.php:162
msgid "Read More"
msgstr "Читайте далее"

#: framework/extensions/product-reviews/metabox.php:172
msgid "Short Description"
msgstr "Краткое описание"

#: framework/extensions/product-reviews/metabox.php:187
msgid "Scores"
msgstr "Оценки"

#: framework/extensions/product-reviews/metabox.php:227
msgid "Product specs"
msgstr "Характеристики товара"

#: framework/extensions/product-reviews/metabox.php:252,
#: framework/extensions/product-reviews/views/single-top.php:262
msgid "Pros"
msgstr "Преимущества"

#: framework/extensions/product-reviews/metabox.php:272,
#: framework/extensions/product-reviews/views/single-top.php:285
msgid "Cons"
msgstr "Недостатки"

#: framework/extensions/trending/customizer.php:169
msgid "Trending Posts"
msgstr "Популярные записи"

#: framework/extensions/trending/config.php:6
msgid "Highlight your most popular posts or products based on the number of comments or reviews they have gotten in the specified period of time."
msgstr "Выделите свои самые популярные записи или товары на основе количества комментариев или отзывов, полученных за указанный период времени."

#: framework/extensions/trending/customizer.php:8,
#: framework/features/blocks/search/options.php:16,
#: framework/premium/extensions/shortcuts/views/bar.php:51,
#: framework/premium/features/premium-header/items/search-input/options.php:14
msgid "Products"
msgstr "Товары"

#: framework/extensions/trending/customizer.php:37
msgid "All categories"
msgstr "Все рубрики"

#: framework/extensions/trending/customizer.php:42
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:210
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:34
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:66
#: static/js/editor/blocks/tax-query/Edit.js:156
msgid "Taxonomy"
msgstr "Таксономия"

#: framework/extensions/trending/customizer.php:43
msgid "All taxonomies"
msgstr "Все таксономии"

#: framework/extensions/trending/customizer.php:179
msgid "Module Title"
msgstr "Заголовок модуля"

#: framework/extensions/trending/customizer.php:182,
#: framework/extensions/trending/helpers.php:352
msgid "Trending now"
msgstr "Сейчас популярно"

#: framework/extensions/trending/customizer.php:187
msgid "Module Title Tag"
msgstr "Тег заголовка модуля"

#: framework/extensions/trending/customizer.php:324,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:284
msgid "Source"
msgstr "Источник"

#: framework/extensions/trending/customizer.php:329
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:229
msgid "Taxonomies"
msgstr "Таксономии"

#: framework/extensions/trending/customizer.php:330
msgid "Custom Query"
msgstr "Произвольный запрос"

#: framework/extensions/trending/customizer.php:354
msgid "Posts ID"
msgstr "ID записей"

#: framework/extensions/trending/customizer.php:358
msgid "Separate posts ID by comma. How to find the %spost ID%s."
msgstr "Разделяйте ID постов запятыми. Как найти %sID записи%s."

#: framework/extensions/trending/customizer.php:375
msgid "Trending From"
msgstr "Популярно "

#: framework/extensions/trending/customizer.php:383
msgid "All Time"
msgstr "За всё время"

#: framework/extensions/trending/customizer.php:384
msgid "Last 24 Hours"
msgstr "Последние 24 часа"

#: framework/extensions/trending/customizer.php:385
msgid "Last 7 Days"
msgstr "Последние 7 дней"

#: framework/extensions/trending/customizer.php:386
msgid "Last Month"
msgstr "Последний месяц"

#: framework/features/header/account-modal.php:37,
#: framework/features/header/items/account/options.php:1053,
#: framework/features/header/items/account/views/logout.php:8
msgid "Login"
msgstr "Вход"

#: framework/features/header/account-modal.php:41
msgid "Sign Up"
msgstr "Регистрация"

#: framework/features/header/account-modal.php:61
msgid "Back to login"
msgstr "Вернуться ко входу"

#: framework/features/header/header-options.php:11
msgid "Sticky Functionality"
msgstr "Функционал закрепления"

#: framework/features/header/header-options.php:37
msgid "Only Main Row"
msgstr "Только главный ряд"

#: framework/features/header/header-options.php:42
msgid "Top & Main Row"
msgstr "Верхний и главный ряд"

#: framework/features/header/header-options.php:47
msgid "All Rows"
msgstr "Все ряды"

#: framework/features/header/header-options.php:52
msgid "Main & Bottom Row"
msgstr "Главный и нижний ряд"

#: framework/features/header/header-options.php:57
msgid "Only Top Row"
msgstr "Только верхний ряд"

#: framework/features/header/header-options.php:62
msgid "Only Bottom Row"
msgstr "Только нижний ряд"

#: framework/features/header/header-options.php:77
msgid "Slide Down"
msgstr "Скольжение вниз"

#: framework/features/header/header-options.php:78
msgid "Fade"
msgstr "Растворение"

#: framework/features/header/header-options.php:79
msgid "Auto Hide/Show"
msgstr "Скрывать/отображать автоматом"

#: framework/features/header/header-options.php:97,
#: framework/features/header/header-options.php:179
msgid "Enable on"
msgstr "Устройства отображения"

#: framework/features/header/header-options.php:125
msgid "Transparent Functionality"
msgstr "Функционал прозрачности"

#: framework/extensions/trending/customizer.php:561,
#: framework/features/header/header-options.php:163,
#: framework/premium/extensions/shortcuts/customizer.php:1017,
#: framework/premium/features/content-blocks/options/header.php:31,
#: framework/premium/features/content-blocks/options/hook.php:36,
#: framework/premium/features/content-blocks/options/maintenance.php:5,
#: framework/premium/features/content-blocks/options/nothing_found.php:29,
#: framework/premium/features/content-blocks/options/nothing_found.php:34,
#: framework/premium/features/content-blocks/options/popup.php:49,
#: framework/premium/features/content-blocks/options/single.php:10,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:443,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:5,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:102,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:24
#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:50
#: framework/premium/static/js/footer/EditConditions.js:97
#: static/js/header/EditConditions.js:103
msgid "Display Conditions"
msgstr "Условия отображения"

#: framework/premium/features/content-blocks/admin-ui.php:653
msgid "Hooks Locations"
msgstr "Расположение зацепов"

#: framework/premium/extensions/shortcuts/customizer.php:765,
#: framework/premium/features/content-blocks/admin-ui.php:342,
#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:12,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:5
msgid "Type"
msgstr "Тип"

#: framework/premium/features/content-blocks/admin-ui.php:343
msgid "Location/Trigger"
msgstr "Положение/триггер"

#: framework/premium/features/content-blocks/admin-ui.php:344,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:80,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:143
msgid "Conditions"
msgstr "Условия"

#: framework/premium/features/content-blocks/admin-ui.php:345
msgid "Output"
msgstr "Вывод"

#: framework/premium/features/content-blocks/admin-ui.php:346
msgid "Enable/Disable"
msgstr "Включить/выключить"

#: framework/features/blocks/about-me/options.php:204,
#: framework/features/blocks/contact-info/options.php:564,
#: framework/features/blocks/share-box/options.php:160,
#: framework/features/blocks/socials/options.php:112,
#: framework/premium/extensions/shortcuts/customizer.php:980,
#: framework/premium/features/content-blocks/admin-ui.php:371,
#: framework/features/header/items/account/options.php:22,
#: framework/features/header/items/account/options.php:348,
#: framework/features/header/items/account/options.php:746,
#: framework/premium/features/content-blocks/options/hook.php:9,
#: framework/premium/features/content-blocks/options/hook.php:77,
#: framework/premium/features/content-blocks/options/hook.php:167,
#: framework/premium/features/content-blocks/options/popup.php:75,
#: framework/premium/features/content-blocks/options/popup.php:222,
#: framework/premium/features/content-blocks/options/popup.php:603,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/select.php:23,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:97,
#: framework/premium/features/premium-header/items/contacts/options.php:451
#: framework/premium/static/js/blocks/ContentBlock.js:56
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:170
msgid "None"
msgstr "Отсутствует"

#: framework/premium/features/content-blocks/admin-ui.php:372,
#: framework/premium/features/content-blocks/options/popup.php:76
msgid "On scroll"
msgstr "При прокрутке"

#: framework/premium/features/content-blocks/admin-ui.php:373,
#: framework/premium/features/content-blocks/options/popup.php:77
msgid "On scroll to element"
msgstr "При прокрутке до элемента"

#: framework/premium/features/content-blocks/admin-ui.php:375,
#: framework/premium/features/content-blocks/options/popup.php:79
msgid "On page load"
msgstr "При загрузке страницы"

#: framework/premium/features/content-blocks/admin-ui.php:376,
#: framework/premium/features/content-blocks/options/popup.php:80
msgid "After inactivity"
msgstr "После бездействия"

#: framework/premium/features/content-blocks/admin-ui.php:377,
#: framework/premium/features/content-blocks/options/popup.php:81
msgid "After x time"
msgstr "Через X минут"

#: framework/premium/features/content-blocks/admin-ui.php:379,
#: framework/premium/features/content-blocks/options/popup.php:83
msgid "On page exit intent"
msgstr "При попытке ухода"

#: framework/premium/features/content-blocks/admin-ui.php:383
msgid "Down"
msgstr "Вниз"

#: framework/premium/features/content-blocks/admin-ui.php:384
msgid "Up"
msgstr "Вверх"

#: framework/premium/features/content-blocks.php:193,
#: framework/premium/features/content-blocks.php:199
msgid "Content Blocks"
msgstr "Блоки содержимого"

#: framework/premium/features/content-blocks.php:194,
#: framework/premium/extensions/mega-menu/options.php:346,
#: framework/premium/features/content-blocks/content-block-layer.php:163,
#: framework/premium/features/content-blocks/content-block-layer.php:214,
#: framework/features/header/items/account/options.php:247,
#: framework/premium/features/premium-header/items/content-block/config.php:4
#: framework/premium/static/js/blocks/ContentBlock.js:78
msgid "Content Block"
msgstr "Блок содержимого"

#: framework/premium/features/content-blocks.php:195,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:138,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:482,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:273
msgid "Add New"
msgstr "Создать"

#: framework/premium/features/content-blocks.php:196
msgid "Add New Content Block"
msgstr "Создать блок содержимого"

#: framework/premium/features/content-blocks.php:197
#: framework/premium/static/js/blocks/ContentBlock.js:89
msgid "Edit Content Block"
msgstr "Править блок содержимого"

#: framework/premium/features/content-blocks.php:198
#: framework/premium/static/js/hooks/CreateHook.js:33
msgid "New Content Block"
msgstr "Новый блок содержимого"

#: framework/premium/features/content-blocks.php:200
msgid "View Content Block"
msgstr "Просмотр блока содержимого"

#: framework/premium/features/content-blocks.php:201
msgid "Search Content Blocks"
msgstr "Поиск блоков содержимого"

#: framework/premium/features/content-blocks.php:202,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:145,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:489,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:280
msgid "Nothing found"
msgstr "Ничего не найдено"

#: framework/premium/features/content-blocks.php:203,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:146,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:490,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:281
msgid "Nothing found in Trash"
msgstr "В корзине ничего не найдено"

#: framework/premium/features/premium-footer.php:14,
#: framework/premium/features/premium-footer.php:28
msgid "Footer Menu 1"
msgstr "Меню подвала 1"

#: framework/premium/features/premium-footer.php:29,
#: framework/premium/features/premium-footer/items/menu-secondary/config.php:4
msgid "Footer Menu 2"
msgstr "Меню подвала 2"

#: framework/premium/features/premium-header.php:57
msgid "Header Menu 3"
msgstr "Меню шапки 3"

#: framework/premium/features/premium-header.php:202
msgid "Header Widget Area "
msgstr "Области виджетов шапки"

#: framework/extensions/trending/customizer.php:251,
#: framework/premium/features/premium-header.php:258,
#: framework/premium/features/premium-header.php:339,
#: framework/premium/features/premium-header.php:359,
#: framework/premium/features/premium-header.php:376,
#: framework/premium/features/socials.php:31,
#: framework/features/blocks/contact-info/options.php:121,
#: framework/features/blocks/contact-info/options.php:186,
#: framework/features/blocks/contact-info/options.php:249,
#: framework/features/blocks/contact-info/options.php:312,
#: framework/features/blocks/contact-info/options.php:375,
#: framework/features/blocks/contact-info/options.php:438,
#: framework/features/blocks/contact-info/options.php:501,
#: framework/features/blocks/search/options.php:86,
#: framework/premium/extensions/mega-menu/options.php:463,
#: framework/features/header/items/account/options.php:347,
#: framework/features/header/items/account/options.php:404,
#: framework/features/header/items/account/options.php:745,
#: framework/features/header/items/account/options.php:785,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:693,
#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:20,
#: framework/premium/features/premium-header/items/contacts/options.php:114,
#: framework/premium/features/premium-header/items/contacts/options.php:159,
#: framework/premium/features/premium-header/items/contacts/options.php:204,
#: framework/premium/features/premium-header/items/contacts/options.php:249,
#: framework/premium/features/premium-header/items/contacts/options.php:295,
#: framework/premium/features/premium-header/items/contacts/options.php:340,
#: framework/premium/features/premium-header/items/contacts/options.php:385,
#: framework/premium/features/premium-header/items/search-input/options.php:79,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:30,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:28,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:28
msgid "Icon"
msgstr "Значок"

#: framework/premium/features/premium-header.php:277,
#: framework/features/blocks/about-me/options.php:168,
#: framework/features/blocks/contact-info/options.php:538,
#: framework/features/blocks/share-box/options.php:122,
#: framework/features/blocks/socials/options.php:74,
#: framework/premium/features/premium-header/items/contacts/options.php:426
msgid "Icons Size"
msgstr "Размер значков"

#: framework/premium/features/premium-header.php:289,
#: framework/premium/extensions/mega-menu/options.php:509
msgid "Icon Position"
msgstr "Положения значка"

#: framework/premium/features/premium-header.php:296,
#: framework/premium/extensions/mega-menu/options.php:516,
#: framework/premium/extensions/shortcuts/customizer.php:886,
#: framework/features/header/items/account/options.php:561,
#: framework/features/header/items/account/options.php:1027,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:95,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:129,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:130
msgid "Left"
msgstr "Слева"

#: framework/premium/features/premium-header.php:297,
#: framework/premium/extensions/mega-menu/options.php:517,
#: framework/premium/extensions/shortcuts/customizer.php:887,
#: framework/features/header/items/account/options.php:562,
#: framework/features/header/items/account/options.php:1031,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:96,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:130,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:131
msgid "Right"
msgstr "Справа"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:14
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/index.js:56
msgid "Newsletter"
msgstr "Рассылка"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:13,
#: framework/features/blocks/contact-info/options.php:39,
#: framework/premium/extensions/mega-menu/options.php:544,
#: framework/premium/extensions/shortcuts/customizer.php:1187,
#: framework/premium/extensions/shortcuts/customizer.php:1229,
#: framework/premium/extensions/shortcuts/customizer.php:1271,
#: framework/features/header/items/account/options.php:577,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:32,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:72,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:98,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:547,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:582,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:616,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:486,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:517,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:547
#: static/js/editor/blocks/breadcrumbs/Edit.js:54
#: static/js/editor/blocks/contact-info/Edit.js:107
msgid "Text"
msgstr "Текст"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:19,
#: framework/features/blocks/about-me/options.php:67
msgid "You can add here some arbitrary HTML code."
msgstr "Сюда можно вставить произвольный HTML-код."

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:74
msgid "Container Style"
msgstr "Стиль контейнера"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:82,
#: framework/premium/features/content-blocks/options/404.php:85,
#: framework/premium/features/content-blocks/options/archive.php:162,
#: framework/premium/features/content-blocks/options/header.php:103,
#: framework/premium/features/content-blocks/options/hook.php:212,
#: framework/premium/features/content-blocks/options/maintenance.php:82,
#: framework/premium/features/content-blocks/options/nothing_found.php:103,
#: framework/premium/features/content-blocks/options/single.php:94,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:22
msgid "Boxed"
msgstr "Обрамлённый"

#: framework/extensions/product-reviews/views/single-top.php:238
msgid "Specs"
msgstr "Характеристики"

#: framework/extensions/newsletter-subscribe/helpers.php:147
msgid "Email address"
msgstr "Email"

#: framework/features/header/modal/login.php:28,
#: framework/features/header/modal/register.php:53
msgid "Password"
msgstr "Пароль"

#: framework/features/header/modal/login.php:53
msgid "Remember Me"
msgstr "Запомнить меня"

#: framework/features/header/modal/login.php:57
msgid "Forgot Password?"
msgstr "Забыли пароль?"

#: framework/features/header/modal/login.php:79
msgid "Log In"
msgstr "Вход"

#: framework/features/header/modal/login.php:23,
#: framework/features/header/modal/lostpassword.php:5
msgid "Username or Email Address"
msgstr "Имя пользователя или Email"

#: framework/features/header/modal/lostpassword.php:13
msgid "Get New Password"
msgstr "Получить новый пароль"

#: framework/features/header/modal/register.php:41
msgid "Username"
msgstr "Имя пользователя"

#: framework/features/header/modal/register.php:106
msgid "Registration confirmation will be emailed to you."
msgstr "Подтверждение регистрации будет отправлено на ваш Email."

#: framework/features/header/modal/register.php:112
msgid "Register"
msgstr "Регистрация"

#: framework/helpers/exts-configs.php:60
msgid "Connect your Adobe Fonts project and use the selected fonts throughout Blocksy and your favorite page builder."
msgstr "Подключите свой проект Adobe Fonts и используйте выбранные шрифты в теме сайта и конструкторе страниц."

#: framework/helpers/exts-configs.php:71
msgid "Inject custom code snippets throughout your website. The extension works globally or on a per post/page basis."
msgstr "Внедрение произвольных фрагментов кода в веб-сайт. Расширение работает глобально или отдельно для каждой записи/страницы."

#: framework/premium/extensions/code-snippets/extension.php:42,
#: framework/premium/extensions/code-snippets/extension.php:97,
#: framework/premium/extensions/code-snippets/extension.php:142
msgid "After body open scripts"
msgstr "После скриптов открытия body"

#: framework/helpers/exts-configs.php:97
msgid "Upload an unlimited number of custom fonts or variable fonts and use them throughout Blocksy and your favorite page builder."
msgstr "Загружайте неограниченное количество пользовательских или вариативных шрифтов и используйте их на своём сайте."

#: framework/helpers/exts-configs.php:109
msgid "Serve your chosen Google Fonts from your own web server. This will increase the loading speed and makes sure your website complies with the privacy regulations."
msgstr "Подгружайте выбранные шрифты Google с вашего собственного веб-сервера. Это повысит скорость загрузки и обеспечит соответствие вашего сайта правилам конфиденциальности."

#: framework/helpers/exts-configs.php:124
msgid "Create beautiful personalised menus that set your website apart from the others. Add icons and badges to your entries and even add Content Blocks inside your dropdowns."
msgstr "Создавайте красивые меню, которые будут отличать ваш сайт от прочих. Добавляйте значки и метки к пунктам меню или даже добавляйте блоки контента в раскрывающиеся списки."

#: framework/premium/extensions/mega-menu/extension.php:160
msgid "Menu Item Settings"
msgstr "Настройки пункта меню"