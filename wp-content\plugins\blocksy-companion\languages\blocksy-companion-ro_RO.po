# Translation of Blocksy Companion Pro in Romanian
# This file is distributed under the same license as the Blocksy Companion Pro package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-06-24 13:15:54+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n == 1) ? 0 : ((n == 0 || n % 100 >= 2 && n % 100 <= 19) ? 1 : 2);\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: ro\n"
"Project-Id-Version: Blocksy Companion Pro\n"

#: static/js/dashboard/helpers/useUpsellModal.js:88
msgid "Business or Agency"
msgstr "Business sau Agency"

#: static/js/options/ConditionsManager/SingleCondition.js:482
msgid "Select value"
msgstr "Selecteaza valoarea"

#: framework/helpers/exts-configs.php:385
msgid "Product Waitlist"
msgstr "Lista de așteptare produs"

#: framework/helpers/exts-configs.php:386
msgid "Allow your customers to sign up for a waitlist for products that are out of stock and get notified when they are back in stock."
msgstr "Permite clienților tăi să se înscrie pe o listă de așteptare pentru produsele care nu sunt în stoc și să fie notificați atunci când acestea redevin disponibile."

#: framework/features/blocks/share-box/options.php:103
msgid "Clipboard"
msgstr ""

#: framework/premium/extensions/shortcuts/customizer.php:609,
#: framework/premium/extensions/shortcuts/customizer.php:635,
#: framework/premium/extensions/shortcuts/views/bar.php:53,
#: framework/features/header/items/account/views/login.php:554,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:4,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/feature.php:189,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/feature.php:193,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-account.php:67
msgid "Waitlist"
msgstr "Lista de așteptare"

#: framework/premium/extensions/shortcuts/customizer.php:895
msgid "Tooltip Visibility"
msgstr "Vizibilitatea casetei de informatii"

#: framework/premium/extensions/shortcuts/customizer.php:1351
msgid "Container Backdrop Blur"
msgstr "Estompare Fundal Container"

#: framework/premium/features/content-blocks/hooks-manager.php:957
msgid "Added to Cart: Before product"
msgstr "Adaugat în Cos: Inainte de Produs"

#: framework/premium/features/content-blocks/hooks-manager.php:961
msgid "Added to Cart: Before actions"
msgstr "Adăugat în Coș: Înainte de Acțiuni"

#: framework/premium/features/content-blocks/hooks-manager.php:965
msgid "Added to Cart: Before suggested products"
msgstr "Adăugat în Coș: Înainte de Produsele Sugerate"

#: framework/premium/features/content-blocks/hooks-manager.php:969
msgid "Added to Cart: After suggested products"
msgstr "Adăugat în Coș: După Produsele Sugerate"

#: framework/premium/features/content-blocks/hooks-manager.php:971
msgid "WooCommerce: Added to Cart"
msgstr "WooCommerce: Adăugat în Coș"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:294
msgid "Upsell Products"
msgstr "Produse Recomandate pentru Creșterea Vânzărilor"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:295
msgid "Cross-sell Products"
msgstr "Produse Recomandate pentru Vânzări Înrudite"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:506
msgid "Auto Close Panel"
msgstr "Închidere Automată a Panoului"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:507
msgid "Automatically close the panel when a filter option is selected."
msgstr "Închide automat panoul atunci când este selectată o opțiune de filtrare."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:15
msgid "Form Type"
msgstr "Tipul Formularului"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:29
msgid "Form Max Width"
msgstr "Lățimea Maximă a Formularului"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:41
msgid "Enable For Backorders"
msgstr "Activează pentru Comenzi în Așteptare"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:42
msgid "Allow users to join the waitlist even if the product is on backorder."
msgstr "Permite utilizatorilor să se înscrie pe lista de așteptare chiar dacă produsul este disponibil pentru comandă în așteptare."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:55
msgid "Show Users Count"
msgstr "Afișează Numărul de Utilizatori"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:56
msgid "Display a counter that reflects the current number of users on the waitlist."
msgstr "Afișează un contor care reflectă numărul curent de utilizatori înscriși pe lista de așteptare."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:69
msgid "Logged In Users Only"
msgstr "Doar pentru Utilizatori Autentificați"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:70
msgid "Display the waitlist feature exclusively to users who are logged in."
msgstr "Afișează funcția de listă de așteptare exclusiv utilizatorilor care sunt autentificați."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:83
msgid "Subscription Confirmation"
msgstr "Confirmarea Abonării"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:84
msgid "Specify which users should verify their waitlist subscription through email confirmation."
msgstr "Specify which users should verify their waitlist subscription through email confirmation."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:107
msgid "Waitlist Form Display Conditions"
msgstr "Condiții de Afișare a Formularului Listei de Așteptare"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:108
msgid "Choose where you want this Waitlist Form to be displayed."
msgstr "Alegeți unde doriți să fie afișat acest formular al listei de așteptare."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:160
msgid "Message Font"
msgstr "Fontul Mesajului"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:168
msgid "Message Color"
msgstr "Culoarea Mesajului"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:225
msgid "Container Padding"
msgstr "Spațiere Interioara Container"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/table.php:40,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:46
msgid "Actions"
msgstr "Actiune"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:96,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:200,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:221,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:282
msgid "Invalid request"
msgstr "Cerere invalida"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:106
msgid "No waitlist found"
msgstr "Nu a fost gasita nici o lista de asteptare"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:127,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:128,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:162
msgid "Waitlists"
msgstr "Lista asteptare"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:153
msgid "Number of items per page"
msgstr "Numar de articole pe pagina"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:171
msgid "Waitlist for %s"
msgstr "Lista de așteptare pentru %s"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:213
msgid "Invalid email"
msgstr "Email invalid"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:229
msgid "You are already on the waitlist"
msgstr "Esti deja pe lista de asteptare"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:267
msgid "You have been added to the waitlist"
msgstr "Ai fost adaugat pe lista de asteptare"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-dashboard.php:291
msgid "You have been removed from the waitlist"
msgstr "Ai fost sters din lista de asteptare"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-layer.php:33
msgid "%s %s joined the waitlist for this item."
msgstr "%s %s s-a înscris pe lista de așteptare pentru acest articol."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-layer.php:243
msgid "Waitlist Form"
msgstr "Formularul listei de asteptare"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-mailer.php:78
msgid "Your waitlist subscription has been successfully canceled."
msgstr "Abonarea ta la lista de așteptare a fost anulată cu succes."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-mailer.php:117
msgid "Your waitlist subscription has been successfully confirmed."
msgstr "Abonarea ta la lista de așteptare a fost confirmată cu succes."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-table.php:11
msgid "Search Products"
msgstr "Cauta produse"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:14
msgid "Export Subscribers"
msgstr "Exporta abonatii"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:63
msgid "Guest"
msgstr "Vizitator"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:75
msgid "Edit this customer"
msgstr "Modifica acest clientul"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:76
msgid "Edit"
msgstr "Editeaza"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:115,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:182
msgid "Delete"
msgstr "Sterge"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:123
msgid "View"
msgstr "Vezi"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:153
msgid "%s ago"
msgstr "Acum %s"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:169
msgid "Is registered"
msgstr "Este înregistrat"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:170
msgid "Date created"
msgstr "Data crearii"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:8
msgid "Waitlist - Back in Stock Notification"
msgstr "Lista de așteptare - Notificare de revenire în stoc"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:9
msgid "This email is sent when a product is back in stock"
msgstr "Acest e-mail este trimis atunci când un produs revine în stoc."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:10
msgid "A product you are waiting for is back in stock"
msgstr "Un produs pe care îl așteptați este din nou disponibil în stoc"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/back-in-stock.php:11
msgid "Good news! The product you have been waiting for is now back in stock!"
msgstr "Veste bună! Produsul pe care l-ați așteptat este acum din nou disponibil în stoc!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:8
msgid "Waitlist - Confirm Subscription"
msgstr "Lista de așteptare - Confirmați Abonarea"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:9
msgid "This email is sent when a user subscribes to a product stock alert and should confirm their subscription"
msgstr "Acest e-mail este trimis atunci când un utilizator se abonează la o alertă de stoc pentru un produs și trebuie să își confirme abonarea."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:10
msgid "Confirm waitlist subscription"
msgstr "Confirmați abonarea la lista de așteptare"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/confirm-subscription.php:11
msgid "Get notified when {product_title} is back in stock"
msgstr "Primeste notificare când {product_title} revine în stoc."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:8
msgid "Waitlist - Subscription Confirmed"
msgstr "Lista de Așteptare - Abonare Confirmată"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:9
msgid "This email is sent after a user confirmed the subscription to a product stock alert"
msgstr "Acest e-mail este trimis după ce un utilizator a confirmat abonarea la alerta de stoc pentru un produs."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:10
msgid "Waitlist subscription confirmed"
msgstr "Abonarea la lista de așteptare a fost confirmată."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/subscription-confirmed.php:11
msgid "You will be notified when {product_title} is back in stock"
msgstr "Vei fi notificat cand {product_title} va reveni în stoc."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/waitlist-email.php:35,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/emails/waitlist-email.php:139
msgid "Customer"
msgstr "Client"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:27
msgid "Enter your email"
msgstr "Introdu email-ul tau"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:40
msgid "Join Waitlist"
msgstr "Alătură-te listei de așteptare"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:53
msgid "This product is currently sold out!"
msgstr "Acest produs este momentan fără stoc"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:61
msgid "No worries! Please enter your e-mail address and we will promptly notify you as soon as the item is back in stock."
msgstr "Nu-ți face griji! Te rugăm să introduci adresa ta de e-mail și te vom anunța imediat ce produsul va reveni în stoc."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:69
msgid "Great! You have been added to the waitlist for this product. Please check your inbox and confirm the subscription to this waitlist."
msgstr "Minunat! Ai fost adaugat pe lista de asteptare pentru acest produs. Te rugam să verifici casuta ta de e-mail si să confirmi abonarea la aceasta lista de asteptare."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:97
msgid "Great! You have been added to the waitlist for this product. You will receive an email as soon as the item is back in stock."
msgstr "Minunat! Ai fost adaugat pe lista de asteptare pentru acest produs. Vei primi un e-mail imediat ce produsul va reveni în stoc."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/form.php:151,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:86
msgid "Unsubscribe"
msgstr "Dezabonare"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:65
msgid "Yes"
msgstr "Da"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:65
msgid "No"
msgstr "Nu"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:21
msgid "You don't have any products in your waitlist yet."
msgstr "Nu ai nici un produs in lista de asteptare momentan."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:42
msgid "Confirmed"
msgstr "Confirmata"

#. translators: %s User name.
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:19,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:17,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:17,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:12,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:10,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-subscription-confirmed.php:10
msgid "Hi, %s!"
msgstr "Buna, %s!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:31,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:19
msgid "Great news! The %s from your waitlist is now back in stock!"
msgstr "Veste buna! Produsul %s de pe lista ta de asteptare este acum disponibil din nou in stoc!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:42,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:24
msgid "Click the link below to secure your purchase before it is gone!"
msgstr "Click pe linkul de mai jos pentru a-ti asigura achizitia înainte ca produsul sa dispara!"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:26,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:15
msgid "You have requested to join the waitlist for this item:"
msgstr "Ai solicitat să te alături listei de așteptare pentru acest articol:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:62
msgid "Click the button below to confirm your subscription. Once confirmed, we will notify you when the item is back in stock."
msgstr "Click pe butonul de mai jos pentru a-ți confirma abonarea. Odată confirmata, te vom anunța când produsul va reveni în stoc."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:66
msgid "Confirm Subscription"
msgstr "Confirma abonarea"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:70,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:26
msgid "Please note, the confirmation period is 2 days."
msgstr "Te rugam să retii că perioada de confirmare este de 2 zile."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:77,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:65
msgid "If you don't want to receive any further notifications, please %s"
msgstr "Daca nu doresti să primesti alte notificari, te rugam sa %s."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:78,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:66
msgid "unsubscribe"
msgstr "dezaboneaza-te"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:26,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-subscription-confirmed.php:15
msgid "You have been successfully added to the waitlist for the following item:"
msgstr "Ai fost adaugat cu succes pe lista de asteptare pentru urmatorul articol:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:28,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:17
msgid "Product:"
msgstr "Produs:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:29,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:18
msgid "Price:"
msgstr "Pret:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-back-in-stock.php:30
msgid "Add to cart:"
msgstr "Adauga in cos:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:19
msgid "Product link:"
msgstr "Link-ul produsului:"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:23
msgid "Click the link below to confirm your subscription. Once confirmed, we will notify you when the item is back in stock."
msgstr "Click pe linkul de mai jos pentru a-ti confirma abonarea. Odata confirmata, te vom anunta cand produsul va reveni în stoc."

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-confirm-subscription.php:32,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/plain/waitlist-subscription-confirmed.php:25
msgid "If you don't want to receive any further notifications, please unsubscribe by clicking on this link - %s"
msgstr "Daca nu doresti să primesti alte notificari, te rugam să te dezabonezi facand clic pe acest link - %s."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:127
msgid "API URL"
msgstr "API URL"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:304
msgid "More information on how to generate an API key for ActiveCampaign can be found %shere%s."
msgstr "Mai multe informatii despre cum sa generezi o cheie API pentru ActiveCampaign pot fi gasite %s aici %s."

#: static/js/dashboard/helpers/useUpsellModal.js:18
#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:111
msgid "Business"
msgstr "Afacere"

#: framework/helpers/exts-configs.php:377,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:4
msgid "Added to Cart Popup"
msgstr "Popup de Adaugare în Cos"

#: framework/helpers/exts-configs.php:378
msgid "Show a dynamic confirmation popup with product recommendations whenever items are added to the cart."
msgstr "Afiseaza un popup dinamic de confirmare cu recomandari de produse de fiecare data cand articole sunt adaugate în cos."

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:15
msgid "Trigger Popup On"
msgstr "Declansează Popup"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:26
msgid "Product Page"
msgstr "Oagina produsului"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:91
msgid "Description Length"
msgstr "Lungimea descrierii"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:102
msgid "Cart Button"
msgstr "Buton cos"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:112
msgid "Checkout Button"
msgstr "Buton finalizare comanda"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:121
msgid "Continue Shopping Button"
msgstr "Buton continuare cumparaturi"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:140
msgid "Shipping Info"
msgstr "Informatii livrare"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:149
msgid "Tax Info"
msgstr "Informatii despre taxe"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:158
msgid "Total Info"
msgstr "Informatii despre total"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:264
msgid "Suggested Products"
msgstr "Produse sugerate"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:292,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/suggested-products.php:51
msgid "Related Products"
msgstr "Produse asociate"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:293
msgid "Recently Viewed Products"
msgstr "Produse vizualizate recent"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:368
msgid "Products Card Type"
msgstr "Tipul cardului de produse"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:382
msgid "Products Visibility"
msgstr "Vizibilitatea produselor"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:516
msgid "Popup Options"
msgstr "Optiune popup"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:736,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:752,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:315,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:96
msgid "Popup Backdrop"
msgstr "Fundalul Popup-ului"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:768
msgid "Close Icon Size"
msgstr "Marimea iconitei de inchidere"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/view.php:95
msgid "Product succesfully added to your cart!"
msgstr "Produsul a fost adaugat cu succes în cosul tau!"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/view.php:102
msgid "Close Modal"
msgstr "Inchide modalul"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:331,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:124
msgid "Popup Shadow"
msgstr "Umbra Popup-ului"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:350,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:143
msgid "Popup Border Radius"
msgstr "Raza colturilor Popup-ului"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/added-product.php:227
msgid "Shipping Cost"
msgstr "Cost transport"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/added-product.php:236
msgid "Tax Amount"
msgstr "Valoarea taxei"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/added-product.php:245
msgid "Cart Total"
msgstr "Total cos"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/product-actions.php:24
msgid "View Cart"
msgstr "Vezi cosul"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/product-actions.php:34
msgid "Checkout"
msgstr "Checkout"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/product-actions.php:44
msgid "Continue Shopping"
msgstr "Continua cumparaturile"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/views/suggested-products.php:53
msgid "Recently Viewed"
msgstr "Vazute recent"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:340
msgid "More information on how to create a list in MailPoet can be found %shere%s."
msgstr "Mai multe informatii despre cum sa creezi o lista în MailPoet pot fi gasite %s aici%s."

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:9
msgid "Color Mode"
msgstr "Modul culoare"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:17
msgid "One Color"
msgstr "O culoare"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:18
msgid "Dual Color"
msgstr "Doua culori"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:28
msgid "Colors"
msgstr "Culori"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:50
msgid "Color 1"
msgstr "Culoare 1"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:58
msgid "Color 2"
msgstr "Culoare 2"

#: framework/features/blocks/share-box/options.php:110,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:88
msgid "Tooltip"
msgstr "Tooltip"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:108
msgid "Tooltip Text"
msgstr "Tooltip text"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:111
msgid "{term_name}"
msgstr "{term_name}"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:122
msgid "Tooltip Image"
msgstr "Tooltip imagine"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:135
msgid "Subtype"
msgstr "Subtip"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:174
msgid "here"
msgstr "aici"

#: framework/premium/features/premium-header/items/contacts/options.php:21
msgid "Item Visibility"
msgstr "Vizibilitate articol"

#: framework/premium/features/premium-header/items/language-switcher/options.php:36
msgid "Hide Missing Language"
msgstr "Ascunde limba lipsa"

#: framework/premium/extensions/woocommerce-extra/features/filters/filter-types/taxonomies.php:52
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Preview.js:94
msgid "Please wait until the lookup table is generated."
msgstr "Te rugam sa astepti pana cand tabelul de cautare este generat."

#: framework/premium/extensions/woocommerce-extra/features/filters/filter-types/taxonomies.php:472
msgid "Collapse"
msgstr "Reduce"

#: framework/premium/extensions/woocommerce-extra/features/filters/filter-types/taxonomies.php:473
msgid "Expand"
msgstr "Extinde"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/filter-presenter.php:64
msgid "category"
msgstr ""

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/filter-presenter.php:108
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Preview.js:32
msgid "Find by %s"
msgstr "Cautare după %s"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:434
msgid "Regenerate the product taxonomies lookup table"
msgstr "Regenerare tabel de căutare pentru taxonomiile produselor"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:435
msgid "This tool will regenerate the product taxonomies lookup table data from existing product(s) data. This process may take a while."
msgstr "Acest instrument va regenera datele tabelului de cautare pentru taxonomiile produselor din datele produselor existente. Acest proces poate dura cateva minute."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:446
msgid "Product taxonomies lookup table data is regenerating"
msgstr "Datele tabelului de cautare pentru taxonomiile produselor sunt în curs de regenerare."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:459
msgid "Regenerate"
msgstr "Regenereaza"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:453
msgid "Filling in progress (%d)"
msgstr "Umplerea este în curs (%d)"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:494
msgid "Resume the product taxonomies lookup table regeneration"
msgstr "Reluați regenerarea tabelului de căutare pentru taxonomiile produselor."

#. translators: %1$s = count of products already processed.
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:497
msgid "This tool will resume the product taxonomies lookup table regeneration at the point in which it was aborted (%1$s products were already processed)."
msgstr "Acest instrument va relua regenerarea tabelului de cautare pentru taxonomiile produselor de la punctul în care a fost întrerupt (%1$s produse au fost deja procesate)."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:516
msgid "Product taxonomies lookup table regeneration process has been resumed."
msgstr "Procesul de regenerare a tabelului de cautare pentru taxonomiile produselor a fost reluat."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:518
msgid "Resume"
msgstr "Reluare"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:466
msgid "Abort the product taxonomies lookup table regeneration"
msgstr "Anulează procesul de regenerare a tabelului de căutare pentru taxonomiile produselor"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:486
msgid "Product taxonomies lookup table regeneration process has been aborted."
msgstr "Procesul de regenerare a tabelului de cautare pentru taxonomiile produselor a fost anulat."

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/taxonomies-products-lookup-table.php:488
msgid "Abort"
msgstr "Anuleaza"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:158
msgid "Container Border Color"
msgstr "Culoarea marginii containerului"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:189
msgid "Container Background Color"
msgstr "Culoarea containerului"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:143
msgid "API Version"
msgstr "Versiune API"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:268
msgid "More information on how to generate an API key for Brevo can be found %shere%s."
msgstr "Mai multe informații despre cum să generezi o cheie API pentru Brevo pot fi găsite %s aici%s."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:834
msgid "Hide Documentation Links"
msgstr "Ascunde link-urile de documentatie"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:842
msgid "Hide Video Links"
msgstr "Ascunde link-urile video"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:104
msgid "Display the currently active filters."
msgstr "Afiseaza filtrele active curente."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:47
msgid "Category 1"
msgstr "Categoria 1"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:53
msgid "Category 2"
msgstr "Categoria 2"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:66
msgid "Attribute 1"
msgstr "Atribut 1"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:72
msgid "Attribute 2"
msgstr "Atribut 2"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:12
msgid "Date"
msgstr "Data"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:10
msgid "Filter by Price Controls"
msgstr "Filtrare dupa control pret"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:106
msgid "Filter by Price"
msgstr "Filtreaza dupa pret"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:11
msgid "Widget for filtering the WooCommerce products by price."
msgstr "Widget pentru filtrarea produselor WooCommerce dupa pret."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:58
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:473
msgid "Show Tooltip"
msgstr "Arata Tooltip"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:70
msgid "Show Prices"
msgstr "Arata preturile"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Disabled.js:6
msgid "Please select a valid taxonomy."
msgstr "Selecteaza o taxonomie valida"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:174
msgid "Filter Settings"
msgstr "Setare filtre"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:253
msgid "Show Search Box"
msgstr "Arata caseta cautare"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:496
msgid "Container Maximum Height"
msgstr "Inaltimea maxima container"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:55
msgid "Exclude Speciffic Items"
msgstr "Exclude articole specifice"

#: static/js/dashboard/VersionMismatch.js:62
#: static/js/notifications/VersionMismatchNotice.js:74
msgid "Update Blocksy Theme Now"
msgstr ""

#: static/js/dashboard/screens/DemoInstall.js:183
msgid "Your site is misconfigured and AJAX requests are not reaching your backend. Please click %shere%s to find the common causes and possible solutions to this.<br> Error code - %s"
msgstr "Site-ul tau este configurat incorect și cererile AJAX nu ajung la backend-ul tau. Te rugăm să faci clic %s aici %s pentru a găsi cauzele comune și soluțiile posibile pentru aceasta.<br> Codul de eroare - %s"

#: static/js/dashboard/screens/DemoInstall.js:201
msgid "Failed to retrieve starter sites list.<br> Error code - %s"
msgstr "Nu s-a reușit obținerea listei de site-uri demo. <br> Cod eroare - %s"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:129
msgid "Installing %s"
msgstr "Instalare %s"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:345
msgid "Preparing data..."
msgstr "Se pregătesc datele..."

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:89
msgid "Required plan"
msgstr "Plan necesar"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:13
msgid "All Plans"
msgstr "Toate planurile"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:15
msgid "Pro"
msgstr "PRO"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:182
msgid "All Builders"
msgstr "Toti constructorii"

#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:206
msgid "Search for a starter site..."
msgstr "Cauta un StartSite"

#: static/js/dashboard/screens/SiteExport.js:176
msgid "Export Site"
msgstr "Site export"

#: static/js/dashboard/screens/SiteExport.js:191
msgid "Starter site"
msgstr "Site de start"

#: static/js/dashboard/screens/SiteExport.js:203
msgid "Select a starter site"
msgstr "Selecteaza un site de start"

#: static/js/editor/blocks/about-me/index.js:15
msgid "About Me Controls"
msgstr "Controale despre mine"

#: static/js/editor/blocks/about-me/index.js:43
msgid "Showcase your personal information across your website."
msgstr "Expune informatiile tale personale pe intregul site web."

#: static/js/editor/blocks/breadcrumbs/Preview.js:63
msgid "Subpage"
msgstr "Subpagina"

#: static/js/editor/blocks/breadcrumbs/index.js:11
msgid "Breadcrumbs"
msgstr "Frimituri"

#: static/js/editor/blocks/breadcrumbs/index.js:12
msgid "Display navigational links, showing users their path within the site."
msgstr "Afișeaza linkuri de navigatie, aratand utilizatorilor calea lor în cadrul site-ului."

#: static/js/editor/blocks/contact-info/index.js:15
msgid "Contact Info Controls"
msgstr "Controale pentru informatiile de contact"

#: static/js/editor/blocks/contact-info/index.js:52
msgid "Display essential contact details to your visitors."
msgstr "Afișeaza detaliile esentiale de contact vizitatorilor tai."

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:102
msgid "9:16"
msgstr "9:16"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:106
msgid "3:4"
msgstr "3:4"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:110
msgid "2:3"
msgstr "2:3"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:133
msgid "Width"
msgstr "Adancime"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:157
msgid "Height"
msgstr "Inaltime"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:178
msgid "Scale"
msgstr "Scalare"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:209
msgid "Resolution"
msgstr "Rezolutie"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:215
msgid "Select the size of the source image."
msgstr "Selecteaza dimensiunea imaginii sursa."

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:57
msgid "Image Settings"
msgstr "Setarea imaginii"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:77
msgid "Aspect Ratio"
msgstr "Aspect ratio"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:82
msgid "Original"
msgstr "Original"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:90
msgid "16:9"
msgstr "16:9"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:94
msgid "4:3"
msgstr "4:3"

#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:98
msgid "3:2"
msgstr "3:2"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:100
msgid "Icon/Logo"
msgstr "Icon/Logo"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:138
msgid "Expand on click"
msgstr "Mareste la click"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:151
msgid "Video thumbnail"
msgstr "Miniatura video"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:161
msgid "Image Hover Effect"
msgstr "Efect de marire pe imagine"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:174
msgid "Zoom In"
msgstr "Mareste"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:178
msgid "Zoom Out"
msgstr "Micsoreaza"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:202
msgid "Alternative Text"
msgstr "Text alternativ"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:215
msgid "Describe the purpose of the image."
msgstr "Descrie scopul imaginii"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:221
msgid "Leave empty if decorative."
msgstr "Lasa gol daca este decorativ"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:238
msgid "Image size"
msgstr "Dimensiunea imaginii"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:271
msgid "Logo Gap"
msgstr "Spatiu intre Logo"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:323
msgid "Custom field fallback"
msgstr "Fallback pentru camp personalizat"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:343
msgid "Term additional class"
msgstr "Clasa suplimentara pentru termen"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:350
msgid "Additional class for term items. Useful for styling."
msgstr "Clasa suplimentara pentru elementele de termen. Utila pentru stilizare."

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:45
msgid "Content Source"
msgstr "Sursa continutului"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:51
msgid "Search for field"
msgstr "Cauta un camp"

#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:87
msgid "Image Source"
msgstr "Sursa imaginii"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:14
msgid "Change heading level"
msgstr "Schimba nivelul titlului"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:38
msgid "Heading 1"
msgstr "Titlu 1"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:39
msgid "Heading 2"
msgstr "Titlu 2"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:40
msgid "Heading 3"
msgstr "Titlu 3"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:41
msgid "Heading 4"
msgstr "Titlu 4"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:42
msgid "Heading 5"
msgstr "Titlu 5"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:43
msgid "Heading 6"
msgstr "Titlu 6"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:44
msgid "Paragraph"
msgstr "Paragraf"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:45
msgid "Span"
msgstr "Span"

#: static/js/editor/blocks/dynamic-data/components/TagNameDropdown.js:46
msgid "Div"
msgstr "Div"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:101
msgid "Archive Image"
msgstr "Imagine de arhiva"

#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:38
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:136
msgid "Stock Status"
msgstr "Starea stocului"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:24
#: static/js/editor/blocks/dynamic-data/preview-parts/wp/TermTextPreview.js:8
msgid "Term Title"
msgstr "Titlul termenului"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:28
#: static/js/editor/blocks/dynamic-data/preview-parts/wp/TermTextPreview.js:9
msgid "Term Description"
msgstr "Descrierea termenului"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:32
msgid "Term Image"
msgstr "Imaginea termenului"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:36
#: static/js/editor/blocks/dynamic-data/preview-parts/wp/TermTextPreview.js:10
msgid "Term Count"
msgstr "Numar de Termeni"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:50
msgid "Excerpt"
msgstr "Fragment"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:55
msgid "Post Date"
msgstr "Data Postarii"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:65
msgid "Terms"
msgstr "Termeni"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:91
#: static/js/editor/blocks/dynamic-data/preview-parts/WpFieldPreview.js:46
msgid "Archive Title"
msgstr "Titlul arhivei"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:96
#: static/js/editor/blocks/dynamic-data/preview-parts/WpFieldPreview.js:50
msgid "Archive Description"
msgstr "Descrierea arhivei"

#: static/js/editor/blocks/dynamic-data/index.js:17
msgid "Fetch and display content from various sources."
msgstr "Recupereaza și afișeaza continut din diverse surse."

#: static/js/editor/blocks/dynamic-data/index.js:33
msgid "Dynamic Title"
msgstr "Titlu dinamic"

#: static/js/editor/blocks/dynamic-data/index.js:37
msgid "Dynamic Excerpt"
msgstr "Extras dinamic"

#: static/js/editor/blocks/dynamic-data/index.js:41
msgid "Dynamic Post Date"
msgstr "Data postarii dinamice"

#: static/js/editor/blocks/dynamic-data/index.js:45
msgid "Dynamic Comments"
msgstr "Comentarii dinamice"

#: static/js/editor/blocks/dynamic-data/index.js:49
msgid "Dynamic Terms"
msgstr "Termeni dinamici"

#: static/js/editor/blocks/dynamic-data/index.js:53
msgid "Dynamic Author"
msgstr "Autor dinamic"

#: static/js/editor/blocks/dynamic-data/index.js:57
msgid "Dynamic Featured Image"
msgstr "Imaginea Principală Dinamică"

#: static/js/editor/blocks/dynamic-data/index.js:61
msgid "Dynamic Author Avatar"
msgstr "Avatarul Dinamic al Autorului"

#: static/js/editor/blocks/dynamic-data/index.js:65
msgid "Dynamic Price"
msgstr "Preț Dinamic"

#: static/js/editor/blocks/dynamic-data/index.js:69
msgid "Dynamic Stock Status"
msgstr "Starea Dinamică a Stocului"

#: static/js/editor/blocks/dynamic-data/index.js:73
msgid "Dynamic Brands"
msgstr "Mărci Dinamice"

#: static/js/editor/blocks/dynamic-data/index.js:77
msgid "Dynamic SKU"
msgstr "SKU Dinamic"

#: static/js/editor/blocks/dynamic-data/index.js:81
msgid "Dynamic Rating"
msgstr "Rating dinamic"

#: static/js/editor/blocks/dynamic-data/index.js:85
msgid "Dynamic Term Title"
msgstr "Titlu dinamic al termenului"

#: static/js/editor/blocks/dynamic-data/index.js:89
msgid "Dynamic Term Description"
msgstr "Titlu dinamic al descrierii"

#: static/js/editor/blocks/dynamic-data/index.js:93
msgid "Dynamic Term Count"
msgstr "Titlu dinamic al numararii"

#: static/js/editor/blocks/dynamic-data/index.js:97
msgid "Dynamic Term Image"
msgstr "Titlu dinamic al imaginii"

#: static/js/editor/blocks/dynamic-data/preview-parts/woo/RatingPreview.js:13
msgid "Rated %s out of 5"
msgstr "Evaluat la %s din 5"

#: static/js/editor/blocks/dynamic-data/utils.js:15
msgid "Unknown"
msgstr "Necunoscut"

#: static/js/editor/blocks/post-template/Edit.js:170
#: static/js/editor/blocks/tax-template/Edit.js:152
msgid "List view"
msgstr "Vizualizare lista"

#: static/js/editor/blocks/post-template/Edit.js:176
#: static/js/editor/blocks/tax-template/Edit.js:158
msgid "Grid view"
msgstr "Vizualizare grila"

#: static/js/editor/blocks/post-template/Edit.js:209
#: static/js/editor/blocks/tax-template/Edit.js:190
msgid "Tablet Columns"
msgstr "Coloane pentru tableta"

#: static/js/editor/blocks/post-template/Edit.js:225
#: static/js/editor/blocks/tax-template/Edit.js:206
msgid "Mobile Columns"
msgstr "Coloane pentru mobil"

#: static/js/editor/blocks/post-template/index.js:13
msgid "Post Template"
msgstr "Sablon postare"

#: static/js/editor/blocks/query/Edit.js:120
#: static/js/editor/blocks/tax-query/Edit.js:123
msgid "Reset layout"
msgstr "Reseteaza layout"

#: static/js/editor/blocks/query/Edit.js:186
msgid "Pagination"
msgstr "Paginatie"

#: static/js/editor/blocks/query/Edit.js:208
#: static/js/editor/blocks/tax-query/Edit.js:189
msgid "Block ID"
msgstr "ID block"

#: static/js/editor/blocks/query/Edit.js:214
#: static/js/editor/blocks/tax-query/Edit.js:195
msgid "Please look at the documentation for more information on why this is useful."
msgstr "Te rugăm să consulți documentația pentru mai multe informații despre de ce este util acest lucru."

#: static/js/editor/blocks/query/edit/PatternSelectionModal.js:82
#: static/js/editor/blocks/tax-query/edit/PatternSelectionModal.js:68
msgid "Choose a pattern"
msgstr "Alege un model"

#: static/js/editor/blocks/query/edit/PatternSelectionModal.js:91
#: static/js/editor/blocks/tax-query/edit/PatternSelectionModal.js:77
msgid "Search for patterns"
msgstr "Cauta dupa sabloane"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:133
msgid "Publish Date"
msgstr "Data publicarii"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:173
msgid "Menu Order"
msgstr "Ordinea meniului"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:216
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:187
msgid "Order"
msgstr "Ordine"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:224
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:195
msgid "Descending"
msgstr "Coborare"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:232
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:203
msgid "Ascending"
msgstr "Urcare"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:275
msgid "Sticky Posts"
msgstr "Postari Sticky"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:299
msgid "Only"
msgstr "Doar"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:318
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:222
msgid "Parameters"
msgstr "Parametri"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:105
#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:91
msgid "Create Custom Layout"
msgstr "Creeaza un layout personalizat"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:37
#: static/js/editor/blocks/query/index.js:12
msgid "Advanced Posts"
msgstr "Postari avansate"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:41
msgid "Inherit the Customizer layout, start with a pattern or create a custom layout"
msgstr "Mosteneste layout-ul din Customizer, începe cu un model sau creeaza un layout personalizat."

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:56
msgid "Inherit From Customizer"
msgstr "Mostenit de la customizer"

#: static/js/editor/blocks/query/edit/PostsPlaceholder.js:66
#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:57
msgid "Choose Pattern"
msgstr "Alege modelul"

#: static/js/editor/blocks/query/edit/TaxonomyControls.js:167
msgid "Search for a term"
msgstr "Cauta un termen"

#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:47
msgid "Include %s"
msgstr "Includeti %s"

#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:75
msgid "Related"
msgstr "Asemănătoare"

#: static/js/editor/blocks/query/index.js:13
#: static/js/editor/blocks/tax-query/index.js:13
msgid "Create advanced queries based on your specified criterias."
msgstr "Creează interogări avansate bazate pe criteriile specificate."

#: static/js/editor/blocks/search/Edit.js:163
msgid "Button Outside"
msgstr "Buton Exterior"

#: static/js/editor/blocks/search/Edit.js:180
msgid "Use button with text"
msgstr "Utilizează buton cu text"

#: static/js/editor/blocks/search/Edit.js:282
msgid "Button Icon Color"
msgstr "Culoarea Iconiței Butonului"

#: static/js/editor/blocks/search/Edit.js:379
msgid "Dropdown Background Color"
msgstr "Culoarea de Fundal a Meniului Derulant"

#: static/js/editor/blocks/search/Edit.js:400
msgid "Dropdown Shadow Color"
msgstr "Culoarea Umbrei Meniului Derulant"

#: static/js/editor/blocks/search/Preview.js:24
msgid "Select category"
msgstr "Selecteaza categoria"

#: static/js/editor/blocks/search/index.js:16
msgid "Advanced Search"
msgstr "Căutare Avansată"

#: static/js/editor/blocks/search/index.js:17
msgid "Quickly find specific content on your site."
msgstr "Găsește rapid conținut specific pe site-ul tău."

#: static/js/editor/blocks/share-box/Edit.js:110
#: static/js/editor/blocks/socials/Edit.js:110
msgid "Icons Background Colors"
msgstr "Culorile de Fundal ale Pictogramelor"

#: static/js/editor/blocks/share-box/Edit.js:142
#: static/js/editor/blocks/socials/Edit.js:142
msgid "Icons Border Colors"
msgstr "Culorile Bordurii Pictogramelor"

#: static/js/editor/blocks/share-box/index.js:15
msgid "Share Box Controls"
msgstr "Controale Casetă de Distribuire"

#: static/js/editor/blocks/share-box/index.js:45
msgid "Share content on social media, boosting visibility & engagement."
msgstr "Distribuie conținut pe rețelele sociale, sporind vizibilitatea și implicarea."

#: static/js/editor/blocks/socials/index.js:15
msgid "Socials Controls"
msgstr "Controale pentru retelele de socializare"

#: static/js/editor/blocks/socials/index.js:45
msgid "Display your social media profiles and boost the site engagement."
msgstr "Afișeaza profilurile tale de pe retelele sociale și creste implicarea pe site."

#: static/js/editor/blocks/socials/index.js:47
#: static/js/editor/blocks/widgets-wrapper/index.js:58
msgid "Socials"
msgstr "Sociale"

#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:120
msgid "ID"
msgstr "ID"

#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:136
msgid "Count"
msgstr "Numarare"

#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:40
#: static/js/editor/blocks/tax-query/index.js:12
msgid "Advanced Taxonomies"
msgstr "Taxonomii avansate"

#: static/js/editor/blocks/tax-query/edit/TermsPlaceholder.js:44
msgid "Start with a pattern or create a custom layout"
msgstr "Incepe cu un model sau creeaza un layout personalizat."

#: static/js/editor/blocks/tax-template/index.js:13
msgid "Taxonomy Template"
msgstr "Sablon de taxonomie"

#: static/js/editor/blocks/widgets-wrapper/Edit.js:81
msgid "Expandable Container"
msgstr "Container extensibil"

#: static/js/editor/blocks/widgets-wrapper/index.js:40
msgid "Widgets Wrapper"
msgstr "Widgets Wrapper"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:41
msgid "Parameters options"
msgstr "Parametrii optiunilor"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:46
msgid "All options are currently hidden"
msgstr "Toate optiunile sunt ascunse acum"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:82
msgid "All options reset"
msgstr "Reseteaza toate optiunile"

#: static/js/editor/components/ToolsPanel/ToolsPanelHeader.js:87
msgid "Reset all"
msgstr "Reseteaza tot"

#: static/js/options/ConditionsManager/ExpireCondition.js:119
msgid "The expiration date cannot be set earlier than the start date."
msgstr "Data de expirare nu poate fi setata mai devreme decat data de inceput."

#: framework/features/demo-install.php:155,
#: framework/features/demo-install/content-installer.php:164,
#: framework/features/demo-install/content-installer.php:159,
#: framework/features/demo-install/demo-register.php:9
msgid "No demo name provided."
msgstr "Numele demo-ului nu a fost furnizat."

#: framework/helpers/exts-configs.php:369,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:248,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:271,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:4
msgid "Stock Scarcity"
msgstr "Lipsa stoc"

#: framework/helpers/exts-configs.php:370
msgid "Show the remaining stock of a product to create a sense of urgency and encourage your visitors to make a purchase."
msgstr "Afiseaza stocul ramas al unui produs pentru a crea un sentiment de urgența si a încuraja vizitatorii sa efectueze o achiziție."

#: framework/views/theme-mismatch.php:36
#: static/js/dashboard/VersionMismatch.js:19
#: static/js/notifications/VersionMismatchNotice.js:27
msgid "Action required - please update Blocksy theme to the latest version!"
msgstr ""

#: framework/views/theme-mismatch.php:41
#: static/js/dashboard/VersionMismatch.js:25
#: static/js/notifications/VersionMismatchNotice.js:35
msgid "We detected that you are using an outdated version of Blocksy theme."
msgstr ""

#: framework/views/theme-mismatch.php:45
#: static/js/dashboard/VersionMismatch.js:32
#: static/js/notifications/VersionMismatchNotice.js:44
msgid "In order to take full advantage of all features the core has to offer - please install and activate the latest version of Blocksy theme."
msgstr "Pentru a beneficia pe deplin de toate functionalitatile pe care le ofera tema, te rugam sa instalezi și sa activezi cea mai recenta versiune a temei Blocksy."

#: framework/extensions/newsletter-subscribe/customizer.php:94,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:127
msgid "Make Name Field Required"
msgstr "Fa campul Nume Obligatoriu"

#: framework/extensions/trending/customizer.php:121
msgid "Tag"
msgstr "Etichetă"

#: framework/extensions/trending/customizer.php:142
msgid "Taxonomy Source"
msgstr "Sursa Taxonomiei"

#: framework/extensions/trending/customizer.php:216
msgid "Module Title Icon Source"
msgstr "Sursa Iconiței Titlului Modulului"

#: framework/extensions/trending/customizer.php:303
msgid "Products Status"
msgstr "Starea Produselor"

#: framework/extensions/trending/customizer.php:307
msgid "On Sale"
msgstr "SALE"

#: framework/extensions/trending/customizer.php:308
msgid "Top Rated"
msgstr "Cel Mai Bine Cotate"

#: framework/extensions/trending/customizer.php:309
msgid "Best Sellers"
msgstr "Cele Mai Vandute"

#: framework/extensions/trending/customizer.php:409
msgid "Show Product Price"
msgstr "Arata pretul produsului"

#: framework/extensions/trending/customizer.php:423
msgid "Show Taxonomy"
msgstr "Arata taxonomii"

#: framework/extensions/trending/customizer.php:441
msgid "Taxonomy Style"
msgstr "Stil Taxonomie"

#: framework/extensions/trending/customizer.php:450
msgid "Underline"
msgstr "Subliniere"

#: framework/extensions/trending/customizer.php:465,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:55
msgid "Image Width"
msgstr "Lățimea Imaginii"

#: framework/extensions/trending/customizer.php:693,
#: framework/extensions/trending/customizer.php:825
msgid "Taxonomy Font"
msgstr "Font Taxonomie"

#: framework/extensions/trending/customizer.php:708,
#: framework/extensions/trending/customizer.php:748
msgid "Taxonomies Font Color"
msgstr "Culoare Font Taxonomii"

#: framework/extensions/trending/customizer.php:780
msgid "Taxonomies Button Color"
msgstr "Culoare Buton Taxonomii"

#: framework/extensions/trending/customizer.php:835
msgid "Taxonomy Font Color"
msgstr "Culoare Font Taxonomie"

#: framework/extensions/trending/customizer.php:860,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:248,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:476,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:494
msgid "Image Border Radius"
msgstr "Raza Bordura Imagine"

#: framework/features/blocks/blocks.php:19
msgctxt "Block pattern category"
msgid "Blocksy"
msgstr "Blocksy"

#: framework/features/blocks/blocks.php:24
msgid "Patterns that contain buttons and call to actions."
msgstr "Modele care contin butoane și apeluri la actiune."

#: framework/features/blocks/blocks.php:71
msgid "Home Page Text"
msgstr "Textul Paginii de Start"

#: framework/features/demo-install/child-theme.php:9
msgid "Sorry, you don't have permission to install child themes."
msgstr "Ne pare rau, nu ai permisiunea de a instala teme copil."

#: framework/features/demo-install/content-eraser.php:23
msgid "Sorry, you don't have permission to erase content."
msgstr "Ne pare rau, nu ai permisiunea de a sterge continutul."

#: framework/features/demo-install/content-installer.php:69
msgid "Sorry, you don't have permission to install content."
msgstr "Ne pare rau, nu ai permisiunea de a instala continut."

#: framework/features/demo-install/content-installer.php:195,
#: framework/features/demo-install/content-installer.php:189
msgid "No demo data found."
msgstr "Nu au fost găsite date demo."

#: framework/features/demo-install/content-installer.php:351,
#: framework/features/demo-install/content-installer.php:346
msgid "No pages to assign."
msgstr "Nu exista pagini de atribuit."

#: framework/features/demo-install/install-finish.php:23
msgid "Sorry, you don't have permission to finish the installation."
msgstr "Ne pare rau, nu ai permisiunea de a finaliza instalarea."

#: framework/features/demo-install/options-import.php:38
msgid "Sorry, you don't have permission to install options."
msgstr "Ne pare rău, nu ai permisiunea de a instala optiuni."

#: framework/features/demo-install/options-import.php:50,
#: framework/features/demo-install/options-import.php:45,
#: framework/features/demo-install/options-import.php:80,
#: framework/features/demo-install/options-import.php:75,
#: framework/features/demo-install/widgets-import.php:48,
#: framework/features/demo-install/widgets-import.php:43
msgid "No demo to install"
msgstr "Nici un Demo de instalat"

#: framework/features/demo-install/plugins-uninstaller.php:9
msgid "Sorry, you don't have permission to uninstall plugins."
msgstr "Ne pare rau, nu ai permisiunea sa dezinstalezi plugin."

#: framework/features/demo-install/required-plugins.php:37
msgid "Sorry, you don't have permission to install plugins."
msgstr "Ne pare rau, nu ai permisiunea sa instalezi plugin."

#: framework/features/demo-install/required-plugins.php:49,
#: framework/features/demo-install/required-plugins.php:44
msgid "No plugins to install."
msgstr "Nici un plugin de instalat"

#: framework/features/demo-install/widgets-import.php:36
msgid "Sorry, you don't have permission to install widgets."
msgstr "Ne pare rau, nu ai permisiunea sa instalezi widgets."

#: framework/features/demo-install/widgets-import.php:79,
#: framework/features/demo-install/widgets-import.php:73
msgid "No widgets to install."
msgstr "Nu există widget-uri de instalat."

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:98
msgid "Fields Gap"
msgstr "Spațiu între Câmpuri"

#: framework/features/blocks/about-me/options.php:21
msgid "User Source"
msgstr "Sursa Utilizatorului"

#: framework/features/blocks/about-me/options.php:26
msgid "Dynamic"
msgstr "Dinamic"

#: framework/features/blocks/about-me/options.php:37
msgid "User"
msgstr "Utilizator"

#: framework/features/blocks/about-me/options.php:92
msgid "Image Shape"
msgstr "Forma Imagine"

#: framework/features/blocks/about-me/options.php:104
msgid "Alignment"
msgstr "Aliniere"

#: framework/features/blocks/about-me/options.php:119,
#: framework/features/blocks/socials/options.php:19
msgid "Social Channels"
msgstr "Canale Sociale"

#: framework/features/blocks/about-me/options.php:195,
#: framework/features/blocks/share-box/options.php:149,
#: framework/features/blocks/socials/options.php:101
msgid "Official"
msgstr "Oficial"

#: framework/features/blocks/about-me/view.php:197
msgid "View Profile"
msgstr "Vizualizeaza Profilul"

#: framework/features/blocks/contact-info/options.php:44
msgid "Contact Information"
msgstr "Informatii de Contact"

#: framework/features/blocks/dynamic-data/options.php:24,
#: framework/features/blocks/dynamic-data/options.php:49
msgid "Date type"
msgstr "Tip Data"

#: framework/features/blocks/dynamic-data/options.php:31
msgid "Published Date"
msgstr "Data Publicării"

#: framework/features/blocks/dynamic-data/options.php:32
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:141
msgid "Modified Date"
msgstr "Data Modificării"

#: framework/features/blocks/dynamic-data/options.php:39
msgid "Default format"
msgstr "Format Implicit"

#: framework/features/blocks/dynamic-data/options.php:41
msgid "Example: January 24, 2022"
msgstr "Exemplu: Ianuarie 24, 2022"

#: framework/features/blocks/dynamic-data/options.php:75
msgid "Custom date format"
msgstr "Format personalizat al datei"

#: framework/features/blocks/dynamic-data/options.php:79
msgid "Enter a date or time"
msgstr "Introdu o data sau o ora"

#: framework/features/blocks/dynamic-data/options.php:96,
#: framework/features/blocks/dynamic-data/options.php:97,
#: framework/features/blocks/dynamic-data/views/wp-field.php:194
msgid "No comments"
msgstr "Fara comentarii"

#: framework/features/blocks/dynamic-data/options.php:102,
#: framework/features/blocks/dynamic-data/options.php:103,
#: framework/features/blocks/dynamic-data/views/wp-field.php:195
msgid "One comment"
msgstr "Un comentariu"

#: framework/features/blocks/dynamic-data/options.php:108
msgid "Multiple comments"
msgstr "Comentarii multiple"

#: framework/features/blocks/dynamic-data/options.php:120
msgid "Separator"
msgstr "Separator"

#: framework/features/blocks/dynamic-data/options.php:132
msgid "Author Field"
msgstr "Autorul campului"

#: framework/features/blocks/dynamic-data/options.php:139
msgid "Nickname"
msgstr "Nickname"

#: framework/features/blocks/dynamic-data/options.php:138
msgid "Display Name"
msgstr "Nume afisat"

#: framework/features/blocks/dynamic-data/options.php:140
msgid "First Name"
msgstr "Prenume"

#: framework/features/blocks/dynamic-data/options.php:141
msgid "Last Name"
msgstr "Nume"

#: framework/features/blocks/dynamic-data/options.php:172
msgid "Link to post"
msgstr "Link catre postare"

#: framework/features/blocks/dynamic-data/options.php:176
msgid "Link to author page"
msgstr "Link catre autorul paginii"

#: framework/features/blocks/dynamic-data/options.php:180
msgid "Link to term page"
msgstr "Link catre pagina cu termeni"

#: framework/features/blocks/dynamic-data/options.php:184
msgid "Link to archive page"
msgstr "Link catre pagina de arhiva"

#: framework/features/blocks/dynamic-data/options.php:197
msgid "Open in new tab"
msgstr "Deschide in pagina noua"

#: framework/features/blocks/dynamic-data/options.php:203
msgid "Link Rel"
msgstr "Relatia linkului"

#: framework/features/blocks/dynamic-data/options.php:220
msgid "Terms accent color"
msgstr "Culoarea de accent a termenilor"

#: framework/features/blocks/search/options.php:147,
#: framework/features/blocks/search/view.php:265
msgid "Select Category"
msgstr "Selecteaza categoria"

#: framework/features/blocks/search/options.php:175,
#: framework/premium/features/premium-header/items/search-input/options.php:164
msgid "Taxonomy Children"
msgstr "Copii ai taxonomiei"

#: framework/features/blocks/search/options.php:205,
#: framework/premium/features/premium-header/items/search-input/options.php:192
msgid "Search Through Taxonomies"
msgstr "Cauta prin taxonomii"

#: framework/features/blocks/search/options.php:209,
#: framework/premium/features/premium-header/items/search-input/options.php:196
msgid "Search through taxonomies from selected custom post types."
msgstr "Cautare prin taxonomii din tipurile de postari personalizate selectate."

#: framework/features/blocks/share-box/options.php:15
msgid "Share Icons"
msgstr ""

#: framework/features/blocks/share-box/options.php:43
msgid "Reddit"
msgstr ""

#: framework/features/blocks/share-box/options.php:49
msgid "Hacker News"
msgstr ""

#: framework/features/blocks/share-box/options.php:67
msgid "Telegram"
msgstr ""

#: framework/features/blocks/share-box/options.php:73
msgid "Viber"
msgstr ""

#: framework/features/blocks/share-box/options.php:79
msgid "WhatsApp"
msgstr ""

#: framework/features/blocks/share-box/options.php:85
msgid "Flipboard"
msgstr ""

#: framework/features/blocks/share-box/options.php:91
msgid "Line"
msgstr ""

#: framework/features/conditions/rules/archive-loop.php:6
msgid "Archive Item with Taxonomy ID"
msgstr "Arhivează Element cu ID Taxonomie"

#: framework/features/conditions/rules/archive-loop.php:13
msgid "WooCommerce Archive Item with Taxonomy ID"
msgstr "Arhivează Element WooCommerce cu ID Taxonomie"

#: framework/features/conditions/rules/archive-loop.php:19
msgid "Archive Loop Speciffic"
msgstr "Specific Arhivă Buclă"

#: framework/features/conditions/rules/posts.php:10
msgid "Post Archives"
msgstr "Arhive Postări"

#: framework/features/conditions/rules/woo.php:54
msgid "Single Product ID"
msgstr "ID Produs Unic"

#: framework/features/conditions/rules/woo.php:59
msgid "Single Product with Taxonomy ID"
msgstr "Produs Unic cu ID Taxonomie"

#: framework/premium/extensions/woocommerce-extra/utils.php:141
msgid "Private: %s"
msgstr "Privat: %s"

#: framework/premium/extensions/woocommerce-extra/utils.php:131
msgid "Protected: %s"
msgstr "Protejat: %s"

#: framework/premium/features/content-blocks/hooks-manager.php:733
msgid "WooCommerce Single Product"
msgstr "Produs Unic WooCommerce"

#: framework/premium/features/media-video/options.php:119
msgid "Video Size"
msgstr "Dimensiune video"

#: framework/premium/features/media-video/options.php:127
#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:192
msgid "Contain"
msgstr "Conține"

#: framework/premium/features/media-video/options.php:128
#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:187
msgid "Cover"
msgstr "Acoperire"

#. translators: placeholder here means the actual URL.
#: framework/premium/features/media-video/options.php:132
msgid "Choose how the video will fill its container. More info about this can be found %shere%s."
msgstr "Alegeți cum va umple videoclipul containerul său. Mai multe informații pot fi găsite %saici%s"

#: framework/features/blocks/dynamic-data/views/avatar-field.php:30
msgid "%s Avatar"
msgstr "%s Avatar"

#: framework/features/blocks/query/block-patterns/posts-layout-1.php:4
msgid "Posts - Layout 1"
msgstr "Postări - Layout 1"

#: framework/features/blocks/query/block-patterns/posts-layout-2.php:4
msgid "Posts - Layout 2"
msgstr "Postări - Layout 2"

#: framework/features/blocks/query/block-patterns/posts-layout-3.php:4
msgid "Posts - Layout 3"
msgstr "Postari - Layout 3"

#: framework/features/blocks/query/block-patterns/posts-layout-4.php:4
msgid "Posts - Layout 4"
msgstr "Postari - Layout 4"

#: framework/features/blocks/query/block-patterns/posts-layout-5.php:4
msgid "Posts - Layout 5"
msgstr "Postari - Layout 5"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-1.php:4
msgid "Taxonomies - Layout 1"
msgstr "Taxonomii - Layout 1"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-2.php:4
msgid "Taxonomies - Layout 2"
msgstr "Taxonomii - Layout 2"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-3.php:4
msgid "Taxonomies - Layout 3"
msgstr "Taxonomii - Layout 3"

#: framework/features/blocks/tax-query/block-patterns/tax-layout-4.php:4
msgid "Taxonomies - Layout 4"
msgstr "Taxonomii - Layout 4"

#: framework/features/header/items/account/options.php:15
msgid "Action Link"
msgstr "Link de actiune"

#: framework/features/header/items/account/options.php:49
msgid "Additional User Info"
msgstr "Informatii suplimentare despre Utilizator"

#: framework/features/header/items/account/options.php:53
msgid "Available fields: {user_email}, {user_name}, {user_role}"
msgstr "Campuri disponibile: {user_email}, {user_name}, {user_role}"

#: framework/features/header/items/account/options.php:142
msgid "Menu"
msgstr "Menu"

#: framework/features/header/items/account/options.php:717
msgid "Items Hover Effect"
msgstr "Efect de Hover pe Articole"

#: framework/features/header/items/account/options.php:727
msgid "Boxed Color"
msgstr "Culoare cutie"

#: framework/features/header/items/account/options.php:1945
msgid "Link Active"
msgstr "Link activ"

#: framework/premium/features/content-blocks/options/popup.php:262
msgid "Close Trigger Delay"
msgstr "Intarziere la declansarea inchiderii"

#: framework/premium/features/content-blocks/options/popup.php:269
msgid "Set the close delay time (in seconds) after the form submit action is detected."
msgstr "Seteaza timpul de intarziere la inchidere (in secunde) dupa ce actiunea de trimitere a formularului este detectata."

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:100,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:813
msgid "Featured Icon/Logo"
msgstr "Iconiță/Logo Evidențiat"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:570
msgid "Upvote"
msgstr "Vot Pozitiv"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:584
msgid "Downvote"
msgstr "Vot Negativ"

#: framework/premium/extensions/woocommerce-extra/features/brands/brands-import-export.php:17,
#: framework/premium/extensions/woocommerce-extra/features/brands/brands-import-export.php:69
msgid "Blocksy Brands"
msgstr "Mărci Blocksy"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/select.php:18
msgid "Choose page"
msgstr "Alegeți pagina"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/select.php:33
msgid "Choose a custom thank you page for this product."
msgstr "Alegeți o pagină personalizată de mulțumire pentru acest produs."

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:49
msgid "Product Image Visibility"
msgstr "Vizibilitatea Imaginii Produsului"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:87
msgid "Product Price & Stock Visibility"
msgstr "Vizibilitatea Prețului și Stocului Produsului"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:42
msgid "Trigger Icon Type"
msgstr "Tip Iconiță Declanșator"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:77
msgid "Trigger Visibility"
msgstr "Vizibilitate Declanșator"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:107
msgid "Trigger Label"
msgstr "Etichetă Declanșator"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:233
msgid "Panel Default State"
msgstr "Starea Implicită a Panoului"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:240
msgid "Closed"
msgstr "Inchis"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:241
msgid "Opened"
msgstr "Deschis"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:488
msgid "Panel AJAX Reveal"
msgstr "Dezvăluire Panou AJAX"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:140
msgid "Autoplay Gallery"
msgstr "Redare Automată Galerie"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:152
msgid "Delay (in seconds)"
msgstr "Întarziere (în secunde)"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:219
msgid "Columns Spacing"
msgstr "Spațiere Coloane"

#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:247,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:275
msgid "Arrows Visibility"
msgstr "Vizibilitatea Săgeților"

#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:11
msgid "Prev/Next Arrow"
msgstr "Săgeată Anterior/Următor"

#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:41
msgid "Prev/Next Background"
msgstr "Fundal Anterior/Următor"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:372,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:160
msgid "Add {items} more items to get free shipping!"
msgstr "Adăugați încă {items} articole pentru a beneficia de livrare gratuită!"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:52
msgid "Count Criteria"
msgstr "Criterii de Numărare"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:69,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:59,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:246,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/price-filter.php:299,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:53,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:34,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:34
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:128
msgid "Price"
msgstr "Pret"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:60
msgid "Items"
msgstr "Articole"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:86
msgid "Goal Items"
msgstr "Articole Țintă"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:90
msgid "Amount of items the client has to buy in order to get free shipping."
msgstr "Numărul de articole pe care clientul trebuie să le cumpere pentru a beneficia de livrare gratuită."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:168
msgid "You can use dynamic code tags such as {items} inside this option."
msgstr "Puteți utiliza etichete de cod dinamic precum {items} în această opțiune."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:195,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:62
msgid "Bar Color"
msgstr "Culoarea Barei"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:187,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:29
msgid "🚨 Hurry up! Only {items} units left in stock!"
msgstr "🚨 Grăbiți-vă! Doar {items} unități rămase în stoc!"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:15
msgid "Stock Threshold"
msgstr "Prag Stoc"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:23
msgid "Show the stock scarcity module when product stock is below this number."
msgstr "Afișați modulul de raritate a stocului când stocul produsului este sub acest număr."

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:27
msgid "Message"
msgstr "Mesaj"

#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:43
msgid "Bar Height"
msgstr "Inaltimea Barei"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:686
msgid "Swatches removed"
msgstr "Mostre Eliminate"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:693
msgid "Swatches saved"
msgstr "Mostre salvate"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:755
msgid "Custom Attributes"
msgstr "Atribute personalizate"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:761,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:800,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:873
msgid "Terms Limit"
msgstr "Limită termeni"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:764,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:803
msgid "Set how many terms you want to display in this attribute."
msgstr "Setează cați termeni dorești să fie afișați pentru acest atribut."

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:774,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:813,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:887
#: static/js/editor/blocks/query/Edit.js:178
#: static/js/editor/blocks/tax-query/Edit.js:165
msgid "Limit"
msgstr "Limita"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:876
msgid "Set how many terms you want to display in each attribute."
msgstr "Setează cați termeni dorești să fie afișați pentru fiecare atribut."

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:179,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:462
msgid "Mixed Swatches"
msgstr "Mostre Mixte"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:233
msgid "Generate Variation URL"
msgstr "Generează URL pentru variație"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:239
msgid "Generate a shareable single product page URL with pre-selected variation attributes."
msgstr "Generează un URL partajabil pentru pagina unui produs unic cu atributele variației preselectate."

#: static/js/options/ConditionsManager/SingleCondition.js:446
msgid "Display if query string is present in URL"
msgstr "Afișează dacă șirul de interogare este prezent în URL"

#: static/js/options/DisplayCondition.js:62
msgid "Add Conditions"
msgstr "Adauga Conditii"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:68
msgid "Upgrade to the agency plan and get instant access to this starter site and many other features."
msgstr "Treceti la planul pentru agentii si obtineti acces instant la acest template si multe alte functionalitati."

#: static/js/dashboard/screens/Extensions/CurrentExtension.js:316
msgid "Documentation"
msgstr "Documentatie"

#: static/js/dashboard/screens/Extensions/CurrentExtension.js:346
msgid "Manage"
msgstr "Administreaza"

#: static/js/dashboard/screens/Extensions/CurrentExtension.js:86
msgid "Video Tutorial"
msgstr "Tutorial Video"

#: static/js/options/ConditionsManager.js:207
msgid "Advanced Mode"
msgstr "Mod avansat"

#: static/js/options/ConditionsManager/PostIdPicker.js:78
msgid "Select product"
msgstr "Selecteaza produsul"

#: static/js/options/ConditionsManager/ScheduleDate.js:149
msgid "Monday"
msgstr "Luni"

#: static/js/options/ConditionsManager/ScheduleDate.js:154
msgid "Tuesday"
msgstr "Marti"

#: static/js/options/ConditionsManager/ScheduleDate.js:159
msgid "Wednesday"
msgstr "Miercuri"

#: static/js/options/ConditionsManager/ScheduleDate.js:164
msgid "Thursday"
msgstr "Joi"

#: static/js/options/ConditionsManager/ScheduleDate.js:169
msgid "Friday"
msgstr "Vineri"

#: static/js/options/ConditionsManager/ScheduleDate.js:174
msgid "Saturday"
msgstr "Sambata"

#: static/js/options/ConditionsManager/ScheduleDate.js:179
msgid "Sunday"
msgstr "Duminica"

#: static/js/options/ConditionsManager/ScheduleDate.js:21
msgid "Mon"
msgstr "Luni"

#: static/js/options/ConditionsManager/ScheduleDate.js:211
msgid "Start Time"
msgstr "Timp de pornire"

#: static/js/options/ConditionsManager/ScheduleDate.js:22
msgid "Tue"
msgstr "Marti"

#: static/js/options/ConditionsManager/ScheduleDate.js:23
msgid "Wed"
msgstr "Miercuri"

#: static/js/options/ConditionsManager/ScheduleDate.js:234
msgid "Stop Time"
msgstr "Timp de oprire"

#: static/js/options/ConditionsManager/ScheduleDate.js:24
msgid "Thu"
msgstr "Joi"

#: static/js/options/ConditionsManager/ScheduleDate.js:25
msgid "Fri"
msgstr "Vineri"

#: static/js/options/ConditionsManager/ScheduleDate.js:26
msgid "Sat"
msgstr "Sambata"

#: static/js/options/ConditionsManager/ScheduleDate.js:27
msgid "Sun"
msgstr "Duminica"

#: static/js/options/ConditionsManager/ScheduleDate.js:58
msgid "Every day"
msgstr "Orice zi"

#: static/js/options/ConditionsManager/ScheduleDate.js:66
msgid "Only weekends"
msgstr "Sambata si Duminica"

#: static/js/options/ConditionsManager/ScheduleDate.js:74
msgid "Only weekdays"
msgstr "De luni pana vineri"

#: static/js/options/ConditionsManager/SingleCondition.js:325
msgid "Select sub field"
msgstr "Selecteaza camp secundar"

#: static/js/options/ConditionsManager/SingleCondition.js:378
msgid "Display based on referer domain"
msgstr "Afișează in baza domeniului de referinta"

#: static/js/options/ConditionsManager/SingleCondition.js:412
msgid "Display if cookie is present"
msgstr "Afiseaza cookie-ul prezent"

#: static/js/dashboard/helpers/useUpsellModal.js:42
msgid "Upgrade to the agency plan and get instant access to this and many other features."
msgstr "Treceti la planul pentru agentii si obtineti acces instant si beneficiati de multe alte functionalitati"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:53
msgid "This is a Pro starter site"
msgstr "Acesta e un template care poate fi accesat doar cu licenta platita"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:54
msgid "Upgrade to any pro plan and get instant access to this starter site and many other features."
msgstr "Treceti la orice plan platit si obtineti acces instant la acest template si multe alte functionalitati."

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:61
msgid "Upgrade to the business or agency plan and get instant access to this starter site and many other features."
msgstr "Upgrade to the business or agency plan and get instant access to this starter site and many other features."

#: static/js/dashboard/helpers/useProExtensionInFree.js:14
msgid "This is a Pro extension"
msgstr "Aceasta este o extensie Pro"

#: static/js/dashboard/helpers/useUpsellModal.js:103
#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:115
msgid "Agency"
msgstr "Agentie"

#: static/js/dashboard/helpers/useUpsellModal.js:11
#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:14
msgid "Free"
msgstr "Gratuit"

#: static/js/dashboard/helpers/useUpsellModal.js:122
msgid "Compare Plans"
msgstr "Compara Planurile"

#: static/js/dashboard/helpers/useUpsellModal.js:17
#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:107
msgid "Personal"
msgstr "Personal"

#: static/js/dashboard/helpers/useUpsellModal.js:27
msgid "This is a Pro feature"
msgstr "Aceasta este o functie PRO"

#: static/js/dashboard/helpers/useUpsellModal.js:28
msgid "Upgrade to any pro plan and get instant access to this and many other feature."
msgstr "Fă upgrade la orice plan Pro și obține acces instantaneu la această funcție și la multe altele."

#: static/js/dashboard/helpers/useUpsellModal.js:35
msgid "Upgrade to the business or agency plan and get instant access to this and many other features."
msgstr "Fă upgrade la planul Business sau Agenție și obține acces instantaneu la această funcție și la multe altele."

#: static/js/dashboard/NoTheme.js:31
msgid "In order to take full advantage of all features it has to offer - please install and activate the Blocksy theme also."
msgstr "Pentru a profita din plin de toate funcțiile oferite, te rugăm să instalezi și să activezi tema Blocksy."

#: static/js/dashboard/NoTheme.js:65
msgid "Install and activate the Blocksy theme"
msgstr "Instalează și activează tema Blocksy"

#: static/js/dashboard/NoTheme.js:18
msgid "Action Required - Install Blocksy Theme"
msgstr "Acțiune necesară - Instalează tema Blocksy"

#: static/js/dashboard/NoTheme.js:24
msgid "Blocksy Companion is the complementary plugin to Blocksy theme. It adds a bunch of great features to the theme and acts as an unlocker for the Blocksy Pro package."
msgstr "Blocksy Companion este pluginul complementar pentru tema Blocksy. Adaugă o mulțime de funcționalități excelente temei și acționează ca un deblocator pentru pachetul Blocksy Pro."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:423
msgid "Show Image Frame"
msgstr "Afișează rama imaginii"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:444
msgid "Show Label"
msgstr "Afiseaza eticheta"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:457
msgid "Show Counter"
msgstr "Afiseaza contorul"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:164
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:82
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:526
msgid "Show Reset Button"
msgstr "Afiseaza butonul RESET"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:168
msgid "Show or hide reset filter button."
msgstr "Afișează sau ascunde butonul de resetare a filtrului."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:9
msgid "Shop Filters Controls"
msgstr "Controale filtre magazin"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:135
msgid "Shop Filters"
msgstr "Filtre magazin"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:10
msgid "Widget for filtering the WooCommerce products loop by category, attribute or brand."
msgstr "Widget pentru filtrarea listei de produse WooCommerce după categorie, atribut sau marcă."

#: framework/premium/static/js/blocks/ContentBlock.js:26
msgid "Insert a specific Content Block anywhere on the site."
msgstr "Inserează un Bloc de Conținut specific oriunde pe site."

#: framework/premium/static/js/hooks/CreateHook.js:156
msgid "Nothing Found Template"
msgstr "Șablon \"Nimic găsit\""

#: framework/premium/static/js/hooks/CreateHook.js:164
msgid "Maintenance Template"
msgstr "Șablon de mentenanță"

#: framework/premium/static/js/media-video/components/EditVideoMeta.js:44
msgid "Video Options"
msgstr "Opțiuni video"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:75
msgid "Display order overhiew section."
msgstr "Afișează secțiunea de prezentare generală a comenzii."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:90
msgid "Order Details"
msgstr "Detalii comandă"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:94
msgid "Display order details section."
msgstr "Afișează secțiunea detalii comandă."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:177
msgid "Filter By"
msgstr "Filtrează după"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:92
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:221
msgid "Attribute"
msgstr "Atribut"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:11
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:145
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:192
msgid "Display Type"
msgstr "Tip de afișare"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:154
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:200
msgid "List"
msgstr "Listă"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:387
msgid "Image Aspect Ratio"
msgstr ""

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:408
msgid "Image Max width"
msgstr ""

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:233
msgid "Multiple Selections"
msgstr "Selecții multiple"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:237
msgid "Allow selecting multiple items in a filter."
msgstr "Permite selectarea mai multor elemente într-un filtru."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:118
msgid "Select attribute"
msgstr "Selecteaza atributul"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:305
msgid "Show Hierarchy"
msgstr "Arata Ierarhia"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:321
msgid "Expandable"
msgstr "Extensibil"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:336
#: static/js/editor/blocks/widgets-wrapper/Edit.js:95
msgid "Expanded by Default"
msgstr "Extins implicit"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:283
msgid "Show Checkboxes"
msgstr "Afișează casetele de selectare"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:364
msgid "Show Brands Images"
msgstr ""

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:368
msgid "Show Swatches"
msgstr "Afișează mostre"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/VariableTab.js:30
msgid "Upload only the %s.woff2%s or %s.ttf%s font file formats. Don't convert variable fonts by yourself. Ask the font provider to hand a correct file or the %svariable%s font won't work."
msgstr "Încarcă doar formatele de fișiere font %s.woff2%s sau %s.ttf%s. Nu converti manual fonturile variabile. Solicită furnizorului de fonturi să ofere un fișier corect, altfel fontul %svariabil%s nu va funcționa."

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/AdvancedTab.js:54
msgid "Preload Subsets"
msgstr "Preîncarcă subseturi"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/GeneralTab.js:10
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:65
msgid "Select Variations"
msgstr "Selectează variații"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:255
msgid "Local Google Fonts Settings"
msgstr "Setări fonturi Google locale"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:267
msgid "Select font"
msgstr "Selecteaza font"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:335
msgid "Download Font"
msgstr "Descarca font"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:217
msgid "Save Font"
msgstr "Salveaza font"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:608
msgid "Companion Plugin Details"
msgstr "Detalii plugin companion"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Disabled.js:7
msgid "Please select a valid attribute."
msgstr "Te rugăm să selectezi un atribut valid."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:41
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:130
msgid "Exclude %s"
msgstr "Exclude %s"

#: framework/features/conditions/rules/woo.php:35
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:31
msgid "Product Attributes"
msgstr "Atributele produsului"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/CustomerDetails.js:10
msgid "Billing address"
msgstr "Adresă de facturare"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/CustomerDetails.js:34
msgid "Shipping address"
msgstr "Adresă de livrare"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:64
msgid "Subtotal"
msgstr "Subtotal"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:77
msgid "Shipping"
msgstr "Transport"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:79
msgid "Free shipping"
msgstr "Transport gratuit"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:83
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:42
msgid "Payment method"
msgstr "Metoda de plata"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:85
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:43
msgid "Cash on delivery"
msgstr "Plata la livrare"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:88
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:28
msgid "Total"
msgstr "Total"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:8
msgid "Order number"
msgstr "Numarul comenzii"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:108
msgid "Customer Details"
msgstr "Detaliile clientului"

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:112
msgid "Display customer details section."
msgstr "Afișează secțiunea detalii client."

#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/index.js:71
msgid "Order Overview"
msgstr "Prezentare generală a comenzii"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:89
msgid "Once you insert your %sProject ID%s and click the \"Fetch Fonts\" button, your fonts will become available in all theme’s typography options."
msgstr "După ce introduci %sID-ul Proiectului%s și faci clic pe butonul „Fetch Fonts”, fonturile tale vor deveni disponibile în toate opțiunile de tipografie ale temei."

#: framework/premium/extensions/custom-fonts/dashboard-static/js/CustomFonts.js:135
#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:320
msgid "Upload Font"
msgstr "Incarca font"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/PreloadTab.js:14
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/AdvancedTab.js:19
msgid "Preload Variations"
msgstr "Preîncarcă variații"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:356
msgid "Simple Font"
msgstr "Font simplu"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:360
msgid "Variable Font"
msgstr "Font variabil"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:364
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:116
msgid "Preload"
msgstr "Preîncarcă"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:174
#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:20
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:342
msgid "Available Fonts"
msgstr "Fonturi disponibile"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:322
msgid "More information on how to generate an API key for Campaign Monitor can be found %shere%s."
msgstr "Mai multe informații despre cum să generezi o cheie API pentru Campaign Monitor pot fi găsite %saici%s."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:83
msgid "Connect Newsletter Provider"
msgstr "Conectează furnizorul de newsletter"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:96
msgid "Provider"
msgstr "Furnizor"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/ListPicker.js:91
msgid "Fetching..."
msgstr "Se încarcă..."

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:164
msgid "Please enter a valid Project ID to get all fonts."
msgstr "Te rugăm să introduci un ID de Proiect valid pentru a obține toate fonturile."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:254
msgid "This provider is used only for testing purposes. It doesnt register any real subscribers."
msgstr "Acest furnizor este utilizat doar în scopuri de testare. Nu înregistrează abonați reali."

#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:486,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:520,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:555,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:589,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:428,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:463,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:494,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:524
msgid "Badge Color"
msgstr "Culoare insignă"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:221
#: static/js/editor/blocks/search/Edit.js:281
msgid "Button Text Color"
msgstr "Culoare text buton"

#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/index.js:17
msgid "Newsletter Controls"
msgstr "Controale newsletter"

#: framework/features/blocks/search/options.php:126,
#: framework/premium/features/premium-header/items/search-input/options.php:119
msgid "Live Results Product Status"
msgstr "Rezultate live - Status produs"

#: framework/features/blocks/search/options.php:138,
#: framework/premium/features/premium-header/items/search-input/options.php:132
msgid "Taxonomy Filter"
msgstr "Filtru taxonomie"

#: framework/features/blocks/search/options.php:156,
#: framework/premium/features/premium-header/items/search-input/options.php:144
msgid "Filter Visibility"
msgstr "Vizibilitate filtru"

#: framework/premium/features/premium-header/items/search-input/options.php:795
msgid "Input Border Radius"
msgstr "Rază de colț pentru bordura câmpului de introducere"

#: framework/premium/features/premium-header/items/search-input/options.php:823
msgid "Dropdown Font"
msgstr "Font pentru meniul derulant"

#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:604
msgid "You don't have any products in your compare list yet."
msgstr "Nu ai încă produse în lista de comparație."

#: framework/features/blocks/dynamic-data/views/woo-field.php:25,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:481
#: static/js/editor/blocks/dynamic-data/preview-parts/woo/StockPreview.js:6
msgid "In Stock"
msgstr "In stoc"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:12,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:37,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/inline-filter.php:78,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/list-filter.php:109
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:198
msgid "Active Filters"
msgstr "Filtre active"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:22
msgid "Active Filters Label"
msgstr "Eticheta filtre active"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/inline-filter.php:26,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/views/list-filter.php:31
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:32
msgid "Reset Filters"
msgstr "Reseteaza filtrele"

#: framework/premium/extensions/woocommerce-extra/features/filters/includes/filter-presenter.php:149,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/price-filter.php:188
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/Preview.js:59
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Preview.js:183
msgid "Reset Filter"
msgstr "Reseteaza filtru"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:728,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:24,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:46,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:142
msgid "Color"
msgstr "Culoare"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:78
msgid "Short Name"
msgstr "Nume scurt"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:245
msgid "Sorry, this product is unavailable. Please choose a different combination."
msgstr "Ne pare rau, acest produs nu este disponibil. Te rugam să alegi o combinatie diferita."

#: framework/features/blocks/dynamic-data/views/woo-field.php:26,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:246,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:481,
#: framework/premium/extensions/woocommerce-extra/features/swatches/includes/swatch-element-render.php:47,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table-single-product-row.php:57
#: static/js/editor/blocks/dynamic-data/preview-parts/woo/StockPreview.js:7
msgid "Out of Stock"
msgstr "Fara stoc"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:540
msgid "Display Variations Inline"
msgstr "Afișează variațiile în linie"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:636,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:736,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:833
msgid "Swatches"
msgstr "Mostre"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:16,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:250
msgid "Color Swatches"
msgstr "Mostre de culoare"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:20,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:74,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:129,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:183
msgid "Swatch Shape"
msgstr "Formă mostre"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:26,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:80,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:135,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:189
msgid "Round"
msgstr "Rotund"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:87,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:142,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:196
msgid "Single Page Swatch Size"
msgstr "Dimensiunea mostrei pe pagină unică"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:42,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:96,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:151,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:205
msgid "Widget Swatch Size"
msgstr "Dimensiunea mostrei pentru widget"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:111,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:166,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:220
msgid "Archive Cards Swatch Size"
msgstr "Dimensiunea mostrei pentru cardurile arhivei"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:70,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:294
msgid "Image Swatches"
msgstr "Mostre de imagine"

#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:125,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:338
msgid "Button Swatches"
msgstr "Buton pentru mostre"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:395
msgid "Wishlist Button"
msgstr "Buton pentru lista de dorințe"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:58
msgid "Specific Product Variation "
msgstr "Variație specifică de Produs "

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:61
msgid "This option will allow you to add a speciffic product variation to wishlist."
msgstr "Această opțiune îți permite să adaugi o variație specifică a unui produs în lista de dorințe."

#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:601,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/views/table-no-results.php:3,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:19
msgid "Browse products"
msgstr "Caută produse"

#: framework/features/blocks/contact-info/options.php:533,
#: framework/premium/features/premium-header/items/contacts/options.php:417
msgid "Link Icons"
msgstr "Link pentru pictograme"

#: framework/premium/features/premium-header/items/contacts/view.php:59
msgid "Download"
msgstr "Descărcare"

#: framework/premium/features/premium-header/items/divider/options.php:22,
#: framework/premium/features/premium-header/items/divider/options.php:51,
#: framework/premium/features/premium-header/items/divider/options.php:66,
#: framework/premium/features/premium-header/items/divider/options.php:81
msgid "Style & Color"
msgstr "Stil și culoare"

#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:87
msgid "Dropdown Arrow"
msgstr "Săgeată meniu derulant"

#: framework/premium/features/premium-header/items/menu-tertiary/config.php:4
msgid "Menu 3"
msgstr "Meniu 3"

#: framework/features/blocks/search/options.php:119,
#: framework/premium/features/premium-header/items/search-input/options.php:111
msgid "Live Results Product Price"
msgstr "Rezultate live - Preț produs"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:274
msgid "Add New Size Guide"
msgstr "Adaugă un nou ghid de mărimi"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:275
msgid "Edit Size Guide"
msgstr "Editează ghidul de mărimi"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:276
msgid "New Size Guide"
msgstr "Ghid de mărimi nou"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:278
msgid "View Size Guide"
msgstr "Vizualizează ghidul de mărimi"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:279
msgid "Search Size Guides"
msgstr "Caută ghiduri de mărimi"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/modal-view.php:25,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/side-view.php:34
msgid "Close Sizes Modal"
msgstr "Închide modalul de mărimi"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:29
msgid "Size Guide Display Conditions"
msgstr "Condiții de afișare pentru ghidul de mărimi"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:30
msgid "Choose where you want this size guide to be displayed."
msgstr "Alege unde dorești ca acest ghid de mărimi să fie afișat."

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:243
msgid "Sorry, no products matched your selection. Please choose a different combination."
msgstr "Ne pare rău, niciun produs nu se potrivește selecției tale. Te rugăm să alegi o altă combinație."

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:244
msgid "Please select some product options before adding this product to your cart."
msgstr "Te rugăm să selectezi câteva opțiuni ale produsului înainte de a-l adăuga în coș."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:76
msgid "Amount the client has to reach in order to get free shipping."
msgstr "Suma pe care clientul trebuie să o atingă pentru a beneficia de transport gratuit."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:104
msgid "The calculation method will be based on WooCommerce zones."
msgstr "Metoda de calcul va fi bazată pe zonele WooCommerce."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:119
msgid "Discount Calculation"
msgstr "Calculul reducerii"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:129
msgid "Include or exclude the discount code when calculating the shipping progress."
msgstr "Include sau exclude codul de reducere atunci când calculezi progresul pentru transportul gratuit."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:133,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:158
msgid "Default Message"
msgstr "Mesaj implicit"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:143
msgid "You can use dynamic code tags such as {price} inside this option."
msgstr "Poți folosi taguri de cod dinamice, cum ar fi {price}, în această opțiune."

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:174
msgid "Success Message"
msgstr "Mesaj de succes"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:5,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:271,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:277
msgid "Size Guides"
msgstr "Ghiduri de mărimi"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:16
msgid "Size Guide Placement"
msgstr "Plasarea ghidului de mărimi"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:23
msgid "Side Panel"
msgstr "Panou lateral"

#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:33
msgid "Reveal From"
msgstr "Dezvăluire din"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:303,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:30
msgid "Columns & Products"
msgstr "Coloane și produse"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:330,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:57
msgid "Number of products"
msgstr "Număr de produse"

#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:32
msgid "Share Box Icons Color"
msgstr "Culoare pictograme cutie partajare"

#: framework/helpers/exts-configs.php:330,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:180,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:4
msgid "Free Shipping Bar"
msgstr "Bară transport gratuit"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:183
msgid "Show if cart is empty"
msgstr "Afișează dacă coșul este gol"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:382,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:176
msgid "Congratulations! You got free shipping 🎉"
msgstr "Felicitări! Ai transport gratuit 🎉"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:359,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:135
msgid "Add {price} more to get free shipping!"
msgstr "Adaugă {price} în plus pentru a beneficia de transport gratuit!"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:15
msgid "Show In Cart Page"
msgstr "Afișează pe pagina coșului"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:21
msgid "Show In Checkout Page"
msgstr "Afișează pe pagina de checkout"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:27
msgid "Show In Mini Cart"
msgstr "Afișează în mini coș"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:34
msgid "Calculation Method"
msgstr "Metodă de calcul"

#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:69
msgid "Goal Amount"
msgstr "Sumă obiectiv"

#: framework/premium/extensions/woocommerce-extra/features/filters/feature.php:126
msgid "Automatically scroll page to top after user interaction."
msgstr "Derulează automat pagina în sus după interacțiunea utilizatorului."

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-cart/feature.php:167
msgid "Shopping Cart"
msgstr "Coș de cumpărături"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-cart/feature.php:169
msgid "Close cart drawer"
msgstr "Închide sertarul coșului"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/feature.php:152,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:525
msgid "WooCommerce Filters Canvas"
msgstr "Canvas pentru filtre WooCommerce"

#: framework/premium/extensions/shortcuts/customizer.php:477,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:4
msgid "Filters Canvas"
msgstr "Filtre Canvas"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:177
msgid "Panel Height"
msgstr "Înălțimea panoului"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:187
msgid "Auto"
msgstr "Automat"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:198
msgid "Custom Height"
msgstr "Înălțime personalizată"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:216
msgid "Panel Columns"
msgstr "Coloane panou"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:279,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:100
msgid "Panel Backdrop"
msgstr "Fundal panou"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:516
msgid "Widget Area Source"
msgstr "Sursa zonei widget"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:524
msgid "WooCommerce Sidebar"
msgstr "Sidebar WooCommerce"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:110,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:242
msgid "Days"
msgstr "Zile"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:111,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:256
msgid "Hours"
msgstr "Ore"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:115,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:270
msgid "Min"
msgstr "Min"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:116,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:284
msgid "Sec"
msgstr "Secunde"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:298,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:330
msgid "Hurry up! This sale ends in"
msgstr "Grăbește-te! Această ofertă se încheie în"

#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:324
msgid "Countdown Box"
msgstr "Caseta de numărătoare inversă"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/helpers.php:36
msgid "Quick view"
msgstr "Vizualizare rapidă"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:19
msgid "Additional Actions"
msgstr "Acțiuni suplimentare"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:25
msgid "Buttons Type"
msgstr "Tipuri de butoane"

#: framework/extensions/trending/customizer.php:449,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:80,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:727
msgid "Button"
msgstr "Buton"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:72
msgid "Modal Trigger"
msgstr "Declanșator modal"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:82
msgid "Card"
msgstr "Card"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:91
msgid "Modal Width"
msgstr "Lățime modal"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:102
msgid "Product Navigation"
msgstr "Navigare produse"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:108
msgid "Display next/previous buttons that will help to easily navigate through products."
msgstr "Afișează butoanele anterior/următor pentru a naviga ușor între produse."

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:175,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:412,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:130,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:138
msgid "Title Font"
msgstr "Font titlu"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:210,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:442,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:168
msgid "Price Font"
msgstr "Font preț"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:219,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:450,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:176
msgid "Price Color"
msgstr "Culoare preț"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:219
msgid "Add To Cart Button"
msgstr "Buton Adaugă în coș"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:291
msgid "View Cart Button"
msgstr "Buton Vizualizează coșul"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:361
msgid "Product Page Button"
msgstr "Buton Pagină produs"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:46
msgid "How many days the products will be marked as \"New\" after creation."
msgstr "Câte zile produsele vor fi marcate ca \"Noi\" după crearea lor."

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:136,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:142
msgid "Product Tabs"
msgstr "Taburi produse"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:137
msgid "Product Tab"
msgstr "Tab produs"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:139
msgid "Add New Product Tab"
msgstr "Adaugă un tab nou pentru produs"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:140
msgid "Edit Product Tab"
msgstr "Editează tabul produsului"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:141
msgid "New Product Tab"
msgstr "Tab produs nou"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:143
msgid "View Product Tab"
msgstr "Vizualizează tabul produsului"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:144
msgid "Search Product Tabs"
msgstr "Caută taburi produse"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:10
msgid "Product Tab Display Conditions"
msgstr "Condiții de afișare pentru taburile produsului"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:11
msgid "Choose where you want this product tab to be displayed."
msgstr "Alege unde dorești să fie afișat acest tab al produsului."

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:27
msgid "Tab Order"
msgstr "Ordinea taburilor"

#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:32
msgid "Default tabs order: Description - 10, Additional Information - 20, Reviews - 30."
msgstr "Ordine taburi implicită: Descriere - 10, Informații suplimentare - 20, Recenzii - 30."

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:106
msgid "Payment Gateways"
msgstr "Metode de plată"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:112,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:65
msgid "Shipping Methods"
msgstr "Metode de livrare"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:265
msgid "Thank you Page"
msgstr "Pagina „Mulțumim”"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:480,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:486
msgid "Thank You Pages"
msgstr "Pagini „Mulțumim”"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:481
msgid "Thank You Page"
msgstr "Pagină „Mulțumim”"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:483
msgid "Add New Thank You Page"
msgstr "Adaugă o pagină nouă „Mulțumim”"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:484
msgid "Edit Thank You Page"
msgstr "Editează pagina „Mulțumim”"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:485
msgid "New Thank You Page"
msgstr "Pagină nouă „Mulțumim”"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:487
msgid "View Thank You Page"
msgstr "Vizualizează pagina „Mulțumim”"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:488
msgid "Search Thank You Pages"
msgstr "Caută pagini „Mulțumim”"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:78
msgid "Payment Gateway"
msgstr "Metodă de plată"

#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:106
msgid "Priority"
msgstr "Prioritate"

#: framework/premium/extensions/woocommerce-extra/features/cart-page/options.php:5
msgid "Coupon Form"
msgstr "Formular cupoane"

#: framework/premium/extensions/woocommerce-extra/features/filters/feature.php:119
msgid "AJAX Filtering"
msgstr "Filtrare AJAX"

#: framework/premium/extensions/woocommerce-extra/features/filters/feature.php:125
msgid "Scroll to Top"
msgstr "Derulează în sus"

#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:276
msgid "Compare Products"
msgstr "Compară produse"

#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:282
msgid "Close Compare Modal"
msgstr "Închide modalul de comparație"

#: framework/premium/extensions/woocommerce-extra/features/compare/helpers.php:93,
#: framework/premium/extensions/woocommerce-extra/features/compare/helpers.php:152
msgid "Add to compare"
msgstr "Adaugă la comparație"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:46
msgid "Compare Placement"
msgstr "Plasarea comparației"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:53
#: static/js/editor/blocks/breadcrumbs/Preview.js:46
msgid "Page"
msgstr "Pagină"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:62
msgid "Select Page"
msgstr "Selectează pagina"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:69
msgid "Select a page where the compare table will be outputted."
msgstr "Selectează o pagină unde va fi afișat tabelul de comparație."

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:75
msgid "Compare Table Fields"
msgstr "Câmpuri tabel de comparație"

#: framework/features/blocks/dynamic-data/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:162
msgid "Length"
msgstr "Lungime"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:130,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:186
msgid "Attributes"
msgstr "Atribute"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:266,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:470
msgid "Availability"
msgstr "Disponibilitate"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:469
msgid "Modal Border Radius"
msgstr "Rază de colț pentru bordura modalului"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:366
msgid "Compare Bar"
msgstr "Bară de comparație"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:394
msgid "Button Icon"
msgstr "Pictogramă buton"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:449
msgid "Compare Bar Display Conditions"
msgstr "Condiții de afișare pentru bara de comparație"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:450
msgid "Add one or more conditions to display the Compare bar."
msgstr "Adaugă una sau mai multe condiții pentru a afișa bara de comparație."

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:463,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:223,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:295,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:365
msgid "Button Font Color"
msgstr "Culoare font buton"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:494,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:253,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:325,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:393
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:250
#: static/js/editor/blocks/search/Edit.js:311
msgid "Button Background Color"
msgstr "Culoare fundal buton"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:15,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:5
msgid "New Badge"
msgstr "Insignă Nou"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:55,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:53
msgid "Featured Badge"
msgstr "Insignă Recomandat"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/feature.php:168,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:82
msgid "HOT"
msgstr "HOT"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/feature.php:202,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:35
msgid "NEW"
msgstr "NOU"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:32,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:79
msgid "Badge Label"
msgstr "Etichetă insignă"

#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:40
msgid "Label Duration"
msgstr "Durata etichetei"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:57
msgid "Allow users to upload images when leaving a review."
msgstr "Permite utilizatorilor să încarce imagini atunci când lasă o recenzie."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:73
msgid "Image Lightbox"
msgstr "Lightbox pentru imagini"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:74
msgid "Allow users to open attached review images in lightbox."
msgstr "Permite utilizatorilor să deschidă imaginile atașate recenziilor într-un lightbox."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:88
msgid "Review Voting"
msgstr "Votare recenzii"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:89
msgid "Allow users to upvote reviews."
msgstr "Permite utilizatorilor să voteze recenziile."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:105
msgid "Allowed Users"
msgstr "Utilizatori permiși"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:106
msgid "Set which users are allowed to vote."
msgstr "Setează care utilizatori au voie să voteze."

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:5
msgid "Affiliate Products"
msgstr "Produse afiliate"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:12
msgid "Product Archive"
msgstr "Arhiva produselor"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:16,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:70
msgid "Image Affiliate Link"
msgstr "Link afiliat imagine"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:27,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:48,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:81
msgid "Open In New Tab"
msgstr "Deschide într-o filă nouă"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:36
msgid "Title Affiliate Link"
msgstr "Link afiliat titlu"

#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:90
msgid "Open Button Link In New Tab"
msgstr "Deschide linkul butonului într-o filă nouă"

#: framework/premium/extensions/woocommerce-extra/features/cart-page/options.php:14
msgid "Quantity Auto Update"
msgstr "Actualizare automată a cantității"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:37
msgid "Product brands base"
msgstr "Bază de branduri pentru produse"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:49
msgid "brand"
msgstr "brand"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:500,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:532
msgid "About Brands"
msgstr "Despre branduri"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:501,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:534
msgid "About %s"
msgstr "Despre %s"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:558,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:578,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:640,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:707,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:718,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:493
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:146
msgid "Brands"
msgstr "Branduri"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:562,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:150,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:177,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:221,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:239,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:257,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:275
msgid "Sticky Row"
msgstr "Rând fix"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:593,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:644
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:257
msgid "Logo Size"
msgstr "Dimensiune logo"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:605,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:656
msgid "Logos Gap"
msgstr "Spațiu între logo-uri"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:708
msgid "Brand"
msgstr "Brand"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:709
msgid "Search Brands"
msgstr "Caută branduri"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:710
msgid "All Brands"
msgstr "Toate brandurile"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:711
msgid "Parent Brand"
msgstr "Brand părinte"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:712
msgid "Parent Brand:"
msgstr "Brand părinte:"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:713
msgid "View Brand"
msgstr "Vizualizează brandul"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:714
msgid "Edit Brand"
msgstr "Editează brandul"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:715
msgid "Update Brand"
msgstr "Actualizează brandul"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:716
msgid "Add New Brand"
msgstr "Adaugă un brand nou"

#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:717
msgid "New Brand Name"
msgstr "Nume nou brand"

#: framework/premium/extensions/woocommerce-extra/features/brands/options.php:5
msgid "Product Brand Tab"
msgstr "Tab pentru brandul produsului"

#: framework/premium/extensions/woocommerce-extra/features/brands/options.php:20
msgid "Brand Name In Tab Title"
msgstr "Numele brandului în titlul tabului"

#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:5
msgid "Product Image"
msgstr "Imagine produs"

#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:54
msgid "Quantity Input"
msgstr "Câmp cantitate"

#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:228
msgid "Compare Button"
msgstr "Buton de comparație"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:139
msgid "Text Hover"
msgstr "Text hover"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:144
msgid "Background Initial"
msgstr "Fundal inițial"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:376
msgid "Star"
msgstr "Stea"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:376
msgid "Stars"
msgstr "Stele"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:442
msgid "%s%% of customers recommend this product."
msgstr "%s%% dintre clienți recomandă acest produs."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:604
msgid "%s of %s found this review helpful"
msgstr "%s din %s au considerat această recenzie utilă"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:666,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:44
msgid "Review Title"
msgstr "Titlu recenzie"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/feature.php:694
msgid "Upload Image (Optional)"
msgstr "Încarcă imagine (Opțional)"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:11
msgid "Reviews Order"
msgstr "Ordinea recenziilor"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:18
msgid "Oldest First"
msgstr "Cele mai vechi primele"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:19
msgid "Newest First"
msgstr "Cele mai noi primele"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:20
msgid "Low Rating First"
msgstr "Rating scăzut primul"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:21
msgid "High Rating First"
msgstr "Rating înalt primul"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:22
msgid "Most Relevant"
msgstr "Cele mai relevante"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:32
msgid "Average Score"
msgstr "Scor mediu"

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:33
msgid "Display an average score for all reviews."
msgstr "Afișează un scor mediu pentru toate recenziile."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:45
msgid "Allow users to add a title when leaving a review."
msgstr "Permite utilizatorilor să adauge un titlu când lasă o recenzie."

#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:56
msgid "Image Upload"
msgstr "Încărcare imagine"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/view.php:78
msgid "Color mode switch"
msgstr "Comutare mod culoare"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:92
msgid "Items Counter"
msgstr "Contor de elemente"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:20,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:13
msgid "Slider"
msgstr "Slider"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:41
msgid "Columns & Posts"
msgstr "Coloane și postări"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:49,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:311,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:38
msgid "Number of columns"
msgstr "Număr de coloane"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:68
msgid "Number of posts"
msgstr "Număr de postări"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:91,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:344,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:75
msgid "Autoplay"
msgstr "Autoplay"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:112,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:356,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:91
msgid "Delay (Seconds)"
msgstr "Întârziere (secunde)"

#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:113,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:357,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:92
msgid "Specify the amount of time (in seconds) to delay between automatically cycling an item."
msgstr "Specifică timpul de întârziere (în secunde) între ciclurile automate ale unui element."

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:140
msgid "Choose the default color mode that a user will see when it visits your site."
msgstr "Alege modul de culoare implicit pe care utilizatorul îl va vedea când vizitează site-ul tău."

#: framework/premium/features/content-blocks/options/popup.php:577
msgid "Enable this option if you want to lock the page scroll while the popup is triggered."
msgstr "Activează această opțiune dacă dorești să blochezi derularea paginii în timp ce popup-ul este declanșat."

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/config.php:4
msgid "Color Switch"
msgstr "Comutare culoare"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:39
msgid "Reverse Icon State"
msgstr "Inversează starea pictogramei"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:102
msgid "Dark Mode Label"
msgstr "Etichetă mod întunecat"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:114
msgid "Light Mode Label"
msgstr "Etichetă mod luminos"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:118,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/view.php:54
msgid "Light Mode"
msgstr "Mod luminos"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:128
msgid "Default Color Mode"
msgstr "Mod de culoare implicit"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:136
msgid "Light"
msgstr "Luminos"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:137
msgid "Dark"
msgstr "Întunecat"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:138
msgid "OS Aware"
msgstr "Compatibil cu sistemul de operare"

#: framework/premium/features/content-blocks/options/popup.php:326
msgid "Set after how many days the popup will relaunch if the additional close trigger is met."
msgstr "Setează după câte zile popup-ul va fi relansat dacă este activat un declanșator suplimentar de închidere."

#: framework/premium/features/content-blocks/options/popup.php:446
msgid "Load Content With AJAX"
msgstr "Încarcă conținut cu AJAX"

#: framework/premium/features/content-blocks/options/popup.php:450
msgid "Enable this option if you want to load the popup content using AJAX."
msgstr "Activează această opțiune dacă dorești să încarci conținutul popup-ului folosind AJAX."

#: framework/premium/features/content-blocks/options/popup.php:459
msgid "Reload Content"
msgstr "Reîncarcă conținutul"

#: framework/premium/features/content-blocks/options/popup.php:466
#: static/js/options/ConditionsManager/ScheduleDate.js:78
msgid "Never"
msgstr "Niciodată"

#: framework/premium/features/content-blocks/options/popup.php:467
msgid "Always"
msgstr "Întotdeauna"

#: framework/premium/features/content-blocks/options/popup.php:469
msgid "Set this option to always if you have dynamic content inside the popup in order to keep everything up to date."
msgstr "Setează această opțiune pe „Întotdeauna” dacă ai conținut dinamic în interiorul popup-ului pentru a păstra totul actualizat."

#: framework/premium/features/content-blocks/options/popup.php:495,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:637
msgid "Popup Visibility"
msgstr "Vizibilitatea popup-ului"

#: framework/premium/features/content-blocks/options/popup.php:573
msgid "Scroll Lock"
msgstr "Blocare derulare"

#: framework/premium/features/content-blocks/options/popup.php:250
msgid "Set the button class selector that will trigger popup to close."
msgstr "Setează selectorul clasei butonului care va declanșa închiderea popup-ului."

#: framework/premium/features/content-blocks/options/popup.php:280
msgid "Relaunch Trigger"
msgstr "Declanșator de relansare"

#: framework/premium/features/content-blocks/options/popup.php:286
msgid "Never relaunch"
msgstr "Nu relansa niciodată"

#: framework/premium/features/content-blocks/options/popup.php:297
msgid "Days After Close"
msgstr "Zile după închidere"

#: framework/premium/features/content-blocks/options/popup.php:303
msgid "Set after how many days the popup will relaunch."
msgstr "Setează după câte zile popup-ul va fi relansat."

#: framework/premium/features/content-blocks/options/popup.php:313
msgid "Days After Form Submit"
msgstr "Zile după trimiterea formularului"

#: framework/premium/features/content-blocks/options/popup.php:317
msgid "Days After Button Click"
msgstr "Zile după clic pe buton"

#: framework/premium/features/content-blocks/options/maintenance.php:16
msgid "Add one or more conditions to display the Maintenance block."
msgstr "Adaugă una sau mai multe condiții pentru a afișa blocul de mentenanță."

#: framework/premium/features/content-blocks/options/popup.php:53
msgid "Popup Display Conditions"
msgstr "Condiții de afișare a popup-ului"

#: framework/premium/features/content-blocks/options/popup.php:54
msgid "Choose where you want this popup to be displayed."
msgstr "Alege unde dorești ca acest popup să fie afișat."

#: framework/premium/features/content-blocks/options/popup.php:78
msgid "On element click"
msgstr "La clic pe element"

#: framework/premium/features/content-blocks/options/popup.php:153
msgid "Close Popup On Scroll Back"
msgstr "Închide popup-ul la derulare înapoi"

#: framework/premium/features/content-blocks/options/popup.php:216
msgid "Additional Close Trigger"
msgstr "Declanșator suplimentar de închidere"

#: framework/premium/features/content-blocks/options/popup.php:223
msgid "On form submit"
msgstr "La trimiterea formularului"

#: framework/premium/features/content-blocks/options/popup.php:224
msgid "On button click"
msgstr "La clic pe buton"

#: framework/premium/features/content-blocks/options/popup.php:235
msgid "The popup will auto-close if a form submit action is detected inside of it."
msgstr "Popup-ul se va închide automat dacă este detectată o acțiune de trimitere a unui formular în interiorul său."

#: framework/premium/features/content-blocks/options/popup.php:247
msgid "Button Class Selector"
msgstr "Selector clasă buton"

#: framework/premium/features/content-blocks/options/hook.php:41,
#: framework/premium/features/content-blocks/options/nothing_found.php:35
msgid "Choose where you want this content block to be displayed."
msgstr "Alege unde dorești să fie afișat acest bloc de conținut."

#: framework/premium/features/content-blocks/options/hook.php:283
msgid "Select a post/page to preview it's content inside the editor while building the hook."
msgstr "Selectează o postare/pagină pentru a previzualiza conținutul său în editor în timp ce construiești hook-ul."

#: framework/premium/features/content-blocks/options/maintenance.php:15
msgid "Maintenance Block Display Conditions"
msgstr "Condiții de afișare pentru blocul de mentenanță"

#: framework/premium/features/media-video/options.php:110
msgid "Display a minimalistic view of the video player."
msgstr "Afișează o vizualizare minimalistă a playerului video."

#: framework/premium/features/performance-typography/feature.php:116
msgid "Preconnect Google Fonts"
msgstr "Preconectează fonturile Google"

#: framework/premium/features/performance-typography/feature.php:127
msgid "Preconnect Adobe Typekit Fonts"
msgstr "Preconectează fonturile Adobe Typekit"

#: framework/features/header/items/account/config.php:4
msgid "Account"
msgstr "Cont"

#: framework/features/header/items/account/options.php:12
msgid "User Info"
msgstr "Informații utilizator"

#: framework/features/header/items/account/options.php:60
msgid "User Avatar"
msgstr "Avatar utilizator"

#: framework/features/header/items/account/options.php:91,
#: framework/features/header/items/account/options.php:658,
#: framework/features/header/items/account/views/login.php:164,
#: framework/features/header/items/account/views/login.php:364
msgid "Edit Profile"
msgstr "Editează profilul"

#: framework/features/header/items/account/options.php:101,
#: framework/features/header/items/account/options.php:105,
#: framework/features/header/items/account/options.php:667,
#: framework/features/header/items/account/views/login.php:170,
#: framework/features/header/items/account/views/login.php:385
msgid "Log Out"
msgstr "Deconectare"

#: framework/features/header/items/account/options.php:185,
#: framework/features/header/items/account/options.php:189,
#: framework/features/header/items/account/views/login.php:591
msgid "Dokan Dashboard"
msgstr "Tabloul de bord Dokan"

#: framework/features/header/items/account/options.php:199,
#: framework/features/header/items/account/options.php:203,
#: framework/features/header/items/account/views/login.php:625
msgid "Dokan Shop"
msgstr "Magazin Dokan"

#: framework/features/header/items/account/options.php:215,
#: framework/features/header/items/account/options.php:219,
#: framework/features/header/items/account/views/login.php:657
msgid "Tutor LMS Dashboard"
msgstr "Tabloul de bord Tutor LMS"

#: framework/features/header/items/account/options.php:235,
#: framework/features/header/items/account/views/login.php:684
msgid "bbPress Dashboard"
msgstr "tabloul de bord bbPress"

#: framework/features/header/items/account/options.php:619,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:32
msgid "Link"
msgstr "Link"

#: framework/features/header/items/account/options.php:630
msgid "Dropdown Items"
msgstr "Elemente din meniul derulant"

#: framework/features/header/items/account/options.php:687
msgid "Link To"
msgstr "Link către"

#: framework/premium/extensions/color-mode-switch/includes/logo-enhancements.php:34
msgid "Dark Mode Logo"
msgstr "Logo mod întunecat"

#: framework/premium/extensions/woocommerce-extra/features/sku-layer.php:60,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:248,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:434
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:140
msgid "SKU"
msgstr "SKU"

#: framework/premium/features/content-blocks/options/404.php:84,
#: framework/premium/features/content-blocks/options/archive.php:161,
#: framework/premium/features/content-blocks/options/header.php:102,
#: framework/premium/features/content-blocks/options/hook.php:211,
#: framework/premium/features/content-blocks/options/maintenance.php:81,
#: framework/premium/features/content-blocks/options/nothing_found.php:102,
#: framework/premium/features/content-blocks/options/single.php:93
msgid "Wide"
msgstr "Larg"

#: framework/premium/features/content-blocks/options/archive.php:10
msgid "Replace Conditions"
msgstr "Condiții de înlocuire"

#: framework/premium/features/content-blocks/options/archive.php:15
msgid "Template Replace Conditions"
msgstr "Condiții de afișare pentru template"

#: framework/premium/features/content-blocks/options/archive.php:16,
#: framework/premium/features/content-blocks/options/header.php:36,
#: framework/premium/features/content-blocks/options/single.php:16
msgid "Choose where you want this template to be displayed."
msgstr "Alege unde dorești să fie afișat acest template."

#: framework/premium/features/content-blocks/options/archive.php:17
msgid "Add Replace Condition"
msgstr "Adaugă o condiție de înlocuire"

#: framework/premium/features/content-blocks/options/archive.php:142,
#: framework/premium/features/content-blocks/options/single.php:74
msgid "Left Sidebar"
msgstr "Sidebar stânga"

#: framework/premium/features/content-blocks/options/archive.php:147,
#: framework/premium/features/content-blocks/options/single.php:79
msgid "Right Sidebar"
msgstr "Sidebar dreapta"

#: framework/premium/features/content-blocks/options/header.php:35,
#: framework/premium/features/content-blocks/options/single.php:15
msgid "Template Display Conditions"
msgstr "Condiții de afișare pentru template"

#: framework/premium/features/content-blocks/options/hook.php:40
msgid "Content Block Display Conditions"
msgstr "Condiții de afișare pentru blocul de conținut"

#. translators: placeholder here means the actual URL.
#: framework/premium/features/media-video/options.php:77
msgid "YouTube won't store information about visitors on your website unless they play the video. More info about this can be found %shere%s."
msgstr "YouTube nu va stoca informații despre vizitatorii site-ului tău decât dacă aceștia redau videoclipul. Mai multe informații pot fi găsite %saici%s."

#: framework/premium/features/media-video/options.php:91
msgid "Autoplay Video"
msgstr "Redare automată video"

#: framework/premium/features/media-video/options.php:94
msgid "Automatically start video playback after the gallery is loaded."
msgstr "Pornește automat redarea videoclipului după ce galeria este încărcată."

#: framework/premium/features/media-video/options.php:99
msgid "Loop Video"
msgstr "Redare în buclă"

#: framework/premium/features/media-video/options.php:102
msgid "Start video again after it ends."
msgstr "Reia videoclipul după terminare."

#: framework/premium/features/media-video/options.php:107
msgid "Simplified Player"
msgstr "Player simplificat"

#: framework/premium/features/content-blocks/hooks-manager.php:720
msgid "After single product \"Add to cart\" button"
msgstr "După butonul „Adaugă în coș” al produsului individual"

#: framework/premium/features/content-blocks/hooks-manager.php:725
msgid "Before single product meta"
msgstr "Înaintea meta datelor produsului individual"

#: framework/premium/features/content-blocks/hooks-manager.php:730
msgid "After single product meta"
msgstr "După meta datele produsului individual"

#: framework/premium/features/media-video/options.php:6
msgid "Video Source"
msgstr "Sursă video"

#: framework/premium/features/media-video/options.php:25
msgid "Upload Video"
msgstr "Încarcă videoclip"

#: framework/premium/features/media-video/options.php:29
msgid "Upload an MP4 file into the media library."
msgstr "Încarcă un fișier MP4 în biblioteca media."

#: framework/premium/features/media-video/options.php:42
msgid "YouTube Url"
msgstr "URL YouTube"

#: framework/premium/features/media-video/options.php:44
msgid "Enter a valid YouTube media URL."
msgstr "Introdu un URL media valid pentru YouTube."

#: framework/premium/features/media-video/options.php:57
msgid "Vimeo Url"
msgstr "URL Vimeo"

#: framework/premium/features/media-video/options.php:59
msgid "Enter a valid Vimeo media URL."
msgstr "Introdu un URL media valid pentru Vimeo."

#: framework/premium/features/media-video/options.php:72
msgid "YouTube Privacy Enhanced Mode"
msgstr "Mod de confidențialitate YouTube"

#: framework/premium/features/content-blocks/hooks-manager.php:234
msgid "After first post meta"
msgstr "După primul meta al postării"

#: framework/premium/features/content-blocks/hooks-manager.php:242
msgid "After second post meta"
msgstr "După al doilea meta al postării"

#: framework/premium/features/content-blocks/hooks-manager.php:580
msgid "Offcanvas Cart - Empty State"
msgstr "Sertar coș - Stare goală"

#: framework/premium/features/content-blocks/hooks-manager.php:705
msgid "Before single product gallery"
msgstr "Inainte de galeria produsului individual"

#: framework/premium/features/content-blocks/hooks-manager.php:710
msgid "After single product gallery"
msgstr "După galeria produsului individual"

#: framework/premium/features/content-blocks/hooks-manager.php:715
msgid "Before single product \"Add to cart\" button"
msgstr "Înainte de butonul „Adaugă în coș” al produsului individual"

#: framework/premium/extensions/mega-menu/options.php:532,
#: framework/premium/extensions/mega-menu/options.php:875
msgid "Badge Settings"
msgstr "Setări insignă"

#: framework/premium/extensions/mega-menu/options.php:764
msgid "Column Background"
msgstr "Fundal coloană"

#: framework/premium/extensions/shortcuts/customizer.php:673,
#: framework/premium/extensions/shortcuts/customizer.php:699,
#: framework/premium/extensions/shortcuts/views/bar.php:55,
#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:241,
#: framework/premium/extensions/woocommerce-extra/features/compare/feature.php:245,
#: framework/premium/extensions/woocommerce-extra/features/compare/helpers.php:110,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:36,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:418,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/bar.php:53,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/config.php:4,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:140,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/view.php:97
msgid "Compare"
msgstr "Compară"

#: framework/premium/extensions/shortcuts/customizer.php:1124
msgid "Item Background Color"
msgstr "Culoare fundal element"

#: framework/premium/extensions/shortcuts/customizer.php:1205
msgid "Wishlist Badge Color"
msgstr "Culoare insignă wishlist"

#: framework/premium/extensions/shortcuts/customizer.php:1247
msgid "Compare Badge Color"
msgstr "Culoare insignă comparare"

#: framework/premium/extensions/sidebars/extension.php:185
msgid "Remove Widget Area"
msgstr "Elimină zona widget"

#: framework/premium/extensions/woocommerce-extra/config.php:21
msgid "This extension requires the WooCommerce plugin to be installed and activated."
msgstr "Această extensie necesită ca pluginul WooCommerce să fie instalat și activat."

#: framework/premium/extensions/woocommerce-extra/extension.php:359
msgid "Cart Page"
msgstr "Pagina coșului"

#: framework/premium/features/content-blocks/admin-ui.php:269
msgid "Nothing Found"
msgstr "Nimic găsit"

#: framework/premium/features/content-blocks/admin-ui.php:270
msgid "Maintenance"
msgstr "Mentenanță"

#: framework/premium/features/content-blocks/admin-ui.php:374
msgid "On click to element"
msgstr "La clic pe element"

#: framework/extensions/product-reviews/extension.php:321,
#: framework/premium/features/content-blocks/content-block-layer.php:194,
#: framework/premium/features/content-blocks/content-block-layer.php:244,
#: framework/premium/extensions/woocommerce-extra/features/sku-layer.php:64,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:739,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:617,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:668,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:339,
#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:203,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/feature.php:190,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:251,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/feature.php:274,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:903,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-layer.php:246
msgid "Bottom Spacing"
msgstr "Spațiu jos"

#: framework/premium/features/content-blocks/hooks-manager.php:194
msgid "Before first post meta"
msgstr "Înainte de primul meta al postării"

#: framework/premium/features/content-blocks/hooks-manager.php:202
msgid "Before second post meta"
msgstr "Înainte de al doilea meta al postării"

#: framework/features/conditions/rules/woo.php:80,
#: framework/features/conditions/rules/woo.php:95
msgid "Product with Taxonomy ID"
msgstr "Produs cu ID de taxonomie"

#: framework/premium/extensions/color-mode-switch/extension.php:96
msgid "Dark Mode Color Palette"
msgstr "Paletă de culori mod întunecat"

#: framework/premium/extensions/mega-menu/options.php:72
msgid "Dropdown Custom Width"
msgstr "Lățime personalizată meniu derulant"

#: framework/premium/extensions/mega-menu/options.php:309
msgid "AJAX Content Loading"
msgstr "Încărcare conținut AJAX"

#: framework/premium/extensions/mega-menu/options.php:312
msgid "If you have complex data inside your mega menu you can enable this option in order to load the dropdown content with AJAX and improve the website loading time."
msgstr "Dacă ai date complexe în meniul mega, poți activa această opțiune pentru a încărca conținutul meniului derulant cu AJAX și a îmbunătăți timpul de încărcare a site-ului."

#: framework/premium/extensions/mega-menu/options.php:403
msgid "Content Visibility"
msgstr "Vizibilitate conținut"

#: framework/premium/features/clone-cpt.php:127
msgid "Post creation failed, could not find original post: "
msgstr "Crearea postării a eșuat, nu s-a putut găsi postarea originală: "

#: framework/premium/features/clone-cpt.php:184
msgid "Post copy created."
msgstr "Copie postare creată."

#: framework/premium/features/local-gravatars.php:34
msgid "Store Gravatars Locally"
msgstr "Stochează Gravatar-urile local"

#: framework/premium/features/local-gravatars.php:39
msgid "Store and load Gravatars locally for increased privacy and performance."
msgstr "Stochează și încarcă Gravatar-urile local pentru confidențialitate și performanță crescute."

#: framework/premium/features/premium-header.php:314,
#: framework/premium/features/socials.php:13,
#: framework/features/blocks/contact-info/options.php:103,
#: framework/features/blocks/contact-info/options.php:168,
#: framework/features/blocks/contact-info/options.php:231,
#: framework/features/blocks/contact-info/options.php:294,
#: framework/features/blocks/contact-info/options.php:357,
#: framework/features/blocks/contact-info/options.php:420,
#: framework/features/blocks/contact-info/options.php:483,
#: framework/features/header/items/account/options.php:375,
#: framework/features/header/items/account/options.php:756,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:9
msgid "Icon Source"
msgstr "Sursă pictogramă"

#: framework/premium/features/socials.php:41
msgid "URL Source"
msgstr "Sursă URL"

#: framework/premium/features/socials.php:59
msgid "Custom URL"
msgstr "URL personalizat"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:60
msgid "Form Style"
msgstr "Stil formular"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:68
msgid "Stacked"
msgstr "Stivuit"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:137,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:152
msgid "Your email *"
msgstr "Email-ul tău *"

#: framework/extensions/newsletter-subscribe/providers/demo.php:16
msgid "Demo List"
msgstr "Listă demo"

#: framework/features/conditions/rules/basic.php:10,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:12
msgid "All Products"
msgstr "Toate produsele"

#: framework/features/conditions/rules/basic.php:40
msgid "All Singulars"
msgstr "Toate singularitățile"

#: framework/features/conditions/rules/basic.php:51
msgid "All Archives"
msgstr "Toate arhivele"

#: framework/features/conditions/rules/date-time.php:5
msgid "Date & Time"
msgstr "Dată și oră"

#: framework/features/conditions/rules/date-time.php:9
msgid "Range Date/Time"
msgstr "Interval dată/oră"

#: framework/features/conditions/rules/date-time.php:14
#: static/js/options/ConditionsManager/ScheduleDate.js:143
msgid "Recurring Days"
msgstr "Zile recurente"

#: framework/features/conditions/rules/requests.php:5
msgid "Requests"
msgstr "Solicitări"

#: framework/features/conditions/rules/requests.php:9
msgid "Request Referer"
msgstr "Referință cerere"

#: framework/features/conditions/rules/requests.php:14
msgid "Request Cookie"
msgstr "Cookie cerere"

#: framework/features/conditions/rules/requests.php:19
msgid "Request URL"
msgstr "URL cerere"

#: framework/features/conditions/rules/specific.php:31
msgid "Post with Author ID"
msgstr "Postare cu ID autor"

#: framework/features/conditions/rules/woo.php:75,
#: framework/features/conditions/rules/woo.php:90
msgid "Product ID"
msgstr "ID produs"

#: framework/extensions/cookies-consent/customizer.php:199
msgid "Accept Button"
msgstr "Buton Acceptare"

#: framework/extensions/cookies-consent/customizer.php:266
msgid "Decline Button"
msgstr "Buton Refuz"

#: framework/extensions/cookies-consent/extension.php:105
msgid "Please accept the Privacy Policy in order to comment."
msgstr "Te rugăm să accepți Politica de Confidențialitate pentru a putea comenta."

#: framework/extensions/cookies-consent/extension.php:106
msgid "Comment Submission Failure"
msgstr "Eșec la trimiterea comentariului"

#: framework/extensions/newsletter-subscribe/customizer.php:28,
#: framework/extensions/newsletter-subscribe/helpers.php:24,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:15
msgid "Enter your email address below and subscribe to our newsletter"
msgstr "Introdu adresa ta de email mai jos și abonează-te la newsletter-ul nostru"

#: framework/extensions/newsletter-subscribe/customizer.php:389
msgid "Container Shadow"
msgstr "Umbra container"

#: framework/extensions/trending/customizer.php:588
msgid "Module Title Font"
msgstr "Font titlu modul"

#: framework/extensions/trending/customizer.php:596
msgid "Module Title Color"
msgstr "Culoare titlu modul"

#: framework/extensions/trending/customizer.php:647
msgid "Posts Title Font"
msgstr "Font titlu postări"

#: framework/extensions/trending/customizer.php:656
msgid "Posts Title Font Color"
msgstr "Culoare font titlu postări"

#: framework/extensions/trending/customizer.php:871
msgid "Arrows Color"
msgstr "Culoare săgeți"

#: framework/premium/features/clone-cpt.php:42,
#: framework/premium/features/clone-cpt.php:45
msgid "Duplicate"
msgstr "Duplicare"

#: framework/premium/features/clone-cpt.php:55
msgid "No post to duplicate"
msgstr "Nicio postare de duplicat"

#: framework/helpers/exts-configs.php:314
msgid "Create a customized order “Thank You” page for your customers, giving them a personalized experience."
msgstr "Creează o pagină personalizată „Mulțumim” pentru clienții tăi, oferindu-le o experiență personalizată."

#: framework/helpers/exts-configs.php:322,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:5
msgid "Advanced Reviews"
msgstr "Recenzii avansate"

#: framework/helpers/exts-configs.php:323
msgid "Enhance your WooCommerce reviews with rich content, images and a thumbs up system that help your shoppers find the perfect product."
msgstr "Îmbunătățește recenziile WooCommerce cu conținut bogat, imagini și un sistem de apreciere care ajută cumpărătorii să găsească produsul perfect."

#: framework/extensions/cookies-consent/customizer.php:112
msgid "Forms Cookies Content"
msgstr "Formulare conținut cookie"

#: framework/helpers/exts-configs.php:362
msgid "Better management for affiliate products with a few simple options that strengthen the external integration with these."
msgstr "Management mai bun pentru produsele afiliate cu câteva opțiuni simple care întăresc integrarea externă."

#: framework/helpers/exts-configs.php:295
msgid "Custom Tabs"
msgstr "Taburi personalizate"

#: framework/helpers/exts-configs.php:296
msgid "Present additional information about your products by adding new custom tabs to the product information section."
msgstr "Prezintă informații suplimentare despre produsele tale adăugând taburi noi personalizate în secțiunea de informații despre produs."

#: framework/helpers/exts-configs.php:304,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:58,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:62,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:272,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/helpers.php:47,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/modal-view.php:18,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/side-view.php:27
msgid "Size Guide"
msgstr "Ghid de mărimi"

#: framework/helpers/exts-configs.php:305
msgid "Show a size chart guide so that your visitors can pick the right size for them when ordering a product."
msgstr "Afișează un ghid de mărimi pentru ca vizitatorii să aleagă dimensiunea potrivită atunci când comandă un produs."

#: framework/helpers/exts-configs.php:313
msgid "Custom Thank You Pages"
msgstr "Pagini personalizate „Mulțumim”"

#: framework/helpers/exts-configs.php:279
msgid "Catch the attention of your clients by showcasing your product variations as colour, image or button swatches."
msgstr "Atrage atenția clienților afișând variațiile produsului sub formă de mostre de culoare, imagini sau butoane."

#: framework/helpers/exts-configs.php:286,
#: framework/features/conditions/rules/woo.php:42
msgid "Product Brands"
msgstr "Branduri de produse"

#: framework/helpers/exts-configs.php:287
msgid "Categorise products by brands and show their logo in archive or single page so users could discover more about their makers."
msgstr "Categorizează produsele după branduri și afișează logo-ul lor în arhive sau pe paginile individuale, astfel încât utilizatorii să descopere mai multe despre producători."

#: framework/helpers/exts-configs.php:361
msgid "Affiliate Product Links"
msgstr "Linkuri de produse afiliate"

#: framework/helpers/exts-configs.php:339
msgid "Replace the standard product gallery with additional layouts which can showcase the photos as a grid or even a slider."
msgstr "Înlocuiește galeria standard a produselor cu layout-uri suplimentare care pot afișa fotografiile sub formă de grilă sau chiar slider."

#: framework/helpers/exts-configs.php:354
msgid "Search by SKU"
msgstr "Căutare după SKU"

#: framework/helpers/exts-configs.php:355
msgid "Advanced searching for products by their SKU classification can be useful in cases of vast product catalogues."
msgstr "Căutarea avansată a produselor după clasificarea SKU poate fi utilă în cazul cataloagelor mari de produse."

#: framework/helpers/exts-configs.php:331
msgid "Add a visual cue that tells your visitors how much the cart total must be to be able to benefit of free shipping."
msgstr "Adaugă un indiciu vizual care informează vizitatorii despre suma minimă a coșului necesară pentru transport gratuit."

#: framework/helpers/exts-configs.php:278,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:4
msgid "Variation Swatches"
msgstr "Mostre de variații"

#: framework/helpers/exts-configs.php:271
msgid "Compare products with a clear and concise table system that gives your users a way to make a quick decision."
msgstr "Compară produsele cu un sistem clar și concis de tabele care oferă utilizatorilor o modalitate rapidă de a lua o decizie."

#: framework/helpers/exts-configs.php:346
msgid "Product Share Box"
msgstr "Cutie de partajare a produselor"

#: framework/helpers/exts-configs.php:347
msgid "Enable social sharing abilities for products available on the site, letting even more users discover your great shop selection."
msgstr "Activează funcționalități de partajare socială pentru produsele de pe site, permițând mai multor utilizatori să descopere selecția magazinului."

#: framework/helpers/exts-configs.php:338
msgid "Advanced Gallery"
msgstr "Galerie avansată"

#: framework/helpers/exts-configs.php:247
msgid "Preview the available products and let your users make quick and informative decisions about their purchase."
msgstr "Previzionează produsele disponibile și permite utilizatorilor să ia decizii rapide și informate despre achiziție."

#: framework/helpers/exts-configs.php:254,
#: framework/premium/extensions/shortcuts/views/bar.php:54
msgid "Filters"
msgstr "Filtre"

#: framework/helpers/exts-configs.php:255
msgid "Drill down the product list with new filtering widgets, an off canvas area for them and showing the active filters on the page."
msgstr "Restrânge lista de produse cu noi widget-uri de filtrare, o zonă off-canvas pentru acestea și afișează filtrele active pe pagină."

#: framework/helpers/exts-configs.php:263
msgid "A set of features that lets you create easily your dream products wishlists and share them with friends and family."
msgstr "Un set de funcționalități care îți permite să creezi liste de dorințe pentru produse și să le partajezi cu prietenii și familia."

#: framework/helpers/exts-configs.php:270
msgid "Compare View"
msgstr "Vizualizare comparare"

#: framework/helpers/exts-configs.php:239
msgid "Adds the “add to cart” actions to the product page as a floating bar if the product summary has disappeared from view."
msgstr "Adaugă acțiunile „Adaugă în coș” pe pagina produsului ca o bară plutitoare, dacă rezumatul produsului nu mai este vizibil."

#: framework/helpers/exts-configs.php:246,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:58
msgid "Quick View"
msgstr "Vizualizare rapidă"

#: framework/helpers/exts-configs.php:157
msgid "Let your guests easily filter the posts by their category or tags taxonomy terms, instantly drilling down the listings."
msgstr "Permite vizitatorilor să filtreze ușor postările după categorii sau termeni taxonomici, restrângând instantaneu lista."

#: framework/helpers/exts-configs.php:163
msgid "Taxonomy Customisations"
msgstr "Personalizări de taxonomie"

#: framework/helpers/exts-configs.php:164
msgid "Additional customisation options for your taxonomies such as hero backgrounds and custom colour labels."
msgstr "Opțiuni suplimentare de personalizare pentru taxonomii, cum ar fi fundaluri hero și etichete de culori personalizate."

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:227
msgctxt "Extension Brand Name"
msgid "Shop Extra"
msgstr "Shop Extra"

#: framework/helpers/exts-configs.php:143
msgid "Display the approximate reading time of an article, so that visitors know what to expect when starting to read the content."
msgstr "Afișează timpul aproximativ de citire al unui articol, astfel încât vizitatorii să știe la ce să se aștepte când încep să citească."

#: framework/helpers/exts-configs.php:149
#: static/js/editor/blocks/dynamic-data/index.js:100
msgid "Dynamic Data"
msgstr "Date dinamice"

#: framework/helpers/exts-configs.php:150
msgid "Integrates custom fields solutions in the meta layers of a post and presents additional information."
msgstr "Integrează soluții pentru câmpuri personalizate în straturile meta ale unei postări și prezintă informații suplimentare."

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:83
msgctxt "Extension Brand Name"
msgid "Color Mode Switch"
msgstr "Comutator mod de culoare"

#: framework/helpers/exts-configs.php:84
msgid "Add a dark colour scheme and switch to your website, which will make it pleasant to look at in low light environments."
msgstr "Adaugă o schemă de culori întunecată și schimb-o pe site-ul tău, pentru a-l face mai plăcut de vizualizat în medii cu lumină slabă."

#. translators: 1: link open 2: link close
#: framework/features/account-auth.php:253
msgid "Registration complete. Please check your email, then visit the %1$slogin page%2$s."
msgstr "Înregistrare finalizată. Vă rugăm să vă verificați adresa de e-mail, apoi vizitați %1$spagina de conectare%2$s."

#: framework/dashboard.php:34,
#: framework/features/header/items/account/options.php:23,
#: framework/features/header/items/account/options.php:73,
#: framework/features/header/items/account/options.php:77,
#: framework/features/header/items/account/options.php:649,
#: framework/features/header/items/account/views/login.php:158,
#: framework/features/header/items/account/views/login.php:335
msgid "Dashboard"
msgstr "Panou de control"

#: framework/dashboard.php:521
msgid "You do not have sufficient permissions to access this page."
msgstr "Nu ai permisiuni suficiente pentru a accesa această pagină."

#: framework/theme-integration.php:221,
#: framework/features/blocks/share-box/options.php:25
msgid "X (Twitter)"
msgstr "X (Twitter)"

#: framework/theme-integration.php:234
msgid "Mastodon"
msgstr "Mastodon"

#: framework/features/conditions/rules/pages.php:39
msgid "Privacy Policy Page"
msgstr "Pagină Politica de Confidențialitate."

#: framework/features/conditions/rules/pages.php:46
msgid "Author Archives"
msgstr "Arhive Autor"

#: framework/features/conditions/rules/woo.php:104,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:42
msgid "WooCommerce"
msgstr "WooCommerce"

#: framework/features/conditions/rules/woo.php:15
msgid "Shop Home"
msgstr "Acasă magazin"

#: framework/features/conditions/rules/woo.php:20,
#: framework/premium/extensions/woocommerce-extra/features/affiliate/options.php:66
msgid "Single Product"
msgstr "Produs unic"

#: framework/features/conditions/rules/woo.php:25
msgid "Product Archives"
msgstr "Arhive produse"

#: framework/features/conditions/rules/woo.php:30,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/options.php:91
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:27
msgid "Product Categories"
msgstr "Categorii Produse"

#: framework/features/conditions/rules/woo.php:49
msgid "Product Tags"
msgstr "Etichete Produse"

#: framework/features/conditions/rules/cpt.php:53
msgid "Custom Post Types"
msgstr "Tipuri de postări personalizate"

#: framework/features/conditions/rules/localization.php:15
msgid "Current Language"
msgstr "Limba curenta"

#: framework/features/conditions/rules/bbPress.php:11,
#: framework/features/header/items/account/options.php:231
msgid "bbPress"
msgstr "bbPress"

#: framework/features/conditions/rules/bbPress.php:15,
#: framework/features/header/items/account/options.php:24,
#: framework/features/header/items/account/options.php:87
msgid "Profile"
msgstr "Profil"

#: framework/extensions/newsletter-subscribe/helpers.php:142
msgid "First name"
msgstr "Prenume"

#: framework/extensions/trending/customizer.php:537
msgid "Display Location"
msgstr "Afiseaza locatia"

#: framework/extensions/trending/customizer.php:545
msgid "Before Footer"
msgstr "Înainte de subsol"

#: framework/extensions/trending/customizer.php:550
msgid "After Footer"
msgstr "Dupa subsol"

#: framework/extensions/trending/customizer.php:555
msgid "After Header"
msgstr "Înainte de antet"

#: framework/extensions/trending/customizer.php:572
msgid "Trending Block Display Conditions"
msgstr "Condiții de afișare pentru blocul de tendințe"

#: framework/extensions/trending/customizer.php:573
msgid "Add one or more conditions to display the trending block."
msgstr "Adaugă una sau mai multe condiții pentru a afișa blocul de tendințe."

#: framework/premium/features/content-blocks/admin-ui.php:641
msgid "Hide Hooks"
msgstr "Ascunde Hook-uri"

#: framework/premium/features/content-blocks/admin-ui.php:642
msgid "Show Hooks"
msgstr "Afișează Hook-uri"

#: framework/premium/features/content-blocks/admin-ui.php:698
msgid "Hide Theme Hooks"
msgstr "Ascunde Hook-uri ale temei"

#: framework/premium/features/content-blocks/admin-ui.php:699
msgid "Show Theme Hooks"
msgstr "Afișează Hook-uri ale temei"

#: framework/premium/features/content-blocks/admin-ui.php:707
msgid "Hide WooCommerce Hooks"
msgstr "Ascunde Hook-uri WooCommerce"

#: framework/premium/features/content-blocks/admin-ui.php:708
msgid "Show WooCommerce Hooks"
msgstr "Afișează Hook-uri ale temei"

#: framework/features/header/modal/register.php:76
msgid "A link to set a new password will be sent to your email address."
msgstr "Un link pentru a seta o nouă parolă va fi trimis pe adresa ta de email."

#: framework/premium/extensions/code-snippets/extension.php:31,
#: framework/premium/extensions/code-snippets/extension.php:86,
#: framework/premium/extensions/code-snippets/extension.php:131
msgid "Header scripts"
msgstr "Scripturi antet"

#: framework/premium/extensions/code-snippets/extension.php:53,
#: framework/premium/extensions/code-snippets/extension.php:108,
#: framework/premium/extensions/code-snippets/extension.php:153
msgid "Footer scripts"
msgstr "Scripturi Footer"

#: framework/premium/extensions/mega-menu/options.php:377,
#: framework/premium/extensions/mega-menu/options.php:385,
#: framework/premium/features/content-blocks/content-block-layer.php:170,
#: framework/premium/features/content-blocks/content-block-layer.php:178,
#: framework/premium/features/content-blocks/content-block-layer.php:221,
#: framework/premium/features/content-blocks/content-block-layer.php:229,
#: framework/features/header/items/account/options.php:254,
#: framework/features/header/items/account/options.php:262,
#: framework/premium/features/premium-header/items/content-block/options.php:13,
#: framework/premium/features/premium-header/items/content-block/options.php:21
#: framework/premium/static/js/blocks/ContentBlock.js:110
msgid "Select Content Block"
msgstr "Selectează blocul de conținut"

#: framework/premium/extensions/mega-menu/options.php:380,
#: framework/premium/features/content-blocks/content-block-layer.php:173,
#: framework/premium/features/content-blocks/content-block-layer.php:224,
#: framework/features/header/items/account/options.php:257,
#: framework/premium/features/premium-header/items/content-block/options.php:16
msgid "Create a new content Block/Hook"
msgstr "Creează un nou bloc de conținut/Hook"

#: framework/premium/extensions/mega-menu/options.php:637
msgid "Heading Font"
msgstr "Font pentru titlu"

#: framework/premium/features/content-blocks/admin-ui.php:155
msgid "Enable"
msgstr "Activează"

#: framework/premium/features/content-blocks/admin-ui.php:156
msgid "Disable"
msgstr "Dezactivează"

#: framework/premium/features/content-blocks/admin-ui.php:211
msgid "Enabled %s content block."
msgid_plural "Enabled %s content blocks."
msgstr[0] "Bloc de conținut %s activat."
msgstr[1] "Blocuri de conținut %s activate."
msgstr[2] "Blocurile de conținut %s activate."

#: framework/premium/features/content-blocks/admin-ui.php:236
msgid "Disabled %s content block."
msgid_plural "Disabled %s content blocks."
msgstr[0] "Bloc de conținut %s dezactivat."
msgstr[1] "Blocuri de conținut %s dezactivate."
msgstr[2] "Blocurile de conținut %s dezactivate."

#: framework/premium/features/content-blocks/admin-ui.php:264
msgid "404 Page"
msgstr "Pagina 404"

#: framework/premium/features/content-blocks/admin-ui.php:267,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:16,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:64
msgid "Archive"
msgstr "Arhiva"

#: framework/premium/features/content-blocks/admin-ui.php:268,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:17,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/options.php:65
msgid "Single"
msgstr "Unic"

#: framework/premium/features/content-blocks/hooks-manager.php:39
msgid "WP body open"
msgstr "Corp WP deschis"

#: framework/premium/features/content-blocks/hooks-manager.php:340
msgid "Before related posts"
msgstr "Înainte de postările asociate"

#: framework/premium/features/content-blocks/hooks-manager.php:341,
#: framework/premium/features/content-blocks/hooks-manager.php:348,
#: framework/premium/features/content-blocks/hooks-manager.php:355,
#: framework/premium/features/content-blocks/hooks-manager.php:362,
#: framework/premium/features/content-blocks/hooks-manager.php:369,
#: framework/premium/features/content-blocks/hooks-manager.php:376,
#: framework/premium/features/content-blocks/hooks-manager.php:383,
#: framework/premium/features/content-blocks/hooks-manager.php:390,
#: framework/premium/features/content-blocks/hooks-manager.php:398,
#: framework/premium/features/content-blocks/hooks-manager.php:405
msgid "Related posts"
msgstr "Postări asociate"

#: framework/premium/features/content-blocks/hooks-manager.php:347
msgid "Related posts top"
msgstr "Postări asociate sus"

#: framework/premium/features/content-blocks/hooks-manager.php:368
msgid "Card top"
msgstr "Card sus"

#: framework/premium/features/content-blocks/hooks-manager.php:375
msgid "Before featured image"
msgstr "Înainte de imaginea principală"

#: framework/premium/features/content-blocks/hooks-manager.php:382
msgid "After featured image"
msgstr "După imaginea principală"

#: framework/premium/features/content-blocks/hooks-manager.php:389
msgid "Card bottom"
msgstr "Card jos"

#: framework/premium/features/content-blocks/hooks-manager.php:397
msgid "Related posts bottom"
msgstr "Postări asociate jos"

#: framework/premium/features/content-blocks/hooks-manager.php:404
msgid "After related posts"
msgstr "După postările asociate"

#: framework/premium/features/content-blocks/hooks-manager.php:568
msgid "Offcanvas Filters - Top"
msgstr "Filtre Offcanvas - Sus"

#: framework/premium/features/content-blocks/hooks-manager.php:574
msgid "Offcanvas Filters - Bottom"
msgstr "Filtre Offcanvas - Jos"

#: framework/premium/features/content-blocks/options/archive.php:31,
#: framework/premium/features/content-blocks/options/single.php:30
msgid "Replacement Behavior"
msgstr "Comportament de înlocuire"

#: framework/premium/features/content-blocks/options/archive.php:38
msgid "Only Card"
msgstr "Doar card"

#: framework/premium/features/content-blocks/options/archive.php:39,
#: framework/premium/features/content-blocks/options/single.php:38
msgid "Full Page"
msgstr "Pagină completă"

#: framework/premium/features/content-blocks/options/404.php:51,
#: framework/premium/features/content-blocks/options/archive.php:118,
#: framework/premium/features/content-blocks/options/maintenance.php:48,
#: framework/premium/features/content-blocks/options/nothing_found.php:69,
#: framework/premium/features/content-blocks/options/single.php:50
msgid "Page Structure"
msgstr "Structura paginii"

#: framework/premium/features/content-blocks/options/single.php:37
msgid "Content Area"
msgstr "Zona de conținut"

#: framework/premium/features/content-blocks/options/single.php:99
msgid "Content Area Vel Spacing"
msgstr "Spațiere verticală în zona de conținut"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:81
msgid "Filter Source"
msgstr "Sursa filtrului"

#: framework/features/blocks/contact-info/options.php:590,
#: framework/premium/features/premium-header/items/contacts/options.php:6
msgid "Items Direction"
msgstr "Direcția elementelor"

#: framework/features/blocks/contact-info/options.php:597,
#: framework/premium/features/premium-header/items/contacts/options.php:14
msgid "Horizontal"
msgstr "Orizontal"

#: framework/features/blocks/contact-info/options.php:596,
#: framework/premium/features/premium-header/items/contacts/options.php:13
msgid "Vertical"
msgstr "Vertica"

#: framework/premium/features/premium-header/items/contacts/options.php:889,
#: framework/premium/features/premium-header/items/contacts/options.php:931,
#: framework/premium/features/premium-header/items/contacts/options.php:969,
#: framework/premium/features/premium-header/items/contacts/options.php:1007
#: static/js/editor/blocks/about-me/Edit.js:148
#: static/js/editor/blocks/contact-info/Edit.js:162
msgid "Icons Background Color"
msgstr "Culoare fundal pictograme"

#: framework/premium/features/premium-header/items/contacts/options.php:893,
#: framework/premium/features/premium-header/items/contacts/options.php:935,
#: framework/premium/features/premium-header/items/contacts/options.php:973,
#: framework/premium/features/premium-header/items/contacts/options.php:1011
#: static/js/editor/blocks/about-me/Edit.js:179
#: static/js/editor/blocks/contact-info/Edit.js:193
msgid "Icons Border Color"
msgstr "Culoare margine pictograme"

#: framework/premium/static/js/options/IconPicker/CustomIcon.js:22
msgid "Click to upload"
msgstr "Apasă pentru a încărca"

#: framework/premium/static/js/options/IconPicker/Modal.js:135
msgid "All Icons"
msgstr "Toate pictogramele"

#: static/js/options/ConditionsManager/SingleCondition.js:296
msgid "All authors"
msgstr "Toți autorii"

#: static/js/dashboard/screens/DemoInstall/components/Error.js:25
msgid "Can't Import Starter Site"
msgstr "Nu se poate importa site-ul demonstrativ"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:31
msgid "Unfortunately, your hosting configuration doesn't meet the minimum requirements for importing a starter site."
msgstr "Din păcate, configurația gazdei tale nu îndeplinește cerințele minime pentru a importa un site demonstrativ."

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/views/panel.php:62
msgid "Close filters modal"
msgstr "Închide fereastra filtrelor"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/view.php:45
msgid "Close quick view"
msgstr "Închide previzualizarea rapidă"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/helpers.php:45
msgid "Quick view toggle"
msgstr "Comutator previzualizare rapidă"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/helpers.php:32
msgid "Quick view icon"
msgstr "Pictogramă previzualizare rapidă"

#: framework/features/header/items/account/options.php:1724,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:314,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:185
msgid "Close Button Type"
msgstr "Tip buton de închidere"

#: framework/features/header/items/account/options.php:726,
#: framework/features/header/items/account/options.php:1731,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:323,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:23,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:194
msgid "Simple"
msgstr "Simplu"

#: framework/features/header/items/account/options.php:1732,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:324,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:195
msgid "Border"
msgstr "Margine"

#: framework/features/header/items/account/options.php:1778,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:366,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:248,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:254,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:298,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:382,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:466
msgid "Border Color"
msgstr "Culoare margine"

#: framework/premium/features/content-blocks/hooks-manager.php:170
msgid "Before description"
msgstr "Înainte de descriere"

#: framework/premium/features/content-blocks/hooks-manager.php:178
msgid "Before breadcrumbs"
msgstr "Înainte de firimituri"

#: framework/premium/features/content-blocks/hooks-manager.php:218
msgid "After description"
msgstr "După descriere"

#: framework/premium/features/content-blocks/hooks-manager.php:226
msgid "After breadcrumbs"
msgstr "După firimituri"

#: framework/premium/features/content-blocks/hooks-manager.php:630
msgid "Before shop loop item actions"
msgstr "Înainte de acțiunile elementelor din lista magazinului"

#: framework/premium/features/content-blocks/hooks-manager.php:635
msgid "After shop loop item actions"
msgstr "După acțiunile elementelor din lista magazinului"

#. translators: placeholder here means the actual URL.
#: framework/features/blocks/socials/options.php:24
msgid "Configure the social links in Customizer ➝ General ➝ %sSocial Network Accounts%s."
msgstr "Configurează linkurile sociale în Personalizator ➝ General ➝ %sRețele sociale%s."

#: framework/features/header/items/account/options.php:306
msgid "Customizing: Logged out State"
msgstr "Personalizare: Stare deconectat"

#: framework/features/header/items/account/options.php:1088
msgid "User Visibility"
msgstr "Vizibilitate utilizator"

#: framework/features/header/items/account/options.php:1099,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:116,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:95
msgid "Logged In"
msgstr "Autentificat"

#: framework/features/header/items/account/options.php:1100,
#: framework/premium/extensions/woocommerce-extra/features/advanced-reviews/options.php:117,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:96
msgid "Logged Out"
msgstr "Deconectat"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1385
msgid "Custom Field"
msgstr "Câmp personalizat"

#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:114
msgid "%s min"
msgid_plural "%s mins"
msgstr[0] "%s minut"
msgstr[1] "%s minute"
msgstr[2] "%s minute"

#: framework/premium/features/content-blocks/options/archive.php:98
msgid "Default Card Layout"
msgstr "Aspect implicit al cardului"

#: framework/premium/features/content-blocks/options/archive.php:103
msgid "Inherit card wrapper settings from Customizer (background color, spacing, shadow)."
msgstr "Moștenește setările învelișului cardului din Personalizator (culoare de fundal, spațiere, umbră)."

#: framework/premium/features/content-blocks/options/archive.php:53,
#: framework/premium/features/content-blocks/options/hook.php:277,
#: framework/premium/features/content-blocks/options/popup.php:483,
#: framework/premium/features/content-blocks/options/single.php:151
msgid "Dynamic Content Preview"
msgstr "Previzualizare conținut dinamic"

#: framework/premium/features/content-blocks/options/archive.php:60
msgid "Select a post/page to preview it's content inside the editor while building the archive."
msgstr "Selectează o postare/pagină pentru a previzualiza conținutul în editor în timpul construirii arhivei."

#: framework/premium/features/content-blocks/options/archive.php:66
msgid "Editor/Card Width"
msgstr "Lățimea editorului/cardului"

#: framework/premium/features/content-blocks/options/archive.php:77
msgid "Set the editor width for better understanging the layout you are building (just for preview purpose, this option won't apply in frontend)."
msgstr "Setează lățimea editorului pentru a înțelege mai bine aspectul pe care îl construiești (doar în scop de previzualizare, această opțiune nu se aplică în frontend)."

#: framework/premium/features/content-blocks/options/popup.php:203
msgid "After X Pages"
msgstr "După X pagini"

#: framework/premium/features/content-blocks/options/popup.php:209
msgid "Set after how many visited pages the popup block will appear."
msgstr "Setează după câte pagini vizitate va apărea blocul popup."

#: framework/premium/features/content-blocks/options/popup.php:489,
#: framework/premium/features/content-blocks/options/single.php:157
msgid "Select a post/page to preview it's content inside the editor while building the post/page."
msgstr "Selectează o postare/pagină pentru a previzualiza conținutul acesteia în editor în timpul construirii postării/paginii."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:236
msgid "More information on how to generate an API key for Mailerlite can be found %shere%s. Please note that it is required at least one group to be created in your account for the integration to work. More info on how to create a group %shere%s."
msgstr "Mai multe informații despre generarea unei chei API pentru Mailerlite pot fi găsite %shere%s. Vă rugăm să rețineți că este necesar să creați cel puțin un grup în contul dvs. pentru ca integrarea să funcționeze. Mai multe informații despre cum să creați un grup %shere%s."

#: framework/premium/static/js/hooks/CodeEditor.js:59
msgid "Code Editor"
msgstr "Editor de Cod"

#: framework/premium/static/js/hooks/CreateHook.js:101
msgid "Template Type"
msgstr "Tip Template"

#: framework/premium/static/js/hooks/CreateHook.js:116
msgid "Archive Template"
msgstr "Template Arhiva"

#: framework/premium/static/js/hooks/CreateHook.js:124
msgid "Single Template"
msgstr "Template Unic"

#: framework/premium/static/js/hooks/CreateHook.js:178
msgid "Hook Name"
msgstr "Numele carligului"

#: framework/premium/static/js/hooks/CreateHook.js:182
msgid "Popup Name"
msgstr "Nume Popup"

#: framework/premium/static/js/hooks/CreateHook.js:186
msgid "Template Name"
msgstr "Nume Template"

#: framework/premium/features/content-blocks/admin-ui.php:263,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:52,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:22
#: framework/premium/static/js/hooks/CreateHook.js:77
msgid "Popup"
msgstr "Popup"

#: framework/premium/static/js/hooks/CreateHook.js:86
msgid "Custom Template"
msgstr "Șablon personalizat"

#: framework/premium/static/js/options/IconPicker.js:20
msgid "Theme Icons"
msgstr "Pictograme temă"

#: framework/premium/static/js/options/IconPicker.js:26
msgid "FontAwesome Brands"
msgstr "FontAwesome Mărci"

#: framework/premium/static/js/options/IconPicker.js:32
msgid "FontAwesome Solid"
msgstr "FontAwesome Solid"

#: framework/premium/static/js/options/IconPicker.js:38
msgid "FontAwesome Regular"
msgstr "FontAwesome Regular"

#: framework/premium/static/js/typography/providers/kadence.js:21
#: framework/premium/static/js/typography/providers/plus-addons.js:23
#: framework/premium/static/js/typography/providers/stackable.js:23
msgid "%s Local Google Fonts"
msgstr "%s Fonturi Google Locale"

#: framework/premium/static/js/typography/providers/kadence.js:26
#: framework/premium/static/js/typography/providers/plus-addons.js:27
#: framework/premium/static/js/typography/providers/stackable.js:27
msgid "%s Typekit"
msgstr "%s Typekit"

#: framework/premium/static/js/typography/providers/kadence.js:31
#: framework/premium/static/js/typography/providers/stackable.js:31
msgid "%s Custom Fonts"
msgstr "%s Fonturi personalizate"

#: framework/premium/static/js/typography/providers/kadence.js:59
msgid "Normal"
msgstr "Norma"

#: framework/premium/static/js/typography/providers/kadence.js:83
msgid "Inherit"
msgstr "Mosteneste"

#: framework/premium/static/js/typography/providers/plus-addons.js:31
msgid "%s Custom"
msgstr "%s Personalizat"

#: framework/premium/static/js/typography/providers/plus-addons.js:35
msgid "%s System"
msgstr "%s Sistem"

#: framework/premium/features/premium-header.php:22,
#: framework/premium/features/premium-header.php:58
msgid "Mobile Menu 1"
msgstr "Meniu mobil 1"

#: framework/premium/features/premium-header.php:59,
#: framework/premium/features/premium-header/items/mobile-menu-secondary/config.php:4
msgid "Mobile Menu 2"
msgstr "Meniu mobil 2"

#: framework/extensions/newsletter-subscribe/providers/active-campaign.php:154,
#: framework/extensions/newsletter-subscribe/providers/brevo.php:116,
#: framework/extensions/newsletter-subscribe/providers/campaign-monitor.php:136,
#: framework/extensions/newsletter-subscribe/providers/convertkit.php:97,
#: framework/extensions/newsletter-subscribe/providers/demo.php:40,
#: framework/extensions/newsletter-subscribe/providers/mailerlite-classic.php:104,
#: framework/extensions/newsletter-subscribe/providers/mailerlite-new.php:123,
#: framework/extensions/newsletter-subscribe/providers/mailpoet.php:93
msgid "Thank you for subscribing to our newsletter!"
msgstr "Vă mulțumim că v-ați abonat la newsletter-ul nostru!"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:59
msgctxt "Extension Brand Name"
msgid "Adobe Fonts"
msgstr "Adobe Fonts"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:70,
#: framework/premium/extensions/code-snippets/extension.php:24,
#: framework/premium/extensions/code-snippets/extension.php:79,
#: framework/premium/extensions/code-snippets/extension.php:122
msgctxt "Extension Brand Name"
msgid "Custom Code Snippets"
msgstr "Fragmente de cod personalizate"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:96
msgctxt "Extension Brand Name"
msgid "Custom Fonts"
msgstr "Fonturi Personalizate"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:108
msgctxt "Extension Brand Name"
msgid "Local Google Fonts"
msgstr "Fonturi locale Google"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:123
msgctxt "Extension Brand Name"
msgid "Advanced Menu"
msgstr "Meniu avansat"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:136
msgctxt "Extension Brand Name"
msgid "Post Types Extra"
msgstr "Tipuri de postări suplimentare"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:179,
#: framework/premium/extensions/shortcuts/config.php:5,
#: framework/premium/extensions/shortcuts/customizer.php:751
msgctxt "Extension Brand Name"
msgid "Shortcuts Bar"
msgstr "Bară de scurtături"

#: framework/premium/extensions/shortcuts/customizer.php:314
msgid "Set link to nofollow"
msgstr "Setează linkul ca nofollow"

#: framework/premium/extensions/shortcuts/customizer.php:320
msgid "Custom class"
msgstr "Clasă personalizată"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:194
msgctxt "Extension Brand Name"
msgid "Multiple Sidebars"
msgstr "Bari laterale multiple"

#. translators: This is a brand name. Preferably to not be translated
#: framework/helpers/exts-configs.php:214
msgctxt "Extension Brand Name"
msgid "White Label"
msgstr "Etichetă albă"

#: framework/features/blocks/share-box/options.php:132,
#: framework/features/blocks/socials/options.php:84
msgid "Icons Spacing"
msgstr "Spațiere pictograme"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/feature.php:157
msgid "Add widgets here."
msgstr "Adăugați widget-uri aici."

#. translators: 1: link open 2: link close
#: framework/features/account-auth.php:106
msgid "Check your email for the confirmation link, then visit the %slogin page%s."
msgstr "Verificați e-mailul pentru linkul de confirmare, apoi vizitați %spagina de autentificare%s."

#: framework/features/account-auth.php:226
msgid "Your account was created successfully. Your login details have been sent to your email address. Please visit the %1$slogin page%2$s."
msgstr "Contul dvs. a fost creat cu succes. Detaliile de autentificare au fost trimise pe adresa dvs. de e-mail. Vă rugăm să vizitați %1$spagina de autentificare%2$s."

#: framework/features/account-auth.php:238
msgid "Your account was created successfully and a password has been sent to your email address. Please visit the %1$slogin page%2$s."
msgstr "RO Your account was created successfully and a password has been sent to your email address. Please visit the %1$slogin page%2$s."

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/cookies-consent/config.php:5,
#: framework/extensions/cookies-consent/customizer.php:5
msgctxt "Extension Brand Name"
msgid "Cookies Consent"
msgstr "Consimțământ pentru cookie-uri"

#: framework/extensions/cookies-consent/customizer.php:76
msgid "Accept Button text"
msgstr "Text Buton Acceptare"

#: framework/extensions/cookies-consent/customizer.php:85
msgid "Decline Button text"
msgstr "Text Buton Refuz"

#: framework/extensions/cookies-consent/customizer.php:88,
#: framework/extensions/cookies-consent/helpers.php:11
msgid "Decline"
msgstr "Refuz"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/newsletter-subscribe/config.php:5
msgctxt "Extension Brand Name"
msgid "Newsletter Subscribe"
msgstr "Abonare la newsletter"

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/product-reviews/config.php:5
msgctxt "Extension Brand Name"
msgid "Product Reviews"
msgstr "Recenzii produse"

#: framework/extensions/product-reviews/metabox.php:60
msgid "Please note that some of this information (price, sku, brand) won't be displayed on the front-end. It is solely used for Google's Schema.org markup."
msgstr "Te rugăm să reții că unele dintre aceste informații (preț, SKU, marcă) nu vor fi afișate în partea vizibilă a site-ului. Acestea sunt utilizate exclusiv pentru marcajul Schema.org al Google."

#. translators: This is a brand name. Preferably to not be translated
#: framework/extensions/trending/config.php:5,
#: framework/extensions/trending/customizer.php:160
msgctxt "Extension Brand Name"
msgid "Trending Posts"
msgstr "Articole populare"

#: framework/extensions/trending/customizer.php:477,
#: framework/features/blocks/about-me/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:32,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:133,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:73
msgid "Image Size"
msgstr "Dimensiune Imagine"

#: framework/features/header/account-modal.php:25
msgid "Close account modal"
msgstr "Închide fereastra de cont"

#: framework/features/header/header-options.php:68
msgid "Effect"
msgstr "Efect"

#: framework/features/header/header-options.php:84
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:73
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:68
msgid "Offset"
msgstr "Decalaj"

#: framework/premium/features/content-blocks/admin-ui.php:275
msgid "All types"
msgstr "Toate tipurile"

#: framework/premium/features/content-blocks/admin-ui.php:378,
#: framework/premium/features/content-blocks/options/popup.php:82
msgid "After x pages"
msgstr "După x pagini"

#: framework/premium/features/content-blocks/popups.php:376
msgid "Close popup"
msgstr "Închide fereastra pop-up"

#: framework/premium/features/media-video/options.php:13
msgid "Upload"
msgstr "Încarcă"

#: framework/premium/static/js/header/CreateHeader.js:117
msgid "Picker header"
msgstr "Antet selector"

#: framework/premium/static/js/header/CreateHeader.js:134
#: static/js/header/PanelsManager.js:58
msgid "Global Header"
msgstr "Antet global"

#: framework/premium/static/js/header/CreateHeader.js:173
msgid "Create New Header"
msgstr "Creează un nou antet"

#: framework/premium/static/js/header/CreateHeader.js:50
msgid "Create new header"
msgstr "Creează un nou antet"

#: framework/premium/static/js/header/CreateHeader.js:53
msgid "Create a new header and assign it to different pages or posts based on your conditions."
msgstr "Creează un antet nou și atribuie-l diferitelor pagini sau articole pe baza condițiilor tale."

#: framework/premium/static/js/header/CreateHeader.js:71
msgid "Header name"
msgstr "Nume header"

#: framework/premium/static/js/hooks/CodeEditor.js:238
msgid "Yes, continue"
msgstr "Da, continuă"

#: framework/premium/static/js/hooks/CodeEditor.js:150
msgid "Use code editor"
msgstr "Folosește editorul de cod"

#: framework/premium/static/js/hooks/CodeEditor.js:153
msgid "Exit code editor"
msgstr "Ieși din editorul de cod"

#: framework/premium/static/js/hooks/CodeEditor.js:165
msgid "Heads up!"
msgstr "Atenție!"

#: framework/premium/static/js/hooks/CodeEditor.js:167
msgid "Enabling & disabling the code editor will erase everything from your post editor and this action is irreversible."
msgstr "Activarea și dezactivarea editorului de cod va șterge totul din editorul de articole, iar această acțiune este ireversibilă."

#: framework/premium/static/js/hooks/CodeEditor.js:174
msgid "Are you sure you want to continue?"
msgstr "Ești sigur că vrei să continui?"

#: framework/premium/static/js/hooks/CreateHook.js:223
msgid "Create Content Block"
msgstr "Creează un Content Block"

#: framework/premium/static/js/hooks/CreateHook.js:36
msgid "Please select the type of your content block and place it in the location you want based on your display and user conditions."
msgstr "Te rugăm să selectezi tipul blocului de conținut și să-l plasezi în locația dorită pe baza condițiilor tale de afișare și utilizator."

#: framework/premium/static/js/hooks/CreateHook.js:108
msgid "Select template type..."
msgstr "Alege tipul de template…"

#: framework/premium/features/content-blocks/admin-ui.php:262
#: framework/premium/static/js/hooks/CreateHook.js:68
msgid "Custom Content/Hooks"
msgstr "Conținut personalizat"

#: framework/premium/static/js/hooks/CreateHook.js:148
msgid "404 Page Template"
msgstr "Template pagină 404"

#: framework/premium/static/js/hooks/CreateHook.js:132
msgid "Header Template"
msgstr "Template antet"

#: framework/premium/static/js/hooks/CreateHook.js:140
msgid "Footer Template"
msgstr "Template subsol"

#: framework/premium/static/js/options/IconPicker.js:137
msgid "Change Icon"
msgstr "Schimbă pictograma"

#: framework/premium/static/js/options/IconPicker.js:155
msgid "Remove Icon"
msgstr "Elimină pictogramă"

#: framework/premium/static/js/options/IconPicker.js:161
msgid "Select"
msgstr "Alege"

#: framework/premium/static/js/options/IconPicker/Modal.js:148
msgid "Upload Icon"
msgstr "Încarcă pictogramă"

#: framework/premium/static/js/options/IconPicker/CustomIcon.js:34
msgid "For performance and customization reasons, only SVG files are allowed."
msgstr "Din motive de performanță și personalizare, doar fișierele SVG sunt permise."

#: framework/premium/static/js/options/IconPicker/IconsList.js:24
msgid "Search icon"
msgstr "Pictogramă căutare"

#: framework/premium/static/js/options/MultipleLocationsSelect.js:151
msgid "Add New Location"
msgstr "Adaugă o locație nouă"

#: framework/premium/static/js/options/MultipleLocationsSelect.js:53
msgid "Select location"
msgstr "Alege locația"

#: static/js/dashboard.js:64
msgid "Starter Sites"
msgstr "Site-uri demonstrative"

#: static/js/dashboard.js:76
msgid "Extensions"
msgstr "Extensii"

#: static/js/header/EditConditions.js:151
#: static/js/options/DisplayCondition.js:98
msgid "Save Conditions"
msgstr "Salvează condițiile"

#: static/js/header/EditConditions.js:107
msgid "Add one or more conditions in order to display your header."
msgstr "Adaugă una sau mai multe condiții pentru a afișa antetul."

#: static/js/header/PanelsManager.js:174
msgid "Remove header"
msgstr "Elimină antet"

#: static/js/header/PanelsManager.js:198
msgid "Remove Header"
msgstr "Înlătură antetul"

#: static/js/header/PanelsManager.js:201
msgid "You are about to remove a custom header, are you sure you want to continue?"
msgstr "Ești pe punctul de a elimina un antet personalizat, ești sigur că vrei să continui?"

#: static/js/dashboard/helpers/SubmitSupport.js:18
msgid "Need help or advice?"
msgstr "Ai nevoie de ajutor?"

#: static/js/dashboard/helpers/SubmitSupport.js:21
msgid "Got a question or need help with the theme? You can always submit a support ticket or ask for help in our friendly Facebook community."
msgstr "Ai o întrebare sau ai nevoie de ajutor cu tema? Poți oricând să trimiți un tichet de suport sau să ceri ajutor în comunitatea noastră prietenoasă de pe Facebook."

#: static/js/dashboard/helpers/SubmitSupport.js:33
msgid "Submit a Support Ticket"
msgstr "Trimite un tichet de suport"

#: static/js/dashboard/helpers/SubmitSupport.js:41
msgid "Join Facebook Community"
msgstr "Intră pe comunitatea Facebook"

#: static/js/dashboard/helpers/useUpsellModal.js:134
msgid "Upgrade Now"
msgstr "Fă upgrade acum"

#: static/js/options/ConditionsManager/SingleCondition.js:114
msgid "Select rule"
msgstr "Alege regula"

#: static/js/options/ConditionsManager/SingleCondition.js:204
msgid "Select taxonomy"
msgstr "Selectează taxonomia"

#: static/js/options/ConditionsManager/SingleCondition.js:236
msgid "Select language"
msgstr "Alege limba"

#: static/js/options/ConditionsManager/SingleCondition.js:292
msgid "Select user"
msgstr "Alege utilizatorul"

#: static/js/options/ConditionsManager/SingleCondition.js:265
msgid "Current user"
msgstr "Utilizator actual"

#: static/js/options/DisplayCondition.js:28
msgid "Add Display Condition"
msgstr "Adaugă condiție afișare"

#: framework/features/conditions-manager.php:242,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:126
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:283
#: static/js/options/ConditionsManager/SingleCondition.js:97
msgid "Include"
msgstr "Include"

#: framework/features/conditions-manager.php:242,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:127
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:291
#: static/js/options/ConditionsManager/SingleCondition.js:98
msgid "Exclude"
msgstr "Exclude"

#: framework/premium/static/js/options/PreviewedPostsSelect.js:170
#: static/js/options/ConditionsManager/PostIdPicker.js:68
msgid "Type to search by ID or title..."
msgstr "Scrie pentru a căuta după ID sau titlu…"

#: framework/premium/static/js/options/PreviewedPostsSelect.js:174
#: static/js/options/ConditionsManager/PostIdPicker.js:74
msgid "Select post"
msgstr "Alege postarea"

#: static/js/options/ConditionsManager/PostIdPicker.js:76
msgid "Select page"
msgstr "Alege pagina"

#: static/js/options/CustomizerOptionsManager.js:113
msgid "Import Options"
msgstr "Importă opțiunile"

#: static/js/options/CustomizerOptionsManager.js:116
msgid "Easily import the theme customizer settings."
msgstr "Importă setările din personalizator cu ușurință."

#: static/js/options/CustomizerOptionsManager.js:140
msgid "Click or drop to upload a file..."
msgstr "Apasă butonul sau plasează fișierul pentru a-l încărca…"

#: static/js/options/CustomizerOptionsManager.js:239
msgid "Import Customizations"
msgstr "Importă personalizări"

#: static/js/options/CustomizerOptionsManager.js:249
msgid "Copy Options"
msgstr "Copiază opțiuni"

#: static/js/options/CustomizerOptionsManager.js:252
msgid "Copy and import your customizations from parent or child theme."
msgstr "Copiază și importă personalizările din tema părinte sau copil."

#: static/js/options/CustomizerOptionsManager.js:308
msgid "Copy From Parent Theme"
msgstr "Copiază din tema părinte"

#: static/js/options/CustomizerOptionsManager.js:314
msgid "Copy From Child Theme"
msgstr "Copiază din tema copil"

#: static/js/options/CustomizerOptionsManager.js:321
msgid "You are about to copy all the settings from your parent theme into the child theme. Are you sure you want to continue?"
msgstr "Ești pe punctul de a copia toate setările din tema părinte în tema copil. Ești sigur că vrei să continui?"

#: static/js/options/CustomizerOptionsManager.js:327
msgid "You are about to copy all the settings from your child theme into the parent theme. Are you sure you want to continue?"
msgstr "Ești pe punctul de a copia toate setările din tema copil în tema părinte. Ești sigur că vrei să continui?"

#: static/js/options/CustomizerOptionsManager.js:376
msgid "Yes, I am sure"
msgstr "Da, sunt sigur"

#: static/js/options/CustomizerOptionsManager.js:390
msgid "Export Settings"
msgstr "Exporta setările"

#: static/js/options/CustomizerOptionsManager.js:394
msgid "Choose what set of settings you want to export."
msgstr "Alege ce set de setări dorești să exporți."

#: static/js/options/CustomizerOptionsManager.js:439
msgid "Customizer settings"
msgstr "Setările personalizatorului"

#: static/js/options/CustomizerOptionsManager.js:443
msgid "Widgets settings"
msgstr "Setările widgeturilor"

#: static/js/options/CustomizerOptionsManager.js:536
msgid "Export"
msgstr "Exportă"

#: static/js/options/CustomizerOptionsManager.js:87
msgid "Export Options"
msgstr "Exportă opțiuni"

#: static/js/options/CustomizerOptionsManager.js:90
msgid "Easily export the theme customizer settings."
msgstr "Importă setările din personalizator cu ușurință."

#: static/js/options/CustomizerOptionsManager.js:107
msgid "Export Customizations"
msgstr "Exportă personalizări"

#: static/js/options/DisplayCondition.js:19
msgid "Transparent Header Display Conditions"
msgstr "Condițiile de afișare pentru antetul transparent"

#: static/js/options/DisplayCondition.js:23
msgid "Add one or more conditions to display the transparent header."
msgstr "Adaugă una sau mai multe condiții pentru a afișa antetul transparent."

#: static/js/dashboard/screens/DemoInstall.js:166
msgid "Loading Starter Sites..."
msgstr "Încărcare site-uri demonstrative…"

#: static/js/dashboard/screens/DemoInstall/components/Progress.js:29
msgid "Installing"
msgstr "Se instalează"

#: static/js/dashboard/screens/DemoInstall/components/Progress.js:34
msgid "Please be patient and don't refresh this page, the import process may take a while, this also depends on your server."
msgstr "Te rugăm să ai răbdare și să nu reîmprospătezi această pagină, procesul de import poate dura ceva timp, depinzând și de serverul tău."

#: static/js/dashboard/screens/DemoInstall/DemoToInstall.js:306
msgid "Back"
msgstr "Înapoi"

#: static/js/dashboard/screens/DemoInstall/DemoToInstall.js:346
msgid "Install"
msgstr "Instalează"

#: static/js/dashboard/screens/DemoInstall/DemoToInstall.js:347
msgid "Next"
msgstr "Următorul"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:182
msgid "Import"
msgstr "Importă"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:127
msgid "Available for"
msgstr "Disponibil pentru"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:166
msgid "Preview"
msgstr "Previzualizează"

#: static/js/dashboard/screens/DemoInstall/SingleDemo.js:181
msgid "Modify"
msgstr "Modifică"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:22
msgid "Starter Site Imported Successfully"
msgstr "Site-ul demonstrativ a fost importat cu succes"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:25
msgid "Now you can view your website or start customizing it"
msgstr "Acum poți vizualiza site-ul tău sau începe personalizarea acestuia"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:33
#: static/js/dashboard/screens/Extensions/CurrentExtension.js:342
msgid "Customize"
msgstr "Personalizează"

#: static/js/dashboard/screens/DemoInstall/Installer/InstallCompleted.js:40
msgid "View site"
msgstr "Vizualizează site"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:12
msgid "copying child theme sources"
msgstr "copierea surselor temei copil"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:16
msgid "activating child theme"
msgstr "activez tema copil"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:21
msgid "installing plugin %s"
msgstr "instalarea pluginului %s"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:28
msgid "activating plugin %s"
msgstr "activarea pluginului %s"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:34
msgid "downloading demo widgets"
msgstr "descărcarea widgeturilor demo"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:38
msgid "installing demo widgets"
msgstr "instalarea widgeturilor demo"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:42
msgid "downloading demo options"
msgstr "descărcarea opțiunilor demo"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:46
msgid "importing images from customizer"
msgstr "importarea imaginilor din personalizator"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:50
msgid "import customizer options"
msgstr "importarea opțiunilor din personalizator"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:54
msgid "activating required extensions"
msgstr "activarea extensiilor necesare"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:58
msgid "removing previously installed posts"
msgstr "eliminarea articolelor instalate anterior"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:62
msgid "removing previously installed taxonomies"
msgstr "eliminarea taxonomiilor instalate anterior"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:66
msgid "removing default WordPress pages"
msgstr "eliminarea paginilor implicite WordPress"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:70
msgid "resetting customizer options"
msgstr "resetarea opțiunilor din personalizator"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:74
msgid "resetting widgets"
msgstr "resetarea widgeturilor"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:89
msgid "users"
msgstr "utilizatori"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:90
msgid "terms"
msgstr "termeni"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:91
msgid "images"
msgstr "imagini"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:92
msgid "posts"
msgstr "postări"

#: static/js/dashboard/screens/DemoInstall/Installer/messages.js:93
msgid "comments"
msgstr "comentarii"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:118
msgid "Child theme"
msgstr "Temă copil"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:143
#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:70
msgid "Erase content"
msgstr "Șterge conținutul"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:185
msgid "Final touches"
msgstr "Ultimele ajustări"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:155
msgid "Import options"
msgstr "Importă opțiunile"

#: static/js/dashboard/screens/DemoInstall/Installer/useInstaller.js:168
msgid "Import widgets"
msgstr "Importă widget-urile"

#: static/js/dashboard/screens/DemoInstall/Installer/contentCalculation.js:9
msgid "Import content"
msgstr "Importă conținutul"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:108
msgid "You already have a child theme properly installed and activated. Move on."
msgstr "Deja ai o temă copil instalată și activată corect. Continuă."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:116
msgid "Learn more about child themes"
msgstr "Află mai multe despre temele copil"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:70
msgid "We strongly recommend to install the child theme, this way you will have freedom to make changes without breaking the parent theme."
msgstr "Recomandăm cu tărie instalarea temei copil, astfel vei avea libertatea să faci modificări fără să afectezi tema părinte."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:77
msgid "We strongly recommend to activate the child theme, this way you will have freedom to make changes without breaking the parent theme."
msgstr "Recomandăm cu tărie activarea temei copil, astfel vei avea libertatea să faci modificări fără să afectezi tema părinte."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:97
msgid "Install Child Theme"
msgstr "Instalează tema copil"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ChildTheme.js:98
msgid "Activate Child Theme"
msgstr "Activează tema copil"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:56
msgid "Import Content"
msgstr "Importă conținutul"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:59
msgid "This will import posts, pages, comments, navigation menus, custom fields, terms and custom posts."
msgstr "Aceasta va importa articole, pagini, comentarii, meniuri de navigare, câmpuri personalizate, termeni și articole personalizate."

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:104
msgid "Clean Install"
msgstr "Instalare curată"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Content.js:106
msgid "This option will remove the previous imported content and will perform a fresh and clean install."
msgstr "Această opțiune va elimina conținutul importat anterior și va efectua o instalare proaspătă și curată."

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:147
msgid "This starter site is already installed"
msgstr "Acest site demonstrativ este deja instalat"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:156
msgid "Starter Site Removed"
msgstr "Site-ul demonstrativ a fost eliminat"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:165
msgid "Dismiss"
msgstr "Renunta"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:174
msgid "What steps do you want to perform next?"
msgstr "Ce pași dorești să efectuezi în continuare?"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:191
msgid "Remove"
msgstr "Elimină"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:200
msgid "Reinstall"
msgstr "Reinstalează"

#: static/js/dashboard/screens/DemoInstall/Wizzard/ModifyDemo.js:55
msgid "Deactivate demo plugins"
msgstr "Dezactivează pluginurile demo"

#: static/js/dashboard/screens/DemoInstall/Wizzard/PickBuilder.js:62
msgid "Choose Page Builder"
msgstr "Alege constructorul de pagini"

#: static/js/dashboard/screens/DemoInstall/Wizzard/PickBuilder.js:65
msgid "This starter site can be imported and used with one of these page builders. Please select one in order to continue."
msgstr "Acest site de pornire poate fi importat și utilizat cu unul dintre acești constructori de pagini. Te rugăm să selectezi unul pentru a continua."

#: static/js/dashboard/screens/DemoInstall/Wizzard/Plugins.js:71
msgid "Install & Activate Plugins"
msgstr "Instalează și activează pluginurile"

#: static/js/dashboard/screens/DemoInstall/Wizzard/Plugins.js:73
msgid "The following plugins are required for this starter site in order to work properly."
msgstr "Următoarele pluginuri sunt necesare pentru ca acest site de pornire să funcționeze corect."

#: static/js/dashboard/screens/Extension.js:95
#: static/js/dashboard/screens/Extensions.js:86
msgid "Loading Extensions Status..."
msgstr "Se încarcă starea extensiilor..."

#: static/js/dashboard/screens/Extensions/Sidebar.js:60
msgid "Free Extensions"
msgstr "Extensii gratuite"

#: static/js/dashboard/screens/Extensions/Sidebar.js:68
msgid "Pro Extensions"
msgstr "Extensii Pro"

#: static/js/dashboard/screens/SiteExport.js:239
msgid "Builder"
msgstr "Constructor"

#: static/js/dashboard/screens/SiteExport.js:311
msgid "Export site"
msgstr "Exportă site"

#: framework/premium/extensions/mega-menu/extension.php:325
msgid "New"
msgstr "Nou"

#: framework/premium/extensions/mega-menu/options.php:16,
#: framework/premium/extensions/mega-menu/options.php:581
msgid "Mega Menu Settings"
msgstr "Setări Mega Meniu"

#: framework/premium/extensions/mega-menu/options.php:28
msgid "Dropdown Width"
msgstr "Lățimea meniului derulant"

#: framework/premium/extensions/mega-menu/options.php:36,
#: framework/premium/extensions/mega-menu/options.php:49
msgid "Content Width"
msgstr "Lățime conținut"

#: framework/premium/extensions/mega-menu/options.php:37,
#: framework/premium/extensions/mega-menu/options.php:58
msgid "Full Width"
msgstr "Lățime completă"

#: framework/premium/extensions/mega-menu/options.php:38,
#: framework/premium/features/content-blocks/options/archive.php:86
msgid "Custom Width"
msgstr "Lățime personalizată"

#: framework/premium/extensions/mega-menu/options.php:57
msgid "Default Width"
msgstr "Lățime prestabilită"

#: framework/premium/extensions/mega-menu/options.php:86,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:178,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:197
msgid "Columns"
msgstr "Coloane"

#: framework/premium/extensions/mega-menu/options.php:333
msgid "Custom Content"
msgstr "Conținut personalizat"

#: framework/premium/extensions/mega-menu/options.php:337
msgid "Content Type"
msgstr "Tip conținut"

#: framework/premium/extensions/mega-menu/options.php:344
msgid "Default (Menu Item)"
msgstr "Implicit (Element de meniu)"

#: framework/premium/extensions/mega-menu/options.php:345
msgid "Custom Text"
msgstr "Text personalizat"

#: framework/premium/extensions/mega-menu/options.php:432,
#: framework/premium/extensions/mega-menu/options.php:791
msgid "Item Label Settings"
msgstr "Setări etichetă element"

#: framework/premium/extensions/mega-menu/options.php:437
msgid "Item Label"
msgstr "Etichetă element"

#: framework/premium/extensions/mega-menu/options.php:448
msgid "Enabled"
msgstr "Activat"

#: framework/premium/extensions/mega-menu/options.php:449,
#: framework/premium/features/content-blocks/options/404.php:111,
#: framework/premium/features/content-blocks/options/archive.php:188,
#: framework/premium/features/content-blocks/options/header.php:129,
#: framework/premium/features/content-blocks/options/hook.php:238,
#: framework/premium/features/content-blocks/options/maintenance.php:108,
#: framework/premium/features/content-blocks/options/nothing_found.php:129,
#: framework/premium/features/content-blocks/options/single.php:120
msgid "Disabled"
msgstr "Dezactivat"

#: framework/premium/extensions/mega-menu/options.php:450
msgid "Heading"
msgstr "Titlu"

#: framework/premium/extensions/mega-menu/options.php:456
msgid "Label Link"
msgstr "Link etichetă"

#: framework/extensions/trending/customizer.php:496,
#: framework/premium/extensions/mega-menu/options.php:550,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:154,
#: framework/premium/features/premium-header/items/contacts/options.php:526,
#: framework/premium/features/premium-header/items/language-switcher/options.php:105,
#: framework/premium/features/premium-header/items/search-input/options.php:223
msgid "Vertical Alignment"
msgstr "Aliniere verticală"

#: framework/features/header/header-options.php:209,
#: framework/features/header/header-options.php:236,
#: framework/features/header/header-options.php:251,
#: framework/features/header/header-options.php:266,
#: framework/premium/extensions/mega-menu/options.php:585,
#: framework/premium/extensions/shortcuts/customizer.php:1181,
#: framework/premium/extensions/shortcuts/customizer.php:1223,
#: framework/premium/extensions/shortcuts/customizer.php:1265,
#: framework/features/header/items/account/options.php:1733,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:37,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:77,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:325,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:196,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:541,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:577,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:611,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:480,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:512,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:542
msgid "Background"
msgstr "Fundal"

#: framework/premium/extensions/mega-menu/options.php:599
msgid "Link Color"
msgstr "Culoare legătură"

#: framework/premium/extensions/mega-menu/options.php:619,
#: framework/features/header/items/account/options.php:1933,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:634,
#: framework/premium/features/premium-header/items/contacts/options.php:641,
#: framework/premium/features/premium-header/items/contacts/options.php:681,
#: framework/premium/features/premium-header/items/contacts/options.php:720
#: static/js/editor/blocks/breadcrumbs/Edit.js:60
#: static/js/editor/blocks/contact-info/Edit.js:114
msgid "Link Initial"
msgstr "Link inițial"

#: framework/premium/extensions/mega-menu/options.php:624
msgid "Link Hover/Active"
msgstr "Link activ/la trecerea mouse-ului"

#: framework/premium/extensions/mega-menu/options.php:629,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:149
msgid "Background Hover"
msgstr "Fundal la trecerea mouse-ului"

#: framework/premium/extensions/mega-menu/options.php:646,
#: framework/premium/extensions/mega-menu/options.php:803
msgid "Heading Color"
msgstr "Culoare titlu"

#: framework/premium/extensions/mega-menu/options.php:658,
#: framework/premium/extensions/mega-menu/options.php:677,
#: framework/premium/extensions/mega-menu/options.php:815
msgid "Initial Color"
msgstr "Culoare inițială"

#: framework/extensions/cookies-consent/customizer.php:147,
#: framework/extensions/newsletter-subscribe/customizer.php:196,
#: framework/premium/extensions/mega-menu/options.php:665,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:342
#: static/js/editor/blocks/about-me/Edit.js:89
#: static/js/editor/blocks/breadcrumbs/Edit.js:43
#: static/js/editor/blocks/contact-info/Edit.js:96
msgid "Text Color"
msgstr "Culoare text"

#: framework/premium/extensions/mega-menu/options.php:684,
#: framework/premium/extensions/shortcuts/customizer.php:1282,
#: framework/premium/features/premium-header/items/search-input/options.php:904,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:161
msgid "Items Divider"
msgstr "Divizor de elemente"

#: framework/premium/extensions/mega-menu/options.php:699
msgid "Columns Divider"
msgstr "Divizor de coloane"

#: framework/premium/extensions/mega-menu/options.php:714,
#: framework/premium/features/premium-header/items/search-input/options.php:886
msgid "Dropdown Shadow"
msgstr "Umbră meniului derulant"

#: framework/premium/extensions/mega-menu/options.php:746
msgid "Column Settings"
msgstr "Setări coloane"

#: framework/premium/extensions/mega-menu/options.php:750
msgid "Column Spacing"
msgstr "Spațiere coloane"

#: framework/premium/extensions/mega-menu/options.php:825,
#: framework/features/header/items/account/options.php:1327,
#: framework/features/header/items/account/options.php:1366,
#: framework/features/header/items/account/options.php:1409,
#: framework/features/header/items/account/options.php:1450,
#: framework/features/header/items/account/options.php:1738,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:300,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:328,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:359,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:388,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:331,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:213,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:353,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:382,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:413,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:442,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:354,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:382,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:417,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:450,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:308,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:336,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:367,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:396
#: static/js/editor/blocks/share-box/Edit.js:81
#: static/js/editor/blocks/socials/Edit.js:81
msgid "Icon Color"
msgstr "Culoare pictogramă"

#: framework/helpers/exts-configs.php:137
msgid "Enables support for Custom Fields inside archive cards and single page post title, adds a reading progress bar for your posts and lets you set featured images and colors for your categories archives."
msgstr "Activează suportul pentru câmpuri personalizate în cardurile arhivelor și în titlul articolelor individuale, adaugă o bară de progres pentru citire și îți permite să setezi imagini reprezentative și culori pentru arhivele categoriilor."

#: framework/helpers/exts-configs.php:180,
#: framework/premium/extensions/shortcuts/config.php:6
msgid "Easily turn your websites into mobile first experiences. You can easily add the most important actions at the bottom of the screen for easy access."
msgstr "Transformă-ți site-urile cu ușurință în experiențe optimizate pentru mobil. Poți adăuga cele mai importante acțiuni în partea de jos a ecranului pentru acces facil."

#: framework/premium/extensions/shortcuts/customizer.php:12,
#: framework/premium/extensions/shortcuts/customizer.php:38,
#: framework/premium/extensions/shortcuts/customizer.php:790,
#: framework/premium/extensions/shortcuts/extension.php:173,
#: framework/premium/extensions/shortcuts/views/bar.php:13,
#: framework/premium/extensions/shortcuts/views/bar.php:46
msgid "Home"
msgstr "Acasă"

#: framework/features/blocks/contact-info/options.php:139,
#: framework/premium/extensions/shortcuts/customizer.php:71,
#: framework/premium/extensions/shortcuts/customizer.php:97,
#: framework/premium/extensions/shortcuts/customizer.php:799,
#: framework/premium/extensions/shortcuts/extension.php:182,
#: framework/premium/extensions/shortcuts/views/bar.php:22,
#: framework/premium/extensions/shortcuts/views/bar.php:47,
#: framework/premium/features/premium-header/items/contacts/options.php:127
msgid "Phone"
msgstr "Telefon"

#: framework/premium/extensions/shortcuts/customizer.php:201,
#: framework/premium/extensions/shortcuts/customizer.php:227,
#: framework/premium/extensions/shortcuts/views/bar.php:49
msgid "Scroll Top"
msgstr "Derulează în sus"

#: framework/premium/extensions/shortcuts/customizer.php:352,
#: framework/premium/extensions/shortcuts/customizer.php:378,
#: framework/premium/extensions/shortcuts/views/bar.php:50
msgid "Cart"
msgstr "Coș"

#: framework/premium/extensions/shortcuts/customizer.php:411,
#: framework/premium/extensions/shortcuts/customizer.php:437
msgid "Shop"
msgstr "Magazin"

#: framework/helpers/exts-configs.php:262,
#: framework/premium/extensions/shortcuts/customizer.php:545,
#: framework/premium/extensions/shortcuts/customizer.php:571,
#: framework/premium/extensions/shortcuts/views/bar.php:52,
#: framework/features/header/items/account/views/login.php:520,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:309,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:406,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:410,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:438,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/feature.php:442,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/helpers.php:168,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:17,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/config.php:4,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:141,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/view.php:100
msgid "Wishlist"
msgstr "Listă de dorințe"

#: framework/premium/extensions/shortcuts/customizer.php:784
msgid "Shortcuts"
msgstr "Scurtături"

#: framework/premium/extensions/shortcuts/customizer.php:839,
#: framework/features/header/items/account/options.php:520,
#: framework/features/header/items/account/options.php:983,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:38,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:91,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:92
msgid "Label Visibility"
msgstr "Vizibilitatea etichetei"

#: framework/premium/extensions/shortcuts/customizer.php:879,
#: framework/features/header/items/account/options.php:554,
#: framework/features/header/items/account/options.php:1017,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:88,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:122,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:123
msgid "Label Position"
msgstr "Poziția etichetei"

#: framework/premium/extensions/shortcuts/customizer.php:888,
#: framework/premium/features/content-blocks/hooks-manager.php:477,
#: framework/features/header/items/account/options.php:563,
#: framework/features/header/items/account/options.php:1035,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:97,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:21,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:131,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:132
msgid "Bottom"
msgstr "Jos"

#: framework/premium/extensions/mega-menu/options.php:482,
#: framework/premium/extensions/shortcuts/customizer.php:925,
#: framework/features/header/items/account/options.php:508,
#: framework/features/header/items/account/options.php:882,
#: framework/features/header/items/account/options.php:968,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:46,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:439,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:202,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:42,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:73,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:74
msgid "Icon Size"
msgstr "Mărime pictogramă"

#: framework/premium/extensions/shortcuts/customizer.php:936,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:383
msgid "Container Height"
msgstr "Înălțime container"

#: framework/premium/extensions/shortcuts/customizer.php:951
msgid "Container Max Width"
msgstr "Lățime maximă container"

#: framework/premium/extensions/shortcuts/customizer.php:975
msgid "Scroll Interaction"
msgstr "Interacțiune la derulare"

#: framework/premium/extensions/shortcuts/customizer.php:981
msgid "Hide"
msgstr "Ascunde"

#: framework/premium/extensions/shortcuts/customizer.php:1023
msgid "Shortcuts Bar Display Conditions"
msgstr "Condiții de afișare pentru bara de scurtături"

#: framework/premium/extensions/shortcuts/customizer.php:1024
msgid "Add one or more conditions to display the shortcuts bar."
msgstr "Adaugă una sau mai multe condiții pentru a afișa bara de scurtături."

#: framework/premium/extensions/shortcuts/customizer.php:1048,
#: framework/features/header/items/account/options.php:1895,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:184,
#: framework/premium/features/premium-header/items/contacts/options.php:577,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:105,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:212,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:42
msgid "Font"
msgstr "Font"

#: framework/features/blocks/about-me/options.php:187,
#: framework/features/blocks/share-box/options.php:141,
#: framework/features/blocks/socials/options.php:93,
#: framework/premium/extensions/shortcuts/customizer.php:1092,
#: framework/premium/features/premium-header/items/contacts/options.php:751,
#: framework/premium/features/premium-header/items/contacts/options.php:780,
#: framework/premium/features/premium-header/items/contacts/options.php:811,
#: framework/premium/features/premium-header/items/contacts/options.php:840
#: static/js/editor/blocks/about-me/Edit.js:117
#: static/js/editor/blocks/contact-info/Edit.js:133
msgid "Icons Color"
msgstr "Culoare pictograme"

#: framework/premium/extensions/shortcuts/customizer.php:1163
msgid "Cart Badge Color"
msgstr "Culoarea insignei coșului"

#: framework/premium/extensions/shortcuts/customizer.php:1302
msgid "Items Divider Height"
msgstr "Înălțimea divizorului de elemente"

#: framework/premium/extensions/shortcuts/customizer.php:1316,
#: framework/features/header/items/account/options.php:2016,
#: framework/premium/features/content-blocks/options/popup.php:545,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:701,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:150,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:175
msgid "Shadow"
msgstr "Umbră"

#: framework/helpers/exts-configs.php:195
msgid "Create unlimited personalized sets of widget areas and display them on any page or post using our conditional logic functionality."
msgstr "Creează seturi nelimitate de zone de widgeturi personalizate și afișează-le pe orice pagină sau articol folosind funcționalitatea noastră de logică condițională."

#: framework/helpers/exts-configs.php:199
msgid "Create New Sidebar"
msgstr "Creează un nou Sidebar"

#: framework/premium/extensions/sidebars/form.php:3
#: framework/premium/extensions/sidebars/static/js/main.js:50
msgid "Create Sidebar/Widget Area"
msgstr "Creează o zonă de bare laterale/widgeturi"

#: framework/premium/extensions/sidebars/form.php:6
msgid "In order to create a new sidebar/widget area simply enter a name in the input below and click the Create Sidebar button."
msgstr "Pentru a crea o nouă zonă laterală/widget, introdu un nume în câmpul de mai jos și apasă butonul Creează bară laterală."

#: framework/premium/extensions/sidebars/form.php:16
#: framework/premium/extensions/sidebars/static/js/main.js:66
msgid "Create Sidebar"
msgstr "Creează Sidebar"

#: framework/premium/extensions/sidebars/form.php:20
msgid "Available Sidebars/Widget Areas"
msgstr "Zone laterale/widget disponibile"

#: framework/helpers/exts-configs.php:215
msgid "Replace Blocksy's branding with your own. Easily hide licensing info and other sections of the theme and companion plugin from your clients and make your final product look more professional."
msgstr "Înlocuiește brandingul Blocksy cu cel propriu. Ascunde cu ușurință informațiile despre licență și alte secțiuni ale temei și pluginului asociat față de clienți pentru a face produsul final să arate mai profesional."

#: framework/helpers/exts-configs.php:228
msgid "Make the shopping experience better for your visitors! Add features such as Product Quick View, Wishlist functionality and a Floating Add to Cart button. Customize the single product gallery/slider and the layout."
msgstr "Îmbunătățește experiența de cumpărături pentru vizitatorii tăi! Adaugă funcționalități precum Vizualizare rapidă produs, Listă de dorințe și un buton plutitor Adaugă în coș. Personalizează galeria/sliderul de produse și aspectul acestora."

#: framework/premium/extensions/woocommerce-extra/features/quick-view/feature.php:14
msgid "Quick View Button"
msgstr "Butonul de previzualizare rapidă"

#: framework/features/header/items/account/options.php:467,
#: framework/features/header/items/account/options.php:841,
#: framework/features/header/items/account/options.php:929,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/archive-card/feature.php:13,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:66,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:111,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:65,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:66
msgid "Type 3"
msgstr "Tip 3"

#: framework/features/header/items/account/options.php:477,
#: framework/features/header/items/account/options.php:851,
#: framework/features/header/items/account/options.php:939,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:71,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:116
msgid "Type 4"
msgstr "Tip 4"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/views/panel.php:61
msgid "Available Filters"
msgstr "Filtre disponibile"

#: framework/premium/extensions/woocommerce-extra/extension.php:128
msgid "Quick view title before"
msgstr "Titlul vizualizării rapide înainte"

#: framework/premium/extensions/woocommerce-extra/extension.php:133
msgid "Quick view title after"
msgstr "Titlul vizualizării rapide după"

#: framework/premium/extensions/woocommerce-extra/extension.php:138
msgid "Quick view price before"
msgstr "Prețul vizualizării rapide înainte"

#: framework/premium/extensions/woocommerce-extra/extension.php:143
msgid "Quick view price after"
msgstr "Prețul vizualizării rapide după"

#: framework/premium/extensions/woocommerce-extra/extension.php:148
msgid "Quick view summary before"
msgstr "Rezumatul vizualizării rapide înainte"

#: framework/premium/extensions/woocommerce-extra/extension.php:153
msgid "Quick view summary after"
msgstr "Rezumatul vizualizării rapide după"

#: framework/helpers/exts-configs.php:238,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:4
msgid "Floating Cart"
msgstr "Coș plutitor"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:15
msgid "Position"
msgstr "Poziție"

#: framework/premium/features/content-blocks/hooks-manager.php:439,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:20
msgid "Top"
msgstr "Sus"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:68
msgid "Product Title Visibility"
msgstr "Vizibilitate titlu produs"

#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:30
msgid "Floating Cart Visibility"
msgstr "Vizibilitatea coșului plutitor"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/view.php:77
msgid "Go to product page"
msgstr "Du-te la pagina de produs"

#: framework/premium/extensions/shortcuts/customizer.php:507,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/helpers.php:40,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:111
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/price-filter/index.js:108
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/index.js:137
msgid "Filter"
msgstr "Filtre"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:532
msgid "Filter Widgets"
msgstr "Widget-uri filtrare"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:536
msgid "Widgets"
msgstr "Widget-uri"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:580
msgid "Widgets Vertical Spacing"
msgstr "Distanțarea verticală a widgeturilor"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:598
msgid "Widgets Font"
msgstr "Font widget-uri"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:606
msgid "Widgets Font Color"
msgstr "Culoare font widget-uri"

#: framework/features/header/items/account/options.php:1927,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:134,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:628,
#: framework/premium/features/premium-header/items/contacts/options.php:635,
#: framework/premium/features/premium-header/items/contacts/options.php:676,
#: framework/premium/features/premium-header/items/contacts/options.php:715
msgid "Text Initial"
msgstr "Text inițial"

#: framework/features/header/items/account/options.php:1939,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:639,
#: framework/premium/features/premium-header/items/contacts/options.php:647,
#: framework/premium/features/premium-header/items/contacts/options.php:687,
#: framework/premium/features/premium-header/items/contacts/options.php:726
#: static/js/editor/blocks/breadcrumbs/Edit.js:66
#: static/js/editor/blocks/contact-info/Edit.js:123
msgid "Link Hover"
msgstr "Link la trecerea mouse-ului"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:123
msgid "Panel Reveal"
msgstr "Dezvăluire panou"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:131,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:39
msgid "Left Side"
msgstr "Partea din stânga"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:132,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:40
msgid "Right Side"
msgstr "Partea din dreapta"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:137,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:45
msgid "Panel Width"
msgstr "Lățime panou"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:295,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:163
msgid "Panel Shadow"
msgstr "Umbră panou"

#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:263,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:76
msgid "Panel Background"
msgstr "Fundal panou"

#: framework/premium/features/content-blocks/options/popup.php:615,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:779
msgid "Close Icon Color"
msgstr "Culoare pictograma buton închidere"

#: framework/premium/features/content-blocks/options/popup.php:646
msgid "Close Icon Background"
msgstr "Fundal pictogramă închidere"

#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:175
#: static/js/editor/blocks/share-box/index.js:47
msgid "Share Box"
msgstr "Casetă distribuire"

#: framework/premium/features/content-blocks/hooks-manager.php:11
msgid "WP head"
msgstr "Capul WP"

#: framework/premium/features/content-blocks/hooks-manager.php:13,
#: framework/premium/features/content-blocks/hooks-manager.php:22,
#: framework/premium/features/content-blocks/hooks-manager.php:32,
#: framework/premium/features/content-blocks/hooks-manager.php:41
msgid "Head"
msgstr "Cap"

#: framework/premium/features/content-blocks/hooks-manager.php:20
msgid "WP head start"
msgstr "Început cap WP"

#: framework/premium/features/content-blocks/hooks-manager.php:30
msgid "WP head end"
msgstr "Sfârșit cap WP"

#: framework/premium/features/content-blocks/hooks-manager.php:48
msgid "Header before"
msgstr "Antet înainte"

#: framework/premium/features/content-blocks/admin-ui.php:265,
#: framework/premium/features/content-blocks/hooks-manager.php:50,
#: framework/premium/features/content-blocks/hooks-manager.php:59
msgid "Header"
msgstr "Antet"

#: framework/premium/features/content-blocks/hooks-manager.php:57
msgid "Header after"
msgstr "Antet după"

#: framework/premium/features/content-blocks/hooks-manager.php:66
msgid "Desktop top"
msgstr "Partea de sus a desktopului"

#: framework/premium/features/content-blocks/hooks-manager.php:68,
#: framework/premium/features/content-blocks/hooks-manager.php:77,
#: framework/premium/features/content-blocks/hooks-manager.php:86,
#: framework/premium/features/content-blocks/hooks-manager.php:95
msgid "Header offcanvas"
msgstr "Antet offcanvas"

#: framework/premium/features/content-blocks/hooks-manager.php:75
msgid "Desktop bottom"
msgstr "Partea de jos a desktopului"

#: framework/premium/features/content-blocks/hooks-manager.php:84
msgid "Mobile top"
msgstr "Partea de sus a mobilului"

#: framework/premium/features/content-blocks/hooks-manager.php:93
msgid "Mobile bottom"
msgstr "Partea de jos a mobilului"

#: framework/premium/features/content-blocks/hooks-manager.php:102
msgid "Sidebar before"
msgstr "Bară laterală înainte"

#: framework/premium/features/content-blocks/hooks-manager.php:103,
#: framework/premium/features/content-blocks/hooks-manager.php:110,
#: framework/premium/features/content-blocks/hooks-manager.php:117,
#: framework/premium/features/content-blocks/hooks-manager.php:124
msgid "Left/Right sidebar"
msgstr "Bară laterală stânga/dreapta"

#: framework/premium/features/content-blocks/hooks-manager.php:109
msgid "Sidebar start"
msgstr "Început bară laterală"

#: framework/premium/features/content-blocks/hooks-manager.php:116
msgid "Sidebar end"
msgstr "Sfârșit bară laterală"

#: framework/premium/features/content-blocks/hooks-manager.php:123
msgid "Sidebar after"
msgstr "Bară laterală după"

#: framework/premium/features/content-blocks/hooks-manager.php:130
msgid "Dynamic sidebar before"
msgstr "Bară laterală dinamică înainte"

#: framework/premium/features/content-blocks/hooks-manager.php:131,
#: framework/premium/features/content-blocks/hooks-manager.php:138,
#: framework/premium/features/content-blocks/hooks-manager.php:146
msgid "All widget areas"
msgstr "Început cap WP"

#: framework/premium/features/content-blocks/hooks-manager.php:137
msgid "Dynamic sidebar"
msgstr "Bară laterală dinamică"

#: framework/premium/features/content-blocks/hooks-manager.php:145
msgid "Dynamic sidebar after"
msgstr "Bară laterală dinamică după"

#: framework/premium/features/content-blocks/hooks-manager.php:154
msgid "Before section"
msgstr "Înainte de secțiune"

#: framework/premium/features/content-blocks/hooks-manager.php:155,
#: framework/premium/features/content-blocks/hooks-manager.php:163,
#: framework/premium/features/content-blocks/hooks-manager.php:171,
#: framework/premium/features/content-blocks/hooks-manager.php:179,
#: framework/premium/features/content-blocks/hooks-manager.php:187,
#: framework/premium/features/content-blocks/hooks-manager.php:195,
#: framework/premium/features/content-blocks/hooks-manager.php:203,
#: framework/premium/features/content-blocks/hooks-manager.php:211,
#: framework/premium/features/content-blocks/hooks-manager.php:219,
#: framework/premium/features/content-blocks/hooks-manager.php:227,
#: framework/premium/features/content-blocks/hooks-manager.php:235,
#: framework/premium/features/content-blocks/hooks-manager.php:243,
#: framework/premium/features/content-blocks/hooks-manager.php:251,
#: framework/premium/features/content-blocks/hooks-manager.php:259
msgid "Page/post title"
msgstr "Titlul paginii/articolului"

#: framework/premium/features/content-blocks/hooks-manager.php:162,
#: framework/premium/features/content-blocks/hooks-manager.php:312,
#: framework/premium/features/content-blocks/hooks-manager.php:354
msgid "Before title"
msgstr "Înainte de titlu"

#: framework/premium/features/content-blocks/hooks-manager.php:186
msgid "Before post meta"
msgstr "Înainte de meta articol"

#: framework/premium/features/content-blocks/hooks-manager.php:210,
#: framework/premium/features/content-blocks/hooks-manager.php:319,
#: framework/premium/features/content-blocks/hooks-manager.php:361
msgid "After title"
msgstr "După titlu"

#: framework/premium/features/content-blocks/hooks-manager.php:250
msgid "After post meta"
msgstr "După meta articol"

#: framework/premium/features/content-blocks/hooks-manager.php:258
msgid "After section"
msgstr "După secțiune"

#: framework/premium/features/content-blocks/hooks-manager.php:266
msgid "Before content"
msgstr "Înainte de conținut"

#: framework/premium/features/content-blocks/hooks-manager.php:274,
#: framework/premium/features/content-blocks/hooks-manager.php:447
msgid "Top content"
msgstr "Conținut de sus"

#: framework/premium/features/content-blocks/hooks-manager.php:282,
#: framework/premium/features/content-blocks/hooks-manager.php:469
msgid "Bottom content"
msgstr "Conținut de jos"

#: framework/premium/features/content-blocks/hooks-manager.php:290
msgid "After content"
msgstr "După conținut"

#: framework/premium/features/content-blocks/hooks-manager.php:298
msgid "Before comments"
msgstr "Înainte de comentarii"

#: framework/premium/features/content-blocks/hooks-manager.php:299,
#: framework/premium/features/content-blocks/hooks-manager.php:306,
#: framework/premium/features/content-blocks/hooks-manager.php:313,
#: framework/premium/features/content-blocks/hooks-manager.php:320,
#: framework/premium/features/content-blocks/hooks-manager.php:327,
#: framework/premium/features/content-blocks/hooks-manager.php:334
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:60
msgid "Comments"
msgstr "Comentarii"

#: framework/premium/features/content-blocks/hooks-manager.php:305
msgid "Top comments"
msgstr "Comentarii de sus"

#: framework/premium/features/content-blocks/hooks-manager.php:326
msgid "Bottom comments"
msgstr "Comentarii de jos"

#: framework/premium/features/content-blocks/hooks-manager.php:333
msgid "After comments"
msgstr "După comentarii"

#: framework/premium/features/content-blocks/hooks-manager.php:411,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:705
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:298
msgid "Before"
msgstr "Înainte"

#: framework/premium/features/content-blocks/hooks-manager.php:412,
#: framework/premium/features/content-blocks/hooks-manager.php:419
msgid "Loop"
msgstr "Buclă"

#: framework/premium/features/content-blocks/hooks-manager.php:418,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:716
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:307
msgid "After"
msgstr "După"

#: framework/premium/features/content-blocks/hooks-manager.php:425
msgid "Start"
msgstr "Start"

#: framework/premium/features/content-blocks/hooks-manager.php:426,
#: framework/premium/features/content-blocks/hooks-manager.php:433
msgid "Loop card"
msgstr "Card buclă"

#: framework/premium/features/content-blocks/hooks-manager.php:432
msgid "End"
msgstr "Sfârșit"

#: framework/premium/features/content-blocks/hooks-manager.php:455
msgid "After certain number of blocks"
msgstr "După un anumit număr de blocuri"

#: framework/premium/features/content-blocks/hooks-manager.php:462
msgid "Before certain number of headings"
msgstr "Înainte de un anumit număr de titluri"

#: framework/premium/features/content-blocks/hooks-manager.php:485
msgid "Login form start"
msgstr "Început formular de autentificare"

#: framework/premium/features/content-blocks/hooks-manager.php:486,
#: framework/premium/features/content-blocks/hooks-manager.php:493,
#: framework/premium/features/content-blocks/hooks-manager.php:500,
#: framework/premium/features/content-blocks/hooks-manager.php:507,
#: framework/premium/features/content-blocks/hooks-manager.php:514,
#: framework/premium/features/content-blocks/hooks-manager.php:521,
#: framework/premium/features/content-blocks/hooks-manager.php:528,
#: framework/premium/features/content-blocks/hooks-manager.php:535,
#: framework/premium/features/content-blocks/hooks-manager.php:542,
#: framework/premium/features/content-blocks/hooks-manager.php:549
msgid "Auth forms"
msgstr "Formulare de autentificare"

#: framework/premium/features/content-blocks/hooks-manager.php:492
msgid "Login form end"
msgstr "Sfârșit formular de autentificare"

#: framework/premium/features/content-blocks/hooks-manager.php:499
msgid "Login form modal start"
msgstr "Început fereastră modală autentificare"

#: framework/premium/features/content-blocks/hooks-manager.php:506
msgid "Login form modal end"
msgstr "Sfârșit fereastră modală autentificare"

#: framework/premium/features/content-blocks/hooks-manager.php:513
msgid "Register form start"
msgstr "Început formular de înregistrare"

#: framework/premium/features/content-blocks/hooks-manager.php:520
msgid "Register form end"
msgstr "Sfârșit formular de înregistrare"

#: framework/premium/features/content-blocks/hooks-manager.php:527
msgid "Register form modal start"
msgstr "Început fereastră modală înregistrare"

#: framework/premium/features/content-blocks/hooks-manager.php:534
msgid "Register form modal end"
msgstr "Sfârșit fereastră modală înregistrare"

#: framework/premium/features/content-blocks/hooks-manager.php:541
msgid "Lost password form modal start"
msgstr "Început fereastră modală parolă pierdută"

#: framework/premium/features/content-blocks/hooks-manager.php:548
msgid "Lost password form modal end"
msgstr "Sfârșit fereastră modală parolă pierdută"

#: framework/premium/features/content-blocks/hooks-manager.php:556
msgid "Before main content"
msgstr "Înainte de conținutul principal"

#: framework/premium/features/content-blocks/hooks-manager.php:562
msgid "After main content"
msgstr "După conținutul principal"

#: framework/premium/features/content-blocks/hooks-manager.php:583
msgid "WooCommerce Global"
msgstr "WooCommerce Global"

#: framework/premium/features/content-blocks/hooks-manager.php:588
msgid "Archive description"
msgstr "Descriere arhivă"

#: framework/premium/features/content-blocks/hooks-manager.php:593
msgid "Before shop loop"
msgstr "Înainte de bucla magazinului"

#: framework/premium/features/content-blocks/hooks-manager.php:610
msgid "Before shop loop item title"
msgstr "Înainte de titlul elementului din bucla magazinului"

#: framework/premium/features/content-blocks/hooks-manager.php:615
msgid "After shop loop item title"
msgstr "După titlul elementului din bucla magazinului"

#: framework/premium/features/content-blocks/hooks-manager.php:620
msgid "Before shop loop item price"
msgstr "Înainte de prețul elementului din bucla magazinului"

#: framework/premium/features/content-blocks/hooks-manager.php:625
msgid "After shop loop item price"
msgstr "După prețul elementului din bucla magazinului"

#: framework/premium/features/content-blocks/hooks-manager.php:640
msgid "After shop loop"
msgstr "După bucla magazinului"

#: framework/premium/features/content-blocks/hooks-manager.php:642
msgid "WooCommerce Archive"
msgstr "Arhivă WooCommerce"

#: framework/premium/features/content-blocks/hooks-manager.php:647
msgid "Before single product"
msgstr "Înainte de produsul unic"

#: framework/premium/features/content-blocks/hooks-manager.php:665
msgid "Product meta start"
msgstr "Început meta produs"

#: framework/premium/features/content-blocks/hooks-manager.php:669
msgid "Product meta end"
msgstr "Sfârșit meta produs"

#: framework/premium/features/content-blocks/hooks-manager.php:673
msgid "Share"
msgstr "Distribuie"

#: framework/premium/features/content-blocks/hooks-manager.php:677
msgid "After single product"
msgstr "După produsul unic"

#: framework/premium/features/content-blocks/hooks-manager.php:683
msgid "Before single product excerpt"
msgstr "Înainte de fragmentul produsului unic"

#: framework/premium/features/content-blocks/hooks-manager.php:688
msgid "After single product excerpt"
msgstr "După fragmentul produsului unic"

#: framework/premium/features/content-blocks/hooks-manager.php:693
msgid "Before single product tabs"
msgstr "Înainte de filele produsului unic"

#: framework/premium/features/content-blocks/hooks-manager.php:699
msgid "After single product tabs"
msgstr "După filele produsului unic"

#: framework/premium/features/content-blocks/hooks-manager.php:738
msgid "Cart is empty"
msgstr "Coșul este gol"

#: framework/premium/features/content-blocks/hooks-manager.php:742
msgid "Before cart"
msgstr "Înainte de coș"

#: framework/premium/features/content-blocks/hooks-manager.php:746
msgid "Before cart table"
msgstr "Înainte de tabelul coșului"

#: framework/premium/features/content-blocks/hooks-manager.php:750
msgid "Before cart contents"
msgstr "Înainte de conținutul coșului"

#: framework/premium/features/content-blocks/hooks-manager.php:754
msgid "Cart contents"
msgstr "Continut cos"

#: framework/premium/features/content-blocks/hooks-manager.php:758
msgid "After cart contents"
msgstr "După conținutul coșului"

#: framework/premium/features/content-blocks/hooks-manager.php:762
msgid "Cart coupon"
msgstr "Cupon coș"

#: framework/premium/features/content-blocks/hooks-manager.php:766
msgid "Cart actions"
msgstr "Acțiuni coș"

#: framework/premium/features/content-blocks/hooks-manager.php:770
msgid "After cart table"
msgstr "După tabelul coșului"

#: framework/premium/features/content-blocks/hooks-manager.php:774
msgid "Cart collaterals"
msgstr "Colaterale coș"

#: framework/premium/features/content-blocks/hooks-manager.php:778
msgid "Before cart totals"
msgstr "Înainte de totalurile coșului"

#: framework/premium/features/content-blocks/hooks-manager.php:782
msgid "Cart totals before order total"
msgstr "Totalurile coșului înainte de totalul comenzii"

#: framework/premium/features/content-blocks/hooks-manager.php:786
msgid "Cart totals after order total"
msgstr "Totalurile coșului după totalul comenzii"

#: framework/premium/features/content-blocks/hooks-manager.php:790
msgid "Proceed to checkout"
msgstr "Continuă la finalizarea comenzii"

#: framework/premium/features/content-blocks/hooks-manager.php:794
msgid "After cart totals"
msgstr "După totalurile coșului"

#: framework/premium/features/content-blocks/hooks-manager.php:798
msgid "After cart"
msgstr "După coș"

#: framework/premium/features/content-blocks/hooks-manager.php:803
msgid "Before Mini Cart"
msgstr "Înainte de Mini Coș"

#: framework/premium/features/content-blocks/hooks-manager.php:808
msgid "Before Mini Cart Contents"
msgstr "Înainte de conținutul Mini Coșului"

#: framework/premium/features/content-blocks/hooks-manager.php:813
msgid "Mini Cart Contents"
msgstr "Conținutul Mini Coșului"

#: framework/premium/features/content-blocks/hooks-manager.php:818
msgid "Widget Shopping Cart Before Buttons"
msgstr "Widget coș cumpărături înainte de butoane"

#: framework/premium/features/content-blocks/hooks-manager.php:823
msgid "Widget Shopping Cart After Buttons"
msgstr "Widget coș cumpărături după butoane"

#: framework/premium/features/content-blocks/hooks-manager.php:828
msgid "After Mini Cart"
msgstr "După Mini Coș"

#: framework/premium/features/content-blocks/hooks-manager.php:830
msgid "WooCommerce Cart"
msgstr "Coșul WooCommerce"

#: framework/premium/features/content-blocks/hooks-manager.php:836
msgid "Before checkout form"
msgstr "Înainte de formularul de finalizare"

#: framework/premium/features/content-blocks/hooks-manager.php:840
msgid "Before customer details"
msgstr "Înainte de detaliile clientului"

#: framework/premium/features/content-blocks/hooks-manager.php:844
msgid "After customer details"
msgstr "După detaliile clientului"

#: framework/premium/features/content-blocks/hooks-manager.php:848
msgid "Checkout billing"
msgstr "Facturare finalizare comandă"

#: framework/premium/features/content-blocks/hooks-manager.php:852
msgid "Before checkout billing form"
msgstr "Înainte de formularul de facturare la finalizare"

#: framework/premium/features/content-blocks/hooks-manager.php:856
msgid "After checkout billing form"
msgstr "După formularul de facturare la finalizare"

#: framework/premium/features/content-blocks/hooks-manager.php:860
msgid "Before order notes"
msgstr "Înainte de notele comenzii"

#: framework/premium/features/content-blocks/hooks-manager.php:864
msgid "After order notes"
msgstr "După notele comenzii"

#: framework/premium/features/content-blocks/hooks-manager.php:868
msgid "Checkout shipping"
msgstr "Livrare finalizare comandă"

#: framework/premium/features/content-blocks/hooks-manager.php:872
msgid "Checkout before order review"
msgstr "Înainte de revizuirea comenzii la finalizare"

#: framework/premium/features/content-blocks/hooks-manager.php:876
msgid "Checkout order review"
msgstr "Revizuirea comenzii la finalizare"

#: framework/premium/features/content-blocks/hooks-manager.php:880
msgid "Review order before cart contents"
msgstr "Revizuiește comanda înainte de conținutul coșului"

#: framework/premium/features/content-blocks/hooks-manager.php:884
msgid "Review order after cart contents"
msgstr "Revizuiește comanda după conținutul coșului"

#: framework/premium/features/content-blocks/hooks-manager.php:888
msgid "Review order before order total"
msgstr "Revizuiește comanda înainte de totalul comenzii"

#: framework/premium/features/content-blocks/hooks-manager.php:892
msgid "Review order after order total"
msgstr "Revizuiește comanda după totalul comenzii"

#: framework/premium/features/content-blocks/hooks-manager.php:896
msgid "Review order before payment"
msgstr "Revizuiește comanda înainte de plată"

#: framework/premium/features/content-blocks/hooks-manager.php:900
msgid "Review order before submit"
msgstr "Revizuiește comanda înainte de trimitere"

#: framework/premium/features/content-blocks/hooks-manager.php:904
msgid "Review order after submit"
msgstr "Revizuiește comanda după trimitere"

#: framework/premium/features/content-blocks/hooks-manager.php:908
msgid "Review order after payment"
msgstr "Revizuiește comanda după plată"

#: framework/premium/features/content-blocks/hooks-manager.php:912
msgid "Checkout after order review"
msgstr "Finalizare comandă după revizuire"

#: framework/premium/features/content-blocks/hooks-manager.php:916
msgid "After checkout form"
msgstr "După formularul de finalizare"

#: framework/premium/features/content-blocks/hooks-manager.php:919
msgid "WooCommerce Checkout"
msgstr "Finalizare WooCommerce"

#: framework/premium/features/content-blocks/hooks-manager.php:925
msgid "Before my account"
msgstr "Înainte de contul meu"

#: framework/premium/features/content-blocks/hooks-manager.php:929
msgid "Before account navigation"
msgstr "Înainte de navigarea în cont"

#: framework/premium/features/content-blocks/hooks-manager.php:933
msgid "Account navigation"
msgstr "Navigare în cont"

#: framework/premium/features/content-blocks/hooks-manager.php:937
msgid "After account navigation"
msgstr "După navigarea în cont"

#: framework/premium/features/content-blocks/hooks-manager.php:941
msgid "Account content"
msgstr "Conținutul contului"

#: framework/premium/features/content-blocks/hooks-manager.php:945
msgid "Account dashboard"
msgstr "Tablou de bord cont"

#: framework/premium/features/content-blocks/hooks-manager.php:949
msgid "After my account"
msgstr "După contul meu"

#: framework/premium/features/content-blocks/hooks-manager.php:951,
#: framework/features/header/items/account/options.php:169,
#: framework/features/header/items/account/options.php:284,
#: framework/features/header/items/account/options.php:285
msgid "WooCommerce Account"
msgstr "Cont WooCommerce"

#: framework/premium/features/content-blocks/hooks-manager.php:977
msgid "WP footer"
msgstr "Subsol WP"

#: framework/premium/features/content-blocks/admin-ui.php:266,
#: framework/premium/features/content-blocks/hooks-manager.php:978,
#: framework/premium/features/content-blocks/hooks-manager.php:986,
#: framework/premium/features/content-blocks/hooks-manager.php:994
msgid "Footer"
msgstr "Subsol"

#: framework/premium/features/content-blocks/hooks-manager.php:985
msgid "Footer before"
msgstr "Subsol înainte"

#: framework/premium/features/content-blocks/hooks-manager.php:993
msgid "Footer after"
msgstr "Subsol după"

#: framework/premium/features/content-blocks/hooks-manager.php:1009
msgid "Custom Hook (%s)"
msgstr "Hook personalizat (%s)"

#: framework/premium/features/content-blocks/hooks-manager.php:1015,
#: framework/premium/features/content-blocks/options/hook.php:116
#: framework/premium/static/js/options/MultipleLocationsSelect.js:94
msgid "After Block Number"
msgstr "După numărul blocului"

#: framework/premium/features/content-blocks/hooks-manager.php:1021,
#: framework/premium/features/content-blocks/options/hook.php:133
#: framework/premium/static/js/options/MultipleLocationsSelect.js:116
msgid "Before Heading Number"
msgstr "Înainte de numărul titlului"

#: static/js/editor/blocks/about-me/index.js:45
msgid "About Me"
msgstr "Despre mine"

#: framework/features/blocks/about-me/options.php:16
msgid "About me"
msgstr "Despre mine"

#: framework/features/blocks/about-me/options.php:49,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:31,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:729,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:68,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:99,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:143,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:166,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:166
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:96
msgid "Image"
msgstr "Imagine"

#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:126
msgid "Upload Image"
msgstr "Încarcă imagine"

#: framework/features/blocks/about-me/options.php:53,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:96,
#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:106,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:809,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:819,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:72
msgid "Select Image"
msgstr "Selectează imagine"

#: framework/features/blocks/about-me/options.php:54
msgid "Change Image"
msgstr "Schimbă imagine"

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:46,
#: framework/premium/extensions/woocommerce-extra/features/checkout/options.php:20,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:123,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:112,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:65
msgid "Image Ratio"
msgstr "Raport imagine"

#: framework/features/blocks/contact-info/options.php:520,
#: framework/premium/extensions/shortcuts/customizer.php:307
msgid "Open link in new tab"
msgstr "Deschide linkul într-o filă nouă"

#: framework/features/blocks/contact-info/options.php:35
#: static/js/editor/blocks/contact-info/index.js:54
msgid "Contact Info"
msgstr "Informații de contact"

#: framework/features/blocks/contact-info/options.php:51,
#: framework/features/blocks/contact-info/options.php:81,
#: framework/features/blocks/contact-info/view.php:15,
#: framework/premium/features/premium-header/items/contacts/options.php:57,
#: framework/premium/features/premium-header/items/contacts/options.php:88,
#: framework/premium/features/premium-header/items/contacts/view.php:35
msgid "Address:"
msgstr "Adresă:"

#: framework/features/blocks/contact-info/options.php:59,
#: framework/features/blocks/contact-info/options.php:146,
#: framework/features/blocks/contact-info/view.php:23,
#: framework/premium/features/premium-header/items/contacts/options.php:65,
#: framework/premium/features/premium-header/items/contacts/options.php:133,
#: framework/premium/features/premium-header/items/contacts/view.php:43
msgid "Phone:"
msgstr "Telefon:"

#: framework/features/blocks/contact-info/options.php:67,
#: framework/features/blocks/contact-info/options.php:209,
#: framework/features/blocks/contact-info/view.php:31,
#: framework/premium/features/premium-header/items/contacts/options.php:73,
#: framework/premium/features/premium-header/items/contacts/options.php:178,
#: framework/premium/features/premium-header/items/contacts/view.php:51
msgid "Mobile:"
msgstr "Mobil:"

#: framework/features/blocks/contact-info/options.php:75,
#: framework/premium/features/premium-header/items/contacts/options.php:82
msgid "Address"
msgstr "Adresă"

#: framework/features/blocks/contact-info/options.php:94,
#: framework/features/blocks/contact-info/options.php:159,
#: framework/features/blocks/contact-info/options.php:222,
#: framework/features/blocks/contact-info/options.php:285,
#: framework/features/blocks/contact-info/options.php:348,
#: framework/features/blocks/contact-info/options.php:411,
#: framework/features/blocks/contact-info/options.php:474,
#: framework/premium/features/premium-header/items/contacts/options.php:107,
#: framework/premium/features/premium-header/items/contacts/options.php:152,
#: framework/premium/features/premium-header/items/contacts/options.php:197,
#: framework/premium/features/premium-header/items/contacts/options.php:242,
#: framework/premium/features/premium-header/items/contacts/options.php:288,
#: framework/premium/features/premium-header/items/contacts/options.php:333,
#: framework/premium/features/premium-header/items/contacts/options.php:378
msgid "Link (optional)"
msgstr "Link (opțional)"

#: framework/features/blocks/contact-info/options.php:265,
#: framework/premium/features/premium-header/items/contacts/options.php:217
msgid "Work Hours"
msgstr "Program lucru"

#: framework/features/blocks/contact-info/options.php:272,
#: framework/premium/features/premium-header/items/contacts/options.php:223
msgid "Opening hours"
msgstr "Ore de funcționare"

#: framework/features/blocks/contact-info/options.php:328,
#: framework/premium/features/premium-header/items/contacts/options.php:263
msgid "Fax"
msgstr "Fax"

#: framework/features/blocks/contact-info/options.php:335,
#: framework/premium/features/premium-header/items/contacts/options.php:269
msgid "Fax:"
msgstr "Fax:"

#: framework/features/blocks/contact-info/options.php:398,
#: framework/premium/features/premium-header/items/contacts/options.php:314
msgid "Email:"
msgstr "E-mail:"

#: framework/features/blocks/contact-info/options.php:454,
#: framework/premium/features/premium-header/items/contacts/options.php:353
msgid "Website"
msgstr "Site web"

#: framework/features/blocks/contact-info/options.php:461,
#: framework/premium/features/premium-header/items/contacts/options.php:359
msgid "Website:"
msgstr "Site web:"

#: framework/features/blocks/about-me/options.php:85,
#: framework/premium/features/content-blocks/options/archive.php:73
msgid "Small"
msgstr "Mic"

#: framework/features/blocks/about-me/options.php:87
msgid "Large"
msgstr "Mare"

#: framework/features/blocks/about-me/options.php:200,
#: framework/features/blocks/contact-info/options.php:557,
#: framework/features/blocks/share-box/options.php:154,
#: framework/features/blocks/socials/options.php:106,
#: framework/premium/features/premium-header/items/contacts/options.php:445
msgid "Icons Shape Type"
msgstr "Tipul formei pictogramelor"

#: framework/features/blocks/about-me/options.php:97,
#: framework/features/blocks/about-me/options.php:205,
#: framework/features/blocks/contact-info/options.php:565,
#: framework/features/blocks/share-box/options.php:161,
#: framework/features/blocks/socials/options.php:113,
#: framework/premium/features/premium-header/items/contacts/options.php:452
msgid "Rounded"
msgstr "Rotunjit"

#: framework/features/blocks/about-me/options.php:98,
#: framework/features/blocks/about-me/options.php:206,
#: framework/features/blocks/contact-info/options.php:566,
#: framework/features/blocks/share-box/options.php:162,
#: framework/features/blocks/socials/options.php:114,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:27,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:136,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:190,
#: framework/premium/features/premium-header/items/contacts/options.php:453
#: static/js/editor/blocks/dynamic-data/components/Dimensions.js:86
msgid "Square"
msgstr "Pătrat"

#: framework/features/blocks/about-me/options.php:215,
#: framework/features/blocks/contact-info/options.php:575,
#: framework/features/blocks/share-box/options.php:171,
#: framework/features/blocks/socials/options.php:123,
#: framework/premium/features/premium-header/items/contacts/options.php:464
msgid "Shape Fill Type"
msgstr "Tipul umplerii formei"

#: framework/features/blocks/about-me/options.php:220,
#: framework/features/blocks/contact-info/options.php:582,
#: framework/features/blocks/share-box/options.php:178,
#: framework/features/blocks/socials/options.php:130,
#: framework/premium/features/premium-header/items/contacts/options.php:472
msgid "Solid"
msgstr "Solid"

#: framework/features/blocks/about-me/options.php:219,
#: framework/features/blocks/contact-info/options.php:581,
#: framework/features/blocks/share-box/options.php:177,
#: framework/features/blocks/socials/options.php:129,
#: framework/premium/features/premium-header/items/contacts/options.php:471
msgid "Outline"
msgstr "Contur"

#: framework/extensions/trending/customizer.php:273,
#: framework/extensions/trending/customizer.php:287
#: static/js/editor/blocks/query/Edit.js:169
msgid "Post Type"
msgstr "Tip de articol"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:149
msgid "Most commented"
msgstr "Cele mai comentate"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:165
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:144
msgid "Random"
msgstr "Aleatoriu"

#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:117
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:112
msgid "Order by"
msgstr "Ordonează după"

#: framework/features/blocks/dynamic-data/options.php:109,
#: framework/features/blocks/dynamic-data/views/wp-field.php:196
msgid "% comments"
msgstr "% comentarii"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:80
msgid "Author Avatar"
msgstr "Avatar autor"

#: framework/features/blocks/socials/options.php:15
msgid "Social Icons"
msgstr "Pictograme rețele sociale"

#: framework/features/blocks/about-me/options.php:124
msgid "You can configure social URLs in %s."
msgstr "Poți configura URL-urile sociale în %s."

#: framework/features/blocks/about-me/options.php:156,
#: framework/features/blocks/socials/options.php:62,
#: framework/premium/features/premium-header/items/contacts/options.php:405
msgid "Open links in new tab"
msgstr "Deschide linkurile într-o filă nouă"

#: framework/features/blocks/about-me/options.php:163,
#: framework/features/blocks/contact-info/options.php:527,
#: framework/features/blocks/share-box/options.php:117,
#: framework/features/blocks/socials/options.php:69,
#: framework/premium/features/premium-header/items/contacts/options.php:411
msgid "Set links to nofollow"
msgstr "Setează linkurile ca nofollow"

#: framework/features/header/items/account/options.php:4
msgid "Profile Page"
msgstr "Pagina de profil"

#: framework/features/header/items/account/options.php:5
msgid "Dashboard Page"
msgstr "Pagina tabloului de bord"

#: framework/features/header/items/account/options.php:7,
#: framework/features/header/items/account/options.php:36,
#: framework/features/header/items/account/options.php:117,
#: framework/features/header/items/account/options.php:280,
#: framework/features/header/items/account/views/login.php:396
msgid "Custom Link"
msgstr "Legătură personalizată"

#: framework/features/header/items/account/options.php:6
msgid "Logout"
msgstr "Dezautentificare"

#: framework/features/header/items/account/options.php:279
msgid "Modal"
msgstr "Fereastră modală"

#: framework/features/header/items/account/options.php:295
msgid "Customizing: Logged in State"
msgstr "Personalizare: Stare conectat"

#: framework/features/header/items/account/options.php:319
msgid "Logged In Options"
msgstr "Opțiuni conectat"

#: framework/features/header/items/account/options.php:324
msgid "Logged Out Options"
msgstr "Opțiuni deconectat"

#: framework/features/header/items/account/options.php:611,
#: framework/features/header/items/account/options.php:1059
msgid "Account Action"
msgstr "Acțiune cont"

#: framework/features/header/items/account/options.php:145
msgid "Select Menu"
msgstr "Selectează meniul"

#: framework/features/header/items/account/options.php:151
msgid "Select menu..."
msgstr "Selectează meniul..."

#. translators: placeholder here means the actual URL.
#: framework/features/header/items/account/options.php:155
msgid "Manage your menus in the %sMenus screen%s."
msgstr "Gestionează meniurile tale în ecranul %sMeniuri%s."

#: framework/features/header/items/account/options.php:702,
#: framework/features/header/items/account/options.php:1073
msgid "Custom Page Link"
msgstr "Link pagină personalizată"

#: framework/features/header/items/account/options.php:340,
#: framework/features/header/items/account/options.php:739
msgid "Account Image"
msgstr "Imagine cont"

#: framework/features/header/items/account/options.php:346
msgid "Avatar"
msgstr "Avatar"

#: framework/features/header/items/account/options.php:357
msgid "Avatar Size"
msgstr "Dimensiune avatar"

#: framework/features/header/items/account/options.php:487,
#: framework/features/header/items/account/options.php:861,
#: framework/features/header/items/account/options.php:949
msgid "Type 5"
msgstr "Tip 5"

#: framework/features/header/items/account/options.php:497,
#: framework/features/header/items/account/options.php:871,
#: framework/features/header/items/account/options.php:959
msgid "Type 6"
msgstr "Tip 6"

#: framework/features/header/items/account/options.php:570
msgid "Label Type"
msgstr "Tip etichetă"

#: framework/features/header/items/account/options.php:587,
#: framework/features/header/items/account/options.php:1045,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:136,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:137
msgid "Label Text"
msgstr "Text etichetă"

#: framework/features/header/items/account/options.php:173,
#: framework/features/header/items/account/options.php:597,
#: framework/features/header/items/account/views/login.php:100,
#: framework/features/header/items/account/views/login.php:472
msgid "My Account"
msgstr "Contul meu"

#: framework/features/header/items/account/options.php:1125,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:163,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:167,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:170
msgid "Label Font"
msgstr "Font etichetă"

#: framework/features/header/items/account/options.php:1135,
#: framework/features/header/items/account/options.php:1174,
#: framework/features/header/items/account/options.php:1218,
#: framework/features/header/items/account/options.php:1260
msgid "Label Color"
msgstr "Culoare etichetă"

#: framework/features/header/header-options.php:214,
#: framework/features/header/items/account/options.php:1140,
#: framework/features/header/items/account/options.php:1332,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:179,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:305,
#: framework/premium/features/premium-header/items/contacts/options.php:590,
#: framework/premium/features/premium-header/items/contacts/options.php:756,
#: framework/premium/features/premium-header/items/contacts/options.php:901,
#: framework/premium/features/premium-header/items/divider/options.php:27,
#: framework/premium/features/premium-header/items/search-input/options.php:271,
#: framework/premium/features/premium-header/items/search-input/options.php:401,
#: framework/premium/features/premium-header/items/search-input/options.php:531,
#: framework/premium/features/premium-header/items/search-input/options.php:667,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:227,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:358,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:57,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:183,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:359,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:492,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:186,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:313,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:434
msgid "Default State"
msgstr "Stare implicită"

#: framework/features/header/header-options.php:219,
#: framework/features/header/items/account/options.php:1148,
#: framework/features/header/items/account/options.php:1340,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:184,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:310,
#: framework/premium/features/premium-header/items/contacts/options.php:595,
#: framework/premium/features/premium-header/items/contacts/options.php:761,
#: framework/premium/features/premium-header/items/contacts/options.php:909,
#: framework/premium/features/premium-header/items/divider/options.php:32,
#: framework/premium/features/premium-header/items/search-input/options.php:276,
#: framework/premium/features/premium-header/items/search-input/options.php:406,
#: framework/premium/features/premium-header/items/search-input/options.php:536,
#: framework/premium/features/premium-header/items/search-input/options.php:672,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:232,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:363,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:62,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:191,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:364,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:500,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:191,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:318,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:442
msgid "Transparent State"
msgstr "Stare transparentă"

#: framework/features/header/header-options.php:227,
#: framework/features/header/items/account/options.php:1161,
#: framework/features/header/items/account/options.php:1353,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:193,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:319,
#: framework/premium/features/premium-header/items/contacts/options.php:604,
#: framework/premium/features/premium-header/items/contacts/options.php:770,
#: framework/premium/features/premium-header/items/contacts/options.php:919,
#: framework/premium/features/premium-header/items/divider/options.php:41,
#: framework/premium/features/premium-header/items/search-input/options.php:285,
#: framework/premium/features/premium-header/items/search-input/options.php:415,
#: framework/premium/features/premium-header/items/search-input/options.php:545,
#: framework/premium/features/premium-header/items/search-input/options.php:681,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:241,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:372,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:71,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:204,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:373,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:510,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:200,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:327,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:452
msgid "Sticky State"
msgstr "Stare fixă"

#: framework/features/header/items/account/options.php:1499
msgid "Item Margin"
msgstr "Margine element"

#: framework/features/header/items/account/options.php:1514
msgid "Modal Options"
msgstr "Opțiuni fereastră modală"

#: framework/features/header/items/account/options.php:1705,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:451
msgid "Modal Shadow"
msgstr "Umbră fereastră modală"

#: framework/features/header/items/account/options.php:1677,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:422
msgid "Modal Background"
msgstr "Fundal fereastră modală"

#: framework/features/header/items/account/options.php:1691,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:437
msgid "Modal Backdrop"
msgstr "Fundal secundar fereastră modală"

#: framework/features/blocks/contact-info/options.php:13,
#: framework/features/header/items/account/options.php:2059,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:441,
#: framework/premium/features/premium-header/items/contacts/options.php:486,
#: framework/premium/features/premium-header/items/contacts/options.php:541,
#: framework/premium/features/premium-header/items/language-switcher/options.php:120,
#: framework/premium/features/premium-header/items/language-switcher/options.php:251,
#: framework/premium/features/premium-header/items/search-input/options.php:239,
#: framework/premium/features/premium-header/items/search-input/options.php:936,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:644,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:578
msgid "Element Visibility"
msgstr "Vizibilitate element"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:655
msgid "You have no %s fields declared for this custom post type."
msgstr "Nu ai câmpuri %s declarate pentru acest tip de articol personalizat."

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:667,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1401,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1412
msgid "Field"
msgstr "Câmp"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:679,
#: framework/premium/features/premium-header/items/language-switcher/options/common.php:24
msgid "Label"
msgstr "Etichetă"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:728
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:319
msgid "Fallback"
msgstr "Alternativă"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:755
msgid "%s Field"
msgstr "Câmp %s"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1399
msgid "%s %s Font"
msgstr "Font %s %s"

#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1410
msgid "%s %s Color"
msgstr "Culoare %s %s"

#: framework/helpers/exts-configs.php:142,
#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:11,
#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:68
msgid "Read Time"
msgstr "Timp de citire"

#: framework/premium/extensions/post-types-extra/features/filtering/helpers.php:191,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:195,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:842
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:65
msgid "All"
msgstr "Toate"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:92,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:803
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:75
msgid "Featured Image"
msgstr "Imagine reprezentativă"

#: framework/premium/extensions/post-types-extra/features/taxonomies-customization/feature.php:110
msgid "Accent Color"
msgstr "Culoare de accent"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/helpers.php:150,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/helpers.php:209
msgid "Add to wishlist"
msgstr "Adaugă în lista de dorințe"

#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:9
msgid "Select a page"
msgstr "Selectează o pagină"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:23
msgid "Show Wishlist Page To"
msgstr "Afișează pagina listei de dorințe pentru"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:29
msgid "Logged Users"
msgstr "Utilizatori conectați"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:30
msgid "All Users"
msgstr "Toți utilizatorii"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:40
msgid "Wishlist Page"
msgstr "Pagina listei de dorințe"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/options.php:46
msgid "The page you select here will display the wish list for your logged out users."
msgstr "Pagina selectată aici va afișa lista de dorințe pentru utilizatorii neconectați."

#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:25
msgid "Archive Page"
msgstr "Archive Page"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:227,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:240,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:276,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:273,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:303,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:332,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:405,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:435,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:464,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:103,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:133,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:162
msgid "Hover/Active"
msgstr "La trecerea mouse-ului/Activ"

#: framework/premium/extensions/woocommerce-extra/features/wish-list/views/table-no-results.php:6
msgid "You don't have any products in your wish list yet."
msgstr "Nu ai încă produse în lista de dorințe."

#: framework/premium/extensions/woocommerce-extra/features/archive-card/feature.php:56,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:54,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:82
msgid "Add to cart"
msgstr "Adaugă în coș"

#: framework/premium/extensions/woocommerce-extra/features/compare/views/bar.php:101,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:142,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:145,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/views/columns/product-remove-button.php:12
msgid "Remove Product"
msgstr "Elimină produs"

#: framework/premium/extensions/woocommerce-extra/includes/woo-import-export.php:36
msgid "Blocksy Variation Images"
msgstr "Imagini variație Blocksy"

#: framework/premium/features/content-blocks/options/404.php:33,
#: framework/premium/features/content-blocks/options/header.php:55,
#: framework/premium/features/content-blocks/options/hook.php:159,
#: framework/premium/features/content-blocks/options/maintenance.php:30,
#: framework/premium/features/content-blocks/options/nothing_found.php:51,
#: framework/premium/features/content-blocks/options/popup.php:41
msgid "Container Structure"
msgstr "Structură container"

#: framework/premium/features/content-blocks/options/404.php:65,
#: framework/premium/features/content-blocks/options/archive.php:132,
#: framework/premium/features/content-blocks/options/header.php:83,
#: framework/premium/features/content-blocks/options/hook.php:192,
#: framework/premium/features/content-blocks/options/maintenance.php:62,
#: framework/premium/features/content-blocks/options/nothing_found.php:83,
#: framework/premium/features/content-blocks/options/single.php:64
msgid "Narrow Width"
msgstr "Lățime îngustă"

#: framework/premium/features/content-blocks/options/404.php:70,
#: framework/premium/features/content-blocks/options/archive.php:137,
#: framework/premium/features/content-blocks/options/header.php:88,
#: framework/premium/features/content-blocks/options/hook.php:197,
#: framework/premium/features/content-blocks/options/maintenance.php:67,
#: framework/premium/features/content-blocks/options/nothing_found.php:88,
#: framework/premium/features/content-blocks/options/single.php:69
msgid "Normal Width"
msgstr "Lățime normală"

#: framework/premium/features/content-blocks/options/404.php:76,
#: framework/premium/features/content-blocks/options/archive.php:153,
#: framework/premium/features/content-blocks/options/header.php:94,
#: framework/premium/features/content-blocks/options/hook.php:203,
#: framework/premium/features/content-blocks/options/maintenance.php:73,
#: framework/premium/features/content-blocks/options/nothing_found.php:94,
#: framework/premium/features/content-blocks/options/single.php:85
msgid "Content Area Style"
msgstr "Stil zonă de conținut"

#: framework/premium/features/content-blocks/options/404.php:90,
#: framework/premium/features/content-blocks/options/archive.php:167,
#: framework/premium/features/content-blocks/options/header.php:108,
#: framework/premium/features/content-blocks/options/hook.php:217,
#: framework/premium/features/content-blocks/options/maintenance.php:87,
#: framework/premium/features/content-blocks/options/nothing_found.php:108
msgid "Content Area Vertical Spacing"
msgstr "Distanțare verticală zonă de conținut"

#: framework/premium/features/content-blocks/options/404.php:102,
#: framework/premium/features/content-blocks/options/archive.php:179,
#: framework/premium/features/content-blocks/options/header.php:120,
#: framework/premium/features/content-blocks/options/hook.php:229,
#: framework/premium/features/content-blocks/options/maintenance.php:99,
#: framework/premium/features/content-blocks/options/nothing_found.php:120,
#: framework/premium/features/content-blocks/options/single.php:111
msgid "Top & Bottom"
msgstr "Sus și jos"

#: framework/premium/features/content-blocks/options/404.php:105,
#: framework/premium/features/content-blocks/options/archive.php:182,
#: framework/premium/features/content-blocks/options/header.php:123,
#: framework/premium/features/content-blocks/options/hook.php:232,
#: framework/premium/features/content-blocks/options/maintenance.php:102,
#: framework/premium/features/content-blocks/options/nothing_found.php:123,
#: framework/premium/features/content-blocks/options/single.php:114
msgid "Only Top"
msgstr "Doar sus"

#: framework/premium/features/content-blocks/options/404.php:108,
#: framework/premium/features/content-blocks/options/archive.php:185,
#: framework/premium/features/content-blocks/options/header.php:126,
#: framework/premium/features/content-blocks/options/hook.php:235,
#: framework/premium/features/content-blocks/options/maintenance.php:105,
#: framework/premium/features/content-blocks/options/nothing_found.php:126,
#: framework/premium/features/content-blocks/options/single.php:117
msgid "Only Bottom"
msgstr "Doar jos"

#: framework/premium/features/content-blocks/options/hook.php:60
msgid "Location & Priority"
msgstr "Locație și prioritate"

#: framework/premium/features/content-blocks/options/hook.php:23,
#: framework/premium/features/content-blocks/options/hook.php:100
#: framework/premium/static/js/options/MultipleLocationsSelect.js:76
msgid "Custom Hook"
msgstr "Hook personalizat"

#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:730
msgid "Mixed"
msgstr "Mixed"

#: framework/premium/features/content-blocks/options/popup.php:438,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:628
msgid "Popup Position"
msgstr "Poziție pop-up"

#: framework/premium/features/content-blocks/options/popup.php:384,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:573
msgid "Popup Size"
msgstr "Mărime pop-up"

#: framework/premium/features/content-blocks/options/popup.php:390,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:580
msgid "Small Size"
msgstr "Mărime mică"

#: framework/premium/features/content-blocks/options/popup.php:391,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:581
msgid "Medium Size"
msgstr "Mărime medie"

#: framework/premium/features/content-blocks/options/popup.php:392,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:582
msgid "Large Size"
msgstr "Mărime mare"

#: framework/premium/features/content-blocks/options/popup.php:393,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:583
msgid "Custom Size"
msgstr "Mărime personalizată"

#: framework/premium/features/content-blocks/options/popup.php:403,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:593
msgid "Max Width"
msgstr "Lățime maximă"

#: framework/premium/features/content-blocks/options/popup.php:419,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:609
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:510
msgid "Max Height"
msgstr "Înălțime maximă"

#: framework/premium/features/content-blocks/options/popup.php:339,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:525
msgid "Popup Animation"
msgstr "Animație pop-up"

#: framework/premium/features/content-blocks/options/popup.php:345,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:531
msgid "Fade in fade out"
msgstr "Apariție-dispariție graduală"

#: framework/premium/features/content-blocks/options/popup.php:346,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:532
msgid "Zoom in zoom out"
msgstr "Zoom intrare-ieșire"

#: framework/premium/features/content-blocks/options/popup.php:347,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:533
msgid "Slide in from left"
msgstr "Glisare din stânga"

#: framework/premium/features/content-blocks/options/popup.php:348,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:534
msgid "Slide in from right"
msgstr "Glisare din dreapta"

#: framework/premium/features/content-blocks/options/popup.php:349,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:535
msgid "Slide in from top"
msgstr "Glisare din partea de sus"

#: framework/premium/features/content-blocks/options/popup.php:350,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:536
msgid "Slide in from bottom"
msgstr "Glisare din partea de jos"

#: framework/premium/features/content-blocks/options/popup.php:355,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:542
msgid "Animation Speed"
msgstr "Viteză animație"

#: framework/premium/features/content-blocks/options/popup.php:372,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:560
msgid "Entrance Value"
msgstr "Valoare intrare"

#: framework/premium/features/content-blocks/options/popup.php:69
msgid "Trigger Condition"
msgstr "Condiție declanșator"

#: framework/premium/features/content-blocks/options/popup.php:93,
#: framework/premium/features/content-blocks/options/popup.php:111
msgid "Element Class"
msgstr "Clasa elementului"

#: framework/premium/features/content-blocks/options/popup.php:99,
#: framework/premium/features/content-blocks/options/popup.php:117
msgid "Separate each class by comma if you have multiple elements."
msgstr "Separă fiecare clasă prin virgulă dacă ai mai multe elemente."

#: framework/premium/features/content-blocks/options/popup.php:129
msgid "Scroll Direction"
msgstr "Direcție de derulare"

#: framework/premium/features/content-blocks/options/popup.php:134
msgid "Scroll Down"
msgstr "Derulează în jos"

#: framework/premium/features/content-blocks/options/popup.php:135
msgid "Scroll Up"
msgstr "Derulează în sus"

#: framework/premium/features/content-blocks/options/popup.php:140
msgid "Scroll Distance"
msgstr "Distanță de derulare"

#: framework/premium/features/content-blocks/options/popup.php:149
msgid "Set the scroll distance till the popup block will appear."
msgstr "Setează distanța de derulare până când blocul pop-up va apărea."

#: framework/premium/features/content-blocks/options/popup.php:167
msgid "Inactivity Time"
msgstr "Timp de inactivitate"

#: framework/premium/features/content-blocks/options/popup.php:173
msgid "Set the inactivity time (in seconds) till the popup block will appear."
msgstr "Setează timpul de inactivitate (în secunde) până când blocul pop-up va apărea."

#: framework/premium/features/content-blocks/options/popup.php:185
msgid "After X Time"
msgstr "După X timp"

#: framework/premium/features/content-blocks/options/popup.php:191
msgid "Set after how much time (in seconds) the popup block will appear."
msgstr "Setează după cât timp (în secunde) va apărea blocul pop-up."

#: framework/premium/features/content-blocks/options/popup.php:522,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:676
msgid "Padding"
msgstr "Spațiere interioară"

#: framework/features/header/items/account/options.php:2034,
#: framework/premium/features/content-blocks/options/popup.php:533,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:298,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:688,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:455,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:330,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:194
msgid "Border Radius"
msgstr "Raza marginii"

#: framework/premium/features/content-blocks/options/popup.php:563,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:666
msgid "Popup Offset"
msgstr "Decalaj pop-up"

#: framework/premium/features/content-blocks/options/popup.php:581
msgid "Container Overflow"
msgstr "Depășire conținut container"

#: framework/premium/features/content-blocks/options/popup.php:588
msgid "Hidden"
msgstr "Ascuns"

#: framework/premium/features/content-blocks/options/popup.php:589
msgid "Visible"
msgstr "Vizibil"

#: framework/premium/features/content-blocks/options/popup.php:590
msgid "Scroll"
msgstr "Derulare"

#: framework/premium/features/content-blocks/options/popup.php:592
msgid "Control what happens to the content that is too big to fit into the popup."
msgstr "Controlează ce se întâmplă cu conținutul care este prea mare pentru a încăpea în pop-up."

#: framework/premium/features/content-blocks/options/popup.php:596
msgid "Close Button"
msgstr "Buton închidere"

#: framework/premium/features/content-blocks/options/popup.php:604
msgid "Inside"
msgstr "Înăuntru"

#: framework/premium/features/content-blocks/options/popup.php:605
msgid "Outside"
msgstr "Afară"

#: framework/premium/features/content-blocks/options/popup.php:679,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:720,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:299,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:72
msgid "Popup Background"
msgstr "Fundal pop-up"

#: framework/premium/features/content-blocks/options/popup.php:693
msgid "Popup Backdrop Background"
msgstr "Fundal secundar pop-up"

#: framework/helpers/exts-configs.php:156,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:25
msgid "Posts Filter"
msgstr "Filtru postări"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:59
msgid "Filtering Behavior"
msgstr "Comportament de filtrare"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:67
msgid "Instant Reload"
msgstr "Reîncărcare instantanee"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:68
msgid "Page Reload"
msgstr "Reîncărcare pagină"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:103
msgid "Items Horizontal Spacing"
msgstr "Distanțare orizontală elemente"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:115,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:79
msgid "Items Vertical Spacing"
msgstr "Distanțare verticală elemente"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:127
msgid "Container Bottom Spacing"
msgstr "Distanțare jos container"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:139,
#: framework/premium/features/premium-header/items/contacts/options.php:510,
#: framework/premium/features/premium-header/items/language-switcher/options.php:90,
#: framework/premium/features/premium-header/items/search-input/options.php:207
msgid "Horizontal Alignment"
msgstr "Aliniere orizontală"

#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:284
msgid "Button Padding"
msgstr "Spațiere interioară buton"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:4
msgid "Read Progress"
msgstr "Progres citire"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:24
msgid "Indicator Height"
msgstr "Înălțime indicator"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:34
msgid "Auto Hide"
msgstr "Ascundere automată"

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:38
msgid "Automatically hide the read progress bar once you arrive at the bottom of the article."
msgstr "Ascunde automat bara de progres a citirii odată ce ai ajuns la sfârșitul articolului."

#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:71
msgid "Main Color"
msgstr "Culoare principală"

#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:83,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:84
msgid "Icon Badge"
msgstr "Insignă pictogramă"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:174,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:178,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:181
msgid "Label Font Color"
msgstr "Culoare font etichetă"

#: framework/premium/features/premium-header/items/contacts/config.php:4
msgid "Contacts"
msgstr "Contacte"

#: framework/features/blocks/about-me/options.php:178,
#: framework/features/blocks/contact-info/options.php:548,
#: framework/premium/features/premium-header/items/contacts/options.php:435,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:16
msgid "Items Spacing"
msgstr "Spațiere elemente"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:420,
#: framework/premium/features/premium-header/items/contacts/options.php:1051,
#: framework/premium/features/premium-header/items/content-block/options.php:40,
#: framework/premium/features/premium-header/items/divider/options.php:107,
#: framework/premium/features/premium-header/items/divider/options.php:125,
#: framework/premium/features/premium-header/items/search-input/options.php:804,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:478,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:172,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:625,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:557
msgid "Margin"
msgstr "Margine"

#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:106,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/view.php:45
msgid "Dark Mode"
msgstr "Mod întunecat"

#: framework/features/header/items/account/options.php:68,
#: framework/features/header/items/account/options.php:1999,
#: framework/premium/features/premium-header/items/divider/config.php:4
msgid "Divider"
msgstr "Despărțitor"

#: framework/premium/features/premium-header/items/divider/options.php:6
msgid "Size"
msgstr "Mărime"

#: framework/features/conditions/rules/localization.php:11,
#: framework/premium/features/premium-header/items/language-switcher/config.php:14
msgid "Languages"
msgstr "Limbi"

#: framework/premium/features/premium-header/items/language-switcher/options.php:274
msgid "Top Level Options"
msgstr "Opțiuni de nivel superior"

#: framework/features/header/items/account/options.php:618,
#: framework/premium/features/premium-header/items/language-switcher/options.php:170
msgid "Dropdown"
msgstr "Meniu derulant"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:23
msgid "Flag"
msgstr "Steag"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:39
msgid "Label Style"
msgstr "Stil etichetă"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:46
msgid "Long"
msgstr "Lung"

#: framework/premium/features/premium-header/items/language-switcher/options/common.php:47
msgid "Short"
msgstr "Scurt"

#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:26
msgid "Hide Current Language"
msgstr "Ascunde limba actuală"

#: framework/features/header/items/account/options.php:1890,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:59
msgid "Dropdown Options"
msgstr "Opțiuni meniu derulant"

#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:70
msgid "Dropdown Top Offset"
msgstr "Decalaj sus meniu derulant"

#: framework/premium/features/premium-header/items/search-input/config.php:4
msgid "Search Box"
msgstr "Casetă căutare"

#: framework/features/blocks/search/options.php:46,
#: framework/features/blocks/search/options.php:52,
#: framework/features/blocks/search/options.php:64,
#: framework/features/blocks/search/options.php:145,
#: framework/premium/features/premium-header/items/search-input/options.php:45
msgid "Placeholder Text"
msgstr "Text substituent"

#: framework/premium/features/premium-header/items/search-input/options.php:55
msgid "Input Maximum Width"
msgstr "Lățime maximă câmp"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:87,
#: framework/features/blocks/search/options.php:70,
#: framework/premium/features/premium-header/items/search-input/options.php:67
msgid "Input Height"
msgstr "Înălțime câmp"

#: framework/features/blocks/search/options.php:97,
#: framework/premium/features/premium-header/items/search-input/options.php:88
msgid "Live Results"
msgstr "Rezultate în timp real"

#: framework/features/blocks/search/options.php:109,
#: framework/premium/features/premium-header/items/search-input/options.php:100
msgid "Live Results Images"
msgstr "Imagini rezultate în timp real"

#: framework/features/blocks/search/options.php:185,
#: framework/premium/features/premium-header/items/search-input/options.php:175
msgid "Search Through Criteria"
msgstr "Caută prin criterii"

#: framework/features/blocks/search/options.php:187,
#: framework/premium/features/premium-header/items/search-input/options.php:176
msgid "Chose in which post types do you want to perform searches."
msgstr "Alege în ce tipuri de articole dorești să efectuezi căutări."

#: framework/premium/features/premium-header/items/search-input/options.php:396,
#: framework/premium/features/premium-header/items/search-input/options.php:425,
#: framework/premium/features/premium-header/items/search-input/options.php:457,
#: framework/premium/features/premium-header/items/search-input/options.php:487
msgid "Input Icon Color"
msgstr "Culoare pictogramă câmp"

#: framework/premium/features/premium-header/items/search-input/options.php:833
#: static/js/editor/blocks/search/Edit.js:346
msgid "Dropdown Text Color"
msgstr "Culoare text meniu derulant"

#: framework/premium/features/premium-header/items/search-input/options.php:864
msgid "Dropdown Background"
msgstr "Fundal meniu derulant"

#: framework/premium/features/premium-header/items/widget-area-1/config.php:4
msgid "Widget Area"
msgstr "Zonă widget"

#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker.js:14
#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker/Implementation.js:72
#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:362
#: static/js/dashboard/NoTheme.js:64 static/js/dashboard/VersionMismatch.js:61
#: static/js/dashboard/screens/SiteExport.js:310
#: static/js/notifications/VersionMismatchNotice.js:73
msgid "Loading..."
msgstr "Încărcare…"

#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker/Implementation.js:73
msgid "Invalid API Key..."
msgstr "Cheie API invalidă..."

#: framework/extensions/newsletter-subscribe/admin-static/js/ListPicker/Implementation.js:101
#: framework/extensions/newsletter-subscribe/dashboard-static/js/ListPicker.js:119
msgid "Select list..."
msgstr "Selectează lista..."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:218
msgid "More information on how to generate an API key for Mailchimp can be found %shere%s."
msgstr "Mai multe informații despre cum să generezi o cheie API pentru Mailchimp pot fi găsite %shere%s."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:286
msgid "More information on how to generate an API key for ConvertKit can be found %shere%s."
msgstr "Mai multe informații despre cum să generezi o cheie API pentru ConvertKit pot fi găsite %shere%s."

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:184
msgid "API Key"
msgstr "Cheie API"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:363
#: framework/extensions/product-reviews/static/js/ProductReviews.js:94
#: framework/premium/extensions/mega-menu/static/js/SettingsManager.js:280
#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:111
#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:910
#: framework/premium/static/js/footer/EditConditions.js:143
#: framework/premium/static/js/media-video/components/EditVideoMeta.js:106
msgid "Save Settings"
msgstr "Salvează setările"

#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:153
msgid "Pick Mailing Service"
msgstr "Alege serviciul de corespondență"

#: framework/extensions/product-reviews/static/js/ProductReviews.js:22
msgid "Product Reviews Settings"
msgstr "Setări recenzii produse"

#: framework/extensions/product-reviews/static/js/ProductReviews.js:25
msgid "Configure the slugs for single and category pages of the product review custom post type."
msgstr "Configurează slugs pentru paginile individuale și categoriile tipului personalizat de articole de recenzie a produselor."

#: framework/extensions/product-reviews/static/js/ProductReviews.js:43
msgid "Single Slug"
msgstr "Slug individual"

#: framework/extensions/product-reviews/static/js/ProductReviews.js:49
msgid "Category Slug"
msgstr "Slug categorie"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:84
msgid "Adobe Fonts Settings"
msgstr "Setări Adobe Fonts"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:116
msgid "Project ID"
msgstr "ID proiect"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:158
msgid "Fetch Fonts"
msgstr "Preia fonturi"

#: framework/premium/extensions/adobe-typekit/dashboard-static/js/AdobeTypekit.js:182
#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:54
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:39
msgid "Variations"
msgstr "Variații"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/CustomFonts.js:74
msgid "Custom Fonts Settings"
msgstr "Setări font-uri personalizate"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/CustomFonts.js:76
msgid "Here you can see all your custom fonts that can be used in all typography options across the theme."
msgstr "Aici poți vedea toate fonturile personalizate care pot fi utilizate în toate opțiunile de tipografie din temă."

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:28
msgid "Dynamic Font"
msgstr "Font dinamic"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:37
msgid "Variable font"
msgstr "Font variabil"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:78
#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:322
msgid "Edit Font"
msgstr "Editează font"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/AllFonts.js:100
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:78
msgid "Remove Font"
msgstr "Elimină font"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:216
msgid "Change"
msgstr "Schimbă"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:217
msgid "Choose"
msgstr "Alege"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:127
#: static/js/options/ConditionsManager/SingleCondition.js:95
msgid "Select variation"
msgstr "Selectează variația"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:147
msgid "Regular"
msgstr "Normal"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:154
#: framework/premium/static/js/typography/providers/kadence.js:71
msgid "Italic"
msgstr "Cursiv"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/RegularTab.js:30
msgid "Upload only the %s.woff2%s or %s.ttf%s font file formats (see browser coverage %shere%s). Use %sthis converter tool%s if you don't have these font formats."
msgstr "Încarcă doar formatele de fișiere font %s.woff2%s sau %s.ttf%s (vezi compatibilitatea browserului %saici%s). Folosește %sacest instrument de conversie%s dacă nu ai aceste formate de font."

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:90
msgid "Font Name"
msgstr "Nume font"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:459
msgid "Save Custom Font"
msgstr "Salvează font-ul personalizat"

#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:79
msgid "Add Variation"
msgstr "Adaugă variație"

#: framework/premium/extensions/local-google-fonts/dashboard-static/js/LocalGoogleFonts.js:258
msgid "Download a font and serve it directly from your server, this is handy for those who want to comply with GDPR regulations or serve the font via CDN."
msgstr "Descarcă un font și servește-l direct de pe serverul tău, acest lucru este util pentru cei care doresc să respecte reglementările GDPR sau să livreze fontul prin CDN."

#: framework/premium/extensions/mega-menu/static/js/SettingsManager.js:189
msgid "Item Settings"
msgstr "Setări element"

#: framework/premium/extensions/sidebars/static/js/BlockWidgetControls.js:60
msgid "Remove Sidebar"
msgstr "Elimină bară laterală"

#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:65
msgid "%s - Sidebar Display Conditions"
msgstr "%s - Condiții de afișare bară laterală"

#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:73
msgid "Add one or more conditions in order to display your sidebar."
msgstr "Adaugă una sau mai multe condiții pentru a afișa bara laterală."

#: framework/premium/extensions/sidebars/static/js/main.js:53
msgid "Enter a name in the input below and hit the Create Sidebar button."
msgstr "Introdu un nume în câmpul de mai jos și apasă pe butonul Creează bară laterală."

#: framework/premium/extensions/sidebars/static/js/main.js:60
msgid "Sidebar name"
msgstr "Nume bară laterală"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:108
msgid "Advanced"
msgstr "Avansat"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:123
msgid "Agency Details"
msgstr "Detalii agenție"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:131
msgid "Agency Name"
msgstr "Nume agenție"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:159
msgid "Agency URL"
msgstr "URL agenție"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:187
msgid "Agency Support/Contact Form URL"
msgstr "URL formular suport/contact agenție"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:220
msgid "Theme Details"
msgstr "Detalii temă"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:228
msgid "Theme Name"
msgstr "Nume temă"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:256
msgid "Theme Description"
msgstr "Descriere temă"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:286
msgid "Theme Screenshot URL"
msgstr "URL captură ecran temă"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:384
msgid "You can insert the link to a self hosted image or upload one. The recommended image size is 1200px wide by 900px tall."
msgstr "Poți introduce linkul către o imagine găzduită local sau să încarci una. Dimensiunea recomandată este de 1200px lățime și 900px înălțime."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:393
msgid "Theme Icon URL"
msgstr "URL pictogramă temă"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:489
msgid "You can insert the link to a self hosted image or upload one. The recommended image size is 18px wide by 18px tall."
msgstr "Poți introduce linkul către o imagine găzduită local sau să încarci una. Dimensiunea recomandată este de 18px lățime și 18px înălțime."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:498
msgid "Gutenberg Options Panel Icon URL"
msgstr "Poți introduce linkul către o imagine găzduită local sau să încarci una. Dimensiunea recomandată este de 18px lățime și 18px înălțime."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:596
msgid "You can insert the link to a self hosted image or upload one. Please note that only icons in SVG format are allowed here to not break the editor interactiveness."
msgstr "Poți introduce linkul către o imagine găzduită local sau să încarci una. Te rugăm să reții că sunt permise doar pictogramele în format SVG pentru a nu afecta interactivitatea editorului."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:616
msgid "Plugin Name"
msgstr "Nume modul"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:644
msgid "Plugin Description"
msgstr "Descriere modul"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:674
msgid "Plugin Thumbnail URL"
msgstr "URL pictogramă modul"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:764
msgid "Choose File"
msgstr "Alege fișierul"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:772
msgid "You can insert the link to a self hosted image or upload one. The recommended image size is 256px wide by 256px tall."
msgstr "Poți introduce linkul către o imagine găzduită local sau să încarci una. Dimensiunea recomandată este de 256px lățime și 256px înălțime."

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:795
msgid "Hide Account Menu Item"
msgstr "Ascunde meniul de cont"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:803
msgid "Hide Starter Sites Tab"
msgstr "Ascunde tab-ul cu site-uri demonstrative"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:810
msgid "Hide Useful Plugins Tab"
msgstr "Ascunde tab-ul cu module utile"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:818
msgid "Hide Changelog Tab"
msgstr "Ascunde tab-ul cu ultimele actualizări"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:826
msgid "Hide Support Section"
msgstr "Ascunde secțiunea de suport"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:876
msgid "Hide White Label Extension"
msgstr "Ascunde extensia White Label"

#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:891
msgid "Please note that the white label extension will be hidden if this option is enabled. In order to bring it back you have to hit the SHIFT key and click on the dashboard logo."
msgstr "Te rugăm să reții că extensia White Label va fi ascunsă dacă această opțiune este activată. Pentru a o readuce, trebuie să apeși tasta SHIFT și să dai clic pe logo-ul din tabloul de bord."

#: framework/premium/static/js/footer/CloneItem.js:66
#: framework/premium/static/js/header/CloneItem.js:66
msgid "Clone Item"
msgstr "Multiplică elementul"

#: framework/premium/static/js/footer/CloneItem.js:90
#: framework/premium/static/js/header/CloneItem.js:90
msgid "Remove Item"
msgstr "Elimină elementul"

#: framework/premium/static/js/footer/CreateFooter.js:107
#: framework/premium/static/js/header/CreateHeader.js:106
msgid "Copy elements & styles from"
msgstr "Copiază elemente și stiluri din"

#: framework/premium/static/js/footer/CreateFooter.js:118
msgid "Picker Footer"
msgstr "Selector subsol"

#: framework/premium/static/js/footer/CreateFooter.js:133
#: framework/premium/static/js/footer/PanelsManager.js:52
msgid "Global Footer"
msgstr "Subsol global"

#: framework/premium/static/js/footer/CreateFooter.js:137
#: framework/premium/static/js/header/CreateHeader.js:138
msgid "Secondary"
msgstr "Secundar"

#: framework/premium/static/js/footer/CreateFooter.js:141
#: framework/premium/static/js/header/CreateHeader.js:142
msgid "Centered"
msgstr "Centrat"

#: framework/premium/static/js/footer/CreateFooter.js:172
msgid "Create New Footer"
msgstr "Crează un nou subsol"

#: framework/premium/static/js/footer/CreateFooter.js:50
msgid "Create new footer"
msgstr "Crează un nou subsol"

#: framework/premium/static/js/footer/CreateFooter.js:53
msgid "Create a new footer and assign it to different pages or posts based on your conditions."
msgstr "Creează un subsol nou și atribuie-l diferitelor pagini sau articole pe baza condițiilor tale."

#: framework/premium/static/js/footer/CreateFooter.js:72
msgid "Footer name"
msgstr "Nume subsol"

#: framework/premium/static/js/footer/EditConditions.js:100
msgid "Add one or more conditions in order to display your footer."
msgstr "Adaugă una sau mai multe condiții pentru a afișa subsolul."

#: framework/premium/static/js/footer/EditConditions.js:84
#: static/js/header/EditConditions.js:88
msgid "Add/Edit Conditions"
msgstr "Adaugă sau editează condițiile"

#: framework/premium/static/js/footer/PanelsManager.js:169
msgid "Remove footer"
msgstr "Înlătură subsolul"

#: framework/premium/static/js/footer/PanelsManager.js:193
msgid "Remove Footer"
msgstr "Înlătură subsolul"

#: framework/premium/static/js/footer/PanelsManager.js:196
msgid "You are about to remove a custom footer, are you sure you want to continue?"
msgstr "Ești pe punctul de a elimina un subsol personalizat, ești sigur că vrei să continui?"

#: framework/premium/static/js/footer/PanelsManager.js:212
#: framework/premium/static/js/hooks/CodeEditor.js:189
#: static/js/header/PanelsManager.js:217
#: static/js/options/CustomizerOptionsManager.js:463
msgid "Cancel"
msgstr "Anulează"

#: framework/premium/static/js/footer/PanelsManager.js:228
#: static/js/header/PanelsManager.js:233
msgid "Confirm"
msgstr "Confirmă"

#: framework/premium/static/js/footer/PanelsManager.js:68
#: static/js/header/PanelsManager.js:74
#: static/js/options/DisplayCondition.js:61
msgid "Edit Conditions"
msgstr "Editează condițiile"

#. translators: %s: PHP version
#: blocksy-companion.php:182
msgid "Blocksy requires PHP version %s+, plugin is currently NOT RUNNING."
msgstr "Blocksy necesită versiunea PHP %s+, pluginul nu rulează în prezent."

#. translators: %s: WordPress version
#: blocksy-companion.php:193
msgid "Blocksy requires WordPress version %s+. Because you are using an earlier version, the plugin is currently NOT RUNNING."
msgstr "Blocksy necesită versiunea WordPress %s+. Deoarece folosești o versiune mai veche, pluginul nu rulează în prezent."

#: framework/premium/extensions/adobe-typekit/extension.php:46
msgid "Adobe Typekit"
msgstr "Adobe Typekit"

#: framework/premium/extensions/custom-fonts/extension.php:154
msgid "Custom Fonts"
msgstr "Font-uri proprii"

#: framework/premium/extensions/local-google-fonts/extension.php:122
msgid "Local Google Fonts"
msgstr "Font-uri locale Google"

#: framework/theme-integration.php:220,
#: framework/features/blocks/share-box/options.php:19
msgid "Facebook"
msgstr "Facebook"

#: framework/theme-integration.php:222,
#: framework/features/blocks/share-box/options.php:37
msgid "LinkedIn"
msgstr "LinkedIn"

#: framework/theme-integration.php:223
msgid "Dribbble"
msgstr "Dribbble"

#: framework/theme-integration.php:224
msgid "Instagram"
msgstr "Instagram"

#: framework/theme-integration.php:225,
#: framework/features/blocks/share-box/options.php:31
msgid "Pinterest"
msgstr "Pinterest"

#: framework/theme-integration.php:226
msgid "WordPress"
msgstr "WordPress"

#: framework/theme-integration.php:227
msgid "GitHub"
msgstr "GitHub"

#: framework/theme-integration.php:228,
#: framework/features/blocks/about-me/options.php:86,
#: framework/premium/features/content-blocks/options/archive.php:74
msgid "Medium"
msgstr "Medium"

#: framework/theme-integration.php:229,
#: framework/premium/features/media-video/options.php:14
msgid "YouTube"
msgstr "YouTube"

#: framework/theme-integration.php:230,
#: framework/premium/features/media-video/options.php:15
msgid "Vimeo"
msgstr "Vimeo"

#: framework/theme-integration.php:231,
#: framework/features/blocks/share-box/options.php:55
msgid "VKontakte"
msgstr "VKontakte"

#: framework/theme-integration.php:232,
#: framework/features/blocks/share-box/options.php:61
msgid "Odnoklassniki"
msgstr "Odnoklassniki"

#: framework/theme-integration.php:233
msgid "TikTok"
msgstr "TikTok"

#: framework/theme-integration.php:278
msgid "Companion"
msgstr "Companion"

#: framework/theme-integration.php:296
msgid "PRO"
msgstr "Pro"

#: framework/features/account-auth.php:119,
#: framework/features/account-auth.php:267
msgid "Check your email"
msgstr "Verifică-ți e-mailul"

#: framework/features/account-auth.php:273
msgid "Registration Form"
msgstr "Formular de înregistrare"

#: framework/features/account-auth.php:274
msgid "Register For This Site"
msgstr "Înregistrează-te pentru acest site"

#: framework/features/conditions/rules/cpt.php:17
msgid "%s Single"
msgstr "%s Individual"

#: framework/features/conditions/rules/cpt.php:27
msgid "%s Archive"
msgstr "Arhivă %s"

#: framework/features/conditions/rules/cpt.php:40
msgid "%s %s Taxonomy"
msgstr "Taxonomie %s %s"

#: framework/features/conditions/rules/basic.php:3
msgid "Entire Website"
msgstr "Tot website-ul"

#: framework/features/conditions/rules/basic.php:57
msgid "Basic"
msgstr "De bază"

#: framework/extensions/trending/customizer.php:4,
#: framework/features/blocks/query/block.php:19,
#: framework/features/blocks/search/options.php:6,
#: framework/features/conditions/rules/posts.php:33,
#: framework/premium/features/premium-header/items/search-input/options.php:4
msgid "Posts"
msgstr "Postări"

#: framework/features/conditions/rules/posts.php:27,
#: framework/premium/features/content-blocks/hooks-manager.php:440,
#: framework/premium/features/content-blocks/hooks-manager.php:448,
#: framework/premium/features/content-blocks/hooks-manager.php:456,
#: framework/premium/features/content-blocks/hooks-manager.php:463,
#: framework/premium/features/content-blocks/hooks-manager.php:470,
#: framework/premium/features/content-blocks/hooks-manager.php:478
msgid "Single Post"
msgstr "Articol individual"

#: framework/features/conditions/rules/posts.php:15
msgid "Post Categories"
msgstr "Categorii articole"

#: framework/features/conditions/rules/posts.php:20
msgid "Post Tags"
msgstr "Etichete articole"

#: framework/features/blocks/search/options.php:7,
#: framework/features/conditions/rules/pages.php:52,
#: framework/premium/features/premium-header/items/search-input/options.php:5
msgid "Pages"
msgstr "Pagini"

#: framework/features/conditions/rules/pages.php:8
msgid "Single Page"
msgstr "Pagină individuală"

#: framework/features/conditions/rules/specific.php:48
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:69
msgid "Specific"
msgstr "Specific"

#: framework/features/conditions/rules/specific.php:10
msgid "Post ID"
msgstr "ID postare"

#: framework/features/conditions/rules/specific.php:15
msgid "Page ID"
msgstr "ID pagină"

#: framework/features/conditions/rules/specific.php:20
#: static/js/options/ConditionsManager/PostIdPicker.js:79
msgid "Custom Post Type ID"
msgstr "ID tip articol personalizat"

#: framework/features/conditions/rules/specific.php:38,
#: framework/features/conditions/rules/woo.php:65
msgid "Taxonomy ID"
msgstr "ID taxonomie"

#: framework/features/conditions/rules/specific.php:25
msgid "Post with Taxonomy ID"
msgstr "Articol cu ID taxonomie"

#: framework/features/conditions/rules/pages.php:15
msgid "404"
msgstr "404"

#: framework/features/blocks/search/options.php:60,
#: framework/features/blocks/search/options.php:66,
#: framework/features/blocks/search/view.php:80,
#: framework/features/blocks/search/view.php:261,
#: framework/features/conditions/rules/pages.php:22,
#: framework/premium/features/premium-header/items/search-input/options.php:48,
#: framework/premium/features/premium-header/items/search-input/view.php:126
#: static/js/editor/blocks/query/edit/PatternSelectionModal.js:92
#: static/js/editor/blocks/search/Preview.js:23
#: static/js/editor/blocks/tax-query/edit/PatternSelectionModal.js:78
msgid "Search"
msgstr "Căutare"

#: framework/features/conditions/rules/pages.php:27
msgid "Blog"
msgstr "Blog"

#: framework/features/conditions/rules/pages.php:33
msgid "Front Page"
msgstr "Pagina principală"

#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:70
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:157
msgid "Author"
msgstr "Autor"

#: framework/features/conditions/rules/user-auth.php:20
msgid "User Auth"
msgstr "Autentificare utilizator"

#: framework/features/conditions/rules/user-auth.php:24
msgid "User Logged In"
msgstr "Utilizator conectat"

#: framework/features/conditions/rules/user-auth.php:29
msgid "User Logged Out"
msgstr "Utilizator deconectat"

#: framework/features/conditions/rules/user-auth.php:34
msgid "User Role"
msgstr "User Role"

#: framework/premium/features/content-blocks/options/404.php:19,
#: framework/premium/features/content-blocks/options/header.php:19,
#: framework/premium/features/content-blocks/options/hook.php:24,
#: framework/premium/features/content-blocks/options/nothing_found.php:19,
#: framework/premium/features/content-blocks/options/popup.php:25
msgid "Other"
msgstr "Altul"

#: framework/features/conditions-manager.php:307
msgid "Language"
msgstr "Limbă"

#: framework/features/demo-install.php:98
msgid "Your PHP installation doesn't have support for XML. Please install the <i>xml</i> or <i>simplexml</i> PHP extension in order to be able to install starter sites. You might need to contact your hosting provider to assist you in doing so."
msgstr "Instalarea PHP de pe serverul tău nu are suport pentru XML. Te rugăm să instalezi extensia PHP <i>xml</i> sau <i>simplexml</i> pentru a putea instala site-uri de pornire. Este posibil să fie nevoie să contactezi furnizorul tău de găzduire pentru asistență."

#: framework/features/dynamic-css.php:48
msgid "Dynamic CSS Output"
msgstr "Generare CSS dinamic"

#: framework/features/dynamic-css.php:52
msgid "The strategy of outputting the dynamic CSS. File - all the CSS code will be placed in a static file, otherwise it will be placed inline in head."
msgstr "Strategia de generare a CSS-ului dinamic. Fișier - tot codul CSS va fi plasat într-un fișier static, altfel va fi plasat inline în secțiunea head."

#: framework/features/dynamic-css.php:54
msgid "File"
msgstr "Fișier"

#: framework/features/dynamic-css.php:55,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:67,
#: framework/premium/features/premium-header/items/language-switcher/options.php:165
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:159
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:204
msgid "Inline"
msgstr "Inline"

#: framework/features/google-analytics.php:69
msgid "Google Analytics v4"
msgstr "Google Analytics v4"

#: framework/features/google-analytics.php:74
msgid "Link your Google Analytics 4 tracking ID. More info and instructions can be found %shere%s."
msgstr "Conectează ID-ul de urmărire Google Analytics 4. Mai multe informații și instrucțiuni pot fi găsite %saici%s."

#: framework/features/opengraph-meta-data.php:13
msgid "OpenGraph Meta Data"
msgstr "Meta Data OpenGraph"

#: framework/features/opengraph-meta-data.php:17
msgid "Enable the OpenGraph rich meta data features for your website."
msgstr "Activează funcționalitățile OpenGraph pentru meta date îmbogățite pe site-ul tău."

#: framework/features/opengraph-meta-data.php:26
msgid "Facebook Page URL"
msgstr "Facebook Page URL"

#: framework/features/opengraph-meta-data.php:33
msgid "Facebook App ID"
msgstr "Facebook App ID"

#: framework/features/opengraph-meta-data.php:40
msgid "Twitter Username"
msgstr "Twitter Username"

#: framework/extensions/cookies-consent/config.php:6
msgid "Display a cookie acceptance box in order to comply with the privacy regulations in your country."
msgstr "Afișează o casetă pentru acceptarea cookie-urilor pentru a respecta reglementările privind confidențialitatea din țara ta."

#: framework/extensions/cookies-consent/customizer.php:15,
#: framework/extensions/newsletter-subscribe/customizer.php:12,
#: framework/extensions/product-reviews/extension.php:382,
#: framework/extensions/product-reviews/metabox.php:6,
#: framework/extensions/trending/customizer.php:173,
#: framework/features/header/header-options.php:6,
#: framework/premium/extensions/mega-menu/options.php:6,
#: framework/premium/extensions/shortcuts/customizer.php:761,
#: framework/features/header/items/account/options.php:330,
#: framework/premium/features/content-blocks/options/404.php:55,
#: framework/premium/features/content-blocks/options/archive.php:122,
#: framework/premium/features/content-blocks/options/header.php:73,
#: framework/premium/features/content-blocks/options/hook.php:182,
#: framework/premium/features/content-blocks/options/maintenance.php:52,
#: framework/premium/features/content-blocks/options/nothing_found.php:73,
#: framework/premium/features/content-blocks/options/popup.php:30,
#: framework/premium/features/content-blocks/options/single.php:54,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:6,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:35,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:279,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:520,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:41,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:120,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:378,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:9,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:20,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:66,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:11,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:10,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:10,
#: framework/premium/features/premium-header/items/contacts/options.php:45,
#: framework/premium/features/premium-header/items/content-block/options.php:5,
#: framework/premium/features/premium-header/items/language-switcher/options.php:278,
#: framework/premium/features/premium-header/items/search-input/options.php:40,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:19,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:163,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:63,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:5,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:5
#: framework/premium/extensions/custom-fonts/dashboard-static/js/Uploader.js:352
#: framework/premium/extensions/local-google-fonts/dashboard-static/js/SingleFont.js:111
#: framework/premium/extensions/white-label/dashboard-static/js/EditSettings.js:104
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:43
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:38
msgid "General"
msgstr "General"

#: framework/extensions/cookies-consent/customizer.php:28,
#: framework/premium/extensions/shortcuts/customizer.php:773,
#: framework/features/header/items/account/options.php:447,
#: framework/features/header/items/account/options.php:821,
#: framework/features/header/items/account/options.php:909,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:23,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:48,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:375,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:31,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:56,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:101,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:55,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:56
msgid "Type 1"
msgstr "Tip 1"

#: framework/extensions/cookies-consent/customizer.php:33,
#: framework/premium/extensions/shortcuts/customizer.php:778,
#: framework/features/header/items/account/options.php:457,
#: framework/features/header/items/account/options.php:831,
#: framework/features/header/items/account/options.php:919,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:28,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:53,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:376,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:36,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:61,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:106,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:60,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:61
msgid "Type 2"
msgstr "Tip 2"

#: framework/extensions/cookies-consent/customizer.php:40
msgid "Cookie period"
msgstr "Perioada Cookie"

#: framework/extensions/cookies-consent/customizer.php:48
msgid "One hour"
msgstr "O oră"

#: framework/extensions/cookies-consent/customizer.php:49
msgid "One day"
msgstr "O zi"

#: framework/extensions/cookies-consent/customizer.php:50
msgid "One week"
msgstr "O săptămână"

#: framework/extensions/cookies-consent/customizer.php:51
msgid "One month"
msgstr "O lună"

#: framework/extensions/cookies-consent/customizer.php:52
msgid "Three months"
msgstr "Trei luni"

#: framework/extensions/cookies-consent/customizer.php:53
msgid "Six months"
msgstr "Șase luni"

#: framework/extensions/cookies-consent/customizer.php:54
msgid "One year"
msgstr "Un an"

#: framework/extensions/cookies-consent/customizer.php:55
msgid "Forever"
msgstr "Pentru totdeauna"

#: framework/extensions/cookies-consent/customizer.php:62,
#: framework/features/blocks/contact-info/options.php:87,
#: framework/features/blocks/contact-info/options.php:152,
#: framework/features/blocks/contact-info/options.php:215,
#: framework/features/blocks/contact-info/options.php:278,
#: framework/features/blocks/contact-info/options.php:341,
#: framework/features/blocks/contact-info/options.php:404,
#: framework/features/blocks/contact-info/options.php:467,
#: framework/premium/features/content-blocks/hooks-manager.php:267,
#: framework/premium/features/content-blocks/hooks-manager.php:275,
#: framework/premium/features/content-blocks/hooks-manager.php:283,
#: framework/premium/features/content-blocks/hooks-manager.php:291,
#: framework/premium/features/premium-header/items/contacts/options.php:97,
#: framework/premium/features/premium-header/items/contacts/options.php:142,
#: framework/premium/features/premium-header/items/contacts/options.php:187,
#: framework/premium/features/premium-header/items/contacts/options.php:232,
#: framework/premium/features/premium-header/items/contacts/options.php:278,
#: framework/premium/features/premium-header/items/contacts/options.php:323,
#: framework/premium/features/premium-header/items/contacts/options.php:368
msgid "Content"
msgstr "Conținut"

#: framework/extensions/cookies-consent/customizer.php:64,
#: framework/extensions/cookies-consent/helpers.php:7
msgid "We use cookies to ensure that we give you the best experience on our website."
msgstr "Folosim cookie-uri pentru a ne asigura că îți oferim cea mai bună experiență pe site-ul nostru."

#: framework/features/blocks/search/options.php:58
msgid "Button Text"
msgstr "Button Text"

#: framework/extensions/cookies-consent/customizer.php:80,
#: framework/extensions/cookies-consent/helpers.php:10
msgid "Accept"
msgstr "Acceptă"

#: framework/extensions/cookies-consent/customizer.php:142,
#: framework/extensions/newsletter-subscribe/customizer.php:170,
#: framework/extensions/product-reviews/extension.php:422,
#: framework/extensions/trending/customizer.php:582,
#: framework/features/header/header-options.php:203,
#: framework/premium/extensions/mega-menu/options.php:567,
#: framework/premium/extensions/shortcuts/customizer.php:1032,
#: framework/features/header/items/account/options.php:1107,
#: framework/premium/features/content-blocks/options/404.php:119,
#: framework/premium/features/content-blocks/options/archive.php:196,
#: framework/premium/features/content-blocks/options/header.php:137,
#: framework/premium/features/content-blocks/options/hook.php:246,
#: framework/premium/features/content-blocks/options/maintenance.php:116,
#: framework/premium/features/content-blocks/options/nothing_found.php:137,
#: framework/premium/features/content-blocks/options/popup.php:517,
#: framework/premium/features/content-blocks/options/single.php:128,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:147,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:178,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:169,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:406,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:661,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:289,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:458,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:108,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:253,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:570,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:124,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:132,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:190,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:66,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:245,
#: framework/premium/features/premium-header/items/contacts/options.php:571,
#: framework/premium/features/premium-header/items/content-block/options.php:36,
#: framework/premium/features/premium-header/items/language-switcher/options.php:284,
#: framework/premium/features/premium-header/items/search-input/options.php:260,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:66,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:100,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:152,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:154
msgid "Design"
msgstr "Aspect"

#: framework/extensions/cookies-consent/customizer.php:203,
#: framework/extensions/cookies-consent/customizer.php:270,
#: framework/premium/extensions/mega-menu/options.php:879,
#: framework/premium/extensions/shortcuts/customizer.php:1058,
#: framework/features/header/items/account/options.php:1518,
#: framework/features/header/items/account/options.php:1902,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:202,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:233,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:262,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:195,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:113,
#: framework/premium/features/premium-header/items/contacts/options.php:585,
#: framework/premium/features/premium-header/items/contacts/options.php:614,
#: framework/premium/features/premium-header/items/contacts/options.php:655,
#: framework/premium/features/premium-header/items/contacts/options.php:694,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:111,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:222,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:251,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:281,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:310,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:52,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:81,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:111,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:140,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:217,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:261,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:303,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:209,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:240,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:269
msgid "Font Color"
msgstr "Culoare font"

#: framework/extensions/cookies-consent/customizer.php:164,
#: framework/extensions/cookies-consent/customizer.php:191,
#: framework/extensions/cookies-consent/customizer.php:220,
#: framework/extensions/cookies-consent/customizer.php:251,
#: framework/extensions/cookies-consent/customizer.php:287,
#: framework/extensions/cookies-consent/customizer.php:316,
#: framework/extensions/newsletter-subscribe/customizer.php:188,
#: framework/extensions/newsletter-subscribe/customizer.php:213,
#: framework/extensions/newsletter-subscribe/customizer.php:245,
#: framework/extensions/newsletter-subscribe/customizer.php:276,
#: framework/extensions/newsletter-subscribe/customizer.php:313,
#: framework/extensions/newsletter-subscribe/customizer.php:345,
#: framework/extensions/trending/customizer.php:610,
#: framework/extensions/trending/customizer.php:673,
#: framework/extensions/trending/customizer.php:726,
#: framework/extensions/trending/customizer.php:766,
#: framework/extensions/trending/customizer.php:798,
#: framework/extensions/trending/customizer.php:848,
#: framework/extensions/trending/customizer.php:889,
#: framework/premium/extensions/mega-menu/options.php:845,
#: framework/premium/extensions/mega-menu/options.php:892,
#: framework/premium/extensions/mega-menu/options.php:911,
#: framework/premium/extensions/shortcuts/customizer.php:1076,
#: framework/premium/extensions/shortcuts/customizer.php:1110,
#: framework/premium/extensions/shortcuts/customizer.php:1142,
#: framework/features/header/items/account/options.php:1198,
#: framework/features/header/items/account/options.php:1242,
#: framework/features/header/items/account/options.php:1284,
#: framework/features/header/items/account/options.php:1389,
#: framework/features/header/items/account/options.php:1432,
#: framework/features/header/items/account/options.php:1473,
#: framework/features/header/items/account/options.php:1539,
#: framework/features/header/items/account/options.php:1572,
#: framework/features/header/items/account/options.php:1610,
#: framework/features/header/items/account/options.php:1653,
#: framework/features/header/items/account/options.php:1758,
#: framework/features/header/items/account/options.php:1801,
#: framework/features/header/items/account/options.php:1852,
#: framework/features/header/items/account/options.php:1974,
#: framework/premium/features/content-blocks/options/popup.php:632,
#: framework/premium/features/content-blocks/options/popup.php:663,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:219,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:250,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:279,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:345,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:376,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:405,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1430,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:220,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:234,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:270,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:196,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:232,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:433,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:463,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:797,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:480,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:511,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:126,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:349,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:383,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:422,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:151,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:180,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:159,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:189,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:209,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:239,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:269,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:311,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:341,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:381,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:409,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:27,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:57,
#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:50,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:215,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:231,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:267,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:308,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:82,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:275,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:319,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:363,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:403,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:442,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:487,
#: framework/premium/features/premium-header/items/contacts/options.php:797,
#: framework/premium/features/premium-header/items/contacts/options.php:828,
#: framework/premium/features/premium-header/items/contacts/options.php:857,
#: framework/premium/features/premium-header/items/contacts/options.php:956,
#: framework/premium/features/premium-header/items/contacts/options.php:994,
#: framework/premium/features/premium-header/items/contacts/options.php:1032,
#: framework/premium/features/premium-header/items/search-input/options.php:313,
#: framework/premium/features/premium-header/items/search-input/options.php:345,
#: framework/premium/features/premium-header/items/search-input/options.php:375,
#: framework/premium/features/premium-header/items/search-input/options.php:443,
#: framework/premium/features/premium-header/items/search-input/options.php:475,
#: framework/premium/features/premium-header/items/search-input/options.php:505,
#: framework/premium/features/premium-header/items/search-input/options.php:573,
#: framework/premium/features/premium-header/items/search-input/options.php:605,
#: framework/premium/features/premium-header/items/search-input/options.php:635,
#: framework/premium/features/premium-header/items/search-input/options.php:708,
#: framework/premium/features/premium-header/items/search-input/options.php:738,
#: framework/premium/features/premium-header/items/search-input/options.php:768,
#: framework/premium/features/premium-header/items/search-input/options.php:851,
#: framework/premium/features/premium-header/items/search-input/options.php:879,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:84,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:105,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:128,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:154,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:268,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:298,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:327,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:399,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:430,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:459,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:98,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:128,
#: framework/premium/features/premium-header/items/language-switcher/options/inline.php:157,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:241,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:285,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:327,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:403,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:438,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:471,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:226,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:257,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:286,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:353,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:384,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:413
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:261
#: static/js/editor/blocks/about-me/Edit.js:189
#: static/js/editor/blocks/contact-info/Edit.js:203
#: static/js/editor/blocks/search/Edit.js:409
#: static/js/editor/blocks/share-box/Edit.js:152
#: static/js/editor/blocks/socials/Edit.js:152
msgid "Initial"
msgstr "Inițial"

#: framework/extensions/cookies-consent/customizer.php:170,
#: framework/extensions/cookies-consent/customizer.php:226,
#: framework/extensions/cookies-consent/customizer.php:257,
#: framework/extensions/cookies-consent/customizer.php:292,
#: framework/extensions/cookies-consent/customizer.php:321,
#: framework/extensions/newsletter-subscribe/customizer.php:219,
#: framework/extensions/newsletter-subscribe/customizer.php:350,
#: framework/extensions/trending/customizer.php:678,
#: framework/extensions/trending/customizer.php:731,
#: framework/extensions/trending/customizer.php:772,
#: framework/extensions/trending/customizer.php:804,
#: framework/extensions/trending/customizer.php:895,
#: framework/premium/extensions/mega-menu/options.php:850,
#: framework/premium/extensions/shortcuts/customizer.php:1082,
#: framework/premium/extensions/shortcuts/customizer.php:1116,
#: framework/premium/extensions/shortcuts/customizer.php:1148,
#: framework/features/header/items/account/options.php:1207,
#: framework/features/header/items/account/options.php:1250,
#: framework/features/header/items/account/options.php:1292,
#: framework/features/header/items/account/options.php:1398,
#: framework/features/header/items/account/options.php:1440,
#: framework/features/header/items/account/options.php:1481,
#: framework/features/header/items/account/options.php:1545,
#: framework/features/header/items/account/options.php:1764,
#: framework/features/header/items/account/options.php:1810,
#: framework/features/header/items/account/options.php:1861,
#: framework/features/header/items/account/options.php:1979,
#: framework/premium/features/content-blocks/options/popup.php:638,
#: framework/premium/features/content-blocks/options/popup.php:669,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:225,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:255,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:284,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:351,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:381,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:410,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:1436,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:802,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:486,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:517,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:354,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:389,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:428,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:245,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:275,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:317,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:347,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:386,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:414,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:33,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/design-options.php:63,
#: framework/premium/extensions/woocommerce-extra/features/share-box-layer/feature.php:56,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:236,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:273,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:314,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:281,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:325,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:369,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:409,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:448,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:493,
#: framework/premium/features/premium-header/items/contacts/options.php:803,
#: framework/premium/features/premium-header/items/contacts/options.php:833,
#: framework/premium/features/premium-header/items/contacts/options.php:862,
#: framework/premium/features/premium-header/items/contacts/options.php:961,
#: framework/premium/features/premium-header/items/contacts/options.php:999,
#: framework/premium/features/premium-header/items/contacts/options.php:1037,
#: framework/premium/features/premium-header/items/search-input/options.php:856,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:133,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:250,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:293,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:335,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:409,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:443,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:476,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:232,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:262,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:291,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:359,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:389,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:418
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:270
#: static/js/editor/blocks/about-me/Edit.js:198
#: static/js/editor/blocks/contact-info/Edit.js:212
#: static/js/editor/blocks/search/Edit.js:368
#: static/js/editor/blocks/share-box/Edit.js:161
#: static/js/editor/blocks/socials/Edit.js:161
msgid "Hover"
msgstr "Trecere mouse"

#: framework/extensions/newsletter-subscribe/customizer.php:328,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:253
msgid "Button Color"
msgstr "Culoare buton"

#: framework/extensions/cookies-consent/customizer.php:178,
#: framework/extensions/cookies-consent/customizer.php:234,
#: framework/extensions/cookies-consent/customizer.php:299,
#: framework/premium/extensions/mega-menu/options.php:899,
#: framework/features/header/items/account/options.php:1829,
#: framework/features/header/items/account/options.php:1953,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:134,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:405,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/customizer-options.php:289,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:421,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:92,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:141
msgid "Background Color"
msgstr "Culoare fundal"

#: framework/extensions/cookies-consent/customizer.php:98
msgid "Maximum Width"
msgstr "Lățime maximă"

#: framework/extensions/cookies-consent/customizer.php:119
msgid "I accept the %sPrivacy Policy%s*"
msgstr "Accept %sPolitica de confidențialitate%s*"

#: framework/extensions/cookies-consent/customizer.php:123
msgid "This text will appear under each comment form and subscribe form."
msgstr "Acest text va apărea sub fiecare formular de comentarii și abonare."

#: framework/extensions/cookies-consent/helpers.php:55
msgid "I accept the %sPrivacy Policy%s"
msgstr "Accept %sPolitica de confidențialitate%s"

#: framework/features/blocks/about-me/options.php:128
msgid "Customizer"
msgstr "Personalizator"

#: framework/extensions/newsletter-subscribe/config.php:6
msgid "Easily capture new leads for your newsletter with the help of a widget, shortcode or even a block inserted on your pages or posts."
msgstr "Captivează cu ușurință noi abonați pentru newsletterul tău cu ajutorul unui widget, shortcode sau chiar a unui bloc inserat în paginile sau articolele tale."

#: framework/extensions/newsletter-subscribe/customizer.php:4
msgid "Subscribe Form"
msgstr "Formular de abonare"

#: framework/extensions/newsletter-subscribe/customizer.php:18,
#: framework/features/blocks/about-me/options.php:15,
#: framework/features/blocks/contact-info/options.php:34,
#: framework/features/blocks/contact-info/options.php:80,
#: framework/features/blocks/contact-info/options.php:145,
#: framework/features/blocks/contact-info/options.php:208,
#: framework/features/blocks/contact-info/options.php:271,
#: framework/features/blocks/contact-info/options.php:334,
#: framework/features/blocks/contact-info/options.php:397,
#: framework/features/blocks/contact-info/options.php:460,
#: framework/features/blocks/share-box/options.php:14,
#: framework/features/blocks/socials/options.php:14,
#: framework/premium/extensions/woocommerce-extra/features/brands/feature.php:582,
#: framework/premium/extensions/woocommerce-extra/features/product-sale-countdown/feature.php:327,
#: framework/premium/features/premium-header/items/contacts/options.php:87,
#: framework/premium/features/premium-header/items/contacts/options.php:132,
#: framework/premium/features/premium-header/items/contacts/options.php:177,
#: framework/premium/features/premium-header/items/contacts/options.php:222,
#: framework/premium/features/premium-header/items/contacts/options.php:268,
#: framework/premium/features/premium-header/items/contacts/options.php:313,
#: framework/premium/features/premium-header/items/contacts/options.php:358
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:45
#: static/js/editor/blocks/query/edit/PostsInspectorControls.js:125
msgid "Title"
msgstr "Titlu"

#: framework/extensions/newsletter-subscribe/customizer.php:20,
#: framework/extensions/newsletter-subscribe/helpers.php:21,
#: framework/extensions/newsletter-subscribe/helpers.php:69
msgid "Newsletter Updates"
msgstr "Actualizări newsletter"

#: framework/extensions/newsletter-subscribe/customizer.php:26,
#: framework/features/blocks/about-me/options.php:64,
#: framework/features/blocks/dynamic-data/options.php:142,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:78,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:159,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:248
msgid "Description"
msgstr "Descriere"

#: framework/extensions/newsletter-subscribe/helpers.php:72
msgid "Enter your email address below to subscribe to our newsletter"
msgstr "Introdu adresa ta de e-mail mai jos pentru a te abona la newsletterul nostru"

#: framework/extensions/newsletter-subscribe/customizer.php:41,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:34
msgid "List Source"
msgstr "Sursă listă"

#: framework/extensions/newsletter-subscribe/customizer.php:48,
#: framework/extensions/product-reviews/metabox.php:17,
#: framework/extensions/trending/customizer.php:232,
#: framework/extensions/trending/customizer.php:306,
#: framework/extensions/trending/customizer.php:448,
#: framework/features/header/header-options.php:76,
#: framework/premium/features/premium-header.php:322,
#: framework/premium/features/socials.php:20,
#: framework/premium/features/socials.php:48,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:40,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:81,
#: framework/features/blocks/contact-info/options.php:110,
#: framework/features/blocks/contact-info/options.php:175,
#: framework/features/blocks/contact-info/options.php:238,
#: framework/features/blocks/contact-info/options.php:301,
#: framework/features/blocks/contact-info/options.php:364,
#: framework/features/blocks/contact-info/options.php:427,
#: framework/features/blocks/contact-info/options.php:490,
#: framework/features/header/items/account/options.php:385,
#: framework/features/header/items/account/options.php:766,
#: framework/premium/features/content-blocks/options/hook.php:166,
#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:19,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:12,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:17,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:17
msgid "Default"
msgstr "Prestabilit"

#: framework/extensions/newsletter-subscribe/customizer.php:49,
#: framework/extensions/trending/customizer.php:236,
#: framework/premium/features/premium-header.php:323,
#: framework/premium/features/socials.php:21,
#: framework/premium/features/socials.php:49,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:41,
#: framework/features/blocks/about-me/options.php:27,
#: framework/features/blocks/about-me/options.php:194,
#: framework/features/blocks/contact-info/options.php:111,
#: framework/features/blocks/contact-info/options.php:176,
#: framework/features/blocks/contact-info/options.php:239,
#: framework/features/blocks/contact-info/options.php:302,
#: framework/features/blocks/contact-info/options.php:365,
#: framework/features/blocks/contact-info/options.php:428,
#: framework/features/blocks/contact-info/options.php:491,
#: framework/features/blocks/dynamic-data/options.php:64,
#: framework/features/blocks/share-box/options.php:148,
#: framework/features/blocks/socials/options.php:100,
#: framework/features/conditions/rules/custom.php:33,
#: framework/premium/extensions/shortcuts/customizer.php:260,
#: framework/premium/extensions/shortcuts/customizer.php:287,
#: framework/features/header/items/account/options.php:25,
#: framework/features/header/items/account/options.php:389,
#: framework/features/header/items/account/options.php:770,
#: framework/premium/features/content-blocks/options/archive.php:75,
#: framework/premium/features/content-blocks/options/hook.php:168,
#: framework/premium/features/content-blocks/options/popup.php:287,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:616,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:196,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:188,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:41,
#: framework/premium/extensions/woocommerce-extra/features/swatches/feature.php:843,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:18,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:18
#: static/js/editor/blocks/dynamic-data/utils.js:10
msgid "Custom"
msgstr "Personalizat"

#: framework/extensions/newsletter-subscribe/customizer.php:61,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:50
#: framework/extensions/newsletter-subscribe/dashboard-static/js/EditCredentials.js:199
msgid "List ID"
msgstr "ID listă"

#: framework/extensions/newsletter-subscribe/customizer.php:79,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:109
msgid "Name Field"
msgstr "Câmp nume"

#: framework/extensions/newsletter-subscribe/customizer.php:117,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:121
msgid "Name Label"
msgstr "Etichetă nume"

#: framework/extensions/newsletter-subscribe/customizer.php:119,
#: framework/extensions/newsletter-subscribe/extension.php:208,
#: framework/extensions/newsletter-subscribe/helpers.php:37,
#: framework/extensions/newsletter-subscribe/helpers.php:81,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:122,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:147
msgid "Your name"
msgstr "Numele tău"

#: framework/extensions/newsletter-subscribe/customizer.php:128,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:136
msgid "Mail Label"
msgstr "Etichetă e-mail"

#: framework/extensions/newsletter-subscribe/customizer.php:130,
#: framework/extensions/newsletter-subscribe/extension.php:209,
#: framework/extensions/newsletter-subscribe/helpers.php:41,
#: framework/extensions/newsletter-subscribe/helpers.php:82
msgid "Your email"
msgstr "E-mailul tău"

#: framework/extensions/newsletter-subscribe/customizer.php:137,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:142,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:414
msgid "Button Label"
msgstr "Etichetă buton"

#: framework/extensions/newsletter-subscribe/customizer.php:139,
#: framework/extensions/newsletter-subscribe/extension.php:203,
#: framework/extensions/newsletter-subscribe/helpers.php:31,
#: framework/extensions/newsletter-subscribe/helpers.php:76,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:143,
#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:21
msgid "Subscribe"
msgstr "Abonează-te"

#: framework/extensions/newsletter-subscribe/customizer.php:149,
#: framework/extensions/trending/customizer.php:516,
#: framework/premium/extensions/shortcuts/customizer.php:48,
#: framework/premium/extensions/shortcuts/customizer.php:113,
#: framework/premium/extensions/shortcuts/customizer.php:178,
#: framework/premium/extensions/shortcuts/customizer.php:237,
#: framework/premium/extensions/shortcuts/customizer.php:326,
#: framework/premium/extensions/shortcuts/customizer.php:388,
#: framework/premium/extensions/shortcuts/customizer.php:447,
#: framework/premium/extensions/shortcuts/customizer.php:517,
#: framework/premium/extensions/shortcuts/customizer.php:581,
#: framework/premium/extensions/shortcuts/customizer.php:645,
#: framework/premium/extensions/shortcuts/customizer.php:709,
#: framework/premium/extensions/shortcuts/customizer.php:996,
#: framework/premium/features/content-blocks/options/header.php:167,
#: framework/premium/features/content-blocks/options/hook.php:300,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:155,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:423,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:43
msgid "Visibility"
msgstr "Vizibilitate"

#: framework/extensions/newsletter-subscribe/customizer.php:160,
#: framework/extensions/trending/customizer.php:528,
#: framework/features/header/header-options.php:108,
#: framework/features/header/header-options.php:190,
#: framework/features/blocks/contact-info/options.php:24,
#: framework/features/blocks/search/options.php:168,
#: framework/premium/extensions/mega-menu/options.php:413,
#: framework/premium/extensions/shortcuts/customizer.php:62,
#: framework/premium/extensions/shortcuts/customizer.php:127,
#: framework/premium/extensions/shortcuts/customizer.php:192,
#: framework/premium/extensions/shortcuts/customizer.php:251,
#: framework/premium/extensions/shortcuts/customizer.php:341,
#: framework/premium/extensions/shortcuts/customizer.php:402,
#: framework/premium/extensions/shortcuts/customizer.php:461,
#: framework/premium/extensions/shortcuts/customizer.php:531,
#: framework/premium/extensions/shortcuts/customizer.php:595,
#: framework/premium/extensions/shortcuts/customizer.php:659,
#: framework/premium/extensions/shortcuts/customizer.php:723,
#: framework/premium/extensions/shortcuts/customizer.php:861,
#: framework/premium/extensions/shortcuts/customizer.php:918,
#: framework/premium/extensions/shortcuts/customizer.php:1010,
#: framework/features/header/items/account/options.php:532,
#: framework/features/header/items/account/options.php:995,
#: framework/premium/features/content-blocks/options/header.php:178,
#: framework/premium/features/content-blocks/options/hook.php:311,
#: framework/premium/features/content-blocks/options/popup.php:506,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:70,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:168,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:396,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:651,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:436,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:42,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:61,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:80,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:98,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:90,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:258,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:286,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:49,
#: framework/premium/features/premium-header/items/contacts/options.php:32,
#: framework/premium/features/premium-header/items/contacts/options.php:553,
#: framework/premium/features/premium-header/items/language-switcher/options.php:131,
#: framework/premium/features/premium-header/items/search-input/options.php:157,
#: framework/premium/features/premium-header/items/search-input/options.php:250,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:56,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:104,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:105
msgid "Desktop"
msgstr "Desktop"

#: framework/extensions/newsletter-subscribe/customizer.php:161,
#: framework/extensions/trending/customizer.php:529,
#: framework/features/blocks/contact-info/options.php:25,
#: framework/features/blocks/search/options.php:169,
#: framework/premium/extensions/shortcuts/customizer.php:63,
#: framework/premium/extensions/shortcuts/customizer.php:128,
#: framework/premium/extensions/shortcuts/customizer.php:193,
#: framework/premium/extensions/shortcuts/customizer.php:252,
#: framework/premium/extensions/shortcuts/customizer.php:342,
#: framework/premium/extensions/shortcuts/customizer.php:403,
#: framework/premium/extensions/shortcuts/customizer.php:462,
#: framework/premium/extensions/shortcuts/customizer.php:532,
#: framework/premium/extensions/shortcuts/customizer.php:596,
#: framework/premium/extensions/shortcuts/customizer.php:660,
#: framework/premium/extensions/shortcuts/customizer.php:724,
#: framework/premium/extensions/shortcuts/customizer.php:862,
#: framework/premium/extensions/shortcuts/customizer.php:919,
#: framework/premium/extensions/shortcuts/customizer.php:1011,
#: framework/features/header/items/account/options.php:533,
#: framework/features/header/items/account/options.php:996,
#: framework/features/header/items/account/options.php:2069,
#: framework/premium/features/content-blocks/options/header.php:179,
#: framework/premium/features/content-blocks/options/hook.php:312,
#: framework/premium/features/content-blocks/options/popup.php:507,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:71,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:452,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:169,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:397,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:652,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:437,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:43,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:62,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:81,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:99,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:91,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:259,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:287,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:50,
#: framework/premium/features/premium-header/items/contacts/options.php:33,
#: framework/premium/features/premium-header/items/contacts/options.php:497,
#: framework/premium/features/premium-header/items/contacts/options.php:554,
#: framework/premium/features/premium-header/items/language-switcher/options.php:132,
#: framework/premium/features/premium-header/items/language-switcher/options.php:261,
#: framework/premium/features/premium-header/items/search-input/options.php:158,
#: framework/premium/features/premium-header/items/search-input/options.php:251,
#: framework/premium/features/premium-header/items/search-input/options.php:947,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:57,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:105,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:655,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:106,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:589
msgid "Tablet"
msgstr "Tabletă"

#: framework/extensions/newsletter-subscribe/customizer.php:162,
#: framework/extensions/trending/customizer.php:530,
#: framework/features/header/header-options.php:110,
#: framework/features/header/header-options.php:192,
#: framework/features/blocks/contact-info/options.php:26,
#: framework/features/blocks/contact-info/options.php:202,
#: framework/features/blocks/search/options.php:170,
#: framework/premium/extensions/mega-menu/options.php:414,
#: framework/premium/extensions/shortcuts/customizer.php:64,
#: framework/premium/extensions/shortcuts/customizer.php:129,
#: framework/premium/extensions/shortcuts/customizer.php:194,
#: framework/premium/extensions/shortcuts/customizer.php:253,
#: framework/premium/extensions/shortcuts/customizer.php:343,
#: framework/premium/extensions/shortcuts/customizer.php:404,
#: framework/premium/extensions/shortcuts/customizer.php:463,
#: framework/premium/extensions/shortcuts/customizer.php:533,
#: framework/premium/extensions/shortcuts/customizer.php:597,
#: framework/premium/extensions/shortcuts/customizer.php:661,
#: framework/premium/extensions/shortcuts/customizer.php:725,
#: framework/premium/extensions/shortcuts/customizer.php:863,
#: framework/premium/extensions/shortcuts/customizer.php:920,
#: framework/premium/extensions/shortcuts/customizer.php:1012,
#: framework/features/header/items/account/options.php:534,
#: framework/features/header/items/account/options.php:997,
#: framework/features/header/items/account/options.php:2070,
#: framework/premium/features/content-blocks/options/header.php:180,
#: framework/premium/features/content-blocks/options/hook.php:313,
#: framework/premium/features/content-blocks/options/popup.php:508,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:72,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:453,
#: framework/premium/extensions/post-types-extra/features/filtering/customizer.php:170,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:398,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:653,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:438,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:44,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:63,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:82,
#: framework/premium/extensions/woocommerce-extra/features/floating-cart/options.php:100,
#: framework/premium/extensions/woocommerce-extra/features/offcanvas-filters/options.php:92,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:260,
#: framework/premium/extensions/woocommerce-extra/features/product-gallery/feature.php:288,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:51,
#: framework/premium/features/premium-header/items/contacts/options.php:34,
#: framework/premium/features/premium-header/items/contacts/options.php:172,
#: framework/premium/features/premium-header/items/contacts/options.php:498,
#: framework/premium/features/premium-header/items/contacts/options.php:555,
#: framework/premium/features/premium-header/items/language-switcher/options.php:133,
#: framework/premium/features/premium-header/items/language-switcher/options.php:262,
#: framework/premium/features/premium-header/items/search-input/options.php:159,
#: framework/premium/features/premium-header/items/search-input/options.php:252,
#: framework/premium/features/premium-header/items/search-input/options.php:948,
#: framework/premium/extensions/post-types-extra/features/read-time/read-progress/customizer.php:58,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:106,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:656,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:107,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:590
msgid "Mobile"
msgstr "Telefon"

#: framework/extensions/newsletter-subscribe/customizer.php:175,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:183,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:420,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:138,
#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:146
msgid "Title Color"
msgstr "Culoare titlu"

#: framework/premium/extensions/woocommerce-extra/features/quick-view/options.php:197
msgid "Description Color"
msgstr "Culoare descriere"

#: framework/extensions/newsletter-subscribe/customizer.php:227,
#: framework/features/header/items/account/options.php:1553,
#: framework/premium/features/premium-header/items/search-input/options.php:266,
#: framework/premium/features/premium-header/items/search-input/options.php:295,
#: framework/premium/features/premium-header/items/search-input/options.php:327,
#: framework/premium/features/premium-header/items/search-input/options.php:357
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:128
#: static/js/editor/blocks/search/Edit.js:196
msgid "Input Font Color"
msgstr "Culoare font câmp"

#: framework/extensions/newsletter-subscribe/customizer.php:251,
#: framework/extensions/newsletter-subscribe/customizer.php:282,
#: framework/extensions/newsletter-subscribe/customizer.php:318,
#: framework/features/header/items/account/options.php:1579,
#: framework/features/header/items/account/options.php:1617,
#: framework/features/header/items/account/options.php:1663,
#: framework/premium/features/premium-header/items/search-input/options.php:319,
#: framework/premium/features/premium-header/items/search-input/options.php:350,
#: framework/premium/features/premium-header/items/search-input/options.php:380,
#: framework/premium/features/premium-header/items/search-input/options.php:449,
#: framework/premium/features/premium-header/items/search-input/options.php:480,
#: framework/premium/features/premium-header/items/search-input/options.php:510,
#: framework/premium/features/premium-header/items/search-input/options.php:579,
#: framework/premium/features/premium-header/items/search-input/options.php:610,
#: framework/premium/features/premium-header/items/search-input/options.php:640,
#: framework/premium/features/premium-header/items/search-input/options.php:713,
#: framework/premium/features/premium-header/items/search-input/options.php:743,
#: framework/premium/features/premium-header/items/search-input/options.php:773
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:210
#: static/js/editor/blocks/search/Edit.js:269
msgid "Focus"
msgstr "Focalizare"

#: framework/extensions/newsletter-subscribe/customizer.php:259,
#: framework/features/header/items/account/options.php:1588,
#: framework/premium/features/premium-header/items/search-input/options.php:526,
#: framework/premium/features/premium-header/items/search-input/options.php:555,
#: framework/premium/features/premium-header/items/search-input/options.php:587,
#: framework/premium/features/premium-header/items/search-input/options.php:617
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:159
#: static/js/editor/blocks/search/Edit.js:223
msgid "Input Border Color"
msgstr "Culoare margine câmp"

#: framework/extensions/newsletter-subscribe/customizer.php:296,
#: framework/features/header/items/account/options.php:1631,
#: framework/premium/features/premium-header/items/search-input/options.php:662,
#: framework/premium/features/premium-header/items/search-input/options.php:690,
#: framework/premium/features/premium-header/items/search-input/options.php:720,
#: framework/premium/features/premium-header/items/search-input/options.php:750
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/Edit.js:190
#: static/js/editor/blocks/search/Edit.js:250
msgid "Input Background Color"
msgstr "Culoare fundal câmp"

#: framework/extensions/newsletter-subscribe/customizer.php:357,
#: framework/extensions/trending/customizer.php:903,
#: framework/premium/extensions/shortcuts/customizer.php:1335,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:525,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:209
msgid "Container Background"
msgstr "Fundal container"

#: framework/extensions/newsletter-subscribe/customizer.php:373,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:193
msgid "Container Border"
msgstr "Margine container"

#: framework/extensions/newsletter-subscribe/customizer.php:408,
#: framework/extensions/trending/customizer.php:919
msgid "Container Inner Spacing"
msgstr "Spațiere interioară container"

#: framework/extensions/newsletter-subscribe/customizer.php:422,
#: framework/premium/extensions/shortcuts/customizer.php:1368,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:237
msgid "Container Border Radius"
msgstr "Raza marginii containerului"

#: framework/extensions/newsletter-subscribe/extension.php:155
msgid "Disable Subscribe Form"
msgstr "Dezactivează formularul de abonare"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:253,
#: framework/features/blocks/about-me/options.php:58,
#: framework/features/header/items/account/options.php:578,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:167
#: static/js/editor/blocks/tax-query/edit/TermsInspectorControls.js:128
msgid "Name"
msgstr "Nume"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:262,
#: framework/features/blocks/contact-info/options.php:391,
#: framework/features/blocks/dynamic-data/options.php:143,
#: framework/features/blocks/share-box/options.php:97,
#: framework/features/header/modal/register.php:47,
#: framework/premium/extensions/shortcuts/customizer.php:136,
#: framework/premium/extensions/shortcuts/customizer.php:162,
#: framework/premium/extensions/shortcuts/views/bar.php:48,
#: framework/premium/features/premium-header/items/contacts/options.php:308,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/waitlist-users-table.php:168
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderOverview.js:23
msgid "Email"
msgstr "E-mail"

#: framework/extensions/product-reviews/extension.php:540,
#: framework/extensions/product-reviews/extension.php:541,
#: framework/extensions/product-reviews/extension.php:544,
#: framework/extensions/product-reviews/extension.php:546
msgid "Product Reviews"
msgstr "Recenzii produse"

#: framework/extensions/product-reviews/config.php:6
msgid "This extension lets you easily create an affiliate marketing type of website by giving you options to create a personalized product review and use your affiliate links to direct your readers to the purchase page."
msgstr "Această extensie îți permite să creezi cu ușurință un tip de site pentru marketing afiliat, oferindu-ți opțiuni pentru a crea o recenzie personalizată a produsului și pentru a utiliza linkurile de afiliere pentru a direcționa cititorii către pagina de achiziție."

#: framework/extensions/product-reviews/extension.php:318,
#: framework/extensions/product-reviews/views/single-top.php:139
msgid "Overall Score"
msgstr "Scor general"

#: framework/extensions/product-reviews/extension.php:376
msgid "Review Summary"
msgstr "Rezumat recenzie"

#: framework/extensions/product-reviews/extension.php:387
msgid "Scores Box Width"
msgstr "Lățimea casetei scorurilor"

#: framework/extensions/product-reviews/extension.php:397
msgid "Read More Button"
msgstr "Buton Citește mai mult"

#: framework/extensions/product-reviews/extension.php:408
msgid "Buy Now Button"
msgstr "Buton “Cumpăra acum!”"

#: framework/extensions/product-reviews/extension.php:256,
#: framework/extensions/product-reviews/extension.php:427
msgid "Star Rating Color"
msgstr "Culoare evaluare stele"

#: framework/extensions/product-reviews/extension.php:274,
#: framework/extensions/product-reviews/extension.php:444,
#: framework/extensions/product-reviews/extension.php:472,
#: framework/extensions/product-reviews/extension.php:493,
#: framework/premium/extensions/mega-menu/options.php:855,
#: framework/features/header/items/account/options.php:1985,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:221,
#: framework/premium/extensions/woocommerce-extra/features/shipping-progress/options.php:227,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:88,
#: framework/premium/extensions/woocommerce-extra/features/stock-scarcity/options.php:94,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:286,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:330,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:375,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:414,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:454,
#: framework/premium/extensions/woocommerce-extra/features/swatches/options.php:498
#: static/js/dashboard/screens/Extensions/Sidebar.js:120
msgid "Active"
msgstr "Activ"

#: framework/extensions/product-reviews/extension.php:280,
#: framework/extensions/product-reviews/extension.php:450
msgid "Inactive"
msgstr "Inactiv"

#: framework/extensions/product-reviews/extension.php:458
msgid "Overll Score Text"
msgstr "Text scor general"

#: framework/extensions/product-reviews/extension.php:480
msgid "Overll Score Background"
msgstr "Fundal scor general"

#: framework/extensions/product-reviews/extension.php:545
msgid "Product Review"
msgstr "Recenzie produs"

#: framework/extensions/product-reviews/extension.php:547
msgid "Parent Product Review"
msgstr "Recenzie produs părinte"

#: framework/extensions/product-reviews/extension.php:548
msgid "All Reviews"
msgstr "Toate recenziile"

#: framework/extensions/product-reviews/extension.php:549
msgid "View Product Review"
msgstr "Vezi recenzia produsului"

#: framework/extensions/product-reviews/extension.php:550
msgid "Add New Product Review"
msgstr "Adaugă recenzie nouă produsAdaugă recenzie nouă produs"

#: framework/extensions/product-reviews/extension.php:551
msgid "Add New Review"
msgstr "Adaugă recenzie nouă"

#: framework/extensions/product-reviews/extension.php:552
msgid "Edit Product Review"
msgstr "Editează recenzia produsului"

#: framework/extensions/product-reviews/extension.php:553
msgid "Update Product Review"
msgstr "Actualizează recenzia produsului"

#: framework/extensions/product-reviews/extension.php:554
msgid "Search Product Review"
msgstr "Caută recenzie produs"

#: framework/extensions/product-reviews/extension.php:555
msgid "Not Found"
msgstr "Nu a fost găsit"

#: framework/extensions/product-reviews/extension.php:556
msgid "Not found in Trash"
msgstr "Nu a fost găsit în coșul de gunoi"

#: framework/extensions/product-reviews/extension.php:590,
#: framework/extensions/product-reviews/extension.php:600,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:100,
#: framework/premium/extensions/woocommerce-extra/features/filters/includes/active-filters.php:190
msgid "Categories"
msgstr "Categorii"

#: framework/extensions/product-reviews/extension.php:591,
#: framework/extensions/trending/customizer.php:36,
#: framework/extensions/trending/customizer.php:117
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/active-filters/index.js:88
msgid "Category"
msgstr "Categorie"

#: framework/extensions/product-reviews/extension.php:592
msgid "Search Category"
msgstr "Caută categorie"

#: framework/extensions/product-reviews/extension.php:593
#: static/js/dashboard/screens/DemoInstall/filters/DemoListFilters.js:88
msgid "All Categories"
msgstr "Toate categoriile"

#: framework/extensions/product-reviews/extension.php:594
msgid "Parent Category"
msgstr "Categorie părinte"

#: framework/extensions/product-reviews/extension.php:595
msgid "Parent Category:"
msgstr "Categorie părinte:"

#: framework/extensions/product-reviews/extension.php:596
msgid "Edit Category"
msgstr "Editează categoria"

#: framework/extensions/product-reviews/extension.php:597
msgid "Update Category"
msgstr "Actualizează categoria"

#: framework/extensions/product-reviews/extension.php:598
msgid "Add New Category"
msgstr "Adaugă o categorie nouă"

#: framework/extensions/product-reviews/extension.php:599
msgid "New Category Name"
msgstr "Nume categorie nouă"

#. translators: %s is the theme name.
#: framework/extensions/product-reviews/extension.php:618
msgid "%s Settings"
msgstr "%s Setări"

#: framework/dashboard.php:476, framework/dashboard.php:477,
#: framework/extensions/product-reviews/extension.php:619
msgid "Blocksy"
msgstr "Blocksy"

#: framework/extensions/product-reviews/helpers.php:30,
#: framework/extensions/product-reviews/metabox.php:181,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:230,
#: framework/premium/extensions/woocommerce-extra/features/compare/views/table.php:394
#: static/js/editor/blocks/dynamic-data/hooks/use-dynamic-data-descriptor.js:132
msgid "Rating"
msgstr "Notă"

#: framework/extensions/product-reviews/metabox.php:10
msgid "Review Entity"
msgstr "Entitate recenzie"

#: framework/extensions/product-reviews/metabox.php:18,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/table.php:39,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/includes/views/table.php:34,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-back-in-stock.php:52,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-confirm-subscription.php:33,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/templates/emails/waitlist-subscription-confirmed.php:33
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/ct-order/OrderDetails.js:11
msgid "Product"
msgstr "Produs"

#: framework/extensions/product-reviews/metabox.php:19
msgid "Book"
msgstr "Carte"

#: framework/extensions/product-reviews/metabox.php:21
msgid "Creative Work Season"
msgstr "Sezon operă creativă"

#: framework/extensions/product-reviews/metabox.php:22
msgid "Creative Work Series"
msgstr "Serii operă creativă"

#: framework/extensions/product-reviews/metabox.php:23
msgid "Episode"
msgstr "Episod"

#: framework/extensions/product-reviews/metabox.php:25
msgid "Game"
msgstr "Joc video"

#: framework/extensions/product-reviews/metabox.php:27
msgid "Local Business"
msgstr "Afacere locală"

#: framework/extensions/product-reviews/metabox.php:28
msgid "Media Object"
msgstr "Obiect media"

#: framework/extensions/product-reviews/metabox.php:29
msgid "Movie"
msgstr "Film"

#: framework/extensions/product-reviews/metabox.php:30
msgid "Music Playlist"
msgstr "Listă de redare"

#: framework/extensions/product-reviews/metabox.php:31
msgid "Music Recording"
msgstr "Înregistrare muzicală"

#: framework/extensions/product-reviews/metabox.php:32
msgid "Organization"
msgstr "Organizație"

#: framework/extensions/product-reviews/metabox.php:38
msgid "More info about review entity and how to choose the right one can be found %shere%s."
msgstr "Mai multe informații despre entitatea recenziei și cum să o alegi corect pot fi găsite %saici%s."

#: framework/extensions/product-reviews/metabox.php:69
msgid "Product Price"
msgstr "Preț produs"

#: framework/extensions/product-reviews/metabox.php:76
msgid "Product SKU"
msgstr "Cod produs"

#: framework/extensions/product-reviews/metabox.php:83
msgid "Product Brand"
msgstr "Brand produs"

#: framework/extensions/product-reviews/metabox.php:96
msgid "Gallery"
msgstr "Galerie"

#: framework/extensions/product-reviews/metabox.php:107
msgid "Affiliate Button Label"
msgstr "Etichetă buton afiliat"

#: framework/extensions/product-reviews/metabox.php:109,
#: framework/extensions/product-reviews/views/single-top.php:156
msgid "Buy Now"
msgstr "Cumpără acum"

#: framework/extensions/product-reviews/metabox.php:114
msgid "Affiliate Link"
msgstr "Link afiliat"

#: framework/extensions/product-reviews/metabox.php:120
msgid "Open Link In New Tab"
msgstr "Deschide linkul într-o filă nouă"

#: framework/extensions/product-reviews/metabox.php:127
msgid "Sponsored Attribute"
msgstr "Atribut sponsorizat"

#: framework/extensions/product-reviews/metabox.php:150
msgid "Read More Button Label"
msgstr "Etichetă buton Citește mai mult"

#: framework/extensions/product-reviews/metabox.php:152,
#: framework/extensions/product-reviews/views/single-top.php:162
msgid "Read More"
msgstr "Citește mai mult"

#: framework/extensions/product-reviews/metabox.php:172
msgid "Short Description"
msgstr "Descriere scurtă"

#: framework/extensions/product-reviews/metabox.php:187
msgid "Scores"
msgstr "Note"

#: framework/extensions/product-reviews/metabox.php:227
msgid "Product specs"
msgstr "Specificații produs"

#: framework/extensions/product-reviews/metabox.php:252,
#: framework/extensions/product-reviews/views/single-top.php:262
msgid "Pros"
msgstr "Pro"

#: framework/extensions/product-reviews/metabox.php:272,
#: framework/extensions/product-reviews/views/single-top.php:285
msgid "Cons"
msgstr "Contra"

#: framework/extensions/trending/customizer.php:169
msgid "Trending Posts"
msgstr "Articole populare"

#: framework/extensions/trending/config.php:6
msgid "Highlight your most popular posts or products based on the number of comments or reviews they have gotten in the specified period of time."
msgstr "Evidențiază cele mai populare articole sau produse pe baza numărului de comentarii sau recenzii primite într-o anumită perioadă de timp."

#: framework/extensions/trending/customizer.php:8,
#: framework/features/blocks/search/options.php:16,
#: framework/premium/extensions/shortcuts/views/bar.php:51,
#: framework/premium/features/premium-header/items/search-input/options.php:14
msgid "Products"
msgstr "Produse"

#: framework/extensions/trending/customizer.php:37
msgid "All categories"
msgstr "Toate categoriile"

#: framework/extensions/trending/customizer.php:42
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/Edit.js:210
#: framework/premium/extensions/woocommerce-extra/static/js/blocks/woocommerce-filters/tax-filters/TaxonomySelector.js:34
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:66
#: static/js/editor/blocks/tax-query/Edit.js:156
msgid "Taxonomy"
msgstr "Taxonomie"

#: framework/extensions/trending/customizer.php:43
msgid "All taxonomies"
msgstr "Toate taxonomiile"

#: framework/extensions/trending/customizer.php:179
msgid "Module Title"
msgstr "Titlu modul"

#: framework/extensions/trending/customizer.php:182,
#: framework/extensions/trending/helpers.php:352
msgid "Trending now"
msgstr "Populare acum"

#: framework/extensions/trending/customizer.php:187
msgid "Module Title Tag"
msgstr "Etichetă titlu modul"

#: framework/extensions/trending/customizer.php:324,
#: framework/premium/extensions/woocommerce-extra/features/added-to-cart-popup/customizer-options.php:284
msgid "Source"
msgstr "Sursă"

#: framework/extensions/trending/customizer.php:329
#: static/js/editor/blocks/query/edit/layers/useTaxonomiesLayers.js:229
msgid "Taxonomies"
msgstr "Taxonomii"

#: framework/extensions/trending/customizer.php:330
msgid "Custom Query"
msgstr "Interogare personalizată"

#: framework/extensions/trending/customizer.php:354
msgid "Posts ID"
msgstr "ID-uri postări"

#: framework/extensions/trending/customizer.php:358
msgid "Separate posts ID by comma. How to find the %spost ID%s."
msgstr "Separă ID-urile articolelor prin virgulă. Cum să găsești %sID-ul articolului%s."

#: framework/extensions/trending/customizer.php:375
msgid "Trending From"
msgstr "Popular de la"

#: framework/extensions/trending/customizer.php:383
msgid "All Time"
msgstr "Din totdeauna"

#: framework/extensions/trending/customizer.php:384
msgid "Last 24 Hours"
msgstr "Ultimele 24 de ore"

#: framework/extensions/trending/customizer.php:385
msgid "Last 7 Days"
msgstr "Ultimele 7 zile"

#: framework/extensions/trending/customizer.php:386
msgid "Last Month"
msgstr "Ultima lună"

#: framework/features/header/account-modal.php:37,
#: framework/features/header/items/account/options.php:1053,
#: framework/features/header/items/account/views/logout.php:8
msgid "Login"
msgstr "Autentificare"

#: framework/features/header/account-modal.php:41
msgid "Sign Up"
msgstr "Înregistrare"

#: framework/features/header/account-modal.php:61
msgid "Back to login"
msgstr "Înapoi la autentificare"

#: framework/features/header/header-options.php:11
msgid "Sticky Functionality"
msgstr "Funcționalitate sticky"

#: framework/features/header/header-options.php:37
msgid "Only Main Row"
msgstr "Doar rândul principal"

#: framework/features/header/header-options.php:42
msgid "Top & Main Row"
msgstr "Rândurile de sus și din mijloc"

#: framework/features/header/header-options.php:47
msgid "All Rows"
msgstr "Toate rândurile"

#: framework/features/header/header-options.php:52
msgid "Main & Bottom Row"
msgstr "Rândurile din mijloc și de jos"

#: framework/features/header/header-options.php:57
msgid "Only Top Row"
msgstr "Doar rândul de sus"

#: framework/features/header/header-options.php:62
msgid "Only Bottom Row"
msgstr "Doar rândul de jos"

#: framework/features/header/header-options.php:77
msgid "Slide Down"
msgstr "Glisează în jos"

#: framework/features/header/header-options.php:78
msgid "Fade"
msgstr "Estompează"

#: framework/features/header/header-options.php:79
msgid "Auto Hide/Show"
msgstr "Ascunde/Afișează automat"

#: framework/features/header/header-options.php:97,
#: framework/features/header/header-options.php:179
msgid "Enable on"
msgstr "Activează pe"

#: framework/features/header/header-options.php:125
msgid "Transparent Functionality"
msgstr "Funcționalitate transparență"

#: framework/extensions/trending/customizer.php:561,
#: framework/features/header/header-options.php:163,
#: framework/premium/extensions/shortcuts/customizer.php:1017,
#: framework/premium/features/content-blocks/options/header.php:31,
#: framework/premium/features/content-blocks/options/hook.php:36,
#: framework/premium/features/content-blocks/options/maintenance.php:5,
#: framework/premium/features/content-blocks/options/nothing_found.php:29,
#: framework/premium/features/content-blocks/options/nothing_found.php:34,
#: framework/premium/features/content-blocks/options/popup.php:49,
#: framework/premium/features/content-blocks/options/single.php:10,
#: framework/premium/extensions/woocommerce-extra/features/compare/options.php:443,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/options.php:5,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:102,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/options.php:24
#: framework/premium/extensions/sidebars/static/js/SettingsManager.js:50
#: framework/premium/static/js/footer/EditConditions.js:97
#: static/js/header/EditConditions.js:103
msgid "Display Conditions"
msgstr "Condiții de afișare"

#: framework/premium/features/content-blocks/admin-ui.php:653
msgid "Hooks Locations"
msgstr "Locații hooks"

#: framework/premium/extensions/shortcuts/customizer.php:765,
#: framework/premium/features/content-blocks/admin-ui.php:342,
#: framework/premium/extensions/post-types-extra/features/related-slideshow/feature.php:12,
#: framework/premium/extensions/woocommerce-extra/features/related-slideshow/general-options.php:5
msgid "Type"
msgstr "Tip"

#: framework/premium/features/content-blocks/admin-ui.php:343
msgid "Location/Trigger"
msgstr "Locație/Declanșator"

#: framework/premium/features/content-blocks/admin-ui.php:344,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:80,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:143
msgid "Conditions"
msgstr "Condiții"

#: framework/premium/features/content-blocks/admin-ui.php:345
msgid "Output"
msgstr "Output"

#: framework/premium/features/content-blocks/admin-ui.php:346
msgid "Enable/Disable"
msgstr "Activează/Dezactivează"

#: framework/features/blocks/about-me/options.php:204,
#: framework/features/blocks/contact-info/options.php:564,
#: framework/features/blocks/share-box/options.php:160,
#: framework/features/blocks/socials/options.php:112,
#: framework/premium/extensions/shortcuts/customizer.php:980,
#: framework/premium/features/content-blocks/admin-ui.php:371,
#: framework/features/header/items/account/options.php:22,
#: framework/features/header/items/account/options.php:348,
#: framework/features/header/items/account/options.php:746,
#: framework/premium/features/content-blocks/options/hook.php:9,
#: framework/premium/features/content-blocks/options/hook.php:77,
#: framework/premium/features/content-blocks/options/hook.php:167,
#: framework/premium/features/content-blocks/options/popup.php:75,
#: framework/premium/features/content-blocks/options/popup.php:222,
#: framework/premium/features/content-blocks/options/popup.php:603,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/select.php:23,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:97,
#: framework/premium/features/premium-header/items/contacts/options.php:451
#: framework/premium/static/js/blocks/ContentBlock.js:56
#: static/js/editor/blocks/dynamic-data/components/InspectorControls.js:170
msgid "None"
msgstr "Nici unul"

#: framework/premium/features/content-blocks/admin-ui.php:372,
#: framework/premium/features/content-blocks/options/popup.php:76
msgid "On scroll"
msgstr "La derulare"

#: framework/premium/features/content-blocks/admin-ui.php:373,
#: framework/premium/features/content-blocks/options/popup.php:77
msgid "On scroll to element"
msgstr "La derulare spre element"

#: framework/premium/features/content-blocks/admin-ui.php:375,
#: framework/premium/features/content-blocks/options/popup.php:79
msgid "On page load"
msgstr "La încărcarea paginii"

#: framework/premium/features/content-blocks/admin-ui.php:376,
#: framework/premium/features/content-blocks/options/popup.php:80
msgid "After inactivity"
msgstr "După inactivitate"

#: framework/premium/features/content-blocks/admin-ui.php:377,
#: framework/premium/features/content-blocks/options/popup.php:81
msgid "After x time"
msgstr "După x timp"

#: framework/premium/features/content-blocks/admin-ui.php:379,
#: framework/premium/features/content-blocks/options/popup.php:83
msgid "On page exit intent"
msgstr "La intenția de ieșire din pagină"

#: framework/premium/features/content-blocks/admin-ui.php:383
msgid "Down"
msgstr "Jos"

#: framework/premium/features/content-blocks/admin-ui.php:384
msgid "Up"
msgstr "Sus"

#: framework/premium/features/content-blocks.php:193,
#: framework/premium/features/content-blocks.php:199
msgid "Content Blocks"
msgstr "Blocuri de conținut"

#: framework/premium/features/content-blocks.php:194,
#: framework/premium/extensions/mega-menu/options.php:346,
#: framework/premium/features/content-blocks/content-block-layer.php:163,
#: framework/premium/features/content-blocks/content-block-layer.php:214,
#: framework/features/header/items/account/options.php:247,
#: framework/premium/features/premium-header/items/content-block/config.php:4
#: framework/premium/static/js/blocks/ContentBlock.js:78
msgid "Content Block"
msgstr "Content Block"

#: framework/premium/features/content-blocks.php:195,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:138,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:482,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:273
msgid "Add New"
msgstr "Adaugă nou"

#: framework/premium/features/content-blocks.php:196
msgid "Add New Content Block"
msgstr "Adaugă bloc de conținut nou"

#: framework/premium/features/content-blocks.php:197
#: framework/premium/static/js/blocks/ContentBlock.js:89
msgid "Edit Content Block"
msgstr "Editează blocul de conținut"

#: framework/premium/features/content-blocks.php:198
#: framework/premium/static/js/hooks/CreateHook.js:33
msgid "New Content Block"
msgstr "Bloc de conținut nou"

#: framework/premium/features/content-blocks.php:200
msgid "View Content Block"
msgstr "Vizualizează blocul de conținut"

#: framework/premium/features/content-blocks.php:201
msgid "Search Content Blocks"
msgstr "Caută blocuri de conținut"

#: framework/premium/features/content-blocks.php:202,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:145,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:489,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:280
msgid "Nothing found"
msgstr "Nimic găsit"

#: framework/premium/features/content-blocks.php:203,
#: framework/premium/extensions/woocommerce-extra/features/custom-tabs/feature.php:146,
#: framework/premium/extensions/woocommerce-extra/features/custom-thank-you-page/feature.php:490,
#: framework/premium/extensions/woocommerce-extra/features/size-guide/feature.php:281
msgid "Nothing found in Trash"
msgstr "Nimic găsit în coșul de gunoi"

#: framework/premium/features/premium-footer.php:14,
#: framework/premium/features/premium-footer.php:28
msgid "Footer Menu 1"
msgstr "Meniu subsol 1"

#: framework/premium/features/premium-footer.php:29,
#: framework/premium/features/premium-footer/items/menu-secondary/config.php:4
msgid "Footer Menu 2"
msgstr "Meniu subsol 2"

#: framework/premium/features/premium-header.php:57
msgid "Header Menu 3"
msgstr "Meniu antet 3"

#: framework/premium/features/premium-header.php:202
msgid "Header Widget Area "
msgstr "Zonă widget antet "

#: framework/extensions/trending/customizer.php:251,
#: framework/premium/features/premium-header.php:258,
#: framework/premium/features/premium-header.php:339,
#: framework/premium/features/premium-header.php:359,
#: framework/premium/features/premium-header.php:376,
#: framework/premium/features/socials.php:31,
#: framework/features/blocks/contact-info/options.php:121,
#: framework/features/blocks/contact-info/options.php:186,
#: framework/features/blocks/contact-info/options.php:249,
#: framework/features/blocks/contact-info/options.php:312,
#: framework/features/blocks/contact-info/options.php:375,
#: framework/features/blocks/contact-info/options.php:438,
#: framework/features/blocks/contact-info/options.php:501,
#: framework/features/blocks/search/options.php:86,
#: framework/premium/extensions/mega-menu/options.php:463,
#: framework/features/header/items/account/options.php:347,
#: framework/features/header/items/account/options.php:404,
#: framework/features/header/items/account/options.php:745,
#: framework/features/header/items/account/options.php:785,
#: framework/premium/extensions/post-types-extra/features/dynamic-data/feature.php:693,
#: framework/premium/extensions/post-types-extra/features/read-time/estimated-read-time.php:20,
#: framework/premium/features/premium-header/items/contacts/options.php:114,
#: framework/premium/features/premium-header/items/contacts/options.php:159,
#: framework/premium/features/premium-header/items/contacts/options.php:204,
#: framework/premium/features/premium-header/items/contacts/options.php:249,
#: framework/premium/features/premium-header/items/contacts/options.php:295,
#: framework/premium/features/premium-header/items/contacts/options.php:340,
#: framework/premium/features/premium-header/items/contacts/options.php:385,
#: framework/premium/features/premium-header/items/search-input/options.php:79,
#: framework/premium/features/premium-header/items/language-switcher/options/dropdown.php:30,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:28,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:28
msgid "Icon"
msgstr "Pictogramă"

#: framework/premium/features/premium-header.php:277,
#: framework/features/blocks/about-me/options.php:168,
#: framework/features/blocks/contact-info/options.php:538,
#: framework/features/blocks/share-box/options.php:122,
#: framework/features/blocks/socials/options.php:74,
#: framework/premium/features/premium-header/items/contacts/options.php:426
msgid "Icons Size"
msgstr "Mărime pictograme"

#: framework/premium/features/premium-header.php:289,
#: framework/premium/extensions/mega-menu/options.php:509
msgid "Icon Position"
msgstr "Poziție pictograme"

#: framework/premium/features/premium-header.php:296,
#: framework/premium/extensions/mega-menu/options.php:516,
#: framework/premium/extensions/shortcuts/customizer.php:886,
#: framework/features/header/items/account/options.php:561,
#: framework/features/header/items/account/options.php:1027,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:95,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:129,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:130
msgid "Left"
msgstr "Stânga"

#: framework/premium/features/premium-header.php:297,
#: framework/premium/extensions/mega-menu/options.php:517,
#: framework/premium/extensions/shortcuts/customizer.php:887,
#: framework/features/header/items/account/options.php:562,
#: framework/features/header/items/account/options.php:1031,
#: framework/premium/extensions/color-mode-switch/header-items/color-mode-switcher/options.php:96,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:130,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:131
msgid "Right"
msgstr "Dreapta"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/view.php:14
#: framework/extensions/newsletter-subscribe/admin-static/js/newsletter-block/index.js:56
msgid "Newsletter"
msgstr "Newsletter"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:13,
#: framework/features/blocks/contact-info/options.php:39,
#: framework/premium/extensions/mega-menu/options.php:544,
#: framework/premium/extensions/shortcuts/customizer.php:1187,
#: framework/premium/extensions/shortcuts/customizer.php:1229,
#: framework/premium/extensions/shortcuts/customizer.php:1271,
#: framework/features/header/items/account/options.php:577,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:32,
#: framework/premium/extensions/woocommerce-extra/features/custom-badges/design-options.php:72,
#: framework/premium/extensions/woocommerce-extra/features/swatches/woo-tab-options.php:98,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:547,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:582,
#: framework/premium/extensions/woocommerce-extra/features/compare/header-items/compare/options.php:616,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:486,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:517,
#: framework/premium/extensions/woocommerce-extra/features/wish-list/header-items/wish-list/options.php:547
#: static/js/editor/blocks/breadcrumbs/Edit.js:54
#: static/js/editor/blocks/contact-info/Edit.js:107
msgid "Text"
msgstr "Text"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:19,
#: framework/features/blocks/about-me/options.php:67
msgid "You can add here some arbitrary HTML code."
msgstr "Poți adăuga aici un cod HTML arbitrar."

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:74
msgid "Container Style"
msgstr "Stil container"

#: framework/extensions/newsletter-subscribe/ct-newsletter-subscribe/options.php:82,
#: framework/premium/features/content-blocks/options/404.php:85,
#: framework/premium/features/content-blocks/options/archive.php:162,
#: framework/premium/features/content-blocks/options/header.php:103,
#: framework/premium/features/content-blocks/options/hook.php:212,
#: framework/premium/features/content-blocks/options/maintenance.php:82,
#: framework/premium/features/content-blocks/options/nothing_found.php:103,
#: framework/premium/features/content-blocks/options/single.php:94,
#: framework/premium/extensions/woocommerce-extra/features/product-waitlist/customizer-options.php:22
msgid "Boxed"
msgstr "Încasetat"

#: framework/extensions/product-reviews/views/single-top.php:238
msgid "Specs"
msgstr "Specificații"

#: framework/extensions/newsletter-subscribe/helpers.php:147
msgid "Email address"
msgstr "Email address"

#: framework/features/header/modal/login.php:28,
#: framework/features/header/modal/register.php:53
msgid "Password"
msgstr "Parolă"

#: framework/features/header/modal/login.php:53
msgid "Remember Me"
msgstr "Ține-mă minte"

#: framework/features/header/modal/login.php:57
msgid "Forgot Password?"
msgstr "Ai uitat parola?"

#: framework/features/header/modal/login.php:79
msgid "Log In"
msgstr "Autentificare"

#: framework/features/header/modal/login.php:23,
#: framework/features/header/modal/lostpassword.php:5
msgid "Username or Email Address"
msgstr "Username sau Adresa Email"

#: framework/features/header/modal/lostpassword.php:13
msgid "Get New Password"
msgstr "Cere o parola noua"

#: framework/features/header/modal/register.php:41
msgid "Username"
msgstr "Nume utilizator"

#: framework/features/header/modal/register.php:106
msgid "Registration confirmation will be emailed to you."
msgstr "Confirmarea înregistrării îți va fi trimisă prin e-mail."

#: framework/features/header/modal/register.php:112
msgid "Register"
msgstr "Înregistrare"

#: framework/helpers/exts-configs.php:60
msgid "Connect your Adobe Fonts project and use the selected fonts throughout Blocksy and your favorite page builder."
msgstr "Conectează proiectul Adobe Fonts și utilizează fonturile selectate în Blocksy și în constructorul tău de pagini preferat."

#: framework/helpers/exts-configs.php:71
msgid "Inject custom code snippets throughout your website. The extension works globally or on a per post/page basis."
msgstr "Inserează fragmente de cod personalizate pe tot site-ul. Extensia funcționează global sau pe bază de articol/pagină."

#: framework/premium/extensions/code-snippets/extension.php:42,
#: framework/premium/extensions/code-snippets/extension.php:97,
#: framework/premium/extensions/code-snippets/extension.php:142
msgid "After body open scripts"
msgstr "Scripturi după deschiderea corpului"

#: framework/helpers/exts-configs.php:97
msgid "Upload an unlimited number of custom fonts or variable fonts and use them throughout Blocksy and your favorite page builder."
msgstr "Încarcă un număr nelimitat de fonturi personalizate sau variabile și utilizează-le în Blocksy și în constructorul tău de pagini preferat."

#: framework/helpers/exts-configs.php:109
msgid "Serve your chosen Google Fonts from your own web server. This will increase the loading speed and makes sure your website complies with the privacy regulations."
msgstr "Servește fonturile Google alese de pe propriul server web. Acest lucru va crește viteza de încărcare și va asigura respectarea reglementărilor privind confidențialitatea."

#: framework/helpers/exts-configs.php:124
msgid "Create beautiful personalised menus that set your website apart from the others. Add icons and badges to your entries and even add Content Blocks inside your dropdowns."
msgstr "Creează meniuri personalizate superbe care îți diferențiază site-ul. Adaugă pictograme și insigne în intrările tale și chiar adaugă blocuri de conținut în meniurile derulante."

#: framework/premium/extensions/mega-menu/extension.php:160
msgid "Menu Item Settings"
msgstr "Setări element de meniu"