!function(){var t={184:function(t,e){var r;
/*!
  Copyright (c) 2017 <PERSON>.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/!function(){"use strict";var n={}.hasOwnProperty;function o(){for(var t=[],e=0;e<arguments.length;e++){var r=arguments[e];if(r){var i=typeof r;if("string"===i||"number"===i)t.push(r);else if(Array.isArray(r)&&r.length){var c=o.apply(null,r);c&&t.push(c)}else if("object"===i)for(var u in r)n.call(r,u)&&r[u]&&t.push(u)}}return t.join(" ")}t.exports?(o.default=o,t.exports=o):void 0===(r=function(){return o}.apply(e,[]))||(t.exports=r)}()}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={exports:{}};return t[n](i,i.exports,r),i.exports}r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,{a:e}),e},r.d=function(t,e){for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},function(){"use strict";function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,r||"default");if("object"!=t(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==t(r)?r:r+""}function n(t,r,n){return(r=e(r))in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n,t}var o=window.wp.element,i=window.wp.hooks;function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function u(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,c,u=[],a=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;a=!1}else for(;!(a=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);a=!0);}catch(t){s=!0,o=t}finally{try{if(!a&&null!=r.return&&(c=r.return(),Object(c)!==c))return}finally{if(s)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return c(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?c(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}window.ctEvents;var a=window.blocksyOptions,s=r(184),l=r.n(s),f=window.wp.i18n;function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function y(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach((function(e){n(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var b=function(t){var e=t.setExtsStatus,r=t.extension,i=t.onExtsSync,c=u((0,o.useState)(null),2),s=c[0],p=c[1];return(0,o.createElement)("div",{className:l()("ct-extension-options ct-product-reviews-options")},(0,o.createElement)("h4",null,(0,f.__)("Product Reviews Settings","blocksy-companion")),(0,o.createElement)("p",{className:"ct-modal-description"},(0,f.__)("Configure the slugs for single and category pages of the product review custom post type.","blocksy-companion")),(0,o.createElement)("form",null,(0,o.createElement)(a.OptionsPanel,{onChange:function(t,e){return p((function(r){return y(y({},r),{},n({},t,e))}))},options:{single_slug:{type:"text",value:"",label:(0,f.__)("Single Slug","blocksy-companion")},category_slug:{type:"text",value:"",label:(0,f.__)("Category Slug","blocksy-companion")}},value:y(y({},r.data.settings),s||{}),hasRevertButton:!1}),(0,o.createElement)("button",{className:"ct-button-primary",disabled:!s,onClick:function(t){if(t.preventDefault(),s){var o=y(y({},r.data.settings),s);e((function(t){return y(y({},t),{},n({},r.name,y(y({},t[r.name]),{},{data:y(y({},t[r.name].data),{},{settings:o})})))})),i({extAction:{type:"persist",settings:o}}),p(null)}}},(0,f.__)("Save Settings","blocksy-companion"))))};function m(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function v(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?m(Object(r),!0).forEach((function(e){n(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}(0,i.addFilter)("blocksy.extensions.current_extension_content","blocksy",(function(t,e){var r=e.extension,n=e.onExtsSync,i=e.setExtsStatus;return"product-reviews"!==r.name?t:v(v({},t),{},{content:(0,o.createElement)(b,{setExtsStatus:i,extension:r,onExtsSync:n})})}))}()}();