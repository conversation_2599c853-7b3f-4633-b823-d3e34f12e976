/**
 * Document Analizer Widget JS
 * Clone completo del Document Viewer con funzionalità avanzate
 * Basato sullo stile del Chat Model Widget per coerenza
 */
jQuery(document).ready(function ($) {
    console.log('✅ Document Analizer Widget JS caricato');

    // Verifica che i parametri siano disponibili
    if (typeof documentAnalizerParams === 'undefined') {
        console.warn('⚠️ Document Analizer: Parametri AJAX non disponibili');
        return;
    }

    // Variabili principali (clone dal document-viewer.js)
    let zoomLevel = 1;
    let currentDocumentFile = null;
    let currentPdfFile = null;
    let currentDocumentTitle = '';
    let currentAnalysisResults = '';
    let extractedDocumentContent = '';
    let customLogo = null;
    let analysisDisplayed = false;
    let preventReloadOnSuccess = false;
    let documentDescription = '';
    let ocrProcessor = null;
    let isExtracting = false; // Flag per prevenire estrazioni duplicate
    let isSaving = false; // Flag per prevenire salvataggi duplicati
    let isAnalyzing = false; // Flag per prevenire analisi duplicate
    let isExportingPDF = false; // Flag per prevenire export PDF duplicati

    // Prefisso per gli ID degli elementi del Document Analizer
    const PREFIX = 'analizer-';

    console.log('🔧 Document Analizer: Inizializzazione variabili completata');

    // Funzione per calcolare e visualizzare la spesa stimata (clone del document-viewer)
    function updateEstimatedCost(tokenCount) {
        if (!tokenCount) return;
        
        let formattedCost = '€0,00';
        
        // Utilizzo prioritario del calcolo client-side
        if (window.documentStats && typeof window.documentStats.calculateCost === 'function') {
            const costValue = window.documentStats.calculateCost(tokenCount);
            formattedCost = '€' + costValue;
            console.log('[Document Analizer] Spesa stimata calcolata:', formattedCost, 'per', tokenCount, 'token');
        } else {
            // Fallback al calcolo locale
            const costRate = 0.01; // €0.01 per token
            const estimatedCost = tokenCount * costRate;
            formattedCost = '€' + estimatedCost.toFixed(2).replace('.', ',');
            console.log('[Document Analizer] Spesa stimata locale:', formattedCost, 'per', tokenCount, 'token');
        }
        
        // Aggiorna il costo stimato nell'interfaccia utente del Document Analizer (usando l'ID corretto)
        const costElement = $('#analizer-cost-estimate');
        console.log('[Document Analizer] Looking for cost element: #analizer-cost-estimate, Found:', costElement.length);

        if (costElement.length) {
            costElement.text(formattedCost);
            costElement.addClass('cost-updated');
            setTimeout(() => {
                costElement.removeClass('cost-updated');
            }, 3000);
            console.log('💰 Document Analizer: Costo stimato aggiornato:', formattedCost);
        } else {
            console.warn('⚠️ Document Analizer: Elemento costo stimato non trovato: #analizer-cost-estimate');
        }
        return formattedCost;
    }

    // Funzione per estrarre i punti chiave dall'analisi completa
    function extractKeyPoints(analysisHtml) {
        // Rimuovi tutti i tag HTML per lavorare con testo puro
        let plainText = analysisHtml.replace(/<[^>]*>/g, ' ');

        // Rimuovi i simboli &nbsp; sostituendoli con spazi normali
        plainText = plainText.replace(/&nbsp;/g, ' ');

        // Rimuovi spazi multipli
        plainText = plainText.replace(/\s+/g, ' ').trim();

        // Se il testo è troppo corto, restituisci un messaggio
        if (plainText.length < 100) {
            return '<p><em>Analisi troppo breve per estrarre punti chiave significativi.</em></p>';
        }

        // Dividi il testo in frasi
        const sentences = plainText.split(/[.!?]+/).filter(s => s.trim().length > 20);

        // Prendi le prime 5-7 frasi più significative (quelle più lunghe tendono ad essere più informative)
        const keyPoints = sentences
            .sort((a, b) => b.length - a.length)
            .slice(0, Math.min(7, Math.ceil(sentences.length / 3)))
            .sort((a, b) => plainText.indexOf(a) - plainText.indexOf(b)); // Riordina secondo l'ordine originale

        // Formatta come lista HTML
        let keyPointsHtml = '<div class="key-points-container">';
        keyPointsHtml += '<h4>Punti Chiave dell\'Analisi:</h4>';
        keyPointsHtml += '<ul class="key-points-list">';

        keyPoints.forEach(point => {
            const cleanPoint = point.trim();
            if (cleanPoint.length > 10) {
                keyPointsHtml += `<li>${cleanPoint}.</li>`;
            }
        });

        keyPointsHtml += '</ul></div>';

        return keyPointsHtml;
    }

    // Funzione per creare l'effetto di digitazione del testo
    function typeContentWithEffect(content) {
        // Verifica che ci sia un contenuto valido
        if (!content) {
            $('#analysis-content').html('<p>Nessun contenuto disponibile per l\'analisi.</p>');
            return;
        }

        // Rimuovi i simboli &nbsp; sostituendoli con spazi normali
        content = content.replace(/&nbsp;/g, ' ');

        // Rimuovi il carattere \ prima degli apostrofi
        content = content.replace(/\\'/g, "'");

        // Correggi altri caratteri speciali che potrebbero apparire nel testo
        content = content.replace(/\\"/g, '"');
        content = content.replace(/\\\\/g, '\\');

        // Rimuovi riferimenti a commit GitHub
        content = content.replace(/\[([0-9a-f]+)\]\(https:\/\/github\.com\/.*?\/commit\/[0-9a-f]+\)/g, '');

        // Elimina asterischi e cancelletti dal contenuto dell'analisi
        content = content.replace(/\*/g, '');
        content = content.replace(/#/g, '');

        // Aggiungi CSS necessario per l'effetto di digitazione
        const typingCSS = `
            <style>
                .typing-effect {
                    min-height: 200px;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;
                    overflow-y: visible;
                }

                .typing-effect .cursor {
                    display: inline-block;
                    width: 2px;
                    height: 18px;
                    background-color: #333;
                    animation: blink 1s infinite;
                    margin-left: 2px;
                    vertical-align: middle;
                }

                @keyframes blink {
                    0%, 100% { opacity: 1; }
                    50% { opacity: 0; }
                }

                .paragraph-break {
                    display: block;
                    height: 1em;
                }
            </style>
        `;

        // Aggiungi lo stile se non esiste già
        if (!$('style:contains(.typing-effect)').length) {
            $('head').append(typingCSS);
        }

        // Prepara il contenitore per l'effetto di digitazione
        const $container = $('#analysis-content');
        $container.empty().html('<span class="cursor"></span>');

        // Processa il contenuto HTML per preservare formattazione
        let paragraphs = [];

        // Strategia 1: Se il contenuto contiene tag HTML, estrai i paragrafi
        if (content.includes('<p>')) {
            // Estrai tutti i tag <p> con il loro contenuto
            const regex = /<p[^>]*>(.*?)<\/p>/gs;
            let match;
            while ((match = regex.exec(content)) !== null) {
                paragraphs.push(match[1].trim());
            }
        }
        // Strategia 2: Se non ci sono tag HTML, dividi per nuove linee
        else {
            paragraphs = content.split(/\n\s*\n|\r\n\s*\r\n|\r\s*\r/);
            paragraphs = paragraphs.filter(p => p.trim().length > 0);
        }

        // Se non abbiamo paragrafi, dividi il contenuto in frasi
        if (paragraphs.length === 0) {
            const sentences = content.split(/(?<=[.!?;])\s+/);
            paragraphs = sentences.filter(s => s.trim().length > 0);
        }

        // Se ancora non abbiamo contenuto, usa il testo così com'è
        if (paragraphs.length === 0) {
            paragraphs = [content];
        }

        // Variabili per l'effetto di typing
        let paragraphIndex = 0;
        let charIndex = 0;
        let currentText = '';
        const $cursor = $container.find('.cursor');

        // Imposta la velocità di digitazione (caratteri al secondo) - RALLENTATA
        const typingSpeed = 30; // Rallentato a 30 caratteri al secondo

        // Intervallo per l'effetto di digitazione
        const interval = setInterval(() => {
            // Se abbiamo finito tutti i paragrafi, ferma l'animazione
            if (paragraphIndex >= paragraphs.length) {
                clearInterval(interval);
                $cursor.remove(); // Rimuovi il cursore alla fine
                return;
            }

            // Paragrafo corrente
            const currentParagraph = paragraphs[paragraphIndex];

            // Se abbiamo finito il paragrafo corrente, passa al successivo
            if (charIndex >= currentParagraph.length) {
                paragraphIndex++;
                charIndex = 0;

                // Aggiungi una fine paragrafo e continua con il prossimo
                $cursor.before('<br><br>');

                // Se abbiamo finito tutti i paragrafi, ferma l'animazione
                if (paragraphIndex >= paragraphs.length) {
                    clearInterval(interval);
                    $cursor.remove(); // Rimuovi il cursore alla fine
                    return;
                }

                return; // Aspetta il prossimo ciclo per iniziare il nuovo paragrafo
            }

            // Prendi un gruppo di caratteri più piccolo per una digitazione più lenta e naturale
            const charsToType = Math.floor(Math.random() * 3) + 1; // Ridotto a 1-3 caratteri per volta
            const nextChars = currentParagraph.substr(charIndex, charsToType);
            charIndex += nextChars.length;

            // Inserisci i nuovi caratteri prima del cursore
            $cursor.before(nextChars);

        }, 1000 / typingSpeed);
    }

    // Inizializza OCR processor
    function initOcrProcessor() {
        if (!ocrProcessor && typeof DocumentOCR !== 'undefined') {
            ocrProcessor = new DocumentOCR();
            ocrProcessor.registerCallbacks(
                // Progress callback
                (progress) => {
                    showDocumentNotification('<div class="spinner"></div> Elaborazione OCR in corso... ' + progress + '%', 'processing');
                },
                // Completion callback
                ocrCompletionHandler,
                // Error callback
                (error) => {
                    hideDocumentNotification();
                    showDocumentNotification('Errore OCR: ' + error.message, 'error');
                    console.error('[Document Analizer] OCR error:', error);
                }
            );
        }
        return ocrProcessor;
    }

    // OCR completion callback
    function ocrCompletionHandler(extractedText) {
        extractedDocumentContent = extractedText ? extractedText.trim() : '';
        console.log('[Document Analizer] Testo estratto, lunghezza:', extractedDocumentContent.length);
        
        // Update the inline document info
        $('#' + PREFIX + 'document-info-inline').show();
        $('#' + PREFIX + 'document-chars-inline').html('<strong>Chars:</strong> ' + formatCharCount(extractedDocumentContent.length));
        
        const tokenCount = estimateTokenCount(extractedDocumentContent);
        $('#' + PREFIX + 'document-tokens-inline').html('<strong>Tokens:</strong> ' + formatCharCount(tokenCount));
        
        // Calcola e visualizza la spesa stimata
        updateEstimatedCost(tokenCount);
        
        showDocumentNotification('<strong>✓ Estrazione OCR completata!</strong> ' + 
            formatCharCount(extractedDocumentContent.length) + ' caratteri estratti', 'success');
        
        // Show successful extraction message
        $('#' + PREFIX + 'analysis-results').html(
            '<div class="extraction-success">' +
            '<p><strong>✓ Estrazione OCR completata!</strong></p>' +
            '<p>Sono stati estratti ' + formatCharCount(extractedDocumentContent.length) + ' caratteri.</p>' +
            '<p>Puoi ora inserire una descrizione o domanda e cliccare su "Analizza" per procedere.</p>' +
            '</div>'
        );
    }

    // Funzione per stimare il conteggio dei token
    function estimateTokenCount(text) {
        if (!text) return 0;
        // Stima approssimativa: i token sono ~4 caratteri in media per il testo inglese
        return Math.ceil(text.length / 4);
    }

    // Funzione per formattare il conteggio dei caratteri
    function formatCharCount(count) {
        if (count >= 1000) {
            return (count / 1000).toFixed(1) + 'k';
        }
        return count.toString();
    }

    // Funzione per mostrare notifiche nel Document Analizer
    function showDocumentNotification(message, type = 'success', autoDismiss = true) {
        const $notificationArea = $('#' + PREFIX + 'document-notification-area');
        const $notificationContent = $notificationArea.find('.notification-content');
        
        $notificationContent.html(message);
        $notificationArea.removeClass('error processing');
        
        if (type === 'error') {
            $notificationArea.addClass('error');
            autoDismiss = false;
        } else if (type === 'processing') {
            $notificationArea.addClass('processing');
            autoDismiss = false;
        }
        
        $notificationArea.fadeIn(300);
        
        if (autoDismiss) {
            clearTimeout($notificationArea.data('dismiss-timer'));
            const timer = setTimeout(function() {
                hideDocumentNotification();
            }, 4000);
            $notificationArea.data('dismiss-timer', timer);
        }
        
        return $notificationArea;
    }
    
    // Funzione per nascondere le notifiche
    function hideDocumentNotification() {
        $('#' + PREFIX + 'document-notification-area').fadeOut(300);
    }

    // Event handlers per il Document Analizer
    
    // Custom logo upload handling
    $('#' + PREFIX + 'custom-logo-upload').on('change', function() {
        const file = this.files[0];
        if (file) {
            if (!file.type.match('image.*')) {
                alert('Per favore seleziona un file immagine (JPG, PNG, GIF, etc.)');
                $(this).val('');
                $('#' + PREFIX + 'logo-preview').attr('src', '').hide();
                return;
            }
            
            customLogo = file;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    const imgWidth = this.width;
                    const imgHeight = this.height;
                    
                    let displayHeight = imgHeight;
                    let displayWidth = imgWidth;
                    
                    if (imgHeight > 50) {
                        const ratio = imgWidth / imgHeight;
                        displayHeight = 50;
                        displayWidth = Math.round(displayHeight * ratio);
                    }
                    
                    $('#' + PREFIX + 'logo-preview')
                        .attr('src', e.target.result)
                        .css({
                            'height': displayHeight + 'px',
                            'width': displayWidth + 'px',
                            'object-fit': 'contain',
                            'max-height': '50px'
                        })
                        .show();
                    
                    if (imgHeight > 50) {
                        $('#' + PREFIX + 'logo-dimensions-info').html(`L'immagine originale (${imgWidth}x${imgHeight}px) verrà ridimensionata a ${displayWidth}x${displayHeight}px per adattarsi al documento`).show();
                    } else {
                        $('#' + PREFIX + 'logo-dimensions-info').html(`Dimensioni logo: ${imgWidth}x${imgHeight}px`).show();
                    }
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        } else {
            $('#' + PREFIX + 'logo-preview').attr('src', '').hide();
            $('#' + PREFIX + 'logo-dimensions-info').hide();
            customLogo = null;
        }
    });

    // Analysis title field handling
    $('#' + PREFIX + 'analysis-title').on('change keyup', function() {
        currentDocumentTitle = $(this).val();
    });

    // Document upload handling
    $('#' + PREFIX + 'document-upload').on('change', function() {
        handleFileUpload(this.files[0], 'document');
    });

    // Image upload handling
    $('#' + PREFIX + 'image-upload').on('change', function() {
        handleFileUpload(this.files[0], 'image');
    });

    // Funzione per gestire l'upload dei file
    function handleFileUpload(file, type) {
        if (!file) return;

        currentDocumentFile = file;
        
        // Mostra info del file
        const fileInfo = {
            name: file.name,
            size: formatFileSize(file.size),
            type: file.type
        };
        
        $('#' + PREFIX + 'document-name-inline').html('<strong>File:</strong> ' + fileInfo.name);
        $('#' + PREFIX + 'document-size-inline').html('<strong>Size:</strong> ' + fileInfo.size);
        $('#' + PREFIX + 'document-type-inline').html('<strong>Type:</strong> ' + fileInfo.type);
        
        if (type === 'image') {
            // Inizializza OCR per le immagini
            const processor = initOcrProcessor();
            if (processor) {
                processor.processImage(file);
            }
        } else {
            // Per PDF/Word, mostra nel visualizzatore
            displayDocument(file);
        }
    }

    // Funzione per formattare la dimensione del file
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Funzione per visualizzare il documento
    function displayDocument(file) {
        const url = URL.createObjectURL(file);
        $('#' + PREFIX + 'document-frame').attr('src', url).show();
        $('#' + PREFIX + 'document-display').show();
        $('#' + PREFIX + 'zoom-in, #' + PREFIX + 'zoom-out').show();

        // Avvia l'estrazione del testo per documenti PDF/Word
        if (file.type === 'application/pdf' ||
            file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
            file.type === 'application/msword') {
            extractDocumentContent(file);
        }
    }

    // Funzione per estrarre il contenuto del documento senza analisi
    function extractDocumentContent(file) {
        if (!file) return;

        // Prevenzione estrazioni duplicate
        if (isExtracting) {
            console.log('[Document Analizer] Estrazione già in corso, saltando duplicato');
            return;
        }

        // Se abbiamo già estratto il contenuto per questo file, non farlo di nuovo
        if (extractedDocumentContent && currentDocumentFile &&
            currentDocumentFile.name === file.name &&
            currentDocumentFile.size === file.size) {
            console.log('[Document Analizer] Contenuto già estratto per questo file, saltando duplicato');
            return;
        }

        isExtracting = true;
        console.log('[Document Analizer] Iniziando estrazione per:', file.name);

        // Mostra messaggio di caricamento nell'area notifiche
        showDocumentNotification('<div class="spinner"></div> Estrazione del testo in corso...', 'processing');

        // Se il contenuto è già stato estratto dai file Word convertiti, usa quello
        if (file.type === 'application/pdf' && file.name.match(/\.docx?\.pdf$/i) && extractedDocumentContent) {
            console.log('Using already extracted content from Word conversion');

            // Notifica di successo nell'area notifiche
            hideDocumentNotification();
            showDocumentNotification('<strong>✓ Documento caricato con successo!</strong> Testo estratto dalla conversione Word precedente.', 'success');

            // Mostra e aggiorna le informazioni inline del documento
            $('#' + PREFIX + 'document-info-inline').show();
            $('#' + PREFIX + 'document-chars-inline').html('<strong>Chars:</strong> ' + formatCharCount(extractedDocumentContent.length));

            const tokenCount = estimateTokenCount(extractedDocumentContent);
            $('#' + PREFIX + 'document-tokens-inline').html('<strong>Tokens:</strong> ' + formatCharCount(tokenCount));

            // Calcola e visualizza la spesa stimata
            updateEstimatedCost(tokenCount);

            return;
        }

        var formData = new FormData();
        formData.append('action', 'analyze_document');
        formData.append('nonce', documentAnalizerParams.nonce);
        formData.append('document_file', file);
        formData.append('save_content', 'true');

        console.log('FormData created for extraction:', {
            action: 'analyze_document',
            has_file: formData.has('document_file'),
            file_name: file.name,
            file_type: file.type,
            file_size: file.size,
            nonce: documentAnalizerParams.nonce ? 'presente' : 'mancante'
        });

        $.ajax({
            url: documentAnalizerParams.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            timeout: 120000, // Aumentato a 2 minuti per file più grandi
            beforeSend: function(xhr) {
                console.log('Starting text extraction request to ' + documentAnalizerParams.ajaxUrl);
            },
            success: function(response) {
                console.log('Text extraction response received:', response);

                // Verifica che la risposta sia un oggetto valido
                if (typeof response !== 'object') {
                    try {
                        response = JSON.parse(response);
                        console.log('Response parsed from string to object');
                    } catch (e) {
                        console.error('Failed to parse response:', e);

                        // Mostra errore nell'area notifiche
                        showDocumentNotification('Errore nel formato della risposta dal server. Controlla la console per i dettagli.', 'error');

                        // Mostra anche nell'area risultati analisi
                        if ($('#' + PREFIX + 'analysis-results').find('.analysis-container').length === 0) {
                            $('#' + PREFIX + 'analysis-results').html('<p class="error">Errore nel formato della risposta dal server. Controlla la console per i dettagli.</p>');
                        }
                        return;
                    }
                }

                if (response.success) {
                    if (response.data && response.data.document_content) {
                        extractedDocumentContent = response.data.document_content;

                        // Notifica di successo nell'area notifiche
                        showDocumentNotification('<strong>✓ Estrazione del testo completata!</strong> Sono stati estratti ' +
                            formatCharCount(extractedDocumentContent.length) + ' caratteri.', 'success', true);

                        // Mostra e aggiorna le informazioni inline del documento
                        $('#' + PREFIX + 'document-info-inline').show();
                        $('#' + PREFIX + 'document-chars-inline').html('<strong>Chars:</strong> ' + formatCharCount(extractedDocumentContent.length));

                        const tokenCount = estimateTokenCount(extractedDocumentContent);
                        $('#' + PREFIX + 'document-tokens-inline').html('<strong>Tokens:</strong> ' + formatCharCount(tokenCount));

                        // Calcola e visualizza la spesa stimata
                        updateEstimatedCost(tokenCount);

                        // Aggiorna solo i risultati dell'analisi se non esiste un contenitore di analisi
                        if ($('#' + PREFIX + 'analysis-results').find('.analysis-container').length === 0) {
                            // Messaggio di conferma migliorato con stile
                            $('#' + PREFIX + 'analysis-results').html(
                                '<div class="extraction-success">' +
                                '<p><strong>✓ Estrazione del testo completata!</strong></p>' +
                                '<p>Sono stati estratti ' + formatCharCount(extractedDocumentContent.length) + ' caratteri.</p>' +
                                '<p>Puoi ora inserire una descrizione o domanda e cliccare su "Analizza" per procedere.</p>' +
                                '</div>'
                            );

                            // Fai scomparire automaticamente il messaggio di successo dell'estrazione dopo 4 secondi
                            setTimeout(function() {
                                $('.extraction-success').fadeOut(1000);
                            }, 4000);
                        }

                        console.log('Document content saved to variable, length:', extractedDocumentContent.length);
                    } else {
                        console.error('No document content in response:', response);

                        // Mostra errore nell'area notifiche
                        showDocumentNotification('Estrazione del testo completata, ma nessun contenuto disponibile. Il documento potrebbe essere protetto o non contenere testo estraibile.', 'error');

                        // Aggiorna solo se non esistono risultati di analisi
                        if ($('#' + PREFIX + 'analysis-results').find('.analysis-container').length === 0) {
                            $('#' + PREFIX + 'analysis-results').html('<p class="error">Estrazione del testo completata, ma nessun contenuto disponibile. Il documento potrebbe essere protetto o non contenere testo estraibile.</p>');
                        }
                    }

                    // Se il titolo non è impostato, utilizza il nome del file
                    if (!currentDocumentTitle) {
                        currentDocumentTitle = file.name.replace(/\.[^/.]+$/, ""); // Rimuove l'estensione
                        $('#' + PREFIX + 'analysis-title').val(currentDocumentTitle);
                    }

                    // Reset flag estrazione
                    isExtracting = false;
                    console.log('[Document Analizer] Estrazione completata per:', file.name);
                } else {
                    let errorMessage = 'Errore durante l\'estrazione del testo';

                    if (response.data && response.data.message) {
                        errorMessage += ': ' + response.data.message;
                    }

                    console.error('Text extraction failed:', errorMessage);

                    // Mostra errore nell'area notifiche
                    showDocumentNotification(errorMessage, 'error');

                    // Aggiorna solo se non esistono risultati di analisi
                    if ($('#' + PREFIX + 'analysis-results').find('.analysis-container').length === 0) {
                        $('#' + PREFIX + 'analysis-results').html('<p class="error">' + errorMessage + '</p>');
                    }

                    // Reset flag estrazione anche in caso di errore
                    isExtracting = false;
                    console.log('[Document Analizer] Estrazione fallita per:', file.name);
                }
            },
            error: function(xhr, status, error) {
                console.error('Text extraction AJAX error:', error);

                let errorMessage = 'Errore di connessione durante l\'estrazione del testo';
                if (status === 'timeout') {
                    errorMessage = 'Timeout durante l\'estrazione del testo. Il file potrebbe essere troppo grande o il server sovraccarico.';
                }

                // Mostra errore nell'area notifiche
                showDocumentNotification(errorMessage, 'error');

                // Aggiorna solo se non esistono risultati di analisi
                if ($('#' + PREFIX + 'analysis-results').find('.analysis-container').length === 0) {
                    $('#' + PREFIX + 'analysis-results').html('<p class="error">' + errorMessage + '</p>');
                }

                // Reset flag estrazione anche in caso di errore AJAX
                isExtracting = false;
                console.log('[Document Analizer] Errore AJAX durante estrazione per:', file.name);
            }
        });
    }

    // Preset queries handling
    $('#' + PREFIX + 'preset-queries').on('change', function() {
        const selectedQuery = $(this).val();
        if (selectedQuery) {
            $('#' + PREFIX + 'document-description').val(selectedQuery);
        }
    });

    // Analyze button
    $('#' + PREFIX + 'analyze-description').on('click', function() {
        analyzeDocument();
    });

    // Clear button
    $('#' + PREFIX + 'clear-document').on('click', function() {
        clearDocument();
    });

    // Export PDF button
    $('#' + PREFIX + 'export-pdf').on('click', function() {
        exportToPDF();
    });

    // Save analysis button (remove any existing handlers first)
    $('#' + PREFIX + 'save-analysis').off('click').on('click', function() {
        saveAnalysis();
    });

    // Zoom controls
    $('#' + PREFIX + 'zoom-in').on('click', function() {
        zoomLevel += 0.1;
        updateZoom();
    });

    $('#' + PREFIX + 'zoom-out').on('click', function() {
        zoomLevel = Math.max(0.5, zoomLevel - 0.1);
        updateZoom();
    });

    function updateZoom() {
        $('#' + PREFIX + 'document-frame').css('transform', `scale(${zoomLevel})`);
    }

    // Event handlers per i tab dell'analisi
    $(document).on('click', '.analysis-tab', function() {
        const tabName = $(this).data('tab');

        // Rimuovi la classe active da tutti i tab
        $('.analysis-tab').removeClass('active');
        $('.tab-content').removeClass('active');

        // Aggiungi la classe active al tab cliccato
        $(this).addClass('active');
        $('#tab-' + tabName).addClass('active');
    });

    // Funzione principale per analizzare il documento
    function analyzeDocument() {
        // Previeni analisi duplicate
        if (isAnalyzing) {
            console.log('[Document Analizer] Analisi già in corso, ignorando richiesta duplicata');
            return;
        }

        const description = $('#' + PREFIX + 'document-description').val();
        const annotations = $('#' + PREFIX + 'document-annotations').val();

        if (!extractedDocumentContent && !currentDocumentFile) {
            showDocumentNotification('Carica prima un documento', 'error');
            return;
        }

        if (!description.trim()) {
            showDocumentNotification('Inserisci una descrizione o domanda', 'error');
            return;
        }

        // Imposta il flag per prevenire analisi duplicate
        isAnalyzing = true;

        // Debug logging
        console.log('[Document Analizer] Starting analysis with:', {
            extractedContentLength: extractedDocumentContent ? extractedDocumentContent.length : 0,
            extractedContentPreview: extractedDocumentContent ? extractedDocumentContent.substring(0, 200) + '...' : 'NO CONTENT',
            description: description,
            hasFile: !!currentDocumentFile
        });

        // Additional validation
        if (!extractedDocumentContent || extractedDocumentContent.trim().length === 0) {
            showDocumentNotification('Errore: Nessun contenuto estratto dal documento. Riprova il caricamento.', 'error');
            console.error('[Document Analizer] No extracted content available for analysis');
            return;
        }

        // Mostra messaggio di attesa nell'area risultati prima di iniziare l'analisi
        $('#' + PREFIX + 'analysis-results').html(`
            <div class="analysis-wait-container">
                <div class="analysis-wait-spinner">
                    <div class="spinner"></div>
                </div>
                <h4>Analisi AI in corso...</h4>
                <p class="analysis-wait-message">L'elaborazione di documenti complessi può richiedere fino a un minuto</p>
            </div>
        `);

        // Debug: Log what we're sending
        console.log('[Document Analizer] Sending AJAX request with data:', {
            action: 'analyze_document',
            nonce: documentAnalizerParams.nonce ? 'present' : 'missing',
            document_data_length: extractedDocumentContent ? extractedDocumentContent.length : 0,
            document_data_preview: extractedDocumentContent ? extractedDocumentContent.substring(0, 100) + '...' : 'empty',
            description: description
        });

        // Invio richiesta AJAX (usa la stessa struttura del Document Viewer)
        $.ajax({
            url: documentAnalizerParams.ajaxUrl,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'analizer_analyze_document',
                nonce: documentAnalizerParams.nonce,
                document_data: extractedDocumentContent,
                description: description
            },
            timeout: 120000, // 2 minuti timeout
            beforeSend: function() {
                // Aggiornamento alla Fase 2 dopo l'invio dei dati
                hideDocumentNotification();
                showDocumentNotification('<div class="spinner"></div> Fase 2: Analisi AI in corso...', 'processing');
            },
            success: function(response) {
                hideDocumentNotification();
                if (response.success) {
                    // Mostra il messaggio di Fase 3 - elaborazione completata
                    showDocumentNotification('<strong>✓ Fase 3: Elaborazione AI completata con successo!</strong>', 'success');

                    // Set flag to indicate analysis is displayed
                    analysisDisplayed = true;

                    // Update the UI with the analysis results (same logic as Document Viewer)
                    if (response.data && response.data.results) {
                        // Debug logging per verificare cosa contiene effettivamente la risposta
                        console.log('Risposta analisi ricevuta:', response.data.results);

                        // Estrai il contenuto dell'analisi - aggiungiamo controlli aggiuntivi
                        let analysisContent = '';

                        // Rimuovi eventuali riferimenti a commit GitHub prima di qualsiasi elaborazione
                        let cleanedResults = response.data.results;
                        if (typeof cleanedResults === 'string') {
                            cleanedResults = cleanedResults.replace(/\[([0-9a-f]+)\]\(https:\/\/github\.com\/.*?\/commit\/[0-9a-f]+\)/g, '');
                        }

                        // Verifica se response.data.results contiene un elemento con id="analysis-content"
                        if ($(cleanedResults).find('#analysis-content').length > 0) {
                            analysisContent = $(cleanedResults).find('#analysis-content').html();
                        } else if ($(cleanedResults).filter('#analysis-content').length > 0) {
                            // Se non lo trova come elemento figlio, prova come elemento diretto
                            analysisContent = $(cleanedResults).filter('#analysis-content').html();
                        } else {
                            // Se ancora non lo trova, usa direttamente il risultato (potrebbe già essere HTML)
                            analysisContent = cleanedResults;
                        }

                        console.log('Contenuto analisi estratto:', analysisContent);

                        // Genera punti chiave automaticamente
                        const keyPointsContent = extractKeyPoints(analysisContent || '');

                        // Crea la struttura a tab per l'analisi - versione più robusta
                        const tabsHTML = `
                            <div class="analysis-container">
                                <div class="analysis-tabs">
                                    <div class="analysis-tab active" data-tab="analysis">Analisi Completa</div>
                                    <div class="analysis-tab" data-tab="keypoints">Punti Chiave</div>
                                </div>

                                <div id="tab-analysis" class="tab-content active">
                                    <div id="analysis-content" class="typing-effect"></div>
                                </div>

                                <div id="tab-keypoints" class="tab-content">
                                    ${keyPointsContent}
                                </div>
                            </div>
                        `;

                        // Aggiorna l'interfaccia con la nuova struttura a tab
                        $('#' + PREFIX + 'analysis-results').html(tabsHTML);
                        currentAnalysisResults = tabsHTML;

                        // Applica l'effetto di digitazione al contenuto dell'analisi
                        typeContentWithEffect(analysisContent);

                        // Assicurati che il contenuto sia visibile
                        $('#' + PREFIX + 'analysis-results').show();

                        // Aggiungi il bottone per il mega tooltip dopo che l'analisi è stata caricata
                        addMegaTooltipTrigger();

                        // Aggiorna le statistiche
                        console.log('[Document Analizer] Checking for stats in response:', response.data);
                        if (response.data.stats) {
                            console.log('[Document Analizer] Stats found, updating:', response.data.stats);
                            updateAnalizerStats(response.data.stats);
                        } else {
                            console.log('[Document Analizer] No stats found in response, trying to sync credit manually');
                            // Manually sync credit if stats are not provided
                            syncAnalizerCredit();

                            // Also try to estimate cost based on content length
                            if (extractedDocumentContent) {
                                const tokenCount = estimateTokenCount(extractedDocumentContent);
                                updateEstimatedCost(tokenCount);
                                console.log('[Document Analizer] Manual cost estimation triggered for', tokenCount, 'tokens');
                            }
                        }
                    } else {
                        showDocumentNotification('Errore: Nessun risultato nell\'analisi', 'error');
                    }
                } else {
                    showDocumentNotification('Errore durante l\'analisi: ' + response.data.message, 'error');
                }

                // Reset flag analisi
                isAnalyzing = false;
            },
            error: function(xhr, status, error) {
                hideDocumentNotification();

                // Enhanced error logging
                console.error('[Document Analizer] AJAX Error Details:', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText,
                    statusCode: xhr.status,
                    statusText: xhr.statusText
                });

                let errorMessage = 'Errore di connessione: ' + error;

                // Try to parse server response for more details
                if (xhr.responseText) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.data && response.data.message) {
                            errorMessage = 'Errore server: ' + response.data.message;
                        }
                    } catch (e) {
                        // If response is not JSON, show first 200 chars
                        if (xhr.responseText.length > 0) {
                            errorMessage = 'Errore server: ' + xhr.responseText.substring(0, 200);
                        }
                    }
                }

                showDocumentNotification(errorMessage, 'error');

                // Reset flag analisi anche in caso di errore
                isAnalyzing = false;
            }
        });
    }

    // Funzioni principali del Document Analizer
    
    // Funzione per cancellare il documento
    function clearDocument() {
        if (!confirm(documentAnalizerParams.i18n.clearConfirm)) {
            return;
        }
        
        $.ajax({
            url: documentAnalizerParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'analizer_clear_document',
                nonce: documentAnalizerParams.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Reset all form fields
                    $('#' + PREFIX + 'analysis-title').val('');
                    $('#' + PREFIX + 'document-description').val('');
                    $('#' + PREFIX + 'document-annotations').val('');
                    $('#' + PREFIX + 'analysis-results').html('');
                    $('#' + PREFIX + 'document-display').hide();
                    $('#' + PREFIX + 'document-info-inline').hide();
                    $('#' + PREFIX + 'logo-preview').hide();
                    $('#' + PREFIX + 'logo-dimensions-info').hide();
                    
                    // Reset variables
                    currentDocumentFile = null;
                    currentPdfFile = null;
                    currentDocumentTitle = '';
                    currentAnalysisResults = '';
                    extractedDocumentContent = '';
                    customLogo = null;
                    analysisDisplayed = false;
                    isExtracting = false; // Reset flag estrazione
                    isSaving = false; // Reset flag salvataggio
                    
                    showDocumentNotification('Documento cancellato con successo', 'success');
                    console.log('✅ Document Analizer: Documento cancellato');
                } else {
                    showDocumentNotification('Errore durante la cancellazione', 'error');
                }
            },
            error: function() {
                showDocumentNotification('Errore di connessione', 'error');
            }
        });
    }
    
    // Funzione per esportare in PDF
    function exportToPDF() {
        // Controllo più robusto per verificare se c'è un'analisi da esportare
        const analysisContent = $('#analysis-content').html();
        const keyPointsContent = $('#tab-keypoints').html();

        // Verifica se c'è contenuto di analisi effettivo (non solo la struttura HTML)
        const hasAnalysisContent = analysisContent && analysisContent.trim() &&
                                   !analysisContent.includes('Nessun contenuto disponibile') &&
                                   analysisContent.length > 50; // Almeno 50 caratteri di contenuto reale

        const hasKeyPointsContent = keyPointsContent && keyPointsContent.trim() &&
                                    keyPointsContent.length > 20;

        if (!hasAnalysisContent && !hasKeyPointsContent) {
            showDocumentNotification('Nessuna analisi da esportare. Completa prima un\'analisi del documento.', 'error');
            console.log('[Document Analizer] Export PDF fallito: nessun contenuto di analisi disponibile');
            return;
        }

        console.log('[Document Analizer] Export PDF: contenuto disponibile, mostrando opzioni');

        // Show export options dialog
        showExportOptionsDialog();
    }

    // Function to show export options dialog
    function showExportOptionsDialog() {
        // Create a modal dialog for export options
        const dialogHtml = `
            <div id="analizer-export-options-modal" class="export-options-modal">
                <div class="export-options-content">
                    <h3>Opzioni di Esportazione PDF</h3>
                    <p>Seleziona le sezioni da includere nel documento PDF:</p>

                    <div class="export-option">
                        <input type="checkbox" id="analizer-include-query" checked>
                        <label for="analizer-include-query">Richiesta di Analisi</label>
                    </div>

                    <div class="export-option">
                        <input type="checkbox" id="analizer-include-full-analysis" checked>
                        <label for="analizer-include-full-analysis">Analisi Completa</label>
                    </div>

                    <div class="export-option">
                        <input type="checkbox" id="analizer-include-key-points" checked>
                        <label for="analizer-include-key-points">Punti Chiave</label>
                    </div>

                    <div class="export-option">
                        <input type="checkbox" id="analizer-include-original-doc" checked>
                        <label for="analizer-include-original-doc">Documento Originale</label>
                    </div>

                    <div class="export-buttons">
                        <button type="button" id="analizer-cancel-export" class="btn-secondary">Annulla</button>
                        <button type="button" id="analizer-confirm-export" class="btn-primary">Esporta PDF</button>
                    </div>
                </div>
            </div>
        `;

        // Add modal to body
        $('body').append(dialogHtml);

        // Add styles for the modal
        $('head').append(`
            <style>
                .export-options-modal {
                    display: block;
                    position: fixed;
                    z-index: 1000;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0,0,0,0.5);
                }

                .export-options-content {
                    background-color: #fff;
                    margin: 10% auto;
                    padding: 25px;
                    border-radius: 8px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                    width: 450px;
                    max-width: 90%;
                }

                .export-options-content h3 {
                    margin-top: 0;
                    color: #007cba;
                    border-bottom: 2px solid #eee;
                    padding-bottom: 10px;
                }

                .export-option {
                    margin: 15px 0;
                    display: flex;
                    align-items: center;
                }

                .export-option input[type="checkbox"] {
                    margin-right: 10px;
                    width: 18px;
                    height: 18px;
                }

                .export-option label {
                    font-size: 16px;
                    color: #333;
                }

                .export-buttons {
                    margin-top: 25px;
                    display: flex;
                    justify-content: flex-end;
                    gap: 10px;
                }

                .btn-primary {
                    background-color: #007cba;
                    color: white;
                    border: none;
                    padding: 10px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-weight: 600;
                }

                .btn-secondary {
                    background-color: #f1f1f1;
                    color: #333;
                    border: 1px solid #ddd;
                    padding: 10px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                }

                .btn-primary:hover {
                    background-color: #005a87;
                }

                .btn-secondary:hover {
                    background-color: #e5e5e5;
                }
            </style>
        `);

        // Handle close button
        $('#analizer-cancel-export').on('click', function() {
            $('#analizer-export-options-modal').remove();
        });

        // Handle confirm button
        $('#analizer-confirm-export').on('click', function() {
            // Get options
            const includeQuery = $('#analizer-include-query').is(':checked');
            const includeFullAnalysis = $('#analizer-include-full-analysis').is(':checked');
            const includeKeyPoints = $('#analizer-include-key-points').is(':checked');
            const includeOriginalDoc = $('#analizer-include-original-doc').is(':checked');

            // Validate at least one section is selected
            if (!includeFullAnalysis && !includeKeyPoints) {
                alert('Seleziona almeno una sezione di analisi da includere (Analisi Completa o Punti Chiave).');
                return;
            }

            // Remove modal
            $('#analizer-export-options-modal').remove();

            // Proceed with PDF export
            proceedWithPdfExport(includeQuery, includeFullAnalysis, includeKeyPoints, includeOriginalDoc);
        });
    }

    // Function to get the HTML content for PDF export
    function getAnalysisHtml() {
        // Prova diversi selettori per trovare il contenuto dell'analisi
        let content = '';

        if ($('#analysis-content').length) {
            content = $('#analysis-content').html();
            console.log('[Document Analizer] Contenuto trovato in #analysis-content:', content ? content.length : 0, 'caratteri');
        } else if ($('#tab-analysis .tab-content').length) {
            content = $('#tab-analysis .tab-content').html();
            console.log('[Document Analizer] Contenuto trovato in #tab-analysis .tab-content:', content ? content.length : 0, 'caratteri');
        } else if ($('.analysis-container .tab-content.active').length) {
            content = $('.analysis-container .tab-content.active').html();
            console.log('[Document Analizer] Contenuto trovato in .tab-content.active:', content ? content.length : 0, 'caratteri');
        }

        // Remove cursor if present
        if (content) {
            content = content.replace(/<span class="cursor"><\/span>/g, '');
            content = content.replace(/<span class="cursor"[^>]*><\/span>/g, '');
        }

        console.log('[Document Analizer] Contenuto finale per PDF (analisi):', content ? content.length : 0, 'caratteri');
        return content || '';
    }

    // Function to get the Key Points content for PDF export
    function getKeyPointsHtml() {
        let content = '';

        if ($('#tab-keypoints').length) {
            content = $('#tab-keypoints').html();
            console.log('[Document Analizer] Contenuto trovato in #tab-keypoints:', content ? content.length : 0, 'caratteri');
        } else if ($('#tab-keypoints .tab-content').length) {
            content = $('#tab-keypoints .tab-content').html();
            console.log('[Document Analizer] Contenuto trovato in #tab-keypoints .tab-content:', content ? content.length : 0, 'caratteri');
        }

        console.log('[Document Analizer] Contenuto finale per PDF (key points):', content ? content.length : 0, 'caratteri');
        return content || '';
    }

    // Function to add the mega tooltip trigger button to the analysis section
    function addMegaTooltipTrigger() {
        // Verifica se esiste già un trigger
        if ($('.analysis-container .mega-tooltip-trigger').length === 0) {
            // Crea il bottone trigger per il mega tooltip con icona a forma di lente e tooltip informativo
            const $trigger = $('<button>', {
                'class': 'mega-tooltip-trigger',
                'title': 'Apri la visualizzazione estesa dell\'analisi in una finestra più grande',
                'aria-label': 'Visualizzazione estesa',
                'data-tooltip': 'Visualizza l\'analisi completa in una finestra più grande',
                'html': '<i class="dashicons dashicons-search"></i>'
            });

            // Aggiungi il trigger alla fine dei tab dell'analisi (elemento che esiste)
            if ($('.analysis-container .analysis-tabs').length > 0) {
                $('.analysis-container .analysis-tabs').append($trigger);
                console.log('[Document Analizer] Mega tooltip trigger aggiunto ai tab dell\'analisi');
            } else if ($('.analysis-container').length > 0) {
                // Fallback: aggiungi all'inizio del container dell'analisi
                $('.analysis-container').prepend($trigger);
                console.log('[Document Analizer] Mega tooltip trigger aggiunto al container dell\'analisi (fallback)');
            } else {
                console.warn('[Document Analizer] Impossibile aggiungere mega tooltip trigger: container non trovato');
            }
        }
    }

    // Function to proceed with PDF export after options are selected
    function proceedWithPdfExport(includeQuery, includeFullAnalysis, includeKeyPoints, includeOriginalDoc) {
        // Previeni export PDF duplicati
        if (isExportingPDF) {
            console.log('[Document Analizer] Export PDF già in corso, ignorando richiesta duplicata');
            return;
        }

        console.log('[Document Analizer] Iniziando procedura export PDF con opzioni:', {
            includeQuery, includeFullAnalysis, includeKeyPoints, includeOriginalDoc
        });

        // Imposta il flag per prevenire export duplicati
        isExportingPDF = true;

        // Get analysis content using the same functions as Document Viewer
        let analysisHtml = includeFullAnalysis ? getAnalysisHtml() : '';
        let keyPointsHtml = includeKeyPoints ? getKeyPointsHtml() : '';

        // Validazione più robusta del contenuto
        const hasValidAnalysis = analysisHtml && analysisHtml.trim().length > 50 &&
                                 !analysisHtml.includes('Nessun contenuto disponibile');
        const hasValidKeyPoints = keyPointsHtml && keyPointsHtml.trim().length > 20;

        if (!includeFullAnalysis && !includeKeyPoints) {
            alert('Seleziona almeno una sezione da includere nel PDF.');
            return;
        }

        if (includeFullAnalysis && !hasValidAnalysis) {
            alert('Il contenuto dell\'analisi completa non è disponibile o è vuoto.');
            console.error('[Document Analizer] Analisi completa non valida:', analysisHtml);
            return;
        }

        if (includeKeyPoints && !hasValidKeyPoints) {
            alert('Il contenuto dei punti chiave non è disponibile o è vuoto.');
            console.error('[Document Analizer] Punti chiave non validi:', keyPointsHtml);
            return;
        }

        const title = $('#' + PREFIX + 'analysis-title').val() || currentDocumentTitle || 'Documento senza titolo';
        const description = includeQuery ? $('#' + PREFIX + 'document-description').val() : '';
        const annotations = $('#' + PREFIX + 'document-annotations').val() || '';

        // Debug dei dati esportati
        console.log('Dati per esportazione PDF:', {
            title: title,
            include_query: includeQuery,
            include_full_analysis: includeFullAnalysis,
            include_key_points: includeKeyPoints,
            include_original_doc: includeOriginalDoc,
            analysis_html_length: analysisHtml ? analysisHtml.length : 0,
            key_points_html_length: keyPointsHtml ? keyPointsHtml.length : 0,
            description_length: description ? description.length : 0,
            annotations_length: annotations ? annotations.length : 0,
            hasCustomLogo: !!customLogo
        });

        showDocumentNotification('<div class="spinner"></div> Esportazione del PDF in corso...', 'processing');

        // Use FormData to handle both text data and files
        const formData = new FormData();
        formData.append('action', 'analizer_export_analysis_pdf');
        formData.append('nonce', documentAnalizerParams.nonce);
        formData.append('title', title);
        formData.append('analysis_html', analysisHtml);
        formData.append('key_points_html', keyPointsHtml);
        formData.append('description', description);
        formData.append('annotations', annotations);
        formData.append('include_query', includeQuery ? '1' : '0');
        formData.append('include_full_analysis', includeFullAnalysis ? '1' : '0');
        formData.append('include_key_points', includeKeyPoints ? '1' : '0');
        formData.append('include_original_doc', includeOriginalDoc ? '1' : '0');

        // Add custom logo if present
        if (customLogo) {
            formData.append('custom_logo', customLogo);
        }

        // Add original document if present and requested
        if (currentDocumentFile && includeOriginalDoc) {
            formData.append('uploaded_pdf', currentDocumentFile);
        }

        $.ajax({
            url: documentAnalizerParams.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            timeout: 120000, // 2 minutes timeout for PDF generation (ridotto)
            xhr: function() {
                var xhr = new XMLHttpRequest();
                xhr.responseType = 'blob';
                return xhr;
            },
            success: function(response, status, xhr) {
                // Hide the processing notification
                hideDocumentNotification();

                // Show success notification
                showDocumentNotification('<strong>✓ PDF generato con successo!</strong>', 'success');

                // Create download link for the PDF
                var blob = new Blob([response], { type: 'application/pdf' });
                var link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);
                link.download = (title || 'analisi') + '-analysis.pdf';
                link.click();

                // Clean up
                window.URL.revokeObjectURL(link.href);

                // Reset flag export PDF
                isExportingPDF = false;
            },
            error: function(xhr, status, error) {
                hideDocumentNotification();

                // Enhanced error logging for PDF export
                console.error('[Document Analizer] PDF Export Error Details:', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText,
                    statusCode: xhr.status,
                    statusText: xhr.statusText
                });

                let errorMessage = 'Errore durante l\'esportazione PDF: ';

                if (status === 'timeout') {
                    errorMessage += 'Timeout - Il server sta impiegando troppo tempo per generare il PDF. Riprova.';
                } else if (xhr.status === 500) {
                    errorMessage += 'Errore interno del server. Controlla la configurazione mPDF.';
                } else if (xhr.status === 0) {
                    errorMessage += 'Errore di connessione. Verifica la connessione internet.';
                } else {
                    errorMessage += error || 'Errore sconosciuto';
                }

                showDocumentNotification(errorMessage, 'error');

                // Reset flag export PDF anche in caso di errore
                isExportingPDF = false;
            }
        });
    }

    // Funzione per salvare l'analisi
    function saveAnalysis() {
        // Prevenzione salvataggi duplicati
        if (isSaving) {
            console.log('[Document Analizer] Salvataggio già in corso, saltando duplicato');
            return;
        }

        if (!currentAnalysisResults) {
            showDocumentNotification('Nessuna analisi da salvare', 'error');
            return;
        }

        isSaving = true;
        console.log('[Document Analizer] Iniziando salvataggio analisi');

        // Get the formatted analysis content from the display (like Document Viewer does)
        const title = $('#' + PREFIX + 'analysis-title').val() || currentDocumentTitle || 'Analisi ' + new Date().toLocaleDateString();
        const query = $('#' + PREFIX + 'document-description').val();
        const analysis_results = $('#analysis-content').html(); // Get the formatted HTML content

        // Verify we have the essential data
        if (!title || !analysis_results) {
            console.error('Missing required data for saving: title=' +
                (title ? 'yes' : 'no') + ', analysis_results=' +
                (analysis_results ? 'yes' : 'no'));
            showDocumentNotification('Dati mancanti per il salvataggio', 'error');
            return;
        }

        // Debug info (matching Document Viewer)
        console.log('Preparing to save analysis with data:', {
            'title': title,
            'query_length': query ? query.length : 0,
            'analysis_results_length': analysis_results ? analysis_results.length : 0,
            'has_document': currentDocumentFile ? true : false,
            'has_logo': customLogo ? true : false
        });

        showDocumentNotification('<div class="spinner"></div> Salvataggio analisi nel database...', 'processing');

        const formData = new FormData();
        formData.append('action', 'analizer_save_analysis');
        formData.append('nonce', documentAnalizerParams.nonce);
        formData.append('title', title);
        formData.append('query', query);
        formData.append('analysis_results', analysis_results);

        // Add the logo if it exists
        if (customLogo) {
            console.log('Adding logo to FormData, type:', customLogo.type, 'size:', customLogo.size);
            formData.append('logo', customLogo);
        }

        // Add the document if it exists
        if (currentDocumentFile) {
            console.log('Adding document to FormData, type:', currentDocumentFile.type, 'size:', currentDocumentFile.size);
            formData.append('document', currentDocumentFile);
        }

        console.log('AJAX request details:', {
            'url': documentAnalizerParams.ajaxUrl,
            'action': 'analizer_save_analysis',
            'has_nonce': documentAnalizerParams.nonce ? true : false
        });
        
        // Send the AJAX request to save the data
        $.ajax({
            url: documentAnalizerParams.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            timeout: 120000, // 2 minutes timeout for save operation
            beforeSend: function(xhr) {
                console.log('Starting save analysis request to server');
            },
            success: function(response) {
                console.log('Received server response:', response);
                hideDocumentNotification();

                if (response.success) {
                    showDocumentNotification('<strong>✓ Analisi salvata con successo!</strong>', 'success');
                    $('#' + PREFIX + 'save-result-message').html('<span style="color: green;">✓ Salvato</span>').fadeIn();
                    setTimeout(() => {
                        $('#' + PREFIX + 'save-result-message').fadeOut();
                    }, 3000);
                    console.log('✅ Document Analizer: Analisi salvata con ID:', response.data.analysis_id);
                } else {
                    console.error('Save failed:', response.data);
                    showDocumentNotification('Errore durante il salvataggio: ' + (response.data.message || 'Errore sconosciuto'), 'error');
                }

                // Reset flag salvataggio
                isSaving = false;
                console.log('[Document Analizer] Salvataggio completato');
            },
            error: function(xhr, status, error) {
                hideDocumentNotification();

                // Enhanced error logging for save analysis (matching Document Viewer)
                console.error('[Document Analizer] Save Analysis Error Details:', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText,
                    statusCode: xhr.status,
                    statusText: xhr.statusText
                });

                let errorMessage = 'Errore durante il salvataggio dell\'analisi: ' + error;
                let errorDetail = '';

                // Try to extract more details from the response
                if (xhr.responseText) {
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        if (errorResponse.data && errorResponse.data.message) {
                            errorDetail = errorResponse.data.message;
                        }
                    } catch (e) {
                        // If it's not JSON, just take the first 100 characters
                        errorDetail = xhr.responseText.substring(0, 100) + (xhr.responseText.length > 100 ? '...' : '');
                    }
                }

                // Show error notification with details
                showDocumentNotification(errorMessage + (errorDetail ? '<br>Dettagli: ' + errorDetail : ''), 'error');

                // Reset flag salvataggio anche in caso di errore
                isSaving = false;
                console.log('[Document Analizer] Errore durante salvataggio, flag resettato');
            }
        });
    }

    // Funzione per aggiornare le statistiche del Document Analizer
    function updateAnalizerStats(data) {
        if (!data) return;

        // Aggiorna i valori nelle statistiche del Document Analizer (usando gli ID corretti dal template)
        const costElement = $('#analizer-cost-estimate');
        const actualCostElement = $('#analizer-actual-cost');
        const creditsElement = $('#analizer-credits-available');

        console.log('[Document Analizer] Aggiornamento statistiche con dati:', data);
        console.log('[Document Analizer] Elementi trovati:', {
            costElement: costElement.length,
            actualCostElement: actualCostElement.length,
            creditsElement: creditsElement.length
        });

        if (data.actual_cost !== undefined && actualCostElement.length) {
            const formattedCost = '€' + parseFloat(data.actual_cost).toFixed(2).replace('.', ',');
            actualCostElement.text(formattedCost);
            console.log('[Document Analizer] Costo effettivo aggiornato:', formattedCost);

            // Aggiorna anche il costo stimato se non è già stato aggiornato
            if (costElement.length) {
                costElement.text(formattedCost);
                console.log('[Document Analizer] Costo stimato aggiornato:', formattedCost);
            }
        }

        if (data.credits_available !== undefined && creditsElement.length) {
            const formattedCredit = '€' + parseFloat(data.credits_available).toFixed(2).replace('.', ',');
            creditsElement.text(formattedCredit);
            console.log('[Document Analizer] Crediti disponibili aggiornati:', formattedCredit);
        }

        console.log('[Document Analizer] Statistiche aggiornate completate');
    }

    // Sincronizzazione credito dal database (come nel Document Viewer)
    function syncAnalizerCredit() {
        if (typeof subscriberManagementAjax !== "undefined" && subscriberManagementAjax.ajax_url) {
            $.ajax({
                url: subscriberManagementAjax.ajax_url,
                type: "POST",
                data: {
                    action: "get_current_credit",
                    nonce: subscriberManagementAjax.nonce
                },
                success: function(response) {
                    if (response.success && response.data.credit !== undefined) {
                        const creditElement = $('#analizer-credits-available');
                        if (creditElement.length) {
                            const formattedCredit = "€" + parseFloat(response.data.credit).toFixed(2).replace(".", ",");
                            creditElement.text(formattedCredit);
                            console.log("[Document Analizer] Credito sincronizzato:", formattedCredit);
                        } else {
                            console.warn("[Document Analizer] Elemento crediti non trovato: #analizer-credits-available");
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.warn("[Document Analizer] Errore sincronizzazione credito:", error);
                }
            });
        }
    }

    // Prevenzione submit accidentali del form
    $('#' + PREFIX + 'form').on('submit', function(e) {
        e.preventDefault();
        return false;
    });

    // Nascondi controlli zoom e document display inizialmente
    $('#' + PREFIX + 'zoom-in, #' + PREFIX + 'zoom-out, #' + PREFIX + 'document-display').hide();

    // Inizializzazione
    console.log('[Document Analizer] Widget inizializzato');
    
    // Sincronizza il credito all'avvio
    setTimeout(syncAnalizerCredit, 1000);
    
    // Sincronizza il credito ogni 30 secondi
    setInterval(syncAnalizerCredit, 30000);
    
    // Ascolta eventi di aggiornamento credito da altri widget
    $(document).on("credit-updated", function(event, newCredit) {
        const creditElement = $('#analizer-credits-available');
        if (creditElement.length && newCredit !== undefined) {
            const formattedCredit = "€" + parseFloat(newCredit).toFixed(2).replace(".", ",");
            creditElement.text(formattedCredit);
            console.log("[Document Analizer] Credito aggiornato da evento:", formattedCredit);
        } else {
            console.warn("[Document Analizer] Elemento crediti non trovato per aggiornamento esterno: #analizer-credits-available");
        }
    });

    // Gestione toggle per la sezione analisi recenti
    $(document).on('click', '.document-analizer-stats-container .stats-section-header', function() {
        const $header = $(this);
        const $section = $header.closest('.stats-section');
        const $content = $section.find('.stats-section-content');
        const $icon = $header.find('.toggle-icon');

        if ($content.is(':visible')) {
            $content.slideUp(300);
            $icon.removeClass('expanded');
        } else {
            $content.slideDown(300);
            $icon.addClass('expanded');
        }
    });

    // Gestione click sugli elementi delle analisi recenti
    $(document).on('click', '.document-analizer-stats-container .recent-analysis-item', function(e) {
        e.stopPropagation(); // Previeni il toggle della sezione

        const analysisId = $(this).data('id');
        if (analysisId) {
            console.log('[Document Analizer] Caricamento analisi salvata ID:', analysisId);
            // Qui potresti implementare il caricamento dell'analisi salvata
            // loadSavedAnalysis(analysisId);
        }
    });
});
