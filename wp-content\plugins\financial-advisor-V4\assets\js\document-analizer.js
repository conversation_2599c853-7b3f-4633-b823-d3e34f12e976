/**
 * Document Analizer Widget JS
 * Clone completo del Document Viewer con funzionalità avanzate
 * Basato sullo stile del Chat Model Widget per coerenza
 */
jQuery(document).ready(function ($) {
    console.log('✅ Document Analizer Widget JS caricato');

    // Verifica che i parametri siano disponibili
    if (typeof documentAnalizerParams === 'undefined') {
        console.warn('⚠️ Document Analizer: Parametri AJAX non disponibili');
        return;
    }

    // Variabili principali (clone dal document-viewer.js)
    let zoomLevel = 1;
    let currentDocumentFile = null;
    let currentPdfFile = null;
    let currentDocumentTitle = '';
    let currentAnalysisResults = '';
    let extractedDocumentContent = '';
    let customLogo = null;
    let analysisDisplayed = false;
    let preventReloadOnSuccess = false;
    let documentDescription = '';
    let ocrProcessor = null;

    // Prefisso per gli ID degli elementi del Document Analizer
    const PREFIX = 'analizer-';

    console.log('🔧 Document Analizer: Inizializzazione variabili completata');

    // Funzione per calcolare e visualizzare la spesa stimata (clone del document-viewer)
    function updateEstimatedCost(tokenCount) {
        if (!tokenCount) return;
        
        let formattedCost = '€0,00';
        
        // Utilizzo prioritario del calcolo client-side
        if (window.documentStats && typeof window.documentStats.calculateCost === 'function') {
            const costValue = window.documentStats.calculateCost(tokenCount);
            formattedCost = '€' + costValue;
            console.log('[Document Analizer] Spesa stimata calcolata:', formattedCost, 'per', tokenCount, 'token');
        } else {
            // Fallback al calcolo locale
            const costRate = 0.01; // €0.01 per token
            const estimatedCost = tokenCount * costRate;
            formattedCost = '€' + estimatedCost.toFixed(2).replace('.', ',');
            console.log('[Document Analizer] Spesa stimata locale:', formattedCost, 'per', tokenCount, 'token');
        }
        
        // Aggiorna il costo stimato nell'interfaccia utente del Document Analizer
        const costElement = $('#' + PREFIX + 'cost-estimate');
        if (costElement.length) {
            costElement.text(formattedCost);
            costElement.addClass('cost-updated');
            setTimeout(() => {
                costElement.removeClass('cost-updated');
            }, 3000);
        }
        
        console.log('💰 Document Analizer: Costo stimato aggiornato:', formattedCost);
        return formattedCost;
    }

    // Inizializza OCR processor
    function initOcrProcessor() {
        if (!ocrProcessor && typeof DocumentOCR !== 'undefined') {
            ocrProcessor = new DocumentOCR();
            ocrProcessor.registerCallbacks(
                // Progress callback
                (progress) => {
                    showDocumentNotification('<div class="spinner"></div> Elaborazione OCR in corso... ' + progress + '%', 'processing');
                },
                // Completion callback
                ocrCompletionHandler,
                // Error callback
                (error) => {
                    hideDocumentNotification();
                    showDocumentNotification('Errore OCR: ' + error.message, 'error');
                    console.error('[Document Analizer] OCR error:', error);
                }
            );
        }
        return ocrProcessor;
    }

    // OCR completion callback
    function ocrCompletionHandler(extractedText) {
        extractedDocumentContent = extractedText ? extractedText.trim() : '';
        console.log('[Document Analizer] Testo estratto, lunghezza:', extractedDocumentContent.length);
        
        // Update the inline document info
        $('#' + PREFIX + 'document-info-inline').show();
        $('#' + PREFIX + 'document-chars-inline').html('<strong>Chars:</strong> ' + formatCharCount(extractedDocumentContent.length));
        
        const tokenCount = estimateTokenCount(extractedDocumentContent);
        $('#' + PREFIX + 'document-tokens-inline').html('<strong>Tokens:</strong> ' + formatCharCount(tokenCount));
        
        // Calcola e visualizza la spesa stimata
        updateEstimatedCost(tokenCount);
        
        showDocumentNotification('<strong>✓ Estrazione OCR completata!</strong> ' + 
            formatCharCount(extractedDocumentContent.length) + ' caratteri estratti', 'success');
        
        // Show successful extraction message
        $('#' + PREFIX + 'analysis-results').html(
            '<div class="extraction-success">' +
            '<p><strong>✓ Estrazione OCR completata!</strong></p>' +
            '<p>Sono stati estratti ' + formatCharCount(extractedDocumentContent.length) + ' caratteri.</p>' +
            '<p>Puoi ora inserire una descrizione o domanda e cliccare su "Analizza" per procedere.</p>' +
            '</div>'
        );
    }

    // Funzione per stimare il conteggio dei token
    function estimateTokenCount(text) {
        if (!text) return 0;
        // Stima approssimativa: i token sono ~4 caratteri in media per il testo inglese
        return Math.ceil(text.length / 4);
    }

    // Funzione per formattare il conteggio dei caratteri
    function formatCharCount(count) {
        if (count >= 1000) {
            return (count / 1000).toFixed(1) + 'k';
        }
        return count.toString();
    }

    // Funzione per mostrare notifiche nel Document Analizer
    function showDocumentNotification(message, type = 'success', autoDismiss = true) {
        const $notificationArea = $('#' + PREFIX + 'document-notification-area');
        const $notificationContent = $notificationArea.find('.notification-content');
        
        $notificationContent.html(message);
        $notificationArea.removeClass('error processing');
        
        if (type === 'error') {
            $notificationArea.addClass('error');
            autoDismiss = false;
        } else if (type === 'processing') {
            $notificationArea.addClass('processing');
            autoDismiss = false;
        }
        
        $notificationArea.fadeIn(300);
        
        if (autoDismiss) {
            clearTimeout($notificationArea.data('dismiss-timer'));
            const timer = setTimeout(function() {
                hideDocumentNotification();
            }, 4000);
            $notificationArea.data('dismiss-timer', timer);
        }
        
        return $notificationArea;
    }
    
    // Funzione per nascondere le notifiche
    function hideDocumentNotification() {
        $('#' + PREFIX + 'document-notification-area').fadeOut(300);
    }

    // Event handlers per il Document Analizer
    
    // Custom logo upload handling
    $('#' + PREFIX + 'custom-logo-upload').on('change', function() {
        const file = this.files[0];
        if (file) {
            if (!file.type.match('image.*')) {
                alert('Per favore seleziona un file immagine (JPG, PNG, GIF, etc.)');
                $(this).val('');
                $('#' + PREFIX + 'logo-preview').attr('src', '').hide();
                return;
            }
            
            customLogo = file;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    const imgWidth = this.width;
                    const imgHeight = this.height;
                    
                    let displayHeight = imgHeight;
                    let displayWidth = imgWidth;
                    
                    if (imgHeight > 50) {
                        const ratio = imgWidth / imgHeight;
                        displayHeight = 50;
                        displayWidth = Math.round(displayHeight * ratio);
                    }
                    
                    $('#' + PREFIX + 'logo-preview')
                        .attr('src', e.target.result)
                        .css({
                            'height': displayHeight + 'px',
                            'width': displayWidth + 'px',
                            'object-fit': 'contain',
                            'max-height': '50px'
                        })
                        .show();
                    
                    if (imgHeight > 50) {
                        $('#' + PREFIX + 'logo-dimensions-info').html(`L'immagine originale (${imgWidth}x${imgHeight}px) verrà ridimensionata a ${displayWidth}x${displayHeight}px per adattarsi al documento`).show();
                    } else {
                        $('#' + PREFIX + 'logo-dimensions-info').html(`Dimensioni logo: ${imgWidth}x${imgHeight}px`).show();
                    }
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        } else {
            $('#' + PREFIX + 'logo-preview').attr('src', '').hide();
            $('#' + PREFIX + 'logo-dimensions-info').hide();
            customLogo = null;
        }
    });

    // Analysis title field handling
    $('#' + PREFIX + 'analysis-title').on('change keyup', function() {
        currentDocumentTitle = $(this).val();
    });

    // Document upload handling
    $('#' + PREFIX + 'document-upload').on('change', function() {
        handleFileUpload(this.files[0], 'document');
    });

    // Image upload handling
    $('#' + PREFIX + 'image-upload').on('change', function() {
        handleFileUpload(this.files[0], 'image');
    });

    // Funzione per gestire l'upload dei file
    function handleFileUpload(file, type) {
        if (!file) return;

        currentDocumentFile = file;
        
        // Mostra info del file
        const fileInfo = {
            name: file.name,
            size: formatFileSize(file.size),
            type: file.type
        };
        
        $('#' + PREFIX + 'document-name-inline').html('<strong>File:</strong> ' + fileInfo.name);
        $('#' + PREFIX + 'document-size-inline').html('<strong>Size:</strong> ' + fileInfo.size);
        $('#' + PREFIX + 'document-type-inline').html('<strong>Type:</strong> ' + fileInfo.type);
        
        if (type === 'image') {
            // Inizializza OCR per le immagini
            const processor = initOcrProcessor();
            if (processor) {
                processor.processImage(file);
            }
        } else {
            // Per PDF/Word, mostra nel visualizzatore
            displayDocument(file);
        }
    }

    // Funzione per formattare la dimensione del file
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Funzione per visualizzare il documento
    function displayDocument(file) {
        const url = URL.createObjectURL(file);
        $('#' + PREFIX + 'document-frame').attr('src', url).show();
        $('#' + PREFIX + 'document-display').show();
        $('#' + PREFIX + 'zoom-in, #' + PREFIX + 'zoom-out').show();

        // Avvia l'estrazione del testo per documenti PDF/Word
        if (file.type === 'application/pdf' ||
            file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
            file.type === 'application/msword') {
            extractDocumentContent(file);
        }
    }

    // Funzione per estrarre il contenuto del documento senza analisi
    function extractDocumentContent(file) {
        if (!file) return;

        // Mostra messaggio di caricamento nell'area notifiche
        showDocumentNotification('<div class="spinner"></div> Estrazione del testo in corso...', 'processing');

        // Se il contenuto è già stato estratto dai file Word convertiti, usa quello
        if (file.type === 'application/pdf' && file.name.match(/\.docx?\.pdf$/i) && extractedDocumentContent) {
            console.log('Using already extracted content from Word conversion');

            // Notifica di successo nell'area notifiche
            hideDocumentNotification();
            showDocumentNotification('<strong>✓ Documento caricato con successo!</strong> Testo estratto dalla conversione Word precedente.', 'success');

            // Aggiorna le informazioni inline del documento con il conteggio caratteri
            $('#' + PREFIX + 'document-chars-inline').html('<strong>Chars:</strong> ' + formatCharCount(extractedDocumentContent.length));

            const tokenCount = estimateTokenCount(extractedDocumentContent);
            $('#' + PREFIX + 'document-tokens-inline').html('<strong>Tokens:</strong> ' + formatCharCount(tokenCount));

            // Calcola e visualizza la spesa stimata
            updateEstimatedCost(tokenCount);

            return;
        }

        var formData = new FormData();
        formData.append('action', 'analyze_document');
        formData.append('nonce', documentAnalizerParams.nonce);
        formData.append('document_file', file);
        formData.append('save_content', 'true');

        console.log('FormData created for extraction:', {
            action: 'analyze_document',
            has_file: formData.has('document_file'),
            file_name: file.name,
            file_type: file.type,
            file_size: file.size,
            nonce: documentAnalizerParams.nonce ? 'presente' : 'mancante'
        });

        $.ajax({
            url: documentAnalizerParams.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            timeout: 120000, // Aumentato a 2 minuti per file più grandi
            beforeSend: function(xhr) {
                console.log('Starting text extraction request to ' + documentAnalizerParams.ajaxUrl);
            },
            success: function(response) {
                console.log('Text extraction response received:', response);

                // Verifica che la risposta sia un oggetto valido
                if (typeof response !== 'object') {
                    try {
                        response = JSON.parse(response);
                        console.log('Response parsed from string to object');
                    } catch (e) {
                        console.error('Failed to parse response:', e);

                        // Mostra errore nell'area notifiche
                        showDocumentNotification('Errore nel formato della risposta dal server. Controlla la console per i dettagli.', 'error');

                        // Mostra anche nell'area risultati analisi
                        if ($('#' + PREFIX + 'analysis-results').find('.analysis-container').length === 0) {
                            $('#' + PREFIX + 'analysis-results').html('<p class="error">Errore nel formato della risposta dal server. Controlla la console per i dettagli.</p>');
                        }
                        return;
                    }
                }

                if (response.success) {
                    if (response.data && response.data.document_content) {
                        extractedDocumentContent = response.data.document_content;

                        // Notifica di successo nell'area notifiche
                        showDocumentNotification('<strong>✓ Estrazione del testo completata!</strong> Sono stati estratti ' +
                            formatCharCount(extractedDocumentContent.length) + ' caratteri.', 'success', true);

                        // Aggiorna le informazioni inline del documento con il conteggio caratteri
                        $('#' + PREFIX + 'document-chars-inline').html('<strong>Chars:</strong> ' + formatCharCount(extractedDocumentContent.length));

                        const tokenCount = estimateTokenCount(extractedDocumentContent);
                        $('#' + PREFIX + 'document-tokens-inline').html('<strong>Tokens:</strong> ' + formatCharCount(tokenCount));

                        // Calcola e visualizza la spesa stimata
                        updateEstimatedCost(tokenCount);

                        // Aggiorna solo i risultati dell'analisi se non esiste un contenitore di analisi
                        if ($('#' + PREFIX + 'analysis-results').find('.analysis-container').length === 0) {
                            // Messaggio di conferma migliorato con stile
                            $('#' + PREFIX + 'analysis-results').html(
                                '<div class="extraction-success">' +
                                '<p><strong>✓ Estrazione del testo completata!</strong></p>' +
                                '<p>Sono stati estratti ' + formatCharCount(extractedDocumentContent.length) + ' caratteri.</p>' +
                                '<p>Puoi ora inserire una descrizione o domanda e cliccare su "Analizza" per procedere.</p>' +
                                '</div>'
                            );

                            // Fai scomparire automaticamente il messaggio di successo dell'estrazione dopo 4 secondi
                            setTimeout(function() {
                                $('.extraction-success').fadeOut(1000);
                            }, 4000);
                        }

                        console.log('Document content saved to variable, length:', extractedDocumentContent.length);
                    } else {
                        console.error('No document content in response:', response);

                        // Mostra errore nell'area notifiche
                        showDocumentNotification('Estrazione del testo completata, ma nessun contenuto disponibile. Il documento potrebbe essere protetto o non contenere testo estraibile.', 'error');

                        // Aggiorna solo se non esistono risultati di analisi
                        if ($('#' + PREFIX + 'analysis-results').find('.analysis-container').length === 0) {
                            $('#' + PREFIX + 'analysis-results').html('<p class="error">Estrazione del testo completata, ma nessun contenuto disponibile. Il documento potrebbe essere protetto o non contenere testo estraibile.</p>');
                        }
                    }

                    // Se il titolo non è impostato, utilizza il nome del file
                    if (!currentDocumentTitle) {
                        currentDocumentTitle = file.name.replace(/\.[^/.]+$/, ""); // Rimuove l'estensione
                        $('#' + PREFIX + 'analysis-title').val(currentDocumentTitle);
                    }
                } else {
                    let errorMessage = 'Errore durante l\'estrazione del testo';

                    if (response.data && response.data.message) {
                        errorMessage += ': ' + response.data.message;
                    }

                    console.error('Text extraction failed:', errorMessage);

                    // Mostra errore nell'area notifiche
                    showDocumentNotification(errorMessage, 'error');

                    // Aggiorna solo se non esistono risultati di analisi
                    if ($('#' + PREFIX + 'analysis-results').find('.analysis-container').length === 0) {
                        $('#' + PREFIX + 'analysis-results').html('<p class="error">' + errorMessage + '</p>');
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('Text extraction AJAX error:', error);

                let errorMessage = 'Errore di connessione durante l\'estrazione del testo';
                if (status === 'timeout') {
                    errorMessage = 'Timeout durante l\'estrazione del testo. Il file potrebbe essere troppo grande o il server sovraccarico.';
                }

                // Mostra errore nell'area notifiche
                showDocumentNotification(errorMessage, 'error');

                // Aggiorna solo se non esistono risultati di analisi
                if ($('#' + PREFIX + 'analysis-results').find('.analysis-container').length === 0) {
                    $('#' + PREFIX + 'analysis-results').html('<p class="error">' + errorMessage + '</p>');
                }
            }
        });
    }

    // Preset queries handling
    $('#' + PREFIX + 'preset-queries').on('change', function() {
        const selectedQuery = $(this).val();
        if (selectedQuery) {
            $('#' + PREFIX + 'document-description').val(selectedQuery);
        }
    });

    // Analyze button
    $('#' + PREFIX + 'analyze-description').on('click', function() {
        analyzeDocument();
    });

    // Clear button
    $('#' + PREFIX + 'clear-document').on('click', function() {
        clearDocument();
    });

    // Export PDF button
    $('#' + PREFIX + 'export-pdf').on('click', function() {
        exportToPDF();
    });

    // Save analysis button
    $('#' + PREFIX + 'save-analysis').on('click', function() {
        saveAnalysis();
    });

    // Zoom controls
    $('#' + PREFIX + 'zoom-in').on('click', function() {
        zoomLevel += 0.1;
        updateZoom();
    });

    $('#' + PREFIX + 'zoom-out').on('click', function() {
        zoomLevel = Math.max(0.5, zoomLevel - 0.1);
        updateZoom();
    });

    function updateZoom() {
        $('#' + PREFIX + 'document-frame').css('transform', `scale(${zoomLevel})`);
    }

    // Funzione principale per analizzare il documento
    function analyzeDocument() {
        const description = $('#' + PREFIX + 'document-description').val();
        const annotations = $('#' + PREFIX + 'document-annotations').val();
        
        if (!extractedDocumentContent && !currentDocumentFile) {
            showDocumentNotification('Carica prima un documento', 'error');
            return;
        }
        
        if (!description.trim()) {
            showDocumentNotification('Inserisci una descrizione o domanda', 'error');
            return;
        }

        // Prepara i dati per l'analisi (riutilizza la logica del Document Viewer)
        const formData = new FormData();
        formData.append('action', 'analyze_document');
        formData.append('nonce', documentAnalizerParams.nonce);
        formData.append('description', description);
        formData.append('annotations', annotations);
        formData.append('title', currentDocumentTitle);
        formData.append('content', extractedDocumentContent);

        if (currentDocumentFile) {
            formData.append('document', currentDocumentFile);
        }

        if (customLogo) {
            formData.append('logo', customLogo);
        }

        // Invio richiesta AJAX
        $.ajax({
            url: documentAnalizerParams.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            beforeSend: function() {
                // Mostra lo spinner durante l'analisi
                showDocumentNotification('<div class="spinner"></div> Fase 2: Analisi AI in corso...', 'processing');
            },
            success: function(response) {
                hideDocumentNotification();
                if (response.success) {
                    $('#' + PREFIX + 'analysis-results').html(response.data.html);
                    currentAnalysisResults = response.data.html;
                    
                    // Aggiorna le statistiche
                    if (response.data.stats) {
                        updateAnalizerStats(response.data.stats);
                    }
                    
                    showDocumentNotification('Analisi completata con successo!', 'success');
                } else {
                    showDocumentNotification('Errore durante l\'analisi: ' + response.data.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                hideDocumentNotification();
                showDocumentNotification('Errore di connessione: ' + error, 'error');
                console.error('[Document Analizer] AJAX error:', error);
            }
        });
    }

    // Funzioni principali del Document Analizer
    
    // Funzione per cancellare il documento
    function clearDocument() {
        if (!confirm(documentAnalizerParams.i18n.clearConfirm)) {
            return;
        }
        
        $.ajax({
            url: documentAnalizerParams.ajaxUrl,
            type: 'POST',
            data: {
                action: 'analizer_clear_document',
                nonce: documentAnalizerParams.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Reset all form fields
                    $('#' + PREFIX + 'analysis-title').val('');
                    $('#' + PREFIX + 'document-description').val('');
                    $('#' + PREFIX + 'document-annotations').val('');
                    $('#' + PREFIX + 'analysis-results').html('');
                    $('#' + PREFIX + 'document-display').hide();
                    $('#' + PREFIX + 'document-info-inline').hide();
                    $('#' + PREFIX + 'logo-preview').hide();
                    $('#' + PREFIX + 'logo-dimensions-info').hide();
                    
                    // Reset variables
                    currentDocumentFile = null;
                    currentPdfFile = null;
                    currentDocumentTitle = '';
                    currentAnalysisResults = '';
                    extractedDocumentContent = '';
                    customLogo = null;
                    analysisDisplayed = false;
                    
                    showDocumentNotification('Documento cancellato con successo', 'success');
                    console.log('✅ Document Analizer: Documento cancellato');
                } else {
                    showDocumentNotification('Errore durante la cancellazione', 'error');
                }
            },
            error: function() {
                showDocumentNotification('Errore di connessione', 'error');
            }
        });
    }
    
    // Funzione per esportare in PDF
    function exportToPDF() {
        if (!currentAnalysisResults) {
            showDocumentNotification('Nessuna analisi da esportare', 'error');
            return;
        }
        
        showDocumentNotification('Generazione PDF in corso...', 'processing');
        
        const formData = new FormData();
        formData.append('action', 'analizer_export_analysis_pdf');
        formData.append('nonce', documentAnalizerParams.nonce);
        formData.append('title', currentDocumentTitle || 'Analisi Documento');
        formData.append('analysis_html', currentAnalysisResults);
        formData.append('description', $('#' + PREFIX + 'document-description').val());
        formData.append('annotations', $('#' + PREFIX + 'document-annotations').val());
        
        // Aggiungi logo personalizzato se presente
        if (customLogo) {
            formData.append('custom_logo', customLogo);
        }
        
        // Aggiungi file originale se presente
        if (currentDocumentFile) {
            formData.append('uploaded_pdf', currentDocumentFile);
        }
        
        $.ajax({
            url: documentAnalizerParams.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                hideDocumentNotification();
                if (response.success) {
                    showDocumentNotification('PDF generato con successo', 'success');
                    console.log('✅ Document Analizer: PDF esportato');
                } else {
                    showDocumentNotification('Errore durante la generazione PDF: ' + (response.data.message || 'Errore sconosciuto'), 'error');
                }
            },
            error: function() {
                hideDocumentNotification();
                showDocumentNotification('Errore di connessione durante l\'esportazione PDF', 'error');
            }
        });
    }
    
    // Funzione per salvare l'analisi
    function saveAnalysis() {
        if (!currentAnalysisResults) {
            showDocumentNotification('Nessuna analisi da salvare', 'error');
            return;
        }
        
        const title = currentDocumentTitle || 'Analisi ' + new Date().toLocaleDateString();
        
        showDocumentNotification('Salvataggio in corso...', 'processing');
        
        const formData = new FormData();
        formData.append('action', 'analizer_save_analysis');
        formData.append('nonce', documentAnalizerParams.nonce);
        formData.append('title', title);
        formData.append('query', $('#' + PREFIX + 'document-description').val());
        formData.append('analysis_results', currentAnalysisResults);
        
        // Aggiungi logo personalizzato se presente
        if (customLogo) {
            formData.append('logo', customLogo);
        }
        
        // Aggiungi file originale se presente
        if (currentDocumentFile) {
            formData.append('document', currentDocumentFile);
        }
        
        $.ajax({
            url: documentAnalizerParams.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                hideDocumentNotification();
                if (response.success) {
                    showDocumentNotification('Analisi salvata con successo', 'success');
                    $('#' + PREFIX + 'save-result-message').html('<span style="color: green;">✓ Salvato</span>').fadeIn();
                    setTimeout(() => {
                        $('#' + PREFIX + 'save-result-message').fadeOut();
                    }, 3000);
                    console.log('✅ Document Analizer: Analisi salvata con ID:', response.data.analysis_id);
                } else {
                    showDocumentNotification('Errore durante il salvataggio: ' + (response.data.message || 'Errore sconosciuto'), 'error');
                }
            },
            error: function() {
                hideDocumentNotification();
                showDocumentNotification('Errore di connessione durante il salvataggio', 'error');
            }
        });
    }

    // Funzione per aggiornare le statistiche del Document Analizer
    function updateAnalizerStats(data) {
        if (!data) return;
        
        // Aggiorna i valori nelle statistiche del Document Analizer
        const costElement = $('#' + PREFIX + 'cost-estimate');
        const actualCostElement = $('#' + PREFIX + 'actual-cost');
        const creditsElement = $('#' + PREFIX + 'credits-available');
        
        if (data.actual_cost !== undefined && actualCostElement.length) {
            const formattedCost = '€' + parseFloat(data.actual_cost).toFixed(2).replace('.', ',');
            actualCostElement.text(formattedCost);
            if (costElement.length) {
                costElement.text(formattedCost);
            }
        }
        
        if (data.credits_available !== undefined && creditsElement.length) {
            const formattedCredit = '€' + parseFloat(data.credits_available).toFixed(2).replace('.', ',');
            creditsElement.text(formattedCredit);
        }
        
        console.log('[Document Analizer] Statistiche aggiornate:', data);
    }

    // Sincronizzazione credito dal database (come nel Document Viewer)
    function syncAnalizerCredit() {
        if (typeof subscriberManagementAjax !== "undefined" && subscriberManagementAjax.ajax_url) {
            $.ajax({
                url: subscriberManagementAjax.ajax_url,
                type: "POST",
                data: {
                    action: "get_current_credit",
                    nonce: subscriberManagementAjax.nonce
                },
                success: function(response) {
                    if (response.success && response.data.credit !== undefined) {
                        const creditElement = $('#' + PREFIX + 'credits-available');
                        if (creditElement.length) {
                            const formattedCredit = "€" + parseFloat(response.data.credit).toFixed(2).replace(".", ",");
                            creditElement.text(formattedCredit);
                            console.log("[Document Analizer] Credito sincronizzato:", formattedCredit);
                        }
                    }
                },
                error: function(xhr, status, error) {
                    console.warn("[Document Analizer] Errore sincronizzazione credito:", error);
                }
            });
        }
    }

    // Prevenzione submit accidentali del form
    $('#' + PREFIX + 'form').on('submit', function(e) {
        e.preventDefault();
        return false;
    });

    // Nascondi controlli zoom e document display inizialmente
    $('#' + PREFIX + 'zoom-in, #' + PREFIX + 'zoom-out, #' + PREFIX + 'document-display').hide();

    // Inizializzazione
    console.log('[Document Analizer] Widget inizializzato');
    
    // Sincronizza il credito all'avvio
    setTimeout(syncAnalizerCredit, 1000);
    
    // Sincronizza il credito ogni 30 secondi
    setInterval(syncAnalizerCredit, 30000);
    
    // Ascolta eventi di aggiornamento credito da altri widget
    $(document).on("credit-updated", function(event, newCredit) {
        const creditElement = $('#' + PREFIX + 'credits-available');
        if (creditElement.length && newCredit !== undefined) {
            const formattedCredit = "€" + parseFloat(newCredit).toFixed(2).replace(".", ",");
            creditElement.text(formattedCredit);
            console.log("[Document Analizer] Credito aggiornato da evento:", formattedCredit);
        }
    });
});
