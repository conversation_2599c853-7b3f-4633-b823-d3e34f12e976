!function(){"use strict";var t={n:function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,{a:r}),r},d:function(e,r){for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},o:function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}},e=window.ctEvents,r=t.n(e),n=window.ctFrontend;function i(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function o(t){return function(t){if(Array.isArray(t))return i(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return i(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?i(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var a=function(t,e,r){return Math.max(t,Math.min(e,r))},c=function(t,e,r){return e[0]+(e[1]-e[0])/(t[1]-t[0])*(r-t[0])},s=function(t){var e=getComputedStyle(t),r=getComputedStyle(t.firstElementChild);if("none"===e.display)return 0;var n=parseFloat(e.borderTopWidth)+parseFloat(e.borderBottomWidth)+parseFloat(r.borderTopWidth)+parseFloat(r.borderBottomWidth),i=parseFloat(e.getPropertyValue("--height"));if(t.querySelector('[data-items] > [data-id="logo"]')){var o=getComputedStyle(t.querySelector('[data-items] > [data-id="logo"]')),a=parseFloat(o.height);if(a+=parseFloat(o.marginTop)+parseFloat(o.marginBottom),t.querySelector(".site-logo-container")){var c=getComputedStyle(t.querySelector(".site-logo-container")),s=parseFloat(c.getPropertyValue("--logo-shrink-height")||0);s>0&&(a=a-s+parseFloat(c.getPropertyValue("--logo-max-height")||50))}a>i&&(i=a)}var l=['[data-items] > [data-id*="widget-area"]','[data-items] > [data-id*="content-block"]','[data-items] > [data-id*="text"]'].concat(['[data-items] > [data-id*="menu"]']).reduce((function(e,r){if(t.querySelector(r)){var n=getComputedStyle(t.querySelector(r)),i=parseFloat(n.height);if(i>e)return i}return e}),0);return l>i&&(i=l),i+n},l=function(t){if(t.blcInitialHeight)return t.blcInitialHeight;var e=t.firstElementChild;t.firstElementChild.firstElementChild&&(e=t.firstElementChild.firstElementChild);var r=e.getBoundingClientRect().height;return t.blcInitialHeight=r,r},d=function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(t.blcStickyHeight)return t.blcStickyHeight;var r=l(t),n=getComputedStyle(t),i=getComputedStyle(t.firstElementChild);if(t.closest('[data-sticky*="yes"]')){var o=parseFloat(n.borderTopWidth)+parseFloat(n.borderBottomWidth)+parseFloat(i.borderTopWidth)+parseFloat(i.borderBottomWidth);e||(o=0);var a=t.getBoundingClientRect().height-o,c=s(t);if(!(t.querySelector(".site-logo-container")&&parseFloat(getComputedStyle(t.querySelector(".site-logo-container")).getPropertyValue("--logo-sticky-shrink")||1)<1)&&(Math.round(a)!==Math.round(r)||Math.round(r)>Math.round(c)))return t.blcStickyHeight=t.getBoundingClientRect().height,a}var d=100;t.dataset.row.includes("middle")&&(d=n.getPropertyValue("--sticky-shrink"));return d&&(r*=parseFloat(d)/100),r},u=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){return 0},e=document.querySelector(".ct-floating-bar");e&&e.style.setProperty("--header-sticky-height-animated",t())},y=null,f=function(t){var e=t.stickyContainer,r=t.startPosition;o(e.querySelectorAll('[data-row*="middle"]')).map((function(t){if(t.querySelector('[data-id="logo"] .site-logo-container')){var e=t.querySelector('[data-id="logo"] .site-logo-container'),n=function(t){var e=t.logo,r=t.row;if(y)return y;var n=parseFloat(getComputedStyle(e).getPropertyValue("--logo-max-height")||50),i=parseFloat(getComputedStyle(e).getPropertyValue("--logo-sticky-shrink").toString().replace(",",".")||1),o=l(r),a=d(r);return y={initialHeight:n,stickyShrink:i,rowInitialHeight:o,rowStickyHeight:a}}({logo:e,row:t}),i=n.initialHeight,o=n.stickyShrink,s=n.rowInitialHeight,u=n.rowStickyHeight,f=i*o;1!==o&&e.style.setProperty("--logo-shrink-height","".concat(c([r,r+Math.abs(s===u?i-f:s-u)],[1,o],a(r,r+Math.abs(s===u?i-f:s-u),scrollY))*i,"px"))}}))},h=null,g=function(t){var e=t.stickyContainer,r=(t.containerInitialHeight,t.startPosition);e.querySelector('[data-row*="middle"]')&&[e.querySelector('[data-row*="middle"]')].map((function(t){var e=function(t){var e=t.row;if(h)return h;var r=l(e),n=d(e);return h={rowInitialHeight:r,rowStickyHeight:n}}({row:t}),n=e.rowInitialHeight,i=e.rowStickyHeight;if(n!==i){var o=i;o=c([r,r+Math.abs(n-i)],[n,i],a(r,r+Math.abs(n-i),scrollY)),t.style.setProperty("--shrink-height","".concat(Math.round(o),"px"))}}))},m=function(t){var e=t.stickyContainer,r=o(e.querySelectorAll("[data-row]")).reduce((function(t,e){return t+d(e,!1)}),0);return{stickyContainerHeight:r,stickyContainerHeightAbsolute:r+parseFloat(getComputedStyle(e).top)}},p=null;function k(t,e){var r=function(){t.removeEventListener("transitionend",n),e()},n=function(e){e.target===t&&r()};t.addEventListener("transitionend",n)}var v=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"yes";Array.from(t.querySelectorAll("[data-row][data-transparent-row]")).map((function(t){t.dataset.transparentRow=e}))},w=null,S={},C=null,b=null,x=!1,A=function(){h=null,y=null,w=null,C=null,b=null,P=null,x=!1};if(r().on("blocksy:sticky:compute",(function(){setTimeout((function(){A(),O()}),100)})),window.wp&&wp.customize&&wp.customize.selectiveRefresh){var H=!1;wp.customize.selectiveRefresh.bind("partial-content-rendered",(function(t){H||(H=!0,setTimeout((function(){A(),x=!0,O(),H=!1}),500))}))}var q=function(t){if(w)return w;-1===t.dataset.sticky.indexOf("shrink")&&t.dataset.sticky.indexOf("auto-hide");var e=t.closest("header").getBoundingClientRect().top+scrollY;if(e>0){var r=document.elementFromPoint(0,3);r&&function(t){for(var e=[];t&&t!==document;t=t.parentNode)e.push(t);return e}(r).map((function(t){return getComputedStyle(t).position})).indexOf("fixed")>-1&&(e-=r.getBoundingClientRect().height)}-1===t.dataset.sticky.indexOf("shrink")&&-1===t.dataset.sticky.indexOf("auto-hide")&&(e+=200);var n=t.parentNode,i=getComputedStyle(document.body),a=parseFloat(i.getPropertyValue("--header-sticky-offset")||0);a+=parseFloat(i.getPropertyValue("--theme-frame-size"))||0;var c=function(t){return t.filter((function(t){return 0!==t.height})).reduce((function(t,e){return t.indexOf(0)>-1?[].concat(o(t),[0]):[].concat(o(t),[e.sticky?0:e.height])}),[]).reduce((function(t,e){return t+e}),0)}(Array.from(n.parentNode.children).filter((function(t){return t.dataset.row||t.classList.contains("ct-sticky-container")})).map((function(t){return t.classList.contains("ct-sticky-container")?{sticky:!0}:{height:t.getBoundingClientRect().height}}))),s=c+e;s=s>0?s-a:s;var l=!0,d=document.querySelector(".woocommerce-store-notice");return d&&(l=!1,d.offsetHeight>0&&(l=!0)),l&&(w=s),s},P=null,O=function(){if(P!==scrollY){var t=document.querySelector('[data-device="'.concat((0,n.getCurrentScreen)(),'"] [data-sticky]'));if(t){var e=(0,n.getCurrentScreen)({withTablet:!0}),r=S[e],i=!r||x;r&&!x||(S[e]=o(t.querySelectorAll("[data-row]")).reduce((function(t,e){return t+s(e)}),0),r=S[e]),i&&(x=!1,t.parentNode.style.height="".concat(r,"px"));var c=q(t),l=C;if(null===l)l=t.closest("[data-device]").getBoundingClientRect().height,C=l;var y=b,h=t.dataset.sticky.split(":").filter((function(t){return"yes"!==t&&"no"!==t&&"fixed"!==t}));y||(y=o(t.querySelectorAll("[data-row]")).reduce((function(t,e){return t+d(e)}),0),b=parseInt(y),u((function(){return-1===h.indexOf("auto-hide")?y>o(t.querySelectorAll("[data-row]")).reduce((function(t,e){return t+s(e)}),0)?"".concat(y,"px"):"".concat(o(t.querySelectorAll("[data-row]")).reduce((function(t,e){return t+d(e)}),0),"px"):"0px"})));var w=c>0&&Math.abs(window.scrollY-c)<5||window.scrollY>c;h.indexOf("shrink")>-1&&(w=c>0?window.scrollY>=c:window.scrollY>0),setTimeout((function(){w&&-1===document.body.dataset.header.indexOf("shrink")&&(document.body.dataset.header="".concat(document.body.dataset.header,":shrink")),!w&&document.body.dataset.header.indexOf("shrink")>-1&&(document.body.dataset.header=document.body.dataset.header.replace(":shrink",""))}),300);var A=scrollY;h.indexOf("shrink")>-1&&function(t){var e=t.containerInitialHeight,r=t.stickyContainer,n=(t.stickyContainerHeight,t.isSticky),i=t.startPosition,a=t.stickyComponents;if(0===i&&0===window.scrollY&&(r.dataset.sticky=["fixed"].concat(o(a)).join(":")),n){if(a.indexOf("yes")>-1)return;-1===r.dataset.sticky.indexOf("yes")&&(v(r,"no"),r.dataset.sticky=["yes"].concat(o(a)).join(":")),f({stickyContainer:r,startPosition:i}),g({stickyContainer:r,containerInitialHeight:e,startPosition:i})}else Array.from(r.querySelectorAll("[data-row]")).map((function(t){return t.removeAttribute("style")})),Array.from(r.querySelectorAll('[data-row*="middle"] .site-logo-container')).map((function(t){return t.removeAttribute("style")})),v(r,"yes"),0===i&&window.scrollY<=0?r.dataset.sticky=["fixed"].concat(o(a)).join(":"):r.dataset.sticky=a.join(":")}({stickyContainer:t,stickyContainerHeight:y,containerInitialHeight:r,isSticky:w,startPosition:c,stickyComponents:h}),h.indexOf("auto-hide")>-1&&function(t){var e=t.currentScrollY,r=t.stickyContainer,n=t.containerInitialHeight,i=t.headerInitialHeight,c=t.startPosition,s=t.isSticky,l=t.stickyComponents;s&&e-t.prevScrollY==0&&u((function(){return"0px"})),s?-1===r.dataset.sticky.indexOf("yes")&&e>2*i+c&&(r.dataset.sticky=["yes"].concat(o(l)).join(":"),f({stickyContainer:r,startPosition:c}),g({stickyContainer:r,containerInitialHeight:n,startPosition:c}),v(r,"no"),document.body.removeAttribute("style")):(Array.from(r.querySelectorAll("[data-row]")).map((function(t){return t.removeAttribute("style")})),Array.from(r.querySelectorAll('[data-row*="middle"] .site-logo-container')).map((function(t){return t.removeAttribute("style")})),r.dataset.sticky=o(l).join(":"),v(r,"yes"),u((function(){return"0px"})),p=null),null===p&&(p=1e3);var d=p+t.prevScrollY-e,y=0;if(e>2*i+c||r.dataset.sticky.indexOf("yes")>-1){if(e<=c)y=0;else if(e>t.prevScrollY){var h=m({stickyContainer:r}).stickyContainerHeightAbsolute;y=Math.abs(d)>h?-h:d}else y=d>0?0:d;r.style.transform="translateY(".concat(y,"px)"),p=y}else r.removeAttribute("style");r.dataset.sticky.indexOf("yes")>-1&&(e<=c||e>t.prevScrollY||(f({stickyContainer:r,startPosition:c}),g({stickyContainer:r,containerInitialHeight:n,startPosition:c}))),u((function(){var t=m({stickyContainer:r}).stickyContainerHeight,e=a(0,t-Math.abs(y),t);return-1===r.dataset.sticky.indexOf("yes")&&(e=0),"".concat(e,"px")}))}({stickyContainer:t,isSticky:w,startPosition:c,stickyComponents:h,containerInitialHeight:r,stickyContainerHeight:y,headerInitialHeight:l,currentScrollY:A,prevScrollY:P}),(h.indexOf("slide")>-1||h.indexOf("fade")>-1)&&function(t){var e=t.stickyContainer,r=t.isSticky,n=t.startPosition,i=t.stickyComponents;r?(-1===e.dataset.sticky.indexOf("yes")&&(e.dataset.sticky=["yes-start"].concat(o(i)).join(":"),setTimeout((function(){e.dataset.sticky=e.dataset.sticky.replace("yes-start","yes-end"),k(e,(function(){e.dataset.sticky=e.dataset.sticky.replace("yes-end","yes")}))}),1)),v(e,"no")):-1===e.dataset.sticky.indexOf("yes-hide")&&e.dataset.sticky.indexOf("yes:")>-1&&(Math.abs(window.scrollY-n)>10?(e.dataset.sticky=i.join(":"),setTimeout((function(){Array.from(e.querySelectorAll("[data-row]")).map((function(t){return t.removeAttribute("style")}))}),300),v(e,"yes")):(e.dataset.sticky=["yes-hide-start"].concat(o(i)).join(":"),requestAnimationFrame((function(){e.dataset.sticky=e.dataset.sticky.replace("yes-hide-start","yes-hide-end"),k(e,(function(){e.dataset.sticky=i.join(":"),setTimeout((function(){Array.from(e.querySelectorAll("[data-row]")).map((function(t){return t.removeAttribute("style")}))}),300),v(e,"yes")}))}))))}({stickyContainer:t,isSticky:w,startPosition:c,stickyComponents:h}),P=A}}},F=function(){if(document.querySelector("header [data-sticky]")){var t=window.width;window.addEventListener("resize",(function(e){window.width!==t&&(t=window.width,A(),O(e),r().trigger("ct:header:update"))}),!1),window.addEventListener("orientationchange",(function(t){A(),O(t),r().trigger("ct:header:update")})),window.addEventListener("scroll",O,!1),window.addEventListener("load",O,!1),O()}};document.body.className.indexOf("e-preview")>-1?setTimeout((function(){F()}),500):F(),(0,n.registerDynamicChunk)("blocksy_sticky_header",{mount:function(t){}})}();