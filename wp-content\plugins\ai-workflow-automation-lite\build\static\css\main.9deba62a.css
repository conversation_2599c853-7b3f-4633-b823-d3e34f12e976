@import url(https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:ital,wght@0,400;0,700;1,400&display=swap);.sidebar-upgrade-button:hover{background-color:#fff!important;color:#764ba2!important}body{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;margin:0}code{font-family:source-code-pro,Menlo,Monaco,Consolas,Courier New,monospace}.react-flow{--xy-edge-stroke-default:#b1b1b7;--xy-edge-stroke-width-default:2;--xy-edge-stroke-selected-default:#555;--xy-connectionline-stroke-default:#b1b1b7;--xy-connectionline-stroke-width-default:2;--xy-attribution-background-color-default:#ffffff80;--xy-minimap-background-color-default:#fff;--xy-minimap-mask-background-color-default:#f0f0f099;--xy-minimap-mask-stroke-color-default:#0000;--xy-minimap-mask-stroke-width-default:1;--xy-minimap-node-background-color-default:#e2e2e2;--xy-minimap-node-stroke-color-default:#0000;--xy-minimap-node-stroke-width-default:2;--xy-background-color-default:#0000;--xy-background-pattern-dots-color-default:#91919a;--xy-background-pattern-lines-color-default:#eee;--xy-background-pattern-cross-color-default:#e2e2e2;--xy-node-color-default:inherit;--xy-node-border-default:1px solid #1a192b;--xy-node-background-color-default:#fff;--xy-node-group-background-color-default:#f0f0f040;--xy-node-boxshadow-hover-default:0 1px 4px 1px #00000014;--xy-node-boxshadow-selected-default:0 0 0 0.5px #1a192b;--xy-node-border-radius-default:3px;--xy-handle-background-color-default:#1a192b;--xy-handle-border-color-default:#fff;--xy-selection-background-color-default:#0059dc14;--xy-selection-border-default:1px dotted #0059dccc;--xy-controls-button-background-color-default:#fefefe;--xy-controls-button-background-color-hover-default:#f4f4f4;--xy-controls-button-color-default:inherit;--xy-controls-button-color-hover-default:inherit;--xy-controls-button-border-color-default:#eee;--xy-controls-box-shadow-default:0 0 2px 1px #00000014;--xy-edge-label-background-color-default:#fff;--xy-edge-label-color-default:inherit;--xy-resize-background-color-default:#3367d9;background-color:var(--xy-background-color-default);background-color:var(--xy-background-color,var(--xy-background-color-default));direction:ltr}.react-flow.dark{--xy-edge-stroke-default:#c083f3;--xy-edge-stroke-width-default:2;--xy-edge-stroke-selected-default:#ab54f3;--xy-connectionline-stroke-default:#c183f3d0;--xy-connectionline-stroke-width-default:2;--xy-attribution-background-color-default:#96969640;--xy-minimap-background-color-default:#141414;--xy-minimap-mask-background-color-default:#3c3c3c99;--xy-minimap-mask-stroke-color-default:#0000;--xy-minimap-mask-stroke-width-default:1;--xy-minimap-node-background-color-default:#2b2b2b;--xy-minimap-node-stroke-color-default:#0000;--xy-minimap-node-stroke-width-default:2;--xy-background-color-default:#141414;--xy-background-pattern-dots-color-default:#777;--xy-background-pattern-lines-color-default:#777;--xy-background-pattern-cross-color-default:#777;--xy-node-color-default:#f8f8f8;--xy-node-border-default:1px solid #3c3c3c;--xy-node-background-color-default:#1e1e1e;--xy-node-group-background-color-default:#f0f0f040;--xy-node-boxshadow-hover-default:0 1px 4px 1px #ffffff14;--xy-node-boxshadow-selected-default:0 0 0 0.5px #999;--xy-handle-background-color-default:#bebebe;--xy-handle-border-color-default:#1e1e1e;--xy-selection-background-color-default:#c8c8dc14;--xy-selection-border-default:1px dotted #c8c8dccc;--xy-controls-button-background-color-default:#2b2b2b;--xy-controls-button-background-color-hover-default:#3e3e3e;--xy-controls-button-color-default:#f8f8f8;--xy-controls-button-color-hover-default:#fff;--xy-controls-button-border-color-default:#5b5b5b;--xy-controls-box-shadow-default:0 0 2px 1px #00000014;--xy-edge-label-background-color-default:#141414;--xy-edge-label-color-default:#f8f8f8}.react-flow__background{background-color:var(--xy-background-color-default);background-color:var(--xy-background-color,var(--xy-background-color-props,var(--xy-background-color-default)));pointer-events:none;z-index:-1}.react-flow__container{height:100%;left:0;position:absolute;top:0;width:100%}.react-flow__pane{z-index:1}.react-flow__pane.draggable{cursor:grab}.react-flow__pane.dragging{cursor:grabbing}.react-flow__pane.selection{cursor:pointer}.react-flow__viewport{pointer-events:none;transform-origin:0 0;z-index:2}.react-flow__renderer{z-index:4}.react-flow__selection{z-index:6}.react-flow__nodesselection-rect:focus,.react-flow__nodesselection-rect:focus-visible{outline:none}.react-flow__edge-path{stroke:var(--xy-edge-stroke-default);stroke:var(--xy-edge-stroke,var(--xy-edge-stroke-default));stroke-width:var(--xy-edge-stroke-width-default);stroke-width:var(--xy-edge-stroke-width,var(--xy-edge-stroke-width-default));fill:none}.react-flow__connection-path{stroke:var(--xy-connectionline-stroke-default);stroke:var(--xy-connectionline-stroke,var(--xy-connectionline-stroke-default));stroke-width:var(--xy-connectionline-stroke-width-default);stroke-width:var(--xy-connectionline-stroke-width,var(--xy-connectionline-stroke-width-default));fill:none}.react-flow .react-flow__edges{position:absolute}.react-flow .react-flow__edges svg{overflow:visible;pointer-events:none;position:absolute}.react-flow__edge{pointer-events:visibleStroke}.react-flow__edge.selectable{cursor:pointer}.react-flow__edge.animated path{stroke-dasharray:5;animation:dashdraw .5s linear infinite}.react-flow__edge.animated path.react-flow__edge-interaction{stroke-dasharray:none;animation:none}.react-flow__edge.inactive{pointer-events:none}.react-flow__edge.selected,.react-flow__edge:focus,.react-flow__edge:focus-visible{outline:none}.react-flow__edge.selectable:focus .react-flow__edge-path,.react-flow__edge.selectable:focus-visible .react-flow__edge-path,.react-flow__edge.selected .react-flow__edge-path{stroke:var(--xy-edge-stroke-selected-default);stroke:var(--xy-edge-stroke-selected,var(--xy-edge-stroke-selected-default))}.react-flow__edge-textwrapper{pointer-events:all}.react-flow__edge .react-flow__edge-text{pointer-events:none;-webkit-user-select:none;user-select:none}.react-flow__connection{pointer-events:none}.react-flow__connection .animated{stroke-dasharray:5;animation:dashdraw .5s linear infinite}svg.react-flow__connectionline{overflow:visible;position:absolute;z-index:1001}.react-flow__nodes{pointer-events:none;transform-origin:0 0}.react-flow__node{box-sizing:border-box;cursor:default;pointer-events:all;position:absolute;transform-origin:0 0;-webkit-user-select:none;user-select:none}.react-flow__node.selectable{cursor:pointer}.react-flow__node.draggable{cursor:grab;pointer-events:all}.react-flow__node.draggable.dragging{cursor:grabbing}.react-flow__nodesselection{pointer-events:none;transform-origin:left top;z-index:3}.react-flow__nodesselection-rect{cursor:grab;pointer-events:all;position:absolute}.react-flow__handle{background-color:var(--xy-handle-background-color-default);background-color:var(--xy-handle-background-color,var(--xy-handle-background-color-default));border:1px solid var(--xy-handle-border-color,var(--xy-handle-border-color-default));border-radius:100%;height:6px;min-height:5px;min-width:5px;pointer-events:none;position:absolute;width:6px}.react-flow__handle.connectingfrom{pointer-events:all}.react-flow__handle.connectionindicator{cursor:crosshair;pointer-events:all}.react-flow__handle-bottom{bottom:0;left:50%;top:auto;transform:translate(-50%,50%)}.react-flow__handle-top{left:50%;top:0;transform:translate(-50%,-50%)}.react-flow__handle-left{left:0;top:50%;transform:translate(-50%,-50%)}.react-flow__handle-right{right:0;top:50%;transform:translate(50%,-50%)}.react-flow__edgeupdater{cursor:move;pointer-events:all}.react-flow__panel{margin:15px;position:absolute;z-index:5}.react-flow__panel.top{top:0}.react-flow__panel.bottom{bottom:0}.react-flow__panel.left{left:0}.react-flow__panel.right{right:0}.react-flow__panel.center{left:50%;transform:translateX(-50%)}.react-flow__attribution{background:var(--xy-attribution-background-color-default);background:var(--xy-attribution-background-color,var(--xy-attribution-background-color-default));font-size:10px;margin:0;padding:2px 3px}.react-flow__attribution a{color:#999;text-decoration:none}@keyframes dashdraw{0%{stroke-dashoffset:10}}.react-flow__edgelabel-renderer{pointer-events:none}.react-flow__edgelabel-renderer,.react-flow__viewport-portal{height:100%;left:0;position:absolute;top:0;-webkit-user-select:none;user-select:none;width:100%}.react-flow__minimap{background:var(--xy-minimap-background-color-default);background:var(
    --xy-minimap-background-color-props,var(--xy-minimap-background-color,var(--xy-minimap-background-color-default))
  )}.react-flow__minimap-svg{display:block}.react-flow__minimap-mask{fill:var(--xy-minimap-mask-background-color-default);fill:var(
      --xy-minimap-mask-background-color-props,var(--xy-minimap-mask-background-color,var(--xy-minimap-mask-background-color-default))
    );stroke:var(--xy-minimap-mask-stroke-color-default);stroke:var(
      --xy-minimap-mask-stroke-color-props,var(--xy-minimap-mask-stroke-color,var(--xy-minimap-mask-stroke-color-default))
    );stroke-width:var(--xy-minimap-mask-stroke-width-default);stroke-width:var(
      --xy-minimap-mask-stroke-width-props,var(--xy-minimap-mask-stroke-width,var(--xy-minimap-mask-stroke-width-default))
    )}.react-flow__minimap-node{fill:var(--xy-minimap-node-background-color-default);fill:var(
      --xy-minimap-node-background-color-props,var(--xy-minimap-node-background-color,var(--xy-minimap-node-background-color-default))
    );stroke:var(--xy-minimap-node-stroke-color-default);stroke:var(
      --xy-minimap-node-stroke-color-props,var(--xy-minimap-node-stroke-color,var(--xy-minimap-node-stroke-color-default))
    );stroke-width:var(--xy-minimap-node-stroke-width-default);stroke-width:var(
      --xy-minimap-node-stroke-width-props,var(--xy-minimap-node-stroke-width,var(--xy-minimap-node-stroke-width-default))
    )}.react-flow__background-pattern.dots{fill:var(--xy-background-pattern-dots-color-default);fill:var(
      --xy-background-pattern-color-props,var(--xy-background-pattern-color,var(--xy-background-pattern-dots-color-default))
    )}.react-flow__background-pattern.lines{stroke:var(--xy-background-pattern-lines-color-default);stroke:var(
      --xy-background-pattern-color-props,var(--xy-background-pattern-color,var(--xy-background-pattern-lines-color-default))
    )}.react-flow__background-pattern.cross{stroke:var(--xy-background-pattern-cross-color-default);stroke:var(
      --xy-background-pattern-color-props,var(--xy-background-pattern-color,var(--xy-background-pattern-cross-color-default))
    )}.react-flow__controls{box-shadow:var(--xy-controls-box-shadow-default);box-shadow:var(--xy-controls-box-shadow,var(--xy-controls-box-shadow-default));display:flex;flex-direction:column}.react-flow__controls.horizontal{flex-direction:row}.react-flow__controls-button{align-items:center;background:var(--xy-controls-button-background-color-default);background:var(--xy-controls-button-background-color,var(--xy-controls-button-background-color-default));border:none;border-bottom:1px solid var(
        --xy-controls-button-border-color-props,var(--xy-controls-button-border-color,var(--xy-controls-button-border-color-default))
      );color:var(--xy-controls-button-color-default);color:var(
      --xy-controls-button-color-props,var(--xy-controls-button-color,var(--xy-controls-button-color-default))
    );cursor:pointer;display:flex;height:26px;justify-content:center;padding:4px;-webkit-user-select:none;user-select:none;width:26px}.react-flow__controls-button svg{fill:currentColor;max-height:12px;max-width:12px;width:100%}.react-flow__edge.updating .react-flow__edge-path{stroke:#777}.react-flow__edge-text{font-size:10px}.react-flow__node.selectable:focus,.react-flow__node.selectable:focus-visible{outline:none}.react-flow__node-default.selectable:hover,.react-flow__node-group.selectable:hover,.react-flow__node-input.selectable:hover,.react-flow__node-output.selectable:hover{box-shadow:var(--xy-node-boxshadow-hover-default);box-shadow:var(--xy-node-boxshadow-hover,var(--xy-node-boxshadow-hover-default))}.react-flow__node-default.selectable.selected,.react-flow__node-default.selectable:focus,.react-flow__node-default.selectable:focus-visible,.react-flow__node-group.selectable.selected,.react-flow__node-group.selectable:focus,.react-flow__node-group.selectable:focus-visible,.react-flow__node-input.selectable.selected,.react-flow__node-input.selectable:focus,.react-flow__node-input.selectable:focus-visible,.react-flow__node-output.selectable.selected,.react-flow__node-output.selectable:focus,.react-flow__node-output.selectable:focus-visible{box-shadow:var(--xy-node-boxshadow-selected-default);box-shadow:var(--xy-node-boxshadow-selected,var(--xy-node-boxshadow-selected-default))}.react-flow__node-group{background-color:var(--xy-node-group-background-color-default);background-color:var(--xy-node-group-background-color,var(--xy-node-group-background-color-default))}.react-flow__nodesselection-rect,.react-flow__selection{background:var(--xy-selection-background-color-default);background:var(--xy-selection-background-color,var(--xy-selection-background-color-default));border:var(--xy-selection-border,var(--xy-selection-border-default))}.react-flow__nodesselection-rect:focus,.react-flow__nodesselection-rect:focus-visible,.react-flow__selection:focus,.react-flow__selection:focus-visible{outline:none}.react-flow__controls-button:hover{background:var(--xy-controls-button-background-color-hover-default);background:var(
        --xy-controls-button-background-color-hover-props,var(--xy-controls-button-background-color-hover,var(--xy-controls-button-background-color-hover-default))
      );color:var(--xy-controls-button-color-hover-default);color:var(
        --xy-controls-button-color-hover-props,var(--xy-controls-button-color-hover,var(--xy-controls-button-color-hover-default))
      )}.react-flow__controls-button:disabled{pointer-events:none}.react-flow__controls-button:disabled svg{fill-opacity:.4}.react-flow__controls-button:last-child{border-bottom:none}.react-flow__resize-control{position:absolute}.react-flow__resize-control.left,.react-flow__resize-control.right{cursor:ew-resize}.react-flow__resize-control.bottom,.react-flow__resize-control.top{cursor:ns-resize}.react-flow__resize-control.bottom.right,.react-flow__resize-control.top.left{cursor:nwse-resize}.react-flow__resize-control.bottom.left,.react-flow__resize-control.top.right{cursor:nesw-resize}.react-flow__resize-control.handle{background-color:var(--xy-resize-background-color-default);background-color:var(--xy-resize-background-color,var(--xy-resize-background-color-default));border:1px solid #fff;border-radius:1px;height:4px;transform:translate(-50%,-50%);width:4px}.react-flow__resize-control.handle.left{left:0;top:50%}.react-flow__resize-control.handle.right{left:100%;top:50%}.react-flow__resize-control.handle.top{left:50%;top:0}.react-flow__resize-control.handle.bottom{left:50%;top:100%}.react-flow__resize-control.handle.bottom.left,.react-flow__resize-control.handle.top.left{left:0}.react-flow__resize-control.handle.bottom.right,.react-flow__resize-control.handle.top.right{left:100%}.react-flow__resize-control.line{border-color:var(--xy-resize-background-color,var(--xy-resize-background-color-default));border-style:solid;border-width:0}.react-flow__resize-control.line.left,.react-flow__resize-control.line.right{height:100%;top:0;transform:translate(-50%);width:1px}.react-flow__resize-control.line.left{border-left-width:1px;left:0}.react-flow__resize-control.line.right{border-right-width:1px;left:100%}.react-flow__resize-control.line.bottom,.react-flow__resize-control.line.top{height:1px;left:0;transform:translateY(-50%);width:100%}.react-flow__resize-control.line.top{border-top-width:1px;top:0}.react-flow__resize-control.line.bottom{border-bottom-width:1px;top:100%}.react-flow__edge-textbg{fill:var(--xy-edge-label-background-color-default);fill:var(--xy-edge-label-background-color,var(--xy-edge-label-background-color-default))}.react-flow__edge-text{fill:var(--xy-edge-label-color-default);fill:var(--xy-edge-label-color,var(--xy-edge-label-color-default))}

/*!
 * Quill Editor v1.3.7
 * https://quilljs.com/
 * Copyright (c) 2014, Jason Chen
 * Copyright (c) 2013, salesforce.com
 */.ql-container{box-sizing:border-box;font-family:Helvetica,Arial,sans-serif;font-size:13px;height:100%;margin:0;position:relative}.ql-container.ql-disabled .ql-tooltip{visibility:hidden}.ql-container.ql-disabled .ql-editor ul[data-checked]>li:before{pointer-events:none}.ql-clipboard{height:1px;left:-100000px;overflow-y:hidden;position:absolute;top:50%}.ql-clipboard p{margin:0;padding:0}.ql-editor{word-wrap:break-word;box-sizing:border-box;height:100%;line-height:1.42;outline:none;overflow-y:auto;padding:12px 15px;tab-size:4;-moz-tab-size:4;text-align:left;white-space:pre-wrap}.ql-editor>*{cursor:text}.ql-editor blockquote,.ql-editor h1,.ql-editor h2,.ql-editor h3,.ql-editor h4,.ql-editor h5,.ql-editor h6,.ql-editor ol,.ql-editor p,.ql-editor pre,.ql-editor ul{counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;margin:0;padding:0}.ql-editor ol,.ql-editor ul{padding-left:1.5em}.ql-editor ol>li,.ql-editor ul>li{list-style-type:none}.ql-editor ul>li:before{content:"\2022"}.ql-editor ul[data-checked=false],.ql-editor ul[data-checked=true]{pointer-events:none}.ql-editor ul[data-checked=false]>li *,.ql-editor ul[data-checked=true]>li *{pointer-events:all}.ql-editor ul[data-checked=false]>li:before,.ql-editor ul[data-checked=true]>li:before{color:#777;cursor:pointer;pointer-events:all}.ql-editor ul[data-checked=true]>li:before{content:"\2611"}.ql-editor ul[data-checked=false]>li:before{content:"\2610"}.ql-editor li:before{display:inline-block;white-space:nowrap;width:1.2em}.ql-editor li:not(.ql-direction-rtl):before{margin-left:-1.5em;margin-right:.3em;text-align:right}.ql-editor li.ql-direction-rtl:before{margin-left:.3em;margin-right:-1.5em}.ql-editor ol li:not(.ql-direction-rtl),.ql-editor ul li:not(.ql-direction-rtl){padding-left:1.5em}.ql-editor ol li.ql-direction-rtl,.ql-editor ul li.ql-direction-rtl{padding-right:1.5em}.ql-editor ol li{counter-increment:list-0;counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li:before{content:counter(list-0,decimal) ". "}.ql-editor ol li.ql-indent-1{counter-increment:list-1}.ql-editor ol li.ql-indent-1:before{content:counter(list-1,lower-alpha) ". "}.ql-editor ol li.ql-indent-1{counter-reset:list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-2{counter-increment:list-2}.ql-editor ol li.ql-indent-2:before{content:counter(list-2,lower-roman) ". "}.ql-editor ol li.ql-indent-2{counter-reset:list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-3{counter-increment:list-3}.ql-editor ol li.ql-indent-3:before{content:counter(list-3,decimal) ". "}.ql-editor ol li.ql-indent-3{counter-reset:list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-4{counter-increment:list-4}.ql-editor ol li.ql-indent-4:before{content:counter(list-4,lower-alpha) ". "}.ql-editor ol li.ql-indent-4{counter-reset:list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-5{counter-increment:list-5}.ql-editor ol li.ql-indent-5:before{content:counter(list-5,lower-roman) ". "}.ql-editor ol li.ql-indent-5{counter-reset:list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-6{counter-increment:list-6}.ql-editor ol li.ql-indent-6:before{content:counter(list-6,decimal) ". "}.ql-editor ol li.ql-indent-6{counter-reset:list-7 list-8 list-9}.ql-editor ol li.ql-indent-7{counter-increment:list-7}.ql-editor ol li.ql-indent-7:before{content:counter(list-7,lower-alpha) ". "}.ql-editor ol li.ql-indent-7{counter-reset:list-8 list-9}.ql-editor ol li.ql-indent-8{counter-increment:list-8}.ql-editor ol li.ql-indent-8:before{content:counter(list-8,lower-roman) ". "}.ql-editor ol li.ql-indent-8{counter-reset:list-9}.ql-editor ol li.ql-indent-9{counter-increment:list-9}.ql-editor ol li.ql-indent-9:before{content:counter(list-9,decimal) ". "}.ql-editor .ql-indent-1:not(.ql-direction-rtl){padding-left:3em}.ql-editor li.ql-indent-1:not(.ql-direction-rtl){padding-left:4.5em}.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:3em}.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:4.5em}.ql-editor .ql-indent-2:not(.ql-direction-rtl){padding-left:6em}.ql-editor li.ql-indent-2:not(.ql-direction-rtl){padding-left:7.5em}.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:6em}.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:7.5em}.ql-editor .ql-indent-3:not(.ql-direction-rtl){padding-left:9em}.ql-editor li.ql-indent-3:not(.ql-direction-rtl){padding-left:10.5em}.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:9em}.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:10.5em}.ql-editor .ql-indent-4:not(.ql-direction-rtl){padding-left:12em}.ql-editor li.ql-indent-4:not(.ql-direction-rtl){padding-left:13.5em}.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:12em}.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:13.5em}.ql-editor .ql-indent-5:not(.ql-direction-rtl){padding-left:15em}.ql-editor li.ql-indent-5:not(.ql-direction-rtl){padding-left:16.5em}.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:15em}.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:16.5em}.ql-editor .ql-indent-6:not(.ql-direction-rtl){padding-left:18em}.ql-editor li.ql-indent-6:not(.ql-direction-rtl){padding-left:19.5em}.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:18em}.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:19.5em}.ql-editor .ql-indent-7:not(.ql-direction-rtl){padding-left:21em}.ql-editor li.ql-indent-7:not(.ql-direction-rtl){padding-left:22.5em}.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:21em}.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:22.5em}.ql-editor .ql-indent-8:not(.ql-direction-rtl){padding-left:24em}.ql-editor li.ql-indent-8:not(.ql-direction-rtl){padding-left:25.5em}.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:24em}.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:25.5em}.ql-editor .ql-indent-9:not(.ql-direction-rtl){padding-left:27em}.ql-editor li.ql-indent-9:not(.ql-direction-rtl){padding-left:28.5em}.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:27em}.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:28.5em}.ql-editor .ql-video{display:block;max-width:100%}.ql-editor .ql-video.ql-align-center{margin:0 auto}.ql-editor .ql-video.ql-align-right{margin:0 0 0 auto}.ql-editor .ql-bg-black{background-color:#000}.ql-editor .ql-bg-red{background-color:#e60000}.ql-editor .ql-bg-orange{background-color:#f90}.ql-editor .ql-bg-yellow{background-color:#ff0}.ql-editor .ql-bg-green{background-color:#008a00}.ql-editor .ql-bg-blue{background-color:#06c}.ql-editor .ql-bg-purple{background-color:#93f}.ql-editor .ql-color-white{color:#fff}.ql-editor .ql-color-red{color:#e60000}.ql-editor .ql-color-orange{color:#f90}.ql-editor .ql-color-yellow{color:#ff0}.ql-editor .ql-color-green{color:#008a00}.ql-editor .ql-color-blue{color:#06c}.ql-editor .ql-color-purple{color:#93f}.ql-editor .ql-font-serif{font-family:Georgia,Times New Roman,serif}.ql-editor .ql-font-monospace{font-family:Monaco,Courier New,monospace}.ql-editor .ql-size-small{font-size:.75em}.ql-editor .ql-size-large{font-size:1.5em}.ql-editor .ql-size-huge{font-size:2.5em}.ql-editor .ql-direction-rtl{direction:rtl;text-align:inherit}.ql-editor .ql-align-center{text-align:center}.ql-editor .ql-align-justify{text-align:justify}.ql-editor .ql-align-right{text-align:right}.ql-editor.ql-blank:before{color:#0009;content:attr(data-placeholder);font-style:italic;left:15px;pointer-events:none;position:absolute;right:15px}.ql-snow .ql-toolbar:after,.ql-snow.ql-toolbar:after{clear:both;content:"";display:table}.ql-snow .ql-toolbar button,.ql-snow.ql-toolbar button{background:none;border:none;cursor:pointer;display:inline-block;float:left;height:24px;padding:3px 5px;width:28px}.ql-snow .ql-toolbar button svg,.ql-snow.ql-toolbar button svg{float:left;height:100%}.ql-snow .ql-toolbar button:active:hover,.ql-snow.ql-toolbar button:active:hover{outline:none}.ql-snow .ql-toolbar input.ql-image[type=file],.ql-snow.ql-toolbar input.ql-image[type=file]{display:none}.ql-snow .ql-toolbar .ql-picker-item.ql-selected,.ql-snow .ql-toolbar .ql-picker-item:hover,.ql-snow .ql-toolbar .ql-picker-label.ql-active,.ql-snow .ql-toolbar .ql-picker-label:hover,.ql-snow .ql-toolbar button.ql-active,.ql-snow .ql-toolbar button:focus,.ql-snow .ql-toolbar button:hover,.ql-snow.ql-toolbar .ql-picker-item.ql-selected,.ql-snow.ql-toolbar .ql-picker-item:hover,.ql-snow.ql-toolbar .ql-picker-label.ql-active,.ql-snow.ql-toolbar .ql-picker-label:hover,.ql-snow.ql-toolbar button.ql-active,.ql-snow.ql-toolbar button:focus,.ql-snow.ql-toolbar button:hover{color:#06c}.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar button.ql-active .ql-fill,.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:focus .ql-fill,.ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:hover .ql-fill,.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar button.ql-active .ql-fill,.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-snow.ql-toolbar button:focus .ql-fill,.ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,.ql-snow.ql-toolbar button:hover .ql-fill,.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill{fill:#06c}.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-snow .ql-toolbar button.ql-active .ql-stroke,.ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,.ql-snow .ql-toolbar button:focus .ql-stroke,.ql-snow .ql-toolbar button:focus .ql-stroke-miter,.ql-snow .ql-toolbar button:hover .ql-stroke,.ql-snow .ql-toolbar button:hover .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-snow.ql-toolbar button.ql-active .ql-stroke,.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,.ql-snow.ql-toolbar button:focus .ql-stroke,.ql-snow.ql-toolbar button:focus .ql-stroke-miter,.ql-snow.ql-toolbar button:hover .ql-stroke,.ql-snow.ql-toolbar button:hover .ql-stroke-miter{stroke:#06c}@media (pointer:coarse){.ql-snow .ql-toolbar button:hover:not(.ql-active),.ql-snow.ql-toolbar button:hover:not(.ql-active){color:#444}.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill{fill:#444}.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter{stroke:#444}}.ql-snow,.ql-snow *{box-sizing:border-box}.ql-snow .ql-hidden{display:none}.ql-snow .ql-out-bottom,.ql-snow .ql-out-top{visibility:hidden}.ql-snow .ql-tooltip{position:absolute;transform:translateY(10px)}.ql-snow .ql-tooltip a{cursor:pointer;text-decoration:none}.ql-snow .ql-tooltip.ql-flip{transform:translateY(-10px)}.ql-snow .ql-formats{display:inline-block;vertical-align:middle}.ql-snow .ql-formats:after{clear:both;content:"";display:table}.ql-snow .ql-stroke{fill:none;stroke:#444;stroke-linecap:round;stroke-linejoin:round;stroke-width:2}.ql-snow .ql-stroke-miter{fill:none;stroke:#444;stroke-miterlimit:10;stroke-width:2}.ql-snow .ql-fill,.ql-snow .ql-stroke.ql-fill{fill:#444}.ql-snow .ql-empty{fill:none}.ql-snow .ql-even{fill-rule:evenodd}.ql-snow .ql-stroke.ql-thin,.ql-snow .ql-thin{stroke-width:1}.ql-snow .ql-transparent{opacity:.4}.ql-snow .ql-direction svg:last-child{display:none}.ql-snow .ql-direction.ql-active svg:last-child{display:inline}.ql-snow .ql-direction.ql-active svg:first-child{display:none}.ql-snow .ql-editor h1{font-size:2em}.ql-snow .ql-editor h2{font-size:1.5em}.ql-snow .ql-editor h3{font-size:1.17em}.ql-snow .ql-editor h4{font-size:1em}.ql-snow .ql-editor h5{font-size:.83em}.ql-snow .ql-editor h6{font-size:.67em}.ql-snow .ql-editor a{text-decoration:underline}.ql-snow .ql-editor blockquote{border-left:4px solid #ccc;margin-bottom:5px;margin-top:5px;padding-left:16px}.ql-snow .ql-editor code,.ql-snow .ql-editor pre{background-color:#f0f0f0;border-radius:3px}.ql-snow .ql-editor pre{margin-bottom:5px;margin-top:5px;padding:5px 10px;white-space:pre-wrap}.ql-snow .ql-editor code{font-size:85%;padding:2px 4px}.ql-snow .ql-editor pre.ql-syntax{background-color:#23241f;color:#f8f8f2;overflow:visible}.ql-snow .ql-editor img{max-width:100%}.ql-snow .ql-picker{color:#444;display:inline-block;float:left;font-size:14px;font-weight:500;height:24px;position:relative;vertical-align:middle}.ql-snow .ql-picker-label{cursor:pointer;display:inline-block;height:100%;padding-left:8px;padding-right:2px;position:relative;width:100%}.ql-snow .ql-picker-label:before{display:inline-block;line-height:22px}.ql-snow .ql-picker-options{background-color:#fff;display:none;min-width:100%;padding:4px 8px;position:absolute;white-space:nowrap}.ql-snow .ql-picker-options .ql-picker-item{cursor:pointer;display:block;padding-bottom:5px;padding-top:5px}.ql-snow .ql-picker.ql-expanded .ql-picker-label{color:#ccc;z-index:2}.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill{fill:#ccc}.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke{stroke:#ccc}.ql-snow .ql-picker.ql-expanded .ql-picker-options{display:block;margin-top:-1px;top:100%;z-index:1}.ql-snow .ql-color-picker,.ql-snow .ql-icon-picker{width:28px}.ql-snow .ql-color-picker .ql-picker-label,.ql-snow .ql-icon-picker .ql-picker-label{padding:2px 4px}.ql-snow .ql-color-picker .ql-picker-label svg,.ql-snow .ql-icon-picker .ql-picker-label svg{right:4px}.ql-snow .ql-icon-picker .ql-picker-options{padding:4px 0}.ql-snow .ql-icon-picker .ql-picker-item{height:24px;padding:2px 4px;width:24px}.ql-snow .ql-color-picker .ql-picker-options{padding:3px 5px;width:152px}.ql-snow .ql-color-picker .ql-picker-item{border:1px solid #0000;float:left;height:16px;margin:2px;padding:0;width:16px}.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg{margin-top:-9px;position:absolute;right:0;top:50%;width:18px}.ql-snow .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=""]):before{content:attr(data-label)}.ql-snow .ql-picker.ql-header{width:98px}.ql-snow .ql-picker.ql-header .ql-picker-item:before,.ql-snow .ql-picker.ql-header .ql-picker-label:before{content:"Normal"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]:before{content:"Heading 1"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]:before{content:"Heading 2"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]:before{content:"Heading 3"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]:before{content:"Heading 4"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]:before{content:"Heading 5"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]:before,.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]:before{content:"Heading 6"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]:before{font-size:2em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]:before{font-size:1.5em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]:before{font-size:1.17em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]:before{font-size:1em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]:before{font-size:.83em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]:before{font-size:.67em}.ql-snow .ql-picker.ql-font{width:108px}.ql-snow .ql-picker.ql-font .ql-picker-item:before,.ql-snow .ql-picker.ql-font .ql-picker-label:before{content:"Sans Serif"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=serif]:before{content:"Serif"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=monospace]:before{content:"Monospace"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before{font-family:Georgia,Times New Roman,serif}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before{font-family:Monaco,Courier New,monospace}.ql-snow .ql-picker.ql-size{width:98px}.ql-snow .ql-picker.ql-size .ql-picker-item:before,.ql-snow .ql-picker.ql-size .ql-picker-label:before{content:"Normal"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=small]:before{content:"Small"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=large]:before{content:"Large"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=huge]:before{content:"Huge"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before{font-size:10px}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before{font-size:18px}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before{font-size:32px}.ql-snow .ql-color-picker.ql-background .ql-picker-item{background-color:#fff}.ql-snow .ql-color-picker.ql-color .ql-picker-item{background-color:#000}.ql-toolbar.ql-snow{border:1px solid #ccc;box-sizing:border-box;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;padding:8px}.ql-toolbar.ql-snow .ql-formats{margin-right:15px}.ql-toolbar.ql-snow .ql-picker-label{border:1px solid #0000}.ql-toolbar.ql-snow .ql-picker-options{border:1px solid #0000;box-shadow:0 2px 8px #0003}.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label,.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options{border-color:#ccc}.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item.ql-selected,.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item:hover{border-color:#000}.ql-toolbar.ql-snow+.ql-container.ql-snow{border-top:0}.ql-snow .ql-tooltip{background-color:#fff;border:1px solid #ccc;box-shadow:0 0 5px #ddd;color:#444;padding:5px 12px;white-space:nowrap}.ql-snow .ql-tooltip:before{content:"Visit URL:";line-height:26px;margin-right:8px}.ql-snow .ql-tooltip input[type=text]{border:1px solid #ccc;display:none;font-size:13px;height:26px;margin:0;padding:3px 5px;width:170px}.ql-snow .ql-tooltip a.ql-preview{display:inline-block;max-width:200px;overflow-x:hidden;text-overflow:ellipsis;vertical-align:top}.ql-snow .ql-tooltip a.ql-action:after{border-right:1px solid #ccc;content:"Edit";margin-left:16px;padding-right:8px}.ql-snow .ql-tooltip a.ql-remove:before{content:"Remove";margin-left:8px}.ql-snow .ql-tooltip a{line-height:26px}.ql-snow .ql-tooltip.ql-editing a.ql-preview,.ql-snow .ql-tooltip.ql-editing a.ql-remove{display:none}.ql-snow .ql-tooltip.ql-editing input[type=text]{display:inline-block}.ql-snow .ql-tooltip.ql-editing a.ql-action:after{border-right:0;content:"Save";padding-right:0}.ql-snow .ql-tooltip[data-mode=link]:before{content:"Enter link:"}.ql-snow .ql-tooltip[data-mode=formula]:before{content:"Enter formula:"}.ql-snow .ql-tooltip[data-mode=video]:before{content:"Enter video:"}.ql-snow a{color:#06c}.ql-container.ql-snow{border:1px solid #ccc}:root{--wp-admin-bar-height:32px;--wp-admin-sidebar-width:160px}body.wp-admin #wpcontent{padding-left:0!important}#wpbody-content{padding-bottom:0}.wrap{margin:0;padding:0}#wp-ai-workflows-root{height:calc(100vh - 32px);height:calc(100vh - var(--wp-admin-bar-height));margin:0;overflow-y:auto}.ant-layout{min-height:calc(100vh - 32px)!important;min-height:calc(100vh - var(--wp-admin-bar-height))!important}.ant-layout-header{align-items:center;display:flex;margin-top:0;padding:0 16px!important}.ant-layout-header .ant-image{height:32px;margin-right:24px}.ant-layout-header .ant-menu{flex:1 1}.ant-layout-content{padding-top:16px}.react-flow__handle{background-color:#1890ff;border:2px solid #fff;border-radius:50%;height:12px;width:12px}.react-flow__handle-left{left:-4px}.react-flow__handle-right{right:-4px}.react-flow__node{z-index:1}.react-flow__edge{z-index:2}.react-flow__handle{z-index:3}.ant-card{z-index:4}.ant-modal{z-index:1000}.custom-node .ant-card{background-color:#fff;border-radius:5px;box-shadow:0 1px 3px #0000001f,0 1px 2px #0000003d}.custom-node .ant-card-head{border-bottom:none}.custom-node .ant-card-body{padding:12px}@media screen and (max-width:782px){:root{--wp-admin-bar-height:46px}.ant-layout-header{margin-top:-46px}}@media screen and (max-width:600px){:root{--wp-admin-bar-height:0}.ant-layout-header{margin-top:0}}.field-collapse .ant-collapse-content{max-height:500px;overflow-y:auto}.ant-input.ant-input-textarea-show-count{min-height:32px;resize:vertical}.quill{background:#fff;margin-bottom:8px}.quill .ql-container{min-height:100px}.workflow-generation-overlay{align-items:center;-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px);background-color:#000000bf;bottom:0;display:flex;justify-content:center;left:0;position:fixed;right:0;top:0;z-index:2000}.workflow-generation-content{background-color:#fffffff2;border-radius:16px;box-shadow:0 8px 32px #0000001a;max-width:500px;padding:40px;text-align:center;width:90%}.ant-modal-mask{background-color:#000000a6!important}.ant-modal-wrap{z-index:1050}.workflow-generation-loader{z-index:2000}.workflow-prompt-input{border:1px solid #d9d9d9;border-radius:8px;transition:all .3s}.workflow-prompt-input:hover{border-color:#40a9ff}.workflow-prompt-input:focus{border-color:#40a9ff;box-shadow:0 0 0 2px #1890ff33;outline:none}.ant-modal-content{border-radius:12px;overflow:hidden}.ant-modal-header{border-radius:12px 12px 0 0}.generate-ai-button{background:linear-gradient(45deg,#ff6b6b,#4ecdc4);border:none;color:#fff;transition:all .3s}.generate-ai-button:hover{box-shadow:0 4px 12px #00000026;opacity:.9;transform:translateY(-1px)}.chat-node.ai-workflows-node{.ant-card{transition:box-shadow .3s ease}.ant-card:hover{box-shadow:0 2px 8px #00000026}.chat-preview-container{align-items:center;background:#f5f5f5;border:1px solid #d9d9d9;border-radius:8px;display:flex;justify-content:center;margin:8px 0;min-height:200px;padding:12px}.ant-color-picker{width:100%}.model-select-option{align-items:center;display:flex;gap:8px;.model-icon{font-size:16px}.model-info{flex:1 1}}.ant-tabs-nav{margin-bottom:12px}.ant-collapse-ghost{background:#0000}.quill{background:#fff;border:1px solid #d9d9d9;border-radius:4px;margin-bottom:8px;.ql-toolbar{border-bottom:1px solid #d9d9d9;border-radius:4px 4px 0 0}.ql-container{border-radius:0 0 4px 4px}}.embedding-code{background:#f5f5f5;border-radius:4px;font-family:monospace;margin:8px 0;padding:8px}.node-input-handler{margin:8px 0}.parameter-setting{margin-bottom:12px;.parameter-label{align-items:center;display:flex;justify-content:space-between;margin-bottom:4px}.parameter-input{width:100%}.parameter-info{color:#8c8c8c;font-size:12px;margin-top:2px}}.memory-settings{background:#fafafa;border-radius:4px;margin-bottom:12px;padding:8px}.design-preview{border:1px solid #d9d9d9;border-radius:8px;margin:12px 0;overflow:hidden;&.dark{background:#141414;color:#fff}.preview-header{background:#1677ff;background:var(--primary-color,#1677ff);color:#fff;padding:8px}.preview-content{min-height:100px;padding:8px}.preview-input{border-top:1px solid #d9d9d9;padding:8px}}}.chat-node-tooltip{font-size:12px;max-width:300px}.chat-node-modal{.ant-modal-body{max-height:70vh;overflow-y:auto}.ant-form-item-label{font-weight:500}}.edge-delete-button{opacity:0;pointer-events:all;transition:opacity .2s}.edge-delete-button.selected,.edge-delete-button:hover,.react-flow__edge:hover .edge-delete-button{opacity:1}.react-flow__edge{cursor:pointer}.react-flow__edge.selected{z-index:1000}.edge-delete-button{z-index:1001}.react-flow__edge-path{transition:stroke .2s,stroke-width .2s}.wp-ai-workflows-chat-container{--chat-primary:#1677ff;--chat-bg:#fff;--chat-text:#000;--chat-secondary:#f5f5f5;--chat-radius:12px;--chat-shadow:0 4px 12px #0000001a}.wp-ai-workflows-chat-container.dark{--chat-primary:#177ddc;--chat-bg:#1f1f1f;--chat-text:#fff;--chat-secondary:#2f2f2f}.wp-ai-workflows-chat-widget{--chat-border-radius:12px;background:var(--chat-bg);border-radius:var(--chat-radius);border-radius:var(--chat-border-radius);box-shadow:var(--chat-shadow);display:flex;flex-direction:column;max-width:400px;position:fixed;transition:all .3s ease;width:100%;z-index:9999}.wp-ai-workflows-chat-widget.bottom-right{bottom:20px;right:20px}.wp-ai-workflows-chat-widget.bottom-left{bottom:20px;left:20px}.wp-ai-workflows-chat-widget.top-right{right:20px;top:20px}.wp-ai-workflows-chat-widget.top-left{left:20px;top:20px}.chat-header{align-items:center;background:var(--chat-primary);border-top-left-radius:var(--chat-border-radius);border-top-right-radius:var(--chat-border-radius);color:#fff;display:flex;justify-content:space-between;padding:12px 16px}.wp-ai-workflows-chat-widget:not(.inline) .chat-header{cursor:pointer}.header-content{align-items:center;display:flex;gap:8px}.bot-name{font-weight:500}.header-actions{align-items:center;display:flex;gap:8px}.chat-messages{display:flex;flex:1 1;flex-direction:column;gap:12px;overflow-y:auto;padding:16px}.chat-message{display:flex;margin-bottom:8px}.chat-message .message-content{border-radius:calc(var(--chat-border-radius)/2)}.chat-message.user{justify-content:flex-end}.message-content{word-wrap:break-word;border-radius:12px;max-width:80%;padding:8px 12px}.chat-message.user .message-content{background:var(--chat-primary);color:#fff}.chat-message.assistant .message-content{background:var(--chat-secondary);color:var(--chat-text)}.chat-input{border-top:1px solid var(--chat-secondary);display:flex;gap:8px;padding:16px}.chat-input input{background:var(--chat-bg);border:1px solid var(--chat-secondary);border-radius:6px;color:var(--chat-text);flex:1 1;outline:none;padding:8px 12px}.chat-input button{background:var(--chat-primary);border:none;border-radius:6px;color:#fff;cursor:pointer;padding:8px 16px;transition:opacity .2s}.chat-input button:hover{opacity:.9}.chat-launcher{align-items:center;background:var(--chat-primary);border-radius:28px;box-shadow:var(--chat-shadow);color:#fff;cursor:pointer;display:flex;height:56px;justify-content:center;position:fixed;transition:transform .3s ease;width:56px;z-index:9998}.chat-launcher:hover{transform:scale(1.05)}.chat-launcher.bottom-right{bottom:20px;right:20px}.chat-launcher.bottom-left{bottom:20px;left:20px}.chat-launcher.top-right{right:20px;top:20px}.chat-launcher.top-left{left:20px;top:20px}@media (max-width:480px){.wp-ai-workflows-chat-widget:not(.inline){height:auto;margin:16px;max-height:calc(100vh - 32px);max-width:none;min-height:400px;width:calc(100% - 32px)}.wp-ai-workflows-chat-widget.bottom-right{bottom:0;right:0}.wp-ai-workflows-chat-widget.bottom-left{bottom:0;left:0}.wp-ai-workflows-chat-widget.top-right{right:0;top:0}.wp-ai-workflows-chat-widget.top-left{left:0;top:0}.chat-header{border-top-left-radius:var(--chat-border-radius);border-top-right-radius:var(--chat-border-radius)}}@media (max-width:360px){.wp-ai-workflows-chat-widget:not(.inline){border-radius:0;height:100%;margin:0;max-height:100vh;width:100%}.wp-ai-workflows-chat-widget:not(.inline) .chat-header{border-radius:0}}.chat-widget-enter{opacity:0;transform:translateY(20px)}.chat-widget-enter-active{transition:opacity .3s ease,transform .3s ease}.chat-widget-enter-active,.chat-widget-exit{opacity:1;transform:translateY(0)}.chat-widget-exit-active{opacity:0;transform:translateY(20px);transition:opacity .3s ease,transform .3s ease}.typing-indicator{background:var(--chat-secondary);border-radius:12px;display:flex;gap:4px;padding:8px 12px;width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.typing-dot{animation:typingAnimation 1.4s infinite;background:var(--chat-text);border-radius:50%;height:6px;opacity:.6;width:6px}.typing-dot:nth-child(2){animation-delay:.2s}.typing-dot:nth-child(3){animation-delay:.4s}@keyframes typingAnimation{0%,to{transform:translateY(0)}50%{transform:translateY(-4px)}}.close-button,.sound-toggle{align-items:center;background:#0000;border:none;color:#fffc;cursor:pointer;display:flex;justify-content:center;padding:4px;transition:color .2s ease}.close-button:hover,.sound-toggle:hover{color:#fff}.error-message{background:#fff2f0;border:1px solid #ffccc7;border-radius:6px;color:#ff4d4f;margin:8px 0;padding:8px 12px}.chat-messages::-webkit-scrollbar{width:6px}.chat-messages::-webkit-scrollbar-track{background:#0000}.chat-messages::-webkit-scrollbar-thumb{background:#0003;border-radius:3px}.dark .chat-messages::-webkit-scrollbar-thumb{background:#fff3}.message-content pre{background:#0000000d;border-radius:4px;overflow-x:auto;padding:8px}.dark .message-content pre{background:#ffffff1a}.message-content p{margin:0 0 8px}.message-content p:last-child{margin-bottom:0}.message-content ol,.message-content ul{margin:8px 0;padding-left:20px}.message-content code{background:#0000000d;border-radius:4px;font-family:monospace;padding:2px 4px}.dark .message-content code{background:#ffffff1a}.wp-ai-workflows-chat-widget.dark{--chat-bg:#1f1f1f;--chat-text:#fff;--chat-secondary:#2f2f2f;box-shadow:0 4px 12px #0003}.wp-ai-workflows-chat-widget{transition:background-color .3s ease,width .3s ease,height .3s ease}.wp-ai-workflows-chat-widget *{transition:background-color .3s ease,color .3s ease}.file-upload-area{border:2px dashed var(--chat-secondary);border-radius:8px;cursor:pointer;margin:8px 0;padding:16px;text-align:center;transition:border-color .2s}.file-upload-area.dragging,.file-upload-area:hover{border-color:var(--chat-primary)}.file-upload-area.dragging{background:rgba(var(--chat-primary-rgb),.1)}.sr-only{clip:rect(0,0,0,0);border:0;height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}.chat-input button:focus,.chat-input input:focus,.sound-toggle:focus{outline:2px solid var(--chat-primary);outline-offset:2px}.wp-ai-workflows-chat-widget.minimized{height:auto;min-height:0;min-height:auto}.wp-ai-workflows-chat-widget.minimized .chat-input,.wp-ai-workflows-chat-widget.minimized .chat-messages{display:none}.wp-ai-workflows-chat-widget .powered-by{border-top:1px solid #f0f0f0;border-top:1px solid var(--chat-secondary,#f0f0f0);color:inherit;color:var(--chat-text,inherit);font-size:9px;opacity:.7;padding:6px;text-align:center;transition:opacity .2s ease}.wp-ai-workflows-chat-widget .powered-by a{align-items:center;color:inherit;display:inline-flex;gap:4px;text-decoration:none}.wp-ai-workflows-chat-widget .powered-by a:hover{opacity:1;text-decoration:underline}.wp-ai-workflows-chat-widget.dark .powered-by{opacity:.5}.wp-ai-workflows-chat-widget.dark .powered-by a{color:#fffc}.wp-ai-workflows-chat-widget.custom .powered-by,.wp-ai-workflows-chat-widget.dark .powered-by{background:none;border-radius:0}.wp-ai-workflows-chat-widget.inline{background-color:#fff;background-color:var(--chat-bg,#fff);border:1px solid #f0f0f0;border:1px solid var(--chat-secondary,#f0f0f0);border-radius:12px;border-radius:var(--chat-border-radius,12px);box-shadow:none!important;font-family:var(--chat-font-family);font-size:var(--chat-font-size);height:auto!important;margin:20px 0;max-width:100%;min-height:500px;position:relative;width:100%}.wp-ai-workflows-chat-widget.inline .chat-header{background-color:#1677ff;background-color:var(--chat-primary,#1677ff);border-top-left-radius:12px;border-top-left-radius:var(--chat-border-radius,12px);border-top-right-radius:12px;border-top-right-radius:var(--chat-border-radius,12px);padding:12px 16px;position:relative;z-index:2}.wp-ai-workflows-chat-widget.inline .chat-messages{background-color:#fff;background-color:var(--chat-bg,#fff);height:400px;max-height:calc(100vh - 300px);overflow-y:auto;padding:16px}.wp-ai-workflows-chat-widget.inline .chat-input{background-color:#fff;background-color:var(--chat-bg,#fff);border-bottom-left-radius:12px;border-bottom-left-radius:var(--chat-border-radius,12px);border-bottom-right-radius:12px;border-bottom-right-radius:var(--chat-border-radius,12px);border-top:1px solid #f0f0f0;border-top:1px solid var(--chat-secondary,#f0f0f0);bottom:0;left:0;padding:16px;position:relative;right:0}.wp-ai-workflows-chat-widget.inline.dark{background-color:#1f1f1f;border-color:#333}.wp-ai-workflows-chat-widget.inline.dark .chat-input,.wp-ai-workflows-chat-widget.inline.dark .chat-messages{background-color:#1f1f1f}.wp-ai-workflows-chat-widget.inline.custom{background-color:var(--chat-bg);border-color:var(--chat-secondary)}.wp-ai-workflows-chat-widget.inline.custom .chat-input,.wp-ai-workflows-chat-widget.inline.custom .chat-messages{background-color:var(--chat-bg)}.wp-ai-workflows-chat-widget.inline .powered-by{background:inherit;border-bottom-left-radius:var(--chat-border-radius);border-bottom-right-radius:var(--chat-border-radius);border-top:1px solid #0000001a;font-size:9px;margin:0;opacity:.7;padding:6px}.wp-ai-workflows-chat-widget.inline.dark .powered-by{border-top-color:#ffffff1a}.wp-ai-workflows-chat-widget.inline.custom .powered-by{border-top-color:var(--chat-secondary)}.wp-ai-workflows-chat-widget.inline .powered-by a{color:inherit;opacity:.7}.wp-ai-workflows-chat-widget.inline .powered-by,.wp-ai-workflows-chat-widget.inline.custom .powered-by,.wp-ai-workflows-chat-widget.inline.dark .powered-by{background:none!important;color:inherit!important}@media screen and (max-width:768px){.wp-ai-workflows-chat-widget.inline{margin:10px 0;min-height:400px}.wp-ai-workflows-chat-widget.inline .chat-messages{height:300px;max-height:calc(100vh - 200px)}.wp-ai-workflows-chat-widget.inline .chat-header{padding:8px 12px}.wp-ai-workflows-chat-widget.inline .chat-input{padding:12px}}@media screen and (max-width:480px){.wp-ai-workflows-chat-widget.inline{margin:5px 0;min-height:350px}.wp-ai-workflows-chat-widget.inline .chat-messages{height:250px;padding:12px}.wp-ai-workflows-chat-widget.inline .bot-name{font-size:14px}.wp-ai-workflows-chat-widget.inline .message-content{max-width:90%;padding:6px 10px}}.wp-ai-workflows-chat-widget.inline *{font-size:inherit}.wp-ai-workflows-chat-widget.inline .chat-header{font-size:var(--chat-header-font-size)}@media (min-width:768px){.wp-ai-workflows-chat-widget:not(.inline){max-width:90vw}}
/*# sourceMappingURL=main.9deba62a.css.map*/