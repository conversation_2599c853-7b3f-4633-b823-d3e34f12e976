=== AI Workflow Automation ===
Contributors: @massiveshift
Tags: Workflow, AI, Chatbot, Automation, Google Sheets
Requires at least: 6.0
Tested up to: 6.7.1
Requires PHP: 8.0
Stable tag: 1.3.4
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Transform your WordPress site with AI-powered automation for content, customer support, data analysis, research, and business processes. Build AI workflows in minutes!

== Description ==
= The Ultimate WordPress AI Automation Platform - Your All-in-One AI Integration Solution =

★★★★★ TRUSTED BY LEADING WORDPRESS EXPERTS ★★★★★

> "AI Workflow Automation represents a significant step forward in bringing sophisticated AI capabilities to WordPress. It combines native integration, security-conscious design, and powerful automation features to make it a compelling choice for leveraging AI within your workflows. Everyone at WP Mayor loves this plugin."
> — **WP Mayor**, Rating: 4.5/5
[Read the full review](https://wpmayor.com/ai-workflow-automation-review/)

> "AI Workflow Automation promises to transform your WordPress website into an AI-powered automation hub. It lets you create advanced workflows for a wide range of tasks, including creating content, optimizing your blog posts, processing customer support tickets, and more."
> — **WPBeginner**, Verified Badge
[Read the full review](https://www.wpbeginner.com/solutions/ai-workflow-automation/)


[Visit AI Workflow Automation](https://wpaiworkflowautomation.com/?utm_source=wordpressorg&utm_medium=description)

= Revolutionize Your WordPress Site with Advanced Agentic AI Automation =
Turn your WordPress site into an AI-powered automation hub. Whether you're automating customer support, generating content, analyzing data, or streamlining business processes, our visual workflow builder lets you build AI agents and harness the power of AI models like ChatGPT, Claude, Perplexity and Gemini without writing code.

= Why Choose AI Workflow Automation? =

Complete AI Integration: Connect with OpenAI, Anthropic Claude, Google Gemini, Perplexity and more through a single interface
Visual Workflow Builder: Create complex AI automations with our intuitive drag-and-drop builder
Business Process Automation: Streamline operations, customer support, and data analysis
Smart Content Tools: Automate content creation, research, and SEO optimization
Data Integration: Connect with Google Sheets, Google Drive, and external systems via webhooks or custom API calls
Flexible Architecture: Build custom solutions for any business need
Chatbots: Implement complex AI chatbots trained on your own data on your site in seconds as a part of your workflows

Watch a 2-minute demo video tour of the plugin:
[youtube https://www.youtube.com/watch?v=w2dwjJneisI]

== NEW! Latest Features ==

= Integration for WPForms added to Lite version =
* Trigger complex workflows from a WP Form on your front end. Receive data from your users and manipulate the data with AI, and show it back to them via a Shortcode!
* Support for Gravity Forms, Ninja Forms and Contact Form 7 Added to Pro version
* [Check the documentation on WP Forms integration](https://wpaiworkflowautomation.com/docs/wpforms-integration/)

= 🤖 OpenAI o1 and o3 family of reasoning models now are available! =
* Create intelligent AI agents with OpenAI's reasoning models
* Add logic to your Chat bots by using o1 or o3 models as your chat model!

= 🎨 Workflow Annotations & Documentation =
* Add contextual documentation directly in your workflows
* Visual organization with sticky notes, text, and shapes
* Color-coded annotations for better workflow organization
* Resizable and customizable annotation components
* Improved workflow readability and maintainability

= 🤖 AI Chatbot Builder - Transform Your Website with Intelligent Chat (Lite and Pro version accessible!) =

Introducing our powerful AI Chatbot Builder - create custom AI chatbots trained on your business data in minutes! Deploy intelligent conversational AI across your WordPress site to engage visitors, answer questions, and automate customer support.

= Why Choose Our AI Chatbot? =

✨ No-Code Chatbot Builder
* Create custom AI chatbots without any coding
* Train your chatbot on your business data, documentation, and FAQs
* Deploy anywhere on your site with a simple shortcode
* Full chat history tracking and analytics

🎨 Complete Design Customization
* Customize colors, fonts, and themes to match your brand
* Choose from multiple chat widget positions (bottom-right, bottom-left, inline, etc.)
* Customize bot avatar and personality
* Responsive design works perfectly on all devices

🧠 Intelligent Conversation Management
* Maintains context throughout conversations
* Persistent chat history for continuous conversations
* Rate limiting and spam protection
* Smart conversation routing

🔧 Advanced Configuration
* Deploy as a floating widget or embed inline in any page
* Customize initial greeting messages
* Set auto-open delays and triggers
* Control maximum message length and frequency

💡 Key Applications:
* 24/7 Customer Support
* Product Recommendations
* FAQ Automation
* Lead Generation
* Sales Assistance
* Technical Support
* Educational Tools
* Onboarding Assistance

Simply add [wp_ai_workflow_chat id="your-workflow-id"] to any page or post to deploy your chatbot. Train it on your specific business knowledge and let it handle customer inquiries automatically while maintaining your brand voice and expertise.

Upgrade to Pro to access advanced AI models like Claude, Gemini, and Mistral for even more powerful conversational capabilities!

= 🎨 AI Workflow Generator =

NEW! Instantly create workflows based on your requirements
AI-powered workflow suggestions and optimizations
Smart templates for common business processes
Automated best practice implementation

= 🤖 Universal AI Integration =

Connect with multiple AI models simultaneously
Automated model selection based on tasks
Custom prompt engineering tools
Advanced context management

= 🔄 Business Process Automation =

Customer support automation
Lead qualification workflows
Data analysis and reporting
Document processing and extraction
E-commerce automation

= 📊 Data & Analytics =

Automated data collection and analysis
Google Sheets integration
Custom database storage
Real-time reporting workflows

= 🎯 Smart Content & Research =

Content generation and optimization
Market research automation
Competitor analysis
Multi-language support

= 🌐 Integration Hub =

[Gravity Forms Integration](https://wpaiworkflowautomation.com/docs/gravity-forms-trigger/)
[WP Forms Integration] (https://wpaiworkflowautomation.com/docs/wpforms-integration/)
[Contact Form 7 Integration](https://wpaiworkflowautomation.com/docs/contact-form-7-integration/)
[Ninja Forms Integration] (https://wpaiworkflowautomation.com/docs/ninja-forms-integration/)
WooCommerce integration
Webhook connections
Google Workspace sync
Custom API connections

== Real-World Applications ==
✓ Customer Service Teams: Build AI-powered support workflows
✓ Marketing Teams: Automate research, content, and analytics
✓ E-commerce Businesses: Streamline product management and customer communication
✓ Content Teams: Create intelligent content pipelines
✓ Data Analysts: Automate data collection and analysis
✓ Business Owners: Implement AI-driven business processes
✓ Developers: Create custom AI solutions for clients
✓ Researchers: Automate data gathering and analysis using Perplexity API

== Key Components ==

= Diverse Triggers =

Manual activation
Form submissions (Gravity Forms)
Webhook endpoints
WordPress events (Posts, Users, Comments)
Scheduled executions
External system events

= AI Processing Nodes =

General-purpose AI processing
Sentiment analysis
Data extraction
Research automation
Decision making
Language processing

= Action Nodes =

WordPress content management
Email communication
Database operations
External API calls
File management
Google Workspace operations

= Control Flow =

Conditional logic
Human oversight
Approval workflows
Error handling
Retry mechanisms

= Output Options =

Save to Google Sheets
Save to Google Drive
Display results via shortcodes
Store in databases for future use
Send to external services via webhooks

= Visual Documentation & Annotations =
- Add sticky notes to document workflow sections
- Text annotations for instructions and clarifications
- Shape overlays to group and highlight workflow components
- Color-coded visual organization
- Resizable and customizable annotations


== Powerful Integrations ==

Gravity Forms
Advanced Custom Fields (ACF)
WooCommerce
OpenAI API
Perplexity AI
NEW! OpenRouter (for access to Claude, Gemini, and more)
NEW! Firecrawl for web scraping and crawling
NEW! Unsplash for finding and adding images to your content 

== Who Benefits? ==

Content Creators: Automate and enhance your content pipeline, do research at scale, and publish expertly written content anytime!
E-commerce Owners: Generate product descriptions and marketing copy at scale. Automate you customer support by training a workflow on your business knowledge and FAQ
Digital Marketers: Create data-driven, AI-powered marketing campaigns
Customer Support Teams: Implement intelligent response systems
Developers: Extend WordPress capabilities with AI-driven workflows
Researchers: Streamline data collection and analysis processes

== Pro Version Exclusive Features ==

Unlimited AI-powered workflows
Advanced AI model access and features
Advanced Nodes such as Human Input and Conditions
Comprehensive integration library
Priority support
Access to premium workflow templates

== Installation ==

Upload the plugin files to the /wp-content/plugins/wp-ai-workflow-automation directory, or install the plugin through the WordPress plugins screen directly.
Activate the plugin through the 'Plugins' screen in WordPress.
Use the Settings -> WP AI Workflow Automation screen to configure the plugin and add your AI API keys.

== Frequently Asked Questions ==
= Do I need coding skills to use AI Workflow Automation? =
No coding skills required! Our visual workflow builder is designed for users of all technical levels.
= How does this compare to other WordPress automation plugins? =
WP AI Workflow Automation is the first to offer deep integration with generative AI models, providing intelligent and adaptive automations beyond traditional rule-based systems.
= Do I need to create an account on your website to use the plugin? =
No external account is needed. Simply install the plugin and add your AI service API keys to get started.
= Which AI models are supported? =
We currently support OpenAI's models, Perplexity AI, and through OpenRouter, you can access Meta Llama, Mistral models, xAI Grok, DeepSeek, Anthropic's Claude and Google's Gemini models. We're continuously expanding our AI model support.
= Can I use WP AI Workflow Automation on multiple sites? =
Yes! Our Pro plan supports usage across multiple WordPress installations.
== Screenshots ==
1. Visual Workflow Builder
2. Workflow Management Page
3. Workflow Queue

== Changelog ==

V 1.3.4
- Added WP Forms Trigger to Lite version
- Removed a bug related to shortcodes

V 1.3.3
- Added o1 and o3 families of models to AI model and Chat node
- Added dynamic variables to input tags. Now you can call specific date and time, specific post names, WooCommerce products or even generate random numbers in your AI prompts
- Bug fixes and UI improvements

V 1.3.2
- Fixed a bug related to chat widget and processing of answers on some installations

V 1.3.1
- Organize and group your workflows by adding tags to them! Now you can assign tags to your workflows in your workflow management page. And search or filter your workflows based on tags. 
- Bulk actions including bulk delete/activation/deactivation of workflows on the workflow management page. 
- UI/UX improvements to the Workflow Management page. 

V 1.3.0
- Added Workflow Annotations: Now you can add sticky notes, text annotations, and shapes to your workflows for better documentation and organization
- Redesigned Workflow Management interface with improved usability and workflow monitoring
- Enhanced Workflow Queue with detailed execution tracking and better status visualization
- Improved workflow execution monitoring with real-time status updates
- Added detailed execution timeline view for better workflow debugging

V 1.2.3
- Added Chatlogs menu. Now you can see and track all your chatbot activities.
- Fixed a bug regarding memory of the chatbot and auto open function. 

V 1.2.2
- Fixed a bug related to saving chat workflows and receiving the shortcode
- Fixed styling but for smaller screens. 

V 1.2.1
- Fixed a bug related to chat notification sound
- Improved security of the chat feature

V 1.2.0
- Added Chatbot node. Now you can design and deploy your own conversational AI chatbot anywhere on your website, connect it to a workflow and train it on your own data!

V 1.1.0
- Added Unsplash Node. You can now automatically find images and add them to your content! 
- Improved security and encryption. 
- Night mode for the builder environment
- Redesigned sidebar buttons
- Workflow Name on top of each workflow for quick access and quick editing of workflows.
- Other bug fixes.

V 1.0.6
- UI improvements. 
- Analytics class added.
- Minor bug fixes.

V 1.0.5
- fixed a bug related to Post node. 

V 1.0.4
- Initial release of WP AI Workflow Automation



