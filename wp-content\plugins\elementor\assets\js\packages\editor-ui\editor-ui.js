/*! For license information please see editor-ui.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React},"react-dom":function(e){e.exports=window.ReactDOM},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/icons":function(e){e.exports=window.elementorV2.icons},"@elementor/ui":function(e){e.exports=window.elementorV2.ui},"@wordpress/i18n":function(e){e.exports=window.wp.i18n},"./node_modules/@tanstack/react-virtual/dist/esm/index.js":function(e,t,n){n.r(t),n.d(t,{Virtualizer:function(){return s.Virtualizer},approxEqual:function(){return s.approxEqual},debounce:function(){return s.debounce},defaultKeyExtractor:function(){return s.defaultKeyExtractor},defaultRangeExtractor:function(){return s.defaultRangeExtractor},elementScroll:function(){return s.elementScroll},measureElement:function(){return s.measureElement},memo:function(){return s.memo},notUndefined:function(){return s.notUndefined},observeElementOffset:function(){return s.observeElementOffset},observeElementRect:function(){return s.observeElementRect},observeWindowOffset:function(){return s.observeWindowOffset},observeWindowRect:function(){return s.observeWindowRect},useVirtualizer:function(){return a},useWindowVirtualizer:function(){return c},windowScroll:function(){return s.windowScroll}});var o=n("react"),i=n("react-dom"),s=n("./node_modules/@tanstack/virtual-core/dist/esm/index.js");const r="undefined"!=typeof document?o.useLayoutEffect:o.useEffect;function l(e){const t=o.useReducer((()=>({})),{})[1],n={...e,onChange:(n,o)=>{var s;o?(0,i.flushSync)(t):t(),null==(s=e.onChange)||s.call(e,n,o)}},[l]=o.useState((()=>new s.Virtualizer(n)));return l.setOptions(n),r((()=>l._didMount()),[]),r((()=>l._willUpdate())),l}function a(e){return l({observeElementRect:s.observeElementRect,observeElementOffset:s.observeElementOffset,scrollToFn:s.elementScroll,...e})}function c(e){return l({getScrollElement:()=>"undefined"!=typeof document?window:null,observeElementRect:s.observeWindowRect,observeElementOffset:s.observeWindowOffset,scrollToFn:s.windowScroll,initialOffset:()=>"undefined"!=typeof document?window.scrollY:0,...e})}},"./node_modules/@tanstack/virtual-core/dist/esm/index.js":function(e,t,n){n.r(t),n.d(t,{Virtualizer:function(){return g},approxEqual:function(){return o.approxEqual},debounce:function(){return o.debounce},defaultKeyExtractor:function(){return s},defaultRangeExtractor:function(){return r},elementScroll:function(){return p},measureElement:function(){return m},memo:function(){return o.memo},notUndefined:function(){return o.notUndefined},observeElementOffset:function(){return d},observeElementRect:function(){return l},observeWindowOffset:function(){return h},observeWindowRect:function(){return c},windowScroll:function(){return f}});var o=n("./node_modules/@tanstack/virtual-core/dist/esm/utils.js");const i=e=>{const{offsetWidth:t,offsetHeight:n}=e;return{width:t,height:n}},s=e=>e,r=e=>{const t=Math.max(e.startIndex-e.overscan,0),n=Math.min(e.endIndex+e.overscan,e.count-1),o=[];for(let e=t;e<=n;e++)o.push(e);return o},l=(e,t)=>{const n=e.scrollElement;if(!n)return;const o=e.targetWindow;if(!o)return;const s=e=>{const{width:n,height:o}=e;t({width:Math.round(n),height:Math.round(o)})};if(s(i(n)),!o.ResizeObserver)return()=>{};const r=new o.ResizeObserver((t=>{const o=()=>{const e=t[0];if(null==e?void 0:e.borderBoxSize){const t=e.borderBoxSize[0];if(t)return void s({width:t.inlineSize,height:t.blockSize})}s(i(n))};e.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(o):o()}));return r.observe(n,{box:"border-box"}),()=>{r.unobserve(n)}},a={passive:!0},c=(e,t)=>{const n=e.scrollElement;if(!n)return;const o=()=>{t({width:n.innerWidth,height:n.innerHeight})};return o(),n.addEventListener("resize",o,a),()=>{n.removeEventListener("resize",o)}},u="undefined"==typeof window||"onscrollend"in window,d=(e,t)=>{const n=e.scrollElement;if(!n)return;const i=e.targetWindow;if(!i)return;let s=0;const r=e.options.useScrollendEvent&&u?()=>{}:(0,o.debounce)(i,(()=>{t(s,!1)}),e.options.isScrollingResetDelay),l=o=>()=>{const{horizontal:i,isRtl:l}=e.options;s=i?n.scrollLeft*(l?-1:1):n.scrollTop,r(),t(s,o)},c=l(!0),d=l(!1);d(),n.addEventListener("scroll",c,a);const h=e.options.useScrollendEvent&&u;return h&&n.addEventListener("scrollend",d,a),()=>{n.removeEventListener("scroll",c),h&&n.removeEventListener("scrollend",d)}},h=(e,t)=>{const n=e.scrollElement;if(!n)return;const i=e.targetWindow;if(!i)return;let s=0;const r=e.options.useScrollendEvent&&u?()=>{}:(0,o.debounce)(i,(()=>{t(s,!1)}),e.options.isScrollingResetDelay),l=o=>()=>{s=n[e.options.horizontal?"scrollX":"scrollY"],r(),t(s,o)},c=l(!0),d=l(!1);d(),n.addEventListener("scroll",c,a);const h=e.options.useScrollendEvent&&u;return h&&n.addEventListener("scrollend",d,a),()=>{n.removeEventListener("scroll",c),h&&n.removeEventListener("scrollend",d)}},m=(e,t,n)=>{if(null==t?void 0:t.borderBoxSize){const e=t.borderBoxSize[0];if(e)return Math.round(e[n.options.horizontal?"inlineSize":"blockSize"])}return e[n.options.horizontal?"offsetWidth":"offsetHeight"]},f=(e,{adjustments:t=0,behavior:n},o)=>{var i,s;const r=e+t;null==(s=null==(i=o.scrollElement)?void 0:i.scrollTo)||s.call(i,{[o.options.horizontal?"left":"top"]:r,behavior:n})},p=(e,{adjustments:t=0,behavior:n},o)=>{var i,s;const r=e+t;null==(s=null==(i=o.scrollElement)?void 0:i.scrollTo)||s.call(i,{[o.options.horizontal?"left":"top"]:r,behavior:n})};class g{constructor(e){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let e=null;const t=()=>e||(this.targetWindow&&this.targetWindow.ResizeObserver?e=new this.targetWindow.ResizeObserver((e=>{e.forEach((e=>{const t=()=>{this._measureElement(e.target,e)};this.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(t):t()}))})):null);return{disconnect:()=>{var n;null==(n=t())||n.disconnect(),e=null},observe:e=>{var n;return null==(n=t())?void 0:n.observe(e,{box:"border-box"})},unobserve:e=>{var n;return null==(n=t())?void 0:n.unobserve(e)}}})(),this.range=null,this.setOptions=e=>{Object.entries(e).forEach((([t,n])=>{void 0===n&&delete e[t]})),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:s,rangeExtractor:r,onChange:()=>{},measureElement:m,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!1,useAnimationFrameWithResizeObserver:!1,...e}},this.notify=e=>{var t,n;null==(n=(t=this.options).onChange)||n.call(t,this,e)},this.maybeNotify=(0,o.memo)((()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null])),(e=>{this.notify(e)}),{key:"maybeNotify",debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach((e=>e())),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var e;const t=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==t){if(this.cleanup(),!t)return void this.maybeNotify();this.scrollElement=t,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=(null==(e=this.scrollElement)?void 0:e.window)??null,this.elementsCache.forEach((e=>{this.observer.observe(e)})),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,(e=>{this.scrollRect=e,this.maybeNotify()}))),this.unsubs.push(this.options.observeElementOffset(this,((e,t)=>{this.scrollAdjustments=0,this.scrollDirection=t?this.getScrollOffset()<e?"forward":"backward":null,this.scrollOffset=e,this.isScrolling=t,this.maybeNotify()})))}},this.getSize=()=>this.options.enabled?(this.scrollRect=this.scrollRect??this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0),this.getScrollOffset=()=>this.options.enabled?(this.scrollOffset=this.scrollOffset??("function"==typeof this.options.initialOffset?this.options.initialOffset():this.options.initialOffset),this.scrollOffset):(this.scrollOffset=null,0),this.getFurthestMeasurement=(e,t)=>{const n=new Map,o=new Map;for(let i=t-1;i>=0;i--){const t=e[i];if(n.has(t.lane))continue;const s=o.get(t.lane);if(null==s||t.end>s.end?o.set(t.lane,t):t.end<s.end&&n.set(t.lane,!0),n.size===this.options.lanes)break}return o.size===this.options.lanes?Array.from(o.values()).sort(((e,t)=>e.end===t.end?e.index-t.index:e.end-t.end))[0]:void 0},this.getMeasurementOptions=(0,o.memo)((()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled]),((e,t,n,o,i)=>(this.pendingMeasuredCacheIndexes=[],{count:e,paddingStart:t,scrollMargin:n,getItemKey:o,enabled:i})),{key:!1}),this.getMeasurements=(0,o.memo)((()=>[this.getMeasurementOptions(),this.itemSizeCache]),(({count:e,paddingStart:t,scrollMargin:n,getItemKey:o,enabled:i},s)=>{if(!i)return this.measurementsCache=[],this.itemSizeCache.clear(),[];0===this.measurementsCache.length&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach((e=>{this.itemSizeCache.set(e.key,e.size)})));const r=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];const l=this.measurementsCache.slice(0,r);for(let i=r;i<e;i++){const e=o(i),r=1===this.options.lanes?l[i-1]:this.getFurthestMeasurement(l,i),a=r?r.end+this.options.gap:t+n,c=s.get(e),u="number"==typeof c?c:this.options.estimateSize(i),d=a+u,h=r?r.lane:i%this.options.lanes;l[i]={index:i,start:a,size:u,end:d,key:e,lane:h}}return this.measurementsCache=l,l}),{key:"getMeasurements",debug:()=>this.options.debug}),this.calculateRange=(0,o.memo)((()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset(),this.options.lanes]),((e,t,n,o)=>this.range=e.length>0&&t>0?function({measurements:e,outerSize:t,scrollOffset:n,lanes:o}){const i=e.length-1;if(e.length<=o)return{startIndex:0,endIndex:i};let s=v(0,i,(t=>e[t].start),n),r=s;if(1===o)for(;r<i&&e[r].end<n+t;)r++;else if(o>1){const l=Array(o).fill(0);for(;r<i&&l.some((e=>e<n+t));){const t=e[r];l[t.lane]=t.end,r++}const a=Array(o).fill(n+t);for(;s>=0&&a.some((e=>e>=n));){const t=e[s];a[t.lane]=t.start,s--}s=Math.max(0,s-s%o),r=Math.min(i,r+(o-1-r%o))}return{startIndex:s,endIndex:r}}({measurements:e,outerSize:t,scrollOffset:n,lanes:o}):null),{key:"calculateRange",debug:()=>this.options.debug}),this.getVirtualIndexes=(0,o.memo)((()=>{let e=null,t=null;const n=this.calculateRange();return n&&(e=n.startIndex,t=n.endIndex),this.maybeNotify.updateDeps([this.isScrolling,e,t]),[this.options.rangeExtractor,this.options.overscan,this.options.count,e,t]}),((e,t,n,o,i)=>null===o||null===i?[]:e({startIndex:o,endIndex:i,overscan:t,count:n})),{key:"getVirtualIndexes",debug:()=>this.options.debug}),this.indexFromElement=e=>{const t=this.options.indexAttribute,n=e.getAttribute(t);return n?parseInt(n,10):(console.warn(`Missing attribute name '${t}={index}' on measured element.`),-1)},this._measureElement=(e,t)=>{const n=this.indexFromElement(e),o=this.measurementsCache[n];if(!o)return;const i=o.key,s=this.elementsCache.get(i);s!==e&&(s&&this.observer.unobserve(s),this.observer.observe(e),this.elementsCache.set(i,e)),e.isConnected&&this.resizeItem(n,this.options.measureElement(e,t,this))},this.resizeItem=(e,t)=>{const n=this.measurementsCache[e];if(!n)return;const o=t-(this.itemSizeCache.get(n.key)??n.size);0!==o&&((void 0!==this.shouldAdjustScrollPositionOnItemSizeChange?this.shouldAdjustScrollPositionOnItemSizeChange(n,o,this):n.start<this.getScrollOffset()+this.scrollAdjustments)&&(this.options.debug&&console.info("correction",o),this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=o,behavior:void 0})),this.pendingMeasuredCacheIndexes.push(n.index),this.itemSizeCache=new Map(this.itemSizeCache.set(n.key,t)),this.notify(!1))},this.measureElement=e=>{e?this._measureElement(e,void 0):this.elementsCache.forEach(((e,t)=>{e.isConnected||(this.observer.unobserve(e),this.elementsCache.delete(t))}))},this.getVirtualItems=(0,o.memo)((()=>[this.getVirtualIndexes(),this.getMeasurements()]),((e,t)=>{const n=[];for(let o=0,i=e.length;o<i;o++){const i=t[e[o]];n.push(i)}return n}),{key:"getVirtualItems",debug:()=>this.options.debug}),this.getVirtualItemForOffset=e=>{const t=this.getMeasurements();if(0!==t.length)return(0,o.notUndefined)(t[v(0,t.length-1,(e=>(0,o.notUndefined)(t[e]).start),e)])},this.getOffsetForAlignment=(e,t,n=0)=>{const o=this.getSize(),i=this.getScrollOffset();"auto"===t&&(t=e>=i+o?"end":"start"),"center"===t?e+=(n-o)/2:"end"===t&&(e-=o);const s=this.getTotalSize()-o;return Math.max(Math.min(s,e),0)},this.getOffsetForIndex=(e,t="auto")=>{e=Math.max(0,Math.min(e,this.options.count-1));const n=this.measurementsCache[e];if(!n)return;const o=this.getSize(),i=this.getScrollOffset();if("auto"===t)if(n.end>=i+o-this.options.scrollPaddingEnd)t="end";else{if(!(n.start<=i+this.options.scrollPaddingStart))return[i,t];t="start"}const s="end"===t?n.end+this.options.scrollPaddingEnd:n.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(s,t,n.size),t]},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{null!==this.scrollToIndexTimeoutId&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(e,{align:t="start",behavior:n}={})=>{this.cancelScrollToIndex(),"smooth"===n&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(e,t),{adjustments:void 0,behavior:n})},this.scrollToIndex=(e,{align:t="auto",behavior:n}={})=>{e=Math.max(0,Math.min(e,this.options.count-1)),this.cancelScrollToIndex(),"smooth"===n&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");const i=this.getOffsetForIndex(e,t);if(!i)return;const[s,r]=i;this._scrollToOffset(s,{adjustments:void 0,behavior:n}),"smooth"!==n&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout((()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(e))){const t=this.getOffsetForIndex(e,r);if(!t)return;const[i]=t,s=this.getScrollOffset();(0,o.approxEqual)(i,s)||this.scrollToIndex(e,{align:r,behavior:n})}else this.scrollToIndex(e,{align:r,behavior:n})})))},this.scrollBy=(e,{behavior:t}={})=>{this.cancelScrollToIndex(),"smooth"===t&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+e,{adjustments:void 0,behavior:t})},this.getTotalSize=()=>{var e;const t=this.getMeasurements();let n;if(0===t.length)n=this.options.paddingStart;else if(1===this.options.lanes)n=(null==(e=t[t.length-1])?void 0:e.end)??0;else{const e=Array(this.options.lanes).fill(null);let o=t.length-1;for(;o>=0&&e.some((e=>null===e));){const n=t[o];null===e[n.lane]&&(e[n.lane]=n.end),o--}n=Math.max(...e.filter((e=>null!==e)))}return Math.max(n-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(e,{adjustments:t,behavior:n})=>{this.options.scrollToFn(e,{behavior:n,adjustments:t},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(e)}}const v=(e,t,n,o)=>{for(;e<=t;){const i=(e+t)/2|0,s=n(i);if(s<o)e=i+1;else{if(!(s>o))return i;t=i-1}}return e>0?e-1:0}},"./node_modules/@tanstack/virtual-core/dist/esm/utils.js":function(e,t,n){function o(e,t,n){let o,i=n.initialDeps??[];function s(){var s,r,l,a;let c;n.key&&(null==(s=n.debug)?void 0:s.call(n))&&(c=Date.now());const u=e();if(u.length===i.length&&!u.some(((e,t)=>i[t]!==e)))return o;let d;if(i=u,n.key&&(null==(r=n.debug)?void 0:r.call(n))&&(d=Date.now()),o=t(...u),n.key&&(null==(l=n.debug)?void 0:l.call(n))){const e=Math.round(100*(Date.now()-c))/100,t=Math.round(100*(Date.now()-d))/100,o=t/16,i=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${i(t,5)} /${i(e,5)} ms`,`\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(0,Math.min(120-120*o,120))}deg 100% 31%);`,null==n?void 0:n.key)}return null==(a=null==n?void 0:n.onChange)||a.call(n,o),o}return s.updateDeps=e=>{i=e},s}function i(e,t){if(void 0===e)throw new Error("Unexpected undefined"+(t?`: ${t}`:""));return e}n.r(t),n.d(t,{approxEqual:function(){return s},debounce:function(){return r},memo:function(){return o},notUndefined:function(){return i}});const s=(e,t)=>Math.abs(e-t)<=1,r=(e,t,n)=>{let o;return function(...i){e.clearTimeout(o),o=e.setTimeout((()=>t.apply(this,i)),n)}}}},t={};function n(o){var i=t[o];if(void 0!==i)return i.exports;var s=t[o]={exports:{}};return e[o](s,s.exports,n),s.exports}n.d=function(e,t){for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};!function(){n.r(o),n.d(o,{EditableField:function(){return d},EllipsisWithTooltip:function(){return a},ITEM_HEIGHT:function(){return C},InfoAlert:function(){return b},InfoTipCard:function(){return w},IntroductionModal:function(){return m},MenuItemInfotip:function(){return x},MenuListItem:function(){return E},PopoverHeader:function(){return I},PopoverMenuList:function(){return M},PopoverScrollableContent:function(){return R},PopoverSearch:function(){return W},StyledMenuList:function(){return O},ThemeProvider:function(){return v},WarningInfotip:function(){return y},useEditable:function(){return A}});var e=n("react"),t=n("@elementor/ui"),i=n("@wordpress/i18n"),s=n("@elementor/editor-v1-adapters"),r=n("@elementor/icons"),l=n("./node_modules/@tanstack/react-virtual/dist/esm/index.js"),a=({maxWidth:n,title:o,as:i,...s})=>{const[r,l]=u();return l?e.createElement(t.Tooltip,{title:o,placement:"top"},e.createElement(c,{maxWidth:n,ref:r,as:i,...s},o)):e.createElement(c,{maxWidth:n,ref:r,as:i,...s},o)},c=e.forwardRef((({maxWidth:n,as:o=t.Box,...i},s)=>e.createElement(o,{ref:s,position:"relative",...i,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:n}}))),u=()=>{const[t,n]=(0,e.useState)(null),[o,i]=(0,e.useState)(!1);return(0,e.useEffect)((()=>{const e=new ResizeObserver((([{target:e}])=>{i(e.scrollWidth>e.clientWidth)}));return t&&e.observe(t),()=>{e.disconnect()}}),[t]),[n,o]},d=(0,e.forwardRef)((({value:n,error:o,as:i="span",sx:s,...r},l)=>e.createElement(t.Tooltip,{title:o,open:!!o,placement:"top"},e.createElement(h,{ref:l,component:i,...r},n)))),h=(0,t.styled)(t.Box)`
	width: 100%;
	&:focus {
		outline: none;
	}
`,m=({open:n,handleClose:o,title:s,children:r})=>{const[l,a]=(0,e.useState)(!0);return e.createElement(t.Dialog,{open:n,onClose:o,maxWidth:"sm",TransitionComponent:f},s&&e.createElement(t.DialogHeader,{logo:!1},e.createElement(t.DialogTitle,null,s)),r,e.createElement(t.DialogActions,null,e.createElement(t.FormControlLabel,{sx:{marginRight:"auto"},control:e.createElement(t.Checkbox,{checked:!l,onChange:()=>a(!l)}),label:e.createElement(t.Typography,{variant:"body2"},(0,i.__)("Don't show this again","elementor"))}),e.createElement(t.Button,{size:"medium",variant:"contained",sx:{minWidth:"135px"},onClick:()=>o(l)},(0,i.__)("Got it","elementor"))))},f=e.forwardRef(((n,o)=>e.createElement(t.Fade,{ref:o,...n,timeout:{enter:1e3,exit:200}})));function p(){return window.elementor?.getPreferences?.("ui_theme")||"auto"}var g="unstable";function v({children:n}){const o=function(){const[t,n]=(0,e.useState)((()=>p()));return(0,e.useEffect)((()=>(0,s.__privateListenTo)((0,s.v1ReadyEvent)(),(()=>n(p())))),[]),(0,e.useEffect)((()=>(0,s.__privateListenTo)((0,s.commandEndEvent)("document/elements/settings"),(e=>{const t=e;t.args?.settings&&"ui_theme"in t.args.settings&&n(p())}))),[]),t}();return e.createElement(t.ThemeProvider,{colorScheme:o,palette:g},n)}var b=n=>e.createElement(t.Alert,{icon:e.createElement(r.InfoCircleFilledIcon,{fontSize:"small",color:"secondary"}),variant:"standard",color:"secondary",elevation:0,size:"small",...n}),E=({children:n,...o})=>e.createElement(t.MenuItem,{dense:!0,...o,sx:{...o.sx??{}}},e.createElement(t.MenuItemText,{primary:n,primaryTypographyProps:{variant:"caption"}})),x=(0,e.forwardRef)((({showInfoTip:n=!1,children:o,content:i},s)=>n?e.createElement(t.Infotip,{ref:s,placement:"right",arrow:!1,content:e.createElement(b,{sx:{maxWidth:325}},i)},e.createElement("div",{style:{pointerEvents:"initial",width:"100%"},onClick:e=>e.stopPropagation()},o)):e.createElement(e.Fragment,null,o))),w=({content:n,svgIcon:o,learnMoreButton:i,ctaButton:s})=>e.createElement(t.Card,{elevation:0,sx:{width:320}},e.createElement(t.CardContent,{sx:{pb:0}},e.createElement(t.Box,{display:"flex",alignItems:"start"},e.createElement(t.SvgIcon,{fontSize:"tiny",sx:{mr:.5}},o),e.createElement(t.Typography,{variant:"body2"},n))),(s||i)&&e.createElement(t.CardActions,null,i&&e.createElement(t.Button,{size:"small",color:"warning",href:i.href,target:"_blank"},i.label),s&&e.createElement(t.Button,{size:"small",color:"warning",variant:"contained",onClick:s.onClick},s.label))),y=(0,e.forwardRef)((({children:n,open:o,title:i,text:s,placement:r,width:l,offset:a},c)=>e.createElement(t.Infotip,{ref:c,open:o,placement:r,PopperProps:{sx:{width:l||"initial",".MuiTooltip-tooltip":{marginLeft:0,marginRight:0}},modifiers:a?[{name:"offset",options:{offset:a}}]:[]},arrow:!1,content:e.createElement(t.Alert,{color:"error",severity:"warning",variant:"standard",size:"small"},i?e.createElement(t.AlertTitle,null,i):null,s)},n))),S=(0,s.isExperimentActive)("e_v_3_30"),I=({title:n,onClose:o,icon:i,actions:s})=>{const r=S?{pl:2,pr:1,py:1.5,maxHeight:36}:{pl:1.5,pr:.5,py:1.5};return e.createElement(t.Stack,{direction:"row",alignItems:"center",...r,sx:{columnGap:.5}},i,e.createElement(t.Typography,{variant:"subtitle2",sx:S?{fontSize:"12px",mt:.25}:void 0},n),e.createElement(t.Stack,{direction:"row",sx:{ml:"auto"}},s,e.createElement(t.CloseButton,{slotProps:{icon:{fontSize:"tiny"}},sx:{ml:"auto"},onClick:o})))},z=(0,s.isExperimentActive)("e_v_3_30"),C=32,M=({items:n,onSelect:o,onClose:i,selectedValue:s,itemStyle:r,onChange:a,"data-testid":c,menuItemContentTemplate:u,noResultsComponent:d,menuListTemplate:h})=>{const m=(0,e.useRef)(null),f=(({containerRef:t})=>{const[n,o]=(0,e.useState)(0);return(0,e.useEffect)((()=>{const e=t.current;if(!e)return;const n=()=>{o(e.scrollTop)};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}),[t]),n})({containerRef:m}),p=h||O,g=(0,e.useMemo)((()=>n.reduce(((e,t,n)=>("category"===t.type&&e.push(n),e)),[])),[n]),v=(0,l.useVirtualizer)({count:n.length,getScrollElement:()=>m.current,estimateSize:()=>C,overscan:6,rangeExtractor:e=>{const t=[];for(let n=e.startIndex;n<=e.endIndex;n++)t.push(n);const n=((e,t)=>[...e.filter((e=>e<t.startIndex)).slice(-2),...e.filter((e=>e>t.endIndex)).slice(0,2)])(g,e);return n.forEach((e=>{t.includes(e)||t.push(e)})),t.sort(((e,t)=>e-t))},onChange:a});return(({selectedValue:t,items:n,virtualizer:o})=>{(0,e.useEffect)((()=>{if(!t||0===n.length)return;const e=n.findIndex((e=>e.value===t));-1!==e&&o.scrollToIndex(e,{align:"center"})}),[t,n,o])})({selectedValue:s,items:n,virtualizer:v}),e.createElement(t.Box,{ref:m},0===n.length&&d?d:e.createElement(p,{role:"listbox",style:{height:`${v.getTotalSize()}px`},"data-testid":c},v.getVirtualItems().map((l=>{const a=n[l.index],c=l.index===n.length-1,d="category"===n[0]?.type?1===l.index:0===l.index,h=s===a.value,m=s?-1:0;if(!a)return null;if("category"===a.type){const n=l.start+8<=f;return e.createElement(t.MenuSubheader,{key:l.key,style:n?{}:(p=l.start,{position:"absolute",transform:`translateY(${p+8}px)`}),sx:z?{fontWeight:"400",color:"text.tertiary"}:void 0},a.label||a.value)}var p;return e.createElement("li",{key:l.key,role:"option","aria-selected":h,onClick:e=>{e.target.closest("button")||(o(a.value),i())},onKeyDown:e=>{"Enter"===e.key&&(o(a.value),i()),"ArrowDown"===e.key&&c&&(e.preventDefault(),e.stopPropagation()),"ArrowUp"===e.key&&d&&(e.preventDefault(),e.stopPropagation())},tabIndex:h?0:m,style:{transform:`translateY(${l.start+8}px)`,...r?r(a):{}}},u?u(a):a.label||a.value)}))))},O=(0,t.styled)(t.MenuList)((({theme:e})=>({"& > li":{height:C,width:"100%",display:"flex",alignItems:"center"},'& > [role="option"]':{...e.typography.caption,lineHeight:"inherit",padding:e.spacing(.75,2,.75,4),"&:hover, &:focus":{backgroundColor:e.palette.action.hover},'&[aria-selected="true"]':{backgroundColor:e.palette.action.selected},cursor:"pointer",textOverflow:"ellipsis",position:"absolute",top:0,left:0},width:"100%",position:"relative"}))),T=(0,s.isExperimentActive)("e_v_3_30"),R=e.forwardRef((({children:n,height:o=260,width:i=220},s)=>e.createElement(t.Box,{ref:s,sx:{overflowY:"auto",height:o,width:(T?i-32:220)+"px",maxWidth:496}},n))),k=(0,s.isExperimentActive)("e_v_3_30"),_="tiny",W=({value:n,onSearch:o,placeholder:s})=>{const l=(0,e.useRef)(null),a=k?{px:2,pb:1.5}:{px:1.5,pb:1};return e.createElement(t.Box,{...a},e.createElement(t.TextField,{autoFocus:!0,fullWidth:!0,size:_,value:n,inputRef:l,onChange:e=>{o(e.target.value)},placeholder:s,InputProps:{startAdornment:e.createElement(t.InputAdornment,{position:"start"},e.createElement(r.SearchIcon,{fontSize:_})),endAdornment:n&&e.createElement(t.IconButton,{size:_,onClick:()=>{o(""),l.current?.focus()},"aria-label":(0,i.__)("Clear","elementor")},e.createElement(r.XIcon,{color:"action",fontSize:_}))}}))},A=({value:t,onSubmit:n,validation:o,onClick:i,onError:s})=>{const[r,l]=(0,e.useState)(!1),[a,c]=(0,e.useState)(null),u=D(r),d=e=>e!==t,h=()=>{u.current?.blur(),c(null),s?.(null),l(!1)},m={onClick:e=>{r&&e.stopPropagation(),i?.(e)},onKeyDown:e=>(e.stopPropagation(),["Escape"].includes(e.key)?h():["Enter"].includes(e.key)?(e.preventDefault(),(e=>{if(d(e)&&!a)try{n(e)}finally{h()}})(e.target.innerText)):void 0),onInput:e=>{const{innerText:t}=e.target;if(o){const e=d(t)?o(t):null;c(e),s?.(e)}},onBlur:h},f={value:t,role:"textbox",contentEditable:r,...r&&{suppressContentEditableWarning:!0}};return{ref:u,isEditing:r,openEditMode:()=>{l(!0)},closeEditMode:h,value:t,error:a,getProps:()=>({...m,...f})}},D=t=>{const n=(0,e.useRef)(null);return(0,e.useEffect)((()=>{t&&P(n.current)}),[t]),n},P=e=>{const t=getSelection();if(!t||!e)return;const n=document.createRange();n.selectNodeContents(e),t.removeAllRanges(),t.addRange(n)}}(),(window.elementorV2=window.elementorV2||{}).editorUi=o}(),window.elementorV2.editorUi?.init?.();